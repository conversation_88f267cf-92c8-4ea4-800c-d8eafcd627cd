import { Module } from '@nestjs/common';
import { CapabilityController, MasterCapabilityController } from './controllers';
import { AdminApiClient, AttachmentApiClient, HistoryApiClient } from 'src/shared/clients';
import {
	ExcelSheetService,
	SharedAttachmentService,
	SharedPermissionService,
} from 'src/shared/services';
import {
	CapabilityCategoryRepository,
	CapabilityLegRepository,
	CapabilityRepository,
	LocationWiseCapabilityRepository,
	MasterCapabilityDropdownRepository,
	MasterEvidenceRepository,
} from './repositories';
import { DatabaseHelper } from 'src/shared/helpers';
import { CapabilityService, MasterCapabilityService } from './services';
import { LocationRepository } from 'src/location/repositories';
import { BusinessEntityService } from 'src/business-entity/services';
import { UserPermissionRepository } from 'src/permission/repositories';
import { CommonDropdownRepository } from 'src/metadata/repositories';
import { BingoCardConfigRepository } from 'src/bingo-card/repositories';

const repositories = [
	CapabilityRepository,
	LocationRepository,
	LocationWiseCapabilityRepository,
	UserPermissionRepository,
	CapabilityLegRepository,
	CapabilityCategoryRepository,
	MasterCapabilityDropdownRepository,
	MasterEvidenceRepository,
	CommonDropdownRepository,
	BingoCardConfigRepository,
];

@Module({
	controllers: [CapabilityController, MasterCapabilityController],
	providers: [
		...repositories,
		AdminApiClient,
		SharedPermissionService,
		DatabaseHelper,
		CapabilityService,
		SharedPermissionService,
		DatabaseHelper,
		AttachmentApiClient,
		SharedAttachmentService,
		BusinessEntityService,
		ExcelSheetService,
		MasterCapabilityService,
		HistoryApiClient,
	],
})
export class CapabilityModule {}
