{"version": 3, "file": "setup-new-location-request-validation.js", "sourceRoot": "", "sources": ["../../../src/location/validators/setup-new-location-request-validation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,8DAAmE;AACnE,2CAA4C;AAC5C,kDAAoD;AAEpD,wDAAsD;AACtD,8CAO0B;AAGnB,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IACnC,YACkB,cAA8B,EAC9B,sBAA8C;QAD9C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC7D,CAAC;IAES,QAAQ,CACpB,cAAwC,EACxC,kBAA2B;;;YAM3B,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,cAAc,CAAC;YAEpE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAEjE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,6BAA6B,CACzF,cAAc,CACd,CAAC;YAEF,IAAI,CAAC,kBAAkB,EAAE;gBACxB,MAAM,IAAI,0BAAa,CAAC,uBAAuB,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aACvE;YAED,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBAC/E,OAAO,YAAY,CAAC,EAAE,KAAK,cAAc,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,EAAE;gBACxB,MAAM,IAAI,0BAAa,CACtB,gDAAgD,EAChD,kBAAU,CAAC,SAAS,CACpB,CAAC;aACF;YAED,MAAM,aAAa,GAAG,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,oBAAoB,mCAAI,EAAE,CAAC;YAErE,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,aAAa,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAE5F,IAAI,CAAC,0BAA0B,CAAC,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;YAExF,OAAO;gBACN,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACL,YAAY;iBACZ;aACD,CAAC;;KACF;IAEY,sBAAsB,CAAC,QAAgB;;YACnD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;YAEtF,IAAI,CAAC,YAAY,EAAE;gBAClB,MAAM,IAAI,0BAAa,CAAC,mBAAmB,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aACnE;YAED,IACC,CAAC,CAAC,6BAAqB,CAAC,OAAO,EAAE,6BAAqB,CAAC,IAAI,CAAC,CAAC,QAAQ,CACpE,YAAY,CAAC,WAAW,CACxB,EACA;gBACD,MAAM,IAAI,0BAAa,CACtB,iDAAiD,EACjD,kBAAU,CAAC,WAAW,CACtB,CAAC;aACF;YAED,OAAO,YAAY,CAAC;QACrB,CAAC;KAAA;IAEM,eAAe,CACrB,kBAAkB,EAClB,aAAuB,EACvB,cAAwC,EACxC,kBAA2B;QAE3B,MAAM,EACL,iBAAiB,EACjB,WAAW,EACX,cAAc,EACd,2BAA2B,EAC3B,iBAAiB,GACjB,GAAG,cAAc,CAAC;QAEnB,IAAI,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE;YAClE,MAAM,IAAI,0BAAa,CACtB,wCAAwC,kBAAkB,CAAC,KAAK,gDAAgD,EAChH,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IAAI,iBAAiB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,6BAAqB,CAAC,kBAAkB,CAAC,EAAE;YAC3F,MAAM,IAAI,0BAAa,CACtB,wCAAwC,kBAAkB,CAAC,KAAK,GAAG,EACnE,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,6BAAqB,CAAC,aAAa,CAAC,IAAI,WAAW,EAAE;YAChF,MAAM,IAAI,0BAAa,CACtB,+BAA+B,kBAAkB,CAAC,KAAK,GAAG,EAC1D,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IACC,kBAAkB;YAClB,aAAa,CAAC,QAAQ,CAAC,6BAAqB,CAAC,aAAa,CAAC;YAC3D,CAAC,WAAW,EACX;YACD,MAAM,IAAI,0BAAa,CACtB,gCAAgC,kBAAkB,CAAC,KAAK,GAAG,EAC3D,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,6BAAqB,CAAC,gBAAgB,CAAC,IAAI,cAAc,EAAE;YACtF,MAAM,IAAI,0BAAa,CACtB,kCAAkC,kBAAkB,CAAC,KAAK,GAAG,EAC7D,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IACC,CAAC,aAAa,CAAC,QAAQ,CAAC,6BAAqB,CAAC,8BAA8B,CAAC;YAC7E,2BAA2B,EAC1B;YACD,MAAM,IAAI,0BAAa,CACtB,wDAAwD,kBAAkB,CAAC,KAAK,GAAG,EACnF,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IACC,aAAa,CAAC,QAAQ,CAAC,6BAAqB,CAAC,8BAA8B,CAAC;YAC5E,CAAC,2BAA2B,EAC3B;YACD,MAAM,IAAI,0BAAa,CACtB,yDAAyD,kBAAkB,CAAC,KAAK,GAAG,EACpF,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,6BAAqB,CAAC,kBAAkB,CAAC,IAAI,iBAAiB,EAAE;YAC3F,MAAM,IAAI,0BAAa,CACtB,oCAAoC,kBAAkB,CAAC,KAAK,GAAG,EAC/D,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;IACF,CAAC;IAEM,0BAA0B,CAChC,cAAwC,EACxC,kBAAkB,EAClB,kBAAkB;;QAElB,MAAM,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,GACtF,cAAc,CAAC;QAUhB,IACC,kBAAkB,CAAC,IAAI,IAAI,2BAAmB,CAAC,EAAE;YACjD,kBAAkB,CAAC,IAAI,IAAI,0BAAkB,CAAC,aAAa;YAC3D,CAAC,mBAAmB,EACnB;YACD,MAAM,IAAI,0BAAa,CACtB,oEAAoE,EACpE,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IAAI,kBAAkB,CAAC,IAAI,KAAK,2BAAmB,CAAC,GAAG,IAAI,kBAAkB,CAAC,IAAI,KAAK,0BAAkB,CAAC,KAAK,IAAI,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,aAAa,CAAA,EAAE;YACpJ,MAAM,IAAI,0BAAa,CACtB,gCAAgC,kBAAkB,CAAC,KAAK,GAAG,EAC3D,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IAAI,kBAAkB,CAAC,IAAI,KAAK,0BAAkB,CAAC,KAAK,KAAI,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,aAAa,CAAA,EAAE;YAC5F,MAAM,IAAI,0BAAa,CACtB,mCAAmC,kBAAkB,CAAC,KAAK,GAAG,EAC9D,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IACC,CAAA,MAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,WAAW,0CAAE,MAAM,IAAG,CAAC;YAC5C,CAAC,CACA,kBAAkB,CAAC,IAAI,KAAK,2BAAmB,CAAC,EAAE;gBAClD,kBAAkB,CAAC,IAAI,IAAI,0BAAkB,CAAC,aAAa,CAC3D,EACA;YACD,MAAM,IAAI,0BAAa,CACtB,wEAAwE,EACxE,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IACC,kBAAkB,CAAC,IAAI,KAAK,2BAAmB,CAAC,EAAE;YAClD,CAAC,CAAC,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,MAAM,CAAA,IAAI,mBAAmB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EACxE;YACD,MAAM,IAAI,0BAAa,CACtB,6CAA6C,EAC7C,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QACD,IACC,gBAAgB,CAAC,cAAc,KAAK,4BAAoB,CAAC,KAAK;YAC9D,gBAAgB,CAAC,cAAc,KAAK,4BAAoB,CAAC,SAAS;YAClE,CAAC,gBAAgB,CAAC,UAAU,EAC3B;YACD,MAAM,IAAI,0BAAa,CAAC,0BAA0B,EAAE,kBAAU,CAAC,WAAW,CAAC,CAAC;SAC5E;QAED,IACC,gBAAgB,CAAC,cAAc,KAAK,4BAAoB,CAAC,KAAK;YAC9D,CAAC,gBAAgB,CAAC,qBAAqB,EACtC;YACD,MAAM,IAAI,0BAAa,CAAC,6BAA6B,EAAE,kBAAU,CAAC,WAAW,CAAC,CAAC;SAC/E;QAED,IACC,kBAAkB,CAAC,IAAI,IAAI,2BAAmB,CAAC,EAAE;YACjD,kBAAkB,CAAC,IAAI,IAAI,0BAAkB,CAAC,aAAa;YAC3D,CAAC,mBAAmB,EACnB;YACD,MAAM,IAAI,0BAAa,CACtB,oEAAoE,EACpE,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;QAED,IACC,CAAC,kBAAkB,CAAC,IAAI,IAAI,2BAAmB,CAAC,EAAE;YACjD,kBAAkB,CAAC,IAAI,IAAI,2BAAmB,CAAC,EAAE,CAAC;YACnD,CAAC,CAAC,CAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,QAAQ,CAAA,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EACzE;YACD,MAAM,IAAI,0BAAa,CACtB,mFAAmF,EACnF,kBAAU,CAAC,WAAW,CACtB,CAAC;SACF;IACF,CAAC;CACD,CAAA;AA9PY,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAGsB,wBAAc;QACN,qCAAsB;GAHpD,uBAAuB,CA8PnC;AA9PY,0DAAuB"}