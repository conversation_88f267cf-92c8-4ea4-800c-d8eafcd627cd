"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisProvider = void 0;
const client_1 = require("@redis/client");
const config_service_1 = require("../../config/config.service");
exports.RedisProvider = {
    provide: 'REDIS_CLIENT',
    useFactory: (configService) => __awaiter(void 0, void 0, void 0, function* () {
        const client = (0, client_1.createClient)({ url: configService.get('REDIS_URL') });
        yield client.connect();
        return client;
    }),
    inject: [config_service_1.ConfigService],
};
//# sourceMappingURL=redis.provider.js.map