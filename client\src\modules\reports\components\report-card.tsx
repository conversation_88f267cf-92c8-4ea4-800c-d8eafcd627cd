import CustomCard from '@/components/custom-card/custom-card';
import { IconButton, MenuItem, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { useTheme } from '@mui/material/styles';
import { GeneralReportResponse, ReportResponse } from '@/shared/models/report.model';
import { useNavigate } from 'react-router';
import { ReplaceUrlVariable, paths } from '@/routes/paths';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useTranslate } from '@/locales/use-locales';
import { LockOutlined, Public } from '@mui/icons-material';
import CustomPopover, { usePopover } from '@/components/custom-popover';
import { LoadingButton } from '@mui/lab';
import { ConfirmDialog } from '@/components/custom-dialog';
import ModifyReport from './modify-report';
import { useBoolean } from '@/hooks/use-boolean';
import { useLoading } from '@/hooks/use-loading';
import { deleteReport, queryClient } from '@/shared/services';
import { enqueueSnackbar } from 'notistack';
import { useMutation } from 'react-query';

interface ReportCardProps {
  report: ReportResponse;
  canEdit: boolean;
  canDelete: boolean;
  accounts: any;
  confirm: any;
  deleteConfirm: any;
  handleClose: any;
}
export default function ReportCard({ report, canEdit, canDelete, accounts, deleteConfirm }: ReportCardProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslate();
  const popover = usePopover();
  const confirm = useBoolean();
  const { setLoading, setMessage } = useLoading();
  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('submitting'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };
  const handleDelete = async (id: number): Promise<GeneralReportResponse> => {
    return deleteReport(id); //TODO Update this
  };
  const { mutateAsync, isLoading } = useMutation({
    mutationFn: handleDelete,
    onSuccess: (response: GeneralReportResponse) => {
      queryClient.invalidateQueries(['reports']);
      enqueueSnackbar(response.message, {
        variant: 'success',
      });
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });
  return (
    <>
      <CustomCard
        cardStyle={{
          height: 'auto',
          width: 'auto',
          p: 0,
          alignItems: 'left',
          justifyContent: 'left',
          transition: 'all 0.3s',
          '&:hover': {
            boxShadow: theme.shadows[10],
            transform: 'translateY(-4px)',
          },
        }}
        id={report.id}
        cardContentStyle={{ p: 0 }}
        sx={{
          position: 'relative',
          cursor: 'pointer',
        }}
      >
        <>
          {(canEdit || canDelete) && (
            <IconButton
              size="small"
              onClick={(event) => {
                popover.onOpen(event);
              }}
              sx={{ position: 'absolute', top: 8, right: 8 }}
            >
              <MoreVertIcon />
            </IconButton>
          )}
          <Stack
            onClick={() => {
              const path = ReplaceUrlVariable(paths.bingoCard.root, {
                id: report.id,
              });
              navigate(path);
            }}
            spacing={0.5}
            sx={{ px: 2, pt: 1, minHeight: 80, cursor: 'pointer' }}
            alignItems="flex-start"
          >
            <Typography variant="userTitle" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
              <Stack direction="row" spacing={1} alignItems={'center'}>
                {!report.isPersonalReport ? <Public /> : <LockOutlined />}
                <Typography variant={'tableMainTitile'}>{`${report.title}`}</Typography>
              </Stack>
            </Typography>
            <Typography variant="value" align="justify">
              {report.description}
            </Typography>
          </Stack>
          {(canDelete || report?.createdBy == accounts[0].username) && (
            <CustomPopover open={popover.open} onClose={popover.onClose} arrow="right-top" sx={{ minWidth: 140 }}>
              {(report?.createdBy == accounts[0].username || canDelete) && (
                <MenuItem
                  onClick={() => {
                    popover.onClose();
                    confirm.onTrue();
                  }}
                >
                  {t('label.edit')}
                </MenuItem>
              )}
              <MenuItem
                onClick={() => {
                  popover.onClose();
                  deleteConfirm.onTrue();
                }}
              >
                {t('label.delete')}
              </MenuItem>
            </CustomPopover>
          )}
        </>
      </CustomCard>
      <ConfirmDialog
        open={deleteConfirm.value}
        onClose={() => {
          // setSelectedReport(null);
          deleteConfirm.onFalse();
        }}
        title={t('label.delete')}
        content={t('messages.are_you_sure_want_to_delete')}
        action={
          <LoadingButton
            variant="contained"
            onClick={async () => {
              try {
                startLoadingState();
                if (report) {
                  await mutateAsync(report.id);
                }
                deleteConfirm.onFalse();
              } catch (err) {
              } finally {
                stopLoadingState();
              }
            }}
            loading={isLoading}
          >
            {t('label.delete')}
          </LoadingButton>
        }
      />
      <ModifyReport
        initialData={report}
        open={confirm.value}
        onClose={() => {
          confirm.onFalse();
        }}
      />
    </>
  );
}
