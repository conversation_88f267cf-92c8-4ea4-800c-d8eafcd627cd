import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/react-hierarchy-chart/dist/esm/index.js
var import_react = __toESM(require_react());
function styleInject(css, ref) {
  if (ref === void 0) ref = {};
  var insertAt = ref.insertAt;
  if (!css || typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z$1 = ".react-hierarchy-node-container {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n.react-hierarchy-node-container-vertical {\n  flex-direction: column;\n}\n.react-hierarchy-node-container .react-hierarchy-node {\n  display: flex;\n  align-items: center;\n}\n.react-hierarchy-node-container .react-hierarchy-node .node-line {\n  background-color: darkgrey;\n}\n.react-hierarchy-node-container .react-hierarchy-node .node-line-horizontal {\n  width: 1em;\n  height: 0.1em;\n}\n.react-hierarchy-node-container .react-hierarchy-node .node-line-vertical {\n  height: 1em;\n  width: 0.1em;\n}\n.react-hierarchy-node-container .react-hierarchy-node .node-box {\n  display: flex;\n  align-items: center;\n  color: black;\n  border-radius: 0.2em;\n  box-shadow: 0.05em 0.05em 0.2em 0.05em rgba(0, 0, 0, 0.168627451);\n}\n.react-hierarchy-node-container .react-hierarchy-node-horizontal {\n  padding: 1em 0;\n}\n.react-hierarchy-node-container .react-hierarchy-node-vertical {\n  flex-direction: column;\n  padding: 0 1em;\n}\n.react-hierarchy-node-container .react-hierarchy-children {\n  display: flex;\n  flex: 1;\n}\n.react-hierarchy-node-container .react-hierarchy-children-horizontal {\n  flex-direction: column;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container {\n  display: flex;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container:first-child > .lines-container .lines:first-child {\n  background-color: transparent;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container:last-child > .lines-container .lines:last-child {\n  background-color: transparent;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container-vertical {\n  flex-direction: column;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container .lines {\n  flex: 1;\n  background-color: darkgrey;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container .lines-container {\n  display: flex;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container .lines-container-vertical .lines {\n  height: 0.1em;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container .lines-container-horizontal {\n  flex-direction: column;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container .lines-container-horizontal .lines {\n  width: 0.1em;\n  background-color: darkgrey;\n}\n.react-hierarchy-node-container .react-hierarchy-children .children-container .m-line {\n  width: 0.1em;\n  height: 0.1em;\n  background-color: darkgrey;\n}";
styleInject(css_248z$1);
var HierarchyNode = function(_a) {
  var _b, _c;
  var node = _a.node, direction = _a.direction, randerNode = _a.randerNode, hasParent = _a.hasParent;
  return import_react.default.createElement(
    "div",
    { className: "react-hierarchy-node-container react-hierarchy-node-container-".concat(direction) },
    import_react.default.createElement(
      "div",
      { className: "react-hierarchy-node react-hierarchy-node-".concat(direction) },
      hasParent && import_react.default.createElement("div", { className: "node-line node-line-".concat(direction) }),
      import_react.default.createElement("div", { className: "node-box ngx-hierarchy-border ".concat(node.cssClass) }, randerNode(node)),
      ((_b = node === null || node === void 0 ? void 0 : node.childs) === null || _b === void 0 ? void 0 : _b.length) ? import_react.default.createElement("div", { className: "node-line node-line-".concat(direction) }) : import_react.default.createElement(import_react.default.Fragment, null)
    ),
    ((_c = node.childs) === null || _c === void 0 ? void 0 : _c.length) && import_react.default.createElement("div", { className: "react-hierarchy-children react-hierarchy-children-".concat(direction) }, node.childs.map(function(child, index) {
      var _a2;
      (((_a2 = node.childs) === null || _a2 === void 0 ? void 0 : _a2.length) || 0) - 1 === index;
      return import_react.default.createElement(
        "div",
        { className: "children-container children-container-".concat(direction), key: "HierarchyNodeChild".concat(child.key || Date.now) },
        import_react.default.createElement(
          "div",
          { className: "lines-container lines-container-".concat(direction) },
          import_react.default.createElement("div", { className: "lines" }),
          import_react.default.createElement("div", { className: "m-line" }),
          import_react.default.createElement("div", { className: "lines" })
        ),
        import_react.default.createElement(HierarchyNode, { node: child, direction, randerNode, hasParent: true })
      );
    }))
  );
};
var css_248z = ".react-hierarchy-container {\n  display: flex;\n}\n.react-hierarchy-container .react-hierarchy-root-vertical {\n  display: flex;\n  margin-bottom: 2%;\n  width: 100%;\n}\n.react-hierarchy-container .react-hierarchy-root-horizontal {\n  display: flex;\n  width: 100%;\n}\n.react-hierarchy-container .ngx-hierarchy-border {\n  border: 1px solid darkgrey;\n}";
styleInject(css_248z);
var ReactHierarchy = function(_a) {
  var nodes = _a.nodes, direction = _a.direction, randerNode = _a.randerNode;
  return import_react.default.createElement("div", { className: "react-hierarchy-container", key: Date.now() }, nodes.map(function(node, index) {
    return import_react.default.createElement(
      "div",
      { className: "react-hierarchy-root-".concat(direction), key: "HierarchyNodeP".concat(node.key || Date.now) },
      import_react.default.createElement(HierarchyNode, { node, direction, randerNode })
    );
  }));
};
export {
  HierarchyNode,
  ReactHierarchy as ReactHiererchyChart
};
//# sourceMappingURL=react-hierarchy-chart.js.map
