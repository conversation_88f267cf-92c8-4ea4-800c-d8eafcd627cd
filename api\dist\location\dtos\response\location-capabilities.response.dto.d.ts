import { BaseCoreSolutionResponseDto, BasicLocationTypeResponseDto } from 'src/metadata/dtos';
import { CAPABILITY_LEVEL_ENUM, CAPABILITY_STATUS_ENUM, CAPABILITY_TYPE_ENUM, INTERNAL_CAPABILITY_STATUS_ENUM, LEASE_OWNERSHIP_STATUS_TYPE, LOCATION_STATUS_TYPE, PROVIDER_ENUM } from 'src/shared/enums';
declare class CategoryResponseDto {
    title: string;
}
declare class MasterCapabilityResponseDto {
    capability: string;
    product: string;
    category: CategoryResponseDto;
    subCategory: string;
    capabilityType: CAPABILITY_TYPE_ENUM;
    level: CAPABILITY_LEVEL_ENUM;
    verticals: string[];
}
declare class CapabilityDetailResponseDto {
    id: number;
    status: CAPABILITY_STATUS_ENUM;
    internalStatus: INTERNAL_CAPABILITY_STATUS_ENUM;
    statusDate: string;
    provider: PROVIDER_ENUM;
    providerName: string;
    capabilityDetail: MasterCapabilityResponseDto;
}
declare class LocationDetailResponseDto {
    id: number;
    locationName: string;
    entityId: number;
    entityTitle: string;
    status: LOCATION_STATUS_TYPE;
    statusDate: string;
    leaseOwnershipStatus: LEASE_OWNERSHIP_STATUS_TYPE;
    locationType: BasicLocationTypeResponseDto;
    coreSolution: BaseCoreSolutionResponseDto;
}
export declare class LocationCapabilitiesResponseDto {
    locationDetail: LocationDetailResponseDto;
    capabilityList: CapabilityDetailResponseDto[];
}
export {};
