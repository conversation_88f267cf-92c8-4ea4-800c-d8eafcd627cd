import { useEffect, useState } from 'react';
import { Box, Card } from '@mui/material';
import BingoCardFilterDrawer from './bingo-card-filter';
import { KeyValue } from '@/shared/models';
import { CAPABILITY_FILTER_CONFIG, filterheaderBingoCard } from '@/modules/capabilities/config';
import BingoHeirarchyStructure from './bingo-heirarchy';
import BingoFlatStructure from './bingo-flat';
import CustomTableFilterChip from '@/components/custom-tables/custom-table-filter-chip';
import { Capabilities, LocationCapabilityList } from '@/shared/models/bingo-card.model';
import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';
import { VIEW_TYPE } from '@/shared/enum/capability.enum';
import { LoadingScreen } from '@/components/loading-screen';

import { CARD_STYLE } from '@/modules/location-setup/components/location-details/sections';

interface Props {
  showFilterDrawer?: boolean;
  setShowFilterDrawer?: React.Dispatch<React.SetStateAction<boolean>>;
  filters: Record<string, KeyValue[]>;
  setFilters: React.Dispatch<React.SetStateAction<Record<string, KeyValue[]>>>;
  legCapabilityGroup?: any;
  locationCapabilityList?: LocationCapabilityList[];
  businessEntityHierarchy: TreeViewDataType | null;
  onShowAvailableChange?: React.Dispatch<React.SetStateAction<boolean>>;
  rootBusinessEntityId?: any;
  setReset: React.Dispatch<React.SetStateAction<boolean>>;
  onViewChange?: React.Dispatch<React.SetStateAction<string>>;
  isLoading: boolean;
  viewType: string;
  presentCapabilties?: Capabilities[];
}

const BingoCardComponent = ({
  showFilterDrawer,
  setShowFilterDrawer,
  filters,
  setFilters,
  legCapabilityGroup,
  locationCapabilityList,
  businessEntityHierarchy,
  onShowAvailableChange,
  rootBusinessEntityId,
  setReset,
  isLoading,
  presentCapabilties,
  onViewChange,
  viewType,
}: Props) => {
  const [tempFilters, setTempFilters] = useState<Record<string, KeyValue[]>>({});
  const [showFilterChip, setShowFilterChip] = useState(false);
  // const [viewType, setViewType] = useState<string>(VIEW_TYPE.FLAT);
  const [showAvailable, setShowAvailable] = useState(false);
  const [columnCount, setColumnCount] = useState(0);
  // const [newFilters, setNewFilters] = useState<Record<string, KeyValue[]>>({});

  const handleFilterChange = (key: string, value: KeyValue[]) => {
    setTempFilters((prev) => ({ ...prev, [key]: value }));
  };

  useEffect(() => {
    let count = 0;
    legCapabilityGroup?.legs?.map((x: any) => {
      count = count + x.capabilities?.length;
    });
    setColumnCount(count + 1);
  }, [legCapabilityGroup]);

  const applyFilters = () => {
    const appliedFilters = Object.fromEntries(Object.entries(tempFilters).filter(([_, values]) => values.length > 0));
    setFilters(appliedFilters);
    // setNewFilters(appliedFilters);
    setShowFilterChip(Object.keys(appliedFilters).length > 0);
    setShowFilterDrawer?.(false);
  };
  const handleClearFilter = (key: string, value?: string | number | null) => {
    setFilters((prev) => {
      const updated = { ...prev };
      if (value !== undefined) {
        updated[key] = updated[key]?.filter((v) => v.label !== value);
      }
      if (!updated[key]?.length) delete updated[key];
      return updated;
    });

    setTempFilters((prev) => {
      const updated = { ...prev };
      if (value !== undefined) {
        updated[key] = updated[key]?.filter((v) => v.label !== value);
      }
      if (!updated[key]?.length) delete updated[key];
      return updated;
    });

    const hasFilters = Object.keys(filters).some((key) => filters[key].length > 0);
    setShowFilterChip(hasFilters);
    const skipReset = ['region', 'cluster', 'country', 'area'];
    if (!skipReset.includes(key)) {
      setReset(true);
    }
  };
  const resetFilters = () => {
    setTempFilters({});
    setFilters({});
    setShowFilterChip(false);
    setShowFilterDrawer?.(false);
    const skipReset = ['region', 'cluster', 'country', 'area'];
    if (!Object.keys(filters).every((key) => skipReset.includes(key))) {
      setReset(true);
    }
  };

  const handleClearAllFilters = () => {
    setFilters({});
    setTempFilters({});
    setShowFilterChip(false);
    const skipReset = ['region', 'cluster', 'country', 'area'];
    if (!Object.keys(filters).every((key) => skipReset.includes(key))) {
      setReset(true);
    }
  };

  const handleShowAvailableChange = (showAvailable: any) => {
    onShowAvailableChange?.(showAvailable);
    setShowAvailable(showAvailable);
  };

  const handleViewChange = (viewType: any) => {
    onViewChange?.(viewType);
    // setViewType(viewType);
  };

  return (
    <Box>
      {showFilterChip && (
        <CustomTableFilterChip
          filters={filters || []}
          onClearFilter={handleClearFilter}
          onClearAll={handleClearAllFilters}
          filterConfig={CAPABILITY_FILTER_CONFIG}
        />
      )}

      {isLoading ? (
        <Card sx={CARD_STYLE}>
          <Box sx={{ minHeight: '46vh', alignContent: 'center' }}>
            {' '}
            <LoadingScreen sx={{ boxShadow: 'none' }} />
          </Box>
        </Card>
      ) : (
        viewType &&
        legCapabilityGroup?.legs?.length > 0 &&
        (viewType === VIEW_TYPE.HIERARCHICAL ? (
          <BingoHeirarchyStructure
            legCapabilityGroup={legCapabilityGroup}
            locationCapabilityList={locationCapabilityList}
            businessEntityHierarchy={businessEntityHierarchy}
            onShowAvailableChange={(show) => handleShowAvailableChange(show)}
            rootBusinessEntityId={rootBusinessEntityId}
            showAvailable={showAvailable}
            isLoading={isLoading}
            columnCount={columnCount}
            onViewChange={(viewType) => handleViewChange(viewType)}
            viewType={viewType}
          />
        ) : (
          <BingoFlatStructure
            legCapabilityGroup={legCapabilityGroup}
            locationCapabilityList={locationCapabilityList}
            onShowAvailableChange={(show) => handleShowAvailableChange(show)}
            showAvailable={showAvailable}
            isLoading={isLoading}
            columnCount={columnCount}
            onViewChange={(viewType) => handleViewChange(viewType)}
            viewType={viewType}
          />
        ))
      )}

      <BingoCardFilterDrawer
        open={showFilterDrawer || false}
        onClose={() => setShowFilterDrawer?.(false)}
        filters={tempFilters}
        onFilterChange={handleFilterChange}
        onSearch={applyFilters}
        onCancel={resetFilters}
        locationCapabilityList={presentCapabilties ?? []}
        tableHeaders={filterheaderBingoCard}
        tableData={[]}
      />
    </Box>
  );
};

export default BingoCardComponent;
