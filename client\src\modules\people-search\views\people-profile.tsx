import CustomBreadcrumbs from '@/components/custom-breadcrumbs/custom-breadcrumbs.tsx';
import Form<PERSON>rovider, { RHFTextField } from '@/components/hook-form';
import { LoadingScreen } from '@/components/loading-screen';
import Scrollbar from '@/components/scrollbar';
import { useLoading } from '@/hooks/use-loading';
import { useTranslate } from '@/locales/use-locales';
import { useSearchParams } from '@/routes/hooks';
import { paths } from '@/routes/paths.ts';
import { UserDetail } from '@/shared/models';
import { getPeopleProfile } from '@/shared/services';
import { getIcon } from '@/shared/utils/get-icon.ts';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Card, List, Stack, Typography } from '@mui/material';
import { Grid } from '@mui/system';
import { enqueueSnackbar } from 'notistack';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router';
import * as Yup from 'yup';
import UserActionMessage from '../components/custom-list-with-message';

export default function PeopleProfile() {
  const { t } = useTranslate();
  const navigate = useNavigate();
  const searchParams = useSearchParams();
  const email = searchParams.get('email') ?? '';
  const isAAD = searchParams.get('isAAD') === 'true';
  const [dataList, setDataList] = useState([]);
  const [emptyResponseMsg, setEmptyResponseMsg] = useState('empty_state_messages.search_user_to_view_result');

  const EventSchema = Yup.object().shape({
    isAdSearch: Yup.boolean().default(false),
    email: Yup.string()
      .email(t('error_messages.valid_email'))
      .default('')
      .when('isAdSearch', {
        is: false,
        then: (schema) => schema.required(t('error_messages.email_required')),
        otherwise: (schema) => schema.notRequired(),
      }),
    userDetails: Yup.object<UserDetail>().when('isAdSearch', {
      is: true,
      then: (schema) => schema.required(t('error_messages.email_required')),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  const methods = useForm({
    resolver: yupResolver(EventSchema),
    defaultValues: {
      isAdSearch: false,
      email: '',
      userDetails: undefined,
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    setValue,
    getValues,
    reset,
  } = methods;

  // const isAdSearch = getValues('isAdSearch') ?? true;
  // const userDetails: any = getValues('userDetails');
  // const loginId = userDetails?.loginId ?? '';
  // const fullName = userDetails?.firstName + ' ' + userDetails?.lastName;

  const { setLoading, setMessage } = useLoading();
  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('label.searching'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };

  const { mutateAsync } = useMutation({
    mutationFn: (loginId: string) => getPeopleProfile(loginId),
    onSuccess: (response: any) => {
      response?.data?.length === 0 && setEmptyResponseMsg('empty_state_messages.user_has_no_assignment');
      setDataList(response?.data);
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });

  const { data: peopleProfileList, isFetching: _isPeopleProfileListFetching } = useQuery({
    queryKey: ['people-profile-list'],
    enabled: !!(email && isAAD),
    queryFn: () => getPeopleProfile(email),
    onSuccess(response) {
      response?.data?.length === 0 && setEmptyResponseMsg('empty_state_messages.user_has_no_assignment');
      setDataList(response.data);
      stopLoadingState();
    },
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
    keepPreviousData: false,
  });

  const renderList = (
    <Scrollbar>
      <List disablePadding>
        {dataList?.map((item: any, index) => (
          <UserActionMessage key={`people-search-${item.type}-${index}`} type={item.type} details={item.details} />
        ))}
      </List>
    </Scrollbar>
  );

  const onSubmit = handleSubmit(
    async (data: any) => {
      startLoadingState();

      const loginId = data.isAdSearch ? data.userDetails?.loginId : (data.email ?? '');
      try {
        await mutateAsync(loginId);
      } catch (err) {
        console.log(err);
      }
      // reset();

      stopLoadingState();
    },
    (errors) => {
      console.log(errors);
    },
  );
  const onReset = () => {
    reset();
    setEmptyResponseMsg('empty_state_messages.search_user_to_view_result');
    setDataList([]);
  };

  if (_isPeopleProfileListFetching) {
    return <LoadingScreen sx={{ minHeight: 300 }} />;
  }

  return (
    <>
      <Box px={{ md: 2 }}>
        <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pr: 1 }}>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent="space-between" alignItems="center">
            <CustomBreadcrumbs
              heading={t('headings.people_profile')}
              links={[
                { name: t('label.home'), href: paths.root },
                { name: t('headings.people_profile'), href: paths.peopleProfile.root },
              ]}
            />
            <Stack direction="row">
              <Box
                component="span"
                display="flex"
                alignItems="center"
                sx={{ cursor: 'pointer', gap: 1, mr: 1 }}
                onClick={() => navigate(-1)}
              >
                <Box component="img" src={getIcon('backBtn')} sx={{ width: 24, height: 16 }} />
                <Typography variant="value">{t('btn_name.back')}</Typography>
              </Box>
            </Stack>
          </Stack>
        </Card>
      </Box>

      <Grid px={{ md: 2 }} container spacing={2} mt={2}>
        <Box overflow="hidden" flex={1}>
          <Card sx={{ border: 'solid 1px #E8D6D6', borderRadius: '7px', paddingTop: 2, minHeight: 300 }}>
            <Box mb={2} px={2}>
              <FormProvider methods={methods} onSubmit={onSubmit}>
                {/* <ContactVariantSelector getValues={getValues} setValue={setValue} /> */}
                {!isAAD && (
                  <Grid container spacing={1} alignItems="flex-start">
                    {' '}
                    {/* align top to keep layout stable */}
                    <Grid size={{ xs: 12, md: 4, lg: 3 }}>
                      {/* {isAdSearch ? (
                      <AdSearchVariant
                        key={userInputKey}
                        size="small"
                        loginId={email}
                        setValue={setValue}
                        errors={errors}
                      />
                    ) : ( */}
                      <RHFTextField
                        name="email"
                        placeholder={t('placeholder.enter_email')}
                        label={<span>{t('label.email')} *</span>}
                        fullWidth
                        size="small"
                        error={Boolean(errors.email)}
                        helperText={errors.email?.message}
                        onChange={(event) => {
                          setValue('email', event.target.value, { shouldValidate: true });
                        }}
                      />
                      {/* )} */}
                    </Grid>
                    <Grid size={{ xs: 12, md: 'auto' }}>
                      <Stack direction="row" spacing={1} mt={{ xs: 1, md: 0 }}>
                        <Button
                          color="primary"
                          onClick={onReset}
                          sx={{
                            px: 3,
                            borderRadius: 3,
                            border: '1px solid gray',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {t('btn_name.reset')}
                        </Button>
                        <LoadingButton
                          onClick={onSubmit}
                          variant="contained"
                          disabled={isSubmitting}
                          sx={{
                            borderRadius: 3,
                            minWidth: '38%',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {t('btn_name.search')}
                        </LoadingButton>
                      </Stack>
                    </Grid>
                  </Grid>
                )}
              </FormProvider>
            </Box>
            {dataList.length == 0 ? (
              <Stack flexGrow={1} mt={12} alignItems="center" justifyContent="center">
                <Typography variant="h6" component="span" sx={{ color: 'text.disabled', textAlign: 'center' }}>
                  {t(emptyResponseMsg)}
                </Typography>
              </Stack>
            ) : (
              renderList
            )}
          </Card>
        </Box>
      </Grid>
    </>
  );
}
