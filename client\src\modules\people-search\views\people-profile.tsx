import { AdSearchVariant } from '@/components/contact-management/modify/contact-modify-variations';
import { ContactVariantSelector } from '@/components/contact-management/modify/sections';
import CustomBreadcrumbs from '@/components/custom-breadcrumbs/custom-breadcrumbs.tsx';
import FormProvider, { RHFTextField } from '@/components/hook-form';
import { LoadingScreen } from '@/components/loading-screen';
import Scrollbar from '@/components/scrollbar';
import { MasonryLayout } from '@/components/masonry-layout';
import { useLoading } from '@/hooks/use-loading';
import { useTranslate } from '@/locales/use-locales';
import { useSearchParams } from '@/routes/hooks';
import { paths } from '@/routes/paths.ts';
import { USER_TYPE_DISPLAY } from '@/shared/enum/permission.enum';
import { UserDetail } from '@/shared/models';
import { getPeopleProfile } from '@/shared/services';
import { getIcon } from '@/shared/utils/get-icon.ts';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Card, Stack, Typography, Switch, FormControlLabel } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Grid } from '@mui/system';
import { enqueueSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import { useLocation, useNavigate } from 'react-router';
import * as Yup from 'yup';
import UserActionMessage from '../components/custom-list-with-message';

export default function PeopleProfile() {
  const theme = useTheme();
  const { t } = useTranslate();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = useSearchParams();
  const email = searchParams.get('email') ?? '';
  const [dataList, setDataList] = useState([]);
  const [userInputKey, setUserInputKey] = useState(0);
  const [hasSearched, setHasSearched] = useState(false);
  const [emptyResponseMsg, setEmptyResponseMsg] = useState('empty_state_messages.search_user_to_view_result');
  const [useAdvancedLayout, setUseAdvancedLayout] = useState(true);

  const EventSchema = Yup.object().shape({
    isAdSearch: Yup.boolean().default(true),
    email: Yup.string()
      .email(t('error_messages.valid_email'))
      .default('')
      .when('isAdSearch', {
        is: false,
        then: (schema) => schema.required(t('error_messages.email_required')),
        otherwise: (schema) => schema.notRequired(),
      }),
    userDetails: Yup.object<UserDetail>().when('isAdSearch', {
      is: true,
      then: (schema) => schema.required(t('error_messages.email_required')),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  const methods = useForm({
    resolver: yupResolver(EventSchema),
    mode: 'onSubmit',
    defaultValues: {
      isAdSearch: true,
      email: '',
      userDetails: undefined,
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    setValue,
    getValues,
    reset,
  } = methods;

  const isAdSearch = getValues('isAdSearch') ?? true;
  const userDetails: any = getValues('userDetails');
  const loginId = userDetails?.loginId ?? '';
  const firstName = userDetails?.firstName ?? '';
  const lastName = userDetails?.lastName ?? '';
  const role = userDetails?.role ?? '';
  const jobTitle = userDetails?.jobTitle ?? '';
  const emailAddress = email !== '' ? email : getValues('email');

  useEffect(() => {
    onReset();
    setDataList([]);
    setHasSearched(false);
  }, [location.search]);

  useEffect(() => {
    setUserInputKey((prev) => prev + 1);
    setDataList([]);
    setHasSearched(false);
    setValue('userDetails', undefined as any, { shouldValidate: false, shouldDirty: false });
    setValue('email', '', { shouldValidate: false, shouldDirty: false });
  }, [isAdSearch]);

  const { setLoading, setMessage } = useLoading();
  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('label.searching'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };

  const { mutateAsync } = useMutation({
    mutationFn: (loginId: string) => getPeopleProfile(loginId),
    onSuccess: (response: any) => {
      response?.data?.length === 0 && setEmptyResponseMsg('empty_state_messages.user_has_no_assignment');
      setDataList(response?.data);
      setHasSearched(true);
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });

  const { isFetching: _isPeopleProfileListFetching } = useQuery({
    queryKey: ['people-profile-list', email],
    enabled: !!email,
    queryFn: () => getPeopleProfile(email),
    onSuccess(response) {
      response?.data?.length === 0 && setEmptyResponseMsg('empty_state_messages.user_has_no_assignment');
      setDataList(response.data);
      setHasSearched(true);
      stopLoadingState();
    },
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
    keepPreviousData: false,
  });

  // CSS-based masonry layout for auto-adjusting card positions
  const CSSMasonryLayout = ({ children }: { children: React.ReactNode[] }) => {
    return (
      <Box
        sx={{
          columnCount: { xs: 1, md: 2 },
          columnGap: 2,
          columnFill: 'balance',
          '& > *': {
            breakInside: 'avoid',
            marginBottom: 2,
            display: 'inline-block',
            width: '100%',
          },
        }}
      >
        {children}
      </Box>
    );
  };

  // Prepare card data
  const cardData = Object.entries(
    dataList?.reduce((acc: Record<string, any[]>, item: any) => {
      const type = item.type;
      const detailsArray = Array.isArray(item.details) ? item.details : [item.details];
      if (!acc[type]) acc[type] = [];
      acc[type].push(...detailsArray);
      return acc;
    }, {}),
  ).map(([type, groupedDetails]) => (
    <Card
      key={type}
      sx={{
        border: 'solid 1px #E8D6D6',
        borderRadius: '7px',
        py: 2,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-2px)',
        },
      }}
    >
      <Typography variant="mainTitle" p={2}>
        {USER_TYPE_DISPLAY[type as keyof typeof USER_TYPE_DISPLAY]}
      </Typography>
      <Grid container spacing={2}>
        {groupedDetails.map((detail, index) => (
          <Grid size={{ xs: 12 }} key={index}>
            <UserActionMessage type={type} details={detail} />
          </Grid>
        ))}
      </Grid>
    </Card>
  ));

  const renderList = (
    // <Scrollbar>
    //   <Box sx={{ mb: 2 }}>
    //     <FormControlLabel
    //       control={
    //         <Switch
    //           checked={useAdvancedLayout}
    //           onChange={(e) => setUseAdvancedLayout(e.target.checked)}
    //           color="primary"
    //         />
    //       }
    //       label={`${useAdvancedLayout ? 'Advanced' : 'CSS'} Masonry Layout`}
    //       sx={{ ml: 1 }}
    //     />
    //   </Box>

    //   {useAdvancedLayout ? (
    //     <MasonryLayout columns={2} gap={16} minColumnWidth={300}>
    //       {cardData}
    //     </MasonryLayout>
    //   ) : (
    <CSSMasonryLayout>{cardData}</CSSMasonryLayout>
    //   )}
    // </Scrollbar>
  );

  const onSubmit = handleSubmit(
    async (data: any) => {
      startLoadingState();

      const loginId = data.isAdSearch ? data.userDetails?.loginId : (data.email ?? '');
      try {
        await mutateAsync(loginId);
      } catch (err) {
        console.log(err);
      }
      stopLoadingState();
    },
    (errors) => {
      console.log(errors);
    },
  );
  const onReset = () => {
    reset();
    setUserInputKey((prev) => prev + 1);
    setEmptyResponseMsg('empty_state_messages.search_user_to_view_result');
    setDataList([]);
  };

  if (_isPeopleProfileListFetching) {
    return <LoadingScreen sx={{ minHeight: 300 }} />;
  }

  return (
    <>
      <Box px={{ md: 2 }}>
        <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pr: 1 }}>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            flexWrap={{ xs: 'wrap', md: 'nowrap' }}
          >
            <CustomBreadcrumbs
              heading={t('headings.people_profile')}
              links={[
                { name: t('label.home'), href: paths.root },
                { name: t('headings.people_profile'), href: paths.peopleProfile.root },
              ]}
              sx={{ flexShrink: 1 }}
            />
            <Stack direction="row">
              <Box
                component="span"
                display="flex"
                alignItems="center"
                sx={{ cursor: 'pointer', gap: 1, mr: 1, ml: { xs: 'auto', md: 2 } }}
                onClick={() => navigate(-1)}
              >
                <Box component="img" src={getIcon('backBtn')} sx={{ width: 24, height: 16 }} />
                <Typography variant="value">{t('btn_name.back')}</Typography>
              </Box>
            </Stack>
          </Stack>
        </Card>
      </Box>
      {email === '' && (
        <Box px={{ md: 2 }} mt={2}>
          <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pr: 1 }}>
            <Box mb={2} px={2}>
              <FormProvider methods={methods} onSubmit={onSubmit}>
                <ContactVariantSelector getValues={getValues} setValue={setValue} />

                <Grid container spacing={1} alignItems="flex-start">
                  <Grid size={{ xs: 12, md: 4, lg: 3 }}>
                    {isAdSearch ? (
                      <AdSearchVariant
                        key={userInputKey}
                        size="small"
                        loginId={loginId}
                        setValue={setValue}
                        errors={errors}
                      />
                    ) : (
                      <RHFTextField
                        name="email"
                        placeholder={t('placeholder.enter_email')}
                        label={<span>{t('label.email')} *</span>}
                        fullWidth
                        size="small"
                        error={Boolean(errors.email)}
                        helperText={errors.email?.message}
                        onChange={(event) => {
                          setValue('email', event.target.value, { shouldValidate: false, shouldDirty: true });
                        }}
                      />
                    )}
                  </Grid>
                  <Grid size={{ xs: 12, md: 'auto' }}>
                    <Stack direction="row" spacing={1} mt={{ xs: 1, md: 0 }}>
                      <Button
                        color="primary"
                        onClick={onReset}
                        sx={{
                          px: 3,
                          borderRadius: 3,
                          border: '1px solid gray',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {t('btn_name.reset')}
                      </Button>
                      <LoadingButton
                        onClick={onSubmit}
                        variant="contained"
                        disabled={isSubmitting}
                        sx={{
                          borderRadius: 3,
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {t('btn_name.search')}
                      </LoadingButton>
                    </Stack>
                  </Grid>
                </Grid>
              </FormProvider>
            </Box>
          </Card>
        </Box>
      )}

      <Grid px={{ md: 2 }} container spacing={2} mt={2}>
        {!Boolean(errors.email) &&
          hasSearched &&
          (firstName || lastName || jobTitle || role || email || loginId || emailAddress) && (
            <Grid size={{ xs: 12, sm: 2.5 }}>
              <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pb: 4 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" p={2}>
                  <Typography variant="mainTitle">{t('headings.profile_details')}</Typography>
                </Box>
                <Stack spacing={1} sx={{ px: 2 }}>
                  {(firstName || lastName) && (
                    <>
                      <Typography variant="label">{t('label.name')}</Typography>
                      <Typography
                        variant="userSubTitle"
                        sx={{ color: theme.palette.text.primary, fontSize: '0.85rem' }}
                      >
                        {firstName} {lastName}
                      </Typography>
                    </>
                  )}

                  {jobTitle && (
                    <>
                      <Typography variant="label">{t('label.jobTitle')}</Typography>
                      <Typography
                        variant="userSubTitle"
                        sx={{ color: theme.palette.text.primary, fontSize: '0.85rem' }}
                      >
                        {jobTitle.toUpperCase()}
                      </Typography>
                    </>
                  )}

                  {role && (
                    <>
                      <Typography variant="label">{t('label.role')}</Typography>
                      <Typography
                        variant="userSubTitle"
                        sx={{ color: theme.palette.text.secondary, fontSize: '0.85rem' }}
                      >
                        {role.toUpperCase()}
                      </Typography>
                    </>
                  )}

                  {(email || loginId || emailAddress) && (
                    <>
                      <Typography variant="label">{t('label.email')}</Typography>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        {/* <Box component="img" src={getIcon('email', ExtentionEnum.PNG)} sx={{ height: 18 }} /> */}
                        <Typography
                          variant="body2"
                          sx={{
                            wordBreak: 'break-word',
                            '&:hover': { color: theme.palette.primary.main },
                          }}
                        >
                          {email || loginId || emailAddress}
                        </Typography>
                      </Stack>
                    </>
                  )}
                </Stack>
              </Card>
            </Grid>
          )}
        <Box overflow="hidden" flex={1}>
          {dataList.length == 0 ? (
            <Card sx={{ border: 'solid 1px #E8D6D6', borderRadius: '7px', paddingTop: 2, minHeight: 300 }}>
              <Stack flexGrow={1} mt={12} alignItems="center" justifyContent="center">
                <Typography variant="h6" component="span" sx={{ color: 'text.disabled', textAlign: 'center' }}>
                  {t(emptyResponseMsg)}
                </Typography>
              </Stack>
            </Card>
          ) : (
            renderList
          )}
        </Box>
      </Grid>
    </>
  );
}
