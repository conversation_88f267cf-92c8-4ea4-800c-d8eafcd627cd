import {
    HttpException,
    HttpStatus,
    Injectable,
} from '@nestjs/common';
import { LocationRepository, LocationIndustryVerticalRepository, PartnerBranchRepository } from '../repositories';
import { Attachment, CurrentContext, RequestContext } from 'src/shared/types';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper, multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import {
    ContactManagement,
    CreateLocationRequestDto,
    IndustryVerticals,
    LocationBasicDetailResponseDto,
    LocationCompleteDetailResponseDto,
    LocationDropdownResponseDto,
    LocationListResponseDto,
    PaginatedLocationListResponseDto,
    UpdateLocationStatusRequestDto,
} from '../dtos';
import { SetupNewLocationRequest } from '../validators';
import { ATTACHMENT_ENTITY_TYPE, CONTACT_DETAIL_OBJECT_TYPE, HIERARCHY_ENTITY_TYPE, HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE, LOCATION_STATUS_TYPE, LOCATION_TYPE_ENUM, PERMISSIONS } from 'src/shared/enums';
import { ContactDetailRepository } from 'src/contact-details/repositories';
import { LocationLifecycleManagementsTypeRepository } from 'src/metadata/repositories';
import { LocationFilterRequestDto } from '../dtos/request/location-filter-request.dto';
import { Pagination } from 'src/core/pagination';
import { ExcelSheetService, SharedAttachmentService, SharedPermissionService } from 'src/shared/services';
import { ATTACHMENT_REL_PATH } from 'src/shared/constants';
import { AdminApiClient, AttachmentApiClient, HistoryApiClient } from 'src/shared/clients';
import { toNumber } from 'lodash';
import { LocationWiseCapabilityRepository } from 'src/capability/repositories/location-wise-capability.repository';
import { LocationCapabilitiesResponseDto } from '../dtos/response/location-capabilities.response.dto';
import { BusinessEntityService } from 'src/business-entity/services';
import { ExportLocationWiseCapabilityFilterRequestDto } from 'src/capability/dtos';
import { UserPermissionRepository } from 'src/permission/repositories';
import moment from 'moment';

@Injectable()
export class LocationService {
    constructor(
        private readonly locationRepository: LocationRepository,
        private readonly locationIndustryVerticalRepository: LocationIndustryVerticalRepository,
        private readonly setupNewLocationRequest: SetupNewLocationRequest,
        private readonly databaseHelper: DatabaseHelper,
        private readonly contactDetailRepository: ContactDetailRepository,
        private readonly locationLifecycleManagementsTypeRepository: LocationLifecycleManagementsTypeRepository,
        private readonly sharedAttachmentService: SharedAttachmentService,
        private readonly attachmentApiClient: AttachmentApiClient,
        private readonly locationWiseCapabilityRepository: LocationWiseCapabilityRepository,
        private readonly businessEntityService: BusinessEntityService,
        private readonly partnerBranchRepository: PartnerBranchRepository,
        private readonly excelSheetService: ExcelSheetService,
        private readonly adminApiClient: AdminApiClient,
        private readonly permissionService: SharedPermissionService,
        private readonly userPermissionRepository: UserPermissionRepository,
        private readonly historyService: HistoryApiClient
    ) { }

    public async getAllLocations(searchTerm?: string): Promise<LocationDropdownResponseDto[]> {
        if (!searchTerm.trim()) {
            return [];
        }
        const locations = await this.locationRepository.getAllLocations(searchTerm);
        return multiObjectToInstance(LocationDropdownResponseDto, locations);
    }

    public async upsertLocation(
        createLocationDto: CreateLocationRequestDto,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {

        const hasLegalPermission = await this.permissionService.checkAnyPermission([PERMISSIONS.GLOBAL_LEGAL], currentContext.user, createLocationDto.entityId, null);

        if (!hasLegalPermission) {
            if (createLocationDto.id) {
                const hasPermission = await this.permissionService.checkAnyPermission([PERMISSIONS.GLOBAL_MANAGE, PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, createLocationDto.entityId, createLocationDto.id);

                if (!hasPermission) {
                    throw new HttpException('You don\'t have permission to update this location.', HttpStatus.FORBIDDEN);
                }
            } else {
                const hasPermission = await this.permissionService.checkAnyPermission([PERMISSIONS.GLOBAL_MANAGE], currentContext.user, createLocationDto.entityId, null);

                if (!hasPermission) {
                    throw new HttpException('You don\'t have permission to add new location in this entity.', HttpStatus.FORBIDDEN);
                }
            }
        }

        if (!hasLegalPermission && createLocationDto?.agentDetail) {
            throw new HttpException('You don\'t have permission to add agent detail.', HttpStatus.FORBIDDEN);
        }

        const validRequestData = await this.setupNewLocationRequest.validate(createLocationDto, hasLegalPermission);

        const { status, data, message } = validRequestData;

        if (status) {

            const { id, entityId, basicInformation } = createLocationDto;
            const { locationName } = basicInformation;

            const isLocationNameExist = await this.locationRepository.isLocationNameExit(entityId, locationName, id || null);

            if (isLocationNameExist) {
                throw new HttpException('Location name already exist.', HttpStatus.CONFLICT);
            }

            const { entityDetail } = data;

            const locationData = await this.transformLocationData(entityDetail, createLocationDto);

            if (id) {
                return await this.updateLocation(locationData, createLocationDto, currentContext);
            } else {
                return await this.addNewLocation(locationData, createLocationDto, currentContext);
            }
        }

        throw new HttpException(message, HttpStatus.NOT_FOUND);
    }

    private async addNewLocation(locationData: any, createLocationDto: CreateLocationRequestDto, currentContext: CurrentContext) {
        return await this.databaseHelper.startTransaction(async () => {

            const location = await this.locationRepository.createLocation(locationData, currentContext);

            const { contactManagements, competencyDetails, agentDetail, additionalDetails } = createLocationDto;

            const { industryVerticals } = competencyDetails || {}
            const { aggrements } = agentDetail || {};
            const { additionalDocuments } = additionalDetails || {};

            if (industryVerticals?.length) {
                await this.addNewIndutryVertical(industryVerticals, location.id, currentContext);
            }

            if (contactManagements?.length) {
                await this.addNewContactManagement(contactManagements, location.id, currentContext);
            }

            await this.uploadNewLocationDocuments(additionalDocuments, aggrements, location.id, currentContext);

            await this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: location.id,
                entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                action_performed: HISTORY_ACTION_TYPE.CREATE,
                action_date: new Date(),
                comments: 'New location added.',
                additional_info: {
                    locationData: createLocationDto
                }
            });

            return {
                message: 'New location added successfully. ',
                data: {
                    locationId: location.id,
                },
            };
        });
    }

    private async updateLocation(locationData: any, createLocationDto: CreateLocationRequestDto, currentContext: CurrentContext) {
        return await this.databaseHelper.startTransaction(async () => {

            const { id, deleteExistingCapabilities, contactManagements, competencyDetails, agentDetail, additionalDetails, deletedFileIds } = createLocationDto;

            const locationDetail = await this.locationRepository.getLocationDetailForUpdate(id);

            if (!locationDetail) {
                throw new HttpException('Location not found.', HttpStatus.NOT_FOUND);
            }

            await this.locationRepository.updateLocationDetailById(id, locationData, currentContext);

            const { industryVerticals } = competencyDetails || {}
            const { aggrements } = agentDetail || {};
            const { additionalDocuments } = additionalDetails || {};

            await this.updateIndustryVerticals(id, industryVerticals, currentContext);

            await this.updateContacts(id, contactManagements, currentContext);


            if (aggrements?.length) {
                const newAggrements = aggrements.filter(attachment => attachment.isNew || !attachment.file_id);

                if (newAggrements?.length) {
                    await this.sharedAttachmentService.addBulkAttachment(
                        newAggrements,
                        id,
                        ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS,
                        ATTACHMENT_REL_PATH.LOCATION_AGENT_AGREEMENTS,
                        currentContext.user.username
                    );
                }
            }

            if (additionalDocuments?.length) {
                const newAdditionalDocuments = additionalDocuments.filter(attachment => attachment.isNew || !attachment.file_id);

                if (newAdditionalDocuments?.length) {
                    await this.sharedAttachmentService.addBulkAttachment(
                        newAdditionalDocuments,
                        id,
                        ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS,
                        ATTACHMENT_REL_PATH.LOCATION_ADDITIONAL_DOCUMENTS,
                        currentContext.user.username
                    );
                }
            }

            if (deletedFileIds?.length) {
                await this.sharedAttachmentService.deleteBulkAttachmentByFileIds(deletedFileIds);
            }

            let history = [];

            if (deleteExistingCapabilities) {
                await this.locationWiseCapabilityRepository.deleteByLocationId(id, currentContext);
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: HISTORY_ACTION_TYPE.DELETE_ALL_CAPABILITIES,
                    action_date: new Date(),
                    comments: 'All existing capabilities deleted.',
                });
            }

            history.push({
                created_by: currentContext.user.username,
                entity_id: id,
                entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                action_performed: HISTORY_ACTION_TYPE.UPDATE,
                action_date: new Date(),
                comments: 'Location details updated.',
                additional_info: {
                    locationData: createLocationDto
                }
            });

            if (createLocationDto.basicInformation.locationStatus !== locationDetail.status) {
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: HISTORY_ACTION_TYPE.STATUS_CHANGE,
                    action_date: new Date(),
                    comments: `Location status changed from ${locationDetail.status} to ${createLocationDto.basicInformation.locationStatus}.`,
                    additional_info: {
                        old: {
                            status: locationDetail.status,
                            statusDate: locationDetail.statusDate,
                            statusComment: locationDetail.statusComment
                        },
                        new: {
                            status: createLocationDto.basicInformation.locationStatus,
                            statusDate: createLocationDto.basicInformation.statusDate,
                            statusComment: createLocationDto.basicInformation.locationStatusComment
                        }
                    }
                });
            }

            if (createLocationDto?.basicInformation?.statusDate && locationDetail?.statusDate && !moment(createLocationDto.basicInformation.statusDate).isSame(
                moment(locationDetail.statusDate),
                'day'
            )) {
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: HISTORY_ACTION_TYPE.UPDATE,
                    action_date: new Date(),
                    comments: `Location status date changed from ${locationDetail.statusDate} to ${createLocationDto.basicInformation.statusDate}.`,
                    additional_info: {
                        old: {
                            statusDate: locationDetail.statusDate
                        },
                        new: {
                            statusDate: createLocationDto.basicInformation.statusDate
                        }
                    }
                });
            }

            if (history.length) {
                await this.historyService.addBulkRequestHistory(history);
            }

            return {
                message: 'Location updated successfully.',
                data: {
                    locationId: id,
                },
            };
        });
    }

    private async updateIndustryVerticals(locationId: number, industryVerticals: IndustryVerticals[], currentContext: CurrentContext) {
        const existingIndustryVerticals = await this.locationIndustryVerticalRepository.getIndustryVerticalsByLocationId(locationId);

        if (industryVerticals?.length || existingIndustryVerticals?.length) {

            let newIndustryVerticals = [];
            let updateIndustryVerticals = [];
            let deleteIndustryVerticals = [];

            if (existingIndustryVerticals?.length) {

                const existingIndustryVerticalsMap = new Map(
                    existingIndustryVerticals.map(industryVertical => [industryVertical.industryVerticalCode, industryVertical])
                );

                if (industryVerticals?.length) {
                    for (const newIndustryVertical of industryVerticals) {
                        const existingIndustryVertical = existingIndustryVerticalsMap.get(newIndustryVertical.industryVerticalCode);

                        if (existingIndustryVertical) {
                            updateIndustryVerticals.push({
                                ...existingIndustryVertical,
                                expertUsers: newIndustryVertical.expertUsers,
                            });
                        } else {
                            newIndustryVerticals.push(newIndustryVertical);
                        }
                    }
                }

                deleteIndustryVerticals = existingIndustryVerticals.filter(industryVertical => !industryVerticals?.find(newIndustryVertical => newIndustryVertical.industryVerticalCode === industryVertical.industryVerticalCode))?.map(industryVertical => industryVertical.id);

            } else {
                newIndustryVerticals = industryVerticals;
            }

            if (newIndustryVerticals?.length) {
                await this.addNewIndutryVertical(newIndustryVerticals, locationId, currentContext);
            }

            if (updateIndustryVerticals?.length) {
                const updatePromises = updateIndustryVerticals.map(industryVertical => {
                    return this.locationIndustryVerticalRepository.updateIndustryVerticalById(
                        industryVertical.id,
                        {
                            expertUsers: industryVertical.expertUsers,
                        },
                        currentContext
                    );
                });

                await Promise.all(updatePromises);
            }

            if (deleteIndustryVerticals?.length) {
                await this.locationIndustryVerticalRepository.deleteBulkIndustryVertical(deleteIndustryVerticals, currentContext);
            }
        }
    }

    private async updateContacts(locationId, contactManagements: ContactManagement[], currentContext) {
        const existingLocationManagement =
            await this.contactDetailRepository.getContactDetailsByCondition({
                locationId: locationId,
                objectType: CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
            });

        // Prepare lists for add, update, and delete contacts
        const newEntries = [];
        const updatedEntries = [];
        const entriesToDelete = [];

        // Map existing management by objectId for quick lookup
        const existingManagementMap = new Map(
            existingLocationManagement.map(entry => [toNumber(entry.objectId), entry]),
        );

        for (const newEntry of contactManagements || []) {
            const existingEntry = existingManagementMap.get(newEntry.contactTypeId);

            if (existingEntry) {
                // If entry exists, update the entries
                if (newEntry.users?.length > 0) {
                    updatedEntries.push({
                        ...existingEntry,
                        userDetails: newEntry.users,
                    });
                } else {
                    // If userDetails length is 0, mark for deletion
                    entriesToDelete.push(existingEntry.id);
                }
            } else {
                // If entry does not exist, add to new entries
                newEntries.push({
                    locationId: locationId,
                    objectId: newEntry.contactTypeId,
                    objectType: CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
                    userDetails: newEntry.users,
                });
            }
        }

        // Add new entries
        if (newEntries.length > 0) {
            await this.contactDetailRepository.addBulkContacts(newEntries, currentContext);
        }

        // Update existing entries
        for (const updatedEntry of updatedEntries) {
            await this.contactDetailRepository.updateUserDetailsByCondition(
                { id: updatedEntry.id },
                updatedEntry.userDetails,
                currentContext,
            );
        }

        // Delete entries
        if (entriesToDelete.length > 0) {
            await this.contactDetailRepository.deleteByIds(entriesToDelete, currentContext);
        }
    }

    private async transformLocationData(entityDetail, createLocationDto: CreateLocationRequestDto) {
        const {
            id,
            coreSolutionId,
            locationTypeId,
            branchArchetypeCode,
            basicInformation,
            locationInformation,
            competencyDetails,
            agentDetail,
            contractDetail,
            locationLifecycleManagement,
            additionalDetails,
        } = createLocationDto;

        const {
            legalEntityid,
            locationStatusComment,
            statusDate,
            strategicClassificationCode,
            leaseOwnershipStatus,
            locationStatus,
            locationName,
            ...restBasicInformation
        } = basicInformation;

        const { industryVerticals, ...restCompetencyDetails } = competencyDetails || {};
        const { tags, additionalDocuments, ...restAdditionalDetails } = additionalDetails || {};
        const { aggrements, ...restAgentDetail } = agentDetail || {};

        let locationLifeCycleManagementTransformed = {};

        if (locationLifecycleManagement && Object.keys(locationLifecycleManagement).length > 0) {
            const {
                tiaBId,
                systemDetails,
                tiabSince,
                activationStageId,
                minimumFfwFteInPlaceId,
                tomOpsId,
                tomFinId,
                additionalRegistrationNumbers,
            } = locationLifecycleManagement;

            const locationLifeCycleManagementData =
                await this.locationLifecycleManagementsTypeRepository.getListByIds([
                    tiaBId,
                    activationStageId,
                    minimumFfwFteInPlaceId,
                    tomOpsId,
                    tomFinId,
                ]);

            locationLifeCycleManagementTransformed = {
                systemDetails,
                tiabSince,
                additionalRegistrationNumbers,
                tiaB: locationLifeCycleManagementData.find(l => l.id === tiaBId),
                activationStage: locationLifeCycleManagementData.find(l => l.id === activationStageId),
                minimumFfwFteInPlace: locationLifeCycleManagementData.find(
                    l => l.id === minimumFfwFteInPlaceId,
                ),
                tomOps: locationLifeCycleManagementData.find(l => l.id === tomOpsId),
                tomFin: locationLifeCycleManagementData.find(l => l.id === tomFinId),
            };
        }

        let countryDetail;

        if (!id) {
            if (entityDetail.entity_type !== HIERARCHY_ENTITY_TYPE.COUNTRY) {
                let parentEntities = await this.adminApiClient.getParentsOfEntity(entityDetail.id);
                const countryData = parentEntities.length ? parentEntities.find((entity) => entity.entity_type === HIERARCHY_ENTITY_TYPE.COUNTRY) : null;

                countryDetail = {
                    entityId: countryData?.id,
                    entityCode: countryData?.code,
                    entityTitle: countryData?.full_name,
                }

            } else {
                countryDetail = {
                    entityId: entityDetail.id,
                    entityCode: entityDetail.code,
                    entityTitle: entityDetail.full_name,
                }
            }
        }

        const locationData = {
            ...(!id && {
                entityId: entityDetail.id,
                entityCode: entityDetail.code,
                entityTitle: entityDetail.full_name,
                entityType: entityDetail.entity_type,
                coreSolutionId,
                countryDetail: {
                    entityId: countryDetail?.entityId || null,
                    entityCode: countryDetail?.entityCode || null,
                    entityTitle: countryDetail?.entityTitle || null,
                },
            }),
            locationTypeId,
            branchArchetypeCode: branchArchetypeCode || null,
            legalEntityId: legalEntityid || null,
            locationName: locationName,
            status: locationStatus,
            statusComment: locationStatusComment || null,
            statusDate: statusDate || null,
            strategicClassificationCode: strategicClassificationCode || null,
            leaseOwnershipStatus: leaseOwnershipStatus,
            latitude: locationInformation.latitude,
            longitude: locationInformation.longitude,
            googleListing: locationInformation?.googleListing || null,
            brands: locationInformation?.brands || null,
            ffLocationIds: locationInformation?.locationIds || null,
            tags: additionalDetails?.tags || [],
            otherDetails: {
                ...(restBasicInformation &&
                    Object.keys(restBasicInformation || {}).length > 0 && {
                    basicInformation: restBasicInformation,
                }),
                ...(restCompetencyDetails &&
                    Object.keys(restCompetencyDetails || {}).length > 0 && {
                    competencyDetails: restCompetencyDetails,
                }),
                ...(restAgentDetail && Object.keys(restAgentDetail).length > 0 && { agentDetail: restAgentDetail }),
                ...(contractDetail && Object.keys(contractDetail).length > 0 && { contractDetail }),
                ...(locationLifeCycleManagementTransformed &&
                    Object.keys(locationLifeCycleManagementTransformed).length > 0 && {
                    locationLifecycleManagement: locationLifeCycleManagementTransformed
                }),
                ...(restAdditionalDetails &&
                    Object.keys(restAdditionalDetails || {}).length > 0 && {
                    additionalDetails: restAdditionalDetails,
                }),
            },
        };

        return locationData;
    }

    private async addNewIndutryVertical(industryVerticals: any[], locationId: number, currentContext: CurrentContext) {
        if (industryVerticals?.length) {
            const industryVerticalData = industryVerticals.map(industryVertical => {
                return {
                    locationId,
                    industryVerticalCode: industryVertical.industryVerticalCode,
                    expertUsers: industryVertical.expertUsers,
                };
            });

            await this.locationIndustryVerticalRepository.addBulkIndustryVerticals(
                industryVerticalData,
                currentContext,
            );
        }
    }

    private async addNewContactManagement(contactManagements: any[], locationId: number, currentContext: CurrentContext) {
        if (contactManagements?.length) {
            const transformedManagementList = contactManagements.map(contactManagement => {
                return {
                    locationId,
                    objectId: contactManagement.contactTypeId,
                    objectType: CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
                    userDetails: contactManagement.users,
                };
            });

            if (transformedManagementList.length) {
                await this.contactDetailRepository.addBulkContacts(
                    transformedManagementList,
                    currentContext,
                );
            }
        }
    }

    private async uploadNewLocationDocuments(additionalDocuments: Attachment[], aggrements: Attachment[], locationId: any, currentContext: CurrentContext) {
        if (additionalDocuments?.length) {
            await this.sharedAttachmentService.supportingDocumentsActivity(
                additionalDocuments,
                locationId,
                ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS,
                ATTACHMENT_REL_PATH.LOCATION_ADDITIONAL_DOCUMENTS,
                currentContext.user.username
            );
        }

        if (aggrements?.length) {
            await this.sharedAttachmentService.supportingDocumentsActivity(
                aggrements,
                locationId,
                ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS,
                ATTACHMENT_REL_PATH.LOCATION_AGENT_AGREEMENTS,
                currentContext.user.username
            );
        }
    }

    public async getBasicLocationDetailById(id: number): Promise<LocationBasicDetailResponseDto> {
        const location = await this.locationRepository.getLocationById(id);

        if (!location) {
            throw new HttpException('Location not found', HttpStatus.NOT_FOUND);
        }

        return singleObjectToInstance(LocationBasicDetailResponseDto, location, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
        });
    }

    public async getCompleteLocationDetailById(
        id: number,
    ): Promise<LocationCompleteDetailResponseDto> {
        const locationDetail = await this.locationRepository.getCompleteLocationDetailById(id);

        if (!locationDetail) {
            throw new HttpException('Location not found', HttpStatus.NOT_FOUND);
        }

        const industryVerticals =
            await this.locationIndustryVerticalRepository.getIndustryVerticalsByLocationId(id);

        // Get contact management details
        const contactManagements = await this.contactDetailRepository.getDetailedContactsByCondition({
            locationId: id,
            objectType: CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
        });

        let competencyDetails = {};

        if (industryVerticals?.length) {
            competencyDetails = {
                ...competencyDetails,
                industryVerticals,
            };
        }

        if (locationDetail?.otherDetails?.competencyDetails?.sustainabilityExperts?.length) {
            competencyDetails = {
                ...competencyDetails,
                sustainabilityExperts:
                    locationDetail?.otherDetails?.competencyDetails?.sustainabilityExperts,
            };
        }

        const additionalDetails = {
            ...(locationDetail.tags?.length > 0 && { tags: locationDetail.tags }),
            ...(locationDetail?.otherDetails?.additionalDetails?.customer && {
                customer: locationDetail?.otherDetails.additionalDetails.customer,
            }),
            ...(locationDetail?.otherDetails?.additionalDetails?.usefulLinks?.length > 0 && {
                usefulLinks: locationDetail?.otherDetails.additionalDetails.usefulLinks,
            }),
        };

        let parentEntities = [];

        if (locationDetail.entityType !== HIERARCHY_ENTITY_TYPE.COUNTRY) {
            parentEntities = await this.adminApiClient.getParentsOfEntity(locationDetail.entityId);
        }

        const returnData: any = {
            id: locationDetail.id,

            entityDetail: {
                id: locationDetail.entityId,
                code: locationDetail.entityCode,
                title: locationDetail.entityTitle,
                type: locationDetail.entityType,
                countryId: locationDetail.entityType === HIERARCHY_ENTITY_TYPE.COUNTRY ? locationDetail.entityId : parentEntities.length ? parentEntities.find((entity) => entity.entity_type === HIERARCHY_ENTITY_TYPE.COUNTRY).id : null,
            },

            coreSolution: locationDetail.coreSolution,

            locationType: locationDetail.locationType,

            ...(locationDetail?.branchArchetype && { branchArchetype: locationDetail.branchArchetype }),

            basicInformation: {
                locationName: locationDetail.locationName || '',
                introduction: locationDetail?.otherDetails?.basicInformation?.introduction || '',
                locationStatus: locationDetail.status,
                leaseOwnershipStatus: locationDetail.leaseOwnershipStatus,
                address: locationDetail?.otherDetails?.basicInformation?.address || '',

                ...(locationDetail?.strategicClassification && {
                    strategicClassification: locationDetail.strategicClassification,
                }),

                ...(locationDetail?.legalEntity && { legalEntity: locationDetail.legalEntity }),

                ...(locationDetail?.statusComment && {
                    locationStatusComment: locationDetail.statusComment,
                }),

                ...(locationDetail?.statusDate && { statusDate: locationDetail.statusDate }),

                ...(locationDetail?.otherDetails?.basicInformation?.leaseExpirationDate && {
                    leaseExpirationDate: locationDetail?.otherDetails?.basicInformation?.leaseExpirationDate,
                }),
            },

            locationInformation: {
                latitude: locationDetail.latitude,
                longitude: locationDetail.longitude,

                ...(locationDetail?.ffLocationIds?.length && { locationIds: locationDetail.ffLocationIds }),

                ...(locationDetail?.brands?.length && { brands: locationDetail.brands }),

                ...(locationDetail?.googleListing && { googleListing: locationDetail.googleListing }),
            },

            ...(Object.keys(competencyDetails).length > 0 && { competencyDetails }),

            ...(locationDetail?.otherDetails?.agentDetail &&
                Object.keys(locationDetail?.otherDetails?.agentDetail).length > 0 && {
                agentDetail: locationDetail?.otherDetails?.agentDetail,
            }),

            ...(locationDetail?.otherDetails?.contractDetail &&
                Object.keys(locationDetail?.otherDetails?.contractDetail).length > 0 && {
                contractDetail: locationDetail?.otherDetails?.contractDetail,
            }),

            ...(locationDetail?.otherDetails?.locationLifecycleManagement &&
                Object.keys(locationDetail?.otherDetails?.locationLifecycleManagement).length > 0 && {
                locationLifecycleManagement: locationDetail?.otherDetails?.locationLifecycleManagement,
            }),

            ...(Object.keys(additionalDetails).length > 0 && { additionalDetails }),

            ...(contactManagements.length && { contactManagements }),
        };

        const [additionalDocuments, aggrements] = await Promise.all([
            this.attachmentApiClient.getAllAttachments(id, ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS),
            locationDetail.locationType.code === LOCATION_TYPE_ENUM.AGENT ?
                this.attachmentApiClient.getAllAttachments(id, ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS) : []
        ]);

        if (additionalDocuments?.length) {
            returnData.additionalDetails = {
                ...returnData.additionalDetails || {},
                additionalDocuments
            };
        }

        if (aggrements?.length) {
            returnData.agentDetail = {
                ...returnData.agentDetail || {},
                aggrements
            };
        }

        return singleObjectToInstance(LocationCompleteDetailResponseDto, returnData, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
        });
    }

    public async getLocationListByFilter(
        page: number,
        limit: number,
        orderBy: string,
        orderDirection: string,
        filterDto: LocationFilterRequestDto = {},
    ): Promise<PaginatedLocationListResponseDto> {

        if (filterDto?.entityIds?.length || filterDto?.hierarchyEntityId) {
            filterDto.entityIds = await this.businessEntityService.getBusinessEntitiesChildIds(
                filterDto?.hierarchyEntityId ? [filterDto.hierarchyEntityId] : filterDto.entityIds,
            );
        }

        const { rows, count } = await this.locationRepository.getLocationsByFilter(filterDto, page, limit, orderBy, orderDirection);

        const records = multiObjectToInstance(LocationListResponseDto, rows, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
        });

        return new Pagination({ records, total: count });
    }

    public async deleteLocation(id: number, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const locationDetail = await this.locationRepository.getLocationById(id);

        if (!locationDetail) {
            throw new HttpException('Location not found', HttpStatus.NOT_FOUND);
        }

        const hasPermission = await this.permissionService.checkAnyPermission([PERMISSIONS.GLOBAL_MANAGE], currentContext.user, locationDetail.entityId, null);

        if (!hasPermission) {
            throw new HttpException('You don\'t have permission to delete this location.', HttpStatus.FORBIDDEN);
        }

        return await this.databaseHelper.startTransaction(async () => {
            await this.locationRepository.deleteLocation(id, currentContext);

            await this.locationIndustryVerticalRepository.deleteIndustryVerticalByLocationId(id, currentContext);
            await this.partnerBranchRepository.deletePartnerBranches(id, currentContext);
            await this.contactDetailRepository.deleteByLocationId(id, currentContext);
            await this.locationWiseCapabilityRepository.deleteByLocationId(id, currentContext);
            await this.userPermissionRepository.deleteByLocationId(id, currentContext);

            const [additionalDocuments, aggrements] = await Promise.all([
                this.attachmentApiClient.getAllAttachments(id, ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS),
                locationDetail.locationType.code === LOCATION_TYPE_ENUM.AGENT ?
                    this.attachmentApiClient.getAllAttachments(id, ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS) : []
            ]);

            let fileIds = [];

            if (additionalDocuments?.length) {
                fileIds = [...fileIds, ...additionalDocuments.map(attachment => attachment.file_id)];
            }

            if (aggrements?.length) {
                fileIds = [...fileIds, ...aggrements.map(attachment => attachment.file_id)];
            }

            if (fileIds?.length) {
                await this.sharedAttachmentService.deleteBulkAttachmentByFileIds(fileIds);
            }

            await this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: id,
                entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                action_performed: HISTORY_ACTION_TYPE.DELETE,
                action_date: new Date(),
                comments: 'Location has been deleted.',
            });

            return { message: 'Location deleted successfully' };
        });

    }

    public async updateLocationStatus(id: number, updateStatusRequestDto: UpdateLocationStatusRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto> {

        if (
            (updateStatusRequestDto.locationStatus !== LOCATION_STATUS_TYPE.OTHER && updateStatusRequestDto.locationStatus !== LOCATION_STATUS_TYPE.IN_ACTIVE) &&
            !updateStatusRequestDto.statusDate
        ) {
            throw new HttpException('Status date is required.', HttpStatus.BAD_REQUEST);
        }

        if (
            (updateStatusRequestDto.locationStatus === LOCATION_STATUS_TYPE.OTHER) &&
            !updateStatusRequestDto.locationStatusComment
        ) {
            throw new HttpException('Status Comment is required.', HttpStatus.BAD_REQUEST);
        }

        const locationDetail = await this.locationRepository.getLocationById(id);

        if (!locationDetail) {
            throw new HttpException('Location not found', HttpStatus.NOT_FOUND);
        }

        const hasPermission = await this.permissionService.checkAnyPermission([PERMISSIONS.GLOBAL_MANAGE, PERMISSIONS.GLOBAL_LEGAL, PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, locationDetail.entityId, locationDetail.id);

        if (!hasPermission) {
            throw new HttpException('You don\'t have permission to update status of this location.', HttpStatus.FORBIDDEN);
        }

        return await this.databaseHelper.startTransaction(async () => {
            await this.locationRepository.updateLocationDetailById(id, {
                status: updateStatusRequestDto.locationStatus,
                statusDate: updateStatusRequestDto?.statusDate || null,
                statusComment: updateStatusRequestDto?.locationStatusComment || null,
            }, currentContext);

            let history = [];

            if (updateStatusRequestDto.locationStatus !== locationDetail.status) {
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: HISTORY_ACTION_TYPE.STATUS_CHANGE,
                    action_date: new Date(),
                    comments: `Location status changed from ${locationDetail.status} to ${updateStatusRequestDto.locationStatus}.`,
                    additional_info: {
                        old: {
                            status: locationDetail.status,
                            statusDate: locationDetail.statusDate,
                            statusComment: locationDetail.statusComment
                        },
                        new: {
                            status: updateStatusRequestDto.locationStatus,
                            statusDate: updateStatusRequestDto?.statusDate || null,
                            statusComment: updateStatusRequestDto?.locationStatusComment || null
                        }
                    }
                });
            }


            if (updateStatusRequestDto?.statusDate && locationDetail?.statusDate && !moment(updateStatusRequestDto.statusDate).isSame(
                moment(locationDetail.statusDate),
                'day'
            )) {
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: HISTORY_ACTION_TYPE.UPDATE,
                    action_date: new Date(),
                    comments: `Location status date changed from ${locationDetail.statusDate} to ${updateStatusRequestDto.statusDate}.`,
                    additional_info: {
                        old: {
                            statusDate: locationDetail.statusDate
                        },
                        new: {
                            statusDate: updateStatusRequestDto.statusDate
                        }
                    }
                });
            }

            if (updateStatusRequestDto?.locationStatusComment && locationDetail?.statusComment && updateStatusRequestDto.locationStatusComment !== locationDetail.statusComment) {
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: HISTORY_ACTION_TYPE.UPDATE,
                    action_date: new Date(),
                    comments: `Location detail updated.`,
                    additional_info: {
                        old: {
                            statusComment: locationDetail.statusComment
                        },
                        new: {
                            statusComment: updateStatusRequestDto.locationStatusComment
                        }
                    }
                });
            }

            if (history.length) {
                await this.historyService.addBulkRequestHistory(history);
            }

            return { message: 'Status updated successfully.' };
        });
    }

    public async getLocationWiseCapabilities(locationId: number): Promise<LocationCapabilitiesResponseDto> {
        const locationDetail = await this.locationRepository.getLocationById(locationId);

        if (!locationDetail) {
            throw new HttpException('Location not found', HttpStatus.NOT_FOUND);
        }

        const capabilityList = await this.locationWiseCapabilityRepository.getCapabilitiesByLocationId(locationId);

        return singleObjectToInstance(LocationCapabilitiesResponseDto, {
            locationDetail,
            capabilityList
        }, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
        });
    }

    public async exportLocations(requestContext: RequestContext, filterDto?: LocationFilterRequestDto
    ): Promise<{ report: any, filename: string }> {

        const { currentContext, headers } = requestContext;

        if (filterDto?.entityIds?.length || filterDto?.hierarchyEntityId) {
            filterDto.entityIds = await this.businessEntityService.getBusinessEntitiesChildIds(
                filterDto?.hierarchyEntityId ? [filterDto.hierarchyEntityId] : filterDto.entityIds,
            );
        }

        const locationList = await this.locationRepository.getLocationsToExport(filterDto);

        if (!locationList?.length) {
            throw new HttpException('No locations available to export', HttpStatus.NOT_FOUND);
        }

        const SHEET_NAME = 'CapMApp Location List - ' + new Date().getTime();
        const report = await this.excelSheetService.createLocationSheet(locationList, SHEET_NAME);

        await this.historyService.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: -1,
            entity_type: HISTORY_ENTITY_TYPE.EXPORT,
            action_performed: HISTORY_ACTION_TYPE.EXPORT,
            action_date: new Date(),
            comments: 'Location list exported.',
            additional_info: {
                filters: filterDto,
                fileName: SHEET_NAME,
                totalExportCount: locationList.length,
                host: headers?.host,
                origin: headers?.origin
            }
        });

        return { report, filename: SHEET_NAME };
    }

    public async exportLocationWiseCapabilities(locationId: number, requestContext: RequestContext, filterDto?: ExportLocationWiseCapabilityFilterRequestDto
    ): Promise<{ report: any, filename: string }> {

        const { currentContext, headers } = requestContext;

        const locationDetail = await this.locationRepository.getLocationNameById(locationId);

        if (!locationDetail) {
            throw new HttpException('Location not found', HttpStatus.NOT_FOUND);
        }

        const capabilityList = await this.locationWiseCapabilityRepository.exportCapabilitiesByLocationId(locationId, filterDto);

        if (!capabilityList?.length) {
            throw new HttpException('No entries available to export', HttpStatus.NOT_FOUND);
        }

        const SHEET_NAME = `${locationDetail.locationName} available entries - ${new Date().getTime()}`;
        const report = await this.excelSheetService.createLocationWiseCapabilitiesSheet(capabilityList, SHEET_NAME);

        await this.historyService.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: -1,
            entity_type: HISTORY_ENTITY_TYPE.EXPORT,
            action_performed: HISTORY_ACTION_TYPE.EXPORT,
            action_date: new Date(),
            comments: `Available capability list exported for ${locationDetail.locationName}.`,
            additional_info: {
                filters: filterDto,
                fileName: SHEET_NAME,
                totalExportCount: capabilityList.length,
                host: headers?.host,
                origin: headers?.origin
            }
        });

        return { report, filename: SHEET_NAME };
    }
}
