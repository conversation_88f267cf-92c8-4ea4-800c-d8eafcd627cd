import { BingoCardConfig } from 'src/bingo-card/models';
import {
	LocationWiseCapabilityDetail,
	MasterCapability,
	MasterCapabilityDropdowns,
	MetaEvidence,
} from 'src/capability/models';
import { CapabilityCategory } from 'src/capability/models/master-capability-category.model';
import { CapabilityLeg } from 'src/capability/models/master-capability-legs.model';
import { ContactDetail } from 'src/contact-details/models';
import {
	Country,
	LegalEntity,
	Location,
	LocationIndustryVertical,
	PartnerBranch,
} from 'src/location/models';
import {
	CommonDropdown,
	CoreSolution,
	LocationLifeCycleManagement,
	LocationType,
} from 'src/metadata/models';
import { AccessControlConfig, UserPermission } from 'src/permission/models';
import { SupportQuery } from 'src/support-query/models';

const models = [
	CoreSolution,
	LocationType,
	CommonDropdown,
	PartnerBranch,
	Location,
	ContactDetail,
	Country,
	LegalEntity,
	AccessControlConfig,
	UserPermission,
	LocationLifeCycleManagement,
	MasterCapability,
	MetaEvidence,
	LocationIndustryVertical,
	LocationWiseCapabilityDetail,
	BingoCardConfig,
	CapabilityLeg,
	CapabilityCategory,
	MasterCapabilityDropdowns,
	SupportQuery,
];

export const getSequelizeOrmConfig = (enableSSL: boolean = false) => {
	const sslConfig = enableSSL
		? {
				ssl: true,
				dialectOptions: {
					ssl: {
						require: true,
					},
				},
		  }
		: {};

	return {
		synchronize: true,
		autoLoadModels: true,
		models,
		...sslConfig,
	};
};
