{"version": 3, "sources": ["../../@mui/material/Accordion/AccordionContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n * @type {React.Context<{} | {expanded: boolean, disabled: boolean, toggle: () => void}>}\n */\nconst AccordionContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  AccordionContext.displayName = 'AccordionContext';\n}\nexport default AccordionContext;"], "mappings": ";;;;;;;;AAEA,YAAuB;AAMvB,IAAM,mBAAsC,oBAAc,CAAC,CAAC;AAC5D,IAAI,MAAuC;AACzC,mBAAiB,cAAc;AACjC;AACA,IAAO,2BAAQ;", "names": []}