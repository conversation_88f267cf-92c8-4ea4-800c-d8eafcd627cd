import { LocationWiseCapabilityRepository } from 'src/capability/repositories';
import { ConfigService } from 'src/config/config.service';
import { LoggerService } from 'src/core/services';
import { MSGraphApiClient } from 'src/shared/clients';
import { SharedNotificationService } from 'src/shared/services';
export declare class SchedulerService {
    private readonly locationWiseCapabilityRepository;
    private readonly configService;
    private readonly loggerService;
    private readonly mSGraphApiClient;
    private readonly sharedNotificationService;
    constructor(locationWiseCapabilityRepository: LocationWiseCapabilityRepository, configService: ConfigService, loggerService: LoggerService, mSGraphApiClient: MSGraphApiClient, sharedNotificationService: SharedNotificationService);
    private readonly THRESHOLD_DAY_LIMIT;
    runScheduler(type: string): Promise<void>;
    runEntriesNotification(): Promise<void>;
}
