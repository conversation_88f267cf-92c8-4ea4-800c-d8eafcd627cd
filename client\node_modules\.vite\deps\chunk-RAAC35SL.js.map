{"version": 3, "sources": ["../../@mui/material/DialogTitle/DialogTitle.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography from \"../Typography/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDialogTitleUtilityClass } from \"./dialogTitleClasses.js\";\nimport DialogContext from \"../Dialog/DialogContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDialogTitleUtilityClass, classes);\n};\nconst DialogTitleRoot = styled(Typography, {\n  name: 'MuiDialogTitle',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  padding: '16px 24px',\n  flex: '0 0 auto'\n});\nconst DialogTitle = /*#__PURE__*/React.forwardRef(function DialogTitle(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogTitle'\n  });\n  const {\n    className,\n    id: idProp,\n    ...other\n  } = props;\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const {\n    titleId = idProp\n  } = React.useContext(DialogContext);\n  return /*#__PURE__*/_jsx(DialogTitleRoot, {\n    component: \"h2\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    variant: \"h6\",\n    id: idProp ?? titleId,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogTitle.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogTitle;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;AAQtB,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,4BAA4B,OAAO;AAClE;AACA,IAAM,kBAAkB,eAAO,oBAAY;AAAA,EACzC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,MAAM;AACR,CAAC;AACD,IAAM,cAAiC,iBAAW,SAASA,aAAY,SAAS,KAAK;AACnF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,IAAI;AAAA,IACJ,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AACnB,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM;AAAA,IACJ,UAAU;AAAA,EACZ,IAAU,iBAAW,qBAAa;AAClC,aAAoB,mBAAAC,KAAK,iBAAiB;AAAA,IACxC,WAAW;AAAA,IACX,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,IAAI,UAAU;AAAA,IACd,GAAG;AAAA,EACL,CAAC;AACH,CAAC;AACD,OAAwC,YAAY,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAId,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,sBAAQ;", "names": ["DialogTitle", "_jsx", "PropTypes"]}