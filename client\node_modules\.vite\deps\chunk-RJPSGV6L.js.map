{"version": 3, "sources": ["../../@mui/material/Stack/Stack.js", "../../@mui/material/Stack/stackClasses.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createStack } from '@mui/system';\nimport styled from \"../styles/styled.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nconst Stack = createStack({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: '<PERSON>iStack'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the CSS flexbox `gap` is used instead of applying `margin` to children.\n   *\n   * While CSS `gap` removes the [known limitations](https://mui.com/joy-ui/react-stack/#limitations),\n   * it is not fully supported in some browsers. We recommend checking https://caniuse.com/?search=flex%20gap before using this flag.\n   *\n   * To enable this flag globally, follow the [theme's default props](https://mui.com/material-ui/customization/theme-components/#default-props) configuration.\n   * @default false\n   */\n  useFlexGap: PropTypes.bool\n} : void 0;\nexport default Stack;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStackUtilityClass(slot) {\n  return generateUtilityClass('MuiStack', slot);\n}\nconst stackClasses = generateUtilityClasses('MuiStack', ['root']);\nexport default stackClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,wBAAsB;AAItB,IAAM,QAAQ,YAAY;AAAA,EACxB,uBAAuB,eAAO,OAAO;AAAA,IACnC,MAAM;AAAA,IACN,MAAM;AAAA,IACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAAA,EAC/C,CAAC;AAAA,EACD,eAAe,aAAW,gBAAgB;AAAA,IACxC,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACH,CAAC;AACD,OAAwC,MAAM,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/E,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,WAAW,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,kBAAkB,UAAU,eAAe,KAAK,CAAC,GAAG,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC,kBAAkB,UAAU,eAAe,KAAK,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAI9M,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC,CAAC,GAAG,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjK,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtJ,YAAY,kBAAAA,QAAU;AACxB,IAAI;AACJ,IAAO,gBAAQ;;;ACxDf,IAAM,eAAe,uBAAuB,YAAY,CAAC,MAAM,CAAC;AAChE,IAAO,uBAAQ;", "names": ["PropTypes"]}