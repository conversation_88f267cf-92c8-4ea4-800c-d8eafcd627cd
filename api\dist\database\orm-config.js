"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSequelizeOrmConfig = void 0;
const models_1 = require("../bingo-card/models");
const models_2 = require("../capability/models");
const master_capability_category_model_1 = require("../capability/models/master-capability-category.model");
const master_capability_legs_model_1 = require("../capability/models/master-capability-legs.model");
const models_3 = require("../contact-details/models");
const models_4 = require("../location/models");
const models_5 = require("../metadata/models");
const models_6 = require("../permission/models");
const models_7 = require("../support-query/models");
const models = [
    models_5.CoreSolution,
    models_5.LocationType,
    models_5.CommonDropdown,
    models_4.PartnerBranch,
    models_4.Location,
    models_3.ContactDetail,
    models_4.Country,
    models_4.LegalEntity,
    models_6.AccessControlConfig,
    models_6.UserPermission,
    models_5.LocationLifeCycleManagement,
    models_2.MasterCapability,
    models_2.MetaEvidence,
    models_4.LocationIndustryVertical,
    models_2.LocationWiseCapabilityDetail,
    models_1.BingoCardConfig,
    master_capability_legs_model_1.CapabilityLeg,
    master_capability_category_model_1.CapabilityCategory,
    models_2.MasterCapabilityDropdowns,
    models_7.SupportQuery,
];
const getSequelizeOrmConfig = (enableSSL = false) => {
    const sslConfig = enableSSL
        ? {
            ssl: true,
            dialectOptions: {
                ssl: {
                    require: true,
                },
            },
        }
        : {};
    return Object.assign({ synchronize: true, autoLoadModels: true, models }, sslConfig);
};
exports.getSequelizeOrmConfig = getSequelizeOrmConfig;
//# sourceMappingURL=orm-config.js.map