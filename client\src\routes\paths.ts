import { AppConfig } from '../config';

export const ROOTS = {
  HOME: `${AppConfig.uiBaseUrl}`,
  MASTER_SETUP: `${AppConfig.uiBaseUrl}location-setup`,
  LOCATION: `${AppConfig.uiBaseUrl}locations`,
  CAPABILITIES: `${AppConfig.uiBaseUrl}capabilities`,
  USER_ACCESS: `${AppConfig.uiBaseUrl}user-access`,
  BINGO_CARD: `${AppConfig.uiBaseUrl}bingo-card`,
  Master_CAPABILITIES: `${AppConfig.uiBaseUrl}master-capabilities`,
  REPORTS: `${AppConfig.uiBaseUrl}reports`,
  PEOPLE_PROFILE: `${AppConfig.uiBaseUrl}people-profile`,
};

export const paths = {
  root: `${AppConfig.uiBaseUrl}`,
  applicationRoot: () => {
    return `${AppConfig.uiBaseUrl}`;
  },
  home: {
    root: ROOTS.HOME,
    app: `${ROOTS.HOME}/home`,
  },
  capabilities: {
    root: ROOTS.CAPABILITIES,
    capabilityDetail: `/:capabilityId`,
    capabilityEdit: `/:capabilityId/edit`,
  },
  masterCapabilities: {
    root: ROOTS.Master_CAPABILITIES,
    capabilityDetail: `/:capabilityId`,
    capabilityEdit: `/:capabilityId/edit`,
  },
  reports: {
    root: ROOTS.REPORTS,
  },
  peopleProfile: {
    root: ROOTS.PEOPLE_PROFILE,
  },
  locations: {
    root: ROOTS.LOCATION,
    // locationDetail: `/:locationId`,
    // locationEdit: `/:locationId/edit`,
    userAccess: `/:locationId/user-access`,

    locationCapabilities: `/:locationId/capabilities`,
    // searchCapability: `/:locationId/add-capability/`,
    addLocationCapability: `/:locationId/capabilities/:capabilityId`,
  },
  locationSetup: {
    root: `${ROOTS.MASTER_SETUP}/`,
    groupDetails: 'group/:id',
    regionDetails: 'region/:id',
    clusterDetails: 'cluster/:id',
    countryDetails: 'country/:id',
    countryEdit: 'country/:id/edit',
    setupLocation: ':id/setup',
    areaDetails: 'area/:id',
    locationDetails: `location/:id`,
    locationEdit: `location/:id/edit`,
  },
  userAccess: {
    root: ROOTS.USER_ACCESS,
  },
  bingoCard: {
    root: `${ROOTS.BINGO_CARD}/:id`,
  },
  error: {
    page403: `${AppConfig.uiBaseUrl}403`,
    page404: `${AppConfig.uiBaseUrl}404`,
    page500: `${AppConfig.uiBaseUrl}500`,
  },
};

export const ReplaceUrlVariable = (url: string, variables: any) => {
  Object.keys(variables).forEach((variableKey: string) => {
    var re = new RegExp(':' + variableKey, 'g');
    url = url.replace(re, variables[variableKey]);
  });
  return url;
};
