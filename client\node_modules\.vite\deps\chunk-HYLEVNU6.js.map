{"version": 3, "sources": ["../../@mui/material/RadioGroup/RadioGroup.js", "../../@mui/material/RadioGroup/radioGroupClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FormGroup from \"../FormGroup/index.js\";\nimport { getRadioGroupUtilityClass } from \"./radioGroupClasses.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport useControlled from \"../utils/useControlled.js\";\nimport RadioGroupContext from \"./RadioGroupContext.js\";\nimport useId from \"../utils/useId.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = props => {\n  const {\n    classes,\n    row,\n    error\n  } = props;\n  const slots = {\n    root: ['root', row && 'row', error && 'error']\n  };\n  return composeClasses(slots, getRadioGroupUtilityClass, classes);\n};\nconst RadioGroup = /*#__PURE__*/React.forwardRef(function RadioGroup(props, ref) {\n  const {\n    // private\n    // eslint-disable-next-line react/prop-types\n    actions,\n    children,\n    className,\n    defaultValue,\n    name: nameProp,\n    onChange,\n    value: valueProp,\n    ...other\n  } = props;\n  const rootRef = React.useRef(null);\n  const classes = useUtilityClasses(props);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'RadioGroup'\n  });\n  React.useImperativeHandle(actions, () => ({\n    focus: () => {\n      let input = rootRef.current.querySelector('input:not(:disabled):checked');\n      if (!input) {\n        input = rootRef.current.querySelector('input:not(:disabled)');\n      }\n      if (input) {\n        input.focus();\n      }\n    }\n  }), []);\n  const handleRef = useForkRef(ref, rootRef);\n  const name = useId(nameProp);\n  const contextValue = React.useMemo(() => ({\n    name,\n    onChange(event) {\n      setValueState(event.target.value);\n      if (onChange) {\n        onChange(event, event.target.value);\n      }\n    },\n    value\n  }), [name, onChange, setValueState, value]);\n  return /*#__PURE__*/_jsx(RadioGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(FormGroup, {\n      role: \"radiogroup\",\n      ref: handleRef,\n      className: clsx(classes.root, className),\n      ...other,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? RadioGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * The name used to reference the value of the control.\n   * If you don't provide this prop, it falls back to a randomly generated name.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when a radio button is selected.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * @param {string} value The value of the selected radio button.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * Value of the selected radio button. The DOM API casts this to a string.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default RadioGroup;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getRadioGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiRadioGroup', slot);\n}\nconst radioGroupClasses = generateUtilityClasses('MuiRadioGroup', ['root', 'row', 'error']);\nexport default radioGroupClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACA,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,QAAQ,OAAO,OAAO,CAAC;AAC1F,IAAO,4BAAQ;;;ADMf,yBAA4B;AAC5B,IAAM,oBAAoB,WAAS;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,OAAO,OAAO,SAAS,OAAO;AAAA,EAC/C;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AACA,IAAM,aAAgC,iBAAW,SAASA,YAAW,OAAO,KAAK;AAC/E,QAAM;AAAA;AAAA;AAAA,IAGJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA,OAAO;AAAA,IACP,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,UAAU,kBAAkB,KAAK;AACvC,QAAM,CAAC,OAAO,aAAa,IAAI,sBAAc;AAAA,IAC3C,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,EACR,CAAC;AACD,EAAM,0BAAoB,SAAS,OAAO;AAAA,IACxC,OAAO,MAAM;AACX,UAAI,QAAQ,QAAQ,QAAQ,cAAc,8BAA8B;AACxE,UAAI,CAAC,OAAO;AACV,gBAAQ,QAAQ,QAAQ,cAAc,sBAAsB;AAAA,MAC9D;AACA,UAAI,OAAO;AACT,cAAM,MAAM;AAAA,MACd;AAAA,IACF;AAAA,EACF,IAAI,CAAC,CAAC;AACN,QAAM,YAAY,mBAAW,KAAK,OAAO;AACzC,QAAM,OAAO,cAAM,QAAQ;AAC3B,QAAM,eAAqB,cAAQ,OAAO;AAAA,IACxC;AAAA,IACA,SAAS,OAAO;AACd,oBAAc,MAAM,OAAO,KAAK;AAChC,UAAI,UAAU;AACZ,iBAAS,OAAO,MAAM,OAAO,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,UAAU,eAAe,KAAK,CAAC;AAC1C,aAAoB,mBAAAC,KAAK,0BAAkB,UAAU;AAAA,IACnD,OAAO;AAAA,IACP,cAAuB,mBAAAA,KAAK,mBAAW;AAAA,MACrC,MAAM;AAAA,MACN,KAAK;AAAA,MACL,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC,GAAG;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,WAAW,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,kBAAAA,QAAU;AACnB,IAAI;AACJ,IAAO,qBAAQ;", "names": ["RadioGroup", "_jsx", "PropTypes"]}