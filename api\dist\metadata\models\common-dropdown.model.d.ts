import { BaseModel } from 'src/shared/models';
import { CoreSolution } from './core-solution.model';
import { LocationType } from './location-type.model';
import { COMMON_DROPDOWN_TYPE } from 'src/shared/enums';
export declare class CommonDropdown extends BaseModel<CommonDropdown> {
    title: string;
    code: string | null;
    coreSolutionId?: number | null;
    coreSolution: CoreSolution;
    locationTypeId?: number | null;
    locationType: LocationType;
    otherDetails: object | null;
    type: COMMON_DROPDOWN_TYPE;
}
