import { BasicAccessControlConfigResponseDto } from './access-control-config-response.dto';
export declare class PermissionResponseDto {
    permissionName: string;
    locations: number[];
}
export declare class LocationInfoDto {
    id: number;
    locationName: string;
}
export declare class UserPermissionResponseDto {
    id: number;
    loginId: string;
    manageLocation: boolean;
    manageCapability: boolean;
    accessControlConfig?: BasicAccessControlConfigResponseDto;
    location?: LocationInfoDto;
}
export declare class PaginatedUserPermissionResponseDto {
    pageTotal: number;
    total: number;
    records: UserPermissionResponseDto[];
}
