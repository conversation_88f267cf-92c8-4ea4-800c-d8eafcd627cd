"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactDetailController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const passport_1 = require("@nestjs/passport");
const services_1 = require("../services");
const enums_1 = require("../../shared/enums");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const dtos_2 = require("../dtos");
let ContactDetailController = class ContactDetailController {
    constructor(contactDetailService) {
        this.contactDetailService = contactDetailService;
    }
    getContactUserList(objectType, objectId, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.contactDetailService.getContactUserList({ objectType, objectId, entityId, });
        });
    }
    addHierarchyContact(request, newHierarchyUserRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.contactDetailService.addHierarchyContact(newHierarchyUserRequestDto, request.currentContext);
        });
    }
    deleteUserFromContact(request, deleteHierarchyUserRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.contactDetailService.deleteUserFromContact(deleteHierarchyUserRequestDto, request.currentContext);
        });
    }
    patchContactTitle(request, patchContactTitleRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.contactDetailService.patchContactDetail(patchContactTitleRequestDto, request.currentContext);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiQuery)({
        name: 'objectType',
        type: 'enum',
        enum: enums_1.CONTACT_DETAIL_OBJECT_TYPE,
        required: true,
        allowEmptyValue: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'objectId',
        type: 'string',
        description: 'If objectType is PILLAR, pass PILLAR enum value (LOGISTICS or PORTS_AND_TERMINALS). For other objectTypes, pass a numeric ID.',
        required: true,
        allowEmptyValue: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'For CORE_SOLUTION_ID type it will be hierarchy entity Id and for CONTACT_TYPE_ID type it will be location id.',
        required: true,
        allowEmptyValue: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get contact detail list.',
        type: [dtos_2.ContactListingResponseDto],
    }),
    (0, common_1.Get)('/user-list'),
    __param(0, (0, common_1.Query)('objectType')),
    __param(1, (0, common_1.Query)('objectId')),
    __param(2, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Number]),
    __metadata("design:returntype", Promise)
], ContactDetailController.prototype, "getContactUserList", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.GLOBAL_MANAGE, { checkEntity: true }),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add core solution (Capability) wise new contact for Group, Region and Cluster .',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/hierarchy-contact'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.NewHierarchyUserRequestDto]),
    __metadata("design:returntype", Promise)
], ContactDetailController.prototype, "addHierarchyContact", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Remove user from contact list.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)('/user-detail'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.DeleteHierarchyUserRequestDto]),
    __metadata("design:returntype", Promise)
], ContactDetailController.prototype, "deleteUserFromContact", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update user detail.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Patch)('/user-detail'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.PatchContactTitleRequestDto]),
    __metadata("design:returntype", Promise)
], ContactDetailController.prototype, "patchContactTitle", null);
ContactDetailController = __decorate([
    (0, swagger_1.ApiTags)('Contact Detail APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('contact-detail'),
    __metadata("design:paramtypes", [services_1.ContactDetailService])
], ContactDetailController);
exports.ContactDetailController = ContactDetailController;
//# sourceMappingURL=contact-detail.controller.js.map