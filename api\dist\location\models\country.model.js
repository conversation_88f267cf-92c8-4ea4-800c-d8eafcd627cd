"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Country = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const legal_entity_model_1 = require("./legal-entity.model");
let Country = class Country extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_id', type: sequelize_typescript_1.DataType.NUMBER, allowNull: false }),
    __metadata("design:type", Number)
], Country.prototype, "entityId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_code', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Country.prototype, "entityCode", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Country.prototype, "entityTitle", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'other_details', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], Country.prototype, "otherDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => legal_entity_model_1.LegalEntity),
    __metadata("design:type", Array)
], Country.prototype, "legalEntities", void 0);
Country = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'data_country_details' })
], Country);
exports.Country = Country;
//# sourceMappingURL=country.model.js.map