"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetaData = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
const metadata_enum_1 = require("../../shared/enums/metadata.enum");
let MetaData = class MetaData extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'name', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], MetaData.prototype, "name", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'type',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(metadata_enum_1.METADATA_TYPE_ENUM)),
        allowNull: false,
    }),
    __metadata("design:type", String)
], MetaData.prototype, "type", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'legs', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], MetaData.prototype, "legs", void 0);
MetaData = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'meta_data' })
], MetaData);
exports.MetaData = MetaData;
//# sourceMappingURL=metadata.model.js.map