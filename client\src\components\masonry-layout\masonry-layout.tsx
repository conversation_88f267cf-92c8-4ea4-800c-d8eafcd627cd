import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Box } from '@mui/material';

interface MasonryLayoutProps {
  children: React.ReactNode[];
  columns?: number;
  gap?: number;
  minColumnWidth?: number;
}

export const MasonryLayout: React.FC<MasonryLayoutProps> = ({
  children,
  columns,
  gap = 16,
  minColumnWidth = 300,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [itemPositions, setItemPositions] = useState<Array<{ left: number; top: number; width: number }>>([]);
  const [containerHeight, setContainerHeight] = useState(400);
  const [isLayoutReady, setIsLayoutReady] = useState(false);

  const calculateLayout = useCallback(() => {
    if (!containerRef.current || children.length === 0) return;

    const container = containerRef.current;
    const containerWidth = container.offsetWidth;
    
    // Calculate number of columns
    const maxColumns = columns || Math.max(1, Math.floor(containerWidth / (minColumnWidth + gap)));
    const actualColumns = Math.min(maxColumns, children.length);
    const columnWidth = (containerWidth - (gap * (actualColumns - 1))) / actualColumns;
    
    const columnHeights = new Array(actualColumns).fill(0);
    const newItemPositions: Array<{ left: number; top: number; width: number }> = [];

    // Calculate positions for each item
    children.forEach((_, index) => {
      const itemElement = itemRefs.current[index];
      if (!itemElement) return;

      // Find the shortest column
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));
      
      // Calculate position
      const left = shortestColumnIndex * (columnWidth + gap);
      const top = columnHeights[shortestColumnIndex];
      
      newItemPositions.push({
        left,
        top,
        width: columnWidth,
      });
      
      // Get actual height of the rendered element
      const itemHeight = itemElement.offsetHeight || 200;
      columnHeights[shortestColumnIndex] += itemHeight + gap;
    });

    setItemPositions(newItemPositions);
    setContainerHeight(Math.max(...columnHeights));
    setIsLayoutReady(true);
  }, [children, columns, gap, minColumnWidth]);

  // Initial layout calculation
  useEffect(() => {
    // Wait for all items to render
    const timer = setTimeout(() => {
      calculateLayout();
    }, 100);

    return () => clearTimeout(timer);
  }, [calculateLayout]);

  // Recalculate on window resize
  useEffect(() => {
    const handleResize = () => {
      setIsLayoutReady(false);
      setTimeout(calculateLayout, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateLayout]);

  return (
    <Box
      ref={containerRef}
      sx={{
        position: 'relative',
        width: '100%',
        height: containerHeight,
        minHeight: 400,
        transition: 'height 0.3s ease',
      }}
    >
      {children.map((child, index) => {
        const position = itemPositions[index];
        
        return (
          <Box
            key={index}
            ref={(el) => (itemRefs.current[index] = el)}
            sx={{
              position: isLayoutReady && position ? 'absolute' : 'static',
              left: position?.left || 0,
              top: position?.top || 0,
              width: position?.width || '100%',
              opacity: isLayoutReady ? 1 : 0.8,
              transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              transform: isLayoutReady ? 'translateY(0)' : 'translateY(10px)',
              visibility: isLayoutReady ? 'visible' : 'hidden',
            }}
          >
            {child}
          </Box>
        );
      })}
      
      {/* Invisible items for initial measurement */}
      {!isLayoutReady && (
        <Box sx={{ position: 'absolute', top: -9999, left: -9999, visibility: 'hidden' }}>
          {children.map((child, index) => (
            <Box
              key={`measure-${index}`}
              ref={(el) => {
                if (el && !itemRefs.current[index]) {
                  itemRefs.current[index] = el;
                }
              }}
              sx={{ width: minColumnWidth }}
            >
              {child}
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default MasonryLayout;
