{"version": 3, "sources": ["../../react-geolocated/dist-modules/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useGeolocated = useGeolocated;\nconst react_1 = require(\"react\");\n/**\n * Hook abstracting away the interaction with the Geolocation API.\n * @param config - the configuration to use\n */\nfunction useGeolocated(config = {}) {\n    const { positionOptions = {\n        enableHighAccuracy: true,\n        maximumAge: 0,\n        timeout: Infinity,\n    }, isOptimisticGeolocationEnabled = true, userDecisionTimeout = undefined, suppressLocationOnMount = false, watchPosition = false, geolocationProvider = typeof navigator !== \"undefined\"\n        ? navigator.geolocation\n        : undefined, watchLocationPermissionChange = false, onError, onSuccess, } = config;\n    const userDecisionTimeoutId = (0, react_1.useRef)(0);\n    const isCurrentlyMounted = (0, react_1.useRef)(true);\n    const watchId = (0, react_1.useRef)(0);\n    const [isGeolocationEnabled, setIsGeolocationEnabled] = (0, react_1.useState)(isOptimisticGeolocationEnabled);\n    const [coords, setCoords] = (0, react_1.useState)();\n    const [timestamp, setTimestamp] = (0, react_1.useState)();\n    const [positionError, setPositionError] = (0, react_1.useState)();\n    const [permissionState, setPermissionState] = (0, react_1.useState)();\n    const cancelUserDecisionTimeout = (0, react_1.useCallback)(() => {\n        if (userDecisionTimeoutId.current) {\n            window.clearTimeout(userDecisionTimeoutId.current);\n        }\n    }, []);\n    const handlePositionError = (0, react_1.useCallback)((error) => {\n        cancelUserDecisionTimeout();\n        if (isCurrentlyMounted.current) {\n            setCoords(() => undefined);\n            setIsGeolocationEnabled(false);\n            setPositionError(error);\n        }\n        onError === null || onError === void 0 ? void 0 : onError(error);\n    }, [onError, cancelUserDecisionTimeout]);\n    const handlePositionSuccess = (0, react_1.useCallback)((position) => {\n        cancelUserDecisionTimeout();\n        if (isCurrentlyMounted.current) {\n            setCoords(position.coords);\n            setTimestamp(position.timestamp);\n            setIsGeolocationEnabled(true);\n            setPositionError(() => undefined);\n        }\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(position);\n    }, [onSuccess, cancelUserDecisionTimeout]);\n    const getPosition = (0, react_1.useCallback)(() => {\n        if (!geolocationProvider ||\n            !geolocationProvider.getCurrentPosition ||\n            !geolocationProvider.watchPosition) {\n            throw new Error(\"The provided geolocation provider is invalid\");\n        }\n        if (userDecisionTimeout) {\n            userDecisionTimeoutId.current = window.setTimeout(() => {\n                handlePositionError();\n            }, userDecisionTimeout);\n        }\n        if (watchPosition) {\n            watchId.current = geolocationProvider.watchPosition(handlePositionSuccess, handlePositionError, positionOptions);\n        }\n        else {\n            geolocationProvider.getCurrentPosition(handlePositionSuccess, handlePositionError, positionOptions);\n        }\n    }, [\n        geolocationProvider,\n        watchPosition,\n        userDecisionTimeout,\n        handlePositionError,\n        handlePositionSuccess,\n        positionOptions,\n    ]);\n    (0, react_1.useEffect)(() => {\n        let permission;\n        if (watchLocationPermissionChange &&\n            geolocationProvider &&\n            \"permissions\" in navigator) {\n            navigator.permissions\n                .query({ name: \"geolocation\" })\n                .then((result) => {\n                permission = result;\n                permission.onchange = () => {\n                    setPermissionState(permission.state);\n                };\n            })\n                .catch((e) => {\n                console.error(\"Error updating the permissions\", e);\n            });\n        }\n        return () => {\n            if (permission) {\n                permission.onchange = null;\n            }\n        };\n    }, []); // eslint-disable-line react-hooks/exhaustive-deps\n    (0, react_1.useEffect)(() => {\n        if (!suppressLocationOnMount) {\n            getPosition();\n        }\n        return () => {\n            cancelUserDecisionTimeout();\n            if (watchPosition && watchId.current) {\n                geolocationProvider === null || geolocationProvider === void 0 ? void 0 : geolocationProvider.clearWatch(watchId.current);\n            }\n        };\n    }, [permissionState]); // eslint-disable-line react-hooks/exhaustive-deps\n    return {\n        getPosition,\n        coords,\n        timestamp,\n        isGeolocationEnabled,\n        isGeolocationAvailable: Boolean(geolocationProvider),\n        positionError,\n    };\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,gBAAgB;AACxB,QAAM,UAAU;AAKhB,aAAS,cAAc,SAAS,CAAC,GAAG;AAChC,YAAM,EAAE,kBAAkB;AAAA,QACtB,oBAAoB;AAAA,QACpB,YAAY;AAAA,QACZ,SAAS;AAAA,MACb,GAAG,iCAAiC,MAAM,sBAAsB,QAAW,0BAA0B,OAAO,gBAAgB,OAAO,sBAAsB,OAAO,cAAc,cACxK,UAAU,cACV,QAAW,gCAAgC,OAAO,SAAS,UAAW,IAAI;AAChF,YAAM,yBAAyB,GAAG,QAAQ,QAAQ,CAAC;AACnD,YAAM,sBAAsB,GAAG,QAAQ,QAAQ,IAAI;AACnD,YAAM,WAAW,GAAG,QAAQ,QAAQ,CAAC;AACrC,YAAM,CAAC,sBAAsB,uBAAuB,KAAK,GAAG,QAAQ,UAAU,8BAA8B;AAC5G,YAAM,CAAC,QAAQ,SAAS,KAAK,GAAG,QAAQ,UAAU;AAClD,YAAM,CAAC,WAAW,YAAY,KAAK,GAAG,QAAQ,UAAU;AACxD,YAAM,CAAC,eAAe,gBAAgB,KAAK,GAAG,QAAQ,UAAU;AAChE,YAAM,CAAC,iBAAiB,kBAAkB,KAAK,GAAG,QAAQ,UAAU;AACpE,YAAM,6BAA6B,GAAG,QAAQ,aAAa,MAAM;AAC7D,YAAI,sBAAsB,SAAS;AAC/B,iBAAO,aAAa,sBAAsB,OAAO;AAAA,QACrD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,YAAM,uBAAuB,GAAG,QAAQ,aAAa,CAAC,UAAU;AAC5D,kCAA0B;AAC1B,YAAI,mBAAmB,SAAS;AAC5B,oBAAU,MAAM,MAAS;AACzB,kCAAwB,KAAK;AAC7B,2BAAiB,KAAK;AAAA,QAC1B;AACA,oBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK;AAAA,MACnE,GAAG,CAAC,SAAS,yBAAyB,CAAC;AACvC,YAAM,yBAAyB,GAAG,QAAQ,aAAa,CAAC,aAAa;AACjE,kCAA0B;AAC1B,YAAI,mBAAmB,SAAS;AAC5B,oBAAU,SAAS,MAAM;AACzB,uBAAa,SAAS,SAAS;AAC/B,kCAAwB,IAAI;AAC5B,2BAAiB,MAAM,MAAS;AAAA,QACpC;AACA,sBAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,QAAQ;AAAA,MAC5E,GAAG,CAAC,WAAW,yBAAyB,CAAC;AACzC,YAAM,eAAe,GAAG,QAAQ,aAAa,MAAM;AAC/C,YAAI,CAAC,uBACD,CAAC,oBAAoB,sBACrB,CAAC,oBAAoB,eAAe;AACpC,gBAAM,IAAI,MAAM,8CAA8C;AAAA,QAClE;AACA,YAAI,qBAAqB;AACrB,gCAAsB,UAAU,OAAO,WAAW,MAAM;AACpD,gCAAoB;AAAA,UACxB,GAAG,mBAAmB;AAAA,QAC1B;AACA,YAAI,eAAe;AACf,kBAAQ,UAAU,oBAAoB,cAAc,uBAAuB,qBAAqB,eAAe;AAAA,QACnH,OACK;AACD,8BAAoB,mBAAmB,uBAAuB,qBAAqB,eAAe;AAAA,QACtG;AAAA,MACJ,GAAG;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,OAAC,GAAG,QAAQ,WAAW,MAAM;AACzB,YAAI;AACJ,YAAI,iCACA,uBACA,iBAAiB,WAAW;AAC5B,oBAAU,YACL,MAAM,EAAE,MAAM,cAAc,CAAC,EAC7B,KAAK,CAAC,WAAW;AAClB,yBAAa;AACb,uBAAW,WAAW,MAAM;AACxB,iCAAmB,WAAW,KAAK;AAAA,YACvC;AAAA,UACJ,CAAC,EACI,MAAM,CAAC,MAAM;AACd,oBAAQ,MAAM,kCAAkC,CAAC;AAAA,UACrD,CAAC;AAAA,QACL;AACA,eAAO,MAAM;AACT,cAAI,YAAY;AACZ,uBAAW,WAAW;AAAA,UAC1B;AAAA,QACJ;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,OAAC,GAAG,QAAQ,WAAW,MAAM;AACzB,YAAI,CAAC,yBAAyB;AAC1B,sBAAY;AAAA,QAChB;AACA,eAAO,MAAM;AACT,oCAA0B;AAC1B,cAAI,iBAAiB,QAAQ,SAAS;AAClC,oCAAwB,QAAQ,wBAAwB,SAAS,SAAS,oBAAoB,WAAW,QAAQ,OAAO;AAAA,UAC5H;AAAA,QACJ;AAAA,MACJ,GAAG,CAAC,eAAe,CAAC;AACpB,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,wBAAwB,QAAQ,mBAAmB;AAAA,QACnD;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA;", "names": []}