{"version": 3, "file": "logger.service.js", "sourceRoot": "", "sources": ["../../../src/core/services/logger.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA2D;AAC3D,gEAA0D;AAGnD,IAAM,aAAa,GAAnB,MAAM,aAAa;IACzB,YAA6B,MAAqB,EAAmB,MAAqB;QAA7D,WAAM,GAAN,MAAM,CAAe;QAAmB,WAAM,GAAN,MAAM,CAAe;QACzF,IAAI,CAAC,MAAM,GAAG,IAAI,sBAAa,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACrD,CAAC;IAKD,GAAG,CAAC,OAAY,EAAE,OAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACnC,CAAC;IAKD,KAAK,CAAC,OAAY,EAAE,KAAc,EAAE,OAAgB;QACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAKD,IAAI,CAAC,OAAY,EAAE,OAAgB;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACpC,CAAC;IAKD,KAAK,CAAC,OAAY,EAAE,OAAgB;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAKD,OAAO,CAAC,OAAY,EAAE,OAAgB;QACrC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;CACD,CAAA;AAxCY,aAAa;IADzB,IAAA,mBAAU,GAAE;qCAEyB,sBAAa,EAA2B,8BAAa;GAD9E,aAAa,CAwCzB;AAxCY,sCAAa"}