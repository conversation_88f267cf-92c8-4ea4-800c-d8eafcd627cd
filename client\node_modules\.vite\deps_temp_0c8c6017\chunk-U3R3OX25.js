import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/List/ListContext.js
var React = __toESM(require_react());
var ListContext = React.createContext({});
if (true) {
  ListContext.displayName = "ListContext";
}
var ListContext_default = ListContext;

export {
  ListContext_default
};
//# sourceMappingURL=chunk-U3R3OX25.js.map
