"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactDetailService = void 0;
const common_1 = require("@nestjs/common");
const helpers_1 = require("../../shared/helpers");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const dtos_1 = require("../dtos");
const clients_1 = require("../../shared/clients");
const repositories_1 = require("../../metadata/repositories");
const repositories_2 = require("../repositories");
const repositories_3 = require("../../location/repositories");
const sequelize_1 = require("sequelize");
const lodash_1 = require("lodash");
let ContactDetailService = class ContactDetailService {
    constructor(adminApiServie, coreSolutionRepository, contactDetailRepository, countryRepository, historyService, databaseHelper) {
        this.adminApiServie = adminApiServie;
        this.coreSolutionRepository = coreSolutionRepository;
        this.contactDetailRepository = contactDetailRepository;
        this.countryRepository = countryRepository;
        this.historyService = historyService;
        this.databaseHelper = databaseHelper;
    }
    addHierarchyContact(newHierarchyUserRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId, coreSolutionId, userDetail } = newHierarchyUserRequestDto;
            const coreSolutionDetail = yield this.coreSolutionRepository.getCoreSolutionById(coreSolutionId);
            if (!coreSolutionDetail) {
                throw new exceptions_1.HttpException('Capability not found!', enums_1.HttpStatus.NOT_FOUND);
            }
            const entityDetal = yield this.adminApiServie.getBusinessEntityDetailsById(entityId);
            if (!entityDetal) {
                throw new exceptions_1.HttpException('Business entity not found!', enums_1.HttpStatus.NOT_FOUND);
            }
            if (entityDetal.entity_type === enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY) {
                const isCountryAdded = yield this.countryRepository.isCountryExist(entityId);
                if (!isCountryAdded) {
                    throw new exceptions_1.HttpException('Setup country before adding contact!', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            const conditionForExisting = {
                entityId: entityId,
                objectId: coreSolutionId,
                objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID,
            };
            const existingContactDetail = yield this.contactDetailRepository.getContactDetailByCondition(conditionForExisting);
            if (existingContactDetail) {
                const isUserExist = existingContactDetail.userDetails.find(exisingUserDetail => exisingUserDetail.loginId.toLowerCase() === userDetail.loginId.toLowerCase());
                if (isUserExist) {
                    throw new exceptions_1.HttpException('Contact already exists.', enums_1.HttpStatus.BAD_REQUEST);
                }
                existingContactDetail.userDetails.push(userDetail);
                return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    yield this.contactDetailRepository.updateUserDetailsByCondition(conditionForExisting, existingContactDetail.userDetails, currentContext);
                    yield this.historyService.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: (0, lodash_1.toNumber)(entityId),
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.HIERARCHY_CONTACT,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                        action_date: new Date(),
                        comments: `${userDetail.loginId} added for ${coreSolutionDetail.title}.`,
                        additional_info: {
                            contactData: newHierarchyUserRequestDto
                        }
                    });
                    return { message: 'Contact added successfully!' };
                }));
            }
            else {
                return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    const newContactDetail = {
                        objectId: coreSolutionId,
                        objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID,
                        entityId: entityId,
                        entityCode: entityDetal.code,
                        entityTitle: entityDetal.full_name,
                        entityType: entityDetal.entity_type,
                        userDetails: [userDetail],
                    };
                    const newContact = yield this.contactDetailRepository.addHierarchyContact(newContactDetail, currentContext);
                    yield this.historyService.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: (0, lodash_1.toNumber)(entityId),
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.HIERARCHY_CONTACT,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                        action_date: new Date(),
                        comments: `${userDetail.loginId} added for ${coreSolutionDetail.title}.`,
                        additional_info: {
                            contactData: newHierarchyUserRequestDto
                        }
                    });
                    return { message: 'Contact added successfully!', data: newContact };
                }));
            }
        });
    }
    deleteUserFromContact(deleteHierarchyUserRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { contactId, loginId } = deleteHierarchyUserRequestDto;
            const existingContactDetail = yield this.contactDetailRepository.getContactDetailByCondition({
                id: contactId,
            });
            if (!existingContactDetail) {
                throw new exceptions_1.HttpException('Contact detail not found!', enums_1.HttpStatus.NOT_FOUND);
            }
            const userDetails = existingContactDetail.userDetails || [];
            if (userDetails.length === 0) {
                throw new exceptions_1.HttpException('No User Found!', enums_1.HttpStatus.NOT_FOUND);
            }
            const updatedUserList = userDetails.filter((userDetail) => userDetail.loginId.toLowerCase() !== loginId.toLowerCase());
            if (updatedUserList.length === userDetails.length) {
                throw new exceptions_1.HttpException('User not found!', enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.adminApiServie.hasPermissionToUser(currentContext.user.username, enums_1.PERMISSIONS.GLOBAL_MANAGE, existingContactDetail.entityId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException('You do not have permission to delete this user.', enums_1.HttpStatus.FORBIDDEN);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.contactDetailRepository.updateUserDetailsByCondition({ id: contactId }, updatedUserList, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: (0, lodash_1.toNumber)(existingContactDetail.entityId),
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.HIERARCHY_CONTACT,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                    action_date: new Date(),
                    comments: `${loginId} deleted successfully.`,
                    additional_info: {
                        contactData: deleteHierarchyUserRequestDto
                    }
                });
                return { message: 'User deleted successfully.' };
            }));
        });
    }
    patchContactDetail(patchContactTitleRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { contactId, loginId, updatedUserDetail } = patchContactTitleRequestDto;
            const existingContactDetail = yield this.contactDetailRepository.getContactDetailByCondition({
                id: contactId,
            });
            if (!existingContactDetail) {
                throw new exceptions_1.HttpException('Contact detail not found!', enums_1.HttpStatus.NOT_FOUND);
            }
            const userDetails = existingContactDetail.userDetails || [];
            if (userDetails.length === 0) {
                throw new exceptions_1.HttpException('No User Found!', enums_1.HttpStatus.NOT_FOUND);
            }
            const currentUserIndex = userDetails.findIndex((user) => user.loginId.toLowerCase() === loginId.toLowerCase());
            if (currentUserIndex === -1) {
                throw new exceptions_1.HttpException('User not found!', enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.adminApiServie.hasPermissionToUser(currentContext.user.username, enums_1.PERMISSIONS.GLOBAL_MANAGE, existingContactDetail.entityId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException('You do not have permission to update this user.', enums_1.HttpStatus.FORBIDDEN);
            }
            yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                userDetails[currentUserIndex] = Object.assign(Object.assign({}, userDetails[currentUserIndex]), updatedUserDetail);
                yield this.contactDetailRepository.updateUserDetailsByCondition({ id: contactId }, userDetails, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: (0, lodash_1.toNumber)(existingContactDetail.entityId),
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.HIERARCHY_CONTACT,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                    action_date: new Date(),
                    comments: `${loginId} detail updated successfully.`,
                    additional_info: {
                        contactData: patchContactTitleRequestDto
                    }
                });
            }));
            return { message: 'User detail updated successfully.' };
        });
    }
    getContactUserList(filterData) {
        return __awaiter(this, void 0, void 0, function* () {
            const { objectType, objectId, entityId } = filterData;
            if (objectType === enums_1.CONTACT_DETAIL_OBJECT_TYPE.PILLAR) {
                if (!isNaN(Number(objectId)) || !Object.values(enums_1.PILLAR).includes(objectId)) {
                    throw new exceptions_1.HttpException(`When objectType is PILLAR, objectId must be one of ${Object.values(enums_1.PILLAR).join(', ')}`, enums_1.HttpStatus.FORBIDDEN);
                }
            }
            else {
                const numValue = Number(objectId);
                if (isNaN(numValue)) {
                    throw new exceptions_1.HttpException(`objectId must be a number for the specified objectType`, enums_1.HttpStatus.BAD_REQUEST);
                }
            }
            const condition = {
                objectType: objectType === enums_1.CONTACT_DETAIL_OBJECT_TYPE.PILLAR
                    ? enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID
                    : objectType,
                objectId,
            };
            if (objectType === enums_1.CONTACT_DETAIL_OBJECT_TYPE.PILLAR) {
                const coreSolutions = yield this.coreSolutionRepository.getCoreSolutionByPillar(objectId);
                const coreSolutionIds = coreSolutions.map(coreSolution => coreSolution.id);
                if (coreSolutionIds.length > 0) {
                    condition.objectId = { [sequelize_1.Op.in]: coreSolutionIds };
                }
                else {
                    return [];
                }
                condition.entityId = entityId;
            }
            if (objectType === enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID) {
                condition.entityId = entityId;
            }
            if (objectType === enums_1.CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID) {
                condition.locationid = entityId;
            }
            condition.userDetails = {
                [sequelize_1.Op.and]: [{ [sequelize_1.Op.ne]: null }, { [sequelize_1.Op.notIn]: [[]] }, { [sequelize_1.Op.ne]: [] }],
            };
            const contactDetails = yield this.contactDetailRepository.getContactAsIndividualUserByCondition(condition);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.ContactListingResponseDto, contactDetails);
        });
    }
};
ContactDetailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        repositories_1.CoreSolutionRepository,
        repositories_2.ContactDetailRepository,
        repositories_3.CountryRepository,
        clients_1.HistoryApiClient,
        helpers_1.DatabaseHelper])
], ContactDetailService);
exports.ContactDetailService = ContactDetailService;
//# sourceMappingURL=contact-detail.services.js.map