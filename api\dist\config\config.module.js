"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigModule = void 0;
const common_1 = require("@nestjs/common");
const config_controller_1 = require("./config.controller");
const config_service_1 = require("./config.service");
const clients_1 = require("../shared/clients");
const services_1 = require("../shared/services");
const services_2 = require("../business-entity/services");
const repositories_1 = require("../location/repositories");
const repositories_2 = require("../permission/repositories");
let ConfigModule = class ConfigModule {
};
ConfigModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [
            {
                provide: config_service_1.ConfigService,
                useValue: new config_service_1.ConfigService(),
            },
            clients_1.AdminApiClient,
            services_1.SharedPermissionService,
            services_2.BusinessEntityService,
            repositories_1.LocationRepository,
            repositories_2.UserPermissionRepository
        ],
        controllers: [config_controller_1.ConfigController],
        imports: [],
        exports: [config_service_1.ConfigService],
    })
], ConfigModule);
exports.ConfigModule = ConfigModule;
//# sourceMappingURL=config.module.js.map