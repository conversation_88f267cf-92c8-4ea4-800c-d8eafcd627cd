{"version": 3, "file": "legal-entity.model.js", "sourceRoot": "", "sources": ["../../../src/location/models/legal-entity.model.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+DAA+F;AAC/F,gDAA8C;AAC9C,mDAA0C;AAG1C,qDAA4C;AAGrC,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,kBAAsB;CAuBtD,CAAA;AAtBG;IAAC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,uBAAO,CAAC;IACzB,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,+BAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;8CAChD;AAEzB;IAAC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,uBAAO,CAAC;8BACT,uBAAO;4CAAC;AAExB;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,+BAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;yCAC/C;AASpB;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,+BAAQ,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;iDAChC;AAE1C;IAAC,IAAA,8BAAO,EAAC,GAAG,EAAE,CAAC,yBAAQ,CAAC;;8CACK;AAtBpB,WAAW;IADvB,IAAA,4BAAK,EAAC,EAAE,SAAS,EAAE,qBAAqB,EAAE,CAAC;GAC/B,WAAW,CAuBvB;AAvBY,kCAAW"}