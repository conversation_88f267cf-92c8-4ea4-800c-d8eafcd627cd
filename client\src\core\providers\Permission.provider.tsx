import { FC, PropsWithChildren, useEffect, useState } from 'react';
import { SplashScreen } from '@/components/loading-screen';
import { getUserPermissions } from '@/shared/services';
import { UserPermissionsContext } from '../contexts';
import { Box, Container } from '@mui/material';
import Page503 from '@/components/error/503-view';
import Page500 from '@/components/error/500-view';
import NoPermissionPage from '@/components/error/no-permission';
import { CustomMessageView } from '@/components/error';

const PermissionProvider: FC<PropsWithChildren> = ({ children }) => {
  const [userPermissions, setUserPermissions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<number | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean>(false);

  const showImage = false;
  const URI = import.meta.env.VITE_SERVICE_HELP_URI || '';

  useEffect(() => {
    const loadAsync = async () => {
      try {
        const permissions = await getUserPermissions();

        // Check if user has any permissions
        if (permissions && permissions.length > 0) {
          setUserPermissions(permissions);
          setHasPermission(true);
        } else {
          setHasPermission(false);
        }

        setIsLoading(false);
      } catch (err: any) {
        setIsLoading(false);
        setHasError(err?.statusCode || 500);
        setHasPermission(false); // No permission on error
      }
    };
    loadAsync();
  }, []);

  // Show loading screen while checking permissions
  if (isLoading) {
    return <SplashScreen />;
  }

  // Show error pages if there was an error fetching permissions
  if (hasError) {
    return (
      <Container maxWidth={'lg'}>
        <Box
          sx={{
            right: 0,
            width: 1,
            bottom: 0,
            height: 1,
            zIndex: 9998,
            display: 'flex',
            position: 'absolute',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
          }}
        >
          {hasError === 401 || hasError === 403 ? (
            <CustomMessageView
              heading="Unauthorised Access!"
              message="You are not authorised to access this page, please contact your administrator."
              showImage={true}
              showButton={false}
              permissionError={true}
            />
          ) : hasError === 503 ? (
            <Page503 />
          ) : (
            <Page500 />
          )}
        </Box>
      </Container>
    );
  }

  // Show no permission page if user has no permissions
  if (!hasPermission) {
    //Enabled for all users to access the system.
    // return <NoPermissionPage showImage={showImage} URI={URI} />;
  }

  // Render children if user has permissions
  return <UserPermissionsContext.Provider value={userPermissions}>{children}</UserPermissionsContext.Provider>;
};

export default PermissionProvider;
