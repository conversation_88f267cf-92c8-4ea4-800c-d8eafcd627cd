"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetaEvidence = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
const master_capability_model_1 = require("./master-capability.model");
let MetaEvidence = class MetaEvidence extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'name', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], MetaEvidence.prototype, "name", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'description', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], MetaEvidence.prototype, "description", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'code', type: sequelize_typescript_1.DataType.STRING, allowNull: false, unique: true }),
    __metadata("design:type", String)
], MetaEvidence.prototype, "code", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'type', type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.EVIDENCE_TYPE)), allowNull: false }),
    __metadata("design:type", String)
], MetaEvidence.prototype, "type", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => master_capability_model_1.MasterCapability),
    __metadata("design:type", Array)
], MetaEvidence.prototype, "masterCapabilities", void 0);
MetaEvidence = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'meta_evidences' })
], MetaEvidence);
exports.MetaEvidence = MetaEvidence;
//# sourceMappingURL=meta-evidence.model.js.map