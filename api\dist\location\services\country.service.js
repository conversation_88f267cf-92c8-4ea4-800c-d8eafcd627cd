"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CountryService = void 0;
const common_1 = require("@nestjs/common");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const dtos_1 = require("../dtos");
const repositories_1 = require("../repositories");
const clients_1 = require("../../shared/clients");
const helpers_1 = require("../../shared/helpers");
const repositories_2 = require("../../contact-details/repositories");
const lodash_1 = require("lodash");
let CountryService = class CountryService {
    constructor(countryRepository, adminApiClient, databaseHelper, contactDetailRepository, historyService) {
        this.countryRepository = countryRepository;
        this.adminApiClient = adminApiClient;
        this.databaseHelper = databaseHelper;
        this.contactDetailRepository = contactDetailRepository;
        this.historyService = historyService;
    }
    upsertCountry(newCountrySetupRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId } = newCountrySetupRequestDto;
            const existingCountryDetail = yield this.countryRepository.getCountryDetailByCondition({
                entityId,
            });
            if (existingCountryDetail) {
                return yield this.updateCountrySetup(existingCountryDetail, newCountrySetupRequestDto, currentContext);
            }
            else {
                return yield this.createCountrySetup(newCountrySetupRequestDto, currentContext);
            }
        });
    }
    getCountryDetailWithLegalEntity(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const countryDetail = yield this.countryRepository.getCountryDetailWithLegalEntitiesByCondition({ entityId });
            if (!countryDetail) {
                throw new exceptions_1.HttpException('Country not found.', enums_1.HttpStatus.NOT_FOUND);
            }
            const { otherDetails } = countryDetail, countryDetailWithoutOtherDetails = __rest(countryDetail, ["otherDetails"]);
            const countryManagement = yield this.contactDetailRepository.getContactDetailsByCondition({
                entityId: entityId,
                objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID,
            });
            const transformedResults = Object.assign(Object.assign({}, countryDetailWithoutOtherDetails), { countryDetail: otherDetails, countryManagement });
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CountryWithEntityResponseDto, transformedResults, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        });
    }
    createCountrySetup(newCountrySetupRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId, countryManagement } = newCountrySetupRequestDto, otherDetails = __rest(newCountrySetupRequestDto, ["entityId", "countryManagement"]);
            const entityDetail = yield this.adminApiClient.getBusinessEntityDetailsById(entityId);
            if (!entityDetail) {
                throw new exceptions_1.HttpException('Country entity not found.', enums_1.HttpStatus.NOT_FOUND);
            }
            if (entityDetail.entity_type !== enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY) {
                throw new exceptions_1.HttpException('Provided entity is not a country.', enums_1.HttpStatus.BAD_REQUEST);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const newCountry = {
                    entityId,
                    entityCode: entityDetail.code,
                    entityTitle: entityDetail.full_name,
                    otherDetails,
                };
                const countryDetail = yield this.countryRepository.setupNewCountry(newCountry, currentContext);
                if (countryManagement === null || countryManagement === void 0 ? void 0 : countryManagement.length) {
                    const transformedManagementList = countryManagement.map(countryManagement => {
                        return {
                            entityId,
                            entityCode: entityDetail.code,
                            entityTitle: entityDetail.full_name,
                            entityType: entityDetail.entity_type,
                            objectId: countryManagement.coreSolutionId,
                            objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID,
                            userDetails: countryManagement.userDetails,
                        };
                    });
                    if (transformedManagementList.length) {
                        yield this.contactDetailRepository.addBulkContacts(transformedManagementList, currentContext);
                    }
                }
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: countryDetail.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.COUNTRY,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: 'New country added.',
                    additional_info: {
                        countryData: newCountrySetupRequestDto
                    }
                });
                return { message: 'New country setup successfully.' };
            }));
        });
    }
    updateCountrySetup(existingCountryDetail, newCountrySetupRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId, countryManagement } = newCountrySetupRequestDto, otherDetails = __rest(newCountrySetupRequestDto, ["entityId", "countryManagement"]);
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                var _a;
                yield this.countryRepository.updateCountryDetailsByCondition({ id: existingCountryDetail.id }, otherDetails, currentContext);
                const existingCountryManagement = yield this.contactDetailRepository.getContactDetailsByCondition({
                    entityId: entityId,
                    objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID,
                });
                const newEntries = [];
                const updatedEntries = [];
                const entriesToDelete = [];
                const existingManagementMap = new Map(existingCountryManagement.map(entry => [(0, lodash_1.toNumber)(entry.objectId), entry]));
                for (const newEntry of countryManagement || []) {
                    const existingEntry = existingManagementMap.get(newEntry.coreSolutionId);
                    if (existingEntry) {
                        if (((_a = newEntry.userDetails) === null || _a === void 0 ? void 0 : _a.length) > 0) {
                            updatedEntries.push(Object.assign(Object.assign({}, existingEntry), { userDetails: newEntry.userDetails }));
                        }
                        else {
                            entriesToDelete.push(existingEntry.id);
                        }
                    }
                    else {
                        newEntries.push({
                            entityId,
                            entityCode: existingCountryDetail.entityCode,
                            entityTitle: existingCountryDetail.entityTitle,
                            entityType: existingCountryDetail.entityType,
                            objectId: newEntry.coreSolutionId,
                            objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CORE_SOLUTION_ID,
                            userDetails: newEntry.userDetails,
                        });
                    }
                }
                if (newEntries.length > 0) {
                    yield this.contactDetailRepository.addBulkContacts(newEntries, currentContext);
                }
                for (const updatedEntry of updatedEntries) {
                    yield this.contactDetailRepository.updateUserDetailsByCondition({ id: updatedEntry.id }, updatedEntry.userDetails, currentContext);
                }
                if (entriesToDelete.length > 0) {
                    yield this.contactDetailRepository.deleteByIds(entriesToDelete, currentContext);
                }
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: existingCountryDetail.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.COUNTRY,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                    action_date: new Date(),
                    comments: 'Country detail updated.',
                    additional_info: {
                        countryData: newCountrySetupRequestDto,
                        oldCountryData: existingCountryDetail
                    }
                });
                return { message: 'country setup updated successfully.' };
            }));
        });
    }
};
CountryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.CountryRepository,
        clients_1.AdminApiClient,
        helpers_1.DatabaseHelper,
        repositories_2.ContactDetailRepository,
        clients_1.HistoryApiClient])
], CountryService);
exports.CountryService = CountryService;
//# sourceMappingURL=country.service.js.map