import { BaseRepository } from 'src/shared/repositories';
import { CommonDropdown } from '../models';
import { GetMetadataByTypeRequestDto } from '../dtos';
import { CurrentContext } from 'src/shared/types';
export declare class CommonDropdownRepository extends BaseRepository<CommonDropdown> {
    constructor();
    setupNewMetadataOfCommonTypes(payload: any, currentContext: CurrentContext): Promise<CommonDropdown>;
    getDropdownList(filter: GetMetadataByTypeRequestDto): Promise<CommonDropdown[] | null>;
    getStrategicClassificationType(coreSolutionId: number, locationTypeId: number): Promise<CommonDropdown[] | null>;
    isDropdownRecordExists(condition: any): Promise<boolean>;
}
