import { getIcon } from '@/shared/utils/get-icon';

export enum FIELD_KEY_ENUM {
  CAPABILITY_TYPE = 'entry_type',
  CAPABILITY_LEVEL = 'entry_level',
  CAPABILITY = 'entry',
  OWNER = 'owner',
  CORE_SOLUTION = 'core_solution',
  VERTICALS = 'verticals',

  PROVIDER = 'provider',
  CAPABILITY_STATUS = 'status',
  CAPABILITY_NAME = 'entries',
  PRODUCT_FAMILY = 'product_family',
  PRODUCT = 'product',
  CATEGORY = 'category',
  LOCATION = 'location',
  VIEW = 'view',
}

export enum LOCATION_ENTITY_TYPE {
  REGION = 'region',
  CLUSTER = 'cluster',
  COUNTRY = 'country',
}

// Enum for table column keys
export enum TableColumnKeys {
  STATUS = 'status',
  PRODUCT = 'product',
  LINK = 'link',
  DESCRIPTION = 'description',
  CAPABILITY_NAME = 'capability_name',
}

// Enum for icon paths
// we can not pass funtion in enum it requireds hard code string that is why I have converted it to object
export const IconPaths = {
  CAPABILITY: getIcon('Capability'),
  LOCATION: getIcon('Location'),
  FILTER: getIcon('Filter'),
};

// Enum for error messages
export enum ErrorMessages {
  BUSINESS_UNIT_FETCH_ERROR = 'Something went wrong while fetching locations!',
  NO_CAPABILITIES_FOUND = 'No Entries Found',
}

// Enum for action items
export enum ActionItems {
  VIEW = 'View',
  EDIT = 'Edit',
  DELETE = 'Delete',
}

// Enum for filter constants
export enum FilterConstants {
  TRIM_LENGTH = 30,
  MOBILE_TRIM_LENGTH = 25,
}

export enum CapabilityText {
  HEADING = 'Entries',
}

export enum ButtonText {
  LOCATIONS = 'Locations',
  FILTER = 'Filter',
}

export enum LOCATION_CAPABILITY_FILTER_FIELD_KEY_ENUM {
  CAPABILITY_TYPE = 'capabilityTypes',
  CAPABILITY_LEVEL = 'capabilityLevel',
  VERTICALS = 'verticalCodes',
  PROVIDER = 'providers',
  CAPABILITY_NAME = 'searchTerm',
  CAPABILITY_STATUS = 'statuses',
}
