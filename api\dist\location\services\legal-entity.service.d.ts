import { CurrentContext } from 'src/shared/types';
import { LegalEntityDropdownResponseDto, LegalEntitySetupRequestDto } from '../dtos';
import { MessageResponseDto } from 'src/shared/dtos';
import { CountryRepository, LegalEntityRepository, LocationRepository } from '../repositories';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedPermissionService } from 'src/shared/services';
import { HistoryApiClient } from 'src/shared/clients';
export declare class LegalEntityService {
    private readonly countryRepository;
    private readonly legalEntityRepository;
    private readonly locationRepository;
    private readonly sharedPermissionService;
    private readonly historyService;
    private readonly databaseHelper;
    constructor(countryRepository: CountryRepository, legalEntityRepository: LegalEntityRepository, locationRepository: LocationRepository, sharedPermissionService: SharedPermissionService, historyService: HistoryApiClient, databaseHelper: DatabaseHelper);
    upsertLegalEntity(legalEntitySetupRequestDto: LegalEntitySetupRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteLegalEntity(legalEntityId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getLegalEntityList(countryId: number, searchTerm?: string): Promise<LegalEntityDropdownResponseDto[]>;
}
