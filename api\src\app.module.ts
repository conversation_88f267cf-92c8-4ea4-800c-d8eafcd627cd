import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { SequelizeModule } from '@nestjs/sequelize';
import { Dialect } from 'sequelize';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AttachmentModule } from './attachment/attachments.module';
import { AuthModule } from './auth/auth.module';
import { BingoCardModule } from './bingo-card/bingo-card.module';
import { BusinessEntityModule } from './business-entity/business-entity.module';
import { CapabilityModule } from './capability/capability.module';
import { ConfigModule } from './config/config.module';
import { ConfigService } from './config/config.service';
import { ContactDetailModule } from './contact-details/contact-detail.module';
import { CoreModule } from './core/core.module';
import { HttpRequestInterceptor } from './core/interceptors';
import { LoggingInterceptor } from './core/interceptors/logger.interceptor';
import { DatabaseModule } from './database/database.module';
import { getSequelizeOrmConfig } from './database/orm-config';
import { GraphUserModule } from './graph-user/graph-user.module';
import { HistoryModule } from './history/history.module';
import { LocationModule } from './location/location.module';
import { MetadataModule } from './metadata/metadata.modules';
import { PermissionModule } from './permission/permission.module';
import { SharedModule } from './shared/shared.module';
import { SupportQueryModule } from './support-query/support-query.module';

@Module({
	imports: [
		DatabaseModule,
		ConfigModule,
		CoreModule,
		AuthModule,
		SharedModule,
		BusinessEntityModule,
		PermissionModule,
		GraphUserModule,
		AttachmentModule,
		MetadataModule,
		ContactDetailModule,
		LocationModule,
		CapabilityModule,
		HistoryModule,
		BingoCardModule,
		SupportQueryModule,
		SequelizeModule.forRootAsync({
			imports: [ConfigModule],
			useFactory: async (configService: ConfigService) => {
				const { database } = configService.getAppConfig();
				const { dialect, host, password, db, port, username, schema, enableSSL } = database;
				return {
					dialect: dialect as Dialect,
					host,
					port,
					database: db,
					username,
					password,
					schema,
					logging: false,
					...getSequelizeOrmConfig(enableSSL),
				};
			},
			inject: [ConfigService],
		}),
	],
	controllers: [AppController],
	providers: [
		AppService,
		Logger,
		{
			provide: APP_INTERCEPTOR,
			useClass: LoggingInterceptor,
		},
		{
			provide: APP_INTERCEPTOR,
			useClass: HttpRequestInterceptor,
		},
	],
})
export class AppModule {}
