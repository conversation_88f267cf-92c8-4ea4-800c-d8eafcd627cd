import { BusinessEntity } from 'src/shared/types';
import { CoreSolutionRepository } from 'src/metadata/repositories';
import { AdminApiClient } from 'src/shared/clients';
import { CreateLocationRequestDto } from '../dtos';
export declare class SetupNewLocationRequest {
    private readonly adminApiClient;
    private readonly coreSolutionRepository;
    constructor(adminApiClient: AdminApiClient, coreSolutionRepository: CoreSolutionRepository);
    validate(requestPayload: CreateLocationRequestDto, hasLegalPermission: boolean): Promise<{
        status: boolean;
        message?: string;
        data?: any;
    }>;
    getEntityDetailIfValid(entityId: number): Promise<BusinessEntity>;
    validateSection(locationTypeDetail: any, validSections: string[], requestPayload: CreateLocationRequestDto, hasLegalPermission: boolean): void;
    conditionalInputValidation(requestPayload: CreateLocationRequestDto, coreSolutionDetail: any, locationTypeDetail: any): void;
}
