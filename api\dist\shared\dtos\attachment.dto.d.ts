export declare class AttachmentDto {
    file_base64: string;
    attachment_name: string;
    type?: string;
    fileimage?: string | null;
    file?: any;
    attachment_content_type: string;
    file_id?: string;
    isdeleted?: boolean;
    isPrimary?: boolean;
    id?: number;
    isNew?: boolean;
    isEdited?: boolean;
    url?: string;
    size?: string;
    description?: string;
    prev_description?: string;
    additional_info?: string;
    section?: string;
    created_by?: string;
    created_on?: string;
    progress?: number;
}
