import { Injectable } from '@nestjs/common';
import { Pagination } from 'src/core/pagination';
import { AdminApiClient, MSGraphApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { EMAIL_TEMPLATES, NOTIFICATION_ENTITY_TYPE, USER_EMAIL_GROUP } from 'src/shared/enums';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedNotificationService } from 'src/shared/services';
import { CurrentContext } from 'src/shared/types';
import { PaginatedSupportQueryListResponseDto, SupportQueryRequestDto } from '../dtos';
import { SupportQueryRepository } from '../repositories';

@Injectable()
export class SupportQueryService {
	constructor(
		private readonly adminApiClient: AdminApiClient,
		private readonly supportQueryRepository: SupportQueryRepository,
		private readonly databaseHelper: DatabaseHelper,
		private readonly sharedNotificationService: SharedNotificationService,
		private readonly adminApiServie: AdminApiClient,
		private readonly mSGraphApiClient: MSGraphApiClient,
	) {}

	/**
	 * Create a new user support query
	 * @param data User support query data
	 * @param currentContext Current user context
	 * @returns Created user support query
	 */
	public async createUserSupportQuery(
		data: SupportQueryRequestDto,
		currentContext: CurrentContext,
	): Promise<MessageResponseDto> {
		const { query, url } = data;

		return await this.databaseHelper.startTransaction(async () => {
			try {
				await this.supportQueryRepository.createSupportQuery({ url, query }, currentContext);
				const userDetail = await this.adminApiServie.getRoleUserListByRoleName(
					USER_EMAIL_GROUP.PRODUCT_SUPPORT_EMAIL_GROUP,
				);

				const userEmails: string[] = [];

				const userIds = userDetail?.map(user => user.user_name.toLowerCase());
				const userAdDetails = userIds.length
					? await this.mSGraphApiClient.getUsersDetails(userIds)
					: [];
				userEmails.push(...userAdDetails.map(user => user.mail.toLowerCase()));

				const placeholderValues = { query };

				if (userEmails.length) {
					await this.sharedNotificationService.sendNotification(
						-1,
						NOTIFICATION_ENTITY_TYPE.SUPPORT_EMAIL_NOTIFICATION,
						{ to: userEmails },
						EMAIL_TEMPLATES.SUPPORT_EMAIL_NOTIFICATION,
						placeholderValues,
					);
				}
			} catch (error) {
				console.error('Failed to send support notification email:', error);
			}

			return { message: 'Your query raised successfully' };
		});
	}

	public async getQueryListByFilter(
		page: number,
		limit: number,
		filterDto: any = {},
	): Promise<PaginatedSupportQueryListResponseDto> {
		const { rows, count } = await this.supportQueryRepository.getQueryListByFilter(
			filterDto,
			page,
			limit,
		);

		return new Pagination({ records: rows, total: count });
	}
}
