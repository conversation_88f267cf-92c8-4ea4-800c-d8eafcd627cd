import { Injectable } from '@nestjs/common';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { SupportQueryRequestDto } from '../dtos';
import { SupportQueryRepository } from '../repositories';

@Injectable()
export class SupportQueryService {
	constructor(
		private readonly supportQueryRepository: SupportQueryRepository,
		private readonly databaseHelper: DatabaseHelper,
	) {}

	/**
	 * Create a new user support query
	 * @param data User support query data
	 * @param currentContext Current user context
	 * @returns Created user support query
	 */
	public async createUserSupportQuery(
		data: SupportQueryRequestDto,
		currentContext: CurrentContext,
	): Promise<MessageResponseDto> {
		const { query, url } = data;

		return await this.databaseHelper.startTransaction(async () => {
			const supportQuery = await this.supportQueryRepository.createSupportQuery(
				{
					url,
					query,
				},
				currentContext,
			);

			return { message: 'Your query raised successfully' };
		});
	}
}
