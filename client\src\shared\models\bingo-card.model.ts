import {
  CAPABILITY_LEVEL_ENUM,
  CAPABILITY_STATUS_ENUM,
  CAPABILITY_TYPE_ENUM,
  PROVIDER_ENUM,
} from '../enum/capability.enum';

export interface CapabilityList {
  id: number;
  title: string;
  isPersonalReport: boolean;

  description: string;
  capabilities: Capabilities[];
}

export interface ConfigList {
  id: number;
  title: string;
  description: string;
}

export interface Capabilities {
  id: number;
  product: string;
  category: Category;
  subCategory: string;
  capability: string;
  capabilityType: string;
  level: string;
  verticals: string[];
  legs: Legs[];
}

export interface Category {
  title: string;
}

export interface Legs {
  shortName: string;
  fullName: string;
  code: string;
}

export interface LocationCapabilityList {
  id: number;
  locationName: string;
  entityId: 0;
  status: string;
  capabilities: LocationCapability[];
}

export interface LocationCapability {
  id: number;
  capabilityId: number;
  status: any;
  legs: string[];
  capabilityDetail: CapabilityDetail;
}

export interface CapabilityDetail {
  id: number;
  capability: string;
  product: string;
  category: Category;
  subCategory: string;
  capabilityType: string;
  level: string;
  verticals: string[];
}

export interface BingoCardCapabilityListFiltersModel {
  coreSolutionIds?: number[];
  entryIds?: number[];
  capabilityTypes?: CAPABILITY_TYPE_ENUM[];
  capabilityLevel?: CAPABILITY_LEVEL_ENUM[];
  verticalCodes?: string[];
  statuses?: CAPABILITY_STATUS_ENUM[];
}
