import { BingoCardConfig } from 'src/bingo-card/models';
import { LocationWiseCapabilityDetail, MasterCapability, MasterCapabilityDropdowns, MetaEvidence } from 'src/capability/models';
import { CapabilityCategory } from 'src/capability/models/master-capability-category.model';
import { CapabilityLeg } from 'src/capability/models/master-capability-legs.model';
import { ContactDetail } from 'src/contact-details/models';
import { Country, LegalEntity, Location, LocationIndustryVertical, PartnerBranch } from 'src/location/models';
import { CommonDropdown, CoreSolution, LocationLifeCycleManagement, LocationType } from 'src/metadata/models';
import { AccessControlConfig, UserPermission } from 'src/permission/models';
import { SupportQuery } from 'src/support-query/models';
export declare const getSequelizeOrmConfig: (enableSSL?: boolean) => {
    ssl: boolean;
    dialectOptions: {
        ssl: {
            require: boolean;
        };
    };
    synchronize: boolean;
    autoLoadModels: boolean;
    models: (typeof LocationType | typeof CoreSolution | typeof CommonDropdown | typeof Location | typeof MetaEvidence | typeof MasterCapability | typeof CapabilityCategory | typeof LocationWiseCapabilityDetail | typeof CapabilityLeg | typeof MasterCapabilityDropdowns | typeof LocationLifeCycleManagement | typeof Country | typeof LegalEntity | typeof LocationIndustryVertical | typeof ContactDetail | typeof PartnerBranch | typeof UserPermission | typeof AccessControlConfig | typeof BingoCardConfig | typeof SupportQuery)[];
} | {
    ssl?: undefined;
    dialectOptions?: undefined;
    synchronize: boolean;
    autoLoadModels: boolean;
    models: (typeof LocationType | typeof CoreSolution | typeof CommonDropdown | typeof Location | typeof MetaEvidence | typeof MasterCapability | typeof CapabilityCategory | typeof LocationWiseCapabilityDetail | typeof CapabilityLeg | typeof MasterCapabilityDropdowns | typeof LocationLifeCycleManagement | typeof Country | typeof LegalEntity | typeof LocationIndustryVertical | typeof ContactDetail | typeof PartnerBranch | typeof UserPermission | typeof AccessControlConfig | typeof BingoCardConfig | typeof SupportQuery)[];
};
