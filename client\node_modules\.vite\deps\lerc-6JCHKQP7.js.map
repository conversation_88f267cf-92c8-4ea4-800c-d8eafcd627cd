{"version": 3, "sources": ["../../lerc/LercDecode.js", "../../geotiff/dist-module/compression/lerc.js", "../../zstddec/zstddec.ts"], "sourcesContent": ["﻿/* jshint forin: false, bitwise: false */\n/*\nCopyright 2015-2021 Esri\n\nLicensed under the Apache License, Version 2.0 (the \"License\");\nyou may not use this file except in compliance with the License.\nYou may obtain a copy of the License at\n\nhttp://www.apache.org/licenses/LICENSE-2.0\n\nUnless required by applicable law or agreed to in writing, software\ndistributed under the License is distributed on an \"AS IS\" BASIS,\nWITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\nSee the License for the specific language governing permissions and\nlimitations under the License.\n\nA copy of the license and additional notices are located with the\nsource distribution at:\n\nhttp://github.com/Esri/lerc/\n\nContributors:  <PERSON>, (LERC v1)\n               <PERSON><PERSON><PERSON>, (LERC v1)\n               Wenxue Ju (LERC v1, v2.x)\n*/\n\n/* Copyright 2015-2021 Esri. Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 @preserve */\n\n/**\n * a module for decoding LERC blobs\n * @module Lerc\n */\n(function() {\n  //this decoder supports all lerc versions, each version has its own class (LercDecode and Lerc2Decode). \n  //the exported module handles format variation autoamtically.\n\n  //the original LercDecode for Version 1\n  var LercDecode = (function() {\n\n    // Note: currently, this module only has an implementation for decoding LERC data, not encoding. The name of\n    // the class was chosen to be future proof.\n\n    var CntZImage = {};\n\n    CntZImage.defaultNoDataValue = -3.4027999387901484e+38; // smallest Float32 value\n\n    /**\n     * Decode a LERC byte stream and return an object containing the pixel data and some required and optional\n     * information about it, such as the image's width and height.\n     *\n     * @param {ArrayBuffer} input The LERC input byte stream\n     * @param {object} [options] Decoding options, containing any of the following properties:\n     * @config {number} [inputOffset = 0]\n     *        Skip the first inputOffset bytes of the input byte stream. A valid LERC file is expected at that position.\n     * @config {Uint8Array} [encodedMask = null]\n     *        If specified, the decoder will not read mask information from the input and use the specified encoded\n     *        mask data instead. Mask header/data must not be present in the LERC byte stream in this case.\n     * @config {number} [noDataValue = LercCode.defaultNoDataValue]\n     *        Pixel value to use for masked pixels.\n     * @config {ArrayBufferView|Array} [pixelType = Float32Array]\n     *        The desired type of the pixelData array in the return value. Note that it is the caller's responsibility to\n     *        provide an appropriate noDataValue if the default pixelType is overridden.\n     * @config {boolean} [returnMask = false]\n     *        If true, the return value will contain a maskData property of type Uint8Array which has one element per\n     *        pixel, the value of which is 1 or 0 depending on whether that pixel's data is present or masked. If the\n     *        input LERC data does not contain a mask, maskData will not be returned.\n     * @config {boolean} [returnEncodedMask = false]\n     *        If true, the return value will contain a encodedMaskData property, which can be passed into encode() as\n     *        encodedMask.\n     * @config {boolean} [returnFileInfo = false]\n     *        If true, the return value will have a fileInfo property that contains metadata obtained from the\n     *        LERC headers and the decoding process.\n     * @config {boolean} [computeUsedBitDepths = false]\n     *        If true, the fileInfo property in the return value will contain the set of all block bit depths\n     *        encountered during decoding. Will only have an effect if returnFileInfo option is true.\n     * @returns {{width, height, pixelData, minValue, maxValue, noDataValue, maskData, encodedMaskData, fileInfo}}\n     */\n    CntZImage.decode = function(input, options) {\n      options = options || {};\n\n      var skipMask = options.encodedMaskData || (options.encodedMaskData === null);\n      var parsedData = parse(input, options.inputOffset || 0, skipMask);\n\n      var noDataValue = (options.noDataValue !== null) ? options.noDataValue : CntZImage.defaultNoDataValue;\n\n      var uncompressedData = uncompressPixelValues(parsedData, options.pixelType || Float32Array,\n        options.encodedMaskData, noDataValue, options.returnMask);\n\n      var result = {\n        width: parsedData.width,\n        height: parsedData.height,\n        pixelData: uncompressedData.resultPixels,\n        minValue: uncompressedData.minValue,\n        maxValue: parsedData.pixels.maxValue,\n        noDataValue: noDataValue\n      };\n\n      if (uncompressedData.resultMask) {\n        result.maskData = uncompressedData.resultMask;\n      }\n\n      if (options.returnEncodedMask && parsedData.mask) {\n        result.encodedMaskData = parsedData.mask.bitset ? parsedData.mask.bitset : null;\n      }\n\n      if (options.returnFileInfo) {\n        result.fileInfo = formatFileInfo(parsedData);\n        if (options.computeUsedBitDepths) {\n          result.fileInfo.bitDepths = computeUsedBitDepths(parsedData);\n        }\n      }\n\n      return result;\n    };\n\n    var uncompressPixelValues = function(data, TypedArrayClass, maskBitset, noDataValue, storeDecodedMask) {\n      var blockIdx = 0;\n      var numX = data.pixels.numBlocksX;\n      var numY = data.pixels.numBlocksY;\n      var blockWidth = Math.floor(data.width / numX);\n      var blockHeight = Math.floor(data.height / numY);\n      var scale = 2 * data.maxZError;\n      var minValue = Number.MAX_VALUE, currentValue;\n      maskBitset = maskBitset || ((data.mask) ? data.mask.bitset : null);\n\n      var resultPixels, resultMask;\n      resultPixels = new TypedArrayClass(data.width * data.height);\n      if (storeDecodedMask && maskBitset) {\n        resultMask = new Uint8Array(data.width * data.height);\n      }\n      var blockDataBuffer = new Float32Array(blockWidth * blockHeight);\n\n      var xx, yy;\n      for (var y = 0; y <= numY; y++) {\n        var thisBlockHeight = (y !== numY) ? blockHeight : (data.height % numY);\n        if (thisBlockHeight === 0) {\n          continue;\n        }\n        for (var x = 0; x <= numX; x++) {\n          var thisBlockWidth = (x !== numX) ? blockWidth : (data.width % numX);\n          if (thisBlockWidth === 0) {\n            continue;\n          }\n\n          var outPtr = y * data.width * blockHeight + x * blockWidth;\n          var outStride = data.width - thisBlockWidth;\n\n          var block = data.pixels.blocks[blockIdx];\n\n          var blockData, blockPtr, constValue;\n          if (block.encoding < 2) {\n            // block is either uncompressed or bit-stuffed (encodings 0 and 1)\n            if (block.encoding === 0) {\n              // block is uncompressed\n              blockData = block.rawData;\n            } else {\n              // block is bit-stuffed\n              unstuff(block.stuffedData, block.bitsPerPixel, block.numValidPixels, block.offset, scale, blockDataBuffer, data.pixels.maxValue);\n              blockData = blockDataBuffer;\n            }\n            blockPtr = 0;\n          }\n          else if (block.encoding === 2) {\n            // block is all 0\n            constValue = 0;\n          }\n          else {\n            // block has constant value (encoding === 3)\n            constValue = block.offset;\n          }\n\n          var maskByte;\n          if (maskBitset) {\n            for (yy = 0; yy < thisBlockHeight; yy++) {\n              if (outPtr & 7) {\n                //\n                maskByte = maskBitset[outPtr >> 3];\n                maskByte <<= outPtr & 7;\n              }\n              for (xx = 0; xx < thisBlockWidth; xx++) {\n                if (!(outPtr & 7)) {\n                  // read next byte from mask\n                  maskByte = maskBitset[outPtr >> 3];\n                }\n                if (maskByte & 128) {\n                  // pixel data present\n                  if (resultMask) {\n                    resultMask[outPtr] = 1;\n                  }\n                  currentValue = (block.encoding < 2) ? blockData[blockPtr++] : constValue;\n                  minValue = minValue > currentValue ? currentValue : minValue;\n                  resultPixels[outPtr++] = currentValue;\n                } else {\n                  // pixel data not present\n                  if (resultMask) {\n                    resultMask[outPtr] = 0;\n                  }\n                  resultPixels[outPtr++] = noDataValue;\n                }\n                maskByte <<= 1;\n              }\n              outPtr += outStride;\n            }\n          } else {\n            // mask not present, simply copy block over\n            if (block.encoding < 2) {\n              // duplicating this code block for performance reasons\n              // blockData case:\n              for (yy = 0; yy < thisBlockHeight; yy++) {\n                for (xx = 0; xx < thisBlockWidth; xx++) {\n                  currentValue = blockData[blockPtr++];\n                  minValue = minValue > currentValue ? currentValue : minValue;\n                  resultPixels[outPtr++] = currentValue;\n                }\n                outPtr += outStride;\n              }\n            }\n            else {\n              // constValue case:\n              minValue = minValue > constValue ? constValue : minValue;\n              for (yy = 0; yy < thisBlockHeight; yy++) {\n                for (xx = 0; xx < thisBlockWidth; xx++) {\n                  resultPixels[outPtr++] = constValue;\n                }\n                outPtr += outStride;\n              }\n            }\n          }\n          if ((block.encoding === 1) && (blockPtr !== block.numValidPixels)) {\n            throw \"Block and Mask do not match\";\n          }\n          blockIdx++;\n        }\n      }\n\n      return {\n        resultPixels: resultPixels,\n        resultMask: resultMask,\n        minValue: minValue\n      };\n    };\n\n    var formatFileInfo = function(data) {\n      return {\n        \"fileIdentifierString\": data.fileIdentifierString,\n        \"fileVersion\": data.fileVersion,\n        \"imageType\": data.imageType,\n        \"height\": data.height,\n        \"width\": data.width,\n        \"maxZError\": data.maxZError,\n        \"eofOffset\": data.eofOffset,\n        \"mask\": data.mask ? {\n          \"numBlocksX\": data.mask.numBlocksX,\n          \"numBlocksY\": data.mask.numBlocksY,\n          \"numBytes\": data.mask.numBytes,\n          \"maxValue\": data.mask.maxValue\n        } : null,\n        \"pixels\": {\n          \"numBlocksX\": data.pixels.numBlocksX,\n          \"numBlocksY\": data.pixels.numBlocksY,\n          \"numBytes\": data.pixels.numBytes,\n          \"maxValue\": data.pixels.maxValue,\n          \"noDataValue\": data.noDataValue\n        }\n      };\n    };\n\n    var computeUsedBitDepths = function(data) {\n      var numBlocks = data.pixels.numBlocksX * data.pixels.numBlocksY;\n      var bitDepths = {};\n      for (var i = 0; i < numBlocks; i++) {\n        var block = data.pixels.blocks[i];\n        if (block.encoding === 0) {\n          bitDepths.float32 = true;\n        } else if (block.encoding === 1) {\n          bitDepths[block.bitsPerPixel] = true;\n        } else {\n          bitDepths[0] = true;\n        }\n      }\n\n      return Object.keys(bitDepths);\n    };\n\n    var parse = function(input, fp, skipMask) {\n      var data = {};\n\n      // File header\n      var fileIdView = new Uint8Array(input, fp, 10);\n      data.fileIdentifierString = String.fromCharCode.apply(null, fileIdView);\n      if (data.fileIdentifierString.trim() !== \"CntZImage\") {\n        throw \"Unexpected file identifier string: \" + data.fileIdentifierString;\n      }\n      fp += 10;\n      var view = new DataView(input, fp, 24);\n      data.fileVersion = view.getInt32(0, true);\n      data.imageType = view.getInt32(4, true);\n      data.height = view.getUint32(8, true);\n      data.width = view.getUint32(12, true);\n      data.maxZError = view.getFloat64(16, true);\n      fp += 24;\n\n      // Mask Header\n      if (!skipMask) {\n        view = new DataView(input, fp, 16);\n        data.mask = {};\n        data.mask.numBlocksY = view.getUint32(0, true);\n        data.mask.numBlocksX = view.getUint32(4, true);\n        data.mask.numBytes = view.getUint32(8, true);\n        data.mask.maxValue = view.getFloat32(12, true);\n        fp += 16;\n\n        // Mask Data\n        if (data.mask.numBytes > 0) {\n          var bitset = new Uint8Array(Math.ceil(data.width * data.height / 8));\n          view = new DataView(input, fp, data.mask.numBytes);\n          var cnt = view.getInt16(0, true);\n          var ip = 2, op = 0;\n          do {\n            if (cnt > 0) {\n              while (cnt--) { bitset[op++] = view.getUint8(ip++); }\n            } else {\n              var val = view.getUint8(ip++);\n              cnt = -cnt;\n              while (cnt--) { bitset[op++] = val; }\n            }\n            cnt = view.getInt16(ip, true);\n            ip += 2;\n          } while (ip < data.mask.numBytes);\n          if ((cnt !== -32768) || (op < bitset.length)) {\n            throw \"Unexpected end of mask RLE encoding\";\n          }\n          data.mask.bitset = bitset;\n          fp += data.mask.numBytes;\n        }\n        else if ((data.mask.numBytes | data.mask.numBlocksY | data.mask.maxValue) === 0) {  // Special case, all nodata\n          data.mask.bitset = new Uint8Array(Math.ceil(data.width * data.height / 8));\n        }\n      }\n\n      // Pixel Header\n      view = new DataView(input, fp, 16);\n      data.pixels = {};\n      data.pixels.numBlocksY = view.getUint32(0, true);\n      data.pixels.numBlocksX = view.getUint32(4, true);\n      data.pixels.numBytes = view.getUint32(8, true);\n      data.pixels.maxValue = view.getFloat32(12, true);\n      fp += 16;\n\n      var numBlocksX = data.pixels.numBlocksX;\n      var numBlocksY = data.pixels.numBlocksY;\n      // the number of blocks specified in the header does not take into account the blocks at the end of\n      // each row/column with a special width/height that make the image complete in case the width is not\n      // evenly divisible by the number of blocks.\n      var actualNumBlocksX = numBlocksX + ((data.width % numBlocksX) > 0 ? 1 : 0);\n      var actualNumBlocksY = numBlocksY + ((data.height % numBlocksY) > 0 ? 1 : 0);\n      data.pixels.blocks = new Array(actualNumBlocksX * actualNumBlocksY);\n      var blockI = 0;\n      for (var blockY = 0; blockY < actualNumBlocksY; blockY++) {\n        for (var blockX = 0; blockX < actualNumBlocksX; blockX++) {\n\n          // Block\n          var size = 0;\n          var bytesLeft = input.byteLength - fp;\n          view = new DataView(input, fp, Math.min(10, bytesLeft));\n          var block = {};\n          data.pixels.blocks[blockI++] = block;\n          var headerByte = view.getUint8(0); size++;\n          block.encoding = headerByte & 63;\n          if (block.encoding > 3) {\n            throw \"Invalid block encoding (\" + block.encoding + \")\";\n          }\n          if (block.encoding === 2) {\n            fp++;\n            continue;\n          }\n          if ((headerByte !== 0) && (headerByte !== 2)) {\n            headerByte >>= 6;\n            block.offsetType = headerByte;\n            if (headerByte === 2) {\n              block.offset = view.getInt8(1); size++;\n            } else if (headerByte === 1) {\n              block.offset = view.getInt16(1, true); size += 2;\n            } else if (headerByte === 0) {\n              block.offset = view.getFloat32(1, true); size += 4;\n            } else {\n              throw \"Invalid block offset type\";\n            }\n\n            if (block.encoding === 1) {\n              headerByte = view.getUint8(size); size++;\n              block.bitsPerPixel = headerByte & 63;\n              headerByte >>= 6;\n              block.numValidPixelsType = headerByte;\n              if (headerByte === 2) {\n                block.numValidPixels = view.getUint8(size); size++;\n              } else if (headerByte === 1) {\n                block.numValidPixels = view.getUint16(size, true); size += 2;\n              } else if (headerByte === 0) {\n                block.numValidPixels = view.getUint32(size, true); size += 4;\n              } else {\n                throw \"Invalid valid pixel count type\";\n              }\n            }\n          }\n          fp += size;\n\n          if (block.encoding === 3) {\n            continue;\n          }\n\n          var arrayBuf, store8;\n          if (block.encoding === 0) {\n            var numPixels = (data.pixels.numBytes - 1) / 4;\n            if (numPixels !== Math.floor(numPixels)) {\n              throw \"uncompressed block has invalid length\";\n            }\n            arrayBuf = new ArrayBuffer(numPixels * 4);\n            store8 = new Uint8Array(arrayBuf);\n            store8.set(new Uint8Array(input, fp, numPixels * 4));\n            var rawData = new Float32Array(arrayBuf);\n            block.rawData = rawData;\n            fp += numPixels * 4;\n          } else if (block.encoding === 1) {\n            var dataBytes = Math.ceil(block.numValidPixels * block.bitsPerPixel / 8);\n            var dataWords = Math.ceil(dataBytes / 4);\n            arrayBuf = new ArrayBuffer(dataWords * 4);\n            store8 = new Uint8Array(arrayBuf);\n            store8.set(new Uint8Array(input, fp, dataBytes));\n            block.stuffedData = new Uint32Array(arrayBuf);\n            fp += dataBytes;\n          }\n        }\n      }\n      data.eofOffset = fp;\n      return data;\n    };\n\n    var unstuff = function(src, bitsPerPixel, numPixels, offset, scale, dest, maxValue) {\n      var bitMask = (1 << bitsPerPixel) - 1;\n      var i = 0, o;\n      var bitsLeft = 0;\n      var n, buffer;\n      var nmax = Math.ceil((maxValue - offset) / scale);\n      // get rid of trailing bytes that are already part of next block\n      var numInvalidTailBytes = src.length * 4 - Math.ceil(bitsPerPixel * numPixels / 8);\n      src[src.length - 1] <<= 8 * numInvalidTailBytes;\n\n      for (o = 0; o < numPixels; o++) {\n        if (bitsLeft === 0) {\n          buffer = src[i++];\n          bitsLeft = 32;\n        }\n        if (bitsLeft >= bitsPerPixel) {\n          n = (buffer >>> (bitsLeft - bitsPerPixel)) & bitMask;\n          bitsLeft -= bitsPerPixel;\n        } else {\n          var missingBits = (bitsPerPixel - bitsLeft);\n          n = ((buffer & bitMask) << missingBits) & bitMask;\n          buffer = src[i++];\n          bitsLeft = 32 - missingBits;\n          n += (buffer >>> bitsLeft);\n        }\n        //pixel values may exceed max due to quantization\n        dest[o] = n < nmax ? offset + n * scale : maxValue;\n      }\n      return dest;\n    };\n\n    return CntZImage;\n  })();\n\n  //version 2. Supports 2.1, 2.2, 2.3\n  var Lerc2Decode = (function() {\n    \"use strict\";\n    // Note: currently, this module only has an implementation for decoding LERC data, not encoding. The name of\n    // the class was chosen to be future proof, following LercDecode.\n\n    /*****************************************\n    * private static class bitsutffer used by Lerc2Decode\n    *******************************************/\n    var BitStuffer = {\n      //methods ending with 2 are for the new byte order used by Lerc2.3 and above.\n      //originalUnstuff is used to unpack Huffman code table. code is duplicated to unstuffx for performance reasons.\n      unstuff: function(src, dest, bitsPerPixel, numPixels, lutArr, offset, scale, maxValue) {\n        var bitMask = (1 << bitsPerPixel) - 1;\n        var i = 0, o;\n        var bitsLeft = 0;\n        var n, buffer, missingBits, nmax;\n\n        // get rid of trailing bytes that are already part of next block\n        var numInvalidTailBytes = src.length * 4 - Math.ceil(bitsPerPixel * numPixels / 8);\n        src[src.length - 1] <<= 8 * numInvalidTailBytes;\n        if (lutArr) {\n          for (o = 0; o < numPixels; o++) {\n            if (bitsLeft === 0) {\n              buffer = src[i++];\n              bitsLeft = 32;\n            }\n            if (bitsLeft >= bitsPerPixel) {\n              n = (buffer >>> (bitsLeft - bitsPerPixel)) & bitMask;\n              bitsLeft -= bitsPerPixel;\n            }\n            else {\n              missingBits = (bitsPerPixel - bitsLeft);\n              n = ((buffer & bitMask) << missingBits) & bitMask;\n              buffer = src[i++];\n              bitsLeft = 32 - missingBits;\n              n += (buffer >>> bitsLeft);\n            }\n            dest[o] = lutArr[n];//offset + lutArr[n] * scale;\n          }\n        }\n        else {\n          nmax = Math.ceil((maxValue - offset) / scale);\n          for (o = 0; o < numPixels; o++) {\n            if (bitsLeft === 0) {\n              buffer = src[i++];\n              bitsLeft = 32;\n            }\n            if (bitsLeft >= bitsPerPixel) {\n              n = (buffer >>> (bitsLeft - bitsPerPixel)) & bitMask;\n              bitsLeft -= bitsPerPixel;\n            }\n            else {\n              missingBits = (bitsPerPixel - bitsLeft);\n              n = ((buffer & bitMask) << missingBits) & bitMask;\n              buffer = src[i++];\n              bitsLeft = 32 - missingBits;\n              n += (buffer >>> bitsLeft);\n            }\n            //pixel values may exceed max due to quantization\n            dest[o] = n < nmax ? offset + n * scale : maxValue;\n          }\n        }\n      },\n\n      unstuffLUT: function(src, bitsPerPixel, numPixels, offset, scale, maxValue) {\n        var bitMask = (1 << bitsPerPixel) - 1;\n        var i = 0, o = 0, missingBits = 0, bitsLeft = 0, n = 0;\n        var buffer;\n        var dest = [];\n\n        // get rid of trailing bytes that are already part of next block\n        var numInvalidTailBytes = src.length * 4 - Math.ceil(bitsPerPixel * numPixels / 8);\n        src[src.length - 1] <<= 8 * numInvalidTailBytes;\n\n        var nmax = Math.ceil((maxValue - offset) / scale);\n        for (o = 0; o < numPixels; o++) {\n          if (bitsLeft === 0) {\n            buffer = src[i++];\n            bitsLeft = 32;\n          }\n          if (bitsLeft >= bitsPerPixel) {\n            n = (buffer >>> (bitsLeft - bitsPerPixel)) & bitMask;\n            bitsLeft -= bitsPerPixel;\n          } else {\n            missingBits = (bitsPerPixel - bitsLeft);\n            n = ((buffer & bitMask) << missingBits) & bitMask;\n            buffer = src[i++];\n            bitsLeft = 32 - missingBits;\n            n += (buffer >>> bitsLeft);\n          }\n          //dest.push(n);\n          dest[o] = n < nmax ? offset + n * scale : maxValue;\n        }\n        dest.unshift(offset);//1st one\n        return dest;\n      },\n\n      unstuff2: function(src, dest, bitsPerPixel, numPixels, lutArr, offset, scale, maxValue) {\n        var bitMask = (1 << bitsPerPixel) - 1;\n        var i = 0, o;\n        var bitsLeft = 0, bitPos = 0;\n        var n, buffer, missingBits;\n        if (lutArr) {\n          for (o = 0; o < numPixels; o++) {\n            if (bitsLeft === 0) {\n              buffer = src[i++];\n              bitsLeft = 32;\n              bitPos = 0;\n            }\n            if (bitsLeft >= bitsPerPixel) {\n              n = ((buffer >>> bitPos) & bitMask);\n              bitsLeft -= bitsPerPixel;\n              bitPos += bitsPerPixel;\n            } else {\n              missingBits = (bitsPerPixel - bitsLeft);\n              n = (buffer >>> bitPos) & bitMask;\n              buffer = src[i++];\n              bitsLeft = 32 - missingBits;\n              n |= (buffer & ((1 << missingBits) - 1)) << (bitsPerPixel - missingBits);\n              bitPos = missingBits;\n            }\n            dest[o] = lutArr[n];\n          }\n        }\n        else {\n          var nmax = Math.ceil((maxValue - offset) / scale);\n          for (o = 0; o < numPixels; o++) {\n            if (bitsLeft === 0) {\n              buffer = src[i++];\n              bitsLeft = 32;\n              bitPos = 0;\n            }\n            if (bitsLeft >= bitsPerPixel) {\n              //no unsigned left shift\n              n = ((buffer >>> bitPos) & bitMask);\n              bitsLeft -= bitsPerPixel;\n              bitPos += bitsPerPixel;\n            } else {\n              missingBits = (bitsPerPixel - bitsLeft);\n              n = (buffer >>> bitPos) & bitMask;//((buffer & bitMask) << missingBits) & bitMask;\n              buffer = src[i++];\n              bitsLeft = 32 - missingBits;\n              n |= (buffer & ((1 << missingBits) - 1)) << (bitsPerPixel - missingBits);\n              bitPos = missingBits;\n            }\n            //pixel values may exceed max due to quantization\n            dest[o] = n < nmax ? offset + n * scale : maxValue;\n          }\n        }\n        return dest;\n      },\n\n      unstuffLUT2: function(src, bitsPerPixel, numPixels, offset, scale, maxValue) {\n        var bitMask = (1 << bitsPerPixel) - 1;\n        var i = 0, o = 0, missingBits = 0, bitsLeft = 0, n = 0, bitPos = 0;\n        var buffer;\n        var dest = [];\n        var nmax = Math.ceil((maxValue - offset) / scale);\n        for (o = 0; o < numPixels; o++) {\n          if (bitsLeft === 0) {\n            buffer = src[i++];\n            bitsLeft = 32;\n            bitPos = 0;\n          }\n          if (bitsLeft >= bitsPerPixel) {\n            //no unsigned left shift\n            n = ((buffer >>> bitPos) & bitMask);\n            bitsLeft -= bitsPerPixel;\n            bitPos += bitsPerPixel;\n          } else {\n            missingBits = (bitsPerPixel - bitsLeft);\n            n = (buffer >>> bitPos) & bitMask;//((buffer & bitMask) << missingBits) & bitMask;\n            buffer = src[i++];\n            bitsLeft = 32 - missingBits;\n            n |= (buffer & ((1 << missingBits) - 1)) << (bitsPerPixel - missingBits);\n            bitPos = missingBits;\n          }\n          //dest.push(n);\n          dest[o] = n < nmax ? offset + n * scale : maxValue;\n        }\n        dest.unshift(offset);\n        return dest;\n      },\n\n      originalUnstuff: function(src, dest, bitsPerPixel, numPixels) {\n        var bitMask = (1 << bitsPerPixel) - 1;\n        var i = 0, o;\n        var bitsLeft = 0;\n        var n, buffer, missingBits;\n\n        // get rid of trailing bytes that are already part of next block\n        var numInvalidTailBytes = src.length * 4 - Math.ceil(bitsPerPixel * numPixels / 8);\n        src[src.length - 1] <<= 8 * numInvalidTailBytes;\n\n        for (o = 0; o < numPixels; o++) {\n          if (bitsLeft === 0) {\n            buffer = src[i++];\n            bitsLeft = 32;\n          }\n          if (bitsLeft >= bitsPerPixel) {\n            n = (buffer >>> (bitsLeft - bitsPerPixel)) & bitMask;\n            bitsLeft -= bitsPerPixel;\n          }\n          else {\n            missingBits = (bitsPerPixel - bitsLeft);\n            n = ((buffer & bitMask) << missingBits) & bitMask;\n            buffer = src[i++];\n            bitsLeft = 32 - missingBits;\n            n += (buffer >>> bitsLeft);\n          }\n          dest[o] = n;\n        }\n        return dest;\n      },\n\n      originalUnstuff2: function(src, dest, bitsPerPixel, numPixels) {\n        var bitMask = (1 << bitsPerPixel) - 1;\n        var i = 0, o;\n        var bitsLeft = 0, bitPos = 0;\n        var n, buffer, missingBits;\n        //micro-optimizations\n        for (o = 0; o < numPixels; o++) {\n          if (bitsLeft === 0) {\n            buffer = src[i++];\n            bitsLeft = 32;\n            bitPos = 0;\n          }\n          if (bitsLeft >= bitsPerPixel) {\n            //no unsigned left shift\n            n = ((buffer >>> bitPos) & bitMask);\n            bitsLeft -= bitsPerPixel;\n            bitPos += bitsPerPixel;\n          } else {\n            missingBits = (bitsPerPixel - bitsLeft);\n            n = (buffer >>> bitPos) & bitMask;//((buffer & bitMask) << missingBits) & bitMask;\n            buffer = src[i++];\n            bitsLeft = 32 - missingBits;\n            n |= (buffer & ((1 << missingBits) - 1)) << (bitsPerPixel - missingBits);\n            bitPos = missingBits;\n          }\n          dest[o] = n;\n        }\n        return dest;\n      }\n    };\n\n    /*****************************************\n    *private static class used by Lerc2Decode\n    ******************************************/\n    var Lerc2Helpers = {\n      HUFFMAN_LUT_BITS_MAX: 12, //use 2^12 lut, treat it like constant\n      computeChecksumFletcher32: function(input) {\n\n        var sum1 = 0xffff, sum2 = 0xffff;\n        var len = input.length;\n        var words = Math.floor(len / 2);\n        var i = 0;\n        while (words) {\n          var tlen = (words >= 359) ? 359 : words;\n          words -= tlen;\n          do {\n            sum1 += (input[i++] << 8);\n            sum2 += sum1 += input[i++];\n          } while (--tlen);\n\n          sum1 = (sum1 & 0xffff) + (sum1 >>> 16);\n          sum2 = (sum2 & 0xffff) + (sum2 >>> 16);\n        }\n\n        // add the straggler byte if it exists\n        if (len & 1) {\n          sum2 += sum1 += (input[i] << 8);\n        }\n        // second reduction step to reduce sums to 16 bits\n        sum1 = (sum1 & 0xffff) + (sum1 >>> 16);\n        sum2 = (sum2 & 0xffff) + (sum2 >>> 16);\n\n        return (sum2 << 16 | sum1) >>> 0;\n      },\n\n      readHeaderInfo: function(input, data) {\n        var ptr = data.ptr;\n        var fileIdView = new Uint8Array(input, ptr, 6);\n        var headerInfo = {};\n        headerInfo.fileIdentifierString = String.fromCharCode.apply(null, fileIdView);\n        if (headerInfo.fileIdentifierString.lastIndexOf(\"Lerc2\", 0) !== 0) {\n          throw \"Unexpected file identifier string (expect Lerc2 ): \" + headerInfo.fileIdentifierString;\n        }\n        ptr += 6;\n        var view = new DataView(input, ptr, 8);\n        var fileVersion = view.getInt32(0, true);\n        headerInfo.fileVersion = fileVersion;\n        ptr += 4;\n        if (fileVersion >= 3) {\n          headerInfo.checksum = view.getUint32(4, true); //nrows\n          ptr += 4;\n        }\n\n        //keys start from here\n        view = new DataView(input, ptr, 12);\n        headerInfo.height = view.getUint32(0, true); //nrows\n        headerInfo.width = view.getUint32(4, true); //ncols\n        ptr += 8;\n        if (fileVersion >= 4) {\n          headerInfo.numDims = view.getUint32(8, true);\n          ptr += 4;\n        }\n        else {\n          headerInfo.numDims = 1;\n        }\n\n        view = new DataView(input, ptr, 40);\n        headerInfo.numValidPixel = view.getUint32(0, true);\n        headerInfo.microBlockSize = view.getInt32(4, true);\n        headerInfo.blobSize = view.getInt32(8, true);\n        headerInfo.imageType = view.getInt32(12, true);\n\n        headerInfo.maxZError = view.getFloat64(16, true);\n        headerInfo.zMin = view.getFloat64(24, true);\n        headerInfo.zMax = view.getFloat64(32, true);\n        ptr += 40;\n        data.headerInfo = headerInfo;\n        data.ptr = ptr;\n\n        var checksum, keyLength;\n        if (fileVersion >= 3) {\n          keyLength = fileVersion >= 4 ? 52 : 48;\n          checksum = this.computeChecksumFletcher32(new Uint8Array(input, ptr - keyLength, headerInfo.blobSize - 14));\n          if (checksum !== headerInfo.checksum) {\n            throw \"Checksum failed.\";\n          }\n        }\n        return true;\n      },\n\n      checkMinMaxRanges: function(input, data) {\n        var headerInfo = data.headerInfo;\n        var OutPixelTypeArray = this.getDataTypeArray(headerInfo.imageType);\n        var rangeBytes = headerInfo.numDims * this.getDataTypeSize(headerInfo.imageType);\n        var minValues = this.readSubArray(input, data.ptr, OutPixelTypeArray, rangeBytes);\n        var maxValues = this.readSubArray(input, data.ptr + rangeBytes, OutPixelTypeArray, rangeBytes);\n        data.ptr += (2 * rangeBytes);\n        var i, equal = true;\n        for (i = 0; i < headerInfo.numDims; i++) {\n          if (minValues[i] !== maxValues[i]) {\n            equal = false;\n            break;\n          }\n        }\n        headerInfo.minValues = minValues;\n        headerInfo.maxValues = maxValues;\n        return equal;\n      },\n\n      readSubArray: function(input, ptr, OutPixelTypeArray, numBytes) {\n        var rawData;\n        if (OutPixelTypeArray === Uint8Array) {\n          rawData = new Uint8Array(input, ptr, numBytes);\n        }\n        else {\n          var arrayBuf = new ArrayBuffer(numBytes);\n          var store8 = new Uint8Array(arrayBuf);\n          store8.set(new Uint8Array(input, ptr, numBytes));\n          rawData = new OutPixelTypeArray(arrayBuf);\n        }\n        return rawData;\n      },\n\n      readMask: function(input, data) {\n        var ptr = data.ptr;\n        var headerInfo = data.headerInfo;\n        var numPixels = headerInfo.width * headerInfo.height;\n        var numValidPixel = headerInfo.numValidPixel;\n\n        var view = new DataView(input, ptr, 4);\n        var mask = {};\n        mask.numBytes = view.getUint32(0, true);\n        ptr += 4;\n\n        // Mask Data\n        if ((0 === numValidPixel || numPixels === numValidPixel) && 0 !== mask.numBytes) {\n          throw (\"invalid mask\");\n        }\n        var bitset, resultMask;\n        if (numValidPixel === 0) {\n          bitset = new Uint8Array(Math.ceil(numPixels / 8));\n          mask.bitset = bitset;\n          resultMask = new Uint8Array(numPixels);\n          data.pixels.resultMask = resultMask;\n          ptr += mask.numBytes;\n        }// ????? else if (data.mask.numBytes > 0 && data.mask.numBytes< data.numValidPixel) {\n        else if (mask.numBytes > 0) {\n          bitset = new Uint8Array(Math.ceil(numPixels / 8));\n          view = new DataView(input, ptr, mask.numBytes);\n          var cnt = view.getInt16(0, true);\n          var ip = 2, op = 0, val = 0;\n          do {\n            if (cnt > 0) {\n              while (cnt--) { bitset[op++] = view.getUint8(ip++); }\n            } else {\n              val = view.getUint8(ip++);\n              cnt = -cnt;\n              while (cnt--) { bitset[op++] = val; }\n            }\n            cnt = view.getInt16(ip, true);\n            ip += 2;\n          } while (ip < mask.numBytes);\n          if ((cnt !== -32768) || (op < bitset.length)) {\n            throw \"Unexpected end of mask RLE encoding\";\n          }\n\n          resultMask = new Uint8Array(numPixels);\n          var mb = 0, k = 0;\n\n          for (k = 0; k < numPixels; k++) {\n            if (k & 7) {\n              mb = bitset[k >> 3];\n              mb <<= k & 7;\n            }\n            else {\n              mb = bitset[k >> 3];\n            }\n            if (mb & 128) {\n              resultMask[k] = 1;\n            }\n          }\n          data.pixels.resultMask = resultMask;\n\n          mask.bitset = bitset;\n          ptr += mask.numBytes;\n        }\n        data.ptr = ptr;\n        data.mask = mask;\n        return true;\n      },\n\n      readDataOneSweep: function(input, data, OutPixelTypeArray, useBSQForOutputDim) {\n        var ptr = data.ptr;\n        var headerInfo = data.headerInfo;\n        var numDims = headerInfo.numDims;\n        var numPixels = headerInfo.width * headerInfo.height;\n        var imageType = headerInfo.imageType;\n        var numBytes = headerInfo.numValidPixel * Lerc2Helpers.getDataTypeSize(imageType) * numDims;\n        //data.pixels.numBytes = numBytes;\n        var rawData;\n        var mask = data.pixels.resultMask;\n        if (OutPixelTypeArray === Uint8Array) {\n          rawData = new Uint8Array(input, ptr, numBytes);\n        }\n        else {\n          var arrayBuf = new ArrayBuffer(numBytes);\n          var store8 = new Uint8Array(arrayBuf);\n          store8.set(new Uint8Array(input, ptr, numBytes));\n          rawData = new OutPixelTypeArray(arrayBuf);\n        }\n        if (rawData.length === numPixels * numDims) {\n          if (useBSQForOutputDim) {\n            data.pixels.resultPixels = Lerc2Helpers.swapDimensionOrder(rawData, numPixels, numDims, OutPixelTypeArray, true);\n          }\n          else {\n            data.pixels.resultPixels = rawData;\n          }\n        }\n        else  //mask\n        {\n          data.pixels.resultPixels = new OutPixelTypeArray(numPixels * numDims);\n          var z = 0, k = 0, i = 0, nStart = 0;\n          if (numDims > 1) {\n            if (useBSQForOutputDim) {\n              for (k = 0; k < numPixels; k++) {\n                if (mask[k]) {\n                  nStart = k;\n                  for (i = 0; i < numDims; i++, nStart+=numPixels) {\n                    data.pixels.resultPixels[nStart] = rawData[z++];\n                  }\n                }\n              }\n            }\n            else {\n              for (k = 0; k < numPixels; k++) {\n                if (mask[k]) {\n                  nStart = k * numDims;\n                  for (i = 0; i < numDims; i++) {\n                    data.pixels.resultPixels[nStart + i] = rawData[z++];\n                  }\n                }\n              }\n            }\n          }\n          else {\n            for (k = 0; k < numPixels; k++) {\n              if (mask[k]) {\n                data.pixels.resultPixels[k] = rawData[z++];\n              }\n            }\n          }\n        }\n        ptr += numBytes;\n        data.ptr = ptr;       //return data;\n        return true;\n      },\n\n      readHuffmanTree: function(input, data) {\n        var BITS_MAX = this.HUFFMAN_LUT_BITS_MAX; //8 is slow for the large test image\n        //var size_max = 1 << BITS_MAX;\n        /* ************************\n        * reading code table\n        *************************/\n        var view = new DataView(input, data.ptr, 16);\n        data.ptr += 16;\n        var version = view.getInt32(0, true);\n        if (version < 2) {\n          throw \"unsupported Huffman version\";\n        }\n        var size = view.getInt32(4, true);\n        var i0 = view.getInt32(8, true);\n        var i1 = view.getInt32(12, true);\n        if (i0 >= i1) {\n          return false;\n        }\n        var blockDataBuffer = new Uint32Array(i1 - i0);\n        Lerc2Helpers.decodeBits(input, data, blockDataBuffer);\n        var codeTable = []; //size\n        var i, j, k, len;\n\n        for (i = i0; i < i1; i++) {\n          j = i - (i < size ? 0 : size);//wrap around\n          codeTable[j] = { first: blockDataBuffer[i - i0], second: null };\n        }\n\n        var dataBytes = input.byteLength - data.ptr;\n        var dataWords = Math.ceil(dataBytes / 4);\n        var arrayBuf = new ArrayBuffer(dataWords * 4);\n        var store8 = new Uint8Array(arrayBuf);\n        store8.set(new Uint8Array(input, data.ptr, dataBytes));\n        var stuffedData = new Uint32Array(arrayBuf); //must start from x*4\n        var bitPos = 0, word, srcPtr = 0;\n        word = stuffedData[0];\n        for (i = i0; i < i1; i++) {\n          j = i - (i < size ? 0 : size);//wrap around\n          len = codeTable[j].first;\n          if (len > 0) {\n            codeTable[j].second = (word << bitPos) >>> (32 - len);\n\n            if (32 - bitPos >= len) {\n              bitPos += len;\n              if (bitPos === 32) {\n                bitPos = 0;\n                srcPtr++;\n                word = stuffedData[srcPtr];\n              }\n            }\n            else {\n              bitPos += len - 32;\n              srcPtr++;\n              word = stuffedData[srcPtr];\n              codeTable[j].second |= word >>> (32 - bitPos);\n            }\n          }\n        }\n\n        //finished reading code table\n\n        /* ************************\n        * building lut\n        *************************/\n        var numBitsLUT = 0, numBitsLUTQick = 0;\n        var tree = new TreeNode();\n        for (i = 0; i < codeTable.length; i++) {\n          if (codeTable[i] !== undefined) {\n            numBitsLUT = Math.max(numBitsLUT, codeTable[i].first);\n          }\n        }\n        if (numBitsLUT >= BITS_MAX) {\n          numBitsLUTQick = BITS_MAX;\n        }\n        else {\n          numBitsLUTQick = numBitsLUT;\n        }\n        // for debugging purpose\n        // if (numBitsLUT >= 30) {\n        //   console.log(\"WARning, large NUM LUT BITS IS \" + numBitsLUT);\n        // }\n        var decodeLut = [], entry, code, numEntries, jj, currentBit, node;\n        for (i = i0; i < i1; i++) {\n          j = i - (i < size ? 0 : size);//wrap around\n          len = codeTable[j].first;\n          if (len > 0) {\n            entry = [len, j];\n            if (len <= numBitsLUTQick) {\n              code = codeTable[j].second << (numBitsLUTQick - len);\n              numEntries = 1 << (numBitsLUTQick - len);\n              for (k = 0; k < numEntries; k++) {\n                decodeLut[code | k] = entry;\n              }\n            }\n            else {\n              //build tree\n              code = codeTable[j].second;\n              node = tree;\n              for (jj = len - 1; jj >= 0; jj--) {\n                currentBit = code >>> jj & 1; //no left shift as length could be 30,31\n                if (currentBit) {\n                  if (!node.right) {\n                    node.right = new TreeNode();\n                  }\n                  node = node.right;\n                }\n                else {\n                  if (!node.left) {\n                    node.left = new TreeNode();\n                  }\n                  node = node.left;\n                }\n                if (jj === 0 && !node.val) {\n                  node.val = entry[1];\n                }\n              }\n            }\n          }\n        }\n        return {\n          decodeLut: decodeLut,\n          numBitsLUTQick: numBitsLUTQick,\n          numBitsLUT: numBitsLUT,\n          tree: tree,\n          stuffedData: stuffedData,\n          srcPtr: srcPtr,\n          bitPos: bitPos\n        };\n      },\n\n      readHuffman: function(input, data, OutPixelTypeArray, useBSQForOutputDim) {\n        var headerInfo = data.headerInfo;\n        var numDims = headerInfo.numDims;\n        var height = data.headerInfo.height;\n        var width = data.headerInfo.width;\n        var numPixels = width * height;\n        //var size_max = 1 << BITS_MAX;\n        /* ************************\n        * reading huffman structure info\n        *************************/\n        var huffmanInfo = this.readHuffmanTree(input, data);\n        var decodeLut = huffmanInfo.decodeLut;\n        var tree = huffmanInfo.tree;\n        //stuffedData includes huffman headers\n        var stuffedData = huffmanInfo.stuffedData;\n        var srcPtr = huffmanInfo.srcPtr;\n        var bitPos = huffmanInfo.bitPos;\n        var numBitsLUTQick = huffmanInfo.numBitsLUTQick;\n        var numBitsLUT = huffmanInfo.numBitsLUT;\n        var offset = data.headerInfo.imageType === 0 ? 128 : 0;\n        /*************************\n        *  decode\n        ***************************/\n        var node, val, delta, mask = data.pixels.resultMask, valTmp, valTmpQuick, currentBit;\n        var i, j, k, ii;\n        var prevVal = 0;\n        if (bitPos > 0) {\n          srcPtr++;\n          bitPos = 0;\n        }\n        var word = stuffedData[srcPtr];\n        var deltaEncode = data.encodeMode === 1;\n        var resultPixelsAllDim = new OutPixelTypeArray(numPixels * numDims);\n        var resultPixels = resultPixelsAllDim;\n        var iDim;\n        // TODO: reevaluate the need to keep inlined decoding code as IE support is phasing out\n        if (numDims < 2 || deltaEncode) {\n          for (iDim = 0; iDim < numDims; iDim++) {\n            if (numDims > 1) {\n              //get the mem block of current dimension\n              resultPixels = new OutPixelTypeArray(resultPixelsAllDim.buffer, numPixels * iDim, numPixels);\n              prevVal = 0;\n            }\n            if (data.headerInfo.numValidPixel === width * height) { //all valid\n              for (k = 0, i = 0; i < height; i++) {\n                for (j = 0; j < width; j++, k++) {\n                  val = 0;\n                  valTmp = (word << bitPos) >>> (32 - numBitsLUTQick);\n                  valTmpQuick = valTmp;// >>> deltaBits;\n                  if (32 - bitPos < numBitsLUTQick) {\n                    valTmp |= ((stuffedData[srcPtr + 1]) >>> (64 - bitPos - numBitsLUTQick));\n                    valTmpQuick = valTmp;// >>> deltaBits;\n                  }\n                  if (decodeLut[valTmpQuick])    // if there, move the correct number of bits and done\n                  {\n                    val = decodeLut[valTmpQuick][1];\n                    bitPos += decodeLut[valTmpQuick][0];\n                  }\n                  else {\n                    valTmp = (word << bitPos) >>> (32 - numBitsLUT);\n                    valTmpQuick = valTmp;// >>> deltaBits;\n                    if (32 - bitPos < numBitsLUT) {\n                      valTmp |= ((stuffedData[srcPtr + 1]) >>> (64 - bitPos - numBitsLUT));\n                      valTmpQuick = valTmp;// >>> deltaBits;\n                    }\n                    node = tree;\n                    for (ii = 0; ii < numBitsLUT; ii++) {\n                      currentBit = valTmp >>> (numBitsLUT - ii - 1) & 1;\n                      node = currentBit ? node.right : node.left;\n                      if (!(node.left || node.right)) {\n                        val = node.val;\n                        bitPos = bitPos + ii + 1;\n                        break;\n                      }\n                    }\n                  }\n    \n                  if (bitPos >= 32) {\n                    bitPos -= 32;\n                    srcPtr++;\n                    word = stuffedData[srcPtr];\n                  }\n    \n                  delta = val - offset;\n                  if (deltaEncode) {\n                    if (j > 0) {\n                      delta += prevVal;    // use overflow\n                    }\n                    else if (i > 0) {\n                      delta += resultPixels[k - width];\n                    }\n                    else {\n                      delta += prevVal;\n                    }\n                    delta &= 0xFF; //overflow\n                    resultPixels[k] = delta;//overflow\n                    prevVal = delta;\n                  }\n                  else {\n                    resultPixels[k] = delta;\n                  }\n                }\n              }\n            }\n            else { //not all valid, use mask\n              for (k = 0, i = 0; i < height; i++) {\n                for (j = 0; j < width; j++, k++) {\n                  if (mask[k]) {\n                    val = 0;\n                    valTmp = (word << bitPos) >>> (32 - numBitsLUTQick);\n                    valTmpQuick = valTmp;// >>> deltaBits;\n                    if (32 - bitPos < numBitsLUTQick) {\n                      valTmp |= ((stuffedData[srcPtr + 1]) >>> (64 - bitPos - numBitsLUTQick));\n                      valTmpQuick = valTmp;// >>> deltaBits;\n                    }\n                    if (decodeLut[valTmpQuick])    // if there, move the correct number of bits and done\n                    {\n                      val = decodeLut[valTmpQuick][1];\n                      bitPos += decodeLut[valTmpQuick][0];\n                    }\n                    else {\n                      valTmp = (word << bitPos) >>> (32 - numBitsLUT);\n                      valTmpQuick = valTmp;// >>> deltaBits;\n                      if (32 - bitPos < numBitsLUT) {\n                        valTmp |= ((stuffedData[srcPtr + 1]) >>> (64 - bitPos - numBitsLUT));\n                        valTmpQuick = valTmp;// >>> deltaBits;\n                      }\n                      node = tree;\n                      for (ii = 0; ii < numBitsLUT; ii++) {\n                        currentBit = valTmp >>> (numBitsLUT - ii - 1) & 1;\n                        node = currentBit ? node.right : node.left;\n                        if (!(node.left || node.right)) {\n                          val = node.val;\n                          bitPos = bitPos + ii + 1;\n                          break;\n                        }\n                      }\n                    }\n    \n                    if (bitPos >= 32) {\n                      bitPos -= 32;\n                      srcPtr++;\n                      word = stuffedData[srcPtr];\n                    }\n    \n                    delta = val - offset;\n                    if (deltaEncode) {\n                      if (j > 0 && mask[k - 1]) {\n                        delta += prevVal;    // use overflow\n                      }\n                      else if (i > 0 && mask[k - width]) {\n                        delta += resultPixels[k - width];\n                      }\n                      else {\n                        delta += prevVal;\n                      }\n    \n                      delta &= 0xFF; //overflow\n                      resultPixels[k] = delta;//overflow\n                      prevVal = delta;\n                    }\n                    else {\n                      resultPixels[k] = delta;\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n        else {\n          for (k = 0, i = 0; i < height; i++) {\n            for (j = 0; j < width; j++) {\n              k = i * width + j;\n              if (!mask || mask[k]) {\n                for (iDim = 0; iDim < numDims; iDim++, k+=numPixels) {\n                  val = 0;\n                  valTmp = (word << bitPos) >>> (32 - numBitsLUTQick);\n                  valTmpQuick = valTmp;\n                  if (32 - bitPos < numBitsLUTQick) {\n                    valTmp |= ((stuffedData[srcPtr + 1]) >>> (64 - bitPos - numBitsLUTQick));\n                    valTmpQuick = valTmp;\n                  }\n                  if (decodeLut[valTmpQuick])\n                  {\n                    val = decodeLut[valTmpQuick][1];\n                    bitPos += decodeLut[valTmpQuick][0];\n                  }\n                  else {\n                    valTmp = (word << bitPos) >>> (32 - numBitsLUT);\n                    valTmpQuick = valTmp;\n                    if (32 - bitPos < numBitsLUT) {\n                      valTmp |= ((stuffedData[srcPtr + 1]) >>> (64 - bitPos - numBitsLUT));\n                      valTmpQuick = valTmp;\n                    }\n                    node = tree;\n                    for (ii = 0; ii < numBitsLUT; ii++) {\n                      currentBit = valTmp >>> (numBitsLUT - ii - 1) & 1;\n                      node = currentBit ? node.right : node.left;\n                      if (!(node.left || node.right)) {\n                        val = node.val;\n                        bitPos = bitPos + ii + 1;\n                        break;\n                      }\n                    }\n                  }\n\n                  if (bitPos >= 32) {\n                    bitPos -= 32;\n                    srcPtr++;\n                    word = stuffedData[srcPtr];\n                  }\n\n                  delta = val - offset;\n                  resultPixels[k] = delta;\n                }\n              }\n            }\n          }\n        }\n        data.ptr = data.ptr + (srcPtr + 1) * 4 + (bitPos > 0 ? 4 : 0);\n        data.pixels.resultPixels = resultPixelsAllDim;\n        //swap for BIP layout\n        if (numDims > 1 && !useBSQForOutputDim) {\n          data.pixels.resultPixels = Lerc2Helpers.swapDimensionOrder(resultPixelsAllDim, numPixels, numDims, OutPixelTypeArray);\n        }\n      },\n\n      decodeBits: function(input, data, blockDataBuffer, offset, iDim) {\n        {\n          //bitstuff encoding is 3\n          var headerInfo = data.headerInfo;\n          var fileVersion = headerInfo.fileVersion;\n          //var block = {};\n          var blockPtr = 0;\n          var viewByteLength = ((input.byteLength - data.ptr) >= 5) ? 5 : (input.byteLength - data.ptr);\n          var view = new DataView(input, data.ptr, viewByteLength);\n          var headerByte = view.getUint8(0);\n          blockPtr++;\n          var bits67 = headerByte >> 6;\n          var n = (bits67 === 0) ? 4 : 3 - bits67;\n          var doLut = (headerByte & 32) > 0 ? true : false;//5th bit\n          var numBits = headerByte & 31;\n          var numElements = 0;\n          if (n === 1) {\n            numElements = view.getUint8(blockPtr); blockPtr++;\n          } else if (n === 2) {\n            numElements = view.getUint16(blockPtr, true); blockPtr += 2;\n          } else if (n === 4) {\n            numElements = view.getUint32(blockPtr, true); blockPtr += 4;\n          } else {\n            throw \"Invalid valid pixel count type\";\n          }\n          //fix: huffman codes are bit stuffed, but not bound by data's max value, so need to use originalUnstuff\n          //offset = offset || 0;\n          var scale = 2 * headerInfo.maxZError;\n          var stuffedData, arrayBuf, store8, dataBytes, dataWords;\n          var lutArr, lutData, lutBytes, lutBitsPerElement, bitsPerPixel;\n          var zMax = headerInfo.numDims > 1 ? headerInfo.maxValues[iDim] : headerInfo.zMax;\n          if (doLut) {\n            data.counter.lut++;\n            lutBytes = view.getUint8(blockPtr);\n            lutBitsPerElement = numBits;\n            blockPtr++;\n            dataBytes = Math.ceil((lutBytes - 1) * numBits / 8);\n            dataWords = Math.ceil(dataBytes / 4);\n            arrayBuf = new ArrayBuffer(dataWords * 4);\n            store8 = new Uint8Array(arrayBuf);\n\n            data.ptr += blockPtr;\n            store8.set(new Uint8Array(input, data.ptr, dataBytes));\n\n            lutData = new Uint32Array(arrayBuf);\n            data.ptr += dataBytes;\n\n            bitsPerPixel = 0;\n            while ((lutBytes - 1) >>> bitsPerPixel) {\n              bitsPerPixel++;\n            }\n            dataBytes = Math.ceil(numElements * bitsPerPixel / 8);\n            dataWords = Math.ceil(dataBytes / 4);\n            arrayBuf = new ArrayBuffer(dataWords * 4);\n            store8 = new Uint8Array(arrayBuf);\n            store8.set(new Uint8Array(input, data.ptr, dataBytes));\n            stuffedData = new Uint32Array(arrayBuf);\n            data.ptr += dataBytes;\n            if (fileVersion >= 3) {\n              lutArr = BitStuffer.unstuffLUT2(lutData, numBits, lutBytes - 1, offset, scale, zMax);\n            }\n            else {\n              lutArr = BitStuffer.unstuffLUT(lutData, numBits, lutBytes - 1, offset, scale, zMax);\n            }\n            //lutArr.unshift(0);\n            if (fileVersion >= 3) {\n              //BitStuffer.unstuff2(block, blockDataBuffer, headerInfo.zMax);\n              BitStuffer.unstuff2(stuffedData, blockDataBuffer, bitsPerPixel, numElements, lutArr);\n            }\n            else {\n              BitStuffer.unstuff(stuffedData, blockDataBuffer, bitsPerPixel, numElements, lutArr);\n            }\n          }\n          else {\n            //console.debug(\"bitstuffer\");\n            data.counter.bitstuffer++;\n            bitsPerPixel = numBits;\n            data.ptr += blockPtr;\n            if (bitsPerPixel > 0) {\n              dataBytes = Math.ceil(numElements * bitsPerPixel / 8);\n              dataWords = Math.ceil(dataBytes / 4);\n              arrayBuf = new ArrayBuffer(dataWords * 4);\n              store8 = new Uint8Array(arrayBuf);\n              store8.set(new Uint8Array(input, data.ptr, dataBytes));\n              stuffedData = new Uint32Array(arrayBuf);\n              data.ptr += dataBytes;\n              if (fileVersion >= 3) {\n                if (offset == null) {\n                  BitStuffer.originalUnstuff2(stuffedData, blockDataBuffer, bitsPerPixel, numElements);\n                }\n                else {\n                  BitStuffer.unstuff2(stuffedData, blockDataBuffer, bitsPerPixel, numElements, false, offset, scale, zMax);\n                }\n              }\n              else {\n                if (offset == null) {\n                  BitStuffer.originalUnstuff(stuffedData, blockDataBuffer, bitsPerPixel, numElements);\n                }\n                else {\n                  BitStuffer.unstuff(stuffedData, blockDataBuffer, bitsPerPixel, numElements, false, offset, scale, zMax);\n                }\n              }\n            }\n          }\n        }\n\n      },\n\n      readTiles: function(input, data, OutPixelTypeArray, useBSQForOutputDim) {\n        var headerInfo = data.headerInfo;\n        var width = headerInfo.width;\n        var height = headerInfo.height;\n        var numPixels = width * height;\n        var microBlockSize = headerInfo.microBlockSize;\n        var imageType = headerInfo.imageType;\n        var dataTypeSize = Lerc2Helpers.getDataTypeSize(imageType);\n        var numBlocksX = Math.ceil(width / microBlockSize);\n        var numBlocksY = Math.ceil(height / microBlockSize);\n        data.pixels.numBlocksY = numBlocksY;\n        data.pixels.numBlocksX = numBlocksX;\n        data.pixels.ptr = 0;\n        var row = 0, col = 0, blockY = 0, blockX = 0, thisBlockHeight = 0, thisBlockWidth = 0, bytesLeft = 0, headerByte = 0, bits67 = 0, testCode = 0, outPtr = 0, outStride = 0, numBytes = 0, bytesleft = 0, z = 0, blockPtr = 0;\n        var view, block, arrayBuf, store8, rawData;\n        var blockEncoding;\n        var blockDataBuffer = new OutPixelTypeArray(microBlockSize * microBlockSize);\n        var lastBlockHeight = (height % microBlockSize) || microBlockSize;\n        var lastBlockWidth = (width % microBlockSize) || microBlockSize;\n        var offsetType, offset;\n        var numDims = headerInfo.numDims, iDim;\n        var mask = data.pixels.resultMask;\n        var resultPixels = data.pixels.resultPixels;\n        var fileVersion = headerInfo.fileVersion;\n        var fileVersionCheckNum = fileVersion >= 5 ? 14 : 15;\n        var isDiffEncoding;\n        var zMax = headerInfo.zMax;\n        //var resultPixelsAllDim = resultPixels;\n        var resultPixelsPrevDim;\n        for (blockY = 0; blockY < numBlocksY; blockY++) {\n          thisBlockHeight = (blockY !== numBlocksY - 1) ? microBlockSize : lastBlockHeight;\n          for (blockX = 0; blockX < numBlocksX; blockX++) {\n            //console.debug(\"y\" + blockY + \" x\" + blockX);\n            thisBlockWidth = (blockX !== numBlocksX - 1) ? microBlockSize : lastBlockWidth;\n\n            outPtr = blockY * width * microBlockSize + blockX * microBlockSize;\n            outStride = width - thisBlockWidth;\n\n            for (iDim = 0; iDim < numDims; iDim++) {\n              if (numDims > 1) {\n                resultPixelsPrevDim = resultPixels;\n                outPtr = blockY * width * microBlockSize + blockX * microBlockSize;\n                resultPixels = new OutPixelTypeArray(data.pixels.resultPixels.buffer, numPixels * iDim * dataTypeSize, numPixels);\n                zMax = headerInfo.maxValues[iDim];\n              } else {\n                resultPixelsPrevDim = null;\n              }\n              bytesLeft = input.byteLength - data.ptr;\n              view = new DataView(input, data.ptr, Math.min(10, bytesLeft));\n              block = {};\n              blockPtr = 0;\n              headerByte = view.getUint8(0);\n              blockPtr++;\n              isDiffEncoding = headerInfo.fileVersion >= 5 ? headerByte & 4 : 0;\n              bits67 = (headerByte >> 6) & 0xFF;\n              testCode = (headerByte >> 2) & fileVersionCheckNum;    // use bits 2345 for integrity check\n              if (testCode !== (((blockX * microBlockSize) >> 3) & fileVersionCheckNum)) {\n                throw \"integrity issue\";\n              }\n\n              if (isDiffEncoding && iDim === 0) {\n                throw \"integrity issue\";\n              }\n\n              blockEncoding = headerByte & 3;\n              if (blockEncoding > 3) {\n                data.ptr += blockPtr;\n                throw \"Invalid block encoding (\" + blockEncoding + \")\";\n              }\n              else if (blockEncoding === 2) { //constant 0\n                if (isDiffEncoding) {\n                  if (mask) {\n                    for (row = 0; row < thisBlockHeight; row++) {\n                      for (col = 0; col < thisBlockWidth; col++) {\n                        if (mask[outPtr]) {\n                          resultPixels[outPtr] = resultPixelsPrevDim[outPtr];\n                        }\n                        outPtr++;\n                      }\n                    }\n                  }\n                  else {\n                    for (row = 0; row < thisBlockHeight; row++) {\n                      for (col = 0; col < thisBlockWidth; col++) {\n                        resultPixels[outPtr] = resultPixelsPrevDim[outPtr];\n                        outPtr++;\n                      }\n                    }\n                  }\n                }\n                data.counter.constant++;\n                data.ptr += blockPtr;\n                continue;\n              }\n              else if (blockEncoding === 0) {  //uncompressed\n                if (isDiffEncoding) {\n                  // doesn't make sense, should not happen\n                  throw \"integrity issue\";\n                }\n                data.counter.uncompressed++;\n                data.ptr += blockPtr;\n                numBytes = thisBlockHeight * thisBlockWidth * dataTypeSize;\n                bytesleft = input.byteLength - data.ptr;\n                numBytes = numBytes < bytesleft ? numBytes : bytesleft;\n                //bit alignment\n                arrayBuf = new ArrayBuffer((numBytes % dataTypeSize) === 0 ? numBytes : (numBytes + dataTypeSize - numBytes % dataTypeSize));\n                store8 = new Uint8Array(arrayBuf);\n                store8.set(new Uint8Array(input, data.ptr, numBytes));\n                rawData = new OutPixelTypeArray(arrayBuf);\n                z = 0;\n                if (mask) {\n                  for (row = 0; row < thisBlockHeight; row++) {\n                    for (col = 0; col < thisBlockWidth; col++) {\n                      if (mask[outPtr]) {\n                        resultPixels[outPtr] = rawData[z++];\n                      }\n                      outPtr++;\n                    }\n                    outPtr += outStride;\n                  }\n                }\n                else {//all valid\n                  for (row = 0; row < thisBlockHeight; row++) {\n                    for (col = 0; col < thisBlockWidth; col++) {\n                      resultPixels[outPtr++] = rawData[z++];\n                    }\n                    outPtr += outStride;\n                  }\n                }\n                data.ptr += z * dataTypeSize;\n              }\n              else { //1 or 3\n                offsetType = Lerc2Helpers.getDataTypeUsed((isDiffEncoding && imageType < 6) ? 4 : imageType, bits67);\n                offset = Lerc2Helpers.getOnePixel(block, blockPtr, offsetType, view);\n                blockPtr += Lerc2Helpers.getDataTypeSize(offsetType);\n                if (blockEncoding === 3) //constant offset value\n                {\n                  data.ptr += blockPtr;\n                  data.counter.constantoffset++;\n                  //you can delete the following resultMask case in favor of performance because val is constant and users use nodata mask, otherwise nodatavalue post processing handles it too.\n                  //while the above statement is true, we're not doing it as we want to keep invalid pixel value at 0 rather than arbitrary values\n                  if (mask) {\n                    for (row = 0; row < thisBlockHeight; row++) {\n                      for (col = 0; col < thisBlockWidth; col++) {\n                        if (mask[outPtr]) {\n                          resultPixels[outPtr] = isDiffEncoding ? Math.min(zMax, resultPixelsPrevDim[outPtr] + offset) : offset;\n                        }\n                        outPtr++;\n                      }\n                      outPtr += outStride;\n                    }\n                  }\n                  else {\n                    for (row = 0; row < thisBlockHeight; row++) {\n                      for (col = 0; col < thisBlockWidth; col++) {\n                        resultPixels[outPtr] = isDiffEncoding ? Math.min(zMax, resultPixelsPrevDim[outPtr] + offset) : offset;\n                        outPtr++;\n                      }\n                      outPtr += outStride;\n                    }\n                  }\n                }\n                else { //bitstuff encoding is 3\n                  data.ptr += blockPtr;\n                  //heavy lifting\n                  Lerc2Helpers.decodeBits(input, data, blockDataBuffer, offset, iDim);\n                  blockPtr = 0;\n                  // duplicate code to favor performance, diff encoding is for multidimension only\n                  if (isDiffEncoding) {\n                    if (mask) {\n                      for (row = 0; row < thisBlockHeight; row++) {\n                        for (col = 0; col < thisBlockWidth; col++) {\n                          if (mask[outPtr]) {\n                            resultPixels[outPtr] = blockDataBuffer[blockPtr++] + resultPixelsPrevDim[outPtr];\n                          }\n                          outPtr++;\n                        }\n                        outPtr += outStride;\n                      }\n                    }\n                    else {\n                      for (row = 0; row < thisBlockHeight; row++) {\n                        for (col = 0; col < thisBlockWidth; col++) {\n                          resultPixels[outPtr] = blockDataBuffer[blockPtr++] + resultPixelsPrevDim[outPtr];\n                          outPtr++;\n                        }\n                        outPtr += outStride;\n                      }\n                    }\n                  }\n                  else if (mask) {\n                    for (row = 0; row < thisBlockHeight; row++) {\n                      for (col = 0; col < thisBlockWidth; col++) {\n                        if (mask[outPtr]) {\n                          resultPixels[outPtr] = blockDataBuffer[blockPtr++];\n                        }\n                        outPtr++;\n                      }\n                      outPtr += outStride;\n                    }\n                  }\n                  else {\n                    for (row = 0; row < thisBlockHeight; row++) {\n                      for (col = 0; col < thisBlockWidth; col++) {\n                        resultPixels[outPtr++] = blockDataBuffer[blockPtr++];\n                      }\n                      outPtr += outStride;\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n        //swap for BIP: it's always easier for clients to handle BSQ so we keep existing logic and introduce a swap here to minimze changes\n        if (numDims > 1 && !useBSQForOutputDim) {\n          data.pixels.resultPixels = Lerc2Helpers.swapDimensionOrder(data.pixels.resultPixels, numPixels, numDims, OutPixelTypeArray);\n        }\n      },\n\n      /*****************\n      *  private methods (helper methods)\n      *****************/\n\n      formatFileInfo: function(data) {\n        return {\n          \"fileIdentifierString\": data.headerInfo.fileIdentifierString,\n          \"fileVersion\": data.headerInfo.fileVersion,\n          \"imageType\": data.headerInfo.imageType,\n          \"height\": data.headerInfo.height,\n          \"width\": data.headerInfo.width,\n          \"numValidPixel\": data.headerInfo.numValidPixel,\n          \"microBlockSize\": data.headerInfo.microBlockSize,\n          \"blobSize\": data.headerInfo.blobSize,\n          \"maxZError\": data.headerInfo.maxZError,\n          \"pixelType\": Lerc2Helpers.getPixelType(data.headerInfo.imageType),\n          \"eofOffset\": data.eofOffset,\n          \"mask\": data.mask ? {\n            \"numBytes\": data.mask.numBytes\n          } : null,\n          \"pixels\": {\n            \"numBlocksX\": data.pixels.numBlocksX,\n            \"numBlocksY\": data.pixels.numBlocksY,\n            //\"numBytes\": data.pixels.numBytes,\n            \"maxValue\": data.headerInfo.zMax,\n            \"minValue\": data.headerInfo.zMin,\n            \"noDataValue\": data.noDataValue\n          }\n        };\n      },\n\n      constructConstantSurface: function(data, useBSQForOutputDim) {\n        var val = data.headerInfo.zMax;\n        var valMin = data.headerInfo.zMin;\n        var maxValues = data.headerInfo.maxValues;\n        var numDims = data.headerInfo.numDims;\n        var numPixels = data.headerInfo.height * data.headerInfo.width;\n        var i = 0, k = 0, nStart = 0;\n        var mask = data.pixels.resultMask;\n        var resultPixels = data.pixels.resultPixels;\n        if (mask) {\n          if (numDims > 1) {\n            if (useBSQForOutputDim) {\n              for (i = 0; i < numDims; i++) {\n                nStart = i * numPixels;\n                val = maxValues[i];\n                for (k = 0; k < numPixels; k++) {\n                  if (mask[k]) {\n                    resultPixels[nStart + k] = val;\n                  }\n                }\n              }  \n            }\n            else {\n              for (k = 0; k < numPixels; k++) {\n                if (mask[k]) {\n                  nStart = k * numDims;\n                  for (i = 0; i < numDims; i++) {\n                    resultPixels[nStart + numDims] = maxValues[i];\n                  }\n                }\n              }\n            }\n          }\n          else {\n            for (k = 0; k < numPixels; k++) {\n              if (mask[k]) {\n                resultPixels[k] = val;\n              }\n            }\n          }\n        }\n        else {\n          if (numDims > 1 && valMin !== val) {\n            if (useBSQForOutputDim) {\n              for (i = 0; i < numDims; i++) {\n                nStart = i * numPixels;\n                val = maxValues[i];\n                for (k = 0; k < numPixels; k++) {\n                  resultPixels[nStart + k] = val;\n                }\n              }\n            }\n            else {\n              for (k = 0; k < numPixels; k++) {\n                nStart = k * numDims;\n                for (i = 0; i < numDims; i++) {\n                  resultPixels[nStart + i] = maxValues[i];\n                }\n              }\n            }\n          }\n          else {\n            for (k = 0; k < numPixels * numDims; k++) {\n              resultPixels[k] = val;\n            }\n          }\n        }\n        return;\n      },\n\n      getDataTypeArray: function(t) {\n        var tp;\n        switch (t) {\n          case 0: //char\n            tp = Int8Array;\n            break;\n          case 1: //byte\n            tp = Uint8Array;\n            break;\n          case 2: //short\n            tp = Int16Array;\n            break;\n          case 3: //ushort\n            tp = Uint16Array;\n            break;\n          case 4:\n            tp = Int32Array;\n            break;\n          case 5:\n            tp = Uint32Array;\n            break;\n          case 6:\n            tp = Float32Array;\n            break;\n          case 7:\n            tp = Float64Array;\n            break;\n          default:\n            tp = Float32Array;\n        }\n        return tp;\n      },\n\n      getPixelType: function(t) {\n        var tp;\n        switch (t) {\n          case 0: //char\n            tp = \"S8\";\n            break;\n          case 1: //byte\n            tp = \"U8\";\n            break;\n          case 2: //short\n            tp = \"S16\";\n            break;\n          case 3: //ushort\n            tp = \"U16\";\n            break;\n          case 4:\n            tp = \"S32\";\n            break;\n          case 5:\n            tp = \"U32\";\n            break;\n          case 6:\n            tp = \"F32\";\n            break;\n          case 7:\n            tp = \"F64\";\n            break;\n          default:\n            tp = \"F32\";\n        }\n        return tp;\n      },\n\n      isValidPixelValue: function(t, val) {\n        if (val == null) {\n          return false;\n        }\n        var isValid;\n        switch (t) {\n          case 0: //char\n            isValid = val >= -128 && val <= 127;\n            break;\n          case 1: //byte  (unsigned char)\n            isValid = val >= 0 && val <= 255;\n            break;\n          case 2: //short\n            isValid = val >= -32768 && val <= 32767;\n            break;\n          case 3: //ushort\n            isValid = val >= 0 && val <= 65536;\n            break;\n          case 4: //int 32\n            isValid = val >= -2147483648 && val <= 2147483647;\n            break;\n          case 5: //uinit 32\n            isValid = val >= 0 && val <= 4294967296;\n            break;\n          case 6:\n            isValid = val >= -3.4027999387901484e+38 && val <= 3.4027999387901484e+38;\n            break;\n          case 7:\n            isValid = val >= -1.7976931348623157e+308 && val <= 1.7976931348623157e+308;\n            break;\n          default:\n            isValid = false;\n        }\n        return isValid;\n      },\n\n      getDataTypeSize: function(t) {\n        var s = 0;\n        switch (t) {\n          case 0: //ubyte\n          case 1: //byte\n            s = 1;\n            break;\n          case 2: //short\n          case 3: //ushort\n            s = 2;\n            break;\n          case 4:\n          case 5:\n          case 6:\n            s = 4;\n            break;\n          case 7:\n            s = 8;\n            break;\n          default:\n            s = t;\n        }\n        return s;\n      },\n\n      getDataTypeUsed: function(dt, tc) {\n        var t = dt;\n        switch (dt) {\n          case 2: //short\n          case 4: //long\n            t = dt - tc;\n            break;\n          case 3: //ushort\n          case 5: //ulong\n            t = dt - 2 * tc;\n            break;\n          case 6: //float\n            if (0 === tc) {\n              t = dt;\n            }\n            else if (1 === tc) {\n              t = 2;\n            }\n            else {\n              t = 1;//byte\n            }\n            break;\n          case 7: //double\n            if (0 === tc) {\n              t = dt;\n            }\n            else {\n              t = dt - 2 * tc + 1;\n            }\n            break;\n          default:\n            t = dt;\n            break;\n        }\n        return t;\n      },\n\n      getOnePixel: function(block, blockPtr, offsetType, view) {\n        var temp = 0;\n        switch (offsetType) {\n          case 0: //char\n            temp = view.getInt8(blockPtr);\n            break;\n          case 1: //byte\n            temp = view.getUint8(blockPtr);\n            break;\n          case 2:\n            temp = view.getInt16(blockPtr, true);\n            break;\n          case 3:\n            temp = view.getUint16(blockPtr, true);\n            break;\n          case 4:\n            temp = view.getInt32(blockPtr, true);\n            break;\n          case 5:\n            temp = view.getUInt32(blockPtr, true);\n            break;\n          case 6:\n            temp = view.getFloat32(blockPtr, true);\n            break;\n          case 7:\n            temp = view.getFloat64(blockPtr, true);\n            break;\n          default:\n            throw (\"the decoder does not understand this pixel type\");\n        }\n        return temp;\n      },\n\n      swapDimensionOrder: function(pixels, numPixels, numDims, OutPixelTypeArray, inputIsBIP) {\n        var i = 0, j = 0, iDim = 0, temp = 0, swap = pixels;\n        if (numDims > 1) {\n          swap = new OutPixelTypeArray(numPixels * numDims);\n          if (inputIsBIP) {\n            for (i=0; i<numPixels; i++) {\n              temp = i;\n              for (iDim=0; iDim < numDims; iDim++, temp += numPixels) {\n                swap[temp] = pixels[j++];\n              }\n            }  \n          }\n          else {\n            for (i=0; i<numPixels; i++) {\n              temp = i;\n              for (iDim=0; iDim < numDims; iDim++, temp += numPixels) {\n                swap[j++] = pixels[temp];\n              }\n            }\n          }\n        }\n        return swap;\n      }\n    };\n\n    /***************************************************\n    *private class for a tree node. Huffman code is in Lerc2Helpers\n    ****************************************************/\n    var TreeNode = function(val, left, right) {\n      this.val = val;\n      this.left = left;\n      this.right = right;\n    };\n\n    var Lerc2Decode = {\n      /*\n      * ********removed options compared to LERC1. We can bring some of them back if needed.\n       * removed pixel type. LERC2 is typed and doesn't require user to give pixel type\n       * changed encodedMaskData to maskData. LERC2 's js version make it faster to use maskData directly.\n       * removed returnMask. mask is used by LERC2 internally and is cost free. In case of user input mask, it's returned as well and has neglible cost.\n       * removed nodatavalue. Because LERC2 pixels are typed, nodatavalue will sacrify a useful value for many types (8bit, 16bit) etc,\n       *       user has to be knowledgable enough about raster and their data to avoid usability issues. so nodata value is simply removed now.\n       *       We can add it back later if their's a clear requirement.\n       * removed encodedMask. This option was not implemented in LercDecode. It can be done after decoding (less efficient)\n       * removed computeUsedBitDepths.\n       *\n       *\n       * response changes compared to LERC1\n       * 1. encodedMaskData is not available\n       * 2. noDataValue is optional (returns only if user's noDataValue is with in the valid data type range)\n       * 3. maskData is always available\n      */\n      /*****************\n      *  public properties\n      ******************/\n      //HUFFMAN_LUT_BITS_MAX: 12, //use 2^12 lut, not configurable\n\n      /*****************\n      *  public methods\n      *****************/\n\n      /**\n       * Decode a LERC2 byte stream and return an object containing the pixel data and optional metadata.\n       *\n       * @param {ArrayBuffer} input The LERC input byte stream\n       * @param {object} [options] options Decoding options\n       * @param {number} [options.inputOffset] The number of bytes to skip in the input byte stream. A valid LERC file is expected at that position\n       * @param {boolean} [options.returnFileInfo] If true, the return value will have a fileInfo property that contains metadata obtained from the LERC headers and the decoding process\n       * @param {boolean} [options.returnPixelInterleavedDims]  If true, returned dimensions are pixel-interleaved, a.k.a [p1_dim0, p1_dim1, p1_dimn, p2_dim0...], default is [p1_dim0, p2_dim0, ..., p1_dim1, p2_dim1...]\n       */\n      decode: function(/*byte array*/ input, /*object*/ options) {\n        //currently there's a bug in the sparse array, so please do not set to false\n        options = options || {};\n        var noDataValue = options.noDataValue;\n\n        //initialize\n        var i = 0, data = {};\n        data.ptr = options.inputOffset || 0;\n        data.pixels = {};\n\n        // File header\n        if (!Lerc2Helpers.readHeaderInfo(input, data)) {\n          return;\n        }\n\n        var headerInfo = data.headerInfo;\n        var fileVersion = headerInfo.fileVersion;\n        var OutPixelTypeArray = Lerc2Helpers.getDataTypeArray(headerInfo.imageType);\n\n        // version check\n        if (fileVersion > 5) {\n          throw \"unsupported lerc version 2.\" + fileVersion;\n        }\n\n        // Mask Header\n        Lerc2Helpers.readMask(input, data);\n        if (headerInfo.numValidPixel !== headerInfo.width * headerInfo.height && !data.pixels.resultMask) {\n          data.pixels.resultMask = options.maskData;\n        }\n\n        var numPixels = headerInfo.width * headerInfo.height;\n        data.pixels.resultPixels = new OutPixelTypeArray(numPixels * headerInfo.numDims);\n\n        data.counter = {\n          onesweep: 0,\n          uncompressed: 0,\n          lut: 0,\n          bitstuffer: 0,\n          constant: 0,\n          constantoffset: 0\n        };\n        var useBSQForOutputDim = !options.returnPixelInterleavedDims;\n        if (headerInfo.numValidPixel !== 0) {\n          //not tested\n          if (headerInfo.zMax === headerInfo.zMin) //constant surface\n          {\n            Lerc2Helpers.constructConstantSurface(data, useBSQForOutputDim);\n          }\n          else if (fileVersion >= 4 && Lerc2Helpers.checkMinMaxRanges(input, data)) {\n            Lerc2Helpers.constructConstantSurface(data, useBSQForOutputDim);\n          }\n          else {\n            var view = new DataView(input, data.ptr, 2);\n            var bReadDataOneSweep = view.getUint8(0);\n            data.ptr++;\n            if (bReadDataOneSweep) {\n              //console.debug(\"OneSweep\");\n              Lerc2Helpers.readDataOneSweep(input, data, OutPixelTypeArray, useBSQForOutputDim);\n            }\n            else {\n              //lerc2.1: //bitstuffing + lut\n              //lerc2.2: //bitstuffing + lut + huffman\n              //lerc2.3: new bitstuffer\n              if (fileVersion > 1 && headerInfo.imageType <= 1 && Math.abs(headerInfo.maxZError - 0.5) < 0.00001) {\n                //this is 2.x plus 8 bit (unsigned and signed) data, possiblity of Huffman\n                var flagHuffman = view.getUint8(1);\n                data.ptr++;\n                data.encodeMode = flagHuffman;\n                if (flagHuffman > 2 || (fileVersion < 4 && flagHuffman > 1)) {\n                  throw \"Invalid Huffman flag \" + flagHuffman;\n                }\n                if (flagHuffman) {//1 - delta Huffman, 2 - Huffman\n                  //console.log(\"Huffman\");\n                  Lerc2Helpers.readHuffman(input, data, OutPixelTypeArray, useBSQForOutputDim);\n                }\n                else {\n                  //console.log(\"Tiles\");\n                  Lerc2Helpers.readTiles(input, data, OutPixelTypeArray, useBSQForOutputDim);\n                }\n              }\n              else { //lerc2.x non-8 bit data\n                //console.log(\"Tiles\");\n                Lerc2Helpers.readTiles(input, data, OutPixelTypeArray, useBSQForOutputDim);\n              }\n            }\n          }\n        }\n\n        data.eofOffset = data.ptr;\n        var diff;\n        if (options.inputOffset) {\n          diff = data.headerInfo.blobSize + options.inputOffset - data.ptr;\n          if (Math.abs(diff) >= 1) {\n            //console.debug(\"incorrect eof: dataptr \" + data.ptr + \" offset \" + options.inputOffset + \" blobsize \" + data.headerInfo.blobSize + \" diff: \" + diff);\n            data.eofOffset = options.inputOffset + data.headerInfo.blobSize;\n          }\n        }\n        else {\n          diff = data.headerInfo.blobSize - data.ptr;\n          if (Math.abs(diff) >= 1) {\n            //console.debug(\"incorrect first band eof: dataptr \" + data.ptr + \" blobsize \" + data.headerInfo.blobSize + \" diff: \" + diff);\n            data.eofOffset = data.headerInfo.blobSize;\n          }\n        }\n\n        var result = {\n          width: headerInfo.width,\n          height: headerInfo.height,\n          pixelData: data.pixels.resultPixels,\n          minValue: headerInfo.zMin,\n          maxValue: headerInfo.zMax,\n          validPixelCount: headerInfo.numValidPixel,\n          dimCount: headerInfo.numDims,\n          dimStats: {\n            minValues: headerInfo.minValues,\n            maxValues: headerInfo.maxValues\n          },\n          maskData: data.pixels.resultMask\n          //noDataValue: noDataValue\n        };\n\n        //we should remove this if there's no existing client\n        //optional noDataValue processing, it's user's responsiblity\n        if (data.pixels.resultMask && Lerc2Helpers.isValidPixelValue(headerInfo.imageType, noDataValue)) {\n          var mask = data.pixels.resultMask;\n          for (i = 0; i < numPixels; i++) {\n            if (!mask[i]) {\n              result.pixelData[i] = noDataValue;\n            }\n          }\n          result.noDataValue = noDataValue;\n        }\n        data.noDataValue = noDataValue;\n        if (options.returnFileInfo) {\n          result.fileInfo = Lerc2Helpers.formatFileInfo(data);\n        }\n        return result;\n      },\n\n      getBandCount: function(/*byte array*/ input) {\n        var count = 0;\n        var i = 0;\n        var temp = {};\n        temp.ptr = 0;\n        temp.pixels = {};\n        while (i < input.byteLength - 58) {\n          Lerc2Helpers.readHeaderInfo(input, temp);\n          i += temp.headerInfo.blobSize;\n          count++;\n          temp.ptr = i;\n        }\n        return count;\n      }\n    };\n\n    return Lerc2Decode;\n  })();\n\n  var isPlatformLittleEndian = (function() {\n    var a = new ArrayBuffer(4);\n    var b = new Uint8Array(a);\n    var c = new Uint32Array(a);\n    c[0] = 1;\n    return b[0] === 1;\n  })();\n\n  var Lerc = {\n    /************wrapper**********************************************/\n    /**\n     * A wrapper for decoding both LERC1 and LERC2 byte streams capable of handling multiband pixel blocks for various pixel types.\n     *\n     * @alias module:Lerc\n     * @param {ArrayBuffer} input The LERC input byte stream\n     * @param {object} [options] The decoding options below are optional.\n     * @param {number} [options.inputOffset] The number of bytes to skip in the input byte stream. A valid Lerc file is expected at that position.\n     * @param {string} [options.pixelType] (LERC1 only) Default value is F32. Valid pixel types for input are U8/S8/S16/U16/S32/U32/F32.\n     * @param {number} [options.noDataValue] (LERC1 only). It is recommended to use the returned mask instead of setting this value.\n     * @param {boolean} [options.returnPixelInterleavedDims] (nDim LERC2 only) If true, returned dimensions are pixel-interleaved, a.k.a [p1_dim0, p1_dim1, p1_dimn, p2_dim0...], default is [p1_dim0, p2_dim0, ..., p1_dim1, p2_dim1...]\n     * @returns {{width, height, pixels, pixelType, mask, statistics}}\n       * @property {number} width Width of decoded image.\n       * @property {number} height Height of decoded image.\n       * @property {array} pixels [band1, band2, …] Each band is a typed array of width*height.\n       * @property {string} pixelType The type of pixels represented in the output.\n       * @property {mask} mask Typed array with a size of width*height, or null if all pixels are valid.\n       * @property {array} statistics [statistics_band1, statistics_band2, …] Each element is a statistics object representing min and max values\n    **/\n    decode: function(encodedData, options) {\n      if (!isPlatformLittleEndian) {\n        throw \"Big endian system is not supported.\";\n      }\n      options = options || {};\n      var inputOffset = options.inputOffset || 0;\n      var fileIdView = new Uint8Array(encodedData, inputOffset, 10);\n      var fileIdentifierString = String.fromCharCode.apply(null, fileIdView);\n      var lerc, majorVersion;\n      if (fileIdentifierString.trim() === \"CntZImage\") {\n        lerc = LercDecode;\n        majorVersion = 1;\n      }\n      else if (fileIdentifierString.substring(0, 5) === \"Lerc2\") {\n        lerc = Lerc2Decode;\n        majorVersion = 2;\n      }\n      else {\n        throw \"Unexpected file identifier string: \" + fileIdentifierString;\n      }\n\n      var iPlane = 0, eof = encodedData.byteLength - 10, encodedMaskData, bandMasks = [], bandMask, maskData;\n      var decodedPixelBlock = {\n        width: 0,\n        height: 0,\n        pixels: [],\n        pixelType: options.pixelType,\n        mask: null,\n        statistics: []\n      };\n      var uniqueBandMaskCount = 0;\n\n      while (inputOffset < eof) {\n        var result = lerc.decode(encodedData, {\n          inputOffset: inputOffset,//for both lerc1 and lerc2\n          encodedMaskData: encodedMaskData,//lerc1 only\n          maskData: maskData,//lerc2 only\n          returnMask: iPlane === 0 ? true : false,//lerc1 only\n          returnEncodedMask: iPlane === 0 ? true : false,//lerc1 only\n          returnFileInfo: true,//for both lerc1 and lerc2\n          returnPixelInterleavedDims: options.returnPixelInterleavedDims,//for ndim lerc2 only\n          pixelType: options.pixelType || null,//lerc1 only\n          noDataValue: options.noDataValue || null//lerc1 only\n        });\n\n        inputOffset = result.fileInfo.eofOffset;\n        maskData = result.maskData;//lerc2\n        if (iPlane === 0) {\n          encodedMaskData = result.encodedMaskData;//lerc1\n          decodedPixelBlock.width = result.width;\n          decodedPixelBlock.height = result.height;\n          decodedPixelBlock.dimCount = result.dimCount || 1;\n          //decodedPixelBlock.dimStats = decodedPixelBlock.dimStats;\n          decodedPixelBlock.pixelType = result.pixelType || result.fileInfo.pixelType;\n          decodedPixelBlock.mask = maskData;\n        }\n        if (majorVersion > 1) {\n          if (maskData) {\n            bandMasks.push(maskData);\n          }\n          if (result.fileInfo.mask && result.fileInfo.mask.numBytes > 0) {\n            uniqueBandMaskCount++;\n          }\n        }\n\n        iPlane++;\n        decodedPixelBlock.pixels.push(result.pixelData);\n        decodedPixelBlock.statistics.push({\n          minValue: result.minValue,\n          maxValue: result.maxValue,\n          noDataValue: result.noDataValue,\n          dimStats: result.dimStats\n        });\n      }\n      var i, j, numPixels;\n      if (majorVersion > 1 && uniqueBandMaskCount > 1) {\n        numPixels = decodedPixelBlock.width * decodedPixelBlock.height;\n        decodedPixelBlock.bandMasks = bandMasks;\n        maskData = new Uint8Array(numPixels);\n        maskData.set(bandMasks[0]);\n        for (i = 1; i < bandMasks.length; i++) {\n          bandMask = bandMasks[i];\n          for (j = 0; j < numPixels; j++) {\n            maskData[j] = maskData[j] & bandMask[j];\n          }\n        }\n        decodedPixelBlock.maskData = maskData;\n      }\n\n      return decodedPixelBlock;\n    }\n  };\n\n  if (typeof define === \"function\" && define.amd) {/* jshint ignore:line */\n    //amd loaders such as dojo and requireJS\n    //http://wiki.commonjs.org/wiki/Modules/AsynchronousDefinition\n    define([], function() { return Lerc; });/* jshint ignore:line */\n  }\n  else if (typeof module !== \"undefined\" && module.exports) {/* jshint ignore:line */\n    //commonJS module 1.0/1.1/1.1.1 systems, such as nodeJS\n    //http://wiki.commonjs.org/wiki/Modules\n    module.exports = Lerc;/* jshint ignore:line */\n  }\n  else {\n    //assign to this, most likely window\n    this.Lerc = Lerc;\n  }\n\n})();\n", "import { inflate } from 'pako';\nimport Lerc from 'lerc';\nimport { ZSTDDecoder } from 'zstddec';\nimport BaseDecoder from './basedecoder.js';\nimport { LercParameters, LercAddCompression } from '../globals.js';\n\nexport const zstd = new ZSTDDecoder();\n\nexport default class LercDecoder extends BaseDecoder {\n  constructor(fileDirectory) {\n    super();\n\n    this.planarConfiguration = typeof fileDirectory.PlanarConfiguration !== 'undefined' ? fileDirectory.PlanarConfiguration : 1;\n    this.samplesPerPixel = typeof fileDirectory.SamplesPerPixel !== 'undefined' ? fileDirectory.SamplesPerPixel : 1;\n\n    this.addCompression = fileDirectory.LercParameters[LercParameters.AddCompression];\n  }\n\n  decodeBlock(buffer) {\n    switch (this.addCompression) {\n      case LercAddCompression.None:\n        break;\n      case LercAddCompression.Deflate:\n        buffer = inflate(new Uint8Array(buffer)).buffer; // eslint-disable-line no-param-reassign, prefer-destructuring\n        break;\n      case LercAddCompression.Zstandard:\n        buffer = zstd.decode(new Uint8Array(buffer)).buffer; // eslint-disable-line no-param-reassign, prefer-destructuring\n        break;\n      default:\n        throw new Error(`Unsupported LERC additional compression method identifier: ${this.addCompression}`);\n    }\n\n    const lercResult = Lerc.decode(buffer, { returnPixelInterleavedDims: this.planarConfiguration === 1 });\n    const lercData = lercResult.pixels[0];\n    return lercData.buffer;\n  }\n}\n", "interface DecoderExports {\n\tmemory: Uint8Array;\n\n\tZSTD_findDecompressedSize: (compressedPtr: number, compressedSize: number) => BigInt;\n\tZSTD_decompress: (uncompressedPtr: number, uncompressedSize: number, compressedPtr: number, compressedSize: number) => number;\n\tmalloc: (ptr: number) => number;\n\tfree: (ptr: number) => void;\n}\n\nlet init: Promise<void>;\nlet instance: {exports: DecoderExports};\nlet heap: Uint8Array;\n\nconst IMPORT_OBJECT = {\n\n\tenv: {\n\n\t\temscripten_notify_memory_growth: function ( index: number ): void {\n\n\t\t\theap = new Uint8Array( instance.exports.memory.buffer );\n\n\t\t}\n\n\t}\n\n};\n\n/**\n * ZSTD (Zstandard) decoder.\n */\nexport class ZSTDDecoder {\n\n\tinit (): Promise<void> {\n\n\t\tif ( init ) return init;\n\n\t\tif ( typeof fetch !== 'undefined' ) {\n\n\t\t\t// Web.\n\n\t\t\tinit = fetch( 'data:application/wasm;base64,' + wasm )\n\t\t\t\t.then( ( response ) => response.arrayBuffer() )\n\t\t\t\t.then( ( arrayBuffer ) => WebAssembly.instantiate( arrayBuffer, IMPORT_OBJECT ) )\n\t\t\t\t.then( this._init );\n\n\t\t} else {\n\n\t\t\t// Node.js.\n\n\t\t\tinit = WebAssembly\n\t\t\t\t.instantiate( Buffer.from( wasm, 'base64' ), IMPORT_OBJECT )\n\t\t\t\t.then( this._init );\n\n\t\t}\n\n\t\treturn init;\n\n\t}\n\n\t_init ( result: WebAssembly.WebAssemblyInstantiatedSource ): void {\n\n\t\tinstance = result.instance as unknown as { exports: DecoderExports };\n\n\t\tIMPORT_OBJECT.env.emscripten_notify_memory_growth( 0 ); // initialize heap.\n\n\t}\n\n\tdecode ( array: Uint8Array, uncompressedSize = 0 ): Uint8Array {\n\n\t\tif ( ! instance ) throw new Error( `ZSTDDecoder: Await .init() before decoding.` );\n\n\t\t// Write compressed data into WASM memory.\n\t\tconst compressedSize = array.byteLength;\n\t\tconst compressedPtr = instance.exports.malloc( compressedSize );\n\t\theap.set( array, compressedPtr );\n\n\t\t// Decompress into WASM memory.\n\t\tuncompressedSize = uncompressedSize || Number( instance.exports.ZSTD_findDecompressedSize( compressedPtr, compressedSize ) );\n\t\tconst uncompressedPtr = instance.exports.malloc( uncompressedSize );\n\t\tconst actualSize = instance.exports.ZSTD_decompress( uncompressedPtr, uncompressedSize, compressedPtr, compressedSize );\n\n\t\t// Read decompressed data and free WASM memory.\n\t\tconst dec = heap.slice( uncompressedPtr, uncompressedPtr + actualSize );\n\t\tinstance.exports.free( compressedPtr );\n\t\tinstance.exports.free( uncompressedPtr );\n\n\t\treturn dec;\n\n\t}\n\n}\n\n/**\n * BSD License\n *\n * For Zstandard software\n *\n * Copyright (c) 2016-present, Yann Collet, Facebook, Inc. All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *\n *  * Redistributions of source code must retain the above copyright notice, this\n *    list of conditions and the following disclaimer.\n *\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *    this list of conditions and the following disclaimer in the documentation\n *    and/or other materials provided with the distribution.\n *\n *  * Neither the name Facebook nor the names of its contributors may be used to\n *    endorse or promote products derived from this software without specific\n *    prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\n * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n// wasm:begin\nconst wasm = '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';\n// wasm:end\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAgCA,KAAC,WAAW;AAKV,UAAI,aAAc,WAAW;AAK3B,YAAI,YAAY,CAAC;AAEjB,kBAAU,qBAAqB;AAiC/B,kBAAU,SAAS,SAAS,OAAO,SAAS;AAC1C,oBAAU,WAAW,CAAC;AAEtB,cAAI,WAAW,QAAQ,mBAAoB,QAAQ,oBAAoB;AACvE,cAAI,aAAa,MAAM,OAAO,QAAQ,eAAe,GAAG,QAAQ;AAEhE,cAAI,cAAe,QAAQ,gBAAgB,OAAQ,QAAQ,cAAc,UAAU;AAEnF,cAAI,mBAAmB;AAAA,YAAsB;AAAA,YAAY,QAAQ,aAAa;AAAA,YAC5E,QAAQ;AAAA,YAAiB;AAAA,YAAa,QAAQ;AAAA,UAAU;AAE1D,cAAI,SAAS;AAAA,YACX,OAAO,WAAW;AAAA,YAClB,QAAQ,WAAW;AAAA,YACnB,WAAW,iBAAiB;AAAA,YAC5B,UAAU,iBAAiB;AAAA,YAC3B,UAAU,WAAW,OAAO;AAAA,YAC5B;AAAA,UACF;AAEA,cAAI,iBAAiB,YAAY;AAC/B,mBAAO,WAAW,iBAAiB;AAAA,UACrC;AAEA,cAAI,QAAQ,qBAAqB,WAAW,MAAM;AAChD,mBAAO,kBAAkB,WAAW,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,UAC7E;AAEA,cAAI,QAAQ,gBAAgB;AAC1B,mBAAO,WAAW,eAAe,UAAU;AAC3C,gBAAI,QAAQ,sBAAsB;AAChC,qBAAO,SAAS,YAAY,qBAAqB,UAAU;AAAA,YAC7D;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,wBAAwB,SAAS,MAAM,iBAAiB,YAAY,aAAa,kBAAkB;AACrG,cAAI,WAAW;AACf,cAAI,OAAO,KAAK,OAAO;AACvB,cAAI,OAAO,KAAK,OAAO;AACvB,cAAI,aAAa,KAAK,MAAM,KAAK,QAAQ,IAAI;AAC7C,cAAI,cAAc,KAAK,MAAM,KAAK,SAAS,IAAI;AAC/C,cAAI,QAAQ,IAAI,KAAK;AACrB,cAAI,WAAW,OAAO,WAAW;AACjC,uBAAa,eAAgB,KAAK,OAAQ,KAAK,KAAK,SAAS;AAE7D,cAAI,cAAc;AAClB,yBAAe,IAAI,gBAAgB,KAAK,QAAQ,KAAK,MAAM;AAC3D,cAAI,oBAAoB,YAAY;AAClC,yBAAa,IAAI,WAAW,KAAK,QAAQ,KAAK,MAAM;AAAA,UACtD;AACA,cAAI,kBAAkB,IAAI,aAAa,aAAa,WAAW;AAE/D,cAAI,IAAI;AACR,mBAAS,IAAI,GAAG,KAAK,MAAM,KAAK;AAC9B,gBAAI,kBAAmB,MAAM,OAAQ,cAAe,KAAK,SAAS;AAClE,gBAAI,oBAAoB,GAAG;AACzB;AAAA,YACF;AACA,qBAAS,IAAI,GAAG,KAAK,MAAM,KAAK;AAC9B,kBAAI,iBAAkB,MAAM,OAAQ,aAAc,KAAK,QAAQ;AAC/D,kBAAI,mBAAmB,GAAG;AACxB;AAAA,cACF;AAEA,kBAAI,SAAS,IAAI,KAAK,QAAQ,cAAc,IAAI;AAChD,kBAAI,YAAY,KAAK,QAAQ;AAE7B,kBAAI,QAAQ,KAAK,OAAO,OAAO,QAAQ;AAEvC,kBAAI,WAAW,UAAU;AACzB,kBAAI,MAAM,WAAW,GAAG;AAEtB,oBAAI,MAAM,aAAa,GAAG;AAExB,8BAAY,MAAM;AAAA,gBACpB,OAAO;AAEL,0BAAQ,MAAM,aAAa,MAAM,cAAc,MAAM,gBAAgB,MAAM,QAAQ,OAAO,iBAAiB,KAAK,OAAO,QAAQ;AAC/H,8BAAY;AAAA,gBACd;AACA,2BAAW;AAAA,cACb,WACS,MAAM,aAAa,GAAG;AAE7B,6BAAa;AAAA,cACf,OACK;AAEH,6BAAa,MAAM;AAAA,cACrB;AAEA,kBAAI;AACJ,kBAAI,YAAY;AACd,qBAAK,KAAK,GAAG,KAAK,iBAAiB,MAAM;AACvC,sBAAI,SAAS,GAAG;AAEd,+BAAW,WAAW,UAAU,CAAC;AACjC,iCAAa,SAAS;AAAA,kBACxB;AACA,uBAAK,KAAK,GAAG,KAAK,gBAAgB,MAAM;AACtC,wBAAI,EAAE,SAAS,IAAI;AAEjB,iCAAW,WAAW,UAAU,CAAC;AAAA,oBACnC;AACA,wBAAI,WAAW,KAAK;AAElB,0BAAI,YAAY;AACd,mCAAW,MAAM,IAAI;AAAA,sBACvB;AACA,qCAAgB,MAAM,WAAW,IAAK,UAAU,UAAU,IAAI;AAC9D,iCAAW,WAAW,eAAe,eAAe;AACpD,mCAAa,QAAQ,IAAI;AAAA,oBAC3B,OAAO;AAEL,0BAAI,YAAY;AACd,mCAAW,MAAM,IAAI;AAAA,sBACvB;AACA,mCAAa,QAAQ,IAAI;AAAA,oBAC3B;AACA,iCAAa;AAAA,kBACf;AACA,4BAAU;AAAA,gBACZ;AAAA,cACF,OAAO;AAEL,oBAAI,MAAM,WAAW,GAAG;AAGtB,uBAAK,KAAK,GAAG,KAAK,iBAAiB,MAAM;AACvC,yBAAK,KAAK,GAAG,KAAK,gBAAgB,MAAM;AACtC,qCAAe,UAAU,UAAU;AACnC,iCAAW,WAAW,eAAe,eAAe;AACpD,mCAAa,QAAQ,IAAI;AAAA,oBAC3B;AACA,8BAAU;AAAA,kBACZ;AAAA,gBACF,OACK;AAEH,6BAAW,WAAW,aAAa,aAAa;AAChD,uBAAK,KAAK,GAAG,KAAK,iBAAiB,MAAM;AACvC,yBAAK,KAAK,GAAG,KAAK,gBAAgB,MAAM;AACtC,mCAAa,QAAQ,IAAI;AAAA,oBAC3B;AACA,8BAAU;AAAA,kBACZ;AAAA,gBACF;AAAA,cACF;AACA,kBAAK,MAAM,aAAa,KAAO,aAAa,MAAM,gBAAiB;AACjE,sBAAM;AAAA,cACR;AACA;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,YAAI,iBAAiB,SAAS,MAAM;AAClC,iBAAO;AAAA,YACL,wBAAwB,KAAK;AAAA,YAC7B,eAAe,KAAK;AAAA,YACpB,aAAa,KAAK;AAAA,YAClB,UAAU,KAAK;AAAA,YACf,SAAS,KAAK;AAAA,YACd,aAAa,KAAK;AAAA,YAClB,aAAa,KAAK;AAAA,YAClB,QAAQ,KAAK,OAAO;AAAA,cAClB,cAAc,KAAK,KAAK;AAAA,cACxB,cAAc,KAAK,KAAK;AAAA,cACxB,YAAY,KAAK,KAAK;AAAA,cACtB,YAAY,KAAK,KAAK;AAAA,YACxB,IAAI;AAAA,YACJ,UAAU;AAAA,cACR,cAAc,KAAK,OAAO;AAAA,cAC1B,cAAc,KAAK,OAAO;AAAA,cAC1B,YAAY,KAAK,OAAO;AAAA,cACxB,YAAY,KAAK,OAAO;AAAA,cACxB,eAAe,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAEA,YAAI,uBAAuB,SAAS,MAAM;AACxC,cAAI,YAAY,KAAK,OAAO,aAAa,KAAK,OAAO;AACrD,cAAI,YAAY,CAAC;AACjB,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,gBAAI,QAAQ,KAAK,OAAO,OAAO,CAAC;AAChC,gBAAI,MAAM,aAAa,GAAG;AACxB,wBAAU,UAAU;AAAA,YACtB,WAAW,MAAM,aAAa,GAAG;AAC/B,wBAAU,MAAM,YAAY,IAAI;AAAA,YAClC,OAAO;AACL,wBAAU,CAAC,IAAI;AAAA,YACjB;AAAA,UACF;AAEA,iBAAO,OAAO,KAAK,SAAS;AAAA,QAC9B;AAEA,YAAI,QAAQ,SAAS,OAAO,IAAI,UAAU;AACxC,cAAI,OAAO,CAAC;AAGZ,cAAI,aAAa,IAAI,WAAW,OAAO,IAAI,EAAE;AAC7C,eAAK,uBAAuB,OAAO,aAAa,MAAM,MAAM,UAAU;AACtE,cAAI,KAAK,qBAAqB,KAAK,MAAM,aAAa;AACpD,kBAAM,wCAAwC,KAAK;AAAA,UACrD;AACA,gBAAM;AACN,cAAI,OAAO,IAAI,SAAS,OAAO,IAAI,EAAE;AACrC,eAAK,cAAc,KAAK,SAAS,GAAG,IAAI;AACxC,eAAK,YAAY,KAAK,SAAS,GAAG,IAAI;AACtC,eAAK,SAAS,KAAK,UAAU,GAAG,IAAI;AACpC,eAAK,QAAQ,KAAK,UAAU,IAAI,IAAI;AACpC,eAAK,YAAY,KAAK,WAAW,IAAI,IAAI;AACzC,gBAAM;AAGN,cAAI,CAAC,UAAU;AACb,mBAAO,IAAI,SAAS,OAAO,IAAI,EAAE;AACjC,iBAAK,OAAO,CAAC;AACb,iBAAK,KAAK,aAAa,KAAK,UAAU,GAAG,IAAI;AAC7C,iBAAK,KAAK,aAAa,KAAK,UAAU,GAAG,IAAI;AAC7C,iBAAK,KAAK,WAAW,KAAK,UAAU,GAAG,IAAI;AAC3C,iBAAK,KAAK,WAAW,KAAK,WAAW,IAAI,IAAI;AAC7C,kBAAM;AAGN,gBAAI,KAAK,KAAK,WAAW,GAAG;AAC1B,kBAAI,SAAS,IAAI,WAAW,KAAK,KAAK,KAAK,QAAQ,KAAK,SAAS,CAAC,CAAC;AACnE,qBAAO,IAAI,SAAS,OAAO,IAAI,KAAK,KAAK,QAAQ;AACjD,kBAAI,MAAM,KAAK,SAAS,GAAG,IAAI;AAC/B,kBAAI,KAAK,GAAG,KAAK;AACjB,iBAAG;AACD,oBAAI,MAAM,GAAG;AACX,yBAAO,OAAO;AAAE,2BAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,kBAAG;AAAA,gBACtD,OAAO;AACL,sBAAI,MAAM,KAAK,SAAS,IAAI;AAC5B,wBAAM,CAAC;AACP,yBAAO,OAAO;AAAE,2BAAO,IAAI,IAAI;AAAA,kBAAK;AAAA,gBACtC;AACA,sBAAM,KAAK,SAAS,IAAI,IAAI;AAC5B,sBAAM;AAAA,cACR,SAAS,KAAK,KAAK,KAAK;AACxB,kBAAK,QAAQ,UAAY,KAAK,OAAO,QAAS;AAC5C,sBAAM;AAAA,cACR;AACA,mBAAK,KAAK,SAAS;AACnB,oBAAM,KAAK,KAAK;AAAA,YAClB,YACU,KAAK,KAAK,WAAW,KAAK,KAAK,aAAa,KAAK,KAAK,cAAc,GAAG;AAC/E,mBAAK,KAAK,SAAS,IAAI,WAAW,KAAK,KAAK,KAAK,QAAQ,KAAK,SAAS,CAAC,CAAC;AAAA,YAC3E;AAAA,UACF;AAGA,iBAAO,IAAI,SAAS,OAAO,IAAI,EAAE;AACjC,eAAK,SAAS,CAAC;AACf,eAAK,OAAO,aAAa,KAAK,UAAU,GAAG,IAAI;AAC/C,eAAK,OAAO,aAAa,KAAK,UAAU,GAAG,IAAI;AAC/C,eAAK,OAAO,WAAW,KAAK,UAAU,GAAG,IAAI;AAC7C,eAAK,OAAO,WAAW,KAAK,WAAW,IAAI,IAAI;AAC/C,gBAAM;AAEN,cAAI,aAAa,KAAK,OAAO;AAC7B,cAAI,aAAa,KAAK,OAAO;AAI7B,cAAI,mBAAmB,cAAe,KAAK,QAAQ,aAAc,IAAI,IAAI;AACzE,cAAI,mBAAmB,cAAe,KAAK,SAAS,aAAc,IAAI,IAAI;AAC1E,eAAK,OAAO,SAAS,IAAI,MAAM,mBAAmB,gBAAgB;AAClE,cAAI,SAAS;AACb,mBAAS,SAAS,GAAG,SAAS,kBAAkB,UAAU;AACxD,qBAAS,SAAS,GAAG,SAAS,kBAAkB,UAAU;AAGxD,kBAAI,OAAO;AACX,kBAAI,YAAY,MAAM,aAAa;AACnC,qBAAO,IAAI,SAAS,OAAO,IAAI,KAAK,IAAI,IAAI,SAAS,CAAC;AACtD,kBAAI,QAAQ,CAAC;AACb,mBAAK,OAAO,OAAO,QAAQ,IAAI;AAC/B,kBAAI,aAAa,KAAK,SAAS,CAAC;AAAG;AACnC,oBAAM,WAAW,aAAa;AAC9B,kBAAI,MAAM,WAAW,GAAG;AACtB,sBAAM,6BAA6B,MAAM,WAAW;AAAA,cACtD;AACA,kBAAI,MAAM,aAAa,GAAG;AACxB;AACA;AAAA,cACF;AACA,kBAAK,eAAe,KAAO,eAAe,GAAI;AAC5C,+BAAe;AACf,sBAAM,aAAa;AACnB,oBAAI,eAAe,GAAG;AACpB,wBAAM,SAAS,KAAK,QAAQ,CAAC;AAAG;AAAA,gBAClC,WAAW,eAAe,GAAG;AAC3B,wBAAM,SAAS,KAAK,SAAS,GAAG,IAAI;AAAG,0BAAQ;AAAA,gBACjD,WAAW,eAAe,GAAG;AAC3B,wBAAM,SAAS,KAAK,WAAW,GAAG,IAAI;AAAG,0BAAQ;AAAA,gBACnD,OAAO;AACL,wBAAM;AAAA,gBACR;AAEA,oBAAI,MAAM,aAAa,GAAG;AACxB,+BAAa,KAAK,SAAS,IAAI;AAAG;AAClC,wBAAM,eAAe,aAAa;AAClC,iCAAe;AACf,wBAAM,qBAAqB;AAC3B,sBAAI,eAAe,GAAG;AACpB,0BAAM,iBAAiB,KAAK,SAAS,IAAI;AAAG;AAAA,kBAC9C,WAAW,eAAe,GAAG;AAC3B,0BAAM,iBAAiB,KAAK,UAAU,MAAM,IAAI;AAAG,4BAAQ;AAAA,kBAC7D,WAAW,eAAe,GAAG;AAC3B,0BAAM,iBAAiB,KAAK,UAAU,MAAM,IAAI;AAAG,4BAAQ;AAAA,kBAC7D,OAAO;AACL,0BAAM;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AACA,oBAAM;AAEN,kBAAI,MAAM,aAAa,GAAG;AACxB;AAAA,cACF;AAEA,kBAAI,UAAU;AACd,kBAAI,MAAM,aAAa,GAAG;AACxB,oBAAI,aAAa,KAAK,OAAO,WAAW,KAAK;AAC7C,oBAAI,cAAc,KAAK,MAAM,SAAS,GAAG;AACvC,wBAAM;AAAA,gBACR;AACA,2BAAW,IAAI,YAAY,YAAY,CAAC;AACxC,yBAAS,IAAI,WAAW,QAAQ;AAChC,uBAAO,IAAI,IAAI,WAAW,OAAO,IAAI,YAAY,CAAC,CAAC;AACnD,oBAAI,UAAU,IAAI,aAAa,QAAQ;AACvC,sBAAM,UAAU;AAChB,sBAAM,YAAY;AAAA,cACpB,WAAW,MAAM,aAAa,GAAG;AAC/B,oBAAI,YAAY,KAAK,KAAK,MAAM,iBAAiB,MAAM,eAAe,CAAC;AACvE,oBAAI,YAAY,KAAK,KAAK,YAAY,CAAC;AACvC,2BAAW,IAAI,YAAY,YAAY,CAAC;AACxC,yBAAS,IAAI,WAAW,QAAQ;AAChC,uBAAO,IAAI,IAAI,WAAW,OAAO,IAAI,SAAS,CAAC;AAC/C,sBAAM,cAAc,IAAI,YAAY,QAAQ;AAC5C,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,eAAK,YAAY;AACjB,iBAAO;AAAA,QACT;AAEA,YAAI,UAAU,SAAS,KAAK,cAAc,WAAW,QAAQ,OAAO,MAAM,UAAU;AAClF,cAAI,WAAW,KAAK,gBAAgB;AACpC,cAAI,IAAI,GAAG;AACX,cAAI,WAAW;AACf,cAAI,GAAG;AACP,cAAI,OAAO,KAAK,MAAM,WAAW,UAAU,KAAK;AAEhD,cAAI,sBAAsB,IAAI,SAAS,IAAI,KAAK,KAAK,eAAe,YAAY,CAAC;AACjF,cAAI,IAAI,SAAS,CAAC,MAAM,IAAI;AAE5B,eAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,gBAAI,aAAa,GAAG;AAClB,uBAAS,IAAI,GAAG;AAChB,yBAAW;AAAA,YACb;AACA,gBAAI,YAAY,cAAc;AAC5B,kBAAK,WAAY,WAAW,eAAiB;AAC7C,0BAAY;AAAA,YACd,OAAO;AACL,kBAAI,cAAe,eAAe;AAClC,mBAAM,SAAS,YAAY,cAAe;AAC1C,uBAAS,IAAI,GAAG;AAChB,yBAAW,KAAK;AAChB,mBAAM,WAAW;AAAA,YACnB;AAEA,iBAAK,CAAC,IAAI,IAAI,OAAO,SAAS,IAAI,QAAQ;AAAA,UAC5C;AACA,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT,EAAG;AAGH,UAAI,cAAe,WAAW;AAC5B;AAOA,YAAI,aAAa;AAAA;AAAA;AAAA,UAGf,SAAS,SAAS,KAAK,MAAM,cAAc,WAAW,QAAQ,QAAQ,OAAO,UAAU;AACrF,gBAAI,WAAW,KAAK,gBAAgB;AACpC,gBAAI,IAAI,GAAG;AACX,gBAAI,WAAW;AACf,gBAAI,GAAG,QAAQ,aAAa;AAG5B,gBAAI,sBAAsB,IAAI,SAAS,IAAI,KAAK,KAAK,eAAe,YAAY,CAAC;AACjF,gBAAI,IAAI,SAAS,CAAC,MAAM,IAAI;AAC5B,gBAAI,QAAQ;AACV,mBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,oBAAI,aAAa,GAAG;AAClB,2BAAS,IAAI,GAAG;AAChB,6BAAW;AAAA,gBACb;AACA,oBAAI,YAAY,cAAc;AAC5B,sBAAK,WAAY,WAAW,eAAiB;AAC7C,8BAAY;AAAA,gBACd,OACK;AACH,gCAAe,eAAe;AAC9B,uBAAM,SAAS,YAAY,cAAe;AAC1C,2BAAS,IAAI,GAAG;AAChB,6BAAW,KAAK;AAChB,uBAAM,WAAW;AAAA,gBACnB;AACA,qBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,cACpB;AAAA,YACF,OACK;AACH,qBAAO,KAAK,MAAM,WAAW,UAAU,KAAK;AAC5C,mBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,oBAAI,aAAa,GAAG;AAClB,2BAAS,IAAI,GAAG;AAChB,6BAAW;AAAA,gBACb;AACA,oBAAI,YAAY,cAAc;AAC5B,sBAAK,WAAY,WAAW,eAAiB;AAC7C,8BAAY;AAAA,gBACd,OACK;AACH,gCAAe,eAAe;AAC9B,uBAAM,SAAS,YAAY,cAAe;AAC1C,2BAAS,IAAI,GAAG;AAChB,6BAAW,KAAK;AAChB,uBAAM,WAAW;AAAA,gBACnB;AAEA,qBAAK,CAAC,IAAI,IAAI,OAAO,SAAS,IAAI,QAAQ;AAAA,cAC5C;AAAA,YACF;AAAA,UACF;AAAA,UAEA,YAAY,SAAS,KAAK,cAAc,WAAW,QAAQ,OAAO,UAAU;AAC1E,gBAAI,WAAW,KAAK,gBAAgB;AACpC,gBAAI,IAAI,GAAG,IAAI,GAAG,cAAc,GAAG,WAAW,GAAG,IAAI;AACrD,gBAAI;AACJ,gBAAI,OAAO,CAAC;AAGZ,gBAAI,sBAAsB,IAAI,SAAS,IAAI,KAAK,KAAK,eAAe,YAAY,CAAC;AACjF,gBAAI,IAAI,SAAS,CAAC,MAAM,IAAI;AAE5B,gBAAI,OAAO,KAAK,MAAM,WAAW,UAAU,KAAK;AAChD,iBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,kBAAI,aAAa,GAAG;AAClB,yBAAS,IAAI,GAAG;AAChB,2BAAW;AAAA,cACb;AACA,kBAAI,YAAY,cAAc;AAC5B,oBAAK,WAAY,WAAW,eAAiB;AAC7C,4BAAY;AAAA,cACd,OAAO;AACL,8BAAe,eAAe;AAC9B,qBAAM,SAAS,YAAY,cAAe;AAC1C,yBAAS,IAAI,GAAG;AAChB,2BAAW,KAAK;AAChB,qBAAM,WAAW;AAAA,cACnB;AAEA,mBAAK,CAAC,IAAI,IAAI,OAAO,SAAS,IAAI,QAAQ;AAAA,YAC5C;AACA,iBAAK,QAAQ,MAAM;AACnB,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,SAAS,KAAK,MAAM,cAAc,WAAW,QAAQ,QAAQ,OAAO,UAAU;AACtF,gBAAI,WAAW,KAAK,gBAAgB;AACpC,gBAAI,IAAI,GAAG;AACX,gBAAI,WAAW,GAAG,SAAS;AAC3B,gBAAI,GAAG,QAAQ;AACf,gBAAI,QAAQ;AACV,mBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,oBAAI,aAAa,GAAG;AAClB,2BAAS,IAAI,GAAG;AAChB,6BAAW;AACX,2BAAS;AAAA,gBACX;AACA,oBAAI,YAAY,cAAc;AAC5B,sBAAM,WAAW,SAAU;AAC3B,8BAAY;AACZ,4BAAU;AAAA,gBACZ,OAAO;AACL,gCAAe,eAAe;AAC9B,sBAAK,WAAW,SAAU;AAC1B,2BAAS,IAAI,GAAG;AAChB,6BAAW,KAAK;AAChB,wBAAM,UAAW,KAAK,eAAe,MAAQ,eAAe;AAC5D,2BAAS;AAAA,gBACX;AACA,qBAAK,CAAC,IAAI,OAAO,CAAC;AAAA,cACpB;AAAA,YACF,OACK;AACH,kBAAI,OAAO,KAAK,MAAM,WAAW,UAAU,KAAK;AAChD,mBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,oBAAI,aAAa,GAAG;AAClB,2BAAS,IAAI,GAAG;AAChB,6BAAW;AACX,2BAAS;AAAA,gBACX;AACA,oBAAI,YAAY,cAAc;AAE5B,sBAAM,WAAW,SAAU;AAC3B,8BAAY;AACZ,4BAAU;AAAA,gBACZ,OAAO;AACL,gCAAe,eAAe;AAC9B,sBAAK,WAAW,SAAU;AAC1B,2BAAS,IAAI,GAAG;AAChB,6BAAW,KAAK;AAChB,wBAAM,UAAW,KAAK,eAAe,MAAQ,eAAe;AAC5D,2BAAS;AAAA,gBACX;AAEA,qBAAK,CAAC,IAAI,IAAI,OAAO,SAAS,IAAI,QAAQ;AAAA,cAC5C;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,aAAa,SAAS,KAAK,cAAc,WAAW,QAAQ,OAAO,UAAU;AAC3E,gBAAI,WAAW,KAAK,gBAAgB;AACpC,gBAAI,IAAI,GAAG,IAAI,GAAG,cAAc,GAAG,WAAW,GAAG,IAAI,GAAG,SAAS;AACjE,gBAAI;AACJ,gBAAI,OAAO,CAAC;AACZ,gBAAI,OAAO,KAAK,MAAM,WAAW,UAAU,KAAK;AAChD,iBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,kBAAI,aAAa,GAAG;AAClB,yBAAS,IAAI,GAAG;AAChB,2BAAW;AACX,yBAAS;AAAA,cACX;AACA,kBAAI,YAAY,cAAc;AAE5B,oBAAM,WAAW,SAAU;AAC3B,4BAAY;AACZ,0BAAU;AAAA,cACZ,OAAO;AACL,8BAAe,eAAe;AAC9B,oBAAK,WAAW,SAAU;AAC1B,yBAAS,IAAI,GAAG;AAChB,2BAAW,KAAK;AAChB,sBAAM,UAAW,KAAK,eAAe,MAAQ,eAAe;AAC5D,yBAAS;AAAA,cACX;AAEA,mBAAK,CAAC,IAAI,IAAI,OAAO,SAAS,IAAI,QAAQ;AAAA,YAC5C;AACA,iBAAK,QAAQ,MAAM;AACnB,mBAAO;AAAA,UACT;AAAA,UAEA,iBAAiB,SAAS,KAAK,MAAM,cAAc,WAAW;AAC5D,gBAAI,WAAW,KAAK,gBAAgB;AACpC,gBAAI,IAAI,GAAG;AACX,gBAAI,WAAW;AACf,gBAAI,GAAG,QAAQ;AAGf,gBAAI,sBAAsB,IAAI,SAAS,IAAI,KAAK,KAAK,eAAe,YAAY,CAAC;AACjF,gBAAI,IAAI,SAAS,CAAC,MAAM,IAAI;AAE5B,iBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,kBAAI,aAAa,GAAG;AAClB,yBAAS,IAAI,GAAG;AAChB,2BAAW;AAAA,cACb;AACA,kBAAI,YAAY,cAAc;AAC5B,oBAAK,WAAY,WAAW,eAAiB;AAC7C,4BAAY;AAAA,cACd,OACK;AACH,8BAAe,eAAe;AAC9B,qBAAM,SAAS,YAAY,cAAe;AAC1C,yBAAS,IAAI,GAAG;AAChB,2BAAW,KAAK;AAChB,qBAAM,WAAW;AAAA,cACnB;AACA,mBAAK,CAAC,IAAI;AAAA,YACZ;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,kBAAkB,SAAS,KAAK,MAAM,cAAc,WAAW;AAC7D,gBAAI,WAAW,KAAK,gBAAgB;AACpC,gBAAI,IAAI,GAAG;AACX,gBAAI,WAAW,GAAG,SAAS;AAC3B,gBAAI,GAAG,QAAQ;AAEf,iBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,kBAAI,aAAa,GAAG;AAClB,yBAAS,IAAI,GAAG;AAChB,2BAAW;AACX,yBAAS;AAAA,cACX;AACA,kBAAI,YAAY,cAAc;AAE5B,oBAAM,WAAW,SAAU;AAC3B,4BAAY;AACZ,0BAAU;AAAA,cACZ,OAAO;AACL,8BAAe,eAAe;AAC9B,oBAAK,WAAW,SAAU;AAC1B,yBAAS,IAAI,GAAG;AAChB,2BAAW,KAAK;AAChB,sBAAM,UAAW,KAAK,eAAe,MAAQ,eAAe;AAC5D,yBAAS;AAAA,cACX;AACA,mBAAK,CAAC,IAAI;AAAA,YACZ;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAKA,YAAI,eAAe;AAAA,UACjB,sBAAsB;AAAA;AAAA,UACtB,2BAA2B,SAAS,OAAO;AAEzC,gBAAI,OAAO,OAAQ,OAAO;AAC1B,gBAAI,MAAM,MAAM;AAChB,gBAAI,QAAQ,KAAK,MAAM,MAAM,CAAC;AAC9B,gBAAI,IAAI;AACR,mBAAO,OAAO;AACZ,kBAAI,OAAQ,SAAS,MAAO,MAAM;AAClC,uBAAS;AACT,iBAAG;AACD,wBAAS,MAAM,GAAG,KAAK;AACvB,wBAAQ,QAAQ,MAAM,GAAG;AAAA,cAC3B,SAAS,EAAE;AAEX,sBAAQ,OAAO,UAAW,SAAS;AACnC,sBAAQ,OAAO,UAAW,SAAS;AAAA,YACrC;AAGA,gBAAI,MAAM,GAAG;AACX,sBAAQ,QAAS,MAAM,CAAC,KAAK;AAAA,YAC/B;AAEA,oBAAQ,OAAO,UAAW,SAAS;AACnC,oBAAQ,OAAO,UAAW,SAAS;AAEnC,oBAAQ,QAAQ,KAAK,UAAU;AAAA,UACjC;AAAA,UAEA,gBAAgB,SAAS,OAAO,MAAM;AACpC,gBAAI,MAAM,KAAK;AACf,gBAAI,aAAa,IAAI,WAAW,OAAO,KAAK,CAAC;AAC7C,gBAAI,aAAa,CAAC;AAClB,uBAAW,uBAAuB,OAAO,aAAa,MAAM,MAAM,UAAU;AAC5E,gBAAI,WAAW,qBAAqB,YAAY,SAAS,CAAC,MAAM,GAAG;AACjE,oBAAM,wDAAwD,WAAW;AAAA,YAC3E;AACA,mBAAO;AACP,gBAAI,OAAO,IAAI,SAAS,OAAO,KAAK,CAAC;AACrC,gBAAI,cAAc,KAAK,SAAS,GAAG,IAAI;AACvC,uBAAW,cAAc;AACzB,mBAAO;AACP,gBAAI,eAAe,GAAG;AACpB,yBAAW,WAAW,KAAK,UAAU,GAAG,IAAI;AAC5C,qBAAO;AAAA,YACT;AAGA,mBAAO,IAAI,SAAS,OAAO,KAAK,EAAE;AAClC,uBAAW,SAAS,KAAK,UAAU,GAAG,IAAI;AAC1C,uBAAW,QAAQ,KAAK,UAAU,GAAG,IAAI;AACzC,mBAAO;AACP,gBAAI,eAAe,GAAG;AACpB,yBAAW,UAAU,KAAK,UAAU,GAAG,IAAI;AAC3C,qBAAO;AAAA,YACT,OACK;AACH,yBAAW,UAAU;AAAA,YACvB;AAEA,mBAAO,IAAI,SAAS,OAAO,KAAK,EAAE;AAClC,uBAAW,gBAAgB,KAAK,UAAU,GAAG,IAAI;AACjD,uBAAW,iBAAiB,KAAK,SAAS,GAAG,IAAI;AACjD,uBAAW,WAAW,KAAK,SAAS,GAAG,IAAI;AAC3C,uBAAW,YAAY,KAAK,SAAS,IAAI,IAAI;AAE7C,uBAAW,YAAY,KAAK,WAAW,IAAI,IAAI;AAC/C,uBAAW,OAAO,KAAK,WAAW,IAAI,IAAI;AAC1C,uBAAW,OAAO,KAAK,WAAW,IAAI,IAAI;AAC1C,mBAAO;AACP,iBAAK,aAAa;AAClB,iBAAK,MAAM;AAEX,gBAAI,UAAU;AACd,gBAAI,eAAe,GAAG;AACpB,0BAAY,eAAe,IAAI,KAAK;AACpC,yBAAW,KAAK,0BAA0B,IAAI,WAAW,OAAO,MAAM,WAAW,WAAW,WAAW,EAAE,CAAC;AAC1G,kBAAI,aAAa,WAAW,UAAU;AACpC,sBAAM;AAAA,cACR;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,mBAAmB,SAAS,OAAO,MAAM;AACvC,gBAAI,aAAa,KAAK;AACtB,gBAAI,oBAAoB,KAAK,iBAAiB,WAAW,SAAS;AAClE,gBAAI,aAAa,WAAW,UAAU,KAAK,gBAAgB,WAAW,SAAS;AAC/E,gBAAI,YAAY,KAAK,aAAa,OAAO,KAAK,KAAK,mBAAmB,UAAU;AAChF,gBAAI,YAAY,KAAK,aAAa,OAAO,KAAK,MAAM,YAAY,mBAAmB,UAAU;AAC7F,iBAAK,OAAQ,IAAI;AACjB,gBAAI,GAAG,QAAQ;AACf,iBAAK,IAAI,GAAG,IAAI,WAAW,SAAS,KAAK;AACvC,kBAAI,UAAU,CAAC,MAAM,UAAU,CAAC,GAAG;AACjC,wBAAQ;AACR;AAAA,cACF;AAAA,YACF;AACA,uBAAW,YAAY;AACvB,uBAAW,YAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UAEA,cAAc,SAAS,OAAO,KAAK,mBAAmB,UAAU;AAC9D,gBAAI;AACJ,gBAAI,sBAAsB,YAAY;AACpC,wBAAU,IAAI,WAAW,OAAO,KAAK,QAAQ;AAAA,YAC/C,OACK;AACH,kBAAI,WAAW,IAAI,YAAY,QAAQ;AACvC,kBAAI,SAAS,IAAI,WAAW,QAAQ;AACpC,qBAAO,IAAI,IAAI,WAAW,OAAO,KAAK,QAAQ,CAAC;AAC/C,wBAAU,IAAI,kBAAkB,QAAQ;AAAA,YAC1C;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,UAAU,SAAS,OAAO,MAAM;AAC9B,gBAAI,MAAM,KAAK;AACf,gBAAI,aAAa,KAAK;AACtB,gBAAI,YAAY,WAAW,QAAQ,WAAW;AAC9C,gBAAI,gBAAgB,WAAW;AAE/B,gBAAI,OAAO,IAAI,SAAS,OAAO,KAAK,CAAC;AACrC,gBAAI,OAAO,CAAC;AACZ,iBAAK,WAAW,KAAK,UAAU,GAAG,IAAI;AACtC,mBAAO;AAGP,iBAAK,MAAM,iBAAiB,cAAc,kBAAkB,MAAM,KAAK,UAAU;AAC/E,oBAAO;AAAA,YACT;AACA,gBAAI,QAAQ;AACZ,gBAAI,kBAAkB,GAAG;AACvB,uBAAS,IAAI,WAAW,KAAK,KAAK,YAAY,CAAC,CAAC;AAChD,mBAAK,SAAS;AACd,2BAAa,IAAI,WAAW,SAAS;AACrC,mBAAK,OAAO,aAAa;AACzB,qBAAO,KAAK;AAAA,YACd,WACS,KAAK,WAAW,GAAG;AAC1B,uBAAS,IAAI,WAAW,KAAK,KAAK,YAAY,CAAC,CAAC;AAChD,qBAAO,IAAI,SAAS,OAAO,KAAK,KAAK,QAAQ;AAC7C,kBAAI,MAAM,KAAK,SAAS,GAAG,IAAI;AAC/B,kBAAI,KAAK,GAAG,KAAK,GAAG,MAAM;AAC1B,iBAAG;AACD,oBAAI,MAAM,GAAG;AACX,yBAAO,OAAO;AAAE,2BAAO,IAAI,IAAI,KAAK,SAAS,IAAI;AAAA,kBAAG;AAAA,gBACtD,OAAO;AACL,wBAAM,KAAK,SAAS,IAAI;AACxB,wBAAM,CAAC;AACP,yBAAO,OAAO;AAAE,2BAAO,IAAI,IAAI;AAAA,kBAAK;AAAA,gBACtC;AACA,sBAAM,KAAK,SAAS,IAAI,IAAI;AAC5B,sBAAM;AAAA,cACR,SAAS,KAAK,KAAK;AACnB,kBAAK,QAAQ,UAAY,KAAK,OAAO,QAAS;AAC5C,sBAAM;AAAA,cACR;AAEA,2BAAa,IAAI,WAAW,SAAS;AACrC,kBAAI,KAAK,GAAG,IAAI;AAEhB,mBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,oBAAI,IAAI,GAAG;AACT,uBAAK,OAAO,KAAK,CAAC;AAClB,yBAAO,IAAI;AAAA,gBACb,OACK;AACH,uBAAK,OAAO,KAAK,CAAC;AAAA,gBACpB;AACA,oBAAI,KAAK,KAAK;AACZ,6BAAW,CAAC,IAAI;AAAA,gBAClB;AAAA,cACF;AACA,mBAAK,OAAO,aAAa;AAEzB,mBAAK,SAAS;AACd,qBAAO,KAAK;AAAA,YACd;AACA,iBAAK,MAAM;AACX,iBAAK,OAAO;AACZ,mBAAO;AAAA,UACT;AAAA,UAEA,kBAAkB,SAAS,OAAO,MAAM,mBAAmB,oBAAoB;AAC7E,gBAAI,MAAM,KAAK;AACf,gBAAI,aAAa,KAAK;AACtB,gBAAI,UAAU,WAAW;AACzB,gBAAI,YAAY,WAAW,QAAQ,WAAW;AAC9C,gBAAI,YAAY,WAAW;AAC3B,gBAAI,WAAW,WAAW,gBAAgB,aAAa,gBAAgB,SAAS,IAAI;AAEpF,gBAAI;AACJ,gBAAI,OAAO,KAAK,OAAO;AACvB,gBAAI,sBAAsB,YAAY;AACpC,wBAAU,IAAI,WAAW,OAAO,KAAK,QAAQ;AAAA,YAC/C,OACK;AACH,kBAAI,WAAW,IAAI,YAAY,QAAQ;AACvC,kBAAI,SAAS,IAAI,WAAW,QAAQ;AACpC,qBAAO,IAAI,IAAI,WAAW,OAAO,KAAK,QAAQ,CAAC;AAC/C,wBAAU,IAAI,kBAAkB,QAAQ;AAAA,YAC1C;AACA,gBAAI,QAAQ,WAAW,YAAY,SAAS;AAC1C,kBAAI,oBAAoB;AACtB,qBAAK,OAAO,eAAe,aAAa,mBAAmB,SAAS,WAAW,SAAS,mBAAmB,IAAI;AAAA,cACjH,OACK;AACH,qBAAK,OAAO,eAAe;AAAA,cAC7B;AAAA,YACF,OAEA;AACE,mBAAK,OAAO,eAAe,IAAI,kBAAkB,YAAY,OAAO;AACpE,kBAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,SAAS;AAClC,kBAAI,UAAU,GAAG;AACf,oBAAI,oBAAoB;AACtB,uBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,wBAAI,KAAK,CAAC,GAAG;AACX,+BAAS;AACT,2BAAK,IAAI,GAAG,IAAI,SAAS,KAAK,UAAQ,WAAW;AAC/C,6BAAK,OAAO,aAAa,MAAM,IAAI,QAAQ,GAAG;AAAA,sBAChD;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF,OACK;AACH,uBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,wBAAI,KAAK,CAAC,GAAG;AACX,+BAAS,IAAI;AACb,2BAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,6BAAK,OAAO,aAAa,SAAS,CAAC,IAAI,QAAQ,GAAG;AAAA,sBACpD;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,OACK;AACH,qBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,sBAAI,KAAK,CAAC,GAAG;AACX,yBAAK,OAAO,aAAa,CAAC,IAAI,QAAQ,GAAG;AAAA,kBAC3C;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,mBAAO;AACP,iBAAK,MAAM;AACX,mBAAO;AAAA,UACT;AAAA,UAEA,iBAAiB,SAAS,OAAO,MAAM;AACrC,gBAAI,WAAW,KAAK;AAKpB,gBAAI,OAAO,IAAI,SAAS,OAAO,KAAK,KAAK,EAAE;AAC3C,iBAAK,OAAO;AACZ,gBAAI,UAAU,KAAK,SAAS,GAAG,IAAI;AACnC,gBAAI,UAAU,GAAG;AACf,oBAAM;AAAA,YACR;AACA,gBAAI,OAAO,KAAK,SAAS,GAAG,IAAI;AAChC,gBAAI,KAAK,KAAK,SAAS,GAAG,IAAI;AAC9B,gBAAI,KAAK,KAAK,SAAS,IAAI,IAAI;AAC/B,gBAAI,MAAM,IAAI;AACZ,qBAAO;AAAA,YACT;AACA,gBAAI,kBAAkB,IAAI,YAAY,KAAK,EAAE;AAC7C,yBAAa,WAAW,OAAO,MAAM,eAAe;AACpD,gBAAI,YAAY,CAAC;AACjB,gBAAI,GAAG,GAAG,GAAG;AAEb,iBAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxB,kBAAI,KAAK,IAAI,OAAO,IAAI;AACxB,wBAAU,CAAC,IAAI,EAAE,OAAO,gBAAgB,IAAI,EAAE,GAAG,QAAQ,KAAK;AAAA,YAChE;AAEA,gBAAI,YAAY,MAAM,aAAa,KAAK;AACxC,gBAAI,YAAY,KAAK,KAAK,YAAY,CAAC;AACvC,gBAAI,WAAW,IAAI,YAAY,YAAY,CAAC;AAC5C,gBAAI,SAAS,IAAI,WAAW,QAAQ;AACpC,mBAAO,IAAI,IAAI,WAAW,OAAO,KAAK,KAAK,SAAS,CAAC;AACrD,gBAAI,cAAc,IAAI,YAAY,QAAQ;AAC1C,gBAAI,SAAS,GAAG,MAAM,SAAS;AAC/B,mBAAO,YAAY,CAAC;AACpB,iBAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxB,kBAAI,KAAK,IAAI,OAAO,IAAI;AACxB,oBAAM,UAAU,CAAC,EAAE;AACnB,kBAAI,MAAM,GAAG;AACX,0BAAU,CAAC,EAAE,SAAU,QAAQ,WAAa,KAAK;AAEjD,oBAAI,KAAK,UAAU,KAAK;AACtB,4BAAU;AACV,sBAAI,WAAW,IAAI;AACjB,6BAAS;AACT;AACA,2BAAO,YAAY,MAAM;AAAA,kBAC3B;AAAA,gBACF,OACK;AACH,4BAAU,MAAM;AAChB;AACA,yBAAO,YAAY,MAAM;AACzB,4BAAU,CAAC,EAAE,UAAU,SAAU,KAAK;AAAA,gBACxC;AAAA,cACF;AAAA,YACF;AAOA,gBAAI,aAAa,GAAG,iBAAiB;AACrC,gBAAI,OAAO,IAAI,SAAS;AACxB,iBAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,kBAAI,UAAU,CAAC,MAAM,QAAW;AAC9B,6BAAa,KAAK,IAAI,YAAY,UAAU,CAAC,EAAE,KAAK;AAAA,cACtD;AAAA,YACF;AACA,gBAAI,cAAc,UAAU;AAC1B,+BAAiB;AAAA,YACnB,OACK;AACH,+BAAiB;AAAA,YACnB;AAKA,gBAAI,YAAY,CAAC,GAAG,OAAO,MAAM,YAAY,IAAI,YAAY;AAC7D,iBAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxB,kBAAI,KAAK,IAAI,OAAO,IAAI;AACxB,oBAAM,UAAU,CAAC,EAAE;AACnB,kBAAI,MAAM,GAAG;AACX,wBAAQ,CAAC,KAAK,CAAC;AACf,oBAAI,OAAO,gBAAgB;AACzB,yBAAO,UAAU,CAAC,EAAE,UAAW,iBAAiB;AAChD,+BAAa,KAAM,iBAAiB;AACpC,uBAAK,IAAI,GAAG,IAAI,YAAY,KAAK;AAC/B,8BAAU,OAAO,CAAC,IAAI;AAAA,kBACxB;AAAA,gBACF,OACK;AAEH,yBAAO,UAAU,CAAC,EAAE;AACpB,yBAAO;AACP,uBAAK,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;AAChC,iCAAa,SAAS,KAAK;AAC3B,wBAAI,YAAY;AACd,0BAAI,CAAC,KAAK,OAAO;AACf,6BAAK,QAAQ,IAAI,SAAS;AAAA,sBAC5B;AACA,6BAAO,KAAK;AAAA,oBACd,OACK;AACH,0BAAI,CAAC,KAAK,MAAM;AACd,6BAAK,OAAO,IAAI,SAAS;AAAA,sBAC3B;AACA,6BAAO,KAAK;AAAA,oBACd;AACA,wBAAI,OAAO,KAAK,CAAC,KAAK,KAAK;AACzB,2BAAK,MAAM,MAAM,CAAC;AAAA,oBACpB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,mBAAO;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UAEA,aAAa,SAAS,OAAO,MAAM,mBAAmB,oBAAoB;AACxE,gBAAI,aAAa,KAAK;AACtB,gBAAI,UAAU,WAAW;AACzB,gBAAI,SAAS,KAAK,WAAW;AAC7B,gBAAI,QAAQ,KAAK,WAAW;AAC5B,gBAAI,YAAY,QAAQ;AAKxB,gBAAI,cAAc,KAAK,gBAAgB,OAAO,IAAI;AAClD,gBAAI,YAAY,YAAY;AAC5B,gBAAI,OAAO,YAAY;AAEvB,gBAAI,cAAc,YAAY;AAC9B,gBAAI,SAAS,YAAY;AACzB,gBAAI,SAAS,YAAY;AACzB,gBAAI,iBAAiB,YAAY;AACjC,gBAAI,aAAa,YAAY;AAC7B,gBAAI,SAAS,KAAK,WAAW,cAAc,IAAI,MAAM;AAIrD,gBAAI,MAAM,KAAK,OAAO,OAAO,KAAK,OAAO,YAAY,QAAQ,aAAa;AAC1E,gBAAI,GAAG,GAAG,GAAG;AACb,gBAAI,UAAU;AACd,gBAAI,SAAS,GAAG;AACd;AACA,uBAAS;AAAA,YACX;AACA,gBAAI,OAAO,YAAY,MAAM;AAC7B,gBAAI,cAAc,KAAK,eAAe;AACtC,gBAAI,qBAAqB,IAAI,kBAAkB,YAAY,OAAO;AAClE,gBAAI,eAAe;AACnB,gBAAI;AAEJ,gBAAI,UAAU,KAAK,aAAa;AAC9B,mBAAK,OAAO,GAAG,OAAO,SAAS,QAAQ;AACrC,oBAAI,UAAU,GAAG;AAEf,iCAAe,IAAI,kBAAkB,mBAAmB,QAAQ,YAAY,MAAM,SAAS;AAC3F,4BAAU;AAAA,gBACZ;AACA,oBAAI,KAAK,WAAW,kBAAkB,QAAQ,QAAQ;AACpD,uBAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK;AAClC,yBAAK,IAAI,GAAG,IAAI,OAAO,KAAK,KAAK;AAC/B,4BAAM;AACN,+BAAU,QAAQ,WAAa,KAAK;AACpC,oCAAc;AACd,0BAAI,KAAK,SAAS,gBAAgB;AAChC,kCAAY,YAAY,SAAS,CAAC,MAAQ,KAAK,SAAS;AACxD,sCAAc;AAAA,sBAChB;AACA,0BAAI,UAAU,WAAW,GACzB;AACE,8BAAM,UAAU,WAAW,EAAE,CAAC;AAC9B,kCAAU,UAAU,WAAW,EAAE,CAAC;AAAA,sBACpC,OACK;AACH,iCAAU,QAAQ,WAAa,KAAK;AACpC,sCAAc;AACd,4BAAI,KAAK,SAAS,YAAY;AAC5B,oCAAY,YAAY,SAAS,CAAC,MAAQ,KAAK,SAAS;AACxD,wCAAc;AAAA,wBAChB;AACA,+BAAO;AACP,6BAAK,KAAK,GAAG,KAAK,YAAY,MAAM;AAClC,uCAAa,WAAY,aAAa,KAAK,IAAK;AAChD,iCAAO,aAAa,KAAK,QAAQ,KAAK;AACtC,8BAAI,EAAE,KAAK,QAAQ,KAAK,QAAQ;AAC9B,kCAAM,KAAK;AACX,qCAAS,SAAS,KAAK;AACvB;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAEA,0BAAI,UAAU,IAAI;AAChB,kCAAU;AACV;AACA,+BAAO,YAAY,MAAM;AAAA,sBAC3B;AAEA,8BAAQ,MAAM;AACd,0BAAI,aAAa;AACf,4BAAI,IAAI,GAAG;AACT,mCAAS;AAAA,wBACX,WACS,IAAI,GAAG;AACd,mCAAS,aAAa,IAAI,KAAK;AAAA,wBACjC,OACK;AACH,mCAAS;AAAA,wBACX;AACA,iCAAS;AACT,qCAAa,CAAC,IAAI;AAClB,kCAAU;AAAA,sBACZ,OACK;AACH,qCAAa,CAAC,IAAI;AAAA,sBACpB;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF,OACK;AACH,uBAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK;AAClC,yBAAK,IAAI,GAAG,IAAI,OAAO,KAAK,KAAK;AAC/B,0BAAI,KAAK,CAAC,GAAG;AACX,8BAAM;AACN,iCAAU,QAAQ,WAAa,KAAK;AACpC,sCAAc;AACd,4BAAI,KAAK,SAAS,gBAAgB;AAChC,oCAAY,YAAY,SAAS,CAAC,MAAQ,KAAK,SAAS;AACxD,wCAAc;AAAA,wBAChB;AACA,4BAAI,UAAU,WAAW,GACzB;AACE,gCAAM,UAAU,WAAW,EAAE,CAAC;AAC9B,oCAAU,UAAU,WAAW,EAAE,CAAC;AAAA,wBACpC,OACK;AACH,mCAAU,QAAQ,WAAa,KAAK;AACpC,wCAAc;AACd,8BAAI,KAAK,SAAS,YAAY;AAC5B,sCAAY,YAAY,SAAS,CAAC,MAAQ,KAAK,SAAS;AACxD,0CAAc;AAAA,0BAChB;AACA,iCAAO;AACP,+BAAK,KAAK,GAAG,KAAK,YAAY,MAAM;AAClC,yCAAa,WAAY,aAAa,KAAK,IAAK;AAChD,mCAAO,aAAa,KAAK,QAAQ,KAAK;AACtC,gCAAI,EAAE,KAAK,QAAQ,KAAK,QAAQ;AAC9B,oCAAM,KAAK;AACX,uCAAS,SAAS,KAAK;AACvB;AAAA,4BACF;AAAA,0BACF;AAAA,wBACF;AAEA,4BAAI,UAAU,IAAI;AAChB,oCAAU;AACV;AACA,iCAAO,YAAY,MAAM;AAAA,wBAC3B;AAEA,gCAAQ,MAAM;AACd,4BAAI,aAAa;AACf,8BAAI,IAAI,KAAK,KAAK,IAAI,CAAC,GAAG;AACxB,qCAAS;AAAA,0BACX,WACS,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG;AACjC,qCAAS,aAAa,IAAI,KAAK;AAAA,0BACjC,OACK;AACH,qCAAS;AAAA,0BACX;AAEA,mCAAS;AACT,uCAAa,CAAC,IAAI;AAClB,oCAAU;AAAA,wBACZ,OACK;AACH,uCAAa,CAAC,IAAI;AAAA,wBACpB;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OACK;AACH,mBAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK;AAClC,qBAAK,IAAI,GAAG,IAAI,OAAO,KAAK;AAC1B,sBAAI,IAAI,QAAQ;AAChB,sBAAI,CAAC,QAAQ,KAAK,CAAC,GAAG;AACpB,yBAAK,OAAO,GAAG,OAAO,SAAS,QAAQ,KAAG,WAAW;AACnD,4BAAM;AACN,+BAAU,QAAQ,WAAa,KAAK;AACpC,oCAAc;AACd,0BAAI,KAAK,SAAS,gBAAgB;AAChC,kCAAY,YAAY,SAAS,CAAC,MAAQ,KAAK,SAAS;AACxD,sCAAc;AAAA,sBAChB;AACA,0BAAI,UAAU,WAAW,GACzB;AACE,8BAAM,UAAU,WAAW,EAAE,CAAC;AAC9B,kCAAU,UAAU,WAAW,EAAE,CAAC;AAAA,sBACpC,OACK;AACH,iCAAU,QAAQ,WAAa,KAAK;AACpC,sCAAc;AACd,4BAAI,KAAK,SAAS,YAAY;AAC5B,oCAAY,YAAY,SAAS,CAAC,MAAQ,KAAK,SAAS;AACxD,wCAAc;AAAA,wBAChB;AACA,+BAAO;AACP,6BAAK,KAAK,GAAG,KAAK,YAAY,MAAM;AAClC,uCAAa,WAAY,aAAa,KAAK,IAAK;AAChD,iCAAO,aAAa,KAAK,QAAQ,KAAK;AACtC,8BAAI,EAAE,KAAK,QAAQ,KAAK,QAAQ;AAC9B,kCAAM,KAAK;AACX,qCAAS,SAAS,KAAK;AACvB;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAEA,0BAAI,UAAU,IAAI;AAChB,kCAAU;AACV;AACA,+BAAO,YAAY,MAAM;AAAA,sBAC3B;AAEA,8BAAQ,MAAM;AACd,mCAAa,CAAC,IAAI;AAAA,oBACpB;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,iBAAK,MAAM,KAAK,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,IAAI;AAC3D,iBAAK,OAAO,eAAe;AAE3B,gBAAI,UAAU,KAAK,CAAC,oBAAoB;AACtC,mBAAK,OAAO,eAAe,aAAa,mBAAmB,oBAAoB,WAAW,SAAS,iBAAiB;AAAA,YACtH;AAAA,UACF;AAAA,UAEA,YAAY,SAAS,OAAO,MAAM,iBAAiB,QAAQ,MAAM;AAC/D;AAEE,kBAAI,aAAa,KAAK;AACtB,kBAAI,cAAc,WAAW;AAE7B,kBAAI,WAAW;AACf,kBAAI,iBAAmB,MAAM,aAAa,KAAK,OAAQ,IAAK,IAAK,MAAM,aAAa,KAAK;AACzF,kBAAI,OAAO,IAAI,SAAS,OAAO,KAAK,KAAK,cAAc;AACvD,kBAAI,aAAa,KAAK,SAAS,CAAC;AAChC;AACA,kBAAI,SAAS,cAAc;AAC3B,kBAAI,IAAK,WAAW,IAAK,IAAI,IAAI;AACjC,kBAAI,SAAS,aAAa,MAAM,IAAI,OAAO;AAC3C,kBAAI,UAAU,aAAa;AAC3B,kBAAI,cAAc;AAClB,kBAAI,MAAM,GAAG;AACX,8BAAc,KAAK,SAAS,QAAQ;AAAG;AAAA,cACzC,WAAW,MAAM,GAAG;AAClB,8BAAc,KAAK,UAAU,UAAU,IAAI;AAAG,4BAAY;AAAA,cAC5D,WAAW,MAAM,GAAG;AAClB,8BAAc,KAAK,UAAU,UAAU,IAAI;AAAG,4BAAY;AAAA,cAC5D,OAAO;AACL,sBAAM;AAAA,cACR;AAGA,kBAAI,QAAQ,IAAI,WAAW;AAC3B,kBAAI,aAAa,UAAU,QAAQ,WAAW;AAC9C,kBAAI,QAAQ,SAAS,UAAU,mBAAmB;AAClD,kBAAI,OAAO,WAAW,UAAU,IAAI,WAAW,UAAU,IAAI,IAAI,WAAW;AAC5E,kBAAI,OAAO;AACT,qBAAK,QAAQ;AACb,2BAAW,KAAK,SAAS,QAAQ;AACjC,oCAAoB;AACpB;AACA,4BAAY,KAAK,MAAM,WAAW,KAAK,UAAU,CAAC;AAClD,4BAAY,KAAK,KAAK,YAAY,CAAC;AACnC,2BAAW,IAAI,YAAY,YAAY,CAAC;AACxC,yBAAS,IAAI,WAAW,QAAQ;AAEhC,qBAAK,OAAO;AACZ,uBAAO,IAAI,IAAI,WAAW,OAAO,KAAK,KAAK,SAAS,CAAC;AAErD,0BAAU,IAAI,YAAY,QAAQ;AAClC,qBAAK,OAAO;AAEZ,+BAAe;AACf,uBAAQ,WAAW,MAAO,cAAc;AACtC;AAAA,gBACF;AACA,4BAAY,KAAK,KAAK,cAAc,eAAe,CAAC;AACpD,4BAAY,KAAK,KAAK,YAAY,CAAC;AACnC,2BAAW,IAAI,YAAY,YAAY,CAAC;AACxC,yBAAS,IAAI,WAAW,QAAQ;AAChC,uBAAO,IAAI,IAAI,WAAW,OAAO,KAAK,KAAK,SAAS,CAAC;AACrD,8BAAc,IAAI,YAAY,QAAQ;AACtC,qBAAK,OAAO;AACZ,oBAAI,eAAe,GAAG;AACpB,2BAAS,WAAW,YAAY,SAAS,SAAS,WAAW,GAAG,QAAQ,OAAO,IAAI;AAAA,gBACrF,OACK;AACH,2BAAS,WAAW,WAAW,SAAS,SAAS,WAAW,GAAG,QAAQ,OAAO,IAAI;AAAA,gBACpF;AAEA,oBAAI,eAAe,GAAG;AAEpB,6BAAW,SAAS,aAAa,iBAAiB,cAAc,aAAa,MAAM;AAAA,gBACrF,OACK;AACH,6BAAW,QAAQ,aAAa,iBAAiB,cAAc,aAAa,MAAM;AAAA,gBACpF;AAAA,cACF,OACK;AAEH,qBAAK,QAAQ;AACb,+BAAe;AACf,qBAAK,OAAO;AACZ,oBAAI,eAAe,GAAG;AACpB,8BAAY,KAAK,KAAK,cAAc,eAAe,CAAC;AACpD,8BAAY,KAAK,KAAK,YAAY,CAAC;AACnC,6BAAW,IAAI,YAAY,YAAY,CAAC;AACxC,2BAAS,IAAI,WAAW,QAAQ;AAChC,yBAAO,IAAI,IAAI,WAAW,OAAO,KAAK,KAAK,SAAS,CAAC;AACrD,gCAAc,IAAI,YAAY,QAAQ;AACtC,uBAAK,OAAO;AACZ,sBAAI,eAAe,GAAG;AACpB,wBAAI,UAAU,MAAM;AAClB,iCAAW,iBAAiB,aAAa,iBAAiB,cAAc,WAAW;AAAA,oBACrF,OACK;AACH,iCAAW,SAAS,aAAa,iBAAiB,cAAc,aAAa,OAAO,QAAQ,OAAO,IAAI;AAAA,oBACzG;AAAA,kBACF,OACK;AACH,wBAAI,UAAU,MAAM;AAClB,iCAAW,gBAAgB,aAAa,iBAAiB,cAAc,WAAW;AAAA,oBACpF,OACK;AACH,iCAAW,QAAQ,aAAa,iBAAiB,cAAc,aAAa,OAAO,QAAQ,OAAO,IAAI;AAAA,oBACxG;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UAEF;AAAA,UAEA,WAAW,SAAS,OAAO,MAAM,mBAAmB,oBAAoB;AACtE,gBAAI,aAAa,KAAK;AACtB,gBAAI,QAAQ,WAAW;AACvB,gBAAI,SAAS,WAAW;AACxB,gBAAI,YAAY,QAAQ;AACxB,gBAAI,iBAAiB,WAAW;AAChC,gBAAI,YAAY,WAAW;AAC3B,gBAAI,eAAe,aAAa,gBAAgB,SAAS;AACzD,gBAAI,aAAa,KAAK,KAAK,QAAQ,cAAc;AACjD,gBAAI,aAAa,KAAK,KAAK,SAAS,cAAc;AAClD,iBAAK,OAAO,aAAa;AACzB,iBAAK,OAAO,aAAa;AACzB,iBAAK,OAAO,MAAM;AAClB,gBAAI,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,SAAS,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,YAAY,GAAG,aAAa,GAAG,SAAS,GAAG,WAAW,GAAG,SAAS,GAAG,YAAY,GAAG,WAAW,GAAG,YAAY,GAAG,IAAI,GAAG,WAAW;AAC1N,gBAAI,MAAM,OAAO,UAAU,QAAQ;AACnC,gBAAI;AACJ,gBAAI,kBAAkB,IAAI,kBAAkB,iBAAiB,cAAc;AAC3E,gBAAI,kBAAmB,SAAS,kBAAmB;AACnD,gBAAI,iBAAkB,QAAQ,kBAAmB;AACjD,gBAAI,YAAY;AAChB,gBAAI,UAAU,WAAW,SAAS;AAClC,gBAAI,OAAO,KAAK,OAAO;AACvB,gBAAI,eAAe,KAAK,OAAO;AAC/B,gBAAI,cAAc,WAAW;AAC7B,gBAAI,sBAAsB,eAAe,IAAI,KAAK;AAClD,gBAAI;AACJ,gBAAI,OAAO,WAAW;AAEtB,gBAAI;AACJ,iBAAK,SAAS,GAAG,SAAS,YAAY,UAAU;AAC9C,gCAAmB,WAAW,aAAa,IAAK,iBAAiB;AACjE,mBAAK,SAAS,GAAG,SAAS,YAAY,UAAU;AAE9C,iCAAkB,WAAW,aAAa,IAAK,iBAAiB;AAEhE,yBAAS,SAAS,QAAQ,iBAAiB,SAAS;AACpD,4BAAY,QAAQ;AAEpB,qBAAK,OAAO,GAAG,OAAO,SAAS,QAAQ;AACrC,sBAAI,UAAU,GAAG;AACf,0CAAsB;AACtB,6BAAS,SAAS,QAAQ,iBAAiB,SAAS;AACpD,mCAAe,IAAI,kBAAkB,KAAK,OAAO,aAAa,QAAQ,YAAY,OAAO,cAAc,SAAS;AAChH,2BAAO,WAAW,UAAU,IAAI;AAAA,kBAClC,OAAO;AACL,0CAAsB;AAAA,kBACxB;AACA,8BAAY,MAAM,aAAa,KAAK;AACpC,yBAAO,IAAI,SAAS,OAAO,KAAK,KAAK,KAAK,IAAI,IAAI,SAAS,CAAC;AAC5D,0BAAQ,CAAC;AACT,6BAAW;AACX,+BAAa,KAAK,SAAS,CAAC;AAC5B;AACA,mCAAiB,WAAW,eAAe,IAAI,aAAa,IAAI;AAChE,2BAAU,cAAc,IAAK;AAC7B,6BAAY,cAAc,IAAK;AAC/B,sBAAI,cAAgB,SAAS,kBAAmB,IAAK,sBAAsB;AACzE,0BAAM;AAAA,kBACR;AAEA,sBAAI,kBAAkB,SAAS,GAAG;AAChC,0BAAM;AAAA,kBACR;AAEA,kCAAgB,aAAa;AAC7B,sBAAI,gBAAgB,GAAG;AACrB,yBAAK,OAAO;AACZ,0BAAM,6BAA6B,gBAAgB;AAAA,kBACrD,WACS,kBAAkB,GAAG;AAC5B,wBAAI,gBAAgB;AAClB,0BAAI,MAAM;AACR,6BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,+BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,gCAAI,KAAK,MAAM,GAAG;AAChB,2CAAa,MAAM,IAAI,oBAAoB,MAAM;AAAA,4BACnD;AACA;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF,OACK;AACH,6BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,+BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,yCAAa,MAAM,IAAI,oBAAoB,MAAM;AACjD;AAAA,0BACF;AAAA,wBACF;AAAA,sBACF;AAAA,oBACF;AACA,yBAAK,QAAQ;AACb,yBAAK,OAAO;AACZ;AAAA,kBACF,WACS,kBAAkB,GAAG;AAC5B,wBAAI,gBAAgB;AAElB,4BAAM;AAAA,oBACR;AACA,yBAAK,QAAQ;AACb,yBAAK,OAAO;AACZ,+BAAW,kBAAkB,iBAAiB;AAC9C,gCAAY,MAAM,aAAa,KAAK;AACpC,+BAAW,WAAW,YAAY,WAAW;AAE7C,+BAAW,IAAI,YAAa,WAAW,iBAAkB,IAAI,WAAY,WAAW,eAAe,WAAW,YAAa;AAC3H,6BAAS,IAAI,WAAW,QAAQ;AAChC,2BAAO,IAAI,IAAI,WAAW,OAAO,KAAK,KAAK,QAAQ,CAAC;AACpD,8BAAU,IAAI,kBAAkB,QAAQ;AACxC,wBAAI;AACJ,wBAAI,MAAM;AACR,2BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,6BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,8BAAI,KAAK,MAAM,GAAG;AAChB,yCAAa,MAAM,IAAI,QAAQ,GAAG;AAAA,0BACpC;AACA;AAAA,wBACF;AACA,kCAAU;AAAA,sBACZ;AAAA,oBACF,OACK;AACH,2BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,6BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,uCAAa,QAAQ,IAAI,QAAQ,GAAG;AAAA,wBACtC;AACA,kCAAU;AAAA,sBACZ;AAAA,oBACF;AACA,yBAAK,OAAO,IAAI;AAAA,kBAClB,OACK;AACH,iCAAa,aAAa,gBAAiB,kBAAkB,YAAY,IAAK,IAAI,WAAW,MAAM;AACnG,6BAAS,aAAa,YAAY,OAAO,UAAU,YAAY,IAAI;AACnE,gCAAY,aAAa,gBAAgB,UAAU;AACnD,wBAAI,kBAAkB,GACtB;AACE,2BAAK,OAAO;AACZ,2BAAK,QAAQ;AAGb,0BAAI,MAAM;AACR,6BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,+BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,gCAAI,KAAK,MAAM,GAAG;AAChB,2CAAa,MAAM,IAAI,iBAAiB,KAAK,IAAI,MAAM,oBAAoB,MAAM,IAAI,MAAM,IAAI;AAAA,4BACjG;AACA;AAAA,0BACF;AACA,oCAAU;AAAA,wBACZ;AAAA,sBACF,OACK;AACH,6BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,+BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,yCAAa,MAAM,IAAI,iBAAiB,KAAK,IAAI,MAAM,oBAAoB,MAAM,IAAI,MAAM,IAAI;AAC/F;AAAA,0BACF;AACA,oCAAU;AAAA,wBACZ;AAAA,sBACF;AAAA,oBACF,OACK;AACH,2BAAK,OAAO;AAEZ,mCAAa,WAAW,OAAO,MAAM,iBAAiB,QAAQ,IAAI;AAClE,iCAAW;AAEX,0BAAI,gBAAgB;AAClB,4BAAI,MAAM;AACR,+BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,iCAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,kCAAI,KAAK,MAAM,GAAG;AAChB,6CAAa,MAAM,IAAI,gBAAgB,UAAU,IAAI,oBAAoB,MAAM;AAAA,8BACjF;AACA;AAAA,4BACF;AACA,sCAAU;AAAA,0BACZ;AAAA,wBACF,OACK;AACH,+BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,iCAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,2CAAa,MAAM,IAAI,gBAAgB,UAAU,IAAI,oBAAoB,MAAM;AAC/E;AAAA,4BACF;AACA,sCAAU;AAAA,0BACZ;AAAA,wBACF;AAAA,sBACF,WACS,MAAM;AACb,6BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,+BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,gCAAI,KAAK,MAAM,GAAG;AAChB,2CAAa,MAAM,IAAI,gBAAgB,UAAU;AAAA,4BACnD;AACA;AAAA,0BACF;AACA,oCAAU;AAAA,wBACZ;AAAA,sBACF,OACK;AACH,6BAAK,MAAM,GAAG,MAAM,iBAAiB,OAAO;AAC1C,+BAAK,MAAM,GAAG,MAAM,gBAAgB,OAAO;AACzC,yCAAa,QAAQ,IAAI,gBAAgB,UAAU;AAAA,0BACrD;AACA,oCAAU;AAAA,wBACZ;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,UAAU,KAAK,CAAC,oBAAoB;AACtC,mBAAK,OAAO,eAAe,aAAa,mBAAmB,KAAK,OAAO,cAAc,WAAW,SAAS,iBAAiB;AAAA,YAC5H;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAMA,gBAAgB,SAAS,MAAM;AAC7B,mBAAO;AAAA,cACL,wBAAwB,KAAK,WAAW;AAAA,cACxC,eAAe,KAAK,WAAW;AAAA,cAC/B,aAAa,KAAK,WAAW;AAAA,cAC7B,UAAU,KAAK,WAAW;AAAA,cAC1B,SAAS,KAAK,WAAW;AAAA,cACzB,iBAAiB,KAAK,WAAW;AAAA,cACjC,kBAAkB,KAAK,WAAW;AAAA,cAClC,YAAY,KAAK,WAAW;AAAA,cAC5B,aAAa,KAAK,WAAW;AAAA,cAC7B,aAAa,aAAa,aAAa,KAAK,WAAW,SAAS;AAAA,cAChE,aAAa,KAAK;AAAA,cAClB,QAAQ,KAAK,OAAO;AAAA,gBAClB,YAAY,KAAK,KAAK;AAAA,cACxB,IAAI;AAAA,cACJ,UAAU;AAAA,gBACR,cAAc,KAAK,OAAO;AAAA,gBAC1B,cAAc,KAAK,OAAO;AAAA;AAAA,gBAE1B,YAAY,KAAK,WAAW;AAAA,gBAC5B,YAAY,KAAK,WAAW;AAAA,gBAC5B,eAAe,KAAK;AAAA,cACtB;AAAA,YACF;AAAA,UACF;AAAA,UAEA,0BAA0B,SAAS,MAAM,oBAAoB;AAC3D,gBAAI,MAAM,KAAK,WAAW;AAC1B,gBAAI,SAAS,KAAK,WAAW;AAC7B,gBAAI,YAAY,KAAK,WAAW;AAChC,gBAAI,UAAU,KAAK,WAAW;AAC9B,gBAAI,YAAY,KAAK,WAAW,SAAS,KAAK,WAAW;AACzD,gBAAI,IAAI,GAAG,IAAI,GAAG,SAAS;AAC3B,gBAAI,OAAO,KAAK,OAAO;AACvB,gBAAI,eAAe,KAAK,OAAO;AAC/B,gBAAI,MAAM;AACR,kBAAI,UAAU,GAAG;AACf,oBAAI,oBAAoB;AACtB,uBAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,6BAAS,IAAI;AACb,0BAAM,UAAU,CAAC;AACjB,yBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,0BAAI,KAAK,CAAC,GAAG;AACX,qCAAa,SAAS,CAAC,IAAI;AAAA,sBAC7B;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF,OACK;AACH,uBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,wBAAI,KAAK,CAAC,GAAG;AACX,+BAAS,IAAI;AACb,2BAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,qCAAa,SAAS,OAAO,IAAI,UAAU,CAAC;AAAA,sBAC9C;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,OACK;AACH,qBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,sBAAI,KAAK,CAAC,GAAG;AACX,iCAAa,CAAC,IAAI;AAAA,kBACpB;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OACK;AACH,kBAAI,UAAU,KAAK,WAAW,KAAK;AACjC,oBAAI,oBAAoB;AACtB,uBAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,6BAAS,IAAI;AACb,0BAAM,UAAU,CAAC;AACjB,yBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,mCAAa,SAAS,CAAC,IAAI;AAAA,oBAC7B;AAAA,kBACF;AAAA,gBACF,OACK;AACH,uBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,6BAAS,IAAI;AACb,yBAAK,IAAI,GAAG,IAAI,SAAS,KAAK;AAC5B,mCAAa,SAAS,CAAC,IAAI,UAAU,CAAC;AAAA,oBACxC;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,OACK;AACH,qBAAK,IAAI,GAAG,IAAI,YAAY,SAAS,KAAK;AACxC,+BAAa,CAAC,IAAI;AAAA,gBACpB;AAAA,cACF;AAAA,YACF;AACA;AAAA,UACF;AAAA,UAEA,kBAAkB,SAAS,GAAG;AAC5B,gBAAI;AACJ,oBAAQ,GAAG;AAAA,cACT,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF;AACE,qBAAK;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,cAAc,SAAS,GAAG;AACxB,gBAAI;AACJ,oBAAQ,GAAG;AAAA,cACT,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF,KAAK;AACH,qBAAK;AACL;AAAA,cACF;AACE,qBAAK;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,mBAAmB,SAAS,GAAG,KAAK;AAClC,gBAAI,OAAO,MAAM;AACf,qBAAO;AAAA,YACT;AACA,gBAAI;AACJ,oBAAQ,GAAG;AAAA,cACT,KAAK;AACH,0BAAU,OAAO,QAAQ,OAAO;AAChC;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,KAAK,OAAO;AAC7B;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,UAAU,OAAO;AAClC;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,KAAK,OAAO;AAC7B;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,eAAe,OAAO;AACvC;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,KAAK,OAAO;AAC7B;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,yBAA2B,OAAO;AACnD;AAAA,cACF,KAAK;AACH,0BAAU,OAAO,0BAA4B,OAAO;AACpD;AAAA,cACF;AACE,0BAAU;AAAA,YACd;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,iBAAiB,SAAS,GAAG;AAC3B,gBAAI,IAAI;AACR,oBAAQ,GAAG;AAAA,cACT,KAAK;AAAA;AAAA,cACL,KAAK;AACH,oBAAI;AACJ;AAAA,cACF,KAAK;AAAA;AAAA,cACL,KAAK;AACH,oBAAI;AACJ;AAAA,cACF,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AACH,oBAAI;AACJ;AAAA,cACF,KAAK;AACH,oBAAI;AACJ;AAAA,cACF;AACE,oBAAI;AAAA,YACR;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,iBAAiB,SAAS,IAAI,IAAI;AAChC,gBAAI,IAAI;AACR,oBAAQ,IAAI;AAAA,cACV,KAAK;AAAA;AAAA,cACL,KAAK;AACH,oBAAI,KAAK;AACT;AAAA,cACF,KAAK;AAAA;AAAA,cACL,KAAK;AACH,oBAAI,KAAK,IAAI;AACb;AAAA,cACF,KAAK;AACH,oBAAI,MAAM,IAAI;AACZ,sBAAI;AAAA,gBACN,WACS,MAAM,IAAI;AACjB,sBAAI;AAAA,gBACN,OACK;AACH,sBAAI;AAAA,gBACN;AACA;AAAA,cACF,KAAK;AACH,oBAAI,MAAM,IAAI;AACZ,sBAAI;AAAA,gBACN,OACK;AACH,sBAAI,KAAK,IAAI,KAAK;AAAA,gBACpB;AACA;AAAA,cACF;AACE,oBAAI;AACJ;AAAA,YACJ;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,aAAa,SAAS,OAAO,UAAU,YAAY,MAAM;AACvD,gBAAI,OAAO;AACX,oBAAQ,YAAY;AAAA,cAClB,KAAK;AACH,uBAAO,KAAK,QAAQ,QAAQ;AAC5B;AAAA,cACF,KAAK;AACH,uBAAO,KAAK,SAAS,QAAQ;AAC7B;AAAA,cACF,KAAK;AACH,uBAAO,KAAK,SAAS,UAAU,IAAI;AACnC;AAAA,cACF,KAAK;AACH,uBAAO,KAAK,UAAU,UAAU,IAAI;AACpC;AAAA,cACF,KAAK;AACH,uBAAO,KAAK,SAAS,UAAU,IAAI;AACnC;AAAA,cACF,KAAK;AACH,uBAAO,KAAK,UAAU,UAAU,IAAI;AACpC;AAAA,cACF,KAAK;AACH,uBAAO,KAAK,WAAW,UAAU,IAAI;AACrC;AAAA,cACF,KAAK;AACH,uBAAO,KAAK,WAAW,UAAU,IAAI;AACrC;AAAA,cACF;AACE,sBAAO;AAAA,YACX;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,oBAAoB,SAAS,QAAQ,WAAW,SAAS,mBAAmB,YAAY;AACtF,gBAAI,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO;AAC7C,gBAAI,UAAU,GAAG;AACf,qBAAO,IAAI,kBAAkB,YAAY,OAAO;AAChD,kBAAI,YAAY;AACd,qBAAK,IAAE,GAAG,IAAE,WAAW,KAAK;AAC1B,yBAAO;AACP,uBAAK,OAAK,GAAG,OAAO,SAAS,QAAQ,QAAQ,WAAW;AACtD,yBAAK,IAAI,IAAI,OAAO,GAAG;AAAA,kBACzB;AAAA,gBACF;AAAA,cACF,OACK;AACH,qBAAK,IAAE,GAAG,IAAE,WAAW,KAAK;AAC1B,yBAAO;AACP,uBAAK,OAAK,GAAG,OAAO,SAAS,QAAQ,QAAQ,WAAW;AACtD,yBAAK,GAAG,IAAI,OAAO,IAAI;AAAA,kBACzB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAKA,YAAI,WAAW,SAAS,KAAK,MAAM,OAAO;AACxC,eAAK,MAAM;AACX,eAAK,OAAO;AACZ,eAAK,QAAQ;AAAA,QACf;AAEA,YAAIA,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAoChB,QAAQ,SAAwB,OAAkB,SAAS;AAEzD,sBAAU,WAAW,CAAC;AACtB,gBAAI,cAAc,QAAQ;AAG1B,gBAAI,IAAI,GAAG,OAAO,CAAC;AACnB,iBAAK,MAAM,QAAQ,eAAe;AAClC,iBAAK,SAAS,CAAC;AAGf,gBAAI,CAAC,aAAa,eAAe,OAAO,IAAI,GAAG;AAC7C;AAAA,YACF;AAEA,gBAAI,aAAa,KAAK;AACtB,gBAAI,cAAc,WAAW;AAC7B,gBAAI,oBAAoB,aAAa,iBAAiB,WAAW,SAAS;AAG1E,gBAAI,cAAc,GAAG;AACnB,oBAAM,gCAAgC;AAAA,YACxC;AAGA,yBAAa,SAAS,OAAO,IAAI;AACjC,gBAAI,WAAW,kBAAkB,WAAW,QAAQ,WAAW,UAAU,CAAC,KAAK,OAAO,YAAY;AAChG,mBAAK,OAAO,aAAa,QAAQ;AAAA,YACnC;AAEA,gBAAI,YAAY,WAAW,QAAQ,WAAW;AAC9C,iBAAK,OAAO,eAAe,IAAI,kBAAkB,YAAY,WAAW,OAAO;AAE/E,iBAAK,UAAU;AAAA,cACb,UAAU;AAAA,cACV,cAAc;AAAA,cACd,KAAK;AAAA,cACL,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,gBAAgB;AAAA,YAClB;AACA,gBAAI,qBAAqB,CAAC,QAAQ;AAClC,gBAAI,WAAW,kBAAkB,GAAG;AAElC,kBAAI,WAAW,SAAS,WAAW,MACnC;AACE,6BAAa,yBAAyB,MAAM,kBAAkB;AAAA,cAChE,WACS,eAAe,KAAK,aAAa,kBAAkB,OAAO,IAAI,GAAG;AACxE,6BAAa,yBAAyB,MAAM,kBAAkB;AAAA,cAChE,OACK;AACH,oBAAI,OAAO,IAAI,SAAS,OAAO,KAAK,KAAK,CAAC;AAC1C,oBAAI,oBAAoB,KAAK,SAAS,CAAC;AACvC,qBAAK;AACL,oBAAI,mBAAmB;AAErB,+BAAa,iBAAiB,OAAO,MAAM,mBAAmB,kBAAkB;AAAA,gBAClF,OACK;AAIH,sBAAI,cAAc,KAAK,WAAW,aAAa,KAAK,KAAK,IAAI,WAAW,YAAY,GAAG,IAAI,MAAS;AAElG,wBAAI,cAAc,KAAK,SAAS,CAAC;AACjC,yBAAK;AACL,yBAAK,aAAa;AAClB,wBAAI,cAAc,KAAM,cAAc,KAAK,cAAc,GAAI;AAC3D,4BAAM,0BAA0B;AAAA,oBAClC;AACA,wBAAI,aAAa;AAEf,mCAAa,YAAY,OAAO,MAAM,mBAAmB,kBAAkB;AAAA,oBAC7E,OACK;AAEH,mCAAa,UAAU,OAAO,MAAM,mBAAmB,kBAAkB;AAAA,oBAC3E;AAAA,kBACF,OACK;AAEH,iCAAa,UAAU,OAAO,MAAM,mBAAmB,kBAAkB;AAAA,kBAC3E;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,iBAAK,YAAY,KAAK;AACtB,gBAAI;AACJ,gBAAI,QAAQ,aAAa;AACvB,qBAAO,KAAK,WAAW,WAAW,QAAQ,cAAc,KAAK;AAC7D,kBAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AAEvB,qBAAK,YAAY,QAAQ,cAAc,KAAK,WAAW;AAAA,cACzD;AAAA,YACF,OACK;AACH,qBAAO,KAAK,WAAW,WAAW,KAAK;AACvC,kBAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AAEvB,qBAAK,YAAY,KAAK,WAAW;AAAA,cACnC;AAAA,YACF;AAEA,gBAAI,SAAS;AAAA,cACX,OAAO,WAAW;AAAA,cAClB,QAAQ,WAAW;AAAA,cACnB,WAAW,KAAK,OAAO;AAAA,cACvB,UAAU,WAAW;AAAA,cACrB,UAAU,WAAW;AAAA,cACrB,iBAAiB,WAAW;AAAA,cAC5B,UAAU,WAAW;AAAA,cACrB,UAAU;AAAA,gBACR,WAAW,WAAW;AAAA,gBACtB,WAAW,WAAW;AAAA,cACxB;AAAA,cACA,UAAU,KAAK,OAAO;AAAA;AAAA,YAExB;AAIA,gBAAI,KAAK,OAAO,cAAc,aAAa,kBAAkB,WAAW,WAAW,WAAW,GAAG;AAC/F,kBAAI,OAAO,KAAK,OAAO;AACvB,mBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,oBAAI,CAAC,KAAK,CAAC,GAAG;AACZ,yBAAO,UAAU,CAAC,IAAI;AAAA,gBACxB;AAAA,cACF;AACA,qBAAO,cAAc;AAAA,YACvB;AACA,iBAAK,cAAc;AACnB,gBAAI,QAAQ,gBAAgB;AAC1B,qBAAO,WAAW,aAAa,eAAe,IAAI;AAAA,YACpD;AACA,mBAAO;AAAA,UACT;AAAA,UAEA,cAAc,SAAwB,OAAO;AAC3C,gBAAI,QAAQ;AACZ,gBAAI,IAAI;AACR,gBAAI,OAAO,CAAC;AACZ,iBAAK,MAAM;AACX,iBAAK,SAAS,CAAC;AACf,mBAAO,IAAI,MAAM,aAAa,IAAI;AAChC,2BAAa,eAAe,OAAO,IAAI;AACvC,mBAAK,KAAK,WAAW;AACrB;AACA,mBAAK,MAAM;AAAA,YACb;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAOA;AAAA,MACT,EAAG;AAEH,UAAI,yBAA0B,WAAW;AACvC,YAAI,IAAI,IAAI,YAAY,CAAC;AACzB,YAAI,IAAI,IAAI,WAAW,CAAC;AACxB,YAAI,IAAI,IAAI,YAAY,CAAC;AACzB,UAAE,CAAC,IAAI;AACP,eAAO,EAAE,CAAC,MAAM;AAAA,MAClB,EAAG;AAEH,UAAIC,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAoBT,QAAQ,SAAS,aAAa,SAAS;AACrC,cAAI,CAAC,wBAAwB;AAC3B,kBAAM;AAAA,UACR;AACA,oBAAU,WAAW,CAAC;AACtB,cAAI,cAAc,QAAQ,eAAe;AACzC,cAAI,aAAa,IAAI,WAAW,aAAa,aAAa,EAAE;AAC5D,cAAI,uBAAuB,OAAO,aAAa,MAAM,MAAM,UAAU;AACrE,cAAI,MAAM;AACV,cAAI,qBAAqB,KAAK,MAAM,aAAa;AAC/C,mBAAO;AACP,2BAAe;AAAA,UACjB,WACS,qBAAqB,UAAU,GAAG,CAAC,MAAM,SAAS;AACzD,mBAAO;AACP,2BAAe;AAAA,UACjB,OACK;AACH,kBAAM,wCAAwC;AAAA,UAChD;AAEA,cAAI,SAAS,GAAG,MAAM,YAAY,aAAa,IAAI,iBAAiB,YAAY,CAAC,GAAG,UAAU;AAC9F,cAAI,oBAAoB;AAAA,YACtB,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ,CAAC;AAAA,YACT,WAAW,QAAQ;AAAA,YACnB,MAAM;AAAA,YACN,YAAY,CAAC;AAAA,UACf;AACA,cAAI,sBAAsB;AAE1B,iBAAO,cAAc,KAAK;AACxB,gBAAI,SAAS,KAAK,OAAO,aAAa;AAAA,cACpC;AAAA;AAAA,cACA;AAAA;AAAA,cACA;AAAA;AAAA,cACA,YAAY,WAAW,IAAI,OAAO;AAAA;AAAA,cAClC,mBAAmB,WAAW,IAAI,OAAO;AAAA;AAAA,cACzC,gBAAgB;AAAA;AAAA,cAChB,4BAA4B,QAAQ;AAAA;AAAA,cACpC,WAAW,QAAQ,aAAa;AAAA;AAAA,cAChC,aAAa,QAAQ,eAAe;AAAA;AAAA,YACtC,CAAC;AAED,0BAAc,OAAO,SAAS;AAC9B,uBAAW,OAAO;AAClB,gBAAI,WAAW,GAAG;AAChB,gCAAkB,OAAO;AACzB,gCAAkB,QAAQ,OAAO;AACjC,gCAAkB,SAAS,OAAO;AAClC,gCAAkB,WAAW,OAAO,YAAY;AAEhD,gCAAkB,YAAY,OAAO,aAAa,OAAO,SAAS;AAClE,gCAAkB,OAAO;AAAA,YAC3B;AACA,gBAAI,eAAe,GAAG;AACpB,kBAAI,UAAU;AACZ,0BAAU,KAAK,QAAQ;AAAA,cACzB;AACA,kBAAI,OAAO,SAAS,QAAQ,OAAO,SAAS,KAAK,WAAW,GAAG;AAC7D;AAAA,cACF;AAAA,YACF;AAEA;AACA,8BAAkB,OAAO,KAAK,OAAO,SAAS;AAC9C,8BAAkB,WAAW,KAAK;AAAA,cAChC,UAAU,OAAO;AAAA,cACjB,UAAU,OAAO;AAAA,cACjB,aAAa,OAAO;AAAA,cACpB,UAAU,OAAO;AAAA,YACnB,CAAC;AAAA,UACH;AACA,cAAI,GAAG,GAAG;AACV,cAAI,eAAe,KAAK,sBAAsB,GAAG;AAC/C,wBAAY,kBAAkB,QAAQ,kBAAkB;AACxD,8BAAkB,YAAY;AAC9B,uBAAW,IAAI,WAAW,SAAS;AACnC,qBAAS,IAAI,UAAU,CAAC,CAAC;AACzB,iBAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,yBAAW,UAAU,CAAC;AACtB,mBAAK,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,yBAAS,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC;AAAA,cACxC;AAAA,YACF;AACA,8BAAkB,WAAW;AAAA,UAC/B;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAG9C,eAAO,CAAC,GAAG,WAAW;AAAE,iBAAOA;AAAA,QAAM,CAAC;AAAA,MACxC,WACS,OAAO,WAAW,eAAe,OAAO,SAAS;AAGxD,eAAO,UAAUA;AAAA,MACnB,OACK;AAEH,aAAK,OAAOA;AAAA,MACd;AAAA,IAEF,GAAG;AAAA;AAAA;;;AClxEH,kBAAiB;;;ACQjB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,IAAMC,gBAAgB;EAErBC,KAAK;IAEJC,iCAAiC,SAAWC,OAAa;AAExDJ,aAAO,IAAIK,WAAYN,SAASO,QAAQC,OAAOC,MAAM;IAEtD;EAEA;;IAOWC,oBAAW;EAEvBX,OAAI;AAEH,QAAKA,KAAO,QAAOA;AAEnB,QAAK,OAAOY,UAAU,aAAc;AAInCZ,aAAOY,MAAO,kCAAkCC,IAAI,EAClDC,KAAQC,cAAcA,SAASC,YAAW,CAAE,EAC5CF,KAAQE,iBAAiBC,YAAYC,YAAaF,aAAab,aAAa,CAAE,EAC9EW,KAAM,KAAKK,KAAK;IAElB,OAAM;AAINnB,aAAOiB,YACLC,YAAaE,OAAOC,KAAMR,MAAM,QAAQ,GAAIV,aAAa,EACzDW,KAAM,KAAKK,KAAK;IAElB;AAED,WAAOnB;EAER;EAEAmB,MAAQG,QAAiD;AAExDrB,eAAWqB,OAAOrB;AAElBE,kBAAcC,IAAIC,gCAAiC,CAAC;EAErD;EAEAkB,OAASC,OAAmBC,mBAAmB,GAAC;AAE/C,QAAK,CAAExB,SAAW,OAAM,IAAIyB,MAAO,6CAA6C;AAGhF,UAAMC,iBAAiBH,MAAMI;AAC7B,UAAMC,gBAAgB5B,SAASO,QAAQsB,OAAQH,cAAc;AAC7DzB,SAAK6B,IAAKP,OAAOK,aAAa;AAG9BJ,uBAAmBA,oBAAoBO,OAAQ/B,SAASO,QAAQyB,0BAA2BJ,eAAeF,cAAc,CAAE;AAC1H,UAAMO,kBAAkBjC,SAASO,QAAQsB,OAAQL,gBAAgB;AACjE,UAAMU,aAAalC,SAASO,QAAQ4B,gBAAiBF,iBAAiBT,kBAAkBI,eAAeF,cAAc;AAGrH,UAAMU,MAAMnC,KAAKoC,MAAOJ,iBAAiBA,kBAAkBC,UAAU;AACrElC,aAASO,QAAQ+B,KAAMV,aAAa;AACpC5B,aAASO,QAAQ+B,KAAML,eAAe;AAEtC,WAAOG;EAER;AAEA;AAmCD,IAAMxB,OAAO;;;ADvHN,IAAM,OAAO,IAAI,YAAY;AAEpC,IAAqB,cAArB,cAAyC,YAAY;AAAA,EACnD,YAAY,eAAe;AACzB,UAAM;AAEN,SAAK,sBAAsB,OAAO,cAAc,wBAAwB,cAAc,cAAc,sBAAsB;AAC1H,SAAK,kBAAkB,OAAO,cAAc,oBAAoB,cAAc,cAAc,kBAAkB;AAE9G,SAAK,iBAAiB,cAAc,eAAe,eAAe,cAAc;AAAA,EAClF;AAAA,EAEA,YAAY,QAAQ;AAClB,YAAQ,KAAK,gBAAgB;AAAA,MAC3B,KAAK,mBAAmB;AACtB;AAAA,MACF,KAAK,mBAAmB;AACtB,iBAAS,UAAQ,IAAI,WAAW,MAAM,CAAC,EAAE;AACzC;AAAA,MACF,KAAK,mBAAmB;AACtB,iBAAS,KAAK,OAAO,IAAI,WAAW,MAAM,CAAC,EAAE;AAC7C;AAAA,MACF;AACE,cAAM,IAAI,MAAM,8DAA8D,KAAK,cAAc,EAAE;AAAA,IACvG;AAEA,UAAM,aAAa,YAAA2B,QAAK,OAAO,QAAQ,EAAE,4BAA4B,KAAK,wBAAwB,EAAE,CAAC;AACrG,UAAM,WAAW,WAAW,OAAO,CAAC;AACpC,WAAO,SAAS;AAAA,EAClB;AACF;", "names": ["Lerc2Decode", "<PERSON><PERSON>", "init", "instance", "heap", "IMPORT_OBJECT", "env", "emscripten_notify_memory_growth", "index", "Uint8Array", "exports", "memory", "buffer", "ZSTDDecoder", "fetch", "wasm", "then", "response", "arrayBuffer", "WebAssembly", "instantiate", "_init", "<PERSON><PERSON><PERSON>", "from", "result", "decode", "array", "uncompressedSize", "Error", "compressedSize", "byteLength", "compressedPtr", "malloc", "set", "Number", "ZSTD_findDecompressedSize", "uncompressedPtr", "actualSize", "ZSTD_decompress", "dec", "slice", "free", "<PERSON><PERSON>"]}