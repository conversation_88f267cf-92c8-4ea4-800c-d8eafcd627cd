"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const history_service_1 = require("./history.service");
const enums_1 = require("../shared/enums");
const decorators_1 = require("../core/decorators");
const passport_1 = require("@nestjs/passport");
const guards_1 = require("../core/guards");
const history_response_dto_1 = require("./history-response.dto");
let HistoryController = class HistoryController {
    constructor(historyService) {
        this.historyService = historyService;
    }
    getCompleteLocationDetailById(id, type) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.historyService.getHistory(id, type);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.APPLICATION_ADMIN),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get history logs.',
        type: [history_response_dto_1.HistoryResponseDto]
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        type: Number,
        description: 'Capability/Location/Entity ID',
        required: true,
    }),
    (0, swagger_1.ApiParam)({
        name: 'type',
        type: 'enum',
        enum: enums_1.HISTORY_ENTITY_TYPE,
        description: 'Type of the history',
        required: true,
    }),
    (0, common_1.Get)(':type/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], HistoryController.prototype, "getCompleteLocationDetailById", null);
HistoryController = __decorate([
    (0, swagger_1.ApiTags)('History APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('history'),
    __metadata("design:paramtypes", [history_service_1.HistoryService])
], HistoryController);
exports.HistoryController = HistoryController;
//# sourceMappingURL=history.controller.js.map