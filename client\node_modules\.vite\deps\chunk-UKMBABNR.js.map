{"version": 3, "sources": ["../../@mui/material/Paper/Paper.js", "../../@mui/material/Paper/paperClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport getOverlayAlpha from \"../styles/getOverlayAlpha.js\";\nimport { getPaperUtilityClass } from \"./paperClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  color: (theme.vars || theme).palette.text.primary,\n  transition: theme.transitions.create('box-shadow'),\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.square,\n    style: {\n      borderRadius: theme.shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      border: `1px solid ${(theme.vars || theme).palette.divider}`\n    }\n  }, {\n    props: {\n      variant: 'elevation'\n    },\n    style: {\n      boxShadow: 'var(--Paper-shadow)',\n      backgroundImage: 'var(--Paper-overlay)'\n    }\n  }]\n})));\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const theme = useTheme();\n  const {\n    className,\n    component = 'div',\n    elevation = 1,\n    square = false,\n    variant = 'elevation',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    elevation,\n    square,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    style: {\n      ...(variant === 'elevation' && {\n        '--Paper-shadow': (theme.vars || theme).shadows[elevation],\n        ...(theme.vars && {\n          '--Paper-overlay': theme.vars.overlays?.[elevation]\n        }),\n        ...(!theme.vars && theme.palette.mode === 'dark' && {\n          '--Paper-overlay': `linear-gradient(${alpha('#fff', getOverlayAlpha(elevation))}, ${alpha('#fff', getOverlayAlpha(elevation))})`\n        })\n      }),\n      ...other.style\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaperUtilityClass(slot) {\n  return generateUtilityClass('MuiPaper', slot);\n}\nconst paperClasses = generateUtilityClasses('MuiPaper', ['root', 'rounded', 'outlined', 'elevation', 'elevation0', 'elevation1', 'elevation2', 'elevation3', 'elevation4', 'elevation5', 'elevation6', 'elevation7', 'elevation8', 'elevation9', 'elevation10', 'elevation11', 'elevation12', 'elevation13', 'elevation14', 'elevation15', 'elevation16', 'elevation17', 'elevation18', 'elevation19', 'elevation20', 'elevation21', 'elevation22', 'elevation23', 'elevation24']);\nexport default paperClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,qBAAqB,MAAM;AACzC,SAAO,qBAAqB,YAAY,IAAI;AAC9C;AACA,IAAM,eAAe,uBAAuB,YAAY,CAAC,QAAQ,WAAW,YAAY,aAAa,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,aAAa,CAAC;AACjd,IAAO,uBAAQ;;;ADQf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,CAAC,UAAU,WAAW,YAAY,eAAe,YAAY,SAAS,EAAE;AAAA,EAClG;AACA,SAAO,eAAe,OAAO,sBAAsB,OAAO;AAC5D;AACA,IAAM,YAAY,eAAO,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,CAAC,WAAW,UAAU,OAAO,SAAS,WAAW,YAAY,eAAe,OAAO,YAAY,WAAW,SAAS,EAAE,CAAC;AAAA,EACzK;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EAC1D,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC1C,YAAY,MAAM,YAAY,OAAO,YAAY;AAAA,EACjD,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,CAAC,WAAW;AAAA,IAClB,OAAO;AAAA,MACL,cAAc,MAAM,MAAM;AAAA,IAC5B;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,QAAQ,cAAc,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC5D;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,WAAW;AAAA,MACX,iBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,QAA2B,iBAAW,SAASA,OAAM,SAAS,KAAK;AAlEzE;AAmEE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM,QAAQ,SAAS;AACvB,QAAM;AAAA,IACJ;AAAA,IACA,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,MAAI,MAAuC;AACzC,QAAI,MAAM,QAAQ,SAAS,MAAM,QAAW;AAC1C,cAAQ,MAAM,CAAC,iDAAiD,SAAS,qCAAqC,yCAAyC,SAAS,iBAAiB,EAAE,KAAK,IAAI,CAAC;AAAA,IAC/L;AAAA,EACF;AACA,aAAoB,mBAAAC,KAAK,WAAW;AAAA,IAClC,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,GAAG;AAAA,IACH,OAAO;AAAA,MACL,GAAI,YAAY,eAAe;AAAA,QAC7B,mBAAmB,MAAM,QAAQ,OAAO,QAAQ,SAAS;AAAA,QACzD,GAAI,MAAM,QAAQ;AAAA,UAChB,oBAAmB,WAAM,KAAK,aAAX,mBAAsB;AAAA,QAC3C;AAAA,QACA,GAAI,CAAC,MAAM,QAAQ,MAAM,QAAQ,SAAS,UAAU;AAAA,UAClD,mBAAmB,mBAAmB,MAAM,QAAQ,gBAAgB,SAAS,CAAC,CAAC,KAAK,MAAM,QAAQ,gBAAgB,SAAS,CAAC,CAAC;AAAA,QAC/H;AAAA,MACF;AAAA,MACA,GAAG,MAAM;AAAA,IACX;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAwC,MAAM,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/E,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,WAAW,eAAe,yBAAiB,WAAS;AAClD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,KAAK,YAAY,YAAY;AAC3C,aAAO,IAAI,MAAM,+BAA+B,SAAS,uBAAuB,OAAO,iFAAiF;AAAA,IAC1K;AACA,WAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,aAAa,UAAU,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AACnI,IAAI;AACJ,IAAO,gBAAQ;", "names": ["Paper", "_jsx", "PropTypes"]}