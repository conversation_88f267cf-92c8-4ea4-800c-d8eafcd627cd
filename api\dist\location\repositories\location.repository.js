"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationRepository = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../shared/repositories");
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
const models_2 = require("../../metadata/models");
const models_3 = require("../../contact-details/models");
const models_4 = require("../../capability/models");
let LocationRepository = class LocationRepository extends repositories_1.BaseRepository {
    constructor() {
        super(models_1.Location);
    }
    getAllLocations(searchTerm) {
        return __awaiter(this, void 0, void 0, function* () {
            const trimmed = searchTerm.trim().toLowerCase().replace(/'/g, "''");
            const whereClause = trimmed
                ? {
                    [sequelize_1.Op.or]: [
                        { locationName: { [sequelize_1.Op.iLike]: `%${trimmed}%` } },
                        (0, sequelize_1.literal)(`LOWER(country_detail->>'entityTitle') LIKE '%${trimmed}%'`),
                        (0, sequelize_1.literal)(`LOWER(country_detail->>'entityCode') LIKE '%${trimmed}%'`)
                    ],
                }
                : {};
            return this.findAll({
                where: whereClause,
                attributes: ['id', 'locationName', 'entityTitle', 'status', 'statusDate', 'leaseOwnershipStatus', 'countryDetail'],
                include: [
                    {
                        model: models_2.LocationType,
                        as: 'locationType',
                        attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CoreSolution,
                        as: 'coreSolution',
                        attributes: ['id', 'title', 'code', 'pillar'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                ],
                order: [['locationName', 'DESC']]
            });
        });
    }
    getLocationNameById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: { id },
                attributes: ['id', 'locationName']
            });
        });
    }
    getLocationById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: { id },
                include: [
                    {
                        model: models_2.LocationType,
                        as: 'locationType',
                        attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CoreSolution,
                        as: 'coreSolution',
                        attributes: ['id', 'title', 'code', 'pillar'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                ]
            });
        });
    }
    getLocationDetailForUpdate(locationId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: { id: locationId },
                include: [
                    {
                        model: models_1.LocationIndustryVertical,
                        as: 'locationIndustryVerticals',
                        attributes: ['id', 'industryVerticalCode', 'expertUsers'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        }
                    },
                    {
                        model: models_3.ContactDetail,
                        as: 'contacts',
                        attributes: ['id', 'objectId', 'objectType', 'userDetails'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        }
                    },
                ]
            });
        });
    }
    getCompleteLocationDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: { id },
                include: [
                    {
                        model: models_2.LocationType,
                        as: 'locationType',
                        attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CoreSolution,
                        as: 'coreSolution',
                        attributes: ['id', 'title', 'code', 'pillar'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_1.LegalEntity,
                        as: 'legalEntity',
                        attributes: ['id', 'name'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CommonDropdown,
                        as: 'branchArchetype',
                        attributes: ['id', 'title', 'code'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CommonDropdown,
                        as: 'strategicClassification',
                        attributes: ['id', 'title', 'code'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                ]
            });
        });
    }
    isLocationExit(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.isRecordExist({ where: { id } });
        });
    }
    getLocationDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: { id },
                attributes: ['id', 'locationName', 'entityId', 'entityTitle', 'coreSolutionId', 'locationTypeId', 'status'],
            });
        });
    }
    isLocationNameExit(entityId, locationName, id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.isRecordExist({
                where: Object.assign({ entityId,
                    locationName }, (id && { id: { [sequelize_1.Op.ne]: id } }))
            });
        });
    }
    createLocation(payload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const location = new models_1.Location(payload);
            return this.save(location, currentContext);
        });
    }
    updateLocationDetailById(locationId, data, currentContext) {
        return this.update(data, currentContext, {
            where: {
                id: locationId
            },
        });
    }
    getLocationsByFilter(filter, page, limit, orderBy, orderDirection) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const whereClause = this.buildWhereClause(filter);
            console.log(this.getOrderByColumn(orderBy));
            console.log(this.getOrderByDirection(orderDirection));
            return this.findAndCountAll(Object.assign(Object.assign(Object.assign({ where: whereClause, include: [
                    {
                        model: models_2.LocationType,
                        as: 'locationType',
                        attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CoreSolution,
                        as: 'coreSolution',
                        attributes: ['id', 'title', 'code', 'pillar'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CommonDropdown,
                        as: 'branchArchetype',
                        attributes: ['id', 'title', 'code'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CommonDropdown,
                        as: 'strategicClassification',
                        attributes: ['id', 'title', 'code'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_1.LocationIndustryVertical,
                        as: 'locationIndustryVerticals',
                        attributes: ['id', 'expertUsers'],
                        required: !!((_a = filter.verticalCodes) === null || _a === void 0 ? void 0 : _a.length),
                        where: ((_b = filter === null || filter === void 0 ? void 0 : filter.verticalCodes) === null || _b === void 0 ? void 0 : _b.length) ? {
                            active: true,
                            deleted: false,
                            industryVerticalCode: {
                                [sequelize_1.Op.in]: filter.verticalCodes,
                            },
                        } : {
                            active: true,
                            deleted: false,
                        },
                        include: [
                            {
                                model: models_2.CommonDropdown,
                                as: 'industryVertical',
                                attributes: ['id', 'title', 'code'],
                                required: false,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ]
                    },
                ] }, (page && { offset: (page - 1) * limit })), (limit && { limit })), { order: [[
                        this.getOrderByColumn(orderBy),
                        this.getOrderByDirection(orderDirection)
                    ]], logging: console.log, distinct: true }));
        });
    }
    getOrderByColumn(orderBy) {
        switch (orderBy) {
            case 'locationName':
                return 'locationName';
            case 'locationStatus':
                return (0, sequelize_1.literal)(`"Location"."status"::text`);
            case 'locationType':
                return (0, sequelize_1.literal)(`"locationType.title"`);
            case 'coreSolution':
                return (0, sequelize_1.literal)(`"coreSolution.title"`);
            default:
                return 'updatedOn';
        }
    }
    getOrderByDirection(orderByDirection) {
        switch (orderByDirection) {
            case 'asc':
            case 'ASC':
                return 'ASC';
            case 'desc':
            case 'DESC':
                return 'DESC';
            default:
                return 'DESC';
        }
    }
    buildWhereClause(filter) {
        const { entityIds, branchArchetypeCodes, brandIds, coreSolutionIds, leaseOwnershipStatuses, locationTypeIds, statuses, strategicClassifications } = filter;
        const whereClause = {};
        const andConditions = [];
        if (entityIds && entityIds.length) {
            whereClause.entityId = { [sequelize_1.Op.in]: entityIds };
        }
        if (coreSolutionIds && coreSolutionIds.length) {
            whereClause.coreSolutionId = { [sequelize_1.Op.in]: coreSolutionIds };
        }
        if (locationTypeIds && locationTypeIds.length) {
            whereClause.locationTypeId = { [sequelize_1.Op.in]: locationTypeIds };
        }
        if (statuses && statuses.length) {
            whereClause.status = { [sequelize_1.Op.in]: statuses };
        }
        if (leaseOwnershipStatuses && leaseOwnershipStatuses.length) {
            whereClause.leaseOwnershipStatus = { [sequelize_1.Op.in]: leaseOwnershipStatuses };
        }
        if (branchArchetypeCodes && branchArchetypeCodes.length) {
            whereClause.branchArchetypeCode = { [sequelize_1.Op.in]: branchArchetypeCodes };
        }
        if (brandIds === null || brandIds === void 0 ? void 0 : brandIds.length) {
            const brandConditions = brandIds.map(brandId => ({
                brands: { [sequelize_1.Op.contains]: [{ id: brandId }] }
            }));
            andConditions.push({ [sequelize_1.Op.or]: brandConditions });
        }
        if (strategicClassifications && strategicClassifications.length) {
            whereClause.strategicClassificationCode = { [sequelize_1.Op.in]: strategicClassifications };
        }
        if (andConditions.length > 0) {
            if (Object.keys(whereClause).length > 0) {
                andConditions.push(whereClause);
                return { [sequelize_1.Op.and]: andConditions };
            }
            else {
                return { [sequelize_1.Op.and]: andConditions };
            }
        }
        return whereClause;
    }
    deleteLocation(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.deleteByCondition({ id }, currentContext);
        });
    }
    isLegalEntityAddedInLocation(legalEntityId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.isRecordExist({ where: { legalEntityId } });
        });
    }
    getLocationsToExport(filter) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const whereClause = this.buildWhereClause(filter);
            return this.findAll({
                where: whereClause,
                include: [
                    {
                        model: models_2.LocationType,
                        as: 'locationType',
                        attributes: ['title'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CoreSolution,
                        as: 'coreSolution',
                        attributes: ['title'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_1.LegalEntity,
                        as: 'legalEntity',
                        attributes: ['name'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CommonDropdown,
                        as: 'branchArchetype',
                        attributes: ['title'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.CommonDropdown,
                        as: 'strategicClassification',
                        attributes: ['title'],
                        required: false,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_1.LocationIndustryVertical,
                        as: 'locationIndustryVerticals',
                        attributes: ['id'],
                        required: !!((_a = filter.verticalCodes) === null || _a === void 0 ? void 0 : _a.length),
                        where: ((_b = filter === null || filter === void 0 ? void 0 : filter.verticalCodes) === null || _b === void 0 ? void 0 : _b.length) ? {
                            active: true,
                            deleted: false,
                            industryVerticalCode: {
                                [sequelize_1.Op.in]: filter.verticalCodes,
                            },
                        } : {
                            active: true,
                            deleted: false,
                        },
                        include: [
                            {
                                model: models_2.CommonDropdown,
                                as: 'industryVertical',
                                attributes: ['title'],
                                required: false,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ]
                    },
                ],
                order: [['updatedOn', 'DESC']],
                distinct: true,
            });
        });
    }
    getLocationsByCondition(condition) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findAll({
                attributes: ['id', 'locationName', 'entityId', 'status'],
                where: condition,
                include: [
                    {
                        model: models_2.LocationType,
                        as: 'locationType',
                        attributes: ['title'],
                        required: true,
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                ],
            });
        });
    }
    getLocationsIdsByEntityIds(entityIds) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findAll({
                attributes: ['id', 'locationName', 'entityId'],
                where: {
                    entityId: { [sequelize_1.Op.in]: entityIds },
                }
            });
        });
    }
    getAllLocationsWithCapabilities(capabilityIds, filter) {
        var _a, _b, _c, _d, _e;
        return __awaiter(this, void 0, void 0, function* () {
            return this.findAll({
                attributes: ['id', 'locationName', 'entityId', 'status'],
                include: [
                    {
                        model: models_4.LocationWiseCapabilityDetail,
                        as: 'capabilities',
                        attributes: ['id', 'capabilityId', 'status', 'legs'],
                        required: false,
                        where: Object.assign({ active: true, deleted: false }, (((_a = filter === null || filter === void 0 ? void 0 : filter.statuses) === null || _a === void 0 ? void 0 : _a.length) ? { status: { [sequelize_1.Op.in]: filter.statuses } } : {})),
                        include: [
                            {
                                model: models_4.MasterCapability,
                                as: 'capabilityDetail',
                                attributes: ['id', 'capability', 'capabilityType', 'legs'],
                                where: Object.assign(Object.assign(Object.assign(Object.assign({ id: { [sequelize_1.Op.in]: capabilityIds }, active: true, deleted: false }, (((_b = filter === null || filter === void 0 ? void 0 : filter.capabilityTypes) === null || _b === void 0 ? void 0 : _b.length) && { capabilityType: { [sequelize_1.Op.in]: filter.capabilityTypes } })), (((_c = filter === null || filter === void 0 ? void 0 : filter.capabilityLevel) === null || _c === void 0 ? void 0 : _c.length) && { level: { [sequelize_1.Op.in]: filter.capabilityLevel } })), (((_d = filter === null || filter === void 0 ? void 0 : filter.coreSolutionIds) === null || _d === void 0 ? void 0 : _d.length) && { coreSolutionId: { [sequelize_1.Op.in]: filter.coreSolutionIds } })), (((_e = filter === null || filter === void 0 ? void 0 : filter.verticalCodes) === null || _e === void 0 ? void 0 : _e.length) && {
                                    [sequelize_1.Op.and]: [
                                        {
                                            [sequelize_1.Op.or]: filter === null || filter === void 0 ? void 0 : filter.verticalCodes.map(code => (0, sequelize_1.where)((0, sequelize_1.col)('verticals'), sequelize_1.Op.contains, JSON.stringify([code]))),
                                        },
                                        {
                                            verticals: {
                                                [sequelize_1.Op.not]: null,
                                            },
                                        },
                                    ],
                                })),
                                required: true,
                                include: [
                                    {
                                        model: models_4.CapabilityCategory,
                                        as: 'category',
                                        attributes: ['title'],
                                        required: true,
                                        where: {
                                            active: true,
                                            deleted: false,
                                        }
                                    },
                                ],
                            },
                        ],
                    },
                ],
                order: [['locationName', 'ASC']],
            });
        });
    }
};
LocationRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], LocationRepository);
exports.LocationRepository = LocationRepository;
//# sourceMappingURL=location.repository.js.map