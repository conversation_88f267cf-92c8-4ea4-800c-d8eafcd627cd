{"database": {"host": "psql-hoapps-nonprod.postgres.database.azure.com", "port": 5432, "username": "goeuser", "password": "Dubai2020", "db": "appsgoe", "dialect": "postgres", "schema": "dev_cdb", "enableSSL": true}, "azureAD": {"authority": "https://login.microsoftonline.com", "tennantId": "2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "1adb1c92-7181-447b-ae47-d5d5bbab1b65", "clientSecret": "****************************************", "version": "v2.0", "discovery": ".well-known/openid-configuration", "audience": "api://1adb1c92-7181-447b-ae47-d5d5bbab1b65", "graphApiUrl": "https://graph.microsoft.com", "graphApiVersion": "v1.0", "scope": ["General"]}, "swagger": {"user": "user", "password": "password"}, "microservices": {"adminApi": {"url": "https://honodedev.dpworld.com/adminapi/api", "tennantId": "56b27696-13a4-4b01-82ac-0001aeace8b6", "appId": "75427ba1-ff2b-11eb-9ba4-000000000005"}, "requestApi": {"url": "https://honodedev.dpworld.com/requestapi/api", "tennantId": "56b27696-13a4-4b01-82ac-0001aeace8b6", "appId": "75427ba1-ff2b-11eb-9ba4-000000000005"}, "notificationApi": {"url": "https://honodedev.dpworld.com/notification/api", "tennantId": "56b27696-13a4-4b01-82ac-0001aeace8b6", "appId": "75427ba1-ff2b-11eb-9ba4-000000000005"}}, "uiClient": {"baseUrl": "http://localhost:4200/CapMApp"}, "webClientConfig": {"apiBaseUrl": "http://localhost:5100/api", "msDetail": {"authority": "https://login.microsoftonline.com/2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "e602d1a3-8381-4200-9e11-aedeeeac5b9e", "redirectUrl": "http://localhost:4200", "scope": ["api://1adb1c92-7181-447b-ae47-d5d5bbab1b65/General", "email", "openid"], "graphUrl": "https://graph.microsoft.com/v1.0/users", "graphScope": ["User.Read.All"]}}, "logLevel": ["error", "info"], "thresholdDaysForReminder": 3}