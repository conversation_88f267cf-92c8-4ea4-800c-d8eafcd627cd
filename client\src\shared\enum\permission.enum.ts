export enum PERMISSIONS {
  APPLICATION_ADMIN = 'CDB.Admin', // -- Need to remove
  G<PERSON><PERSON><PERSON><PERSON>_MANAGE = 'CDB.Manage',
  GLOBAL_LEGAL = 'CDB.Legal',
  GLOBAL_MANAGE_CAPABILITY = 'CDB.ManageCapability',
  LOCAL_MANAGE_LOCATION = 'Local.ManageLocation',
  LOCAL_MANAGE_CAPABILITY = 'Local.ManageCapability',
}

export enum USER_TYPE {
  GEO_CONTACT = 'GEO_CONTACT',
  INDUSTRY_EXPERT = 'INDUSTRY_EXPERT',
  SUSTAINABILITY_EXPERT = 'SUSTAINABILITY_EXPERT',
  LOCATION_CONTACT = 'LOCATION_CONTACT',
  USER_PERMISSION = 'USER_PERMISSION',
  CAPABILITY_OWNER = 'CAPABILITY_OWNER',
  CAPABILITY_COOWNER = 'CAPABILITY_COOWNER',
}

export enum USER_TYPE_DISPLAY {
  GEO_CONTACT = 'Geographical Contact Person',
  INDUSTRY_EXPERT = 'Industry Expert',
  SUSTAINABILITY_EXPERT = 'Sustainability Expert',
  LOCATION_CONTACT = 'Location Contact Person',
  USER_PERMISSION = 'User Access',
  CAPABILITY_OWNER = 'Entry Owner',
  CAPABILITY_COOWNER = 'Entry Co-Owner',
}
