import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Checkbox,
  Chip,
  Drawer,
  FormControl,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  Stack,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';

import { normalizeKey } from '@/shared/utils/trim-text.util';
import { useCallback, useEffect, useState } from 'react';

import LoadingButton from '@mui/lab/LoadingButton';
import { isEmpty } from 'lodash';

import { BusinessEntityTreeViewMulti } from '@/components/business-entity-treeview';
import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';

import AdUserSearchInput from '@/components/ad-user-search-input/ad-user-search-input';
import { FIELD_KEY_ENUM, LOCATION_ENTITY_TYPE } from '../../enums/capability.enum';

import { useTranslate } from '@/locales/use-locales';
import { CustomCapabilityTableFilterDrawerProps, KeyValue } from '../../../../shared/models/capability.model';

import CircularLoader from '@/components/loading-screen/circular-loader';
import { useCapabilityData } from '../../services/useCapability';

import {
  CAPABILITY_LEVEL_ENUM,
  CAPABILITY_LEVEL_ENUM_DISPLAY,
  CAPABILITY_STATUS_ENUM,
  CAPABILITY_STATUS_ENUM_DISPLAY,
  CAPABILITY_TYPE_ENUM,
  CAPABILITY_TYPE_ENUM_DISPLAY,
  PROVIDER_ENUM,
  PROVIDER_ENUM_DISPLAY,
} from '@/shared/enum/capability.enum';

/**
 * CustomCapabilityTableFilterDrawer Component
 *
 * A filter drawer component for capability tables with support for various filter types including:
 * - Multi-select dropdowns with search
 * - Location tree view
 * - AD user search
 * - Text input
 *
 * @param {CustomCapabilityTableFilterDrawerProps} props - Component props
 * @returns {JSX.Element} The filter drawer component
 */
const CustomCapabilityTableFilterDrawer: React.FC<CustomCapabilityTableFilterDrawerProps> = ({
  open,
  onClose,
  filters,
  onFilterChange,
  onSearch,
  onCancel,
  tableHeaders,
  tableData,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { t } = useTranslate();
  const Button = LoadingButton;

  const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>({});
  const [searchTexts, setSearchTexts] = useState<Record<string, string>>({});
  const [selectedNodes, setSelectedNodes] = useState<TreeViewDataType[]>([]);
  const selectedLoginId =
    filters && Object.keys(filters).length > 0 && Array.isArray(filters.owner) && filters.owner.length > 0
      ? filters.owner[0].id
      : '';
  const {
    businessUnitData,
    isFilterFetchingLocationData,
    coresolutions,
    isFetchingCoreSolutions,
    industryVerticals,
    isFetchingVerticals,
    isFetchingCapabilities,
    capabilityList,
  } = useCapabilityData(open);

  const getUniqueValues = useCallback(
    (key: string): KeyValue[] => {
      switch (key) {
        case FIELD_KEY_ENUM.CORE_SOLUTION:
          return coresolutions?.map((item) => ({ id: item.id.toString(), label: item.title })) || [];
        case FIELD_KEY_ENUM.VERTICALS:
          return industryVerticals?.map((item) => ({ id: item.code as string, label: item.title })) || [];
        case FIELD_KEY_ENUM.CAPABILITY_LEVEL:
          return Object.entries(CAPABILITY_LEVEL_ENUM).map(([key, value]) => ({
            id: key,
            label: CAPABILITY_LEVEL_ENUM_DISPLAY[value],
          }));
        case FIELD_KEY_ENUM.CAPABILITY_TYPE:
          return Object.entries(CAPABILITY_TYPE_ENUM).map(([key, value]) => ({
            id: key,
            label: CAPABILITY_TYPE_ENUM_DISPLAY[value],
          }));
        case FIELD_KEY_ENUM.CAPABILITY_STATUS:
          return Object.entries(CAPABILITY_STATUS_ENUM).map(([key, value]) => ({
            id: key,
            label: CAPABILITY_STATUS_ENUM_DISPLAY[value],
          }));
        case FIELD_KEY_ENUM.PROVIDER:
          return Object.entries(PROVIDER_ENUM).map(([key, value]) => ({
            id: key,
            label: PROVIDER_ENUM_DISPLAY[value],
          }));
        case FIELD_KEY_ENUM.CAPABILITY_NAME:
          return capabilityList?.map((item: any) => ({
            id: item.id,
            label: `${item.capability} - ${item.product}`,
          }));
        default:
          return Array.from(new Set(tableData?.map((item) => item[key]?.toString()).filter(Boolean))).map((val) => ({
            id: val,
            label: val,
          }));
      }
    },
    [tableData, industryVerticals, coresolutions, capabilityList],
  );

  const handleMultiSelectChange = (key: string, options: KeyValue[]) => (event: SelectChangeEvent<string[]>) => {
    const selectedIds = event.target.value;
    const selected = options.filter((opt) => selectedIds.includes(opt.id));
    onFilterChange(key, selected);
  };

  const handleSearchTextChange = (fieldKey: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTexts((prev) => ({
      ...prev,
      [fieldKey]: e.target.value,
    }));
  };

  useEffect(() => {
    if (!filters || Object.keys(filters).length === 0) {
      setSelectedNodes([]);
      return;
    }

    const keysToRemove = ['group', 'country', 'area', 'region', 'cluster'];
    const hasAnyLocationKey = keysToRemove.some((key) => filters[key]?.length > 0);

    if (hasAnyLocationKey && filters.hierarchyEntityId?.length > 0) {
      onFilterChange('hierarchyEntityId', []);
      return; // exit early since we're clearing hierarchy-based selection
    }
    if (filters.hierarchyEntityId && filters.hierarchyEntityId?.length > 0) {
      // Clear selected nodes if hierarchyEntityId is present
      setSelectedNodes([]);
      return;
    }

    const allSelected: TreeViewDataType[] = [];

    const collectMatchingNodes = (node: TreeViewDataType) => {
      const nodeType = normalizeKey(node.entityType, tableData);
      const nodeId = node.id;

      if (filters[nodeType]?.some((f) => f.id === nodeId)) {
        allSelected.push(node);
      }

      node.children?.forEach(collectMatchingNodes);
    };

    if (businessUnitData) {
      collectMatchingNodes(businessUnitData);
    }

    setSelectedNodes(allSelected);
  }, [filters, businessUnitData]);

  const handleLocationTree = useCallback(
    (nodes: TreeViewDataType[]) => {
      setSelectedNodes(nodes);

      // Reset all location entity types to empty first
      Object.values(LOCATION_ENTITY_TYPE).forEach((entityType) => {
        onFilterChange(entityType, []);
      });

      // Then apply the new selection
      const filterMap: Record<string, KeyValue[]> = {};

      nodes.forEach((node) => {
        const entityType = normalizeKey(node.entityType, tableData);
        if (!filterMap[entityType]) {
          filterMap[entityType] = [];
        }
        filterMap[entityType].push({ id: node.id, label: node.shortName });
      });

      Object.entries(filterMap).forEach(([entityType, values]) => {
        onFilterChange(entityType, values);
      });
    },
    [onFilterChange, tableData, filters],
  );

  const renderFilterControl = (header: string, fieldKey: string) => {
    const uniqueValues = getUniqueValues(fieldKey);
    const searchText = searchTexts[fieldKey] || '';
    const filteredValues = uniqueValues.filter((val) => val.label.toLowerCase().includes(searchText.toLowerCase()));

    console.log('uniqueValues', uniqueValues);
    switch (fieldKey) {
      case LOCATION_ENTITY_TYPE.REGION:
        return (
          <Box mt={1} mb={1}>
            <BusinessEntityTreeViewMulti
              size="medium"
              key={fieldKey}
              onNodeSelection={handleLocationTree}
              defaultValue={selectedNodes}
              data={isEmpty(businessUnitData) ? [] : [businessUnitData as TreeViewDataType]}
              placeholder={t('placeholder.location')}
            />
          </Box>
        );
      case LOCATION_ENTITY_TYPE.CLUSTER:
        return null;
      case LOCATION_ENTITY_TYPE.COUNTRY:
        return null;

      case FIELD_KEY_ENUM.OWNER:
        return (
          <Box key={fieldKey} mt={1} padding={0}>
            <AdUserSearchInput
              initialUserId={selectedLoginId}
              size="medium"
              placeholder={t('placeholder.owner')}
              onSelect={(user) => {
                const selected = user ? [{ id: (user as any).loginId, label: (user as any).loginId }] : [];
                onFilterChange(FIELD_KEY_ENUM.OWNER, selected);
              }}
            />
          </Box>
        );
      case FIELD_KEY_ENUM.CAPABILITY:
        return null;
      // case FIELD_KEY_ENUM.CAPABILITY_NAME:
      //   return (
      //     <TextField
      //       key={fieldKey}
      //       fullWidth
      //       placeholder={t('placeholder.search_by_capability_name')}
      //       label={header}
      //       value={filters[fieldKey]?.[0]?.label || ''}
      //       onChange={(e) => onFilterChange(fieldKey, [{ id: e.target.value, label: e.target.value }])}
      //       margin="normal"
      //       size="medium"
      //     />
      //   );

      default:
        return (
          <FormControl key={fieldKey} fullWidth size="medium" margin="dense">
            <InputLabel>{header}</InputLabel>
            <Select
              multiple
              value={(filters[fieldKey] || []).map((val) => val.id)}
              onChange={handleMultiSelectChange(fieldKey, uniqueValues)}
              input={<OutlinedInput label={header} />}
              open={openDropdowns[fieldKey] ?? false}
              onOpen={() => setOpenDropdowns((prev) => ({ ...prev, [fieldKey]: true }))}
              onClose={() => setOpenDropdowns((prev) => ({ ...prev, [fieldKey]: false }))}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((id) => {
                    const found = uniqueValues.find((opt) => opt.id === id);
                    return found ? (
                      <Chip
                        sx={{ fontSize: 12, borderRadius: '5px', border: 1 }}
                        size="small"
                        key={id}
                        label={found.label}
                      />
                    ) : null;
                  })}
                </Box>
              )}
              MenuProps={{ PaperProps: { style: { maxHeight: 300 } } }}
            >
              <MenuItem
                disableRipple
                disableTouchRipple
                sx={{
                  pointerEvents: 'auto',
                  backgroundColor: 'transparent',
                  '&.Mui-focusVisible': { backgroundColor: 'transparent' },
                  '&:focus': { backgroundColor: 'transparent' },
                }}
              >
                <TextField
                  size="small"
                  placeholder={t('placeholder.search')}
                  fullWidth
                  value={searchText}
                  onChange={handleSearchTextChange(fieldKey)}
                  onClick={(e) => e.stopPropagation()}
                  onKeyDown={(e) => e.stopPropagation()}
                  autoFocus
                />
              </MenuItem>
              {filteredValues.map((option) => (
                <MenuItem key={option.id} value={option.id}>
                  <Checkbox checked={(filters[fieldKey] || []).some((val) => val.id === option.id)} />
                  <ListItemText primary={option.label} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
    }
  };
  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      hideBackdrop
      slotProps={{
        paper: {
          sx: {
            width: isMobile ? '100%' : 480,
            p: 3,
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
          },
        },
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="mainTitle">{t('headings.filters')}</Typography>
        <IconButton onClick={onClose} size="small" aria-label={t('aria.close_filter')}>
          <CloseIcon />
        </IconButton>
      </Box>

      {isFilterFetchingLocationData || isFetchingCoreSolutions || isFetchingVerticals ? (
        <Box display={'flex'} justifyContent={'center'} alignItems={'center'} height={'100%'}>
          <CircularLoader />
        </Box>
      ) : (
        <>
          <Box sx={{ flexGrow: 1, overflowY: 'auto' }}>
            {tableHeaders?.map((header) => {
              const fieldKey = header?.toString().toLowerCase().replace(/\s/g, '_');

              console.log('------- header, fieldKey ---------');
              console.log(header, fieldKey);

              return <Box key={`filter-control-${fieldKey}`}>{renderFilterControl(header, fieldKey)}</Box>;
            })}
          </Box>

          <Stack direction="row" justifyContent="end" mt={2} spacing={1}>
            <Button
              onClick={() => {
                onCancel();
                setSelectedNodes([]);
              }}
              variant="outlined"
              fullWidth={isMobile}
            >
              {t('btn_name.reset')}
            </Button>
            <Button onClick={onSearch} type="button" variant="contained" fullWidth={isMobile}>
              {t('btn_name.search')}
            </Button>
          </Stack>
        </>
      )}
    </Drawer>
  );
};

export default CustomCapabilityTableFilterDrawer;
