import { COMMON_DROPDOWN_TYPE } from 'src/shared/enums';
import { METADATA_TYPE_ENUM } from 'src/shared/enums/metadata.enum';
declare const combinedEnum: {
    readonly TIMEZONE: COMMON_DROPDOWN_TYPE.TIMEZONE;
    readonly CURRENCY: COMMON_DROPDOWN_TYPE.CURRENCY;
    readonly LANGUAGE: COMMON_DROPDOWN_TYPE.LANGUAGE;
    readonly INDUSTRY_VERTICALS: COMMON_DROPDOWN_TYPE.INDUSTRY_VERTICALS;
    readonly SUSTAINABILITY: COMMON_DROPDOWN_TYPE.SUSTAINABILITY;
    readonly STRATEGIC_CLASSIFICATION: COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION;
    readonly ALL_STRATEGIC_CLASSIFICATION: COMMON_DROPDOWN_TYPE.ALL_STRATEGIC_CLASSIFICATION;
    readonly CONTACT_TYPE: COMMON_DROPDOWN_TYPE.CONTACT_TYPE;
    readonly FF_LOCATION_ID_TYPES: COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES;
    readonly BRANDS: COMMON_DROPDOWN_TYPE.BRANDS;
    readonly BRANCH_ARCHETYPE: COMMON_DROPDOWN_TYPE.BRANCH_ARCHETYPE;
    readonly CATEGORY: METADATA_TYPE_ENUM.CATEGORY;
    readonly PRODUCT: METADATA_TYPE_ENUM.PRODUCT;
    readonly PRODUCT_FAMILY: METADATA_TYPE_ENUM.PRODUCT_FAMILY;
};
export declare class SetupNewMetadataRequestDto {
    name: string;
    legIds?: number[];
    type: typeof combinedEnum[keyof typeof combinedEnum];
    code?: string;
    coreSolutionId?: number;
    locationTypeId?: number;
    offset?: string;
}
export {};
