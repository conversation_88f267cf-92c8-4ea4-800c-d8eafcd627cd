{"version": 3, "sources": ["../../@mui/material/Popover/Popover.js", "../../@mui/material/Popover/popoverClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport refType from '@mui/utils/refType';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport Grow from \"../Grow/index.js\";\nimport Modal from \"../Modal/index.js\";\nimport PaperBase from \"../Paper/index.js\";\nimport { getPopoverUtilityClass } from \"./popoverClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getOffsetTop(rect, vertical) {\n  let offset = 0;\n  if (typeof vertical === 'number') {\n    offset = vertical;\n  } else if (vertical === 'center') {\n    offset = rect.height / 2;\n  } else if (vertical === 'bottom') {\n    offset = rect.height;\n  }\n  return offset;\n}\nexport function getOffsetLeft(rect, horizontal) {\n  let offset = 0;\n  if (typeof horizontal === 'number') {\n    offset = horizontal;\n  } else if (horizontal === 'center') {\n    offset = rect.width / 2;\n  } else if (horizontal === 'right') {\n    offset = rect.width;\n  }\n  return offset;\n}\nfunction getTransformOriginValue(transformOrigin) {\n  return [transformOrigin.horizontal, transformOrigin.vertical].map(n => typeof n === 'number' ? `${n}px` : n).join(' ');\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    paper: ['paper']\n  };\n  return composeClasses(slots, getPopoverUtilityClass, classes);\n};\nexport const PopoverRoot = styled(Modal, {\n  name: 'MuiPopover',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\nexport const PopoverPaper = styled(PaperBase, {\n  name: 'MuiPopover',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => styles.paper\n})({\n  position: 'absolute',\n  overflowY: 'auto',\n  overflowX: 'hidden',\n  // So we see the popover when it's empty.\n  // It's most likely on issue on userland.\n  minWidth: 16,\n  minHeight: 16,\n  maxWidth: 'calc(100% - 32px)',\n  maxHeight: 'calc(100% - 32px)',\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0\n});\nconst Popover = /*#__PURE__*/React.forwardRef(function Popover(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPopover'\n  });\n  const {\n    action,\n    anchorEl,\n    anchorOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    anchorPosition,\n    anchorReference = 'anchorEl',\n    children,\n    className,\n    container: containerProp,\n    elevation = 8,\n    marginThreshold = 16,\n    open,\n    PaperProps: PaperPropsProp = {},\n    // TODO: remove in v7\n    slots = {},\n    slotProps = {},\n    transformOrigin = {\n      vertical: 'top',\n      horizontal: 'left'\n    },\n    TransitionComponent,\n    // TODO: remove in v7\n    transitionDuration: transitionDurationProp = 'auto',\n    TransitionProps = {},\n    // TODO: remove in v7\n    disableScrollLock = false,\n    ...other\n  } = props;\n  const paperRef = React.useRef();\n  const ownerState = {\n    ...props,\n    anchorOrigin,\n    anchorReference,\n    elevation,\n    marginThreshold,\n    transformOrigin,\n    TransitionComponent,\n    transitionDuration: transitionDurationProp,\n    TransitionProps\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // Returns the top/left offset of the position\n  // to attach to on the anchor element (or body if none is provided)\n  const getAnchorOffset = React.useCallback(() => {\n    if (anchorReference === 'anchorPosition') {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!anchorPosition) {\n          console.error('MUI: You need to provide a `anchorPosition` prop when using ' + '<Popover anchorReference=\"anchorPosition\" />.');\n        }\n      }\n      return anchorPosition;\n    }\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n\n    // If an anchor element wasn't provided, just use the parent body element of this Popover\n    const anchorElement = resolvedAnchorEl && resolvedAnchorEl.nodeType === 1 ? resolvedAnchorEl : ownerDocument(paperRef.current).body;\n    const anchorRect = anchorElement.getBoundingClientRect();\n    if (process.env.NODE_ENV !== 'production') {\n      const box = anchorElement.getBoundingClientRect();\n      if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n        console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n      }\n    }\n    return {\n      top: anchorRect.top + getOffsetTop(anchorRect, anchorOrigin.vertical),\n      left: anchorRect.left + getOffsetLeft(anchorRect, anchorOrigin.horizontal)\n    };\n  }, [anchorEl, anchorOrigin.horizontal, anchorOrigin.vertical, anchorPosition, anchorReference]);\n\n  // Returns the base transform origin using the element\n  const getTransformOrigin = React.useCallback(elemRect => {\n    return {\n      vertical: getOffsetTop(elemRect, transformOrigin.vertical),\n      horizontal: getOffsetLeft(elemRect, transformOrigin.horizontal)\n    };\n  }, [transformOrigin.horizontal, transformOrigin.vertical]);\n  const getPositioningStyle = React.useCallback(element => {\n    const elemRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n\n    // Get the transform origin point on the element itself\n    const elemTransformOrigin = getTransformOrigin(elemRect);\n    if (anchorReference === 'none') {\n      return {\n        top: null,\n        left: null,\n        transformOrigin: getTransformOriginValue(elemTransformOrigin)\n      };\n    }\n\n    // Get the offset of the anchoring element\n    const anchorOffset = getAnchorOffset();\n\n    // Calculate element positioning\n    let top = anchorOffset.top - elemTransformOrigin.vertical;\n    let left = anchorOffset.left - elemTransformOrigin.horizontal;\n    const bottom = top + elemRect.height;\n    const right = left + elemRect.width;\n\n    // Use the parent window of the anchorEl if provided\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n\n    // Window thresholds taking required margin into account\n    const heightThreshold = containerWindow.innerHeight - marginThreshold;\n    const widthThreshold = containerWindow.innerWidth - marginThreshold;\n\n    // Check if the vertical axis needs shifting\n    if (marginThreshold !== null && top < marginThreshold) {\n      const diff = top - marginThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    } else if (marginThreshold !== null && bottom > heightThreshold) {\n      const diff = bottom - heightThreshold;\n      top -= diff;\n      elemTransformOrigin.vertical += diff;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (elemRect.height > heightThreshold && elemRect.height && heightThreshold) {\n        console.error(['MUI: The popover component is too tall.', `Some part of it can not be seen on the screen (${elemRect.height - heightThreshold}px).`, 'Please consider adding a `max-height` to improve the user-experience.'].join('\\n'));\n      }\n    }\n\n    // Check if the horizontal axis needs shifting\n    if (marginThreshold !== null && left < marginThreshold) {\n      const diff = left - marginThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    } else if (right > widthThreshold) {\n      const diff = right - widthThreshold;\n      left -= diff;\n      elemTransformOrigin.horizontal += diff;\n    }\n    return {\n      top: `${Math.round(top)}px`,\n      left: `${Math.round(left)}px`,\n      transformOrigin: getTransformOriginValue(elemTransformOrigin)\n    };\n  }, [anchorEl, anchorReference, getAnchorOffset, getTransformOrigin, marginThreshold]);\n  const [isPositioned, setIsPositioned] = React.useState(open);\n  const setPositioningStyles = React.useCallback(() => {\n    const element = paperRef.current;\n    if (!element) {\n      return;\n    }\n    const positioning = getPositioningStyle(element);\n    if (positioning.top !== null) {\n      element.style.setProperty('top', positioning.top);\n    }\n    if (positioning.left !== null) {\n      element.style.left = positioning.left;\n    }\n    element.style.transformOrigin = positioning.transformOrigin;\n    setIsPositioned(true);\n  }, [getPositioningStyle]);\n  React.useEffect(() => {\n    if (disableScrollLock) {\n      window.addEventListener('scroll', setPositioningStyles);\n    }\n    return () => window.removeEventListener('scroll', setPositioningStyles);\n  }, [anchorEl, disableScrollLock, setPositioningStyles]);\n  const handleEntering = () => {\n    setPositioningStyles();\n  };\n  const handleExited = () => {\n    setIsPositioned(false);\n  };\n  React.useEffect(() => {\n    if (open) {\n      setPositioningStyles();\n    }\n  });\n  React.useImperativeHandle(action, () => open ? {\n    updatePosition: () => {\n      setPositioningStyles();\n    }\n  } : null, [open, setPositioningStyles]);\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n    const handleResize = debounce(() => {\n      setPositioningStyles();\n    });\n    const containerWindow = ownerWindow(resolveAnchorEl(anchorEl));\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [anchorEl, open, setPositioningStyles]);\n  let transitionDuration = transitionDurationProp;\n  const externalForwardedProps = {\n    slots: {\n      transition: TransitionComponent,\n      ...slots\n    },\n    slotProps: {\n      transition: TransitionProps,\n      paper: PaperPropsProp,\n      ...slotProps\n    }\n  };\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Grow,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onEntering: (element, isAppearing) => {\n        handlers.onEntering?.(element, isAppearing);\n        handleEntering();\n      },\n      onExited: element => {\n        handlers.onExited?.(element);\n        handleExited();\n      }\n    }),\n    additionalProps: {\n      appear: true,\n      in: open\n    }\n  });\n  if (transitionDurationProp === 'auto' && !TransitionSlot.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  const container = containerProp || (anchorEl ? ownerDocument(resolveAnchorEl(anchorEl)).body : undefined);\n  const [RootSlot, {\n    slots: rootSlotsProp,\n    slotProps: rootSlotPropsProp,\n    ...rootProps\n  }] = useSlot('root', {\n    ref,\n    elementType: PopoverRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      slots: {\n        backdrop: slots.backdrop\n      },\n      slotProps: {\n        backdrop: mergeSlotProps(typeof slotProps.backdrop === 'function' ? slotProps.backdrop(ownerState) : slotProps.backdrop, {\n          invisible: true\n        })\n      },\n      container,\n      open\n    },\n    ownerState,\n    className: clsx(classes.root, className)\n  });\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    ref: paperRef,\n    className: classes.paper,\n    elementType: PopoverPaper,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    additionalProps: {\n      elevation,\n      style: isPositioned ? undefined : {\n        opacity: 0\n      }\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(RootSlot, {\n    ...rootProps,\n    ...(!isHostComponent(RootSlot) && {\n      slots: rootSlotsProp,\n      slotProps: rootSlotPropsProp,\n      disableScrollLock\n    }),\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      ...transitionSlotProps,\n      timeout: transitionDuration,\n      children: /*#__PURE__*/_jsx(PaperSlot, {\n        ...paperProps,\n        children: children\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Popover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A ref for imperative actions.\n   * It currently only supports updatePosition() action.\n   */\n  action: refType,\n  /**\n   * An HTML element, [PopoverVirtualElement](https://mui.com/material-ui/react-popover/#virtual-element),\n   * or a function that returns either.\n   * It's used to set the position of the popover.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.func]), props => {\n    if (props.open && (!props.anchorReference || props.anchorReference === 'anchorEl')) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', `It should be an Element or PopoverVirtualElement instance but it's \\`${resolvedAnchorEl}\\` instead.`].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * This is the point on the anchor where the popover's\n   * `anchorEl` will attach to. This is not used when the\n   * anchorReference is 'anchorPosition'.\n   *\n   * Options:\n   * vertical: [top, center, bottom];\n   * horizontal: [left, center, right].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * This is the position that may be used to set the position of the popover.\n   * The coordinates are relative to the application's client area.\n   */\n  anchorPosition: PropTypes.shape({\n    left: PropTypes.number.isRequired,\n    top: PropTypes.number.isRequired\n  }),\n  /**\n   * This determines which anchor prop to refer to when setting\n   * the position of the popover.\n   * @default 'anchorEl'\n   */\n  anchorReference: PropTypes.oneOf(['anchorEl', 'anchorPosition', 'none']),\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * An HTML element, component instance, or function that returns either.\n   * The `container` will passed to the Modal component.\n   *\n   * By default, it uses the body of the anchorEl's top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * The elevation of the popover.\n   * @default 8\n   */\n  elevation: integerPropType,\n  /**\n   * Specifies how close to the edge of the window the popover can appear.\n   * If null, the popover will not be constrained by the window.\n   * @default 16\n   */\n  marginThreshold: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Props applied to the [`Paper`](https://mui.com/material-ui/api/paper/) element.\n   *\n   * This prop is an alias for `slotProps.paper` and will be overriden by it if both are used.\n   * @deprecated Use `slotProps.paper` instead.\n   *\n   * @default {}\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * This is the point on the popover which\n   * will attach to the anchor's origin.\n   *\n   * Options:\n   * vertical: [top, center, bottom, x(px)];\n   * horizontal: [left, center, right, x(px)].\n   * @default {\n   *   vertical: 'top',\n   *   horizontal: 'left',\n   * }\n   */\n  transformOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOfType([PropTypes.oneOf(['center', 'left', 'right']), PropTypes.number]).isRequired,\n    vertical: PropTypes.oneOfType([PropTypes.oneOf(['bottom', 'center', 'top']), PropTypes.number]).isRequired\n  }),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @deprecated use the `slots.transition` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Popover;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPopoverUtilityClass(slot) {\n  return generateUtilityClass('MuiPopover', slot);\n}\nconst popoverClasses = generateUtilityClasses('MuiPopover', ['root', 'paper']);\nexport default popoverClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,uBAAuB,MAAM;AAC3C,SAAO,qBAAqB,cAAc,IAAI;AAChD;AACA,IAAM,iBAAiB,uBAAuB,cAAc,CAAC,QAAQ,OAAO,CAAC;AAC7E,IAAO,yBAAQ;;;ADiBf,yBAA4B;AACrB,SAAS,aAAa,MAAM,UAAU;AAC3C,MAAI,SAAS;AACb,MAAI,OAAO,aAAa,UAAU;AAChC,aAAS;AAAA,EACX,WAAW,aAAa,UAAU;AAChC,aAAS,KAAK,SAAS;AAAA,EACzB,WAAW,aAAa,UAAU;AAChC,aAAS,KAAK;AAAA,EAChB;AACA,SAAO;AACT;AACO,SAAS,cAAc,MAAM,YAAY;AAC9C,MAAI,SAAS;AACb,MAAI,OAAO,eAAe,UAAU;AAClC,aAAS;AAAA,EACX,WAAW,eAAe,UAAU;AAClC,aAAS,KAAK,QAAQ;AAAA,EACxB,WAAW,eAAe,SAAS;AACjC,aAAS,KAAK;AAAA,EAChB;AACA,SAAO;AACT;AACA,SAAS,wBAAwB,iBAAiB;AAChD,SAAO,CAAC,gBAAgB,YAAY,gBAAgB,QAAQ,EAAE,IAAI,OAAK,OAAO,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,EAAE,KAAK,GAAG;AACvH;AACA,SAAS,gBAAgB,UAAU;AACjC,SAAO,OAAO,aAAa,aAAa,SAAS,IAAI;AACvD;AACA,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,OAAO,CAAC,OAAO;AAAA,EACjB;AACA,SAAO,eAAe,OAAO,wBAAwB,OAAO;AAC9D;AACO,IAAM,cAAc,eAAO,eAAO;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,CAAC,CAAC;AACE,IAAM,eAAe,eAAO,eAAW;AAAA,EAC5C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,UAAU;AAAA,EACV,WAAW;AAAA,EACX,WAAW;AAAA;AAAA;AAAA,EAGX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA;AAAA,EAEX,SAAS;AACX,CAAC;AACD,IAAM,UAA6B,iBAAW,SAASA,SAAQ,SAAS,KAAK;AAC3E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB;AAAA,IACA,YAAY,iBAAiB,CAAC;AAAA;AAAA,IAE9B,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,kBAAkB;AAAA,MAChB,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA;AAAA;AAAA,IAEA,oBAAoB,yBAAyB;AAAA,IAC7C,kBAAkB,CAAC;AAAA;AAAA,IAEnB,oBAAoB;AAAA,IACpB,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,WAAiB,aAAO;AAC9B,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAI5C,QAAM,kBAAwB,kBAAY,MAAM;AAC9C,QAAI,oBAAoB,kBAAkB;AACxC,UAAI,MAAuC;AACzC,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,MAAM,2GAAgH;AAAA,QAChI;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,mBAAmB,gBAAgB,QAAQ;AAGjD,UAAM,gBAAgB,oBAAoB,iBAAiB,aAAa,IAAI,mBAAmB,sBAAc,SAAS,OAAO,EAAE;AAC/H,UAAM,aAAa,cAAc,sBAAsB;AACvD,QAAI,MAAuC;AACzC,YAAM,MAAM,cAAc,sBAAsB;AAChD,UAAuC,IAAI,QAAQ,KAAK,IAAI,SAAS,KAAK,IAAI,UAAU,KAAK,IAAI,WAAW,GAAG;AAC7G,gBAAQ,KAAK,CAAC,kEAAkE,6DAA6D,iFAAiF,EAAE,KAAK,IAAI,CAAC;AAAA,MAC5O;AAAA,IACF;AACA,WAAO;AAAA,MACL,KAAK,WAAW,MAAM,aAAa,YAAY,aAAa,QAAQ;AAAA,MACpE,MAAM,WAAW,OAAO,cAAc,YAAY,aAAa,UAAU;AAAA,IAC3E;AAAA,EACF,GAAG,CAAC,UAAU,aAAa,YAAY,aAAa,UAAU,gBAAgB,eAAe,CAAC;AAG9F,QAAM,qBAA2B,kBAAY,cAAY;AACvD,WAAO;AAAA,MACL,UAAU,aAAa,UAAU,gBAAgB,QAAQ;AAAA,MACzD,YAAY,cAAc,UAAU,gBAAgB,UAAU;AAAA,IAChE;AAAA,EACF,GAAG,CAAC,gBAAgB,YAAY,gBAAgB,QAAQ,CAAC;AACzD,QAAM,sBAA4B,kBAAY,aAAW;AACvD,UAAM,WAAW;AAAA,MACf,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IAClB;AAGA,UAAM,sBAAsB,mBAAmB,QAAQ;AACvD,QAAI,oBAAoB,QAAQ;AAC9B,aAAO;AAAA,QACL,KAAK;AAAA,QACL,MAAM;AAAA,QACN,iBAAiB,wBAAwB,mBAAmB;AAAA,MAC9D;AAAA,IACF;AAGA,UAAM,eAAe,gBAAgB;AAGrC,QAAI,MAAM,aAAa,MAAM,oBAAoB;AACjD,QAAI,OAAO,aAAa,OAAO,oBAAoB;AACnD,UAAM,SAAS,MAAM,SAAS;AAC9B,UAAM,QAAQ,OAAO,SAAS;AAG9B,UAAM,kBAAkB,oBAAY,gBAAgB,QAAQ,CAAC;AAG7D,UAAM,kBAAkB,gBAAgB,cAAc;AACtD,UAAM,iBAAiB,gBAAgB,aAAa;AAGpD,QAAI,oBAAoB,QAAQ,MAAM,iBAAiB;AACrD,YAAM,OAAO,MAAM;AACnB,aAAO;AACP,0BAAoB,YAAY;AAAA,IAClC,WAAW,oBAAoB,QAAQ,SAAS,iBAAiB;AAC/D,YAAM,OAAO,SAAS;AACtB,aAAO;AACP,0BAAoB,YAAY;AAAA,IAClC;AACA,QAAI,MAAuC;AACzC,UAAI,SAAS,SAAS,mBAAmB,SAAS,UAAU,iBAAiB;AAC3E,gBAAQ,MAAM,CAAC,2CAA2C,kDAAkD,SAAS,SAAS,eAAe,QAAQ,uEAAuE,EAAE,KAAK,IAAI,CAAC;AAAA,MAC1O;AAAA,IACF;AAGA,QAAI,oBAAoB,QAAQ,OAAO,iBAAiB;AACtD,YAAM,OAAO,OAAO;AACpB,cAAQ;AACR,0BAAoB,cAAc;AAAA,IACpC,WAAW,QAAQ,gBAAgB;AACjC,YAAM,OAAO,QAAQ;AACrB,cAAQ;AACR,0BAAoB,cAAc;AAAA,IACpC;AACA,WAAO;AAAA,MACL,KAAK,GAAG,KAAK,MAAM,GAAG,CAAC;AAAA,MACvB,MAAM,GAAG,KAAK,MAAM,IAAI,CAAC;AAAA,MACzB,iBAAiB,wBAAwB,mBAAmB;AAAA,IAC9D;AAAA,EACF,GAAG,CAAC,UAAU,iBAAiB,iBAAiB,oBAAoB,eAAe,CAAC;AACpF,QAAM,CAAC,cAAc,eAAe,IAAU,eAAS,IAAI;AAC3D,QAAM,uBAA6B,kBAAY,MAAM;AACnD,UAAM,UAAU,SAAS;AACzB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,cAAc,oBAAoB,OAAO;AAC/C,QAAI,YAAY,QAAQ,MAAM;AAC5B,cAAQ,MAAM,YAAY,OAAO,YAAY,GAAG;AAAA,IAClD;AACA,QAAI,YAAY,SAAS,MAAM;AAC7B,cAAQ,MAAM,OAAO,YAAY;AAAA,IACnC;AACA,YAAQ,MAAM,kBAAkB,YAAY;AAC5C,oBAAgB,IAAI;AAAA,EACtB,GAAG,CAAC,mBAAmB,CAAC;AACxB,EAAM,gBAAU,MAAM;AACpB,QAAI,mBAAmB;AACrB,aAAO,iBAAiB,UAAU,oBAAoB;AAAA,IACxD;AACA,WAAO,MAAM,OAAO,oBAAoB,UAAU,oBAAoB;AAAA,EACxE,GAAG,CAAC,UAAU,mBAAmB,oBAAoB,CAAC;AACtD,QAAM,iBAAiB,MAAM;AAC3B,yBAAqB;AAAA,EACvB;AACA,QAAM,eAAe,MAAM;AACzB,oBAAgB,KAAK;AAAA,EACvB;AACA,EAAM,gBAAU,MAAM;AACpB,QAAI,MAAM;AACR,2BAAqB;AAAA,IACvB;AAAA,EACF,CAAC;AACD,EAAM,0BAAoB,QAAQ,MAAM,OAAO;AAAA,IAC7C,gBAAgB,MAAM;AACpB,2BAAqB;AAAA,IACvB;AAAA,EACF,IAAI,MAAM,CAAC,MAAM,oBAAoB,CAAC;AACtC,EAAM,gBAAU,MAAM;AACpB,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,UAAM,eAAe,iBAAS,MAAM;AAClC,2BAAqB;AAAA,IACvB,CAAC;AACD,UAAM,kBAAkB,oBAAY,gBAAgB,QAAQ,CAAC;AAC7D,oBAAgB,iBAAiB,UAAU,YAAY;AACvD,WAAO,MAAM;AACX,mBAAa,MAAM;AACnB,sBAAgB,oBAAoB,UAAU,YAAY;AAAA,IAC5D;AAAA,EACF,GAAG,CAAC,UAAU,MAAM,oBAAoB,CAAC;AACzC,MAAI,qBAAqB;AACzB,QAAM,yBAAyB;AAAA,IAC7B,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,GAAG;AAAA,IACL;AAAA,IACA,WAAW;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,CAAC,gBAAgB,mBAAmB,IAAI,QAAQ,cAAc;AAAA,IAClE,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,cAAc,eAAa;AAAA,MACzB,GAAG;AAAA,MACH,YAAY,CAAC,SAAS,gBAAgB;AA/S5C;AAgTQ,uBAAS,eAAT,kCAAsB,SAAS;AAC/B,uBAAe;AAAA,MACjB;AAAA,MACA,UAAU,aAAW;AAnT3B;AAoTQ,uBAAS,aAAT,kCAAoB;AACpB,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf,QAAQ;AAAA,MACR,IAAI;AAAA,IACN;AAAA,EACF,CAAC;AACD,MAAI,2BAA2B,UAAU,CAAC,eAAe,gBAAgB;AACvE,yBAAqB;AAAA,EACvB;AAKA,QAAM,YAAY,kBAAkB,WAAW,sBAAc,gBAAgB,QAAQ,CAAC,EAAE,OAAO;AAC/F,QAAM,CAAC,UAAU;AAAA,IACf,OAAO;AAAA,IACP,WAAW;AAAA,IACX,GAAG;AAAA,EACL,CAAC,IAAI,QAAQ,QAAQ;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,4BAA4B;AAAA,IAC5B,iBAAiB;AAAA,MACf,OAAO;AAAA,QACL,UAAU,MAAM;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU,eAAe,OAAO,UAAU,aAAa,aAAa,UAAU,SAAS,UAAU,IAAI,UAAU,UAAU;AAAA,UACvH,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,EACzC,CAAC;AACD,QAAM,CAAC,WAAW,UAAU,IAAI,QAAQ,SAAS;AAAA,IAC/C,KAAK;AAAA,IACL,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA,4BAA4B;AAAA,IAC5B,iBAAiB;AAAA,MACf;AAAA,MACA,OAAO,eAAe,SAAY;AAAA,QAChC,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,mBAAAC,KAAK,UAAU;AAAA,IACjC,GAAG;AAAA,IACH,GAAI,CAAC,wBAAgB,QAAQ,KAAK;AAAA,MAChC,OAAO;AAAA,MACP,WAAW;AAAA,MACX;AAAA,IACF;AAAA,IACA,cAAuB,mBAAAA,KAAK,gBAAgB;AAAA,MAC1C,GAAG;AAAA,MACH,SAAS;AAAA,MACT,cAAuB,mBAAAA,KAAK,WAAW;AAAA,QACrC,GAAG;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,QAAQ,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjF,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,UAAU,eAAe,kBAAAC,QAAU,UAAU,CAAC,iBAAiB,kBAAAA,QAAU,IAAI,CAAC,GAAG,WAAS;AACxF,QAAI,MAAM,SAAS,CAAC,MAAM,mBAAmB,MAAM,oBAAoB,aAAa;AAClF,YAAM,mBAAmB,gBAAgB,MAAM,QAAQ;AACvD,UAAI,oBAAoB,iBAAiB,aAAa,GAAG;AACvD,cAAM,MAAM,iBAAiB,sBAAsB;AACnD,YAAuC,IAAI,QAAQ,KAAK,IAAI,SAAS,KAAK,IAAI,UAAU,KAAK,IAAI,WAAW,GAAG;AAC7G,iBAAO,IAAI,MAAM,CAAC,kEAAkE,6DAA6D,iFAAiF,EAAE,KAAK,IAAI,CAAC;AAAA,QAChP;AAAA,MACF,OAAO;AACL,eAAO,IAAI,MAAM,CAAC,kEAAkE,wEAAwE,gBAAgB,aAAa,EAAE,KAAK,IAAI,CAAC;AAAA,MACvM;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcD,cAAc,kBAAAA,QAAU,MAAM;AAAA,IAC5B,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,IAClG,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,UAAU,UAAU,KAAK,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EAClG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB,kBAAAA,QAAU,MAAM;AAAA,IAC9B,MAAM,kBAAAA,QAAU,OAAO;AAAA,IACvB,KAAK,kBAAAA,QAAU,OAAO;AAAA,EACxB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,iBAAiB,kBAAAA,QAAU,MAAM,CAAC,YAAY,kBAAkB,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcvE,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIzB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrB,WAAW,kBAAAA,QAAgD,UAAU,CAAC,iBAAiB,kBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtG,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,iBAAiB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,MAAM,kBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,YAAY,kBAAAA,QAAgD,MAAM;AAAA,IAChE,WAAW;AAAA,EACb,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EACpE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,UAAU,kBAAAA,QAAU;AAAA,IACpB,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,IAChB,YAAY,kBAAAA,QAAU;AAAA,EACxB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAatJ,iBAAiB,kBAAAA,QAAU,MAAM;AAAA,IAC/B,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,IAClG,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,UAAU,UAAU,KAAK,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EAClG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,qBAAqB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,oBAAoB,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,IACpG,QAAQ,kBAAAA,QAAU;AAAA,IAClB,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOH,iBAAiB,kBAAAA,QAAU;AAC7B,IAAI;AACJ,IAAO,kBAAQ;", "names": ["Popover", "_jsx", "PropTypes"]}