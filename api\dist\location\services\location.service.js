"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const helpers_1 = require("../../shared/helpers");
const dtos_1 = require("../dtos");
const validators_1 = require("../validators");
const enums_1 = require("../../shared/enums");
const repositories_2 = require("../../contact-details/repositories");
const repositories_3 = require("../../metadata/repositories");
const pagination_1 = require("../../core/pagination");
const services_1 = require("../../shared/services");
const constants_1 = require("../../shared/constants");
const clients_1 = require("../../shared/clients");
const lodash_1 = require("lodash");
const location_wise_capability_repository_1 = require("../../capability/repositories/location-wise-capability.repository");
const location_capabilities_response_dto_1 = require("../dtos/response/location-capabilities.response.dto");
const services_2 = require("../../business-entity/services");
const repositories_4 = require("../../permission/repositories");
const moment_1 = __importDefault(require("moment"));
let LocationService = class LocationService {
    constructor(locationRepository, locationIndustryVerticalRepository, setupNewLocationRequest, databaseHelper, contactDetailRepository, locationLifecycleManagementsTypeRepository, sharedAttachmentService, attachmentApiClient, locationWiseCapabilityRepository, businessEntityService, partnerBranchRepository, excelSheetService, adminApiClient, permissionService, userPermissionRepository, historyService) {
        this.locationRepository = locationRepository;
        this.locationIndustryVerticalRepository = locationIndustryVerticalRepository;
        this.setupNewLocationRequest = setupNewLocationRequest;
        this.databaseHelper = databaseHelper;
        this.contactDetailRepository = contactDetailRepository;
        this.locationLifecycleManagementsTypeRepository = locationLifecycleManagementsTypeRepository;
        this.sharedAttachmentService = sharedAttachmentService;
        this.attachmentApiClient = attachmentApiClient;
        this.locationWiseCapabilityRepository = locationWiseCapabilityRepository;
        this.businessEntityService = businessEntityService;
        this.partnerBranchRepository = partnerBranchRepository;
        this.excelSheetService = excelSheetService;
        this.adminApiClient = adminApiClient;
        this.permissionService = permissionService;
        this.userPermissionRepository = userPermissionRepository;
        this.historyService = historyService;
    }
    getAllLocations(searchTerm) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!searchTerm.trim()) {
                return [];
            }
            const locations = yield this.locationRepository.getAllLocations(searchTerm);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.LocationDropdownResponseDto, locations);
        });
    }
    upsertLocation(createLocationDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const hasLegalPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_LEGAL], currentContext.user, createLocationDto.entityId, null);
            if (!hasLegalPermission) {
                if (createLocationDto.id) {
                    const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, createLocationDto.entityId, createLocationDto.id);
                    if (!hasPermission) {
                        throw new common_1.HttpException('You don\'t have permission to update this location.', common_1.HttpStatus.FORBIDDEN);
                    }
                }
                else {
                    const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE], currentContext.user, createLocationDto.entityId, null);
                    if (!hasPermission) {
                        throw new common_1.HttpException('You don\'t have permission to add new location in this entity.', common_1.HttpStatus.FORBIDDEN);
                    }
                }
            }
            if (!hasLegalPermission && (createLocationDto === null || createLocationDto === void 0 ? void 0 : createLocationDto.agentDetail)) {
                throw new common_1.HttpException('You don\'t have permission to add agent detail.', common_1.HttpStatus.FORBIDDEN);
            }
            const validRequestData = yield this.setupNewLocationRequest.validate(createLocationDto, hasLegalPermission);
            const { status, data, message } = validRequestData;
            if (status) {
                const { id, entityId, basicInformation } = createLocationDto;
                const { locationName } = basicInformation;
                const isLocationNameExist = yield this.locationRepository.isLocationNameExit(entityId, locationName, id || null);
                if (isLocationNameExist) {
                    throw new common_1.HttpException('Location name already exist.', common_1.HttpStatus.CONFLICT);
                }
                const { entityDetail } = data;
                const locationData = yield this.transformLocationData(entityDetail, createLocationDto);
                if (id) {
                    return yield this.updateLocation(locationData, createLocationDto, currentContext);
                }
                else {
                    return yield this.addNewLocation(locationData, createLocationDto, currentContext);
                }
            }
            throw new common_1.HttpException(message, common_1.HttpStatus.NOT_FOUND);
        });
    }
    addNewLocation(locationData, createLocationDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const location = yield this.locationRepository.createLocation(locationData, currentContext);
                const { contactManagements, competencyDetails, agentDetail, additionalDetails } = createLocationDto;
                const { industryVerticals } = competencyDetails || {};
                const { aggrements } = agentDetail || {};
                const { additionalDocuments } = additionalDetails || {};
                if (industryVerticals === null || industryVerticals === void 0 ? void 0 : industryVerticals.length) {
                    yield this.addNewIndutryVertical(industryVerticals, location.id, currentContext);
                }
                if (contactManagements === null || contactManagements === void 0 ? void 0 : contactManagements.length) {
                    yield this.addNewContactManagement(contactManagements, location.id, currentContext);
                }
                yield this.uploadNewLocationDocuments(additionalDocuments, aggrements, location.id, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: location.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: 'New location added.',
                    additional_info: {
                        locationData: createLocationDto
                    }
                });
                return {
                    message: 'New location added successfully. ',
                    data: {
                        locationId: location.id,
                    },
                };
            }));
        });
    }
    updateLocation(locationData, createLocationDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                var _a;
                const { id, deleteExistingCapabilities, contactManagements, competencyDetails, agentDetail, additionalDetails, deletedFileIds } = createLocationDto;
                const locationDetail = yield this.locationRepository.getLocationDetailForUpdate(id);
                if (!locationDetail) {
                    throw new common_1.HttpException('Location not found.', common_1.HttpStatus.NOT_FOUND);
                }
                yield this.locationRepository.updateLocationDetailById(id, locationData, currentContext);
                const { industryVerticals } = competencyDetails || {};
                const { aggrements } = agentDetail || {};
                const { additionalDocuments } = additionalDetails || {};
                yield this.updateIndustryVerticals(id, industryVerticals, currentContext);
                yield this.updateContacts(id, contactManagements, currentContext);
                if (aggrements === null || aggrements === void 0 ? void 0 : aggrements.length) {
                    const newAggrements = aggrements.filter(attachment => attachment.isNew || !attachment.file_id);
                    if (newAggrements === null || newAggrements === void 0 ? void 0 : newAggrements.length) {
                        yield this.sharedAttachmentService.addBulkAttachment(newAggrements, id, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS, constants_1.ATTACHMENT_REL_PATH.LOCATION_AGENT_AGREEMENTS, currentContext.user.username);
                    }
                }
                if (additionalDocuments === null || additionalDocuments === void 0 ? void 0 : additionalDocuments.length) {
                    const newAdditionalDocuments = additionalDocuments.filter(attachment => attachment.isNew || !attachment.file_id);
                    if (newAdditionalDocuments === null || newAdditionalDocuments === void 0 ? void 0 : newAdditionalDocuments.length) {
                        yield this.sharedAttachmentService.addBulkAttachment(newAdditionalDocuments, id, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS, constants_1.ATTACHMENT_REL_PATH.LOCATION_ADDITIONAL_DOCUMENTS, currentContext.user.username);
                    }
                }
                if (deletedFileIds === null || deletedFileIds === void 0 ? void 0 : deletedFileIds.length) {
                    yield this.sharedAttachmentService.deleteBulkAttachmentByFileIds(deletedFileIds);
                }
                let history = [];
                if (deleteExistingCapabilities) {
                    yield this.locationWiseCapabilityRepository.deleteByLocationId(id, currentContext);
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE_ALL_CAPABILITIES,
                        action_date: new Date(),
                        comments: 'All existing capabilities deleted.',
                    });
                }
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                    action_date: new Date(),
                    comments: 'Location details updated.',
                    additional_info: {
                        locationData: createLocationDto
                    }
                });
                if (createLocationDto.basicInformation.locationStatus !== locationDetail.status) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.STATUS_CHANGE,
                        action_date: new Date(),
                        comments: `Location status changed from ${locationDetail.status} to ${createLocationDto.basicInformation.locationStatus}.`,
                        additional_info: {
                            old: {
                                status: locationDetail.status,
                                statusDate: locationDetail.statusDate,
                                statusComment: locationDetail.statusComment
                            },
                            new: {
                                status: createLocationDto.basicInformation.locationStatus,
                                statusDate: createLocationDto.basicInformation.statusDate,
                                statusComment: createLocationDto.basicInformation.locationStatusComment
                            }
                        }
                    });
                }
                if (((_a = createLocationDto === null || createLocationDto === void 0 ? void 0 : createLocationDto.basicInformation) === null || _a === void 0 ? void 0 : _a.statusDate) && (locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.statusDate) && !(0, moment_1.default)(createLocationDto.basicInformation.statusDate).isSame((0, moment_1.default)(locationDetail.statusDate), 'day')) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        action_date: new Date(),
                        comments: `Location status date changed from ${locationDetail.statusDate} to ${createLocationDto.basicInformation.statusDate}.`,
                        additional_info: {
                            old: {
                                statusDate: locationDetail.statusDate
                            },
                            new: {
                                statusDate: createLocationDto.basicInformation.statusDate
                            }
                        }
                    });
                }
                if (history.length) {
                    yield this.historyService.addBulkRequestHistory(history);
                }
                return {
                    message: 'Location updated successfully.',
                    data: {
                        locationId: id,
                    },
                };
            }));
        });
    }
    updateIndustryVerticals(locationId, industryVerticals, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const existingIndustryVerticals = yield this.locationIndustryVerticalRepository.getIndustryVerticalsByLocationId(locationId);
            if ((industryVerticals === null || industryVerticals === void 0 ? void 0 : industryVerticals.length) || (existingIndustryVerticals === null || existingIndustryVerticals === void 0 ? void 0 : existingIndustryVerticals.length)) {
                let newIndustryVerticals = [];
                let updateIndustryVerticals = [];
                let deleteIndustryVerticals = [];
                if (existingIndustryVerticals === null || existingIndustryVerticals === void 0 ? void 0 : existingIndustryVerticals.length) {
                    const existingIndustryVerticalsMap = new Map(existingIndustryVerticals.map(industryVertical => [industryVertical.industryVerticalCode, industryVertical]));
                    if (industryVerticals === null || industryVerticals === void 0 ? void 0 : industryVerticals.length) {
                        for (const newIndustryVertical of industryVerticals) {
                            const existingIndustryVertical = existingIndustryVerticalsMap.get(newIndustryVertical.industryVerticalCode);
                            if (existingIndustryVertical) {
                                updateIndustryVerticals.push(Object.assign(Object.assign({}, existingIndustryVertical), { expertUsers: newIndustryVertical.expertUsers }));
                            }
                            else {
                                newIndustryVerticals.push(newIndustryVertical);
                            }
                        }
                    }
                    deleteIndustryVerticals = (_a = existingIndustryVerticals.filter(industryVertical => !(industryVerticals === null || industryVerticals === void 0 ? void 0 : industryVerticals.find(newIndustryVertical => newIndustryVertical.industryVerticalCode === industryVertical.industryVerticalCode)))) === null || _a === void 0 ? void 0 : _a.map(industryVertical => industryVertical.id);
                }
                else {
                    newIndustryVerticals = industryVerticals;
                }
                if (newIndustryVerticals === null || newIndustryVerticals === void 0 ? void 0 : newIndustryVerticals.length) {
                    yield this.addNewIndutryVertical(newIndustryVerticals, locationId, currentContext);
                }
                if (updateIndustryVerticals === null || updateIndustryVerticals === void 0 ? void 0 : updateIndustryVerticals.length) {
                    const updatePromises = updateIndustryVerticals.map(industryVertical => {
                        return this.locationIndustryVerticalRepository.updateIndustryVerticalById(industryVertical.id, {
                            expertUsers: industryVertical.expertUsers,
                        }, currentContext);
                    });
                    yield Promise.all(updatePromises);
                }
                if (deleteIndustryVerticals === null || deleteIndustryVerticals === void 0 ? void 0 : deleteIndustryVerticals.length) {
                    yield this.locationIndustryVerticalRepository.deleteBulkIndustryVertical(deleteIndustryVerticals, currentContext);
                }
            }
        });
    }
    updateContacts(locationId, contactManagements, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const existingLocationManagement = yield this.contactDetailRepository.getContactDetailsByCondition({
                locationId: locationId,
                objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
            });
            const newEntries = [];
            const updatedEntries = [];
            const entriesToDelete = [];
            const existingManagementMap = new Map(existingLocationManagement.map(entry => [(0, lodash_1.toNumber)(entry.objectId), entry]));
            for (const newEntry of contactManagements || []) {
                const existingEntry = existingManagementMap.get(newEntry.contactTypeId);
                if (existingEntry) {
                    if (((_a = newEntry.users) === null || _a === void 0 ? void 0 : _a.length) > 0) {
                        updatedEntries.push(Object.assign(Object.assign({}, existingEntry), { userDetails: newEntry.users }));
                    }
                    else {
                        entriesToDelete.push(existingEntry.id);
                    }
                }
                else {
                    newEntries.push({
                        locationId: locationId,
                        objectId: newEntry.contactTypeId,
                        objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
                        userDetails: newEntry.users,
                    });
                }
            }
            if (newEntries.length > 0) {
                yield this.contactDetailRepository.addBulkContacts(newEntries, currentContext);
            }
            for (const updatedEntry of updatedEntries) {
                yield this.contactDetailRepository.updateUserDetailsByCondition({ id: updatedEntry.id }, updatedEntry.userDetails, currentContext);
            }
            if (entriesToDelete.length > 0) {
                yield this.contactDetailRepository.deleteByIds(entriesToDelete, currentContext);
            }
        });
    }
    transformLocationData(entityDetail, createLocationDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, coreSolutionId, locationTypeId, branchArchetypeCode, basicInformation, locationInformation, competencyDetails, agentDetail, contractDetail, locationLifecycleManagement, additionalDetails, } = createLocationDto;
            const { legalEntityid, locationStatusComment, statusDate, strategicClassificationCode, leaseOwnershipStatus, locationStatus, locationName } = basicInformation, restBasicInformation = __rest(basicInformation, ["legalEntityid", "locationStatusComment", "statusDate", "strategicClassificationCode", "leaseOwnershipStatus", "locationStatus", "locationName"]);
            const _a = competencyDetails || {}, { industryVerticals } = _a, restCompetencyDetails = __rest(_a, ["industryVerticals"]);
            const _b = additionalDetails || {}, { tags, additionalDocuments } = _b, restAdditionalDetails = __rest(_b, ["tags", "additionalDocuments"]);
            const _c = agentDetail || {}, { aggrements } = _c, restAgentDetail = __rest(_c, ["aggrements"]);
            let locationLifeCycleManagementTransformed = {};
            if (locationLifecycleManagement && Object.keys(locationLifecycleManagement).length > 0) {
                const { tiaBId, systemDetails, tiabSince, activationStageId, minimumFfwFteInPlaceId, tomOpsId, tomFinId, additionalRegistrationNumbers, } = locationLifecycleManagement;
                const locationLifeCycleManagementData = yield this.locationLifecycleManagementsTypeRepository.getListByIds([
                    tiaBId,
                    activationStageId,
                    minimumFfwFteInPlaceId,
                    tomOpsId,
                    tomFinId,
                ]);
                locationLifeCycleManagementTransformed = {
                    systemDetails,
                    tiabSince,
                    additionalRegistrationNumbers,
                    tiaB: locationLifeCycleManagementData.find(l => l.id === tiaBId),
                    activationStage: locationLifeCycleManagementData.find(l => l.id === activationStageId),
                    minimumFfwFteInPlace: locationLifeCycleManagementData.find(l => l.id === minimumFfwFteInPlaceId),
                    tomOps: locationLifeCycleManagementData.find(l => l.id === tomOpsId),
                    tomFin: locationLifeCycleManagementData.find(l => l.id === tomFinId),
                };
            }
            let countryDetail;
            if (!id) {
                if (entityDetail.entity_type !== enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY) {
                    let parentEntities = yield this.adminApiClient.getParentsOfEntity(entityDetail.id);
                    const countryData = parentEntities.length ? parentEntities.find((entity) => entity.entity_type === enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY) : null;
                    countryDetail = {
                        entityId: countryData === null || countryData === void 0 ? void 0 : countryData.id,
                        entityCode: countryData === null || countryData === void 0 ? void 0 : countryData.code,
                        entityTitle: countryData === null || countryData === void 0 ? void 0 : countryData.full_name,
                    };
                }
                else {
                    countryDetail = {
                        entityId: entityDetail.id,
                        entityCode: entityDetail.code,
                        entityTitle: entityDetail.full_name,
                    };
                }
            }
            const locationData = Object.assign(Object.assign({}, (!id && {
                entityId: entityDetail.id,
                entityCode: entityDetail.code,
                entityTitle: entityDetail.full_name,
                entityType: entityDetail.entity_type,
                coreSolutionId,
                countryDetail: {
                    entityId: (countryDetail === null || countryDetail === void 0 ? void 0 : countryDetail.entityId) || null,
                    entityCode: (countryDetail === null || countryDetail === void 0 ? void 0 : countryDetail.entityCode) || null,
                    entityTitle: (countryDetail === null || countryDetail === void 0 ? void 0 : countryDetail.entityTitle) || null,
                },
            })), { locationTypeId, branchArchetypeCode: branchArchetypeCode || null, legalEntityId: legalEntityid || null, locationName: locationName, status: locationStatus, statusComment: locationStatusComment || null, statusDate: statusDate || null, strategicClassificationCode: strategicClassificationCode || null, leaseOwnershipStatus: leaseOwnershipStatus, latitude: locationInformation.latitude, longitude: locationInformation.longitude, googleListing: (locationInformation === null || locationInformation === void 0 ? void 0 : locationInformation.googleListing) || null, brands: (locationInformation === null || locationInformation === void 0 ? void 0 : locationInformation.brands) || null, ffLocationIds: (locationInformation === null || locationInformation === void 0 ? void 0 : locationInformation.locationIds) || null, tags: (additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.tags) || [], otherDetails: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (restBasicInformation &&
                    Object.keys(restBasicInformation || {}).length > 0 && {
                    basicInformation: restBasicInformation,
                })), (restCompetencyDetails &&
                    Object.keys(restCompetencyDetails || {}).length > 0 && {
                    competencyDetails: restCompetencyDetails,
                })), (restAgentDetail && Object.keys(restAgentDetail).length > 0 && { agentDetail: restAgentDetail })), (contractDetail && Object.keys(contractDetail).length > 0 && { contractDetail })), (locationLifeCycleManagementTransformed &&
                    Object.keys(locationLifeCycleManagementTransformed).length > 0 && {
                    locationLifecycleManagement: locationLifeCycleManagementTransformed
                })), (restAdditionalDetails &&
                    Object.keys(restAdditionalDetails || {}).length > 0 && {
                    additionalDetails: restAdditionalDetails,
                })) });
            return locationData;
        });
    }
    addNewIndutryVertical(industryVerticals, locationId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            if (industryVerticals === null || industryVerticals === void 0 ? void 0 : industryVerticals.length) {
                const industryVerticalData = industryVerticals.map(industryVertical => {
                    return {
                        locationId,
                        industryVerticalCode: industryVertical.industryVerticalCode,
                        expertUsers: industryVertical.expertUsers,
                    };
                });
                yield this.locationIndustryVerticalRepository.addBulkIndustryVerticals(industryVerticalData, currentContext);
            }
        });
    }
    addNewContactManagement(contactManagements, locationId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            if (contactManagements === null || contactManagements === void 0 ? void 0 : contactManagements.length) {
                const transformedManagementList = contactManagements.map(contactManagement => {
                    return {
                        locationId,
                        objectId: contactManagement.contactTypeId,
                        objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
                        userDetails: contactManagement.users,
                    };
                });
                if (transformedManagementList.length) {
                    yield this.contactDetailRepository.addBulkContacts(transformedManagementList, currentContext);
                }
            }
        });
    }
    uploadNewLocationDocuments(additionalDocuments, aggrements, locationId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            if (additionalDocuments === null || additionalDocuments === void 0 ? void 0 : additionalDocuments.length) {
                yield this.sharedAttachmentService.supportingDocumentsActivity(additionalDocuments, locationId, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS, constants_1.ATTACHMENT_REL_PATH.LOCATION_ADDITIONAL_DOCUMENTS, currentContext.user.username);
            }
            if (aggrements === null || aggrements === void 0 ? void 0 : aggrements.length) {
                yield this.sharedAttachmentService.supportingDocumentsActivity(aggrements, locationId, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS, constants_1.ATTACHMENT_REL_PATH.LOCATION_AGENT_AGREEMENTS, currentContext.user.username);
            }
        });
    }
    getBasicLocationDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const location = yield this.locationRepository.getLocationById(id);
            if (!location) {
                throw new common_1.HttpException('Location not found', common_1.HttpStatus.NOT_FOUND);
            }
            return (0, helpers_1.singleObjectToInstance)(dtos_1.LocationBasicDetailResponseDto, location, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        });
    }
    getCompleteLocationDetailById(id) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5;
        return __awaiter(this, void 0, void 0, function* () {
            const locationDetail = yield this.locationRepository.getCompleteLocationDetailById(id);
            if (!locationDetail) {
                throw new common_1.HttpException('Location not found', common_1.HttpStatus.NOT_FOUND);
            }
            const industryVerticals = yield this.locationIndustryVerticalRepository.getIndustryVerticalsByLocationId(id);
            const contactManagements = yield this.contactDetailRepository.getDetailedContactsByCondition({
                locationId: id,
                objectType: enums_1.CONTACT_DETAIL_OBJECT_TYPE.CONTACT_TYPE_ID,
            });
            let competencyDetails = {};
            if (industryVerticals === null || industryVerticals === void 0 ? void 0 : industryVerticals.length) {
                competencyDetails = Object.assign(Object.assign({}, competencyDetails), { industryVerticals });
            }
            if ((_c = (_b = (_a = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _a === void 0 ? void 0 : _a.competencyDetails) === null || _b === void 0 ? void 0 : _b.sustainabilityExperts) === null || _c === void 0 ? void 0 : _c.length) {
                competencyDetails = Object.assign(Object.assign({}, competencyDetails), { sustainabilityExperts: (_e = (_d = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _d === void 0 ? void 0 : _d.competencyDetails) === null || _e === void 0 ? void 0 : _e.sustainabilityExperts });
            }
            const additionalDetails = Object.assign(Object.assign(Object.assign({}, (((_f = locationDetail.tags) === null || _f === void 0 ? void 0 : _f.length) > 0 && { tags: locationDetail.tags })), (((_h = (_g = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _g === void 0 ? void 0 : _g.additionalDetails) === null || _h === void 0 ? void 0 : _h.customer) && {
                customer: locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails.additionalDetails.customer,
            })), (((_l = (_k = (_j = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _j === void 0 ? void 0 : _j.additionalDetails) === null || _k === void 0 ? void 0 : _k.usefulLinks) === null || _l === void 0 ? void 0 : _l.length) > 0 && {
                usefulLinks: locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails.additionalDetails.usefulLinks,
            }));
            let parentEntities = [];
            if (locationDetail.entityType !== enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY) {
                parentEntities = yield this.adminApiClient.getParentsOfEntity(locationDetail.entityId);
            }
            const returnData = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({ id: locationDetail.id, entityDetail: {
                    id: locationDetail.entityId,
                    code: locationDetail.entityCode,
                    title: locationDetail.entityTitle,
                    type: locationDetail.entityType,
                    countryId: locationDetail.entityType === enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY ? locationDetail.entityId : parentEntities.length ? parentEntities.find((entity) => entity.entity_type === enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY).id : null,
                }, coreSolution: locationDetail.coreSolution, locationType: locationDetail.locationType }, ((locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.branchArchetype) && { branchArchetype: locationDetail.branchArchetype })), { basicInformation: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({ locationName: locationDetail.locationName || '', introduction: ((_o = (_m = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _m === void 0 ? void 0 : _m.basicInformation) === null || _o === void 0 ? void 0 : _o.introduction) || '', locationStatus: locationDetail.status, leaseOwnershipStatus: locationDetail.leaseOwnershipStatus, address: ((_q = (_p = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _p === void 0 ? void 0 : _p.basicInformation) === null || _q === void 0 ? void 0 : _q.address) || '' }, ((locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.strategicClassification) && {
                    strategicClassification: locationDetail.strategicClassification,
                })), ((locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.legalEntity) && { legalEntity: locationDetail.legalEntity })), ((locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.statusComment) && {
                    locationStatusComment: locationDetail.statusComment,
                })), ((locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.statusDate) && { statusDate: locationDetail.statusDate })), (((_s = (_r = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _r === void 0 ? void 0 : _r.basicInformation) === null || _s === void 0 ? void 0 : _s.leaseExpirationDate) && {
                    leaseExpirationDate: (_u = (_t = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _t === void 0 ? void 0 : _t.basicInformation) === null || _u === void 0 ? void 0 : _u.leaseExpirationDate,
                })), locationInformation: Object.assign(Object.assign(Object.assign({ latitude: locationDetail.latitude, longitude: locationDetail.longitude }, (((_v = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.ffLocationIds) === null || _v === void 0 ? void 0 : _v.length) && { locationIds: locationDetail.ffLocationIds })), (((_w = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.brands) === null || _w === void 0 ? void 0 : _w.length) && { brands: locationDetail.brands })), ((locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.googleListing) && { googleListing: locationDetail.googleListing })) }), (Object.keys(competencyDetails).length > 0 && { competencyDetails })), (((_x = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _x === void 0 ? void 0 : _x.agentDetail) &&
                Object.keys((_y = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _y === void 0 ? void 0 : _y.agentDetail).length > 0 && {
                agentDetail: (_z = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _z === void 0 ? void 0 : _z.agentDetail,
            })), (((_0 = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _0 === void 0 ? void 0 : _0.contractDetail) &&
                Object.keys((_1 = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _1 === void 0 ? void 0 : _1.contractDetail).length > 0 && {
                contractDetail: (_2 = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _2 === void 0 ? void 0 : _2.contractDetail,
            })), (((_3 = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _3 === void 0 ? void 0 : _3.locationLifecycleManagement) &&
                Object.keys((_4 = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _4 === void 0 ? void 0 : _4.locationLifecycleManagement).length > 0 && {
                locationLifecycleManagement: (_5 = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.otherDetails) === null || _5 === void 0 ? void 0 : _5.locationLifecycleManagement,
            })), (Object.keys(additionalDetails).length > 0 && { additionalDetails })), (contactManagements.length && { contactManagements }));
            const [additionalDocuments, aggrements] = yield Promise.all([
                this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS),
                locationDetail.locationType.code === enums_1.LOCATION_TYPE_ENUM.AGENT ?
                    this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS) : []
            ]);
            if (additionalDocuments === null || additionalDocuments === void 0 ? void 0 : additionalDocuments.length) {
                returnData.additionalDetails = Object.assign(Object.assign({}, returnData.additionalDetails || {}), { additionalDocuments });
            }
            if (aggrements === null || aggrements === void 0 ? void 0 : aggrements.length) {
                returnData.agentDetail = Object.assign(Object.assign({}, returnData.agentDetail || {}), { aggrements });
            }
            return (0, helpers_1.singleObjectToInstance)(dtos_1.LocationCompleteDetailResponseDto, returnData, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        });
    }
    getLocationListByFilter(page, limit, orderBy, orderDirection, filterDto = {}) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            if (((_a = filterDto === null || filterDto === void 0 ? void 0 : filterDto.entityIds) === null || _a === void 0 ? void 0 : _a.length) || (filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId)) {
                filterDto.entityIds = yield this.businessEntityService.getBusinessEntitiesChildIds((filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId) ? [filterDto.hierarchyEntityId] : filterDto.entityIds);
            }
            const { rows, count } = yield this.locationRepository.getLocationsByFilter(filterDto, page, limit, orderBy, orderDirection);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.LocationListResponseDto, rows, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    deleteLocation(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const locationDetail = yield this.locationRepository.getLocationById(id);
            if (!locationDetail) {
                throw new common_1.HttpException('Location not found', common_1.HttpStatus.NOT_FOUND);
            }
            const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE], currentContext.user, locationDetail.entityId, null);
            if (!hasPermission) {
                throw new common_1.HttpException('You don\'t have permission to delete this location.', common_1.HttpStatus.FORBIDDEN);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.locationRepository.deleteLocation(id, currentContext);
                yield this.locationIndustryVerticalRepository.deleteIndustryVerticalByLocationId(id, currentContext);
                yield this.partnerBranchRepository.deletePartnerBranches(id, currentContext);
                yield this.contactDetailRepository.deleteByLocationId(id, currentContext);
                yield this.locationWiseCapabilityRepository.deleteByLocationId(id, currentContext);
                yield this.userPermissionRepository.deleteByLocationId(id, currentContext);
                const [additionalDocuments, aggrements] = yield Promise.all([
                    this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_ADDITIONAL_DOCUMENTS),
                    locationDetail.locationType.code === enums_1.LOCATION_TYPE_ENUM.AGENT ?
                        this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.LOCATION_AGENT_AGREEMENTS) : []
                ]);
                let fileIds = [];
                if (additionalDocuments === null || additionalDocuments === void 0 ? void 0 : additionalDocuments.length) {
                    fileIds = [...fileIds, ...additionalDocuments.map(attachment => attachment.file_id)];
                }
                if (aggrements === null || aggrements === void 0 ? void 0 : aggrements.length) {
                    fileIds = [...fileIds, ...aggrements.map(attachment => attachment.file_id)];
                }
                if (fileIds === null || fileIds === void 0 ? void 0 : fileIds.length) {
                    yield this.sharedAttachmentService.deleteBulkAttachmentByFileIds(fileIds);
                }
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                    action_date: new Date(),
                    comments: 'Location has been deleted.',
                });
                return { message: 'Location deleted successfully' };
            }));
        });
    }
    updateLocationStatus(id, updateStatusRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            if ((updateStatusRequestDto.locationStatus !== enums_1.LOCATION_STATUS_TYPE.OTHER && updateStatusRequestDto.locationStatus !== enums_1.LOCATION_STATUS_TYPE.IN_ACTIVE) &&
                !updateStatusRequestDto.statusDate) {
                throw new common_1.HttpException('Status date is required.', common_1.HttpStatus.BAD_REQUEST);
            }
            if ((updateStatusRequestDto.locationStatus === enums_1.LOCATION_STATUS_TYPE.OTHER) &&
                !updateStatusRequestDto.locationStatusComment) {
                throw new common_1.HttpException('Status Comment is required.', common_1.HttpStatus.BAD_REQUEST);
            }
            const locationDetail = yield this.locationRepository.getLocationById(id);
            if (!locationDetail) {
                throw new common_1.HttpException('Location not found', common_1.HttpStatus.NOT_FOUND);
            }
            const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.GLOBAL_LEGAL, enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, locationDetail.entityId, locationDetail.id);
            if (!hasPermission) {
                throw new common_1.HttpException('You don\'t have permission to update status of this location.', common_1.HttpStatus.FORBIDDEN);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.locationRepository.updateLocationDetailById(id, {
                    status: updateStatusRequestDto.locationStatus,
                    statusDate: (updateStatusRequestDto === null || updateStatusRequestDto === void 0 ? void 0 : updateStatusRequestDto.statusDate) || null,
                    statusComment: (updateStatusRequestDto === null || updateStatusRequestDto === void 0 ? void 0 : updateStatusRequestDto.locationStatusComment) || null,
                }, currentContext);
                let history = [];
                if (updateStatusRequestDto.locationStatus !== locationDetail.status) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.STATUS_CHANGE,
                        action_date: new Date(),
                        comments: `Location status changed from ${locationDetail.status} to ${updateStatusRequestDto.locationStatus}.`,
                        additional_info: {
                            old: {
                                status: locationDetail.status,
                                statusDate: locationDetail.statusDate,
                                statusComment: locationDetail.statusComment
                            },
                            new: {
                                status: updateStatusRequestDto.locationStatus,
                                statusDate: (updateStatusRequestDto === null || updateStatusRequestDto === void 0 ? void 0 : updateStatusRequestDto.statusDate) || null,
                                statusComment: (updateStatusRequestDto === null || updateStatusRequestDto === void 0 ? void 0 : updateStatusRequestDto.locationStatusComment) || null
                            }
                        }
                    });
                }
                if ((updateStatusRequestDto === null || updateStatusRequestDto === void 0 ? void 0 : updateStatusRequestDto.statusDate) && (locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.statusDate) && !(0, moment_1.default)(updateStatusRequestDto.statusDate).isSame((0, moment_1.default)(locationDetail.statusDate), 'day')) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        action_date: new Date(),
                        comments: `Location status date changed from ${locationDetail.statusDate} to ${updateStatusRequestDto.statusDate}.`,
                        additional_info: {
                            old: {
                                statusDate: locationDetail.statusDate
                            },
                            new: {
                                statusDate: updateStatusRequestDto.statusDate
                            }
                        }
                    });
                }
                if ((updateStatusRequestDto === null || updateStatusRequestDto === void 0 ? void 0 : updateStatusRequestDto.locationStatusComment) && (locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.statusComment) && updateStatusRequestDto.locationStatusComment !== locationDetail.statusComment) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        action_date: new Date(),
                        comments: `Location detail updated.`,
                        additional_info: {
                            old: {
                                statusComment: locationDetail.statusComment
                            },
                            new: {
                                statusComment: updateStatusRequestDto.locationStatusComment
                            }
                        }
                    });
                }
                if (history.length) {
                    yield this.historyService.addBulkRequestHistory(history);
                }
                return { message: 'Status updated successfully.' };
            }));
        });
    }
    getLocationWiseCapabilities(locationId) {
        return __awaiter(this, void 0, void 0, function* () {
            const locationDetail = yield this.locationRepository.getLocationById(locationId);
            if (!locationDetail) {
                throw new common_1.HttpException('Location not found', common_1.HttpStatus.NOT_FOUND);
            }
            const capabilityList = yield this.locationWiseCapabilityRepository.getCapabilitiesByLocationId(locationId);
            return (0, helpers_1.singleObjectToInstance)(location_capabilities_response_dto_1.LocationCapabilitiesResponseDto, {
                locationDetail,
                capabilityList
            }, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        });
    }
    exportLocations(requestContext, filterDto) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const { currentContext, headers } = requestContext;
            if (((_a = filterDto === null || filterDto === void 0 ? void 0 : filterDto.entityIds) === null || _a === void 0 ? void 0 : _a.length) || (filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId)) {
                filterDto.entityIds = yield this.businessEntityService.getBusinessEntitiesChildIds((filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId) ? [filterDto.hierarchyEntityId] : filterDto.entityIds);
            }
            const locationList = yield this.locationRepository.getLocationsToExport(filterDto);
            if (!(locationList === null || locationList === void 0 ? void 0 : locationList.length)) {
                throw new common_1.HttpException('No locations available to export', common_1.HttpStatus.NOT_FOUND);
            }
            const SHEET_NAME = 'CapMApp Location List - ' + new Date().getTime();
            const report = yield this.excelSheetService.createLocationSheet(locationList, SHEET_NAME);
            yield this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: -1,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.EXPORT,
                action_performed: enums_1.HISTORY_ACTION_TYPE.EXPORT,
                action_date: new Date(),
                comments: 'Location list exported.',
                additional_info: {
                    filters: filterDto,
                    fileName: SHEET_NAME,
                    totalExportCount: locationList.length,
                    host: headers === null || headers === void 0 ? void 0 : headers.host,
                    origin: headers === null || headers === void 0 ? void 0 : headers.origin
                }
            });
            return { report, filename: SHEET_NAME };
        });
    }
    exportLocationWiseCapabilities(locationId, requestContext, filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { currentContext, headers } = requestContext;
            const locationDetail = yield this.locationRepository.getLocationNameById(locationId);
            if (!locationDetail) {
                throw new common_1.HttpException('Location not found', common_1.HttpStatus.NOT_FOUND);
            }
            const capabilityList = yield this.locationWiseCapabilityRepository.exportCapabilitiesByLocationId(locationId, filterDto);
            if (!(capabilityList === null || capabilityList === void 0 ? void 0 : capabilityList.length)) {
                throw new common_1.HttpException('No entries available to export', common_1.HttpStatus.NOT_FOUND);
            }
            const SHEET_NAME = `${locationDetail.locationName} available entries - ${new Date().getTime()}`;
            const report = yield this.excelSheetService.createLocationWiseCapabilitiesSheet(capabilityList, SHEET_NAME);
            yield this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: -1,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.EXPORT,
                action_performed: enums_1.HISTORY_ACTION_TYPE.EXPORT,
                action_date: new Date(),
                comments: `Available capability list exported for ${locationDetail.locationName}.`,
                additional_info: {
                    filters: filterDto,
                    fileName: SHEET_NAME,
                    totalExportCount: capabilityList.length,
                    host: headers === null || headers === void 0 ? void 0 : headers.host,
                    origin: headers === null || headers === void 0 ? void 0 : headers.origin
                }
            });
            return { report, filename: SHEET_NAME };
        });
    }
};
LocationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.LocationRepository,
        repositories_1.LocationIndustryVerticalRepository,
        validators_1.SetupNewLocationRequest,
        helpers_1.DatabaseHelper,
        repositories_2.ContactDetailRepository,
        repositories_3.LocationLifecycleManagementsTypeRepository,
        services_1.SharedAttachmentService,
        clients_1.AttachmentApiClient,
        location_wise_capability_repository_1.LocationWiseCapabilityRepository,
        services_2.BusinessEntityService,
        repositories_1.PartnerBranchRepository,
        services_1.ExcelSheetService,
        clients_1.AdminApiClient,
        services_1.SharedPermissionService,
        repositories_4.UserPermissionRepository,
        clients_1.HistoryApiClient])
], LocationService);
exports.LocationService = LocationService;
//# sourceMappingURL=location.service.js.map