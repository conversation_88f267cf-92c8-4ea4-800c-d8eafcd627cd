"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectIdValidationPipe = void 0;
const common_1 = require("@nestjs/common");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
let ObjectIdValidationPipe = class ObjectIdValidationPipe {
    transform(value, metadata) {
        if (!metadata.data) {
            return value;
        }
        const objectType = metadata.data['objectType'];
        if (objectType === enums_1.CONTACT_DETAIL_OBJECT_TYPE.PILLAR) {
            if (!Object.values(enums_1.PILLAR).includes(value)) {
                throw new exceptions_1.HttpException(`When objectType is PILLAR, objectId must be one of: ${Object.values(enums_1.PILLAR).join(', ')}`, enums_1.HttpStatus.FORBIDDEN);
            }
            return value;
        }
        const numValue = Number(value);
        if (isNaN(numValue)) {
            throw new exceptions_1.HttpException(`objectId must be a number for the specified objectType`, enums_1.HttpStatus.BAD_REQUEST);
        }
        return numValue;
    }
};
ObjectIdValidationPipe = __decorate([
    (0, common_1.Injectable)()
], ObjectIdValidationPipe);
exports.ObjectIdValidationPipe = ObjectIdValidationPipe;
//# sourceMappingURL=object-id-validation.pipe.js.map