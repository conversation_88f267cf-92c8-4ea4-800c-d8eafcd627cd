import {
  warnOnce
} from "./chunk-TTAICKHP.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/TreeViewProvider.js
var React2 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/TreeViewContext.js
var React = __toESM(require_react());
var TreeViewContext = React.createContext(null);
if (true) {
  TreeViewContext.displayName = "TreeViewContext";
}

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/TreeViewProvider.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
function TreeViewProvider(props) {
  const {
    value,
    children
  } = props;
  return (0, import_jsx_runtime.jsx)(TreeViewContext.Provider, {
    value,
    children: value.wrapRoot({
      children,
      instance: value.instance
    })
  });
}

// node_modules/@mui/x-tree-view/internals/TreeViewProvider/useTreeViewContext.js
var React3 = __toESM(require_react());
var useTreeViewContext = () => {
  const context = React3.useContext(TreeViewContext);
  if (context == null) {
    throw new Error(["MUI X: Could not find the Tree View context.", "It looks like you rendered your component outside of a SimpleTreeView or RichTreeView parent component.", "This can also happen if you are bundling multiple versions of the Tree View."].join("\n"));
  }
  return context;
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewLabel/useTreeViewLabel.js
var React5 = __toESM(require_react());

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewLabel/useTreeViewLabel.itemPlugin.js
var React4 = __toESM(require_react());
var useTreeViewLabelItemPlugin = ({
  props
}) => {
  const {
    instance
  } = useTreeViewContext();
  const {
    label,
    itemId
  } = props;
  const [labelInputValue, setLabelInputValue] = React4.useState(label);
  const isItemBeingEdited = instance.isItemBeingEdited(itemId);
  React4.useEffect(() => {
    if (!isItemBeingEdited) {
      setLabelInputValue(label);
    }
  }, [isItemBeingEdited, label]);
  return {
    propsEnhancers: {
      labelInput: ({
        externalEventHandlers,
        interactions
      }) => {
        const editable = instance.isItemEditable(itemId);
        if (!editable) {
          return {};
        }
        const handleKeydown = (event) => {
          var _a;
          (_a = externalEventHandlers.onKeyDown) == null ? void 0 : _a.call(externalEventHandlers, event);
          if (event.defaultMuiPrevented) {
            return;
          }
          const target = event.target;
          if (event.key === "Enter" && target.value) {
            interactions.handleSaveItemLabel(event, target.value);
          } else if (event.key === "Escape") {
            interactions.handleCancelItemLabelEditing(event);
          }
        };
        const handleBlur = (event) => {
          var _a;
          (_a = externalEventHandlers.onBlur) == null ? void 0 : _a.call(externalEventHandlers, event);
          if (event.defaultMuiPrevented) {
            return;
          }
          if (event.target.value) {
            interactions.handleSaveItemLabel(event, event.target.value);
          }
        };
        const handleInputChange = (event) => {
          var _a;
          (_a = externalEventHandlers.onChange) == null ? void 0 : _a.call(externalEventHandlers, event);
          setLabelInputValue(event.target.value);
        };
        return {
          value: labelInputValue ?? "",
          "data-element": "labelInput",
          onChange: handleInputChange,
          onKeyDown: handleKeydown,
          onBlur: handleBlur,
          autoFocus: true,
          type: "text"
        };
      }
    }
  };
};

// node_modules/@mui/x-tree-view/internals/plugins/useTreeViewLabel/useTreeViewLabel.js
var useTreeViewLabel = ({
  instance,
  state,
  setState,
  params
}) => {
  const editedItemRef = React5.useRef(state.editedItemId);
  const isItemBeingEditedRef = (itemId) => editedItemRef.current === itemId;
  const setEditedItemId = (editedItemId) => {
    setState((prevState) => _extends({}, prevState, {
      editedItemId
    }));
    editedItemRef.current = editedItemId;
  };
  const isItemBeingEdited = (itemId) => itemId === state.editedItemId;
  const isTreeViewEditable = Boolean(params.isItemEditable);
  const isItemEditable = (itemId) => {
    if (itemId == null || !isTreeViewEditable) {
      return false;
    }
    const item = instance.getItem(itemId);
    if (!item) {
      return false;
    }
    return typeof params.isItemEditable === "function" ? params.isItemEditable(item) : Boolean(params.isItemEditable);
  };
  const updateItemLabel = (itemId, label) => {
    if (!label) {
      throw new Error(["MUI X: The Tree View component requires all items to have a `label` property.", "The label of an item cannot be empty.", itemId].join("\n"));
    }
    setState((prevState) => {
      const item = prevState.items.itemMetaMap[itemId];
      if (item.label !== label) {
        return _extends({}, prevState, {
          items: _extends({}, prevState.items, {
            itemMetaMap: _extends({}, prevState.items.itemMetaMap, {
              [itemId]: _extends({}, item, {
                label
              })
            })
          })
        });
      }
      return prevState;
    });
    if (params.onItemLabelChange) {
      params.onItemLabelChange(itemId, label);
    }
  };
  return {
    instance: {
      setEditedItemId,
      isItemBeingEdited,
      updateItemLabel,
      isItemEditable,
      isTreeViewEditable,
      isItemBeingEditedRef
    },
    publicAPI: {
      updateItemLabel
    }
  };
};
useTreeViewLabel.itemPlugin = useTreeViewLabelItemPlugin;
useTreeViewLabel.getDefaultizedParams = ({
  params,
  experimentalFeatures
}) => {
  const canUseFeature = experimentalFeatures == null ? void 0 : experimentalFeatures.labelEditing;
  if (true) {
    if (params.isItemEditable && !canUseFeature) {
      warnOnce(["MUI X: The label editing feature requires the `labelEditing` experimental feature to be enabled.", "You can do it by passing `experimentalFeatures={{ labelEditing: true}}` to the Rich Tree View Pro component.", "Check the documentation for more details: https://mui.com/x/react-tree-view/rich-tree-view/editing/"]);
    }
  }
  return _extends({}, params, {
    isItemEditable: canUseFeature ? params.isItemEditable ?? false : false
  });
};
useTreeViewLabel.getInitialState = () => ({
  editedItemId: null
});
useTreeViewLabel.params = {
  onItemLabelChange: true,
  isItemEditable: true
};

// node_modules/@mui/x-tree-view/internals/utils/plugins.js
var hasPlugin = (instance, plugin) => {
  const plugins = instance.getAvailablePlugins();
  return plugins.has(plugin);
};

export {
  TreeViewProvider,
  useTreeViewContext,
  useTreeViewLabel,
  hasPlugin
};
//# sourceMappingURL=chunk-6UJFUI4Z.js.map
