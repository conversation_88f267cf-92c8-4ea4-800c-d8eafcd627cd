{"version": 3, "sources": ["../../@mui/material/AccordionSummary/AccordionSummary.js", "../../@mui/material/AccordionSummary/accordionSummaryClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport AccordionContext from \"../Accordion/AccordionContext.js\";\nimport accordionSummaryClasses, { getAccordionSummaryUtilityClass } from \"./accordionSummaryClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    expanded,\n    disabled,\n    disableGutters\n  } = ownerState;\n  const slots = {\n    root: ['root', expanded && 'expanded', disabled && 'disabled', !disableGutters && 'gutters'],\n    focusVisible: ['focusVisible'],\n    content: ['content', expanded && 'expanded', !disableGutters && 'contentGutters'],\n    expandIconWrapper: ['expandIconWrapper', expanded && 'expanded']\n  };\n  return composeClasses(slots, getAccordionSummaryUtilityClass, classes);\n};\nconst AccordionSummaryRoot = styled(ButtonBase, {\n  name: 'MuiAccordionSummary',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(memoTheme(({\n  theme\n}) => {\n  const transition = {\n    duration: theme.transitions.duration.shortest\n  };\n  return {\n    display: 'flex',\n    width: '100%',\n    minHeight: 48,\n    padding: theme.spacing(0, 2),\n    transition: theme.transitions.create(['min-height', 'background-color'], transition),\n    [`&.${accordionSummaryClasses.focusVisible}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.focus\n    },\n    [`&.${accordionSummaryClasses.disabled}`]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity\n    },\n    [`&:hover:not(.${accordionSummaryClasses.disabled})`]: {\n      cursor: 'pointer'\n    },\n    variants: [{\n      props: props => !props.disableGutters,\n      style: {\n        [`&.${accordionSummaryClasses.expanded}`]: {\n          minHeight: 64\n        }\n      }\n    }]\n  };\n}));\nconst AccordionSummaryContent = styled('span', {\n  name: 'MuiAccordionSummary',\n  slot: 'Content',\n  overridesResolver: (props, styles) => styles.content\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  textAlign: 'start',\n  flexGrow: 1,\n  margin: '12px 0',\n  variants: [{\n    props: props => !props.disableGutters,\n    style: {\n      transition: theme.transitions.create(['margin'], {\n        duration: theme.transitions.duration.shortest\n      }),\n      [`&.${accordionSummaryClasses.expanded}`]: {\n        margin: '20px 0'\n      }\n    }\n  }]\n})));\nconst AccordionSummaryExpandIconWrapper = styled('span', {\n  name: 'MuiAccordionSummary',\n  slot: 'ExpandIconWrapper',\n  overridesResolver: (props, styles) => styles.expandIconWrapper\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  color: (theme.vars || theme).palette.action.active,\n  transform: 'rotate(0deg)',\n  transition: theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${accordionSummaryClasses.expanded}`]: {\n    transform: 'rotate(180deg)'\n  }\n})));\nconst AccordionSummary = /*#__PURE__*/React.forwardRef(function AccordionSummary(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionSummary'\n  });\n  const {\n    children,\n    className,\n    expandIcon,\n    focusVisibleClassName,\n    onClick,\n    slots,\n    slotProps,\n    ...other\n  } = props;\n  const {\n    disabled = false,\n    disableGutters,\n    expanded,\n    toggle\n  } = React.useContext(AccordionContext);\n  const handleChange = event => {\n    if (toggle) {\n      toggle(event);\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  const ownerState = {\n    ...props,\n    expanded,\n    disabled,\n    disableGutters\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref,\n    shouldForwardComponentProp: true,\n    className: clsx(classes.root, className),\n    elementType: AccordionSummaryRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    additionalProps: {\n      focusRipple: false,\n      disableRipple: true,\n      disabled,\n      'aria-expanded': expanded,\n      focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName)\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onClick: event => {\n        handlers.onClick?.(event);\n        handleChange(event);\n      }\n    })\n  });\n  const [ContentSlot, contentSlotProps] = useSlot('content', {\n    className: classes.content,\n    elementType: AccordionSummaryContent,\n    externalForwardedProps,\n    ownerState\n  });\n  const [ExpandIconWrapperSlot, expandIconWrapperSlotProps] = useSlot('expandIconWrapper', {\n    className: classes.expandIconWrapper,\n    elementType: AccordionSummaryExpandIconWrapper,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(ContentSlot, {\n      ...contentSlotProps,\n      children: children\n    }), expandIcon && /*#__PURE__*/_jsx(ExpandIconWrapperSlot, {\n      ...expandIconWrapperSlotProps,\n      children: expandIcon\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionSummary.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display as the expand indicator.\n   */\n  expandIcon: PropTypes.node,\n  /**\n   * This prop can help identify which element has keyboard focus.\n   * The class name will be applied when the element gains the focus through keyboard interaction.\n   * It's a polyfill for the [CSS :focus-visible selector](https://drafts.csswg.org/selectors-4/#the-focus-visible-pseudo).\n   * The rationale for using this feature [is explained here](https://github.com/WICG/focus-visible/blob/HEAD/explainer.md).\n   * A [polyfill can be used](https://github.com/WICG/focus-visible) to apply a `focus-visible` class to other components\n   * if needed.\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    content: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    expandIconWrapper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    content: PropTypes.elementType,\n    expandIconWrapper: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionSummary;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAccordionSummaryUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionSummary', slot);\n}\nconst accordionSummaryClasses = generateUtilityClasses('MuiAccordionSummary', ['root', 'expanded', 'focusVisible', 'disabled', 'gutters', 'contentGutters', 'content', 'expandIconWrapper']);\nexport default accordionSummaryClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,gCAAgC,MAAM;AACpD,SAAO,qBAAqB,uBAAuB,IAAI;AACzD;AACA,IAAM,0BAA0B,uBAAuB,uBAAuB,CAAC,QAAQ,YAAY,gBAAgB,YAAY,WAAW,kBAAkB,WAAW,mBAAmB,CAAC;AAC3L,IAAO,kCAAQ;;;ADOf,yBAA2C;AAC3C,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,YAAY,YAAY,YAAY,CAAC,kBAAkB,SAAS;AAAA,IAC3F,cAAc,CAAC,cAAc;AAAA,IAC7B,SAAS,CAAC,WAAW,YAAY,YAAY,CAAC,kBAAkB,gBAAgB;AAAA,IAChF,mBAAmB,CAAC,qBAAqB,YAAY,UAAU;AAAA,EACjE;AACA,SAAO,eAAe,OAAO,iCAAiC,OAAO;AACvE;AACA,IAAM,uBAAuB,eAAO,oBAAY;AAAA,EAC9C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,MAAM;AACJ,QAAM,aAAa;AAAA,IACjB,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,IACP,WAAW;AAAA,IACX,SAAS,MAAM,QAAQ,GAAG,CAAC;AAAA,IAC3B,YAAY,MAAM,YAAY,OAAO,CAAC,cAAc,kBAAkB,GAAG,UAAU;AAAA,IACnF,CAAC,KAAK,gCAAwB,YAAY,EAAE,GAAG;AAAA,MAC7C,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IACxD;AAAA,IACA,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,MACzC,UAAU,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAChD;AAAA,IACA,CAAC,gBAAgB,gCAAwB,QAAQ,GAAG,GAAG;AAAA,MACrD,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC;AAAA,MACT,OAAO,WAAS,CAAC,MAAM;AAAA,MACvB,OAAO;AAAA,QACL,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,UACzC,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC,CAAC;AACF,IAAM,0BAA0B,eAAO,QAAQ;AAAA,EAC7C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU,CAAC;AAAA,IACT,OAAO,WAAS,CAAC,MAAM;AAAA,IACvB,OAAO;AAAA,MACL,YAAY,MAAM,YAAY,OAAO,CAAC,QAAQ,GAAG;AAAA,QAC/C,UAAU,MAAM,YAAY,SAAS;AAAA,MACvC,CAAC;AAAA,MACD,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,QACzC,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,oCAAoC,eAAO,QAAQ;AAAA,EACvD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EAC5C,WAAW;AAAA,EACX,YAAY,MAAM,YAAY,OAAO,aAAa;AAAA,IAChD,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC,CAAC;AAAA,EACD,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,IACzC,WAAW;AAAA,EACb;AACF,EAAE,CAAC;AACH,IAAM,mBAAsC,iBAAW,SAASA,kBAAiB,SAAS,KAAK;AAC7F,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAU,iBAAW,wBAAgB;AACrC,QAAM,eAAe,WAAS;AAC5B,QAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AACA,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACA,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACA,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD;AAAA,IACA,4BAA4B;AAAA,IAC5B,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf,aAAa;AAAA,MACb,eAAe;AAAA,MACf;AAAA,MACA,iBAAiB;AAAA,MACjB,uBAAuB,aAAK,QAAQ,cAAc,qBAAqB;AAAA,IACzE;AAAA,IACA,cAAc,eAAa;AAAA,MACzB,GAAG;AAAA,MACH,SAAS,WAAS;AAnKxB;AAoKQ,uBAAS,YAAT,kCAAmB;AACnB,qBAAa,KAAK;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,CAAC,aAAa,gBAAgB,IAAI,QAAQ,WAAW;AAAA,IACzD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,uBAAuB,0BAA0B,IAAI,QAAQ,qBAAqB;AAAA,IACvF,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,mBAAAC,MAAM,UAAU;AAAA,IAClC,GAAG;AAAA,IACH,UAAU,KAAc,mBAAAC,KAAK,aAAa;AAAA,MACxC,GAAG;AAAA,MACH;AAAA,IACF,CAAC,GAAG,kBAA2B,mBAAAA,KAAK,uBAAuB;AAAA,MACzD,GAAG;AAAA,MACH,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,iBAAiB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1F,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStB,uBAAuB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjC,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC/D,mBAAmB,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IACzE,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,SAAS,kBAAAA,QAAU;AAAA,IACnB,mBAAmB,kBAAAA,QAAU;AAAA,IAC7B,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,2BAAQ;", "names": ["AccordionSummary", "_jsxs", "_jsx", "PropTypes"]}