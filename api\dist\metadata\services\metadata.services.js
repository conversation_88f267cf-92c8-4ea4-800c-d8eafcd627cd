"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetadataService = void 0;
const common_1 = require("@nestjs/common");
const helpers_1 = require("../../shared/helpers");
const repositories_1 = require("../repositories");
const dtos_1 = require("../dtos");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const repositories_2 = require("../../location/repositories");
const repositories_3 = require("../../capability/repositories");
let MetadataService = class MetadataService {
    constructor(coreSolutionRepo, locationTypeRepo, commonDropdownRepo, locationLifecycleManagementsRepo, locationRepository, locationWiseCapabilityRepo) {
        this.coreSolutionRepo = coreSolutionRepo;
        this.locationTypeRepo = locationTypeRepo;
        this.commonDropdownRepo = commonDropdownRepo;
        this.locationLifecycleManagementsRepo = locationLifecycleManagementsRepo;
        this.locationRepository = locationRepository;
        this.locationWiseCapabilityRepo = locationWiseCapabilityRepo;
    }
    getAllCoreSolutions() {
        return __awaiter(this, void 0, void 0, function* () {
            const results = yield this.coreSolutionRepo.getAllCoreSolutions();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.CoreSolutionCompleteResponseDto, results);
        });
    }
    getLocationTypesByCoreSolutionId(coreSolutionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const results = yield this.locationTypeRepo.getLocationTypeByCoreSolutionId(coreSolutionId);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.LocationTypeResponseDto, results);
        });
    }
    getAllLocationTypes() {
        return __awaiter(this, void 0, void 0, function* () {
            const results = yield this.locationTypeRepo.getAllLocationTypes();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.LocationTypeResponseDto, results);
        });
    }
    getAllLocationLifeCycleTypes() {
        return __awaiter(this, void 0, void 0, function* () {
            const results = yield this.locationLifecycleManagementsRepo.getAllLocationLifeCycleManagements();
            const groupedResults = this.groupResultsByType(results);
            const transformedResults = this.transformGroupedResultsOfLocationLifecycleManagements(groupedResults);
            return transformedResults;
        });
    }
    getMetadataByType(requestParms) {
        return __awaiter(this, void 0, void 0, function* () {
            this.validateMetadataByTypeRequestParams(requestParms);
            if (requestParms.type.includes(enums_1.COMMON_DROPDOWN_TYPE.ALL_STRATEGIC_CLASSIFICATION)) {
                if (requestParms.type.includes(enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION)) {
                    requestParms.type = requestParms.type.filter(type => type !== enums_1.COMMON_DROPDOWN_TYPE.ALL_STRATEGIC_CLASSIFICATION);
                }
            }
            const results = yield this.commonDropdownRepo.getDropdownList(requestParms);
            const groupedResults = this.groupResultsByType(results);
            let transformedResults = this.transformGroupedResults(groupedResults);
            if (requestParms.type.includes(enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION) &&
                (requestParms === null || requestParms === void 0 ? void 0 : requestParms.coreSolutionId) &&
                (requestParms === null || requestParms === void 0 ? void 0 : requestParms.locationTypeId)) {
                const strategicClassificationType = yield this.commonDropdownRepo.getListBasedOnCoreSolutionAndLocationType(enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION, requestParms.coreSolutionId, requestParms.locationTypeId);
                transformedResults = Object.assign({ [enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION]: (0, helpers_1.multiObjectToInstance)(dtos_1.MetadataByTypeResponseDto, strategicClassificationType) }, transformedResults);
            }
            if (requestParms.type.includes(enums_1.COMMON_DROPDOWN_TYPE.CONTACT_TYPE) &&
                (requestParms === null || requestParms === void 0 ? void 0 : requestParms.coreSolutionId) &&
                (requestParms === null || requestParms === void 0 ? void 0 : requestParms.locationTypeId)) {
                const contactType = yield this.commonDropdownRepo.getListBasedOnCoreSolutionAndLocationType(enums_1.COMMON_DROPDOWN_TYPE.CONTACT_TYPE, requestParms.coreSolutionId, requestParms.locationTypeId);
                transformedResults = Object.assign({ [enums_1.COMMON_DROPDOWN_TYPE.CONTACT_TYPE]: (0, helpers_1.multiObjectToInstance)(dtos_1.MetadataByTypeResponseDto, contactType) }, transformedResults);
            }
            return transformedResults;
        });
    }
    validateMetadataByTypeRequestParams(requestParms) {
        if (requestParms.type.includes(enums_1.COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES) &&
            !(requestParms === null || requestParms === void 0 ? void 0 : requestParms.coreSolutionId)) {
            throw new exceptions_1.HttpException('Capability Id is required for Location Ids.', enums_1.HttpStatus.BAD_REQUEST);
        }
        if ([enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION, enums_1.COMMON_DROPDOWN_TYPE.CONTACT_TYPE].some(type => requestParms.type.includes(type)) &&
            (!(requestParms === null || requestParms === void 0 ? void 0 : requestParms.coreSolutionId) || !(requestParms === null || requestParms === void 0 ? void 0 : requestParms.locationTypeId))) {
            throw new exceptions_1.HttpException('Capability Id & Location Type Id are required for Strategic Classification & Contact Type.', enums_1.HttpStatus.BAD_REQUEST);
        }
    }
    groupResultsByType(results) {
        return results.reduce((acc, item) => {
            const type = item.type;
            if (!acc[type]) {
                acc[type] = [];
            }
            acc[type].push(item);
            return acc;
        }, {});
    }
    transformGroupedResults(groupedResults) {
        return Object.keys(groupedResults).reduce((acc, key) => {
            acc[key] = (0, helpers_1.multiObjectToInstance)(dtos_1.MetadataByTypeResponseDto, groupedResults[key]);
            return acc;
        }, {});
    }
    transformGroupedResultsOfLocationLifecycleManagements(groupedResults) {
        return Object.keys(groupedResults).reduce((acc, key) => {
            acc[key] = (0, helpers_1.multiObjectToInstance)(dtos_1.LocationLifecycleTypeResponseDto, groupedResults[key]);
            return acc;
        }, {});
    }
    getUniqueTags(type, searchTerm) {
        return __awaiter(this, void 0, void 0, function* () {
            if (type === 'CAPABILITY') {
                return this.locationWiseCapabilityRepo.getUniqueTags(searchTerm);
            }
            else if (type === 'LOCATION') {
                return this.locationRepository.getUniqueTags(searchTerm);
            }
            else {
                throw new exceptions_1.HttpException('Invalid type', enums_1.HttpStatus.BAD_REQUEST);
            }
        });
    }
};
MetadataService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.CoreSolutionRepository,
        repositories_1.LocationTypeRepository,
        repositories_1.CommonDropdownRepository,
        repositories_1.LocationLifecycleManagementsTypeRepository,
        repositories_2.LocationRepository,
        repositories_3.LocationWiseCapabilityRepository])
], MetadataService);
exports.MetadataService = MetadataService;
//# sourceMappingURL=metadata.services.js.map