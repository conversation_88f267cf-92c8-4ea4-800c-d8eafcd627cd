import * as msal from '@azure/msal-node';
import { ConfigService } from 'src/config/config.service';
import { HttpService } from 'src/shared/services';
export declare const MSGraphProviders: ({
    provide: string;
    useFactory: (configService: ConfigService) => Promise<msal.ConfidentialClientApplication>;
    inject: (typeof ConfigService)[];
} | {
    provide: string;
    useFactory: (configService: ConfigService) => HttpService;
    inject: (typeof ConfigService)[];
})[];
