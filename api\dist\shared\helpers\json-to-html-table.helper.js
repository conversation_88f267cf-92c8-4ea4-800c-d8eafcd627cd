"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTableCSS = exports.ReplaceUrlVariable = exports.filterTableData = exports.jsonToHtmlTable = void 0;
function jsonToHtmlTable(options = {}) {
    let tableData = options.data || [];
    let header = options.header;
    const border = isDefined(options.border) ? options.border : 1;
    const cellspacing = isDefined(options.cellspacing) ? options.cellspacing : 0;
    const cellpadding = isDefined(options.cellpadding) ? options.cellpadding : 0;
    const tableId = options.table_id || 'tablify';
    const tableClass = options.table_class || 'tablify';
    const headerMapping = options.header_mapping || {};
    let pretty = options.pretty;
    if (pretty === undefined) {
        pretty = true;
    }
    let isSingleRow = false;
    if (!Array.isArray(tableData)) {
        isSingleRow = true;
        tableData = [tableData];
    }
    if (!Array.isArray(header)) {
        const headerObj = {};
        tableData.forEach(json => {
            const keys = Object.keys(json);
            keys.forEach(key => {
                headerObj[key] = true;
            });
        });
        header = Object.keys(headerObj);
    }
    if (isSingleRow && tableData.length === 1) {
        header = header.filter(h => tableData[0][h]);
    }
    let htmlTable = '';
    let cellArray = [];
    let cellRow = [];
    cellArray.push(cellRow);
    header.forEach(key => {
        cellRow.push(`<th>${headerMapping[key] || key}</th>`);
    });
    tableData.forEach(json => {
        cellRow = [];
        cellArray.push(cellRow);
        header.forEach(key => {
            let value = json[key];
            if (value === undefined) {
                value = '';
            }
            else if (typeof value !== 'string') {
                value = JSON.stringify(value);
            }
            cellRow.push(`<td>${value}</td>`);
        });
    });
    let i;
    let j;
    if (isSingleRow && cellArray.length) {
        cellArray = cellArray[0].map((col, i) => {
            return cellArray.map(row => {
                return row[i];
            });
        });
    }
    let newLine = '';
    let indent = '';
    if (pretty) {
        newLine = '\n';
        indent = '  ';
    }
    if (options.css) {
        htmlTable += `<style>${newLine}${indent}${options.css}${newLine}</style>${newLine}`;
    }
    if (tableData.length) {
        htmlTable += `<table id="${tableId}" class="${tableClass}" border="${border}" cellspacing="${cellspacing}" cellpadding="${cellpadding}">`;
        for (i = 0; i < cellArray.length; i++) {
            htmlTable += newLine;
            htmlTable += indent;
            htmlTable += '<tr>';
            for (j = 0; j < cellArray[i].length; j++) {
                htmlTable += newLine;
                htmlTable += indent;
                htmlTable += indent;
                htmlTable += cellArray[i][j];
            }
            htmlTable += newLine;
            htmlTable += indent;
            htmlTable += '</tr>';
        }
        htmlTable += newLine;
        htmlTable += '</table>';
    }
    return htmlTable;
}
exports.jsonToHtmlTable = jsonToHtmlTable;
function isDefined(x) {
    return x !== undefined && x !== null;
}
function filterTableData(data, placeholderFields) {
    const tableData = data.map(obj => {
        const filteredObj = Object.fromEntries(Object.entries(obj).filter(([key]) => placeholderFields.includes(key)));
        return filteredObj;
    });
    return tableData;
}
exports.filterTableData = filterTableData;
const ReplaceUrlVariable = (url, variables) => {
    Object.keys(variables).forEach((variableKey) => {
        var re = new RegExp(':' + variableKey, 'g');
        url = url.replace(re, variables[variableKey]);
    });
    return url;
};
exports.ReplaceUrlVariable = ReplaceUrlVariable;
const getTableCSS = () => {
    return `
		table {
			border-collapse: collapse;
			width: 100%;
		}
		th, td {
			border: 1px solid #333;
			padding: 8px;
			text-align: left;
		}
		th {
			background-color: #10004F;
			color: #fff;
		}
	`;
};
exports.getTableCSS = getTableCSS;
//# sourceMappingURL=json-to-html-table.helper.js.map