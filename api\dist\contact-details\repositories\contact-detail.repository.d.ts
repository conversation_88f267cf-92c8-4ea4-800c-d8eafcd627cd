import { BaseRepository } from 'src/shared/repositories';
import { ContactDetail } from '../models';
import { CurrentContext } from 'src/shared/types';
import { UserDetail } from 'src/shared/interfaces';
import { PaginationParms } from 'src/core/pagination';
export declare class ContactDetailRepository extends BaseRepository<ContactDetail> {
    constructor();
    getContactDetailsByCondition(condition: any): Promise<ContactDetail[]>;
    getDetailedContactsByCondition(condition: any): Promise<ContactDetail[]>;
    getContactDetailByCondition(condition: any): Promise<ContactDetail>;
    addHierarchyContact(payload: any, currentContext: CurrentContext): Promise<ContactDetail | null>;
    updateUserDetailsByCondition(condition: any, userDetails: UserDetail[], currentContext: CurrentContext): Promise<number | null>;
    getPaginatedContactListByCondition(paginationParms: PaginationParms, condition: any): Promise<{
        rows: ContactDetail[];
        count: number;
    }>;
    getPaginatedContactAsIndividualUserByCondition(paginationParms: PaginationParms, condition: any): Promise<{
        count: number;
        rows: any[];
    }>;
    getContactAsIndividualUserByCondition(condition: any, transform?: boolean): Promise<any>;
    addBulkContacts(records: any, currentContext: CurrentContext): Promise<ContactDetail[]>;
    deleteByIds(ids: number[], currentContext: CurrentContext): Promise<boolean>;
    deleteByLocationId(locationId: number, currentContext: CurrentContext): Promise<boolean>;
}
