import { FormControlLabel, Switch, Tooltip, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

interface LegHeadersProps {
  showAvailable: boolean;
  handleChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  legs: Array<{
    code: string;
    fullName: string;
    color: string;
    capabilities: any[];
  }>;
  columnWidth: number;
}

export const LegHeaders = ({ showAvailable, handleChange, legs, columnWidth }: LegHeadersProps) => {
  const { t } = useTranslation();

  return (
    <tr>
      <th
        style={{
          width: 300,
          minWidth: 300,
          backgroundColor: '#f5f5f5',
          border: '1px solid #e0e0e0',
          textAlign: 'left',
          padding: '5px 16px',
          height: 40,
          position: 'sticky',
          left: 0,
          zIndex: 25,
          pointerEvents: 'auto',
        }}
      >
        <FormControlLabel
          label={t('label.show_available')}
          labelPlacement="start"
          control={<Switch checked={showAvailable} onChange={handleChange} />}
        />
      </th>
      {legs?.map((leg) => <LegHeader key={leg.code + leg.fullName} leg={leg} columnWidth={columnWidth} />)}
    </tr>
  );
};

interface LegHeaderProps {
  leg: {
    code: string;
    fullName: string;
    color: string;
    capabilities: any[];
  };
  columnWidth: number;
}

const LegHeader = ({ leg, columnWidth }: LegHeaderProps) => (
  <th
    colSpan={leg.capabilities?.length}
    style={{
      backgroundColor: leg.color,
      border: '1px solid #e0e0e0',
      textAlign: 'center',
      padding: '4px 8px',
      height: 30,
      width: leg.capabilities?.length * columnWidth,
      minWidth: leg.capabilities?.length * columnWidth,
      maxWidth: leg.capabilities?.length * columnWidth,
    }}
  >
    <Tooltip title={leg.fullName} arrow>
      <Typography
        variant="subtitle2"
        fontWeight="bold"
        sx={{
          fontFamily: 'Pilat Demi',
          fontSize: '0.975rem',
          color: 'white',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: '100%',
          lineHeight: '1.2',
        }}
      >
        {leg.fullName.length > 40 ? `${leg.fullName.substring(0, 40)}...` : leg.fullName}
      </Typography>
    </Tooltip>
  </th>
);
