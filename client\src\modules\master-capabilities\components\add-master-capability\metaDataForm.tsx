import { useTranslation } from 'react-i18next';

import { Loading<PERSON>utton } from '@mui/lab';
import {
  Box,
  Checkbox,
  Chip,
  FormControl,
  FormHelperText,
  Grid2,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
} from '@mui/material';
import { RHFSelect, RHFTextField } from '@/components/hook-form';
import {
  EVIDENC_TYPE_ENUM,
  EVIDENC_TYPE_ENUM_DISPLAY,
  METADATA_TYPE_ENUM,
  METADATA_TYPE_ENUM_DISPLAY,
} from '@/shared/enum/capability.enum';
import * as Yup from 'yup';

import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, useForm } from 'react-hook-form';

import { useLoading } from '@/hooks/use-loading';
import CustomModal from '@/components/custom-modal/custom-modal';
import { CloseIcon } from 'yet-another-react-lightbox';
import { useMutation, useQuery } from 'react-query';
import { enqueueSnackbar } from 'notistack';
import { addNewMetadata, getCoreSolutions, getMasterCapabilityDropdowns, upsertEvidences } from '@/shared/services';
import { MASTER_CAPABILITY_DROPDOWN } from '@/shared/models';
import { useEffect, useState } from 'react';

export default function MetaDataForm({ openMetaDataForm, setOpenMetaDataForm }: any) {
  const { t } = useTranslation();

  const { setLoading, setMessage } = useLoading();

  const { data: coreSolutionsData } = useQuery({
    queryKey: ['coresolutions'],
    queryFn: getCoreSolutions,
    onSuccess: () => {},
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
  });

  const { mutateAsync: metadataMutateAsync } = useMutation({
    mutationFn: addNewMetadata,
    onSuccess: (response: any) => {
      // queryClient.invalidateQueries('capability-list');

      if (setLoading && setMessage) {
        setLoading(false);
        setMessage('');
        setOpenMetaDataForm(false);
      }
      enqueueSnackbar(response.message, {
        variant: 'success',
      });
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );

      if (setLoading && setMessage) {
        setLoading(false);
        setMessage('');
      }
    },
  });

  const {
    mutateAsync: masterDropdownMutateAsync,
    data: masterCapabilityData,
    isLoading: isLoadingMasterData,
  } = useMutation({
    mutationFn: getMasterCapabilityDropdowns,
    onSuccess: (response: any) => {},
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );

      if (setLoading && setMessage) {
        setLoading(false);
        setMessage('');
      }
    },
  });

  useEffect(() => {
    masterDropdownMutateAsync({
      types: [MASTER_CAPABILITY_DROPDOWN.LEGS],
    });
  }, []);

  const metadataSchema = Yup.object().shape({
    name: Yup.string().default('').required(t('messages.field_is_required')),

    type: Yup.string()
      .oneOf(Object.values(METADATA_TYPE_ENUM), t('messages.invalid_selection'))
      .required(t('messages.field_is_required')),

    legs: Yup.array()
      .default([])
      .when('type', {
        is: (val: string) => val === METADATA_TYPE_ENUM.CATEGORY,
        then: (schema) => schema.min(1, t('messages.min_1_needs_selection')).required(t('messages.field_is_required')),
        otherwise: (schema) => schema.notRequired(),
      }),
    code: Yup.string()
      .default('')
      .when('type', {
        is: (val: METADATA_TYPE_ENUM) => typesToShowCode.includes(val),
        then: (schema) => schema.required(t('messages.field_is_required')),
        otherwise: (schema) => schema.notRequired(),
      }),

    coreSolutionId: Yup.string(),
    locationTypeId: Yup.string(),
    offset: Yup.string(),
  });

  const methods = useForm({
    resolver: yupResolver(metadataSchema),
    defaultValues: {
      type: METADATA_TYPE_ENUM.PRODUCT,
      legs: [],
    },
  });

  const {
    clearErrors,
    reset,
    watch,
    formState: { errors },
    setValue,
  } = methods;

  const typeMapping = () => {
    return Object.entries(METADATA_TYPE_ENUM)?.map(([key, value], index) => (
      <MenuItem key={index} value={key}>
        {METADATA_TYPE_ENUM_DISPLAY[value]}
      </MenuItem>
    ));
  };

  const [locationTypes, setLocationTypes] = useState<any>([]);

  const coreSolutionMappingHandler = () => {
    return coreSolutionsData?.map((value, key) => (
      <MenuItem key={key} value={value.id}>
        {value.title}
      </MenuItem>
    ));
  };

  const locationTypeMappingHandler = () => {
    return locationTypes?.map((value: any, key: number) => (
      <MenuItem key={key} value={value.id}>
        {value.title}
      </MenuItem>
    ));
  };

  const onSubmit = methods.handleSubmit(
    (data) => {
      if (data) {
        if (setLoading && setMessage) {
          setLoading(true);
          setMessage(t('submitting'));
        }

        let payload: any = {
          name: data.name,
          type: METADATA_TYPE_ENUM[data.type],
          ...(data.code && { code: data.code }),
          ...(data.offset && { offset: data.offset }),
          ...(data.coreSolutionId && { coreSolutionId: Number(data.coreSolutionId) }),
          ...(data.locationTypeId && { locationTypeId: Number(data.locationTypeId) }),
        };

        if (data?.legs?.length) {
          payload['legIds'] = data.legs;
        }

        metadataMutateAsync(payload);
      }
    },
    (error) => {
      console.log(error);
    },
  );

  const typesToShowCode = [METADATA_TYPE_ENUM.CURRENCY, METADATA_TYPE_ENUM.LANGUAGE, METADATA_TYPE_ENUM.TIMEZONE];

  return (
    <CustomModal
      isOpen={openMetaDataForm}
      handleClose={() => {
        setOpenMetaDataForm(false);
      }}
      innerBoxStyle={{
        overflow: 'auto',
        maxHeight: '550px',
      }}
    >
      <Stack direction={'row'} justifyContent={'space-between'}>
        <Typography variant="mainTitle">{t('label.setup_metadata')}</Typography>
        <div
          onClick={() => {
            setOpenMetaDataForm(false);
            reset();
            clearErrors();
          }}
          style={{ cursor: 'pointer' }}
        >
          <CloseIcon />
        </div>
      </Stack>
      <FormProvider {...methods}>
        <Grid2 container size={12} mt={3} spacing={2}>
          <Grid2 size={{ sm: 12, md: 6 }}>
            <FormControl fullWidth>
              <RHFSelect name="type" label={t('label.type')} required>
                {typeMapping()}
              </RHFSelect>
            </FormControl>
          </Grid2>
          <Grid2 size={{ sm: 12, md: 6 }}>
            <FormControl fullWidth>
              <RHFTextField name="name" label={t('label.name')} required />
            </FormControl>
          </Grid2>
          {watch('type') === METADATA_TYPE_ENUM.CATEGORY && (
            <Grid2 size={{ sm: 12, md: 6 }}>
              <FormControl sx={{ width: '100%' }} error={!!errors?.legs?.message}>
                <InputLabel shrink sx={{ backgroundColor: '#fff', px: 1 }} id="legs-label">
                  {`${t('label.legs')}*`}
                </InputLabel>

                <Select
                  labelId="legs-label"
                  id="legs"
                  multiple
                  value={watch('legs') || []}
                  onChange={(e: SelectChangeEvent<number[]>) => {
                    const selectedIds =
                      typeof e.target.value === 'string' ? e.target.value.split(',').map(Number) : e.target.value;
                    setValue('legs', selectedIds, { shouldValidate: true });
                    clearErrors('legs');
                  }}
                  input={<OutlinedInput label={t('label.legs')} />}
                  renderValue={(selected: number[]) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((id) => {
                        const match = masterCapabilityData?.LEGS?.find((leg: any) => leg.id === id);
                        return match ? (
                          <Chip
                            sx={{ fontSize: 12, borderRadius: '5px', border: 1 }}
                            size="small"
                            key={id}
                            label={match.fullName}
                          />
                        ) : null;
                      })}
                    </Box>
                  )}
                  MenuProps={{ PaperProps: { style: { maxHeight: 300 } } }}
                >
                  {masterCapabilityData?.LEGS?.map((leg: any) => (
                    <MenuItem key={leg.id} value={leg.id}>
                      <Checkbox checked={watch('legs')?.includes(leg.id)} />
                      <ListItemText primary={leg.fullName} />
                    </MenuItem>
                  ))}
                </Select>

                {errors?.legs?.message && <FormHelperText error>{errors?.legs?.message}</FormHelperText>}
              </FormControl>
            </Grid2>
          )}

          {typesToShowCode.includes(watch('type')) && (
            <Grid2 size={{ sm: 12, md: 6 }}>
              <FormControl fullWidth>
                <RHFTextField name="code" label={t('label.code')} required />
              </FormControl>
            </Grid2>
          )}

          {watch('type') === METADATA_TYPE_ENUM.STRATEGIC_CLASSIFICATION && (
            <>
              <Grid2 size={{ sm: 12, md: 6 }}>
                <FormControl fullWidth>
                  <RHFSelect
                    name="coreSolutionId"
                    onChange={(e) => {
                      const selectedId = e.target.value;
                      if (e.target.value == 'all') {
                        setValue('locationTypeId', '');
                        setLocationTypes([]);
                      } else {
                        const selectedCore = coreSolutionsData?.find((item) => item.id === Number(selectedId));
                        setValue('coreSolutionId', selectedId);
                        setLocationTypes(selectedCore?.locationTypes);
                      }

                      return e;
                    }}
                    label={t('label.core_solution')}
                  >
                    <MenuItem value="all">{t('label.all')}</MenuItem>
                    {coreSolutionMappingHandler()}
                  </RHFSelect>
                </FormControl>
              </Grid2>
              {locationTypes?.length > 0 && (
                <Grid2 size={{ sm: 12, md: 6 }}>
                  <FormControl fullWidth>
                    <RHFSelect name="locationTypeId" label={t('label.location_type')}>
                      <MenuItem value="all">{t('label.all')}</MenuItem>
                      {locationTypeMappingHandler()}
                    </RHFSelect>
                  </FormControl>
                </Grid2>
              )}
            </>
          )}

          {watch('type') === METADATA_TYPE_ENUM.TIMEZONE && (
            <Grid2 size={{ sm: 12, md: 6 }}>
              <FormControl fullWidth>
                <RHFTextField name="offset" label={t('label.offset')} />
              </FormControl>
            </Grid2>
          )}
        </Grid2>
        <Grid2 size={12} display="flex" justifyContent="flex-end" gap={1} mt={3}>
          <LoadingButton type="submit" variant="contained" onClick={onSubmit} sx={{ maxWidth: 200 }}>
            {t('btn_name.submit')}
          </LoadingButton>
        </Grid2>
      </FormProvider>
    </CustomModal>
  );
}
