import React, { useContext } from 'react';
import { useMsal, useAccount } from '@azure/msal-react';
import { InteractionRequiredAuthError, BrowserAuthError } from '@azure/msal-browser';
import { AppConfigContext } from '../contexts';
import axios from '@/shared/utils/axios';

interface RequestInterceptorProps {
  children: JSX.Element;
}

const RequestInterceptor: React.FC<RequestInterceptorProps> = ({ children }: RequestInterceptorProps) => {
  const { instance, accounts } = useMsal();
  const appConfig = useContext(AppConfigContext);
  const account = useAccount(accounts[0]);

  /* eslint-disable no-param-reassign */
  axios.interceptors.request.use(
    async (config) => {
      if (!account) {
        throw Error('No active account! Verify a user has been signed in.');
      }

      try {
        // Try silent token acquisition first
        const response = await instance.acquireTokenSilent({
          scopes: appConfig.msDetail.scope,
          account,
          forceRefresh: false, // Allow cached tokens
        });

        const bearer = `Bearer ${response.accessToken}`;
        config.headers.Authorization = bearer;

        return config;
      } catch (error) {
        console.warn('Silent token acquisition failed:', error);

        // Handle specific MSAL errors
        if (error instanceof InteractionRequiredAuthError) {
          // Token expired or interaction required - redirect to login
          try {
            await instance.acquireTokenRedirect({
              scopes: appConfig.msDetail.scope,
              account,
            });
          } catch (redirectError) {
            console.error('Token redirect failed:', redirectError);
            // Fallback: reload the page to trigger fresh authentication
            window.location.reload();
          }
        } else if (error instanceof BrowserAuthError) {
          // Handle browser-specific errors (including timeout)
          if (error.errorCode === 'monitor_window_timeout') {
            console.warn('Token acquisition timeout - attempting force refresh');
            try {
              // Force refresh the token
              const response = await instance.acquireTokenSilent({
                scopes: appConfig.msDetail.scope,
                account,
                forceRefresh: true,
              });

              const bearer = `Bearer ${response.accessToken}`;
              config.headers.Authorization = bearer;

              return config;
            } catch (forceRefreshError) {
              console.error('Force refresh failed:', forceRefreshError);
              // Last resort: redirect to login
              await instance.acquireTokenRedirect({
                scopes: appConfig.msDetail.scope,
                account,
              });
            }
          } else {
            // Other browser auth errors
            console.error('Browser auth error:', error);
            throw error;
          }
        } else {
          // Unknown error - rethrow
          console.error('Unknown authentication error:', error);
          throw error;
        }
      }

      return config;
    },
    (error) => {
      // Request error handler
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );
  /* eslint-enable no-param-reassign */
  return <>{children}</>;
};

export default RequestInterceptor;
