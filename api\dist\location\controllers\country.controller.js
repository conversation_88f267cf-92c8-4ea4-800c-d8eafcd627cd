"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CountryController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const passport_1 = require("@nestjs/passport");
const enums_1 = require("../../shared/enums");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const dtos_2 = require("../dtos");
const services_1 = require("../services");
let CountryController = class CountryController {
    constructor(countryService, legalEntityService) {
        this.countryService = countryService;
        this.legalEntityService = legalEntityService;
    }
    getCountryDetail(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.countryService.getCountryDetailWithLegalEntity(entityId);
        });
    }
    upsertCountry(request, newCountrySetupRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.countryService.upsertCountry(newCountrySetupRequestDto, request.currentContext);
        });
    }
    upsertLegalEntity(request, legalEntitySetupRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.legalEntityService.upsertLegalEntity(legalEntitySetupRequestDto, request.currentContext);
        });
    }
    deleteLegalEntity(request, legalEntityId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.legalEntityService.deleteLegalEntity(legalEntityId, request.currentContext);
        });
    }
    getAllLegalEntity(countryId, searchTerm) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!countryId || Number.isNaN(Number(countryId))) {
                throw new common_1.HttpException('Country ID is required and must be a valid number', common_1.HttpStatus.BAD_REQUEST);
            }
            return this.legalEntityService.getLegalEntityList(countryId, searchTerm);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get country detail with legal entities by country entity Id.',
        type: dtos_2.CountryWithEntityResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Country entity id to get the detail.',
        required: true,
    }),
    (0, common_1.Get)('/detail/:entityId'),
    __param(0, (0, common_1.Param)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CountryController.prototype, "getCountryDetail", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.GLOBAL_MANAGE, { checkEntity: true }),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Add New or Update Existing Country Setup.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/upsert'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.NewCountrySetupRequestDto]),
    __metadata("design:returntype", Promise)
], CountryController.prototype, "upsertCountry", null);
__decorate([
    (0, decorators_1.Permissions)([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.GLOBAL_LEGAL], { checkEntity: true }),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Add New or Update Existing Legal Entity.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/legal-entity/upsert'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.LegalEntitySetupRequestDto]),
    __metadata("design:returntype", Promise)
], CountryController.prototype, "upsertLegalEntity", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete Legal Entity.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)('/legal-entity/:legalEntityId'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('legalEntityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], CountryController.prototype, "deleteLegalEntity", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Legal Entity.',
        type: [dtos_2.LegalEntityDropdownResponseDto],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchTerm',
        type: String,
        description: 'Search by name.',
        required: false,
        allowEmptyValue: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'countryId',
        type: String,
        description: 'Country ID to filter legal entities.',
        required: true,
        allowEmptyValue: false,
    }),
    (0, common_1.Get)('/legal-entity'),
    __param(0, (0, common_1.Query)('countryId')),
    __param(1, (0, common_1.Query)('searchTerm')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], CountryController.prototype, "getAllLegalEntity", null);
CountryController = __decorate([
    (0, swagger_1.ApiTags)('Country Management APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('country'),
    __metadata("design:paramtypes", [services_1.CountryService,
        services_1.LegalEntityService])
], CountryController);
exports.CountryController = CountryController;
//# sourceMappingURL=country.controller.js.map