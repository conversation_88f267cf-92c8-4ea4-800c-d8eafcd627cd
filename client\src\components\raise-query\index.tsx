import { useLoading } from '@/hooks/use-loading';
import { useTranslate } from '@/locales/use-locales';
import { upsertQuery } from '@/shared/services/support.service';
import { yupResolver } from '@hookform/resolvers/yup';
import HelpOutline from '@mui/icons-material/HelpOutline';
import { LoadingButton } from '@mui/lab';
import { Divider, FormControl, IconButton, Popover, Stack, Tooltip, Typography } from '@mui/material';
import { enqueueSnackbar } from 'notistack';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation } from 'react-query';
import { useLocation } from 'react-router';
import * as Yup from 'yup';
import FormProvider, { RHFTextField } from '../hook-form';
import Iconify from '../iconify';

export default function SupportQuery() {
  const { t } = useTranslate();
  const location = useLocation();
  const url = window.location.origin + location.pathname + location.search;
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const { setLoading, setMessage } = useLoading();
  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('submitting'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };

  const Schema = Yup.object().shape({
    query: Yup.string().required(t('error_messages.query_required')).default(''),
  });

  const methods = useForm({
    resolver: yupResolver(Schema),
    mode: 'onSubmit',
  });

  const {
    reset,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
  } = methods;

  const { mutateAsync } = useMutation({
    mutationFn: (data: any) => upsertQuery(data),
    onSuccess: (response: any) => {
      enqueueSnackbar(response.message, {
        variant: 'success',
      });
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });

  const onSubmit = handleSubmit(
    async (data) => {
      startLoadingState();
      try {
        await mutateAsync({ url, query: data.query });
        reset();
        setAnchorEl(null);
        stopLoadingState();
      } catch (err) {
        stopLoadingState();
        console.debug(err);
      }
    },
    (error) => {
      stopLoadingState();
      console.log(error);
    },
  );

  return (
    <>
      <Tooltip title={t('label.raise_a_query')} placement="top">
        <IconButton
          color="primary"
          onClick={(event) => setAnchorEl(event.currentTarget)}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            bgcolor: 'background.paper',
            boxShadow: 3,
            '&:hover': {
              bgcolor: 'grey.100',
            },
            zIndex: 1300,
          }}
        >
          <HelpOutline />
        </IconButton>
      </Tooltip>

      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={() => {
          reset();
          setAnchorEl(null);
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        slotProps={{
          backdrop: { invisible: true },
          paper: {
            sx: { width: '90%', maxWidth: 420, mx: 1 },
          },
        }}
      >
        <Stack direction="row" alignItems="center" spacing={2} sx={{ paddingX: 2 }}>
          <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
            {t('label.raise_a_query')}
          </Typography>
          <IconButton
            size="small"
            onClick={() => {
              reset();
              setAnchorEl(null);
            }}
          >
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Stack>
        <Divider sx={{ mb: 2 }} />

        <FormProvider methods={methods} onSubmit={onSubmit}>
          <FormControl sx={{ paddingX: 1 }} fullWidth error={Boolean(errors.query)}>
            <RHFTextField
              id="query"
              multiline
              minRows={4}
              label={`${t('label.query')}*`}
              name="query"
              value={getValues('query') ?? ''}
              onChange={(event) => {
                setValue('query', event.target.value, { shouldValidate: true });
              }}
              placeholder={`${t('placeholder.please_enter')} ${t('label.query')}`}
            />
          </FormControl>
          <Stack direction="row" justifyContent="end" mt={2} mb={1} mr={1} spacing={1}>
            <LoadingButton onClick={onSubmit} variant="contained">
              {t('btn_name.send')}
            </LoadingButton>
          </Stack>
        </FormProvider>
      </Popover>
    </>
  );
}
