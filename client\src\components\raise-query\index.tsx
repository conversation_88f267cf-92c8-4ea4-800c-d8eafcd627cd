import { useLoading } from '@/hooks/use-loading';
import { useTranslate } from '@/locales/use-locales';
import { queryClient } from '@/shared/services';
import { upsertQuery } from '@/shared/services/support.service';
import { yupResolver } from '@hookform/resolvers/yup';
import { HelpOutline } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import { Button, Divider, FormControl, IconButton, Popover, Stack, Tooltip, Typography, useTheme, useMediaQuery, Fade, Box } from '@mui/material';
import { enqueueSnackbar } from 'notistack';
import { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation } from 'react-query';
import { useLocation } from 'react-router';
import * as Yup from 'yup';
import FormProvider, { RHFTextField } from '../hook-form';
import Iconify from '../iconify';

export default function SupportQuery() {
  const { t } = useTranslate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const location = useLocation();
  const url = window.location.origin + location.pathname + location.search;
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const popoverRef = useRef<HTMLDivElement | null>(null);
  const [queryLength, setQueryLength] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [isCompactMode, setIsCompactMode] = useState(false);
  const { setLoading, setMessage } = useLoading();

  // Smart visibility and positioning based on scroll behavior and viewport
  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      // Hide button while actively scrolling
      setIsVisible(false);

      // Show button after scroll stops
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        setIsVisible(true);
      }, 150);
    };

    const handleResize = () => {
      // Enable compact mode on smaller screens or when viewport height is limited
      const shouldUseCompactMode = window.innerHeight < 600 || window.innerWidth < 768;
      setIsCompactMode(shouldUseCompactMode);
    };

    // Initial check
    handleResize();

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      clearTimeout(scrollTimeout);
    };
  }, []);

  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('submitting'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };

  const Schema = Yup.object().shape({
    query: Yup.string().required(t('error_messages.query_required')).default(''),
  });

  const methods = useForm({
    resolver: yupResolver(Schema),
    mode: 'onSubmit',
  });

  const {
    reset,
    handleSubmit,
    formState: { errors },
    setValue,
  } = methods;

  useEffect(() => {
    if (popoverRef.current) {
      const observer = new ResizeObserver(() => {
        popoverRef.current?.style.setProperty('transform', 'scale(1)');
      });
      observer.observe(popoverRef.current);
      return () => observer.disconnect();
    }
  }, []);

  const { mutateAsync } = useMutation({
    mutationFn: (data: any) => upsertQuery(data),
    onSuccess: (response: any) => {
      enqueueSnackbar(response.message, {
        variant: 'success',
      });
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });

  const onSubmit = handleSubmit(
    async (data) => {
      startLoadingState();
      try {
        await mutateAsync({ url, query: data.query });
        reset();
        setAnchorEl(null);
        await queryClient.invalidateQueries(['support-query-list']);
      } catch (err) {
        console.debug(err);
      } finally {
        stopLoadingState();
      }
    },
    (error) => {
      stopLoadingState();
      console.log(error);
    },
  );

  // Option 1: Collapsible Side Panel
  const renderSidePanel = () => (
    <Box
      sx={{
        position: 'fixed',
        top: '50%',
        right: isVisible ? 0 : -60,
        transform: 'translateY(-50%)',
        zIndex: 1050,
        transition: 'right 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      }}
    >
      <Tooltip title={t('label.raise_a_query')} placement="left">
        <Button
          variant="contained"
          onClick={(event) => setAnchorEl(event.currentTarget)}
          sx={{
            height: 60,
            width: 60,
            borderRadius: '12px 0 0 12px',
            bgcolor: (theme) => theme.palette.primary.main,
            color: 'white',
            boxShadow: (theme) => `-4px 0 20px ${theme.palette.primary.main}30`,
            border: 'none',
            minWidth: 0,
            padding: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '&:hover': {
              bgcolor: (theme) => theme.palette.primary.dark,
              transform: 'translateX(-4px)',
              boxShadow: (theme) => `-8px 0 30px ${theme.palette.primary.main}40`,
            },
            '&:active': {
              transform: 'translateX(-2px)',
            },
          }}
        >
          <HelpOutline sx={{ fontSize: 24 }} />
        </Button>
      </Tooltip>
    </Box>
  );

  // Option 2: Header Integration (Compact)
  const renderHeaderButton = () => (
    <Tooltip title={t('label.raise_a_query')} placement="bottom">
      <IconButton
        onClick={(event) => setAnchorEl(event.currentTarget)}
        sx={{
          color: (theme) => theme.palette.primary.main,
          bgcolor: 'transparent',
          border: `1px solid ${theme.palette.primary.main}30`,
          borderRadius: 2,
          padding: 1,
          transition: 'all 0.2s ease',
          '&:hover': {
            bgcolor: (theme) => `${theme.palette.primary.main}08`,
            borderColor: (theme) => theme.palette.primary.main,
            transform: 'scale(1.05)',
          },
        }}
      >
        <HelpOutline sx={{ fontSize: 20 }} />
      </IconButton>
    </Tooltip>
  );

  // Option 3: Sticky Top Banner (Minimal)
  const renderTopBanner = () => (
    <Box
      sx={{
        position: 'fixed',
        top: isVisible ? 0 : -50,
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 1050,
        transition: 'top 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        bgcolor: (theme) => theme.palette.primary.main,
        color: 'white',
        px: 3,
        py: 1,
        borderRadius: '0 0 12px 12px',
        boxShadow: (theme) => `0 4px 20px ${theme.palette.primary.main}30`,
      }}
    >
      <Button
        onClick={(event) => setAnchorEl(event.currentTarget)}
        sx={{
          color: 'white',
          textTransform: 'none',
          fontSize: '0.875rem',
          fontWeight: 500,
          minWidth: 0,
          padding: '4px 8px',
          '&:hover': {
            bgcolor: 'rgba(255,255,255,0.1)',
          },
        }}
        startIcon={<HelpOutline sx={{ fontSize: 18 }} />}
      >
        {t('label.need_help')}
      </Button>
    </Box>
  );

  // Option 4: Bottom Slide-up Tab
  const renderBottomTab = () => (
    <Box
      sx={{
        position: 'fixed',
        bottom: isVisible ? 0 : -40,
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 1050,
        transition: 'bottom 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      }}
    >
      <Button
        onClick={(event) => setAnchorEl(event.currentTarget)}
        sx={{
          bgcolor: (theme) => theme.palette.primary.main,
          color: 'white',
          borderRadius: '12px 12px 0 0',
          px: 3,
          py: 1,
          textTransform: 'none',
          fontSize: '0.875rem',
          fontWeight: 500,
          boxShadow: (theme) => `0 -4px 20px ${theme.palette.primary.main}30`,
          '&:hover': {
            bgcolor: (theme) => theme.palette.primary.dark,
            transform: 'translateY(-2px)',
          },
        }}
        startIcon={<HelpOutline sx={{ fontSize: 18 }} />}
      >
        {t('label.need_help')}
      </Button>
    </Box>
  );

  return (
    <>
      {/*
        CHOOSE ONE OF THE FOLLOWING OPTIONS:

        Option 1: Side Panel - Slides from right edge, never overlaps content
        Option 2: Header Button - Integrate into header component (recommended)
        Option 3: Top Banner - Slides down from top
        Option 4: Bottom Tab - Slides up from bottom
      */}

      {/* Option 1: Side Panel (Current - No Overlap) */}
      {renderSidePanel()}

      {/* Option 2: Header Integration (Uncomment to use) */}
      {/* {renderHeaderButton()} */}

      {/* Option 3: Top Banner (Uncomment to use) */}
      {/* {renderTopBanner()} */}

      {/* Option 4: Bottom Tab (Uncomment to use) */}
      {/* {renderBottomTab()} */}

      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={() => {
          reset();
          setAnchorEl(null);
          setQueryLength(0); // reset length
        }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        transformOrigin={{ vertical: 'top', horizontal: 'center' }}
        slotProps={{
          backdrop: { invisible: true },
          paper: {
            ref: popoverRef,
            sx: {
              width: '90%',
              maxWidth: 420,
              mx: 1,
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              transition: 'max-height 0.2s ease',
            },
          },
        }}
      >
        <Stack direction="row" alignItems="center" spacing={2} sx={{ paddingX: 2 }}>
          <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
            {t('label.raise_a_query')}
          </Typography>
          <IconButton
            size="small"
            onClick={() => {
              reset();
              setAnchorEl(null);
            }}
          >
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Stack>
        <Divider sx={{ mb: 2 }} />

        <FormProvider methods={methods} onSubmit={onSubmit}>
          <FormControl sx={{ paddingX: 1 }} fullWidth error={Boolean(errors.query)}>
            <RHFTextField
              id="query"
              multiline
              minRows={4}
              label={`${t('label.query')}*`}
              sx={{
                '& textarea': {
                  textAlign: 'justify',
                },
              }}
              onChange={(e) => {
                setValue('query', e.target.value, { shouldValidate: true });
                setQueryLength(e.target.value.length);
              }}
              name="query"
              placeholder={`${t('placeholder.please_enter')} ${t('placeholder.your_query')}`}
            />
          </FormControl>
          <Stack direction="row" justifyContent="end" mt={2} mb={1} mr={1} spacing={1}>
            <LoadingButton onClick={onSubmit} variant="contained">
              {t('btn_name.send')}
            </LoadingButton>
          </Stack>
        </FormProvider>
      </Popover>
    </>
  );
}
