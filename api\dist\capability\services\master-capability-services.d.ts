import { CapabilityLegRepository, CapabilityRepository, MasterCapabilityDropdownRepository, MasterEvidenceRepository } from '../repositories';
import { DatabaseHelper } from 'src/shared/helpers';
import { HistoryApiClient } from 'src/shared/clients';
import { CAPABILITY_DROPDOWN_LEVEL_ENUM, COMMON_DROPDOWN_TYPE } from 'src/shared/enums';
import { CapabilityCategoryRepository } from '../repositories/capability-category.repository';
import { MasterCapabilityDropdownRequestDto, MasterCapabilityFilterRequestDto, PaginatedMasterCapabilityListResponseDto, SetupNewMasterCapabilityRequestDto, SetupNewMasterEvidenceRequestDto } from '../dtos';
import { CurrentContext } from 'src/shared/types';
import { SetupNewMetadataRequestDto } from '../dtos/request/setup-metadata-request.dto';
import { METADATA_TYPE_ENUM } from 'src/shared/enums/metadata.enum';
import { CommonDropdownRepository } from 'src/metadata/repositories';
export declare class MasterCapabilityService {
    private readonly capabilityRepository;
    private readonly databaseHelper;
    private readonly historyService;
    private readonly capabilityLegRepository;
    private readonly capabilityCategoryRepository;
    private readonly masterCapabilityDropdownsRepository;
    private readonly masterEvidenceRepository;
    private readonly commonDropdownRepository;
    constructor(capabilityRepository: CapabilityRepository, databaseHelper: DatabaseHelper, historyService: HistoryApiClient, capabilityLegRepository: CapabilityLegRepository, capabilityCategoryRepository: CapabilityCategoryRepository, masterCapabilityDropdownsRepository: MasterCapabilityDropdownRepository, masterEvidenceRepository: MasterEvidenceRepository, commonDropdownRepository: CommonDropdownRepository);
    getMasterCapabilityDropdown(filterDto: MasterCapabilityDropdownRequestDto): Promise<{}>;
    searchMasterEvidences(searchTerm?: string): Promise<import("../models").MetaEvidence[]>;
    setupNewMasterCapability(requestPayload: SetupNewMasterCapabilityRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
        data: import("../models").MasterCapability;
    }>;
    setupNewMasterEvidence(requestPayload: SetupNewMasterEvidenceRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
        data: import("../models").MetaEvidence;
    }>;
    getMasterCapabilitiesByFilter(page: number, limit: number, filterDto?: MasterCapabilityFilterRequestDto): Promise<PaginatedMasterCapabilityListResponseDto>;
    getDropdownLevel(type: METADATA_TYPE_ENUM | COMMON_DROPDOWN_TYPE): CAPABILITY_DROPDOWN_LEVEL_ENUM;
    setupNewMetadata(requestPayload: SetupNewMetadataRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
        data: any;
    }>;
    setupNewMetadataOfNewTypes(requestPayload: SetupNewMetadataRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
        data: import("../../metadata/models").CommonDropdown;
    }>;
}
