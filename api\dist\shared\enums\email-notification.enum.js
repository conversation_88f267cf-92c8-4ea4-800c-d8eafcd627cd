"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UI_ROUTES = exports.NOTIFICATION_ENTITY_TYPE = exports.USER_EMAIL_GROUP = exports.EMAIL_TEMPLATES = void 0;
var EMAIL_TEMPLATES;
(function (EMAIL_TEMPLATES) {
    EMAIL_TEMPLATES["SUPPORT_EMAIL_NOTIFICATION"] = "SUPPORT.EMAIL";
    EMAIL_TEMPLATES["SCHEDULER_ENTRIES_NOTIFICATION"] = "SCHEDULER.ENTRIES.NOTIFICATION";
})(EMAIL_TEMPLATES = exports.EMAIL_TEMPLATES || (exports.EMAIL_TEMPLATES = {}));
var USER_EMAIL_GROUP;
(function (USER_EMAIL_GROUP) {
    USER_EMAIL_GROUP["PRODUCT_SUPPORT_EMAIL_GROUP"] = "ProductSupportEmailGroup";
})(USER_EMAIL_GROUP = exports.USER_EMAIL_GROUP || (exports.USER_EMAIL_GROUP = {}));
var NOTIFICATION_ENTITY_TYPE;
(function (NOTIFICATION_ENTITY_TYPE) {
    NOTIFICATION_ENTITY_TYPE["SUPPORT_EMAIL_NOTIFICATION"] = "SUPPORT.EMAIL.NOTIFICATION";
    NOTIFICATION_ENTITY_TYPE["SCHEDULER_ENTRIES_NOTIFICATION"] = "SCHEDULER.ENTRIES.NOTIFICATION";
})(NOTIFICATION_ENTITY_TYPE = exports.NOTIFICATION_ENTITY_TYPE || (exports.NOTIFICATION_ENTITY_TYPE = {}));
var UI_ROUTES;
(function (UI_ROUTES) {
    UI_ROUTES["AVAILABLE_CAPABILITY_LIST"] = "/:locationId/capabilities";
    UI_ROUTES["CAPABILITY_DETAIL"] = "/:capabilityId";
})(UI_ROUTES = exports.UI_ROUTES || (exports.UI_ROUTES = {}));
//# sourceMappingURL=email-notification.enum.js.map