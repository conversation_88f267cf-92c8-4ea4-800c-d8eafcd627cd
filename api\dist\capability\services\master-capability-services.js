"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MasterCapabilityService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const helpers_1 = require("../../shared/helpers");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const capability_category_repository_1 = require("../repositories/capability-category.repository");
const dtos_1 = require("../dtos");
const lodash_1 = require("lodash");
const pagination_1 = require("../../core/pagination");
const metadata_enum_1 = require("../../shared/enums/metadata.enum");
const sequelize_1 = require("sequelize");
const repositories_2 = require("../../metadata/repositories");
let MasterCapabilityService = class MasterCapabilityService {
    constructor(capabilityRepository, databaseHelper, historyService, capabilityLegRepository, capabilityCategoryRepository, masterCapabilityDropdownsRepository, masterEvidenceRepository, commonDropdownRepository) {
        this.capabilityRepository = capabilityRepository;
        this.databaseHelper = databaseHelper;
        this.historyService = historyService;
        this.capabilityLegRepository = capabilityLegRepository;
        this.capabilityCategoryRepository = capabilityCategoryRepository;
        this.masterCapabilityDropdownsRepository = masterCapabilityDropdownsRepository;
        this.masterEvidenceRepository = masterEvidenceRepository;
        this.commonDropdownRepository = commonDropdownRepository;
    }
    getMasterCapabilityDropdown(filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { types } = filterDto;
            if (!(types === null || types === void 0 ? void 0 : types.length)) {
                throw new exceptions_1.HttpException('Types are required.', enums_1.HttpStatus.BAD_REQUEST);
            }
            let dropdowns = {};
            if (types.includes(enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES.LEGS)) {
                const allLegs = yield this.capabilityLegRepository.getAllLegs();
                dropdowns[enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES.LEGS] = allLegs;
            }
            if (types.includes(enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES.CATEGORY)) {
                const allCategories = yield this.capabilityCategoryRepository.getAllCategories();
                dropdowns[enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES.CATEGORY] = allCategories;
            }
            if (types.includes(enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES.PRODUCT) ||
                types.includes(enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES['SUB-CATEGORY'])) {
                const allProducts = yield this.masterCapabilityDropdownsRepository.getAllDropdowns(types.map(type => enums_1.CAPABILITY_DROPDOWN_LEVEL_ENUM[type]));
                if (types.includes(enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES['SUB-CATEGORY'])) {
                    dropdowns[enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES['SUB-CATEGORY']] = allProducts.filter(subCategory => {
                        return subCategory.level === enums_1.CAPABILITY_DROPDOWN_LEVEL_ENUM['SUB-CATEGORY'];
                    });
                }
                if (types.includes(enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES.PRODUCT)) {
                    dropdowns[enums_1.MASTER_CAPABILITY_DROPDOWN_TYPES.PRODUCT] = allProducts.filter(product => {
                        return product.level === enums_1.CAPABILITY_DROPDOWN_LEVEL_ENUM.PRODUCT;
                    });
                }
            }
            return dropdowns;
        });
    }
    searchMasterEvidences(searchTerm = '') {
        return __awaiter(this, void 0, void 0, function* () {
            const results = yield this.masterEvidenceRepository.getAllEvidences(searchTerm);
            return results;
        });
    }
    setupNewMasterCapability(requestPayload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const { categoryId, entryName, coreSolutionId, productId, subCategoryId, type, level, evidenceCode, isEvidenceMandatory, legIds, verticalCodes, } = requestPayload;
                let productTitle = null;
                let subCategoryTitle = null;
                if (productId || subCategoryId) {
                    let ids = [];
                    if (productId) {
                        ids.push(productId);
                    }
                    if (subCategoryId) {
                        ids.push(subCategoryId);
                    }
                    const data = yield this.masterCapabilityDropdownsRepository.getDataByIds(ids);
                    if ((data === null || data === void 0 ? void 0 : data.length) < ids.length) {
                        throw new exceptions_1.HttpException('Invalid product or sub category.', enums_1.HttpStatus.BAD_REQUEST);
                    }
                    data.forEach(item => {
                        if (item.level === enums_1.CAPABILITY_DROPDOWN_LEVEL_ENUM.PRODUCT && item.id === productId) {
                            productTitle = item.title;
                        }
                        else if (item.level === enums_1.CAPABILITY_DROPDOWN_LEVEL_ENUM['SUB-CATEGORY'] &&
                            item.id === subCategoryId) {
                            subCategoryTitle = item.title;
                        }
                    });
                }
                const newEntry = {
                    coreSolutionId: coreSolutionId ? coreSolutionId : null,
                    capability: (0, lodash_1.trim)(entryName),
                    evidenceCode: evidenceCode ? evidenceCode : null,
                    isEvidenceMandatory: isEvidenceMandatory ? isEvidenceMandatory : false,
                    capabilityType: type,
                    level,
                    verticals: (verticalCodes === null || verticalCodes === void 0 ? void 0 : verticalCodes.length) ? verticalCodes : null,
                    categoryId,
                    legs: legIds,
                    product: productTitle,
                    subCategory: subCategoryTitle,
                };
                const isDataExist = yield this.capabilityRepository.isCombinationDataExist({
                    coreSolutionId: coreSolutionId ? coreSolutionId : null,
                    categoryId,
                    product: productTitle,
                    subCategory: subCategoryTitle,
                    capability: (0, lodash_1.trim)(entryName),
                    capabilityType: type,
                    level,
                });
                if (isDataExist) {
                    throw new exceptions_1.HttpException('Capability already exist.', enums_1.HttpStatus.CONFLICT);
                }
                const newCapability = yield this.capabilityRepository.setupNewCapability(newEntry, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: newCapability.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.MASTER_CAPABILITY,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: 'New master capability added.',
                    additional_info: {
                        capabilityData: newEntry,
                    },
                });
                return { message: 'New master capability setup successfully.', data: newCapability };
            }));
        });
    }
    setupNewMasterEvidence(requestPayload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const { name, description, type } = requestPayload;
                const isNameExist = yield this.masterEvidenceRepository.isNameExist((0, lodash_1.trim)(name));
                if (isNameExist) {
                    throw new exceptions_1.HttpException('Evidence name already exist.', enums_1.HttpStatus.CONFLICT);
                }
                const newEntry = {
                    name: (0, lodash_1.trim)(name),
                    description: (0, lodash_1.trim)(description),
                    type,
                    code: `E${name}`,
                };
                const newEvidence = yield this.masterEvidenceRepository.setupNewEvidence(newEntry, currentContext);
                yield this.masterEvidenceRepository.updateEvidenceById(newEvidence.id, { code: `E${newEvidence.id}` }, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: newEvidence.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.MASTER_EVIDENCE,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: 'New master evidence added.',
                    additional_info: {
                        evidenceData: newEntry,
                    },
                });
                return { message: 'New master evidence setup successfully.', data: newEvidence };
            }));
        });
    }
    getMasterCapabilitiesByFilter(page, limit, filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.capabilityRepository.getCapabilitiesByFilter(filterDto, page, limit);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.MasterCapabilityListResponseDto, rows, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
            const legs = yield this.capabilityLegRepository.getALlLegsBasic();
            const legMap = new Map(legs.map(leg => [leg.id, leg]));
            records.forEach(record => {
                record.legsDetail = record.legs.map(legId => legMap.get(legId)).filter(Boolean);
            });
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    getDropdownLevel(type) {
        if (type === metadata_enum_1.METADATA_TYPE_ENUM.PRODUCT) {
            return enums_1.CAPABILITY_DROPDOWN_LEVEL_ENUM['SUB-CATEGORY'];
        }
        if (type === metadata_enum_1.METADATA_TYPE_ENUM.PRODUCT_FAMILY) {
            return enums_1.CAPABILITY_DROPDOWN_LEVEL_ENUM['PRODUCT'];
        }
    }
    setupNewMetadata(requestPayload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { name, type, legIds } = requestPayload;
            let trimedName = (0, lodash_1.trim)(name);
            const isCommonDropdownType = Object.values(enums_1.COMMON_DROPDOWN_TYPE).includes(type);
            if (isCommonDropdownType) {
                return yield this.setupNewMetadataOfNewTypes(requestPayload, currentContext);
            }
            if (type === metadata_enum_1.METADATA_TYPE_ENUM.CATEGORY) {
                const isNameExist = yield this.capabilityCategoryRepository.isNameExist(trimedName);
                if (isNameExist) {
                    throw new exceptions_1.HttpException('Metadata already exist.', enums_1.HttpStatus.CONFLICT);
                }
                return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    const newEntry = {
                        title: (0, lodash_1.trim)(name),
                        code: (0, helpers_1.toUpperUnderscore)(name),
                    };
                    const newCategory = yield this.capabilityCategoryRepository.setupNewCategory(newEntry, currentContext);
                    const updatedCategories = yield this.capabilityLegRepository.updateLegsCategories({
                        categories: (0, sequelize_1.fn)('jsonb_concat', (0, sequelize_1.col)('categories'), (0, sequelize_1.literal)(`'[${newCategory.id}]'::jsonb`)),
                    }, currentContext, {
                        where: {
                            id: {
                                [sequelize_1.Op.in]: legIds,
                            },
                        },
                    });
                    yield this.historyService.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: newCategory.id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.METADATA,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                        action_date: new Date(),
                        comments: `New ${(0, lodash_1.toLower)(type)} added.`,
                        additional_info: {
                            metadata: newEntry,
                        },
                    });
                    return { message: 'New master metadata setup successfully.', data: updatedCategories };
                }));
            }
            const isNameExist = yield this.masterCapabilityDropdownsRepository.isNameExist(trimedName, this.getDropdownLevel(type));
            if (isNameExist) {
                throw new exceptions_1.HttpException('Metadata already exist.', enums_1.HttpStatus.CONFLICT);
            }
            const newEntry = {
                title: trimedName,
                level: this.getDropdownLevel(type),
                otherDetails: '',
            };
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const newMetadata = yield this.masterCapabilityDropdownsRepository.setupNewMetadata(newEntry, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: newMetadata.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.METADATA,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: `New ${(0, lodash_1.toLower)(type)} added.`,
                    additional_info: {
                        metadata: newEntry,
                    },
                });
                return { message: 'New master metadata setup successfully.', data: newMetadata };
            }));
        });
    }
    setupNewMetadataOfNewTypes(requestPayload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            let { name, type, code, coreSolutionId, locationTypeId, offset } = requestPayload;
            const title = (0, lodash_1.startCase)((0, lodash_1.toLower)((0, lodash_1.trim)(name)));
            code = code ? (0, lodash_1.upperCase)(code) : (0, helpers_1.toUpperUnderscore)(title);
            let condition = {};
            const typesWithCode = [
                enums_1.COMMON_DROPDOWN_TYPE.CURRENCY,
                enums_1.COMMON_DROPDOWN_TYPE.LANGUAGE,
                enums_1.COMMON_DROPDOWN_TYPE.TIMEZONE,
            ];
            const typesWithTitleOnly = [
                enums_1.COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES,
                enums_1.COMMON_DROPDOWN_TYPE.BRANDS,
            ];
            if (typesWithCode.includes(type)) {
                condition = { title, code };
            }
            if (typesWithTitleOnly.includes(type)) {
                condition = { title };
            }
            if (type !== enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION) {
                const isRecordExist = yield this.commonDropdownRepository.isDropdownRecordExists(condition);
                if (isRecordExist) {
                    throw new exceptions_1.HttpException('Metadata already exist.', enums_1.HttpStatus.CONFLICT);
                }
            }
            const payload = Object.assign(Object.assign(Object.assign({ title,
                code,
                type }, (offset && { offset })), (coreSolutionId && { coreSolutionId })), (locationTypeId && { locationTypeId }));
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const newMetadata = yield this.commonDropdownRepository.setupNewMetadataOfCommonTypes(payload, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: newMetadata.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.METADATA,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: `New ${(0, lodash_1.toLower)(type)} added.`,
                    additional_info: {
                        metadata: payload,
                    },
                });
                return {
                    message: 'New master metadata setup successfully.',
                    data: newMetadata,
                };
            }));
        });
    }
};
MasterCapabilityService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.CapabilityRepository,
        helpers_1.DatabaseHelper,
        clients_1.HistoryApiClient,
        repositories_1.CapabilityLegRepository,
        capability_category_repository_1.CapabilityCategoryRepository,
        repositories_1.MasterCapabilityDropdownRepository,
        repositories_1.MasterEvidenceRepository,
        repositories_2.CommonDropdownRepository])
], MasterCapabilityService);
exports.MasterCapabilityService = MasterCapabilityService;
//# sourceMappingURL=master-capability-services.js.map