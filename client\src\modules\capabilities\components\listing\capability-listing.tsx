import { CAPABILITY_FILTER_CONFIG, filterheaders } from '../../config';

import { useState } from 'react';

import CustomTableFilterChip from '@/components/custom-tables/custom-table-filter-chip';

import { useTranslate } from '@/locales/use-locales';
import MobileViewCapabilityListingCard from './mobile-view-capability-listing-card';

import CustomCapabilityTableFilterDrawer from './custom-capability-filter-drawer';

import { ActionItems } from '../../enums/capability.enum';

import { ConfirmDialog } from '@/components/custom-dialog';
import MobileViewLocationCardSkeleton from '@/components/skeleton/location-card-skeleton';
import { TableSkeleton } from '@/components/table';
import { useBoolean } from '@/hooks/use-boolean';
import { useResponsive } from '@/hooks/use-responsive';
import {
  CapabilityListRecord,
  CapabilityListResponse,
  GeneralLocationResponse,
  KeyValue,
  SelectedLocation,
} from '@/shared/models';
import { deleteCapability, queryClient } from '@/shared/services';
import { LoadingButton } from '@mui/lab';
import { enqueueSnackbar } from 'notistack';
import { useMutation } from 'react-query';
import CustomCapabilityTable from '../new-listing/capability-list-table';
import CapabilityStatusEdit from '../status-edit';

interface Props {
  selectedLocation?: SelectedLocation | undefined;
  showFilterDrawer?: boolean;
  setShowFilterDrawer?: React.Dispatch<React.SetStateAction<boolean>>;
  filters: Record<string, KeyValue[]>;
  setFilters: React.Dispatch<React.SetStateAction<Record<string, KeyValue[]>>>;
  capabilityListData: CapabilityListResponse | undefined;
  page: number;
  setPage: React.Dispatch<React.SetStateAction<number>>;
  rowsPerPage: number;
  setRowsPerPage: React.Dispatch<React.SetStateAction<number>>;
  _isCapabilityListFetching: boolean;
  tempFilters: Record<string, KeyValue[]>;
  setTempFilters: React.Dispatch<React.SetStateAction<Record<string, KeyValue[]>>>;
  showFilterChip: boolean;
  setShowFilterChip: React.Dispatch<React.SetStateAction<boolean>>;
}
const CapabilityListing = ({
  showFilterDrawer,
  setShowFilterDrawer,
  filters,
  setFilters,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  capabilityListData,
  _isCapabilityListFetching,
  tempFilters,
  setTempFilters,
  showFilterChip,
  setShowFilterChip,
}: Props) => {
  const isMobile = useResponsive('down', 'sm');
  const [selectedCapabilityRecord, setSelectedCapabilityRecord] = useState<CapabilityListRecord>();
  const { t } = useTranslate();

  const [statusEditOpen, setStatusEditOpen] = useState(false);

  const confirm = useBoolean();

  const TABLE_HEADERS = [
    { id: 'core_solution', label: t('label.core_solution'), width: 160 },
    { id: 'capability_name', label: t('label.capability_name') },
    { id: 'location_name', label: t('label.location') },
    { id: 'capability_type', label: t('label.type') },
    { id: 'capability_level', label: t('label.level') },
    { id: 'status', label: t('label.status'), width: 130 },
    // { id: 'provider', label: t('label.provider') },
    // { id: 'verticals', label: t('label.verticals') },
    { id: '', label: t('label.action'), width: 88 },
  ];

  const { mutateAsync, isLoading } = useMutation({
    mutationFn: () => {
      return deleteCapability(selectedCapabilityRecord?.id.toString());
    },
    onSuccess: (response: GeneralLocationResponse) => {
      confirm.onFalse();
      enqueueSnackbar(response.message, {
        variant: 'success',
      });
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
    },
  });

  const paginatedData = capabilityListData?.records || [];
  const total = capabilityListData?.total || 0;
  const pageTotal = capabilityListData?.pageTotal || 0;

  const actionItems = [
    { title: t(ActionItems.VIEW), onClick: (row: []) => console.log(ActionItems.VIEW, row) },
    { title: t(ActionItems.EDIT), onClick: (row: []) => console.log(ActionItems.EDIT, row) },
    { title: t(ActionItems.DELETE), onClick: (row: []) => console.log(ActionItems.DELETE, row) },
  ];

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMobilePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage - 1);
  };

  const handleFilterChange = (key: string, value: KeyValue[]) => {
    // if (key === 'entry_name') {
    //   if (value.length > 0 && value[0].id?.toString().trim() !== '' && value[0].label?.toString().trim() !== '') {
    //     setTempFilters((prev) => ({ ...prev, [key]: value }));
    //   } else {
    //     setTempFilters((prev) => {
    //       const updated = { ...prev };
    //       delete updated[key];
    //       return updated;
    //     });
    //   }
    // } else {
    setTempFilters((prev) => ({ ...prev, [key]: value }));
    // }
  };

  const applyFilters = () => {
    const appliedFilters = Object.fromEntries(Object.entries(tempFilters).filter(([_, values]) => values.length > 0));
    setFilters(appliedFilters);
    setPage(0);
    setShowFilterChip(Object.keys(appliedFilters).length > 0);
    setShowFilterDrawer?.(false);
  };
  const handleClearFilter = (key: string, value?: string | number | null) => {
    setFilters((prev) => {
      const updated = { ...prev };
      if (value !== undefined) {
        updated[key] = updated[key]?.filter((v) => v.label !== value);
      }
      if (!updated[key]?.length) delete updated[key];
      return updated;
    });

    setTempFilters((prev) => {
      const updated = { ...prev };
      if (value !== undefined) {
        updated[key] = updated[key]?.filter((v) => v.label !== value);
      }
      if (!updated[key]?.length) delete updated[key];
      return updated;
    });

    setPage(0);
    const hasFilters = Object.keys(filters).some((key) => filters[key].length > 0);
    setShowFilterChip(hasFilters);
  };
  const resetFilters = () => {
    setTempFilters({});
    setFilters({});
    setPage(0);
    setShowFilterChip(false);
    setShowFilterDrawer?.(false);
  };

  const handleClearAllFilters = () => {
    setFilters({});
    setTempFilters({});
    setPage(0);
    setShowFilterChip(false);
  };

  const handleOpenEditStatusPopup = (id: string) => {
    setStatusEditOpen(true);
    const record = paginatedData.find((r) => String(r.id) === id);
    if (record) {
      setSelectedCapabilityRecord(record);
    }
  };

  const handleDeleteEntry = (id: any) => {
    const record = paginatedData.find((r) => String(r.id) === id);
    if (record) {
      setSelectedCapabilityRecord(record);
      confirm.onTrue();
    }
  };

  return (
    <>
      {showFilterChip && (
        <CustomTableFilterChip
          filters={filters || []}
          onClearFilter={handleClearFilter}
          onClearAll={handleClearAllFilters}
          filterConfig={CAPABILITY_FILTER_CONFIG}
        />
      )}

      {_isCapabilityListFetching && isMobile ? (
        <MobileViewLocationCardSkeleton />
      ) : (
        isMobile && (
          <MobileViewCapabilityListingCard
            data={paginatedData || []}
            count={total}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handleMobilePageChange}
            actionOptions={actionItems}
            onDelete={handleDeleteEntry}
            onOpenEditStatusPopup={handleOpenEditStatusPopup}
          />
        )
      )}

      {_isCapabilityListFetching && !isMobile ? (
        <TableSkeleton rowCount={10} colCount={8} colSpacing={1} />
      ) : (
        !isMobile && (
          <CustomCapabilityTable
            tableHeaders={TABLE_HEADERS}
            isPaginated={total >= pageTotal || total < rowsPerPage}
            tableData={paginatedData || []}
            rowsPerPage={rowsPerPage}
            totalRows={total}
            page={page}
            handleChangePage={handleChangePage}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            actionOptions={actionItems}
            onDelete={handleDeleteEntry}
            onOpenEditStatusPopup={handleOpenEditStatusPopup}
          />
        )
      )}

      <CustomCapabilityTableFilterDrawer
        open={showFilterDrawer || false}
        onClose={() => setShowFilterDrawer?.(false)}
        filters={tempFilters}
        onFilterChange={handleFilterChange}
        onSearch={applyFilters}
        onCancel={resetFilters}
        tableHeaders={filterheaders}
        tableData={paginatedData || []}
      />

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={t('label.delete')}
        content={t('messages.are_you_sure_want_to_delete')}
        action={
          <LoadingButton
            loading={isLoading}
            variant="contained"
            onClick={async () => {
              try {
                await mutateAsync();
                await queryClient.invalidateQueries(['capability-list']);
                confirm.onFalse();
              } catch (err) {
                console.debug(err);
              }
            }}
          >
            {t('label.delete')}
          </LoadingButton>
        }
      />
      {statusEditOpen && (
        <CapabilityStatusEdit
          statusEditOpen={statusEditOpen}
          setStatusEditOpen={setStatusEditOpen}
          capabilityDetailData={selectedCapabilityRecord}
        />
      )}
    </>
  );
};

export default CapabilityListing;
