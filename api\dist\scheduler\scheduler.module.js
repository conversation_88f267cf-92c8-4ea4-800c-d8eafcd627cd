"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../capability/repositories");
const config_service_1 = require("../config/config.service");
const repositories_2 = require("../permission/repositories");
const clients_1 = require("../shared/clients");
const helpers_1 = require("../shared/helpers");
const services_1 = require("../shared/services");
const services_2 = require("./services");
const repositories = [repositories_2.UserPermissionRepository, repositories_1.LocationWiseCapabilityRepository];
let SchedulerModule = class SchedulerModule {
};
SchedulerModule = __decorate([
    (0, common_1.Module)({
        controllers: [],
        providers: [
            ...repositories,
            services_2.SchedulerService,
            clients_1.AdminApiClient,
            clients_1.MSGraphApiClient,
            services_1.SharedPermissionService,
            services_1.SharedNotificationService,
            clients_1.HistoryApiClient,
            clients_1.NotificationApiClient,
            helpers_1.DatabaseHelper,
            config_service_1.ConfigService,
        ],
    })
], SchedulerModule);
exports.SchedulerModule = SchedulerModule;
//# sourceMappingURL=scheduler.module.js.map