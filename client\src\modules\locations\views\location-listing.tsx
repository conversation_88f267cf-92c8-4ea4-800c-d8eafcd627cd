import LocationHeirarchy from '@/components/business-entity-treeview/location-heirachy.tsx';
import { LocationDetailContext } from '@/core/contexts/location.context';
import { Box, Button, Card, IconButton, Typography } from '@mui/material';
import saveAs from 'file-saver';
import { enqueueSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import { useMutation, useQuery } from 'react-query';

import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';

import { KeyValue, SelectedLocation } from '@/shared/models/capability.model';
import { getBusinessUnitHierarchyByPermission } from '@/shared/services/business-entity.service.ts';

import CustomBreadcrumbs from '@/components/custom-breadcrumbs';
import SvgColor from '@/components/svg-color';
import { useLoading } from '@/hooks/use-loading';
import { useResponsive } from '@/hooks/use-responsive';
import { paths } from '@/routes/paths';
import { LocationTableConstants } from '@/shared/enum';
import { PERMISSIONS } from '@/shared/enum/permission.enum.ts';
import { exportLocationList, getLocationList } from '@/shared/services/location.service';
import { removeKeysFromFilters } from '@/shared/utils/capability-filter.util';
import { getIcon } from '@/shared/utils/get-icon';
import { mapFiltersToApiPayload } from '@/shared/utils/location-filter.util';
import { Grid, Stack } from '@mui/system';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import LocationListing from '../components/listing/location-listing';

export default function LocationListingView() {
  const [page, setPage] = useState(0);
  const [sortBy, setSortBy] = useState('');
  const [sortDirection, setSortDirection] = useState<'desc' | 'asc'>('desc');

  const [rowsPerPage, setRowsPerPage] = useState(LocationTableConstants.DEFAULT_ROWS_PER_PAGE);
  const [filters, setFilters] = useState<Record<string, KeyValue[]>>(() => {
    try {
      const stored = localStorage.getItem('locationFilters');
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  });
  const [tempFilters, setTempFilters] = useState<Record<string, KeyValue[]>>(() => {
    try {
      const stored = localStorage.getItem('locationFilters');
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  });
  const { t } = useTranslation();
  const navigate = useNavigate();
  const isSmallScreen = useResponsive('down', 'sm');
  const isTablet = useResponsive('between', 'sm', 'lg');
  const isDesktop = useResponsive('up', 'lg');
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [currentlySelected, setCurrentlySelected] = useState<SelectedLocation>();
  const [showFilterChip, setShowFilterChip] = useState(false);

  useEffect(() => {
    const updated = { ...filters };
    delete updated['hierarchyEntityId'];
    localStorage.setItem('locationFilters', JSON.stringify(updated));
    const hasAnyFilters = Object.values(filters).some((arr: KeyValue[]) => arr?.length > 0);
    setShowFilterChip(hasAnyFilters);
  }, [filters]);

  useEffect(() => {
    const keys = ['group', 'country', 'area', 'region', 'cluster'];
    const shouldResetSelection = keys.some((key) => filters.hasOwnProperty(key));
    if (shouldResetSelection) {
      setCurrentlySelected(undefined);
    }
  }, [filters]);

  const { setLoading, setMessage } = useLoading();
  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('exporting'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };
  const apiFilters = mapFiltersToApiPayload(filters);

  const { data: locationListData, isFetching: _isLocationListFetching } = useQuery({
    queryKey: ['locationList', page, rowsPerPage, filters, sortBy, sortDirection],
    queryFn: () =>
      getLocationList(
        {
          page: page + 1,
          limit: rowsPerPage,
          orderBy: sortBy,
          orderDirection: sortDirection,
        },
        apiFilters,
      ),
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
    keepPreviousData: false,
  });

  const locationList = locationListData?.records || [];

  const { mutate: exportLocations } = useMutation({
    mutationFn: () => exportLocationList(apiFilters),
    onSuccess: (response: any) => {
      saveAs(response, 'CapMApp Location List.xlsx');
      enqueueSnackbar(t('location_export_success_msg'), {
        variant: 'success',
      });
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });

  let { data: locationsData, isFetching: isFilterFetchingLocationData } = useQuery({
    queryKey: ['businessunit', PERMISSIONS.APPLICATION_ADMIN],
    queryFn: () => getBusinessUnitHierarchyByPermission(PERMISSIONS.APPLICATION_ADMIN),
    onError: () => {
      enqueueSnackbar('Something went wrong while fetching Business Units!', { variant: 'error' });
    },
    enabled: isDesktop,
  });

  const handleLocationChange = (value: SelectedLocation) => {
    setCurrentlySelected(value);
    setFilters((prev) => {
      const keysToRemove = ['group', 'country', 'area', 'region', 'cluster'];
      const cleanedPrev = Object.fromEntries(Object.entries(prev).filter(([key]) => !keysToRemove.includes(key)));

      return {
        ...cleanedPrev,
        hierarchyEntityId: [{ id: value.id, label: value.fullName }],
      };
    });
    setTempFilters((prev) => {
      const keysToRemove = ['group', 'country', 'area', 'region', 'cluster'];
      const cleanedPrev = Object.fromEntries(Object.entries(prev).filter(([key]) => !keysToRemove.includes(key)));

      return {
        ...cleanedPrev,
        hierarchyEntityId: [{ id: value.id, label: value.fullName }],
      };
    });
  };

  return (
    <LocationDetailContext.Provider value={currentlySelected}>
      <Box px={{ md: 2 }}>
        <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pr: 1 }}>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            flexWrap={{ xs: 'wrap', md: 'nowrap' }}
          >
            <CustomBreadcrumbs
              heading={t('label.locations')}
              links={[
                { name: t('label.home'), href: paths.root },
                { name: t('label.locations'), href: paths.locations.root },
              ]}
              sx={{ flexShrink: 1 }}
            />

            <Box
              component="span"
              display="flex"
              alignItems="center"
              sx={{ cursor: 'pointer', gap: 1, mr: 1, ml: { xs: 'auto', md: 2 } }}
              onClick={() => navigate(-1)}
            >
              <Box component="img" src={getIcon('backBtn')} sx={{ width: 24, height: 16 }} />
              <Typography variant="value">{t('btn_name.back')}</Typography>
            </Box>
          </Stack>
        </Card>
      </Box>
      <Grid px={{ md: 2 }} container spacing={2} mt={2}>
        {isDesktop && (
          <Grid size={{ xs: 12, sm: 2.5 }}>
            <LocationHeirarchy
              selectedLocation={currentlySelected}
              locations={locationsData as TreeViewDataType}
              onChange={handleLocationChange}
              isLoading={isFilterFetchingLocationData}
            />
          </Grid>
        )}
        <Box overflow={'hidden'} flex={1}>
          <Card sx={{ border: 'solid 1px #E8D6D6', borderRadius: '7px', paddingTop: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2} px={2}>
              <Typography variant="mainTitle">
                {[currentlySelected?.fullName ?? 'Global', t('label.locations')].filter(Boolean).join(' - ')}
              </Typography>
              {isSmallScreen ? (
                <Box display="flex" alignItems="center">
                  <IconButton onClick={() => setShowFilterDrawer(true)}>
                    <SvgColor sx={{ height: 16 }} src={getIcon('Filter-B')} />
                  </IconButton>
                  <IconButton
                    onClick={() => {
                      startLoadingState();
                      exportLocations();
                    }}
                  >
                    <SvgColor sx={{ height: 16 }} src={getIcon('export')} />
                  </IconButton>
                </Box>
              ) : (
                <>
                  <Box display="flex" alignItems="center">
                    <Button
                      onClick={() => setShowFilterDrawer(true)}
                      variant="outlined"
                      size={isSmallScreen ? 'small' : 'medium'}
                      sx={{
                        bgcolor: 'white',
                        borderRadius: '24px',
                        textTransform: 'none',
                        whiteSpace: 'nowrap',
                        gap: 0.5,
                        mr: 1,
                      }}
                      startIcon={<SvgColor sx={{ height: 16 }} src={getIcon('Filter-B')} />}
                    >
                      {t('label.filters')}
                    </Button>

                    {locationList.length > 0 && (
                      <Button
                        type="button"
                        variant="contained"
                        onClick={() => {
                          startLoadingState();
                          exportLocations();
                        }}
                        startIcon={<SvgColor sx={{ height: 16 }} src={getIcon('export')} />}
                        size={isSmallScreen ? 'small' : 'medium'}
                        sx={{
                          borderRadius: '24px',
                          textTransform: 'none',
                          whiteSpace: 'nowrap',
                          gap: 0.5,
                        }}
                      >
                        {t('btn_name.export')}
                      </Button>
                    )}
                  </Box>
                </>
              )}
            </Box>
            <LocationListing
              filters={removeKeysFromFilters(filters, ['hierarchyEntityId'])}
              setFilters={setFilters}
              tempFilters={tempFilters}
              setTempFilters={setTempFilters}
              locationListData={locationListData}
              _isLocationListFetching={_isLocationListFetching}
              showFilterDrawer={showFilterDrawer}
              page={page}
              setPage={setPage}
              sortBy={sortBy}
              setSortBy={setSortBy}
              setSortDirection={setSortDirection}
              sortDirection={sortDirection}
              rowsPerPage={rowsPerPage}
              setRowsPerPage={setRowsPerPage}
              setShowFilterDrawer={() => setShowFilterDrawer(!showFilterDrawer)}
              showFilterChip={showFilterChip}
              setShowFilterChip={setShowFilterChip}
            />
          </Card>
        </Box>
      </Grid>
    </LocationDetailContext.Provider>
  );
}
