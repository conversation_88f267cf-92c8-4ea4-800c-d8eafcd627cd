{"version": 3, "file": "support-query.controller.js", "sourceRoot": "", "sources": ["../../../src/support-query/controllers/support-query.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwE;AACxE,+CAA6C;AAC7C,6CAAsE;AACtE,sDAAkD;AAClD,8CAAmD;AAEnD,8CAA+C;AAE/C,kCAA0E;AAC1E,0CAAkD;AAK3C,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAClC,YAA4B,mBAAwC;QAAxC,wBAAmB,GAAnB,mBAAmB,CAAqB;IAAG,CAAC;IAUjE,sBAAsB,CACrB,OAAuB,EACtB,IAA4B;QAEpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACtF,CAAC;CACD,CAAA;AAdA;IAAC,IAAA,wBAAW,EAAC,mBAAW,CAAC,GAAG,CAAC;IAC5B,IAAA,kBAAS,EAAC,IAAA,oBAAS,EAAC,cAAc,CAAC,EAAE,yBAAgB,CAAC;IACtD,IAAA,qBAAW,EAAC;QACZ,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iCAAiC;QAC9C,IAAI,EAAE,8BAAuB;KAC7B,CAAC;IACD,IAAA,aAAI,EAAC,OAAO,CAAC;IAEZ,WAAA,IAAA,YAAG,GAAE,CAAA;IACL,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAO,6BAAsB;;oEAGpC;AAhBW,sBAAsB;IAHlC,IAAA,iBAAO,EAAC,cAAc,CAAC;IACvB,IAAA,uBAAa,GAAE;IACf,IAAA,mBAAU,EAAC,eAAe,CAAC;qCAEsB,8BAAmB;GADxD,sBAAsB,CAiBlC;AAjBY,wDAAsB"}