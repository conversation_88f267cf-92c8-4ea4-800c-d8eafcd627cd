import { Global, Module } from '@nestjs/common';
import { UserPermissionRepository } from 'src/permission/repositories';
import {
	AdminApiClient,
	HistoryApiClient,
	MSGraphApiClient,
	NotificationApiClient,
} from './clients';
import { AttachmentApiClient } from './clients/attachment-api.client';
import {
	EntityService,
	SharedAttachmentService,
	SharedNotificationService,
	SharedPermissionService,
} from './services';
import { WorkflowYearValidator } from './validators';

const repositories = [UserPermissionRepository];
@Global()
@Module({
	providers: [
		AdminApiClient,
		EntityService,
		MSGraphApiClient,
		HistoryApiClient,
		SharedPermissionService,
		SharedAttachmentService,
		SharedNotificationService,
		WorkflowYearValidator,
		NotificationApiClient,
		AttachmentApiClient,
		...repositories,
	],
})
export class SharedModule {}
