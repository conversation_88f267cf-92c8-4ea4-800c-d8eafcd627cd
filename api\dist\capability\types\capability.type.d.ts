import { EVIDENCE_TYPE, SYSTEM_AND_TECHNOLOGY_ENUM } from "src/shared/enums";
import { UserDetail } from "src/shared/interfaces";
type CapabilityTypeDetail = {
    issuingAuthority?: string;
    expirationDate?: Date;
    softwareProviderName?: string;
    systemTechnologyName?: string;
    systemTechnologyVersion?: string;
    systemTechnologyOwner?: SYSTEM_AND_TECHNOLOGY_ENUM;
    contractAgreementLicenseSubscription?: string;
};
type OperationalDetail = {
    instructions?: string;
    mandatoryDocumentsList?: string;
    handlingAndProcedures?: string;
    rulesAndRegulations?: string;
};
type DynamicFields = {
    key: string;
    label: string;
    value: string;
};
type AdditionalDetail = {
    comments?: string;
    tags?: string[];
    additionalContactDetails?: string;
    dynamicFields?: DynamicFields[];
};
export type EvidenceDetail = {
    id: number;
    name: string;
    description: string;
    code: string;
    type: EVIDENCE_TYPE;
    evidenceValue?: string;
};
export type CapabilityOtherDetails = {
    owners: UserDetail;
    coOwners?: UserDetail[];
    description: string;
    capabilityTypeDetail: CapabilityTypeDetail;
    operationalDetail: OperationalDetail;
    additionalDetail: AdditionalDetail;
};
export {};
