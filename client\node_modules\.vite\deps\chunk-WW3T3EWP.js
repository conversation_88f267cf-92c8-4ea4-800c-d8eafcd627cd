import {
  inputBaseClasses_default
} from "./chunk-LDES4GLT.js";
import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-UE7CETWW.js";

// node_modules/@mui/material/OutlinedInput/outlinedInputClasses.js
function getOutlinedInputUtilityClass(slot) {
  return generateUtilityClass("MuiOutlinedInput", slot);
}
var outlinedInputClasses = {
  ...inputBaseClasses_default,
  ...generateUtilityClasses("MuiOutlinedInput", ["root", "notchedOutline", "input"])
};
var outlinedInputClasses_default = outlinedInputClasses;

export {
  getOutlinedInputUtilityClass,
  outlinedInputClasses_default
};
//# sourceMappingURL=chunk-WW3T3EWP.js.map
