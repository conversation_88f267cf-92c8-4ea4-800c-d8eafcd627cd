import { PublicClientApplication, AccountInfo, SilentRequest } from '@azure/msal-browser';
import { BrowserAuthError, InteractionRequiredAuthError } from '@azure/msal-browser';

export interface TokenRefreshConfig {
  scopes: string[];
  renewalOffsetMinutes?: number;
  maxRetryAttempts?: number;
  retryDelayMs?: number;
}

export class TokenRefreshService {
  private msalInstance: PublicClientApplication;
  private config: TokenRefreshConfig;
  private refreshTimer: NodeJS.Timeout | null = null;
  private isRefreshing = false;

  constructor(msalInstance: PublicClientApplication, config: TokenRefreshConfig) {
    this.msalInstance = msalInstance;
    this.config = {
      renewalOffsetMinutes: 5,
      maxRetryAttempts: 3,
      retryDelayMs: 1000,
      ...config,
    };
  }

  /**
   * Start automatic token refresh monitoring
   */
  public startTokenRefreshMonitoring(account: AccountInfo): void {
    this.stopTokenRefreshMonitoring();
    
    // Check token status every minute
    this.refreshTimer = setInterval(() => {
      this.checkAndRefreshToken(account);
    }, 60000); // Check every minute

    // Initial check
    this.checkAndRefreshToken(account);
  }

  /**
   * Stop automatic token refresh monitoring
   */
  public stopTokenRefreshMonitoring(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Check if token needs refresh and refresh if necessary
   */
  private async checkAndRefreshToken(account: AccountInfo): Promise<void> {
    if (this.isRefreshing) {
      return; // Already refreshing
    }

    try {
      // Try to get a token silently to check if it's valid/expired
      const silentRequest = {
        scopes: this.config.scopes,
        account,
        forceRefresh: false,
      };

      const response = await this.msalInstance.acquireTokenSilent(silentRequest);

      if (response.expiresOn) {
        const now = new Date();
        const expiresOn = new Date(response.expiresOn);
        const timeUntilExpiry = (expiresOn.getTime() - now.getTime()) / 1000; // in seconds
        const renewalOffset = (this.config.renewalOffsetMinutes || 5) * 60; // Convert to seconds

        if (timeUntilExpiry <= renewalOffset) {
          console.log(`Token expires in ${Math.round(timeUntilExpiry / 60)} minutes, refreshing...`);
          await this.refreshToken(account);
        }
      }
    } catch (error) {
      // If silent token acquisition fails, the token likely needs refresh
      console.warn('Silent token check failed, attempting refresh:', error);
      await this.refreshToken(account);
    }
  }

  /**
   * Refresh token with retry logic
   */
  public async refreshToken(account: AccountInfo, attempt = 1): Promise<boolean> {
    if (this.isRefreshing && attempt === 1) {
      return false; // Already refreshing
    }

    this.isRefreshing = true;

    try {
      const silentRequest: SilentRequest = {
        scopes: this.config.scopes,
        account,
        forceRefresh: true,
      };

      const response = await this.msalInstance.acquireTokenSilent(silentRequest);
      
      if (response.accessToken) {
        console.log('Token refreshed successfully');
        this.isRefreshing = false;
        return true;
      }
    } catch (error) {
      console.warn(`Token refresh attempt ${attempt} failed:`, error);

      if (error instanceof InteractionRequiredAuthError) {
        console.log('Interaction required, redirecting to login');
        this.isRefreshing = false;
        await this.msalInstance.acquireTokenRedirect({
          scopes: this.config.scopes,
          account,
        });
        return false;
      }

      if (error instanceof BrowserAuthError && error.errorCode === 'monitor_window_timeout') {
        console.warn('Token refresh timeout, retrying...');
        
        if (attempt < (this.config.maxRetryAttempts || 3)) {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelayMs || 1000));
          return this.refreshToken(account, attempt + 1);
        }
      }

      // Max retries reached or other error
      console.error('Token refresh failed after all attempts:', error);
      this.isRefreshing = false;
      return false;
    }

    this.isRefreshing = false;
    return false;
  }

  /**
   * Force immediate token refresh
   */
  public async forceRefresh(account: AccountInfo): Promise<boolean> {
    return this.refreshToken(account);
  }

  /**
   * Check if currently refreshing
   */
  public get isCurrentlyRefreshing(): boolean {
    return this.isRefreshing;
  }

  /**
   * Get time until next token expiry (async)
   */
  public async getTimeUntilExpiry(account: AccountInfo): Promise<number | null> {
    try {
      // Try to get current token information
      const silentRequest = {
        scopes: this.config.scopes,
        account,
        forceRefresh: false,
      };

      const response = await this.msalInstance.acquireTokenSilent(silentRequest);

      if (response.expiresOn) {
        const now = new Date();
        const expiresOn = new Date(response.expiresOn);
        return (expiresOn.getTime() - now.getTime()) / 1000; // in seconds
      }

      return null;
    } catch (error) {
      // Token might be expired or invalid
      console.warn('Could not get token expiry info:', error);
      return null;
    }
  }

  /**
   * Get time until next token expiry (synchronous version for compatibility)
   */
  public getTimeUntilExpirySync(_account: AccountInfo): number | null {
    // This is a simplified version that returns null
    // The async version should be used for accurate results
    return null;
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.stopTokenRefreshMonitoring();
    this.isRefreshing = false;
  }
}
