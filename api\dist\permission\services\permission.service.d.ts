import { LocationRepository } from 'src/location/repositories';
import { AdminApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedPermissionService } from 'src/shared/services';
import { CurrentContext } from 'src/shared/types';
import { AllAccessControlConfigResponseDto, CreateUserPermissionRequestDto, PaginatedUserPermissionResponseDto, PermissionResponseDto } from '../dtos';
import { AccessControlConfigRepository, UserPermissionRepository } from '../repositories';
export declare class PermissionService {
    private readonly adminApiClient;
    private readonly accessControlConfigRepository;
    private readonly userPermissionRepository;
    private readonly locationRepository;
    private readonly mSGraphApiClient;
    private readonly sharedPermissionService;
    private readonly historyService;
    private readonly databaseHelper;
    constructor(adminApiClient: AdminApiClient, accessControlConfigRepository: AccessControlConfigRepository, userPermissionRepository: UserPermissionRepository, locationRepository: LocationRepository, mSGraphApiClient: MSGraphApiClient, sharedPermissionService: SharedPermissionService, historyService: HistoryApiClient, databaseHelper: DatabaseHelper);
    getListOfUserPermissions(currentContext: CurrentContext): Promise<PermissionResponseDto[]>;
    getAllAccessControlConfigs(): Promise<AllAccessControlConfigResponseDto[]>;
    getLocalLocationBasedPermissions(currentContext: CurrentContext, page: number, limit: number, orderBy: string, orderDirection: string, loginid?: string, configGroupId?: number | null, locationId?: number | null): Promise<PaginatedUserPermissionResponseDto>;
    createUserPermission(data: CreateUserPermissionRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteUserPermission(id: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getPeopleSearch(loginId: string): Promise<MessageResponseDto>;
}
