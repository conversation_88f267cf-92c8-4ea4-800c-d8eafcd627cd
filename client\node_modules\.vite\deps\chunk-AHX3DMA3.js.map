{"version": 3, "sources": ["../../@mui/material/Autocomplete/Autocomplete.js", "../../@mui/material/useAutocomplete/useAutocomplete.js", "../../@mui/material/Autocomplete/autocompleteClasses.js"], "sourcesContent": ["'use client';\n\nvar _ClearIcon, _ArrowDropDownIcon;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport useAutocomplete, { createFilterOptions } from \"../useAutocomplete/index.js\";\nimport Popper from \"../Popper/index.js\";\nimport ListSubheader from \"../ListSubheader/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport IconButton from \"../IconButton/index.js\";\nimport Chip from \"../Chip/index.js\";\nimport inputClasses from \"../Input/inputClasses.js\";\nimport inputBaseClasses from \"../InputBase/inputBaseClasses.js\";\nimport outlinedInputClasses from \"../OutlinedInput/outlinedInputClasses.js\";\nimport filledInputClasses from \"../FilledInput/filledInputClasses.js\";\nimport ClearIcon from \"../internal/svg-icons/Close.js\";\nimport ArrowDropDownIcon from \"../internal/svg-icons/ArrowDropDown.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport autocompleteClasses, { getAutocompleteUtilityClass } from \"./autocompleteClasses.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused,\n    popupOpen,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', expanded && 'expanded', focused && 'focused', fullWidth && 'fullWidth', hasClearIcon && 'hasClearIcon', hasPopupIcon && 'hasPopupIcon'],\n    inputRoot: ['inputRoot'],\n    input: ['input', inputFocused && 'inputFocused'],\n    tag: ['tag', `tagSize${capitalize(size)}`],\n    endAdornment: ['endAdornment'],\n    clearIndicator: ['clearIndicator'],\n    popupIndicator: ['popupIndicator', popupOpen && 'popupIndicatorOpen'],\n    popper: ['popper', disablePortal && 'popperDisablePortal'],\n    paper: ['paper'],\n    listbox: ['listbox'],\n    loading: ['loading'],\n    noOptions: ['noOptions'],\n    option: ['option'],\n    groupLabel: ['groupLabel'],\n    groupUl: ['groupUl']\n  };\n  return composeClasses(slots, getAutocompleteUtilityClass, classes);\n};\nconst AutocompleteRoot = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      fullWidth,\n      hasClearIcon,\n      hasPopupIcon,\n      inputFocused,\n      size\n    } = ownerState;\n    return [{\n      [`& .${autocompleteClasses.tag}`]: styles.tag\n    }, {\n      [`& .${autocompleteClasses.tag}`]: styles[`tagSize${capitalize(size)}`]\n    }, {\n      [`& .${autocompleteClasses.inputRoot}`]: styles.inputRoot\n    }, {\n      [`& .${autocompleteClasses.input}`]: styles.input\n    }, {\n      [`& .${autocompleteClasses.input}`]: inputFocused && styles.inputFocused\n    }, styles.root, fullWidth && styles.fullWidth, hasPopupIcon && styles.hasPopupIcon, hasClearIcon && styles.hasClearIcon];\n  }\n})({\n  [`&.${autocompleteClasses.focused} .${autocompleteClasses.clearIndicator}`]: {\n    visibility: 'visible'\n  },\n  /* Avoid double tap issue on iOS */\n  '@media (pointer: fine)': {\n    [`&:hover .${autocompleteClasses.clearIndicator}`]: {\n      visibility: 'visible'\n    }\n  },\n  [`& .${autocompleteClasses.tag}`]: {\n    margin: 3,\n    maxWidth: 'calc(100% - 6px)'\n  },\n  [`& .${autocompleteClasses.inputRoot}`]: {\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      width: 0,\n      minWidth: 30\n    }\n  },\n  [`& .${inputClasses.root}`]: {\n    paddingBottom: 1,\n    '& .MuiInput-input': {\n      padding: '4px 4px 4px 0px'\n    }\n  },\n  [`& .${inputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${inputClasses.input}`]: {\n      padding: '2px 4px 3px 0'\n    }\n  },\n  [`& .${outlinedInputClasses.root}`]: {\n    padding: 9,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '7.5px 4px 7.5px 5px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${outlinedInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    // Don't specify paddingRight, as it overrides the default value set when there is only\n    // one of the popup or clear icon as the specificity is equal so the latter one wins\n    paddingTop: 6,\n    paddingBottom: 6,\n    paddingLeft: 6,\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '2.5px 4px 2.5px 8px'\n    }\n  },\n  [`& .${filledInputClasses.root}`]: {\n    paddingTop: 19,\n    paddingLeft: 8,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${filledInputClasses.input}`]: {\n      padding: '7px 4px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    paddingBottom: 1,\n    [`& .${filledInputClasses.input}`]: {\n      padding: '2.5px 4px'\n    }\n  },\n  [`& .${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 8\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 0,\n    paddingBottom: 0,\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 16,\n      paddingBottom: 17\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 8,\n      paddingBottom: 9\n    }\n  },\n  [`& .${autocompleteClasses.input}`]: {\n    flexGrow: 1,\n    textOverflow: 'ellipsis',\n    opacity: 0\n  },\n  variants: [{\n    props: {\n      fullWidth: true\n    },\n    style: {\n      width: '100%'\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      [`& .${autocompleteClasses.tag}`]: {\n        margin: 2,\n        maxWidth: 'calc(100% - 4px)'\n      }\n    }\n  }, {\n    props: {\n      inputFocused: true\n    },\n    style: {\n      [`& .${autocompleteClasses.input}`]: {\n        opacity: 1\n      }\n    }\n  }, {\n    props: {\n      multiple: true\n    },\n    style: {\n      [`& .${autocompleteClasses.inputRoot}`]: {\n        flexWrap: 'wrap'\n      }\n    }\n  }]\n});\nconst AutocompleteEndAdornment = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'EndAdornment',\n  overridesResolver: (props, styles) => styles.endAdornment\n})({\n  // We use a position absolute to support wrapping tags.\n  position: 'absolute',\n  right: 0,\n  top: '50%',\n  transform: 'translate(0, -50%)'\n});\nconst AutocompleteClearIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'ClearIndicator',\n  overridesResolver: (props, styles) => styles.clearIndicator\n})({\n  marginRight: -2,\n  padding: 4,\n  visibility: 'hidden'\n});\nconst AutocompletePopupIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'PopupIndicator',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popupIndicator, ownerState.popupOpen && styles.popupIndicatorOpen];\n  }\n})({\n  padding: 2,\n  marginRight: -2,\n  variants: [{\n    props: {\n      popupOpen: true\n    },\n    style: {\n      transform: 'rotate(180deg)'\n    }\n  }]\n});\nconst AutocompletePopper = styled(Popper, {\n  name: 'MuiAutocomplete',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${autocompleteClasses.option}`]: styles.option\n    }, styles.popper, ownerState.disablePortal && styles.popperDisablePortal];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.modal,\n  variants: [{\n    props: {\n      disablePortal: true\n    },\n    style: {\n      position: 'absolute'\n    }\n  }]\n})));\nconst AutocompletePaper = styled(Paper, {\n  name: 'MuiAutocomplete',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => styles.paper\n})(memoTheme(({\n  theme\n}) => ({\n  ...theme.typography.body1,\n  overflow: 'auto'\n})));\nconst AutocompleteLoading = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Loading',\n  overridesResolver: (props, styles) => styles.loading\n})(memoTheme(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '14px 16px'\n})));\nconst AutocompleteNoOptions = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'NoOptions',\n  overridesResolver: (props, styles) => styles.noOptions\n})(memoTheme(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '14px 16px'\n})));\nconst AutocompleteListbox = styled('ul', {\n  name: 'MuiAutocomplete',\n  slot: 'Listbox',\n  overridesResolver: (props, styles) => styles.listbox\n})(memoTheme(({\n  theme\n}) => ({\n  listStyle: 'none',\n  margin: 0,\n  padding: '8px 0',\n  maxHeight: '40vh',\n  overflow: 'auto',\n  position: 'relative',\n  [`& .${autocompleteClasses.option}`]: {\n    minHeight: 48,\n    display: 'flex',\n    overflow: 'hidden',\n    justifyContent: 'flex-start',\n    alignItems: 'center',\n    cursor: 'pointer',\n    paddingTop: 6,\n    boxSizing: 'border-box',\n    outline: '0',\n    WebkitTapHighlightColor: 'transparent',\n    paddingBottom: 6,\n    paddingLeft: 16,\n    paddingRight: 16,\n    [theme.breakpoints.up('sm')]: {\n      minHeight: 'auto'\n    },\n    [`&.${autocompleteClasses.focused}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.hover,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    '&[aria-disabled=\"true\"]': {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`&.${autocompleteClasses.focusVisible}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.focus\n    },\n    '&[aria-selected=\"true\"]': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n      [`&.${autocompleteClasses.focused}`]: {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: (theme.vars || theme).palette.action.selected\n        }\n      },\n      [`&.${autocompleteClasses.focusVisible}`]: {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n      }\n    }\n  }\n})));\nconst AutocompleteGroupLabel = styled(ListSubheader, {\n  name: 'MuiAutocomplete',\n  slot: 'GroupLabel',\n  overridesResolver: (props, styles) => styles.groupLabel\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  top: -8\n})));\nconst AutocompleteGroupUl = styled('ul', {\n  name: 'MuiAutocomplete',\n  slot: 'GroupUl',\n  overridesResolver: (props, styles) => styles.groupUl\n})({\n  padding: 0,\n  [`& .${autocompleteClasses.option}`]: {\n    paddingLeft: 24\n  }\n});\nexport { createFilterOptions };\nconst Autocomplete = /*#__PURE__*/React.forwardRef(function Autocomplete(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAutocomplete'\n  });\n\n  /* eslint-disable @typescript-eslint/no-unused-vars */\n  const {\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    ChipProps: ChipPropsProp,\n    className,\n    clearIcon = _ClearIcon || (_ClearIcon = /*#__PURE__*/_jsx(ClearIcon, {\n      fontSize: \"small\"\n    })),\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    clearText = 'Clear',\n    closeText = 'Close',\n    componentsProps,\n    defaultValue = props.multiple ? [] : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled = false,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    disablePortal = false,\n    filterOptions,\n    filterSelectedOptions = false,\n    forcePopupIcon = 'auto',\n    freeSolo = false,\n    fullWidth = false,\n    getLimitTagsText = more => `+${more}`,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp,\n    isOptionEqualToValue,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    limitTags = -1,\n    ListboxComponent: ListboxComponentProp,\n    ListboxProps: ListboxPropsProp,\n    loading = false,\n    loadingText = 'Loading…',\n    multiple = false,\n    noOptionsText = 'No options',\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open,\n    openOnFocus = false,\n    openText = 'Open',\n    options,\n    PaperComponent: PaperComponentProp,\n    PopperComponent: PopperComponentProp,\n    popupIcon = _ArrowDropDownIcon || (_ArrowDropDownIcon = /*#__PURE__*/_jsx(ArrowDropDownIcon, {})),\n    readOnly = false,\n    renderGroup: renderGroupProp,\n    renderInput,\n    renderOption: renderOptionProp,\n    renderTags,\n    selectOnFocus = !props.freeSolo,\n    size = 'medium',\n    slots = {},\n    slotProps = {},\n    value: valueProp,\n    ...other\n  } = props;\n  /* eslint-enable @typescript-eslint/no-unused-vars */\n\n  const {\n    getRootProps,\n    getInputProps,\n    getInputLabelProps,\n    getPopupIndicatorProps,\n    getClearProps,\n    getTagProps,\n    getListboxProps,\n    getOptionProps,\n    value,\n    dirty,\n    expanded,\n    id,\n    popupOpen,\n    focused,\n    focusedTag,\n    anchorEl,\n    setAnchorEl,\n    inputValue,\n    groupedOptions\n  } = useAutocomplete({\n    ...props,\n    componentName: 'Autocomplete'\n  });\n  const hasClearIcon = !disableClearable && !disabled && dirty && !readOnly;\n  const hasPopupIcon = (!freeSolo || forcePopupIcon === true) && forcePopupIcon !== false;\n  const {\n    onMouseDown: handleInputMouseDown\n  } = getInputProps();\n  const {\n    ref: listboxRef,\n    ...otherListboxProps\n  } = getListboxProps();\n  const defaultGetOptionLabel = option => option.label ?? option;\n  const getOptionLabel = getOptionLabelProp || defaultGetOptionLabel;\n\n  // If you modify this, make sure to keep the `AutocompleteOwnerState` type in sync.\n  const ownerState = {\n    ...props,\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    getOptionLabel,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused: focusedTag === -1,\n    popupOpen,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots: {\n      paper: PaperComponentProp,\n      popper: PopperComponentProp,\n      ...slots\n    },\n    slotProps: {\n      chip: ChipPropsProp,\n      listbox: ListboxPropsProp,\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [ListboxSlot, listboxProps] = useSlot('listbox', {\n    elementType: AutocompleteListbox,\n    externalForwardedProps,\n    ownerState,\n    className: classes.listbox,\n    additionalProps: otherListboxProps,\n    ref: listboxRef\n  });\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    elementType: Paper,\n    externalForwardedProps,\n    ownerState,\n    className: classes.paper\n  });\n  const [PopperSlot, popperProps] = useSlot('popper', {\n    elementType: Popper,\n    externalForwardedProps,\n    ownerState,\n    className: classes.popper,\n    additionalProps: {\n      disablePortal,\n      style: {\n        width: anchorEl ? anchorEl.clientWidth : null\n      },\n      role: 'presentation',\n      anchorEl,\n      open: popupOpen\n    }\n  });\n  let startAdornment;\n  if (multiple && value.length > 0) {\n    const getCustomizedTagProps = params => ({\n      className: classes.tag,\n      disabled,\n      ...getTagProps(params)\n    });\n    if (renderTags) {\n      startAdornment = renderTags(value, getCustomizedTagProps, ownerState);\n    } else {\n      startAdornment = value.map((option, index) => {\n        const {\n          key,\n          ...customTagProps\n        } = getCustomizedTagProps({\n          index\n        });\n        return /*#__PURE__*/_jsx(Chip, {\n          label: getOptionLabel(option),\n          size: size,\n          ...customTagProps,\n          ...externalForwardedProps.slotProps.chip\n        }, key);\n      });\n    }\n  }\n  if (limitTags > -1 && Array.isArray(startAdornment)) {\n    const more = startAdornment.length - limitTags;\n    if (!focused && more > 0) {\n      startAdornment = startAdornment.splice(0, limitTags);\n      startAdornment.push(/*#__PURE__*/_jsx(\"span\", {\n        className: classes.tag,\n        children: getLimitTagsText(more)\n      }, startAdornment.length));\n    }\n  }\n  const defaultRenderGroup = params => /*#__PURE__*/_jsxs(\"li\", {\n    children: [/*#__PURE__*/_jsx(AutocompleteGroupLabel, {\n      className: classes.groupLabel,\n      ownerState: ownerState,\n      component: \"div\",\n      children: params.group\n    }), /*#__PURE__*/_jsx(AutocompleteGroupUl, {\n      className: classes.groupUl,\n      ownerState: ownerState,\n      children: params.children\n    })]\n  }, params.key);\n  const renderGroup = renderGroupProp || defaultRenderGroup;\n  const defaultRenderOption = (props2, option) => {\n    // Need to clearly apply key because of https://github.com/vercel/next.js/issues/55642\n    const {\n      key,\n      ...otherProps\n    } = props2;\n    return /*#__PURE__*/_jsx(\"li\", {\n      ...otherProps,\n      children: getOptionLabel(option)\n    }, key);\n  };\n  const renderOption = renderOptionProp || defaultRenderOption;\n  const renderListOption = (option, index) => {\n    const optionProps = getOptionProps({\n      option,\n      index\n    });\n    return renderOption({\n      ...optionProps,\n      className: classes.option\n    }, option, {\n      selected: optionProps['aria-selected'],\n      index,\n      inputValue\n    }, ownerState);\n  };\n  const clearIndicatorSlotProps = externalForwardedProps.slotProps.clearIndicator;\n  const popupIndicatorSlotProps = externalForwardedProps.slotProps.popupIndicator;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(AutocompleteRoot, {\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ...getRootProps(other),\n      children: renderInput({\n        id,\n        disabled,\n        fullWidth: true,\n        size: size === 'small' ? 'small' : undefined,\n        InputLabelProps: getInputLabelProps(),\n        InputProps: {\n          ref: setAnchorEl,\n          className: classes.inputRoot,\n          startAdornment,\n          onMouseDown: event => {\n            if (event.target === event.currentTarget) {\n              handleInputMouseDown(event);\n            }\n          },\n          ...((hasClearIcon || hasPopupIcon) && {\n            endAdornment: /*#__PURE__*/_jsxs(AutocompleteEndAdornment, {\n              className: classes.endAdornment,\n              ownerState: ownerState,\n              children: [hasClearIcon ? /*#__PURE__*/_jsx(AutocompleteClearIndicator, {\n                ...getClearProps(),\n                \"aria-label\": clearText,\n                title: clearText,\n                ownerState: ownerState,\n                ...clearIndicatorSlotProps,\n                className: clsx(classes.clearIndicator, clearIndicatorSlotProps?.className),\n                children: clearIcon\n              }) : null, hasPopupIcon ? /*#__PURE__*/_jsx(AutocompletePopupIndicator, {\n                ...getPopupIndicatorProps(),\n                disabled: disabled,\n                \"aria-label\": popupOpen ? closeText : openText,\n                title: popupOpen ? closeText : openText,\n                ownerState: ownerState,\n                ...popupIndicatorSlotProps,\n                className: clsx(classes.popupIndicator, popupIndicatorSlotProps?.className),\n                children: popupIcon\n              }) : null]\n            })\n          })\n        },\n        inputProps: {\n          className: classes.input,\n          disabled,\n          readOnly,\n          ...getInputProps()\n        }\n      })\n    }), anchorEl ? /*#__PURE__*/_jsx(AutocompletePopper, {\n      as: PopperSlot,\n      ...popperProps,\n      children: /*#__PURE__*/_jsxs(AutocompletePaper, {\n        as: PaperSlot,\n        ...paperProps,\n        children: [loading && groupedOptions.length === 0 ? /*#__PURE__*/_jsx(AutocompleteLoading, {\n          className: classes.loading,\n          ownerState: ownerState,\n          children: loadingText\n        }) : null, groupedOptions.length === 0 && !freeSolo && !loading ? /*#__PURE__*/_jsx(AutocompleteNoOptions, {\n          className: classes.noOptions,\n          ownerState: ownerState,\n          role: \"presentation\",\n          onMouseDown: event => {\n            // Prevent input blur when interacting with the \"no options\" content\n            event.preventDefault();\n          },\n          children: noOptionsText\n        }) : null, groupedOptions.length > 0 ? /*#__PURE__*/_jsx(ListboxSlot, {\n          as: ListboxComponentProp,\n          ...listboxProps,\n          children: groupedOptions.map((option, index) => {\n            if (groupBy) {\n              return renderGroup({\n                key: option.key,\n                group: option.group,\n                children: option.options.map((option2, index2) => renderListOption(option2, option.index + index2))\n              });\n            }\n            return renderListOption(option, index);\n          })\n        }) : null]\n      })\n    }) : null]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Autocomplete.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the portion of the selected suggestion that the user hasn't typed,\n   * known as the completion string, appears inline after the input cursor in the textbox.\n   * The inline completion string is visually highlighted and has a selected state.\n   * @default false\n   */\n  autoComplete: PropTypes.bool,\n  /**\n   * If `true`, the first option is automatically highlighted.\n   * @default false\n   */\n  autoHighlight: PropTypes.bool,\n  /**\n   * If `true`, the selected option becomes the value of the input\n   * when the Autocomplete loses focus unless the user chooses\n   * a different option or changes the character string in the input.\n   *\n   * When using the `freeSolo` mode, the typed value will be the input value\n   * if the Autocomplete loses focus without highlighting an option.\n   * @default false\n   */\n  autoSelect: PropTypes.bool,\n  /**\n   * Control if the input should be blurred when an option is selected:\n   *\n   * - `false` the input is not blurred.\n   * - `true` the input is always blurred.\n   * - `touch` the input is blurred after a touch event.\n   * - `mouse` the input is blurred after a mouse event.\n   * @default false\n   */\n  blurOnSelect: PropTypes.oneOfType([PropTypes.oneOf(['mouse', 'touch']), PropTypes.bool]),\n  /**\n   * Props applied to the [`Chip`](https://mui.com/material-ui/api/chip/) element.\n   * @deprecated Use `slotProps.chip` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ChipProps: PropTypes.object,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display in place of the default clear icon.\n   * @default <ClearIcon fontSize=\"small\" />\n   */\n  clearIcon: PropTypes.node,\n  /**\n   * If `true`, the input's text is cleared on blur if no value is selected.\n   *\n   * Set it to `true` if you want to help the user enter a new value.\n   * Set it to `false` if you want to help the user resume their search.\n   * @default !props.freeSolo\n   */\n  clearOnBlur: PropTypes.bool,\n  /**\n   * If `true`, clear all values when the user presses escape and the popup is closed.\n   * @default false\n   */\n  clearOnEscape: PropTypes.bool,\n  /**\n   * Override the default text for the *clear* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Clear'\n   */\n  clearText: PropTypes.string,\n  /**\n   * Override the default text for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  componentsProps: PropTypes.shape({\n    clearIndicator: PropTypes.object,\n    paper: PropTypes.object,\n    popper: PropTypes.object,\n    popupIndicator: PropTypes.object\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default props.multiple ? [] : null\n   */\n  defaultValue: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.defaultValue !== undefined && !Array.isArray(props.defaultValue)) {\n      return new Error(['MUI: The Autocomplete expects the `defaultValue` prop to be an array when `multiple={true}` or undefined.', `However, ${props.defaultValue} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * If `true`, the input can't be cleared.\n   * @default false\n   */\n  disableClearable: PropTypes.bool,\n  /**\n   * If `true`, the popup won't close when a value is selected.\n   * @default false\n   */\n  disableCloseOnSelect: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, will allow focus on disabled items.\n   * @default false\n   */\n  disabledItemsFocusable: PropTypes.bool,\n  /**\n   * If `true`, the list box in the popup will not wrap focus.\n   * @default false\n   */\n  disableListWrap: PropTypes.bool,\n  /**\n   * If `true`, the `Popper` content will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * A function that determines the filtered options to be rendered on search.\n   *\n   * @default createFilterOptions()\n   * @param {Value[]} options The options to render.\n   * @param {object} state The state of the component.\n   * @returns {Value[]}\n   */\n  filterOptions: PropTypes.func,\n  /**\n   * If `true`, hide the selected options from the list box.\n   * @default false\n   */\n  filterSelectedOptions: PropTypes.bool,\n  /**\n   * Force the visibility display of the popup icon.\n   * @default 'auto'\n   */\n  forcePopupIcon: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.bool]),\n  /**\n   * If `true`, the Autocomplete is free solo, meaning that the user input is not bound to provided options.\n   * @default false\n   */\n  freeSolo: PropTypes.bool,\n  /**\n   * If `true`, the input will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * The label to display when the tags are truncated (`limitTags`).\n   *\n   * @param {number} more The number of truncated tags.\n   * @returns {ReactNode}\n   * @default (more) => `+${more}`\n   */\n  getLimitTagsText: PropTypes.func,\n  /**\n   * Used to determine the disabled state for a given option.\n   *\n   * @param {Value} option The option to test.\n   * @returns {boolean}\n   */\n  getOptionDisabled: PropTypes.func,\n  /**\n   * Used to determine the key for a given option.\n   * This can be useful when the labels of options are not unique (since labels are used as keys by default).\n   *\n   * @param {Value} option The option to get the key for.\n   * @returns {string | number}\n   */\n  getOptionKey: PropTypes.func,\n  /**\n   * Used to determine the string value for a given option.\n   * It's used to fill the input (and the list box options if `renderOption` is not provided).\n   *\n   * If used in free solo mode, it must accept both the type of the options and a string.\n   *\n   * @param {Value} option\n   * @returns {string}\n   * @default (option) => option.label ?? option\n   */\n  getOptionLabel: PropTypes.func,\n  /**\n   * If provided, the options will be grouped under the returned string.\n   * The groupBy value is also used as the text for group headings when `renderGroup` is not provided.\n   *\n   * @param {Value} option The Autocomplete option.\n   * @returns {string}\n   */\n  groupBy: PropTypes.func,\n  /**\n   * If `true`, the component handles the \"Home\" and \"End\" keys when the popup is open.\n   * It should move focus to the first option and last option, respectively.\n   * @default !props.freeSolo\n   */\n  handleHomeEndKeys: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide an id it will fall back to a randomly generated one.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the highlight can move to the input.\n   * @default false\n   */\n  includeInputInList: PropTypes.bool,\n  /**\n   * The input value.\n   */\n  inputValue: PropTypes.string,\n  /**\n   * Used to determine if the option represents the given value.\n   * Uses strict equality by default.\n   * ⚠️ Both arguments need to be handled, an option can only match with one value.\n   *\n   * @param {Value} option The option to test.\n   * @param {Value} value The value to test against.\n   * @returns {boolean}\n   */\n  isOptionEqualToValue: PropTypes.func,\n  /**\n   * The maximum number of tags that will be visible when not focused.\n   * Set `-1` to disable the limit.\n   * @default -1\n   */\n  limitTags: integerPropType,\n  /**\n   * The component used to render the listbox.\n   * @default 'ul'\n   * @deprecated Use `slotProps.listbox.component` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ListboxComponent: PropTypes.elementType,\n  /**\n   * Props applied to the Listbox element.\n   * @deprecated Use `slotProps.listbox` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ListboxProps: PropTypes.object,\n  /**\n   * If `true`, the component is in a loading state.\n   * This shows the `loadingText` in place of suggestions (only if there are no suggestions to show, for example `options` are empty).\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Text to display when in a loading state.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Loading…'\n   */\n  loadingText: PropTypes.node,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   * @default false\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Text to display when there are no options.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'No options'\n   */\n  noOptionsText: PropTypes.node,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value|Value[]} value The new value of the component.\n   * @param {string} reason One of \"createOption\", \"selectOption\", \"removeOption\", \"blur\" or \"clear\".\n   * @param {string} [details]\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggleInput\"`, `\"escape\"`, `\"selectOption\"`, `\"removeOption\"`, `\"blur\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the highlight option changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value} option The highlighted option.\n   * @param {string} reason Can be: `\"keyboard\"`, `\"mouse\"`, `\"touch\"`.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * Callback fired when the input value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} value The new value of the text input.\n   * @param {string} reason Can be: `\"input\"` (user input), `\"reset\"` (programmatic change), `\"clear\"`, `\"blur\"`, `\"selectOption\"`, `\"removeOption\"`\n   */\n  onInputChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * If `true`, the popup will open on input focus.\n   * @default false\n   */\n  openOnFocus: PropTypes.bool,\n  /**\n   * Override the default text for the *open popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Open'\n   */\n  openText: PropTypes.string,\n  /**\n   * A list of options that will be shown in the Autocomplete.\n   */\n  options: PropTypes.array.isRequired,\n  /**\n   * The component used to render the body of the popup.\n   * @default Paper\n   * @deprecated Use `slots.paper` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PaperComponent: PropTypes.elementType,\n  /**\n   * The component used to position the popup.\n   * @default Popper\n   * @deprecated Use `slots.popper` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * The icon to display in place of the default popup icon.\n   * @default <ArrowDropDownIcon />\n   */\n  popupIcon: PropTypes.node,\n  /**\n   * If `true`, the component becomes readonly. It is also supported for multiple tags where the tag cannot be deleted.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the group.\n   *\n   * @param {AutocompleteRenderGroupParams} params The group to render.\n   * @returns {ReactNode}\n   */\n  renderGroup: PropTypes.func,\n  /**\n   * Render the input.\n   *\n   * @param {object} params\n   * @returns {ReactNode}\n   */\n  renderInput: PropTypes.func.isRequired,\n  /**\n   * Render the option, use `getOptionLabel` by default.\n   *\n   * @param {object} props The props to apply on the li element.\n   * @param {Value} option The option to render.\n   * @param {object} state The state of each option.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderOption: PropTypes.func,\n  /**\n   * Render the selected value.\n   *\n   * @param {Value[]} value The `value` provided to the component.\n   * @param {function} getTagProps A tag props getter.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderTags: PropTypes.func,\n  /**\n   * If `true`, the input's text is selected on focus.\n   * It helps the user clear the selected value.\n   * @default !props.freeSolo\n   */\n  selectOnFocus: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    chip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    clearIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popupIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    listbox: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    popper: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the autocomplete.\n   *\n   * The value must have reference equality with the option in order to be selected.\n   * You can customize the equality behavior with the `isOptionEqualToValue` prop.\n   */\n  value: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.value !== undefined && !Array.isArray(props.value)) {\n      return new Error(['MUI: The Autocomplete expects the `value` prop to be an array when `multiple={true}` or undefined.', `However, ${props.value} was provided.`].join('\\n'));\n    }\n    return null;\n  })\n} : void 0;\nexport default Autocomplete;", "'use client';\n\n/* eslint-disable no-constant-condition */\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel) {\n  if (multiple || value == null) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => option.label ?? option,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // useRef ensures it doesn't update dynamically with defaultValue or value props.\n  const initialInputValue = React.useRef(getInputValue(defaultValue ?? valueProp, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange && ['mouse', 'keyboard', 'touch'].includes(reason)) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => ({\n      ...other,\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => ({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: handleTagDelete(index)\n      })\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionKey?.(option) ?? getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAutocompleteUtilityClass(slot) {\n  return generateUtilityClass('MuiAutocomplete', slot);\n}\nconst autocompleteClasses = generateUtilityClasses('MuiAutocomplete', ['root', 'expanded', 'fullWidth', 'focused', 'focusVisible', 'tag', 'tagSizeSmall', 'tagSizeMedium', 'hasPopupIcon', 'hasClearIcon', 'inputRoot', 'input', 'inputFocused', 'endAdornment', 'clearIndicator', 'popupIndicator', 'popupIndicatorOpen', 'popper', 'popperDisablePortal', 'paper', 'listbox', 'loading', 'noOptions', 'option', 'groupLabel', 'groupUl']);\nexport default autocompleteClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,SAAuB;AACvB,wBAAsB;;;ACDtB,YAAuB;AAIvB,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,OAAO,UAAU,KAAK,EAAE,QAAQ,oBAAoB,EAAE;AAC/D;AACO,SAAS,oBAAoB,SAAS,CAAC,GAAG;AAC/C,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,EACT,IAAI;AACJ,SAAO,CAAC,SAAS;AAAA,IACf;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,QAAQ,OAAO,WAAW,KAAK,IAAI;AACvC,QAAI,YAAY;AACd,cAAQ,MAAM,YAAY;AAAA,IAC5B;AACA,QAAI,eAAe;AACjB,cAAQ,gBAAgB,KAAK;AAAA,IAC/B;AACA,UAAM,kBAAkB,CAAC,QAAQ,UAAU,QAAQ,OAAO,YAAU;AAClE,UAAI,aAAa,aAAa,gBAAgB,MAAM;AACpD,UAAI,YAAY;AACd,oBAAY,UAAU,YAAY;AAAA,MACpC;AACA,UAAI,eAAe;AACjB,oBAAY,gBAAgB,SAAS;AAAA,MACvC;AACA,aAAO,cAAc,UAAU,UAAU,WAAW,KAAK,IAAI,UAAU,SAAS,KAAK;AAAA,IACvF,CAAC;AACD,WAAO,OAAO,UAAU,WAAW,gBAAgB,MAAM,GAAG,KAAK,IAAI;AAAA,EACvE;AACF;AACA,IAAM,uBAAuB,oBAAoB;AAGjD,IAAM,WAAW;AACjB,IAAM,kCAAkC,gBAAW;AA/CnD;AA+CsD,oBAAW,YAAY,UAAQ,gBAAW,QAAQ,kBAAnB,mBAAkC,SAAS,SAAS;AAAA;AACzI,IAAM,yBAAyB,CAAC;AAChC,SAAS,cAAc,OAAO,UAAU,gBAAgB;AACtD,MAAI,YAAY,SAAS,MAAM;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,cAAc,eAAe,KAAK;AACxC,SAAO,OAAO,gBAAgB,WAAW,cAAc;AACzD;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM;AAAA;AAAA,IAEJ,oCAAoC;AAAA;AAAA,IAEpC,2BAA2B;AAAA,IAC3B,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,eAAe;AAAA,IACf,cAAc,CAAC,MAAM;AAAA,IACrB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,eAAe,MAAM,WAAW,yBAAyB;AAAA,IACzD,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,UAAU;AAAA,IACV,yBAAyB;AAAA,IACzB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,IACxB,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,gBAAgB,qBAAqB,YAAU,OAAO,SAAS;AAAA,IAC/D;AAAA,IACA,oBAAoB,CAAC,MAAM;AAAA,IAC3B,IAAI;AAAA,IACJ,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,uBAAuB,CAAC,QAAQC,WAAU,WAAWA;AAAA,IACrD,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,IACA,WAAW;AAAA,IACX,gBAAgB,CAAC,MAAM;AAAA,IACvB,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,KAAK,MAAM,MAAM;AACvB,MAAI,iBAAiB;AACrB,mBAAiB,YAAU;AACzB,UAAM,cAAc,mBAAmB,MAAM;AAC7C,QAAI,OAAO,gBAAgB,UAAU;AACnC,UAAI,MAAuC;AACzC,cAAM,kBAAkB,gBAAgB,SAAY,cAAc,GAAG,OAAO,WAAW,KAAK,WAAW;AACvG,gBAAQ,MAAM,yCAAyC,aAAa,aAAa,eAAe,4BAA4B,KAAK,UAAU,MAAM,CAAC,GAAG;AAAA,MACvJ;AACA,aAAO,OAAO,WAAW;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAoB,aAAO,KAAK;AACtC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,WAAiB,aAAO,IAAI;AAClC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,IAAI;AACnD,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,EAAE;AACrD,QAAM,qBAAqB,gBAAgB,IAAI;AAC/C,QAAM,sBAA4B,aAAO,kBAAkB;AAI3D,QAAM,oBAA0B,aAAO,cAAc,gBAAgB,WAAW,UAAU,cAAc,CAAC,EAAE;AAC3G,QAAM,CAAC,OAAO,aAAa,IAAI,cAAc;AAAA,IAC3C,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,EACR,CAAC;AACD,QAAM,CAAC,YAAY,kBAAkB,IAAI,cAAc;AAAA,IACrD,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAClD,QAAM,kBAAwB,kBAAY,CAAC,OAAO,UAAU,WAAW;AAGrE,UAAM,mBAAmB,WAAW,MAAM,SAAS,SAAS,SAAS,aAAa;AAClF,QAAI,CAAC,oBAAoB,CAAC,aAAa;AACrC;AAAA,IACF;AACA,UAAM,gBAAgB,cAAc,UAAU,UAAU,cAAc;AACtE,QAAI,eAAe,eAAe;AAChC;AAAA,IACF;AACA,uBAAmB,aAAa;AAChC,QAAI,eAAe;AACjB,oBAAc,OAAO,eAAe,MAAM;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,gBAAgB,YAAY,UAAU,eAAe,oBAAoB,aAAa,KAAK,CAAC;AAChG,QAAM,CAAC,MAAM,YAAY,IAAI,cAAc;AAAA,IACzC,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,eAAe,gBAAgB,IAAU,eAAS,IAAI;AAC7D,QAAM,4BAA4B,CAAC,YAAY,SAAS,QAAQ,eAAe,eAAe,KAAK;AACnG,QAAM,YAAY,QAAQ,CAAC;AAC3B,QAAM,kBAAkB,YAAY;AAAA,IAAc,QAAQ,OAAO,YAAU;AACzE,UAAI,0BAA0B,WAAW,QAAQ,CAAC,KAAK,GAAG,KAAK,YAAU,WAAW,QAAQ,qBAAqB,QAAQ,MAAM,CAAC,GAAG;AACjI,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;AAAA,IAGD;AAAA,MACE,YAAY,6BAA6B,gBAAgB,KAAK;AAAA,MAC9D;AAAA,IACF;AAAA,EAAC,IAAI,CAAC;AACN,QAAM,gBAAgB,yBAAiB;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,EAAM,gBAAU,MAAM;AACpB,UAAM,cAAc,UAAU,cAAc;AAC5C,QAAI,WAAW,CAAC,aAAa;AAC3B;AAAA,IACF;AAGA,QAAI,YAAY,CAAC,aAAa;AAC5B;AAAA,IACF;AACA,oBAAgB,MAAM,OAAO,OAAO;AAAA,EACtC,GAAG,CAAC,OAAO,iBAAiB,SAAS,cAAc,OAAO,QAAQ,CAAC;AACnE,QAAM,mBAAmB,QAAQ,gBAAgB,SAAS,KAAK,CAAC;AAChE,QAAM,WAAW,yBAAiB,gBAAc;AAC9C,QAAI,eAAe,IAAI;AACrB,eAAS,QAAQ,MAAM;AAAA,IACzB,OAAO;AACL,eAAS,cAAc,oBAAoB,UAAU,IAAI,EAAE,MAAM;AAAA,IACnE;AAAA,EACF,CAAC;AAGD,EAAM,gBAAU,MAAM;AACpB,QAAI,YAAY,aAAa,MAAM,SAAS,GAAG;AAC7C,oBAAc,EAAE;AAChB,eAAS,EAAE;AAAA,IACb;AAAA,EACF,GAAG,CAAC,OAAO,UAAU,YAAY,QAAQ,CAAC;AAC1C,WAAS,iBAAiB,OAAO,WAAW;AAC1C,QAAI,CAAC,WAAW,WAAW,QAAQ,KAAK,SAAS,gBAAgB,QAAQ;AACvE,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AAChB,WAAO,MAAM;AACX,YAAM,SAAS,WAAW,QAAQ,cAAc,uBAAuB,SAAS,IAAI;AAGpF,YAAM,oBAAoB,yBAAyB,QAAQ,CAAC,UAAU,OAAO,YAAY,OAAO,aAAa,eAAe,MAAM;AAClI,UAAI,UAAU,OAAO,aAAa,UAAU,KAAK,CAAC,mBAAmB;AAEnE,eAAO;AAAA,MACT;AAIA,UAAI,cAAc,QAAQ;AACxB,qBAAa,YAAY,KAAK,gBAAgB;AAAA,MAChD,OAAO;AACL,qBAAa,YAAY,IAAI,gBAAgB,UAAU,gBAAgB;AAAA,MACzE;AAIA,UAAI,cAAc,OAAO;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,sBAAsB,yBAAiB,CAAC;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,wBAAoB,UAAU;AAG9B,QAAI,UAAU,IAAI;AAChB,eAAS,QAAQ,gBAAgB,uBAAuB;AAAA,IAC1D,OAAO;AACL,eAAS,QAAQ,aAAa,yBAAyB,GAAG,EAAE,WAAW,KAAK,EAAE;AAAA,IAChF;AACA,QAAI,qBAAqB,CAAC,SAAS,YAAY,OAAO,EAAE,SAAS,MAAM,GAAG;AACxE,wBAAkB,OAAO,UAAU,KAAK,OAAO,gBAAgB,KAAK,GAAG,MAAM;AAAA,IAC/E;AACA,QAAI,CAAC,WAAW,SAAS;AACvB;AAAA,IACF;AACA,UAAM,OAAO,WAAW,QAAQ,cAAc,mBAAmB,wBAAwB,UAAU;AACnG,QAAI,MAAM;AACR,WAAK,UAAU,OAAO,GAAG,wBAAwB,UAAU;AAC3D,WAAK,UAAU,OAAO,GAAG,wBAAwB,eAAe;AAAA,IAClE;AACA,QAAI,cAAc,WAAW;AAC7B,QAAI,WAAW,QAAQ,aAAa,MAAM,MAAM,WAAW;AACzD,oBAAc,WAAW,QAAQ,cAAc,cAAc,kBAAkB;AAAA,IACjF;AAGA,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,QAAI,UAAU,IAAI;AAChB,kBAAY,YAAY;AACxB;AAAA,IACF;AACA,UAAM,SAAS,WAAW,QAAQ,cAAc,uBAAuB,KAAK,IAAI;AAChF,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,UAAU,IAAI,GAAG,wBAAwB,UAAU;AAC1D,QAAI,WAAW,YAAY;AACzB,aAAO,UAAU,IAAI,GAAG,wBAAwB,eAAe;AAAA,IACjE;AAOA,QAAI,YAAY,eAAe,YAAY,gBAAgB,WAAW,WAAW,WAAW,SAAS;AACnG,YAAM,UAAU;AAChB,YAAM,eAAe,YAAY,eAAe,YAAY;AAC5D,YAAM,gBAAgB,QAAQ,YAAY,QAAQ;AAClD,UAAI,gBAAgB,cAAc;AAChC,oBAAY,YAAY,gBAAgB,YAAY;AAAA,MACtD,WAAW,QAAQ,YAAY,QAAQ,gBAAgB,UAAU,MAAM,KAAK,YAAY,WAAW;AACjG,oBAAY,YAAY,QAAQ,YAAY,QAAQ,gBAAgB,UAAU,MAAM;AAAA,MACtF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,yBAAyB,yBAAiB,CAAC;AAAA,IAC/C;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,EACF,MAAM;AACJ,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,UAAM,eAAe,MAAM;AACzB,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,SAAS,SAAS;AACpB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,SAAS;AACpB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,OAAO;AAClB,eAAO;AAAA,MACT;AACA,YAAM,WAAW,oBAAoB,UAAU;AAC/C,UAAI,WAAW,GAAG;AAChB,YAAI,aAAa,MAAM,oBAAoB;AACzC,iBAAO;AAAA,QACT;AACA,YAAI,mBAAmB,oBAAoB,YAAY,MAAM,KAAK,IAAI,IAAI,IAAI,GAAG;AAC/E,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,UAAI,WAAW,UAAU;AACvB,YAAI,aAAa,WAAW,KAAK,oBAAoB;AACnD,iBAAO;AAAA,QACT;AACA,YAAI,mBAAmB,KAAK,IAAI,IAAI,IAAI,GAAG;AACzC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,YAAY,iBAAiB,aAAa,GAAG,SAAS;AAC5D,wBAAoB;AAAA,MAClB,OAAO;AAAA,MACP;AAAA,MACA;AAAA,IACF,CAAC;AAGD,QAAI,gBAAgB,SAAS,SAAS;AACpC,UAAI,cAAc,IAAI;AACpB,iBAAS,QAAQ,QAAQ;AAAA,MAC3B,OAAO;AACL,cAAM,SAAS,eAAe,gBAAgB,SAAS,CAAC;AACxD,iBAAS,QAAQ,QAAQ;AAIzB,cAAM,QAAQ,OAAO,YAAY,EAAE,QAAQ,WAAW,YAAY,CAAC;AACnE,YAAI,UAAU,KAAK,WAAW,SAAS,GAAG;AACxC,mBAAS,QAAQ,kBAAkB,WAAW,QAAQ,OAAO,MAAM;AAAA,QACrE;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,oCAAoC,MAAM;AAC9C,UAAM,cAAc,CAAC,QAAQ,WAAW;AACtC,YAAM,SAAS,SAAS,eAAe,MAAM,IAAI;AACjD,YAAM,SAAS,SAAS,eAAe,MAAM,IAAI;AACjD,aAAO,WAAW;AAAA,IACpB;AACA,QAAI,oBAAoB,YAAY,MAAM,cAAc,mBAAmB,cAAc,gBAAgB,WAAW,gBAAgB,UAAU,cAAc,eAAe,eAAe,WAAW,MAAM,WAAW,cAAc,MAAM,UAAU,cAAc,MAAM,MAAM,CAAC,KAAK,MAAM,eAAe,MAAM,CAAC,CAAC,MAAM,eAAe,GAAG,CAAC,IAAI,YAAY,cAAc,OAAO,KAAK,IAAI;AACtX,YAAM,4BAA4B,cAAc,gBAAgB,oBAAoB,OAAO;AAC3F,UAAI,2BAA2B;AAC7B,eAAO,gBAAgB,UAAU,YAAU;AACzC,iBAAO,eAAe,MAAM,MAAM,eAAe,yBAAyB;AAAA,QAC5E,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,uBAA6B,kBAAY,MAAM;AACnD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAIA,UAAM,iCAAiC,kCAAkC;AACzE,QAAI,mCAAmC,IAAI;AACzC,0BAAoB,UAAU;AAC9B;AAAA,IACF;AACA,UAAM,YAAY,WAAW,MAAM,CAAC,IAAI;AAGxC,QAAI,gBAAgB,WAAW,KAAK,aAAa,MAAM;AACrD,6BAAuB;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AACD;AAAA,IACF;AACA,QAAI,CAAC,WAAW,SAAS;AACvB;AAAA,IACF;AAGA,QAAI,aAAa,MAAM;AACrB,YAAM,gBAAgB,gBAAgB,oBAAoB,OAAO;AAGjE,UAAI,YAAY,iBAAiB,MAAM,UAAU,SAAO,qBAAqB,eAAe,GAAG,CAAC,MAAM,IAAI;AACxG;AAAA,MACF;AACA,YAAM,YAAY,gBAAgB,UAAU,gBAAc,qBAAqB,YAAY,SAAS,CAAC;AACrG,UAAI,cAAc,IAAI;AACpB,+BAAuB;AAAA,UACrB,MAAM;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AACL,4BAAoB;AAAA,UAClB,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA;AAAA,IACF;AAGA,QAAI,oBAAoB,WAAW,gBAAgB,SAAS,GAAG;AAC7D,0BAAoB;AAAA,QAClB,OAAO,gBAAgB,SAAS;AAAA,MAClC,CAAC;AACD;AAAA,IACF;AAGA,wBAAoB;AAAA,MAClB,OAAO,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EAGH,GAAG;AAAA;AAAA,IAEH,gBAAgB;AAAA;AAAA;AAAA,IAGhB,WAAW,QAAQ;AAAA,IAAO;AAAA,IAAuB;AAAA,IAAwB;AAAA,IAAqB;AAAA,IAAW;AAAA,IAAY;AAAA,EAAQ,CAAC;AAC9H,QAAM,mBAAmB,yBAAiB,UAAQ;AAChD,WAAO,YAAY,IAAI;AACvB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,yBAAqB;AAAA,EACvB,CAAC;AACD,MAAI,MAAuC;AAEzC,IAAM,gBAAU,MAAM;AACpB,UAAI,CAAC,SAAS,WAAW,SAAS,QAAQ,aAAa,SAAS;AAC9D,YAAI,SAAS,WAAW,SAAS,QAAQ,aAAa,YAAY;AAChE,kBAAQ,KAAK,CAAC,sCAAsC,aAAa,8BAA8B,8EAA8E,8GAA8G,mFAAmF,EAAE,KAAK,IAAI,CAAC;AAAA,QAC5X,OAAO;AACL,kBAAQ,MAAM,CAAC,6DAA6D,SAAS,OAAO,4CAA4C,YAAY,aAAa,8BAA8B,IAAI,kBAAkB,oBAAoB,qHAAqH,8DAA8D,EAAE,KAAK,IAAI,CAAC;AAAA,QAC1a;AAAA,MACF;AAAA,IACF,GAAG,CAAC,aAAa,CAAC;AAAA,EACpB;AACA,EAAM,gBAAU,MAAM;AACpB,yBAAqB;AAAA,EACvB,GAAG,CAAC,oBAAoB,CAAC;AACzB,QAAM,aAAa,WAAS;AAC1B,QAAI,MAAM;AACR;AAAA,IACF;AACA,iBAAa,IAAI;AACjB,qBAAiB,IAAI;AACrB,QAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,cAAc,CAAC,OAAO,WAAW;AACrC,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,iBAAa,KAAK;AAClB,QAAI,SAAS;AACX,cAAQ,OAAO,MAAM;AAAA,IACvB;AAAA,EACF;AACA,QAAM,cAAc,CAAC,OAAO,UAAU,QAAQ,YAAY;AACxD,QAAI,UAAU;AACZ,UAAI,MAAM,WAAW,SAAS,UAAU,MAAM,MAAM,CAAC,KAAK,MAAM,QAAQ,SAAS,CAAC,CAAC,GAAG;AACpF;AAAA,MACF;AAAA,IACF,WAAW,UAAU,UAAU;AAC7B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,eAAS,OAAO,UAAU,QAAQ,OAAO;AAAA,IAC3C;AACA,kBAAc,QAAQ;AAAA,EACxB;AACA,QAAM,UAAgB,aAAO,KAAK;AAClC,QAAM,iBAAiB,CAAC,OAAO,QAAQ,aAAa,gBAAgB,SAAS,cAAc;AACzF,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,UAAU;AACZ,iBAAW,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI,CAAC;AACnD,UAAI,MAAuC;AACzC,cAAM,UAAU,SAAS,OAAO,SAAO,qBAAqB,QAAQ,GAAG,CAAC;AACxE,YAAI,QAAQ,SAAS,GAAG;AACtB,kBAAQ,MAAM,CAAC,+CAA+C,aAAa,6CAA6C,0EAA0E,QAAQ,MAAM,WAAW,EAAE,KAAK,IAAI,CAAC;AAAA,QACzO;AAAA,MACF;AACA,YAAM,YAAY,SAAS,UAAU,eAAa,qBAAqB,QAAQ,SAAS,CAAC;AACzF,UAAI,cAAc,IAAI;AACpB,iBAAS,KAAK,MAAM;AAAA,MACtB,WAAW,WAAW,YAAY;AAChC,iBAAS,OAAO,WAAW,CAAC;AAC5B,iBAAS;AAAA,MACX;AAAA,IACF;AACA,oBAAgB,OAAO,UAAU,MAAM;AACvC,gBAAY,OAAO,UAAU,QAAQ;AAAA,MACnC;AAAA,IACF,CAAC;AACD,QAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,MAAM,WAAW,CAAC,MAAM,UAAU;AACzE,kBAAY,OAAO,MAAM;AAAA,IAC3B;AACA,QAAI,iBAAiB,QAAQ,iBAAiB,WAAW,QAAQ,WAAW,iBAAiB,WAAW,CAAC,QAAQ,SAAS;AACxH,eAAS,QAAQ,KAAK;AAAA,IACxB;AAAA,EACF;AACA,WAAS,cAAc,OAAO,WAAW;AACvC,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AAChB,WAAO,MAAM;AAEX,UAAI,cAAc,UAAU,cAAc,MAAM,UAAU,cAAc,cAAc,cAAc,IAAI;AACtG,eAAO;AAAA,MACT;AACA,YAAM,SAAS,SAAS,cAAc,oBAAoB,SAAS,IAAI;AAGvE,UAAI,CAAC,UAAU,CAAC,OAAO,aAAa,UAAU,KAAK,OAAO,YAAY,OAAO,aAAa,eAAe,MAAM,QAAQ;AACrH,qBAAa,cAAc,SAAS,IAAI;AAAA,MAC1C,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,OAAO,cAAc;AAC3C,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,QAAI,eAAe,IAAI;AACrB,kBAAY,OAAO,aAAa;AAAA,IAClC;AACA,QAAI,UAAU;AACd,QAAI,eAAe,IAAI;AACrB,UAAI,eAAe,MAAM,cAAc,YAAY;AACjD,kBAAU,MAAM,SAAS;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,iBAAW,cAAc,SAAS,IAAI;AACtC,UAAI,UAAU,GAAG;AACf,kBAAU;AAAA,MACZ;AACA,UAAI,YAAY,MAAM,QAAQ;AAC5B,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,cAAU,cAAc,SAAS,SAAS;AAC1C,kBAAc,OAAO;AACrB,aAAS,OAAO;AAAA,EAClB;AACA,QAAM,cAAc,WAAS;AAC3B,gBAAY,UAAU;AACtB,uBAAmB,EAAE;AACrB,QAAI,eAAe;AACjB,oBAAc,OAAO,IAAI,OAAO;AAAA,IAClC;AACA,gBAAY,OAAO,WAAW,CAAC,IAAI,MAAM,OAAO;AAAA,EAClD;AACA,QAAM,gBAAgB,WAAS,WAAS;AACtC,QAAI,MAAM,WAAW;AACnB,YAAM,UAAU,KAAK;AAAA,IACvB;AACA,QAAI,MAAM,qBAAqB;AAC7B;AAAA,IACF;AACA,QAAI,eAAe,MAAM,CAAC,CAAC,aAAa,YAAY,EAAE,SAAS,MAAM,GAAG,GAAG;AACzE,oBAAc,EAAE;AAChB,eAAS,EAAE;AAAA,IACb;AAGA,QAAI,MAAM,UAAU,KAAK;AACvB,cAAQ,MAAM,KAAK;AAAA,QACjB,KAAK;AACH,cAAI,aAAa,mBAAmB;AAElC,kBAAM,eAAe;AACrB,mCAAuB;AAAA,cACrB,MAAM;AAAA,cACN,WAAW;AAAA,cACX,QAAQ;AAAA,cACR;AAAA,YACF,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AACH,cAAI,aAAa,mBAAmB;AAElC,kBAAM,eAAe;AACrB,mCAAuB;AAAA,cACrB,MAAM;AAAA,cACN,WAAW;AAAA,cACX,QAAQ;AAAA,cACR;AAAA,YACF,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM,CAAC;AAAA,YACP,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AACH,yBAAe,OAAO,UAAU;AAChC;AAAA,QACF,KAAK;AACH,yBAAe,OAAO,MAAM;AAC5B;AAAA,QACF,KAAK;AACH,cAAI,oBAAoB,YAAY,MAAM,WAAW;AACnD,kBAAM,SAAS,gBAAgB,oBAAoB,OAAO;AAC1D,kBAAM,WAAW,oBAAoB,kBAAkB,MAAM,IAAI;AAGjE,kBAAM,eAAe;AACrB,gBAAI,UAAU;AACZ;AAAA,YACF;AACA,2BAAe,OAAO,QAAQ,cAAc;AAG5C,gBAAI,cAAc;AAChB,uBAAS,QAAQ,kBAAkB,SAAS,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,MAAM;AAAA,YACjG;AAAA,UACF,WAAW,YAAY,eAAe,MAAM,8BAA8B,OAAO;AAC/E,gBAAI,UAAU;AAEZ,oBAAM,eAAe;AAAA,YACvB;AACA,2BAAe,OAAO,YAAY,gBAAgB,UAAU;AAAA,UAC9D;AACA;AAAA,QACF,KAAK;AACH,cAAI,WAAW;AAEb,kBAAM,eAAe;AAErB,kBAAM,gBAAgB;AACtB,wBAAY,OAAO,QAAQ;AAAA,UAC7B,WAAW,kBAAkB,eAAe,MAAM,YAAY,MAAM,SAAS,IAAI;AAE/E,kBAAM,eAAe;AAErB,kBAAM,gBAAgB;AACtB,wBAAY,KAAK;AAAA,UACnB;AACA;AAAA,QACF,KAAK;AAEH,cAAI,YAAY,CAAC,YAAY,eAAe,MAAM,MAAM,SAAS,GAAG;AAClE,kBAAM,QAAQ,eAAe,KAAK,MAAM,SAAS,IAAI;AACrD,kBAAM,WAAW,MAAM,MAAM;AAC7B,qBAAS,OAAO,OAAO,CAAC;AACxB,wBAAY,OAAO,UAAU,gBAAgB;AAAA,cAC3C,QAAQ,MAAM,KAAK;AAAA,YACrB,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AAEH,cAAI,YAAY,CAAC,YAAY,eAAe,MAAM,MAAM,SAAS,KAAK,eAAe,IAAI;AACvF,kBAAM,QAAQ;AACd,kBAAM,WAAW,MAAM,MAAM;AAC7B,qBAAS,OAAO,OAAO,CAAC;AACxB,wBAAY,OAAO,UAAU,gBAAgB;AAAA,cAC3C,QAAQ,MAAM,KAAK;AAAA,YACrB,CAAC;AAAA,UACH;AACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc,WAAS;AAC3B,eAAW,IAAI;AACf,QAAI,eAAe,CAAC,YAAY,SAAS;AACvC,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,aAAa,WAAS;AAE1B,QAAI,kCAAkC,UAAU,GAAG;AACjD,eAAS,QAAQ,MAAM;AACvB;AAAA,IACF;AACA,eAAW,KAAK;AAChB,eAAW,UAAU;AACrB,gBAAY,UAAU;AACtB,QAAI,cAAc,oBAAoB,YAAY,MAAM,WAAW;AACjE,qBAAe,OAAO,gBAAgB,oBAAoB,OAAO,GAAG,MAAM;AAAA,IAC5E,WAAW,cAAc,YAAY,eAAe,IAAI;AACtD,qBAAe,OAAO,YAAY,QAAQ,UAAU;AAAA,IACtD,WAAW,aAAa;AACtB,sBAAgB,OAAO,OAAO,MAAM;AAAA,IACtC;AACA,gBAAY,OAAO,MAAM;AAAA,EAC3B;AACA,QAAM,oBAAoB,WAAS;AACjC,UAAM,WAAW,MAAM,OAAO;AAC9B,QAAI,eAAe,UAAU;AAC3B,yBAAmB,QAAQ;AAC3B,uBAAiB,KAAK;AACtB,UAAI,eAAe;AACjB,sBAAc,OAAO,UAAU,OAAO;AAAA,MACxC;AAAA,IACF;AACA,QAAI,aAAa,IAAI;AACnB,UAAI,CAAC,oBAAoB,CAAC,UAAU;AAClC,oBAAY,OAAO,MAAM,OAAO;AAAA,MAClC;AAAA,IACF,OAAO;AACL,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,wBAAwB,WAAS;AACrC,UAAM,QAAQ,OAAO,MAAM,cAAc,aAAa,mBAAmB,CAAC;AAC1E,QAAI,oBAAoB,YAAY,OAAO;AACzC,0BAAoB;AAAA,QAClB;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,yBAAyB,WAAS;AACtC,wBAAoB;AAAA,MAClB;AAAA,MACA,OAAO,OAAO,MAAM,cAAc,aAAa,mBAAmB,CAAC;AAAA,MACnE,QAAQ;AAAA,IACV,CAAC;AACD,YAAQ,UAAU;AAAA,EACpB;AACA,QAAM,oBAAoB,WAAS;AACjC,UAAM,QAAQ,OAAO,MAAM,cAAc,aAAa,mBAAmB,CAAC;AAC1E,mBAAe,OAAO,gBAAgB,KAAK,GAAG,cAAc;AAC5D,YAAQ,UAAU;AAAA,EACpB;AACA,QAAM,kBAAkB,WAAS,WAAS;AACxC,UAAM,WAAW,MAAM,MAAM;AAC7B,aAAS,OAAO,OAAO,CAAC;AACxB,gBAAY,OAAO,UAAU,gBAAgB;AAAA,MAC3C,QAAQ,MAAM,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AACA,QAAM,uBAAuB,WAAS;AACpC,QAAI,MAAM;AACR,kBAAY,OAAO,aAAa;AAAA,IAClC,OAAO;AACL,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAGA,QAAM,kBAAkB,WAAS;AAE/B,QAAI,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,GAAG;AAC/C;AAAA,IACF;AACA,QAAI,MAAM,OAAO,aAAa,IAAI,MAAM,IAAI;AAC1C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAGA,QAAM,cAAc,WAAS;AAE3B,QAAI,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,GAAG;AAC/C;AAAA,IACF;AACA,aAAS,QAAQ,MAAM;AACvB,QAAI,iBAAiB,WAAW,WAAW,SAAS,QAAQ,eAAe,SAAS,QAAQ,mBAAmB,GAAG;AAChH,eAAS,QAAQ,OAAO;AAAA,IAC1B;AACA,eAAW,UAAU;AAAA,EACvB;AACA,QAAM,uBAAuB,WAAS;AACpC,QAAI,CAAC,iBAAiB,eAAe,MAAM,CAAC,OAAO;AACjD,2BAAqB,KAAK;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,QAAQ,YAAY,WAAW,SAAS;AAC5C,UAAQ,UAAU,WAAW,MAAM,SAAS,IAAI,UAAU;AAC1D,MAAI,iBAAiB;AACrB,MAAI,SAAS;AAEX,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,OAAO;AACX,qBAAiB,gBAAgB,OAAO,CAAC,KAAK,QAAQ,UAAU;AAC9D,YAAM,QAAQ,QAAQ,MAAM;AAC5B,UAAI,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,EAAE,UAAU,OAAO;AACzD,YAAI,IAAI,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM;AAAA,MACzC,OAAO;AACL,YAAI,MAAuC;AACzC,cAAI,QAAQ,IAAI,KAAK,KAAK,CAAC,MAAM;AAC/B,oBAAQ,KAAK,qEAAqE,aAAa,gCAAgC,8EAA8E;AAC7M,mBAAO;AAAA,UACT;AACA,kBAAQ,IAAI,OAAO,IAAI;AAAA,QACzB;AACA,YAAI,KAAK;AAAA,UACP,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,SAAS,CAAC,MAAM;AAAA,QAClB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,MAAI,gBAAgB,SAAS;AAC3B,eAAW;AAAA,EACb;AACA,SAAO;AAAA,IACL,cAAc,CAAC,QAAQ,CAAC,OAAO;AAAA,MAC7B,GAAG;AAAA,MACH,WAAW,cAAc,KAAK;AAAA,MAC9B,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB,OAAO;AAAA,MACzB,IAAI,GAAG,EAAE;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,eAAe,OAAO;AAAA,MACpB;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA;AAAA;AAAA,MAGb,yBAAyB,YAAY,KAAK;AAAA,MAC1C,qBAAqB,eAAe,SAAS;AAAA,MAC7C,iBAAiB,mBAAmB,GAAG,EAAE,aAAa;AAAA,MACtD,iBAAiB;AAAA;AAAA;AAAA,MAGjB,cAAc;AAAA,MACd,KAAK;AAAA,MACL,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,eAAe,OAAO;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,wBAAwB,OAAO;AAAA,MAC7B,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa,CAAC;AAAA,MACZ;AAAA,IACF,OAAO;AAAA,MACL,KAAK;AAAA,MACL,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,GAAI,CAAC,YAAY;AAAA,QACf,UAAU,gBAAgB,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,IACA,iBAAiB,OAAO;AAAA,MACtB,MAAM;AAAA,MACN,IAAI,GAAG,EAAE;AAAA,MACT,mBAAmB,GAAG,EAAE;AAAA,MACxB,KAAK;AAAA,MACL,aAAa,WAAS;AAEpB,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC;AAAA,MACf;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,YAAY,WAAW,QAAQ,CAAC,KAAK,GAAG,KAAK,YAAU,UAAU,QAAQ,qBAAqB,QAAQ,MAAM,CAAC;AACnH,YAAM,WAAW,oBAAoB,kBAAkB,MAAM,IAAI;AACjE,aAAO;AAAA,QACL,MAAK,6CAAe,YAAW,eAAe,MAAM;AAAA,QACpD,UAAU;AAAA,QACV,MAAM;AAAA,QACN,IAAI,GAAG,EAAE,WAAW,KAAK;AAAA,QACzB,aAAa;AAAA,QACb,SAAS;AAAA,QACT,cAAc;AAAA,QACd,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,aAAa;AAAA,IACvB;AAAA,IACA,SAAS,WAAW,eAAe;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAO,0BAAQ;;;AC18BR,SAAS,4BAA4B,MAAM;AAChD,SAAO,qBAAqB,mBAAmB,IAAI;AACrD;AACA,IAAM,sBAAsB,uBAAuB,mBAAmB,CAAC,QAAQ,YAAY,aAAa,WAAW,gBAAgB,OAAO,gBAAgB,iBAAiB,gBAAgB,gBAAgB,aAAa,SAAS,gBAAgB,gBAAgB,kBAAkB,kBAAkB,sBAAsB,UAAU,uBAAuB,SAAS,WAAW,WAAW,aAAa,UAAU,cAAc,SAAS,CAAC;AAC1a,IAAO,8BAAQ;;;AFsBf,yBAA2C;AA1B3C,IAAI;AAAJ,IAAgB;AA2BhB,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,YAAY,WAAW,WAAW,aAAa,aAAa,gBAAgB,gBAAgB,gBAAgB,cAAc;AAAA,IACrJ,WAAW,CAAC,WAAW;AAAA,IACvB,OAAO,CAAC,SAAS,gBAAgB,cAAc;AAAA,IAC/C,KAAK,CAAC,OAAO,UAAU,mBAAW,IAAI,CAAC,EAAE;AAAA,IACzC,cAAc,CAAC,cAAc;AAAA,IAC7B,gBAAgB,CAAC,gBAAgB;AAAA,IACjC,gBAAgB,CAAC,kBAAkB,aAAa,oBAAoB;AAAA,IACpE,QAAQ,CAAC,UAAU,iBAAiB,qBAAqB;AAAA,IACzD,OAAO,CAAC,OAAO;AAAA,IACf,SAAS,CAAC,SAAS;AAAA,IACnB,SAAS,CAAC,SAAS;AAAA,IACnB,WAAW,CAAC,WAAW;AAAA,IACvB,QAAQ,CAAC,QAAQ;AAAA,IACjB,YAAY,CAAC,YAAY;AAAA,IACzB,SAAS,CAAC,SAAS;AAAA,EACrB;AACA,SAAO,eAAe,OAAO,6BAA6B,OAAO;AACnE;AACA,IAAM,mBAAmB,eAAO,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,4BAAoB,GAAG,EAAE,GAAG,OAAO;AAAA,IAC5C,GAAG;AAAA,MACD,CAAC,MAAM,4BAAoB,GAAG,EAAE,GAAG,OAAO,UAAU,mBAAW,IAAI,CAAC,EAAE;AAAA,IACxE,GAAG;AAAA,MACD,CAAC,MAAM,4BAAoB,SAAS,EAAE,GAAG,OAAO;AAAA,IAClD,GAAG;AAAA,MACD,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG,OAAO;AAAA,IAC9C,GAAG;AAAA,MACD,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG,gBAAgB,OAAO;AAAA,IAC9D,GAAG,OAAO,MAAM,aAAa,OAAO,WAAW,gBAAgB,OAAO,cAAc,gBAAgB,OAAO,YAAY;AAAA,EACzH;AACF,CAAC,EAAE;AAAA,EACD,CAAC,KAAK,4BAAoB,OAAO,KAAK,4BAAoB,cAAc,EAAE,GAAG;AAAA,IAC3E,YAAY;AAAA,EACd;AAAA;AAAA,EAEA,0BAA0B;AAAA,IACxB,CAAC,YAAY,4BAAoB,cAAc,EAAE,GAAG;AAAA,MAClD,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,CAAC,MAAM,4BAAoB,GAAG,EAAE,GAAG;AAAA,IACjC,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA,EACA,CAAC,MAAM,4BAAoB,SAAS,EAAE,GAAG;AAAA,IACvC,CAAC,IAAI,4BAAoB,YAAY,OAAO,4BAAoB,YAAY,GAAG,GAAG;AAAA,MAChF,cAAc,KAAK;AAAA,IACrB;AAAA,IACA,CAAC,IAAI,4BAAoB,YAAY,IAAI,4BAAoB,YAAY,GAAG,GAAG;AAAA,MAC7E,cAAc,KAAK;AAAA,IACrB;AAAA,IACA,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,MACnC,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,CAAC,MAAM,qBAAa,IAAI,EAAE,GAAG;AAAA,IAC3B,eAAe;AAAA,IACf,qBAAqB;AAAA,MACnB,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,MAAM,qBAAa,IAAI,IAAI,yBAAiB,SAAS,EAAE,GAAG;AAAA,IACzD,CAAC,MAAM,qBAAa,KAAK,EAAE,GAAG;AAAA,MAC5B,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,MAAM,6BAAqB,IAAI,EAAE,GAAG;AAAA,IACnC,SAAS;AAAA,IACT,CAAC,IAAI,4BAAoB,YAAY,OAAO,4BAAoB,YAAY,GAAG,GAAG;AAAA,MAChF,cAAc,KAAK,IAAI;AAAA,IACzB;AAAA,IACA,CAAC,IAAI,4BAAoB,YAAY,IAAI,4BAAoB,YAAY,GAAG,GAAG;AAAA,MAC7E,cAAc,KAAK,IAAI;AAAA,IACzB;AAAA,IACA,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,MACnC,SAAS;AAAA,IACX;AAAA,IACA,CAAC,MAAM,4BAAoB,YAAY,EAAE,GAAG;AAAA,MAC1C,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,CAAC,MAAM,6BAAqB,IAAI,IAAI,yBAAiB,SAAS,EAAE,GAAG;AAAA;AAAA;AAAA,IAGjE,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,aAAa;AAAA,IACb,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,MACnC,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,MAAM,2BAAmB,IAAI,EAAE,GAAG;AAAA,IACjC,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,CAAC,IAAI,4BAAoB,YAAY,OAAO,4BAAoB,YAAY,GAAG,GAAG;AAAA,MAChF,cAAc,KAAK,IAAI;AAAA,IACzB;AAAA,IACA,CAAC,IAAI,4BAAoB,YAAY,IAAI,4BAAoB,YAAY,GAAG,GAAG;AAAA,MAC7E,cAAc,KAAK,IAAI;AAAA,IACzB;AAAA,IACA,CAAC,MAAM,2BAAmB,KAAK,EAAE,GAAG;AAAA,MAClC,SAAS;AAAA,IACX;AAAA,IACA,CAAC,MAAM,4BAAoB,YAAY,EAAE,GAAG;AAAA,MAC1C,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,CAAC,MAAM,2BAAmB,IAAI,IAAI,yBAAiB,SAAS,EAAE,GAAG;AAAA,IAC/D,eAAe;AAAA,IACf,CAAC,MAAM,2BAAmB,KAAK,EAAE,GAAG;AAAA,MAClC,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,CAAC,MAAM,yBAAiB,WAAW,EAAE,GAAG;AAAA,IACtC,YAAY;AAAA,EACd;AAAA,EACA,CAAC,MAAM,2BAAmB,IAAI,IAAI,yBAAiB,WAAW,EAAE,GAAG;AAAA,IACjE,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,MACnC,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,MAAM,2BAAmB,IAAI,IAAI,yBAAiB,WAAW,IAAI,yBAAiB,SAAS,EAAE,GAAG;AAAA,IAC/F,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,MACnC,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,IACnC,UAAU;AAAA,IACV,cAAc;AAAA,IACd,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,WAAW;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,CAAC,MAAM,4BAAoB,GAAG,EAAE,GAAG;AAAA,QACjC,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,QACnC,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,CAAC,MAAM,4BAAoB,SAAS,EAAE,GAAG;AAAA,QACvC,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,2BAA2B,eAAO,OAAO;AAAA,EAC7C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA;AAAA,EAED,UAAU;AAAA,EACV,OAAO;AAAA,EACP,KAAK;AAAA,EACL,WAAW;AACb,CAAC;AACD,IAAM,6BAA6B,eAAO,oBAAY;AAAA,EACpD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,aAAa;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AACd,CAAC;AACD,IAAM,6BAA6B,eAAO,oBAAY;AAAA,EACpD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,gBAAgB,WAAW,aAAa,OAAO,kBAAkB;AAAA,EAClF;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,aAAa;AAAA,EACb,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,WAAW;AAAA,IACb;AAAA,IACA,OAAO;AAAA,MACL,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,qBAAqB,eAAO,gBAAQ;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,4BAAoB,MAAM,EAAE,GAAG,OAAO;AAAA,IAC/C,GAAG,OAAO,QAAQ,WAAW,iBAAiB,OAAO,mBAAmB;AAAA,EAC1E;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,EACrC,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,oBAAoB,eAAO,eAAO;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,GAAG,MAAM,WAAW;AAAA,EACpB,UAAU;AACZ,EAAE,CAAC;AACH,IAAM,sBAAsB,eAAO,OAAO;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC1C,SAAS;AACX,EAAE,CAAC;AACH,IAAM,wBAAwB,eAAO,OAAO;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC1C,SAAS;AACX,EAAE,CAAC;AACH,IAAM,sBAAsB,eAAO,MAAM;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,CAAC,MAAM,4BAAoB,MAAM,EAAE,GAAG;AAAA,IACpC,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,yBAAyB;AAAA,IACzB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,cAAc;AAAA,IACd,CAAC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG;AAAA,MAC5B,WAAW;AAAA,IACb;AAAA,IACA,CAAC,KAAK,4BAAoB,OAAO,EAAE,GAAG;AAAA,MACpC,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA;AAAA,MAEtD,wBAAwB;AAAA,QACtB,iBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB,UAAU,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,MAC9C,eAAe;AAAA,IACjB;AAAA,IACA,CAAC,KAAK,4BAAoB,YAAY,EAAE,GAAG;AAAA,MACzC,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IACxD;AAAA,IACA,2BAA2B;AAAA,MACzB,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,eAAe;AAAA,MACvM,CAAC,KAAK,4BAAoB,OAAO,EAAE,GAAG;AAAA,QACpC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,WAAW,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,OAAO,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,kBAAkB,MAAM,QAAQ,OAAO,YAAY;AAAA;AAAA,QAE7R,wBAAwB;AAAA,UACtB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,QACxD;AAAA,MACF;AAAA,MACA,CAAC,KAAK,4BAAoB,YAAY,EAAE,GAAG;AAAA,QACzC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,WAAW,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,OAAO,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,kBAAkB,MAAM,QAAQ,OAAO,YAAY;AAAA,MAC/R;AAAA,IACF;AAAA,EACF;AACF,EAAE,CAAC;AACH,IAAM,yBAAyB,eAAO,uBAAe;AAAA,EACnD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EAC1D,KAAK;AACP,EAAE,CAAC;AACH,IAAM,sBAAsB,eAAO,MAAM;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,CAAC,MAAM,4BAAoB,MAAM,EAAE,GAAG;AAAA,IACpC,aAAa;AAAA,EACf;AACF,CAAC;AAED,IAAM,eAAkC,kBAAW,SAASC,cAAa,SAAS,KAAK;AACrF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AAGD,QAAM;AAAA,IACJ,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,eAAe;AAAA,IACf,WAAW;AAAA,IACX;AAAA,IACA,YAAY,eAAe,iBAA0B,mBAAAC,KAAK,eAAW;AAAA,MACnE,UAAU;AAAA,IACZ,CAAC;AAAA,IACD,cAAc,CAAC,MAAM;AAAA,IACrB,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ;AAAA,IACA,eAAe,MAAM,WAAW,CAAC,IAAI;AAAA,IACrC,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,WAAW;AAAA,IACX,yBAAyB;AAAA,IACzB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB;AAAA,IACA,wBAAwB;AAAA,IACxB,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,mBAAmB,UAAQ,IAAI,IAAI;AAAA,IACnC;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA,oBAAoB,CAAC,MAAM;AAAA,IAC3B,IAAI;AAAA,IACJ,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,UAAU;AAAA,IACV,cAAc;AAAA,IACd,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,WAAW;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,YAAY,uBAAuB,yBAAkC,mBAAAA,KAAK,uBAAmB,CAAC,CAAC;AAAA,IAC/F,WAAW;AAAA,IACX,aAAa;AAAA,IACb;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,gBAAgB,CAAC,MAAM;AAAA,IACvB,OAAO;AAAA,IACP,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,OAAO;AAAA,IACP,GAAG;AAAA,EACL,IAAI;AAGJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,wBAAgB;AAAA,IAClB,GAAG;AAAA,IACH,eAAe;AAAA,EACjB,CAAC;AACD,QAAM,eAAe,CAAC,oBAAoB,CAAC,YAAY,SAAS,CAAC;AACjE,QAAM,gBAAgB,CAAC,YAAY,mBAAmB,SAAS,mBAAmB;AAClF,QAAM;AAAA,IACJ,aAAa;AAAA,EACf,IAAI,cAAc;AAClB,QAAM;AAAA,IACJ,KAAK;AAAA,IACL,GAAG;AAAA,EACL,IAAI,gBAAgB;AACpB,QAAM,wBAAwB,YAAU,OAAO,SAAS;AACxD,QAAM,iBAAiB,sBAAsB;AAG7C,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,eAAe;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,yBAAyB;AAAA,IAC7B,OAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,GAAG;AAAA,IACL;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,MACT,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,CAAC,aAAa,YAAY,IAAI,QAAQ,WAAW;AAAA,IACrD,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB,iBAAiB;AAAA,IACjB,KAAK;AAAA,EACP,CAAC;AACD,QAAM,CAAC,WAAW,UAAU,IAAI,QAAQ,SAAS;AAAA,IAC/C,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,WAAW,QAAQ;AAAA,EACrB,CAAC;AACD,QAAM,CAAC,YAAY,WAAW,IAAI,QAAQ,UAAU;AAAA,IAClD,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB,iBAAiB;AAAA,MACf;AAAA,MACA,OAAO;AAAA,QACL,OAAO,WAAW,SAAS,cAAc;AAAA,MAC3C;AAAA,MACA,MAAM;AAAA,MACN;AAAA,MACA,MAAM;AAAA,IACR;AAAA,EACF,CAAC;AACD,MAAI;AACJ,MAAI,YAAY,MAAM,SAAS,GAAG;AAChC,UAAM,wBAAwB,aAAW;AAAA,MACvC,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA,GAAG,YAAY,MAAM;AAAA,IACvB;AACA,QAAI,YAAY;AACd,uBAAiB,WAAW,OAAO,uBAAuB,UAAU;AAAA,IACtE,OAAO;AACL,uBAAiB,MAAM,IAAI,CAAC,QAAQ,UAAU;AAC5C,cAAM;AAAA,UACJ;AAAA,UACA,GAAG;AAAA,QACL,IAAI,sBAAsB;AAAA,UACxB;AAAA,QACF,CAAC;AACD,mBAAoB,mBAAAA,KAAK,cAAM;AAAA,UAC7B,OAAO,eAAe,MAAM;AAAA,UAC5B;AAAA,UACA,GAAG;AAAA,UACH,GAAG,uBAAuB,UAAU;AAAA,QACtC,GAAG,GAAG;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACA,MAAI,YAAY,MAAM,MAAM,QAAQ,cAAc,GAAG;AACnD,UAAM,OAAO,eAAe,SAAS;AACrC,QAAI,CAAC,WAAW,OAAO,GAAG;AACxB,uBAAiB,eAAe,OAAO,GAAG,SAAS;AACnD,qBAAe,SAAkB,mBAAAA,KAAK,QAAQ;AAAA,QAC5C,WAAW,QAAQ;AAAA,QACnB,UAAU,iBAAiB,IAAI;AAAA,MACjC,GAAG,eAAe,MAAM,CAAC;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,qBAAqB,gBAAuB,mBAAAC,MAAM,MAAM;AAAA,IAC5D,UAAU,KAAc,mBAAAD,KAAK,wBAAwB;AAAA,MACnD,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA,WAAW;AAAA,MACX,UAAU,OAAO;AAAA,IACnB,CAAC,OAAgB,mBAAAA,KAAK,qBAAqB;AAAA,MACzC,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA,UAAU,OAAO;AAAA,IACnB,CAAC,CAAC;AAAA,EACJ,GAAG,OAAO,GAAG;AACb,QAAM,cAAc,mBAAmB;AACvC,QAAM,sBAAsB,CAAC,QAAQ,WAAW;AAE9C,UAAM;AAAA,MACJ;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,eAAoB,mBAAAA,KAAK,MAAM;AAAA,MAC7B,GAAG;AAAA,MACH,UAAU,eAAe,MAAM;AAAA,IACjC,GAAG,GAAG;AAAA,EACR;AACA,QAAM,eAAe,oBAAoB;AACzC,QAAM,mBAAmB,CAAC,QAAQ,UAAU;AAC1C,UAAM,cAAc,eAAe;AAAA,MACjC;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO,aAAa;AAAA,MAClB,GAAG;AAAA,MACH,WAAW,QAAQ;AAAA,IACrB,GAAG,QAAQ;AAAA,MACT,UAAU,YAAY,eAAe;AAAA,MACrC;AAAA,MACA;AAAA,IACF,GAAG,UAAU;AAAA,EACf;AACA,QAAM,0BAA0B,uBAAuB,UAAU;AACjE,QAAM,0BAA0B,uBAAuB,UAAU;AACjE,aAAoB,mBAAAC,MAAY,iBAAU;AAAA,IACxC,UAAU,KAAc,mBAAAD,KAAK,kBAAkB;AAAA,MAC7C;AAAA,MACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC;AAAA,MACA,GAAG,aAAa,KAAK;AAAA,MACrB,UAAU,YAAY;AAAA,QACpB;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX,MAAM,SAAS,UAAU,UAAU;AAAA,QACnC,iBAAiB,mBAAmB;AAAA,QACpC,YAAY;AAAA,UACV,KAAK;AAAA,UACL,WAAW,QAAQ;AAAA,UACnB;AAAA,UACA,aAAa,WAAS;AACpB,gBAAI,MAAM,WAAW,MAAM,eAAe;AACxC,mCAAqB,KAAK;AAAA,YAC5B;AAAA,UACF;AAAA,UACA,IAAK,gBAAgB,iBAAiB;AAAA,YACpC,kBAA2B,mBAAAC,MAAM,0BAA0B;AAAA,cACzD,WAAW,QAAQ;AAAA,cACnB;AAAA,cACA,UAAU,CAAC,mBAA4B,mBAAAD,KAAK,4BAA4B;AAAA,gBACtE,GAAG,cAAc;AAAA,gBACjB,cAAc;AAAA,gBACd,OAAO;AAAA,gBACP;AAAA,gBACA,GAAG;AAAA,gBACH,WAAW,aAAK,QAAQ,gBAAgB,mEAAyB,SAAS;AAAA,gBAC1E,UAAU;AAAA,cACZ,CAAC,IAAI,MAAM,mBAA4B,mBAAAA,KAAK,4BAA4B;AAAA,gBACtE,GAAG,uBAAuB;AAAA,gBAC1B;AAAA,gBACA,cAAc,YAAY,YAAY;AAAA,gBACtC,OAAO,YAAY,YAAY;AAAA,gBAC/B;AAAA,gBACA,GAAG;AAAA,gBACH,WAAW,aAAK,QAAQ,gBAAgB,mEAAyB,SAAS;AAAA,gBAC1E,UAAU;AAAA,cACZ,CAAC,IAAI,IAAI;AAAA,YACX,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA,YAAY;AAAA,UACV,WAAW,QAAQ;AAAA,UACnB;AAAA,UACA;AAAA,UACA,GAAG,cAAc;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH,CAAC,GAAG,eAAwB,mBAAAA,KAAK,oBAAoB;AAAA,MACnD,IAAI;AAAA,MACJ,GAAG;AAAA,MACH,cAAuB,mBAAAC,MAAM,mBAAmB;AAAA,QAC9C,IAAI;AAAA,QACJ,GAAG;AAAA,QACH,UAAU,CAAC,WAAW,eAAe,WAAW,QAAiB,mBAAAD,KAAK,qBAAqB;AAAA,UACzF,WAAW,QAAQ;AAAA,UACnB;AAAA,UACA,UAAU;AAAA,QACZ,CAAC,IAAI,MAAM,eAAe,WAAW,KAAK,CAAC,YAAY,CAAC,cAAuB,mBAAAA,KAAK,uBAAuB;AAAA,UACzG,WAAW,QAAQ;AAAA,UACnB;AAAA,UACA,MAAM;AAAA,UACN,aAAa,WAAS;AAEpB,kBAAM,eAAe;AAAA,UACvB;AAAA,UACA,UAAU;AAAA,QACZ,CAAC,IAAI,MAAM,eAAe,SAAS,QAAiB,mBAAAA,KAAK,aAAa;AAAA,UACpE,IAAI;AAAA,UACJ,GAAG;AAAA,UACH,UAAU,eAAe,IAAI,CAAC,QAAQ,UAAU;AAC9C,gBAAI,SAAS;AACX,qBAAO,YAAY;AAAA,gBACjB,KAAK,OAAO;AAAA,gBACZ,OAAO,OAAO;AAAA,gBACd,UAAU,OAAO,QAAQ,IAAI,CAAC,SAAS,WAAW,iBAAiB,SAAS,OAAO,QAAQ,MAAM,CAAC;AAAA,cACpG,CAAC;AAAA,YACH;AACA,mBAAO,iBAAiB,QAAQ,KAAK;AAAA,UACvC,CAAC;AAAA,QACH,CAAC,IAAI,IAAI;AAAA,MACX,CAAC;AAAA,IACH,CAAC,IAAI,IAAI;AAAA,EACX,CAAC;AACH,CAAC;AACD,OAAwC,aAAa,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtF,cAAc,kBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUzB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtB,cAAc,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,SAAS,OAAO,CAAC,GAAG,kBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvF,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,iBAAiB,kBAAAA,QAAU,MAAM;AAAA,IAC/B,gBAAgB,kBAAAA,QAAU;AAAA,IAC1B,OAAO,kBAAAA,QAAU;AAAA,IACjB,QAAQ,kBAAAA,QAAU;AAAA,IAClB,gBAAgB,kBAAAA,QAAU;AAAA,EAC5B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,eAAe,kBAAAA,QAAU,KAAK,WAAS;AACnD,QAAI,MAAM,YAAY,MAAM,iBAAiB,UAAa,CAAC,MAAM,QAAQ,MAAM,YAAY,GAAG;AAC5F,aAAO,IAAI,MAAM,CAAC,6GAA6G,YAAY,MAAM,YAAY,gBAAgB,EAAE,KAAK,IAAI,CAAC;AAAA,IAC3L;AACA,WAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,sBAAsB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,wBAAwB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,iBAAiB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,uBAAuB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,gBAAgB,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/E,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrB,kBAAkB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO5B,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWxB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,oBAAoB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtB,sBAAsB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,kBAAkB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIzB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,iBAAiB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,aAAa,kBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU5B,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASxB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,MAAM,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,SAAS,QAAQ,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxH,WAAW,kBAAAA,QAAgD,MAAM;AAAA,IAC/D,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,gBAAgB,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IACtE,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC/D,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC9D,gBAAgB,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EACxE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,SAAS,kBAAAA,QAAU;AAAA,IACnB,OAAO,kBAAAA,QAAU;AAAA,IACjB,QAAQ,kBAAAA,QAAU;AAAA,EACpB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtJ,OAAO,eAAe,kBAAAA,QAAU,KAAK,WAAS;AAC5C,QAAI,MAAM,YAAY,MAAM,UAAU,UAAa,CAAC,MAAM,QAAQ,MAAM,KAAK,GAAG;AAC9E,aAAO,IAAI,MAAM,CAAC,sGAAsG,YAAY,MAAM,KAAK,gBAAgB,EAAE,KAAK,IAAI,CAAC;AAAA,IAC7K;AACA,WAAO;AAAA,EACT,CAAC;AACH,IAAI;AACJ,IAAO,uBAAQ;", "names": ["React", "value", "Autocomplete", "_jsx", "_jsxs", "PropTypes"]}