export type IUserTableFilters = {
  userName: string;
  groupName: string | number;
  locationId?: string | number;
};

export type IUserItem = {
  id: string;
  group_name: string;
  location: { id: number; locationName: string };
  user_name: string;
};

export type IUserTableFilterValue = string | string[];

export interface UserAccessDetail {
  locationId: number;
  loginId: string;
  configGroupId: number;
}

export type GetAllPermissionsParams = {
  page: number;
  rowsPerPage: number;
  filters: IUserTableFilters;
};
export type GetAllPermissionsResponse = {
  records: AccessControlRecord[];
  total: number;
  pageTotal: number;
};

export interface AccessControlConfigResponse {
  id: number;
  groupName: string;
  manageLocation: boolean;
  manageCapability: boolean;
}
export interface Location {
  id: number;
  locationName: string;
}

export interface AccessControlRecord {
  id: number;
  locationId: number;
  loginId: string;
  configGroupId: number;
  manageLocation: boolean;
  manageCapability: boolean;
  accessControlConfig: AccessControlConfigResponse;
  location: Location;
}

// Get all permissions Response
export interface LocalPermissionsResponse {
  pageTotal: number;
  total: number;
  records: AccessControlRecord[];
}

// Add User Permission Response
export interface AddUserPermissionResponse {
  message: string;
  data: any;
}

// Delete User Permission Response
export interface DeleteUserPermissionResponse {
  message: string;
  data: any;
}

export interface PeopleProfileResponse extends DeleteUserPermissionResponse {}
