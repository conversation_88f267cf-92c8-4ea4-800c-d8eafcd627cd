import { ReplaceUrlVariable } from '@/routes/paths';
import * as http from '@/shared/services';
import { API_PATHS } from '../constants';
import { GraphUsersResponse, UserPermissionsResponse } from '../models';
import {
  AccessControlConfigResponse,
  AddUserPermissionResponse,
  DeleteUserPermissionResponse,
  GetAllPermissionsParams,
  GetAllPermissionsResponse,
  PeopleProfileResponse,
} from '../models/user-access.model';

export const searchGraphUsers = (searchText: string, orderBy: string): Promise<GraphUsersResponse | null> => {
  return http.get<GraphUsersResponse>(API_PATHS.ACTIVE_DIRECTORY.SEARCH_GRAPH_USER, {
    params: { searchText, orderBy, count: true },
  });
};

export const getUserDetails = (userId: string): Promise<GraphUsersResponse | null> => {
  return http.get<GraphUsersResponse>(API_PATHS.ACTIVE_DIRECTORY.GET_USER_DETAILS, {
    params: { userId },
  });
};

export const getUserPermissions = (): Promise<UserPermissionsResponse[]> => {
  return http.get<UserPermissionsResponse[]>(API_PATHS.APPLICATION_CONFIGURATION.USER_PERMISSIONS);
};

export const getAllGroupDetails = (): Promise<AccessControlConfigResponse[]> => {
  return http.get<AccessControlConfigResponse[]>(API_PATHS.USER_ACCESS.GET_USER_GROUP_DETAILS);
};

export const getAllPermissions = ({
  page,
  rowsPerPage,
  filters,
}: GetAllPermissionsParams): Promise<GetAllPermissionsResponse> => {
  const params = {
    page,
    limit: rowsPerPage,
    loginid: filters.userName || undefined,
    configGroupId: filters.groupName || undefined,
    locationId: filters.locationId || undefined,
  };

  return http.get<GetAllPermissionsResponse>(API_PATHS.USER_ACCESS.GET_ALL_PERMISSIONS, { params });
};

export const addPermission = ({
  locationId,
  configGroupId,
  loginId,
}: {
  locationId: number;
  configGroupId: number;
  loginId: string;
}): Promise<AddUserPermissionResponse> => {
  return http.post<AddUserPermissionResponse>(API_PATHS.USER_ACCESS.ADD_USER_PERMISSION, {
    locationId,
    configGroupId,
    loginId,
  });
};

export const deletePermission = (userId: number): Promise<DeleteUserPermissionResponse> => {
  return http.remove<DeleteUserPermissionResponse>(
    ReplaceUrlVariable(API_PATHS.USER_ACCESS.DELETE_USER_PERMISSION, { id: userId }),
  );
};

export const getPeopleProfile = (loginId: string): Promise<PeopleProfileResponse> => {
  return http.get<PeopleProfileResponse>(
    ReplaceUrlVariable(API_PATHS.PEOPLE_PROFILE.GET_PEOPLE_PROFILE_LIST, { loginId }),
  );
};
