"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BingoCardModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("./repositories");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const repositories_2 = require("../capability/repositories");
const clients_1 = require("../shared/clients");
const services_2 = require("../shared/services");
const repositories_3 = require("../permission/repositories");
const repositories_4 = require("../location/repositories");
const repositories = [
    repositories_1.BingoCardConfigRepository,
    repositories_2.CapabilityRepository,
    repositories_2.CapabilityLegRepository,
    repositories_4.LocationRepository,
];
let BingoCardModule = class BingoCardModule {
};
BingoCardModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.BingoCardController],
        providers: [
            ...repositories,
            services_1.BingoCardService,
            clients_1.AdminApiClient,
            clients_1.HistoryApiClient,
            services_2.SharedPermissionService,
            repositories_3.UserPermissionRepository,
        ],
    })
], BingoCardModule);
exports.BingoCardModule = BingoCardModule;
//# sourceMappingURL=bingo-card.module.js.map