import { ContactDetailService } from '../services';
import { CONTACT_DETAIL_OBJECT_TYPE, PILLAR } from 'src/shared/enums';
import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { ContactListingResponseDto, DeleteHierarchyUserRequestDto, NewHierarchyUserRequestDto, PatchContactTitleRequestDto } from '../dtos';
export declare class ContactDetailController {
    private readonly contactDetailService;
    constructor(contactDetailService: ContactDetailService);
    getContactUserList(objectType: CONTACT_DETAIL_OBJECT_TYPE, objectId: number | PILLAR, entityId: number): Promise<ContactListingResponseDto[]>;
    addHierarchyContact(request: RequestContext, newHierarchyUserRequestDto: NewHierarchyUserRequestDto): Promise<MessageResponseDto>;
    deleteUserFromContact(request: RequestContext, deleteHierarchyUserRequestDto: DeleteHierarchyUserRequestDto): Promise<MessageResponseDto>;
    patchContactTitle(request: RequestContext, patchContactTitleRequestDto: PatchContactTitleRequestDto): Promise<MessageResponseDto>;
}
