import "./chunk-NYQWP5OZ.js";
import {
  LoadingButton_default,
  getLoadingButtonUtilityClass,
  loadingButtonClasses_default,
  useAutocomplete
} from "./chunk-O252W7OZ.js";
import {
  AlertTitle_default,
  Pagination_default,
  SpeedDialAction_default,
  SpeedDialIcon_default,
  SpeedDial_default,
  ToggleButtonGroup_default,
  usePagination
} from "./chunk-4IVWLPB4.js";
import {
  Tabs_default
} from "./chunk-77DHAT5L.js";
import "./chunk-KODONAEI.js";
import "./chunk-T3N3MTUI.js";
import {
  ToggleButton_default
} from "./chunk-EHDFWZGA.js";
import {
  Rating_default
} from "./chunk-APSGGKZN.js";
import {
  Skeleton_default
} from "./chunk-B3ATM26W.js";
import {
  PaginationItem_default
} from "./chunk-3SQWOWWM.js";
import "./chunk-6AKAVX6D.js";
import "./chunk-Y4PULBJI.js";
import "./chunk-BHAYCTWK.js";
import {
  AvatarGroup_default
} from "./chunk-SAYPEWML.js";
import {
  Typography_default
} from "./chunk-5FONVV63.js";
import {
  useThemeProps
} from "./chunk-FZKRYO5W.js";
import "./chunk-YC4BDAXA.js";
import "./chunk-HOU7SJTN.js";
import "./chunk-I6IMDGDA.js";
import "./chunk-ZOKU5DI5.js";
import {
  Autocomplete_default
} from "./chunk-IPEKBVDO.js";
import "./chunk-HHOTFTPC.js";
import "./chunk-AGCLCLMH.js";
import "./chunk-TGJPMUYK.js";
import "./chunk-4FUVGFH6.js";
import "./chunk-OJAHRWBG.js";
import "./chunk-3FBLO4ZC.js";
import "./chunk-3HPN5KNE.js";
import "./chunk-PR6ZCO7G.js";
import "./chunk-AKKANAVW.js";
import "./chunk-QDFPR22U.js";
import "./chunk-TC7WEYMJ.js";
import "./chunk-HKUARNEK.js";
import "./chunk-ZPQ4Y73R.js";
import {
  Alert_default
} from "./chunk-E2QGP3FH.js";
import "./chunk-6PII2FL3.js";
import "./chunk-ARCWVTKP.js";
import "./chunk-QGA6CCGQ.js";
import "./chunk-M2PJU2RP.js";
import "./chunk-AACZXOME.js";
import "./chunk-RU4OS4O3.js";
import "./chunk-GEWEBP33.js";
import "./chunk-FAMVC6XK.js";
import "./chunk-HZOIS4LS.js";
import "./chunk-A3Q7B7W4.js";
import "./chunk-4FTWOKSW.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-BFL632LT.js";
import "./chunk-ZKV3TKMI.js";
import "./chunk-OLGFUZVB.js";
import "./chunk-MCDEKIA4.js";
import "./chunk-UGHI33QN.js";
import "./chunk-XCKL3XTV.js";
import "./chunk-LCP2UDSO.js";
import {
  isMuiElement_default
} from "./chunk-SLNW3ETP.js";
import "./chunk-IHGJNEEY.js";
import "./chunk-BTIOHRX6.js";
import "./chunk-HN6H4SXL.js";
import "./chunk-P4UD7LEE.js";
import "./chunk-U27VJAZE.js";
import {
  capitalize_default
} from "./chunk-YXVLDLGC.js";
import "./chunk-5DQNP7OD.js";
import "./chunk-7SK3IGWN.js";
import "./chunk-WEUG6UQ7.js";
import "./chunk-E5UMWPXU.js";
import {
  styled_default
} from "./chunk-HGZMMVE4.js";
import {
  composeClasses,
  useForkRef
} from "./chunk-OLICLHZN.js";
import "./chunk-UUHLHOPM.js";
import {
  useEnhancedEffect_default
} from "./chunk-KU2YNSU4.js";
import {
  clsx_default,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-UE7CETWW.js";
import {
  createUnarySpacing,
  deepmerge,
  getValue,
  handleBreakpoints,
  resolveBreakpointValues
} from "./chunk-NUO2DALJ.js";
import {
  require_prop_types
} from "./chunk-J4LPPHPF.js";
import "./chunk-OPLPMYTC.js";
import "./chunk-X53PWDJZ.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import "./chunk-GS3CDLZ6.js";
import "./chunk-4JLRNKH6.js";
import {
  require_react_dom
} from "./chunk-SRNDHWC2.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/lab/Alert/Alert.js
var React = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var warnedOnce = false;
var Alert_default2 = React.forwardRef(function DeprecatedAlert(props, ref) {
  if (!warnedOnce) {
    console.warn(["MUI: The Alert component was moved from the lab to the core.", "", "You should use `import { Alert } from '@mui/material'`", "or `import Alert from '@mui/material/Alert'`"].join("\n"));
    warnedOnce = true;
  }
  return (0, import_jsx_runtime.jsx)(Alert_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/AlertTitle/AlertTitle.js
var React2 = __toESM(require_react());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var warnedOnce2 = false;
var AlertTitle_default2 = React2.forwardRef(function DeprecatedAlertTitle(props, ref) {
  if (!warnedOnce2) {
    console.warn(["MUI: The AlertTitle component was moved from the lab to the core.", "", "You should use `import { AlertTitle } from '@mui/material'`", "or `import AlertTitle from '@mui/material/AlertTitle'`"].join("\n"));
    warnedOnce2 = true;
  }
  return (0, import_jsx_runtime2.jsx)(AlertTitle_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/Autocomplete/Autocomplete.js
var React3 = __toESM(require_react());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var warnedOnce3 = false;
var Autocomplete_default2 = React3.forwardRef(function DeprecatedAutocomplete(props, ref) {
  if (!warnedOnce3) {
    console.warn(["MUI: The Autocomplete component was moved from the lab to the core.", "", "You should use `import { Autocomplete } from '@mui/material'`", "or `import Autocomplete from '@mui/material/Autocomplete'`"].join("\n"));
    warnedOnce3 = true;
  }
  return (0, import_jsx_runtime3.jsx)(Autocomplete_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/AvatarGroup/AvatarGroup.js
var React4 = __toESM(require_react());
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var warnedOnce4 = false;
var AvatarGroup_default2 = React4.forwardRef(function DeprecatedAvatarGroup(props, ref) {
  if (!warnedOnce4) {
    console.warn(["MUI: The AvatarGroup component was moved from the lab to the core.", "", "You should use `import { AvatarGroup } from '@mui/material'`", "or `import AvatarGroup from '@mui/material/AvatarGroup'`"].join("\n"));
    warnedOnce4 = true;
  }
  return (0, import_jsx_runtime4.jsx)(AvatarGroup_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/CalendarPicker/CalendarPicker.js
var React5 = __toESM(require_react());
var warnedOnce5 = false;
var warn = () => {
  if (!warnedOnce5) {
    console.warn(["MUI: The CalendarPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { CalendarPicker } from '@mui/x-date-pickers'`", "or `import { CalendarPicker } from '@mui/x-date-pickers/CalendarPicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce5 = true;
  }
};
var CalendarPicker = React5.forwardRef(function DeprecatedCalendarPicker() {
  warn();
  return null;
});
var CalendarPicker_default = CalendarPicker;
var calendarPickerClasses = {};

// node_modules/@mui/lab/ClockPicker/ClockPicker.js
var React6 = __toESM(require_react());
var warnedOnce6 = false;
var warn2 = () => {
  if (!warnedOnce6) {
    console.warn(["MUI: The ClockPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { ClockPicker } from '@mui/x-date-pickers'`", "or `import { ClockPicker } from '@mui/x-date-pickers/ClockPicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce6 = true;
  }
};
var ClockPicker = React6.forwardRef(function DeprecatedClockPicker() {
  warn2();
  return null;
});
var ClockPicker_default = ClockPicker;
var clockPickerClasses = {};

// node_modules/@mui/lab/DatePicker/DatePicker.js
var React7 = __toESM(require_react());
var warnedOnce7 = false;
var warn3 = () => {
  if (!warnedOnce7) {
    console.warn(["MUI: The DatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { DatePicker } from '@mui/x-date-pickers'`", "or `import { DatePicker } from '@mui/x-date-pickers/DatePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce7 = true;
  }
};
var DatePicker = React7.forwardRef(function DeprecatedDatePicker() {
  warn3();
  return null;
});
var DatePicker_default = DatePicker;

// node_modules/@mui/lab/DateRangePicker/DateRangePicker.js
var React8 = __toESM(require_react());
var warnedOnce8 = false;
var warn4 = () => {
  if (!warnedOnce8) {
    console.warn(["MUI: The DateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`", "", "You should use `import { DateRangePicker } from '@mui/x-date-pickers-pro'`", "or `import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce8 = true;
  }
};
var DateRangePicker = React8.forwardRef(function DeprecatedDateRangePicker() {
  warn4();
  return null;
});
var DateRangePicker_default = DateRangePicker;

// node_modules/@mui/lab/DateRangePickerDay/DateRangePickerDay.js
var React9 = __toESM(require_react());
var warnedOnce9 = false;
var warn5 = () => {
  if (!warnedOnce9) {
    console.warn(["MUI: The DateRangePickerDay component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`", "", "You should use `import { DateRangePickerDay } from '@mui/x-date-pickers-pro'`", "or `import { DateRangePickerDay } from '@mui/x-date-pickers-pro/DateRangePickerDay'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce9 = true;
  }
};
var DateRangePickerDay = React9.forwardRef(function DeprecatedDateRangePickerDay() {
  warn5();
  return null;
});
var DateRangePickerDay_default = DateRangePickerDay;
var getDateRangePickerDayUtilityClass = (slot) => {
  warn5();
  return "";
};

// node_modules/@mui/lab/DateTimePicker/DateTimePicker.js
var React10 = __toESM(require_react());
var warnedOnce10 = false;
var warn6 = () => {
  if (!warnedOnce10) {
    console.warn(["MUI: The DateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { DateTimePicker } from '@mui/x-date-pickers'`", "or `import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce10 = true;
  }
};
var DateTimePicker = React10.forwardRef(function DeprecatedDateTimePicker() {
  warn6();
  return null;
});
var DateTimePicker_default = DateTimePicker;

// node_modules/@mui/lab/DesktopDatePicker/DesktopDatePicker.js
var React11 = __toESM(require_react());
var warnedOnce11 = false;
var warn7 = () => {
  if (!warnedOnce11) {
    console.warn(["MUI: The DesktopDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { DesktopDatePicker } from '@mui/x-date-pickers'`", "or `import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce11 = true;
  }
};
var DesktopDatePicker = React11.forwardRef(function DeprecatedDesktopDatePicker() {
  warn7();
  return null;
});
var DesktopDatePicker_default = DesktopDatePicker;

// node_modules/@mui/lab/DesktopDateRangePicker/DesktopDateRangePicker.js
var React12 = __toESM(require_react());
var warnedOnce12 = false;
var warn8 = () => {
  if (!warnedOnce12) {
    console.warn(["MUI: The DesktopDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`", "", "You should use `import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro'`", "or `import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro/DesktopDateRangePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce12 = true;
  }
};
var DesktopDateRangePicker = React12.forwardRef(function DeprecatedDesktopDateRangePicker() {
  warn8();
  return null;
});
var DesktopDateRangePicker_default = DesktopDateRangePicker;

// node_modules/@mui/lab/DesktopDateTimePicker/DesktopDateTimePicker.js
var React13 = __toESM(require_react());
var warnedOnce13 = false;
var warn9 = () => {
  if (!warnedOnce13) {
    console.warn(["MUI: The DesktopDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { DesktopDateTimePicker } from '@mui/x-date-pickers'`", "or `import { DesktopDateTimePicker } from '@mui/x-date-pickers/DesktopDateTimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce13 = true;
  }
};
var DesktopDateTimePicker = React13.forwardRef(function DeprecatedDesktopDateTimePicker() {
  warn9();
  return null;
});
var DesktopDateTimePicker_default = DesktopDateTimePicker;

// node_modules/@mui/lab/DesktopTimePicker/DesktopTimePicker.js
var React14 = __toESM(require_react());
var warnedOnce14 = false;
var warn10 = () => {
  if (!warnedOnce14) {
    console.warn(["MUI: The DesktopTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { DesktopTimePicker } from '@mui/x-date-pickers'`", "or `import { DesktopTimePicker } from '@mui/x-date-pickers/DesktopTimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce14 = true;
  }
};
var DesktopTimePicker = React14.forwardRef(function DeprecatedDesktopTimePicker() {
  warn10();
  return null;
});
var DesktopTimePicker_default = DesktopTimePicker;

// node_modules/@mui/lab/LocalizationProvider/LocalizationProvider.js
var React15 = __toESM(require_react());
var warnedOnce15 = false;
var warn11 = () => {
  if (!warnedOnce15) {
    console.warn(["MUI: The LocalizationProvider component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { LocalizationProvider } from '@mui/x-date-pickers'`", "or `import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce15 = true;
  }
};
var LocalizationProvider = React15.forwardRef(function DeprecatedLocalizationProvider() {
  warn11();
  return null;
});
var LocalizationProvider_default = LocalizationProvider;

// node_modules/@mui/lab/MobileDatePicker/MobileDatePicker.js
var React16 = __toESM(require_react());
var warnedOnce16 = false;
var warn12 = () => {
  if (!warnedOnce16) {
    console.warn(["MUI: The MobileDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { MobileDatePicker } from '@mui/x-date-pickers'`", "or `import { MobileDatePicker } from '@mui/x-date-pickers/MobileDatePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce16 = true;
  }
};
var MobileDatePicker = React16.forwardRef(function DeprecatedMobileDatePicker(props, ref) {
  warn12();
  return null;
});
var MobileDatePicker_default = MobileDatePicker;

// node_modules/@mui/lab/MobileDateRangePicker/MobileDateRangePicker.js
var React17 = __toESM(require_react());
var warnedOnce17 = false;
var warn13 = () => {
  if (!warnedOnce17) {
    console.warn(["MUI: The MobileDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`", "", "You should use `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro'`", "or `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro/MobileDateRangePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce17 = true;
  }
};
var MobileDateRangePicker = React17.forwardRef(function DeprecatedMobileDateRangePicker() {
  warn13();
  return null;
});
var MobileDateRangePicker_default = MobileDateRangePicker;

// node_modules/@mui/lab/MobileDateTimePicker/MobileDateTimePicker.js
var React18 = __toESM(require_react());
var warnedOnce18 = false;
var warn14 = () => {
  if (!warnedOnce18) {
    console.warn(["MUI: The MobileDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { MobileDateTimePicker } from '@mui/x-date-pickers'`", "or `import { MobileDateTimePicker } from '@mui/x-date-pickers/MobileDateTimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce18 = true;
  }
};
var MobileDateTimePicker = React18.forwardRef(function DeprecatedMobileDateTimePicker() {
  warn14();
  return null;
});
var MobileDateTimePicker_default = MobileDateTimePicker;

// node_modules/@mui/lab/MobileTimePicker/MobileTimePicker.js
var React19 = __toESM(require_react());
var warnedOnce19 = false;
var warn15 = () => {
  if (!warnedOnce19) {
    console.warn(["MUI: The MobileTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { MobileTimePicker } from '@mui/x-date-pickers'`", "or `import { MobileTimePicker } from '@mui/x-date-pickers/MobileTimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce19 = true;
  }
};
var MobileTimePicker = React19.forwardRef(function DeprecatedMobileTimePicker() {
  warn15();
  return null;
});
var MobileTimePicker_default = MobileTimePicker;

// node_modules/@mui/lab/MonthPicker/MonthPicker.js
var React20 = __toESM(require_react());
var warnedOnce20 = false;
var warn16 = () => {
  if (!warnedOnce20) {
    console.warn(["MUI: The MonthPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { MonthPicker } from '@mui/x-date-pickers'`", "or `import { MonthPicker } from '@mui/x-date-pickers/MonthPicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce20 = true;
  }
};
var MonthPicker = React20.forwardRef(function DeprecatedMonthPicker() {
  warn16();
  return null;
});
var MonthPicker_default = MonthPicker;
var monthPickerClasses = {};
var getMonthPickerUtilityClass = (slot) => {
  warn16();
  return "";
};

// node_modules/@mui/lab/Pagination/Pagination.js
var React21 = __toESM(require_react());
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var warnedOnce21 = false;
var Pagination_default2 = React21.forwardRef(function DeprecatedPagination(props, ref) {
  if (!warnedOnce21) {
    console.warn(["MUI: The Pagination component was moved from the lab to the core.", "", "You should use `import { Pagination } from '@mui/material'`", "or `import Pagination from '@mui/material/Pagination'`"].join("\n"));
    warnedOnce21 = true;
  }
  return (0, import_jsx_runtime5.jsx)(Pagination_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/PaginationItem/PaginationItem.js
var React22 = __toESM(require_react());
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var warnedOnce22 = false;
var PaginationItem_default2 = React22.forwardRef(function DeprecatedPaginationItem(props, ref) {
  if (!warnedOnce22) {
    console.warn(["MUI: The PaginationItem component was moved from the lab to the core.", "", "You should use `import { PaginationItem } from '@mui/material'`", "or `import PaginationItem from '@mui/material/PaginationItem'`"].join("\n"));
    warnedOnce22 = true;
  }
  return (0, import_jsx_runtime6.jsx)(PaginationItem_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/CalendarPickerSkeleton/CalendarPickerSkeleton.js
var React23 = __toESM(require_react());
var warnedOnce23 = false;
var warn17 = () => {
  if (!warnedOnce23) {
    console.warn(["MUI: The CalendarPickerSkeleton component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { CalendarPickerSkeleton } from '@mui/x-date-pickers'`", "or `import { CalendarPickerSkeleton } from '@mui/x-date-pickers/CalendarPickerSkeleton'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce23 = true;
  }
};
var CalendarPickerSkeleton = React23.forwardRef(function DeprecatedCalendarPickerSkeleton() {
  warn17();
  return null;
});
var CalendarPickerSkeleton_default = CalendarPickerSkeleton;
var calendarPickerSkeletonClasses = {};
var getCalendarPickerSkeletonUtilityClass = (slot) => {
  warn17();
  return "";
};

// node_modules/@mui/lab/PickersDay/PickersDay.js
var React24 = __toESM(require_react());
var warnedOnce24 = false;
var warn18 = () => {
  if (!warnedOnce24) {
    console.warn(["MUI: The PickersDay component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { PickersDay } from '@mui/x-date-pickers'`", "or `import { PickersDay } from '@mui/x-date-pickers/PickersDay'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce24 = true;
  }
};
var PickersDay = React24.forwardRef(function DeprecatedPickersDay() {
  warn18();
  return null;
});
var PickersDay_default = PickersDay;
var pickersDayClasses = {};
var getPickersDayUtilityClass = (slot) => {
  warn18();
  return "";
};

// node_modules/@mui/lab/Rating/Rating.js
var React25 = __toESM(require_react());
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var warnedOnce25 = false;
var Rating_default2 = React25.forwardRef(function DeprecatedRating(props, ref) {
  if (!warnedOnce25) {
    console.warn(["MUI: The Rating component was moved from the lab to the core.", "", "You should use `import { Rating } from '@mui/material'`", "or `import Rating from '@mui/material/Rating'`"].join("\n"));
    warnedOnce25 = true;
  }
  return (0, import_jsx_runtime7.jsx)(Rating_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/Skeleton/Skeleton.js
var React26 = __toESM(require_react());
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var warnedOnce26 = false;
var Skeleton_default2 = React26.forwardRef(function DeprecatedSkeleton(props, ref) {
  if (!warnedOnce26) {
    console.warn(["MUI: The Skeleton component was moved from the lab to the core.", "", "You should use `import { Skeleton } from '@mui/material'`", "or `import Skeleton from '@mui/material/Skeleton'`"].join("\n"));
    warnedOnce26 = true;
  }
  return (0, import_jsx_runtime8.jsx)(Skeleton_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/SpeedDial/SpeedDial.js
var React27 = __toESM(require_react());
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var warnedOnce27 = false;
var SpeedDial_default2 = React27.forwardRef(function DeprecatedSpeedDial(props, ref) {
  if (!warnedOnce27) {
    console.warn(["MUI: The SpeedDial component was moved from the lab to the core.", "", "You should use `import { SpeedDial } from '@mui/material'`", "or `import SpeedDial from '@mui/material/SpeedDial'`"].join("\n"));
    warnedOnce27 = true;
  }
  return (0, import_jsx_runtime9.jsx)(SpeedDial_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/SpeedDialAction/SpeedDialAction.js
var React28 = __toESM(require_react());
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var warnedOnce28 = false;
var SpeedDialAction_default2 = React28.forwardRef(function DeprecatedSpeedDialAction(props, ref) {
  if (!warnedOnce28) {
    console.warn(["MUI: The SpeedDialAction component was moved from the lab to the core.", "", "You should use `import { SpeedDialAction } from '@mui/material'`", "or `import SpeedDialAction from '@mui/material/SpeedDialAction'`"].join("\n"));
    warnedOnce28 = true;
  }
  return (0, import_jsx_runtime10.jsx)(SpeedDialAction_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/SpeedDialIcon/SpeedDialIcon.js
var React29 = __toESM(require_react());
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var warnedOnce29 = false;
var SpeedDialIcon_default2 = React29.forwardRef(function DeprecatedSpeedDialIcon(props, ref) {
  if (!warnedOnce29) {
    console.warn(["MUI: The SpeedDialIcon component was moved from the lab to the core.", "", "You should use `import { SpeedDialIcon } from '@mui/material'`", "or `import SpeedDialIcon from '@mui/material/SpeedDialIcon'`"].join("\n"));
    warnedOnce29 = true;
  }
  return (0, import_jsx_runtime11.jsx)(SpeedDialIcon_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/StaticDatePicker/StaticDatePicker.js
var React30 = __toESM(require_react());
var warnedOnce30 = false;
var warn19 = () => {
  if (!warnedOnce30) {
    console.warn(["MUI: The StaticDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { StaticDatePicker } from '@mui/x-date-pickers'`", "or `import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce30 = true;
  }
};
var StaticDatePicker = React30.forwardRef(function DeprecatedStaticDatePicker() {
  warn19();
  return null;
});
var StaticDatePicker_default = StaticDatePicker;

// node_modules/@mui/lab/StaticDateRangePicker/StaticDateRangePicker.js
var React31 = __toESM(require_react());
var warnedOnce31 = false;
var warn20 = () => {
  if (!warnedOnce31) {
    console.warn(["MUI: The StaticDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`", "", "You should use `import { StaticDateRangePicker } from '@mui/x-date-pickers-pro'`", "or `import { StaticDateRangePicker } from '@mui/x-date-pickers-pro/StaticDateRangePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce31 = true;
  }
};
var StaticDateRangePicker = React31.forwardRef(function DeprecatedStaticDateRangePicker() {
  warn20();
  return null;
});
var StaticDateRangePicker_default = StaticDateRangePicker;

// node_modules/@mui/lab/StaticDateTimePicker/StaticDateTimePicker.js
var React32 = __toESM(require_react());
var warnedOnce32 = false;
var warn21 = () => {
  if (!warnedOnce32) {
    console.warn(["MUI: The StaticDateTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { StaticDateTimePicker } from '@mui/x-date-pickers'`", "or `import { StaticDateTimePicker } from '@mui/x-date-pickers/StaticDateTimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce32 = true;
  }
};
var StaticDateTimePicker = React32.forwardRef(function DeprecatedStaticDateTimePicker() {
  warn21();
  return null;
});
var StaticDateTimePicker_default = StaticDateTimePicker;

// node_modules/@mui/lab/StaticTimePicker/StaticTimePicker.js
var React33 = __toESM(require_react());
var warnedOnce33 = false;
var warn22 = () => {
  if (!warnedOnce33) {
    console.warn(["MUI: The StaticTimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { StaticTimePicker } from '@mui/x-date-pickers'`", "or `import { StaticTimePicker } from '@mui/x-date-pickers/StaticTimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce33 = true;
  }
};
var StaticTimePicker = React33.forwardRef(function DeprecatedStaticTimePicker() {
  warn22();
  return null;
});
var StaticTimePicker_default = StaticTimePicker;

// node_modules/@mui/lab/TabContext/TabContext.js
var React34 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var Context = React34.createContext(null);
if (true) {
  Context.displayName = "TabContext";
}
function useUniquePrefix() {
  const [id, setId] = React34.useState(null);
  React34.useEffect(() => {
    setId(`mui-p-${Math.round(Math.random() * 1e5)}`);
  }, []);
  return id;
}
function TabContext(props) {
  const {
    children,
    value
  } = props;
  const idPrefix = useUniquePrefix();
  const context = React34.useMemo(() => {
    return {
      idPrefix,
      value
    };
  }, [idPrefix, value]);
  return (0, import_jsx_runtime12.jsx)(Context.Provider, {
    value: context,
    children
  });
}
true ? TabContext.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types.default.node,
  /**
   * The value of the currently selected `Tab`.
   */
  value: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string]).isRequired
} : void 0;
function useTabContext() {
  return React34.useContext(Context);
}
function getPanelId(context, value) {
  const {
    idPrefix
  } = context;
  if (idPrefix === null) {
    return null;
  }
  return `${context.idPrefix}-P-${value}`;
}
function getTabId(context, value) {
  const {
    idPrefix
  } = context;
  if (idPrefix === null) {
    return null;
  }
  return `${context.idPrefix}-T-${value}`;
}

// node_modules/@mui/lab/TabList/TabList.js
var React35 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var _excluded = ["children"];
var TabList = React35.forwardRef(function TabList2(props, ref) {
  const {
    children: childrenProp
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const context = useTabContext();
  if (context === null) {
    throw new TypeError("No TabContext provided");
  }
  const children = React35.Children.map(childrenProp, (child) => {
    if (!React35.isValidElement(child)) {
      return null;
    }
    return React35.cloneElement(child, {
      // SOMEDAY: `Tabs` will set those themselves
      "aria-controls": getPanelId(context, child.props.value),
      id: getTabId(context, child.props.value)
    });
  });
  return (0, import_jsx_runtime13.jsx)(Tabs_default, _extends({}, other, {
    ref,
    value: context.value,
    children
  }));
});
true ? TabList.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * A list of `<Tab />` elements.
   */
  children: import_prop_types2.default.node
} : void 0;
var TabList_default = TabList;

// node_modules/@mui/lab/TabPanel/TabPanel.js
var React36 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());

// node_modules/@mui/lab/TabPanel/tabPanelClasses.js
function getTabPanelUtilityClass(slot) {
  return generateUtilityClass("MuiTabPanel", slot);
}
var tabPanelClasses = generateUtilityClasses("MuiTabPanel", ["root", "hidden"]);
var tabPanelClasses_default = tabPanelClasses;

// node_modules/@mui/lab/TabPanel/TabPanel.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var _excluded2 = ["children", "className", "value", "keepMounted"];
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    hidden
  } = ownerState;
  const slots = {
    root: ["root", hidden && "hidden"]
  };
  return composeClasses(slots, getTabPanelUtilityClass, classes);
};
var TabPanelRoot = styled_default("div", {
  name: "MuiTabPanel",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})(({
  theme
}) => ({
  padding: theme.spacing(3)
}));
var TabPanel = React36.forwardRef(function TabPanel2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTabPanel"
  });
  const {
    children,
    className,
    value,
    keepMounted = false
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const ownerState = _extends({}, props);
  const classes = useUtilityClasses(ownerState);
  const context = useTabContext();
  if (context === null) {
    throw new TypeError("No TabContext provided");
  }
  const id = getPanelId(context, value);
  const tabId = getTabId(context, value);
  return (0, import_jsx_runtime14.jsx)(TabPanelRoot, _extends({
    "aria-labelledby": tabId,
    className: clsx_default(classes.root, className),
    hidden: value !== context.value,
    id,
    ref,
    role: "tabpanel",
    ownerState
  }, other, {
    children: (keepMounted || value === context.value) && children
  }));
});
true ? TabPanel.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types3.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types3.default.object,
  /**
   * @ignore
   */
  className: import_prop_types3.default.string,
  /**
   * Always keep the children in the DOM.
   * @default false
   */
  keepMounted: import_prop_types3.default.bool,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types3.default.oneOfType([import_prop_types3.default.arrayOf(import_prop_types3.default.oneOfType([import_prop_types3.default.func, import_prop_types3.default.object, import_prop_types3.default.bool])), import_prop_types3.default.func, import_prop_types3.default.object]),
  /**
   * The `value` of the corresponding `Tab`. Must use the index of the `Tab` when
   * no `value` was passed to `Tab`.
   */
  value: import_prop_types3.default.string.isRequired
} : void 0;
var TabPanel_default = TabPanel;

// node_modules/@mui/lab/TimePicker/TimePicker.js
var React37 = __toESM(require_react());
var warnedOnce34 = false;
var warn23 = () => {
  if (!warnedOnce34) {
    console.warn(["MUI: The TimePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { TimePicker } from '@mui/x-date-pickers'`", "or `import { TimePicker } from '@mui/x-date-pickers/TimePicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce34 = true;
  }
};
var TimePicker = React37.forwardRef(function DeprecatedTimePicker() {
  warn23();
  return null;
});
var TimePicker_default = TimePicker;

// node_modules/@mui/lab/Timeline/Timeline.js
var React39 = __toESM(require_react());
var import_prop_types4 = __toESM(require_prop_types());

// node_modules/@mui/lab/Timeline/TimelineContext.js
var React38 = __toESM(require_react());
var TimelineContext = React38.createContext({});
if (true) {
  TimelineContext.displayName = "TimelineContext";
}
var TimelineContext_default = TimelineContext;

// node_modules/@mui/lab/Timeline/timelineClasses.js
function getTimelineUtilityClass(slot) {
  return generateUtilityClass("MuiTimeline", slot);
}
var timelineClasses = generateUtilityClasses("MuiTimeline", ["root", "positionLeft", "positionRight", "positionAlternate", "positionAlternateReverse"]);
var timelineClasses_default = timelineClasses;

// node_modules/@mui/lab/internal/convertTimelinePositionToClass.js
function convertTimelinePositionToClass(position) {
  return position === "alternate-reverse" ? "positionAlternateReverse" : `position${capitalize_default(position)}`;
}

// node_modules/@mui/lab/Timeline/Timeline.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var _excluded3 = ["position", "className"];
var useUtilityClasses2 = (ownerState) => {
  const {
    position,
    classes
  } = ownerState;
  const slots = {
    root: ["root", position && convertTimelinePositionToClass(position)]
  };
  return composeClasses(slots, getTimelineUtilityClass, classes);
};
var TimelineRoot = styled_default("ul", {
  name: "MuiTimeline",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, ownerState.position && styles[convertTimelinePositionToClass(ownerState.position)]];
  }
})({
  display: "flex",
  flexDirection: "column",
  padding: "6px 16px",
  flexGrow: 1
});
var Timeline = React39.forwardRef(function Timeline2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTimeline"
  });
  const {
    position = "right",
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded3);
  const ownerState = _extends({}, props, {
    position
  });
  const classes = useUtilityClasses2(ownerState);
  const contextValue = React39.useMemo(() => ({
    position
  }), [position]);
  return (0, import_jsx_runtime15.jsx)(TimelineContext_default.Provider, {
    value: contextValue,
    children: (0, import_jsx_runtime15.jsx)(TimelineRoot, _extends({
      className: clsx_default(classes.root, className),
      ownerState,
      ref
    }, other))
  });
});
true ? Timeline.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types4.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types4.default.object,
  /**
   * className applied to the root element.
   */
  className: import_prop_types4.default.string,
  /**
   * The position where the TimelineContent should appear relative to the time axis.
   * @default 'right'
   */
  position: import_prop_types4.default.oneOf(["alternate-reverse", "alternate", "left", "right"]),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types4.default.oneOfType([import_prop_types4.default.arrayOf(import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.object, import_prop_types4.default.bool])), import_prop_types4.default.func, import_prop_types4.default.object])
} : void 0;
var Timeline_default = Timeline;

// node_modules/@mui/lab/TimelineConnector/TimelineConnector.js
var React40 = __toESM(require_react());
var import_prop_types5 = __toESM(require_prop_types());

// node_modules/@mui/lab/TimelineConnector/timelineConnectorClasses.js
function getTimelineConnectorUtilityClass(slot) {
  return generateUtilityClass("MuiTimelineConnector", slot);
}
var timelineConnectorClasses = generateUtilityClasses("MuiTimelineConnector", ["root"]);
var timelineConnectorClasses_default = timelineConnectorClasses;

// node_modules/@mui/lab/TimelineConnector/TimelineConnector.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var _excluded4 = ["className"];
var useUtilityClasses3 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getTimelineConnectorUtilityClass, classes);
};
var TimelineConnectorRoot = styled_default("span", {
  name: "MuiTimelineConnector",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})(({
  theme
}) => {
  return {
    width: 2,
    backgroundColor: (theme.vars || theme).palette.grey[400],
    flexGrow: 1
  };
});
var TimelineConnector = React40.forwardRef(function TimelineConnector2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTimelineConnector"
  });
  const {
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded4);
  const ownerState = props;
  const classes = useUtilityClasses3(ownerState);
  return (0, import_jsx_runtime16.jsx)(TimelineConnectorRoot, _extends({
    className: clsx_default(classes.root, className),
    ownerState,
    ref
  }, other));
});
true ? TimelineConnector.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types5.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types5.default.object,
  /**
   * @ignore
   */
  className: import_prop_types5.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types5.default.oneOfType([import_prop_types5.default.arrayOf(import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.object, import_prop_types5.default.bool])), import_prop_types5.default.func, import_prop_types5.default.object])
} : void 0;
var TimelineConnector_default = TimelineConnector;

// node_modules/@mui/lab/TimelineContent/TimelineContent.js
var React41 = __toESM(require_react());
var import_prop_types6 = __toESM(require_prop_types());

// node_modules/@mui/lab/TimelineContent/timelineContentClasses.js
function getTimelineContentUtilityClass(slot) {
  return generateUtilityClass("MuiTimelineContent", slot);
}
var timelineContentClasses = generateUtilityClasses("MuiTimelineContent", ["root", "positionLeft", "positionRight", "positionAlternate", "positionAlternateReverse"]);
var timelineContentClasses_default = timelineContentClasses;

// node_modules/@mui/lab/TimelineContent/TimelineContent.js
var import_jsx_runtime17 = __toESM(require_jsx_runtime());
var _excluded5 = ["className"];
var useUtilityClasses4 = (ownerState) => {
  const {
    position,
    classes
  } = ownerState;
  const slots = {
    root: ["root", convertTimelinePositionToClass(position)]
  };
  return composeClasses(slots, getTimelineContentUtilityClass, classes);
};
var TimelineContentRoot = styled_default(Typography_default, {
  name: "MuiTimelineContent",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];
  }
})(({
  ownerState
}) => _extends({
  flex: 1,
  padding: "6px 16px",
  textAlign: "left"
}, ownerState.position === "left" && {
  textAlign: "right"
}));
var TimelineContent = React41.forwardRef(function TimelineContent2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTimelineContent"
  });
  const {
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded5);
  const {
    position: positionContext
  } = React41.useContext(TimelineContext_default);
  const ownerState = _extends({}, props, {
    position: positionContext || "right"
  });
  const classes = useUtilityClasses4(ownerState);
  return (0, import_jsx_runtime17.jsx)(TimelineContentRoot, _extends({
    component: "div",
    className: clsx_default(classes.root, className),
    ownerState,
    ref
  }, other));
});
true ? TimelineContent.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types6.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types6.default.object,
  /**
   * @ignore
   */
  className: import_prop_types6.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types6.default.oneOfType([import_prop_types6.default.arrayOf(import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.object, import_prop_types6.default.bool])), import_prop_types6.default.func, import_prop_types6.default.object])
} : void 0;
var TimelineContent_default = TimelineContent;

// node_modules/@mui/lab/TimelineDot/TimelineDot.js
var React42 = __toESM(require_react());
var import_prop_types7 = __toESM(require_prop_types());

// node_modules/@mui/lab/TimelineDot/timelineDotClasses.js
function getTimelineDotUtilityClass(slot) {
  return generateUtilityClass("MuiTimelineDot", slot);
}
var timelineDotClasses = generateUtilityClasses("MuiTimelineDot", ["root", "filled", "outlined", "filledGrey", "outlinedGrey", "filledPrimary", "outlinedPrimary", "filledSecondary", "outlinedSecondary"]);
var timelineDotClasses_default = timelineDotClasses;

// node_modules/@mui/lab/TimelineDot/TimelineDot.js
var import_jsx_runtime18 = __toESM(require_jsx_runtime());
var _excluded6 = ["className", "color", "variant"];
var useUtilityClasses5 = (ownerState) => {
  const {
    color,
    variant,
    classes
  } = ownerState;
  const slots = {
    root: ["root", variant, color !== "inherit" && `${variant}${capitalize_default(color)}`]
  };
  return composeClasses(slots, getTimelineDotUtilityClass, classes);
};
var TimelineDotRoot = styled_default("span", {
  name: "MuiTimelineDot",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[ownerState.color !== "inherit" && `${ownerState.variant}${capitalize_default(ownerState.color)}`], styles[ownerState.variant]];
  }
})(({
  ownerState,
  theme
}) => _extends({
  display: "flex",
  alignSelf: "baseline",
  borderStyle: "solid",
  borderWidth: 2,
  padding: 4,
  borderRadius: "50%",
  boxShadow: (theme.vars || theme).shadows[1],
  margin: "11.5px 0"
}, ownerState.variant === "filled" && _extends({
  borderColor: "transparent"
}, ownerState.color !== "inherit" && _extends({}, ownerState.color === "grey" ? {
  color: (theme.vars || theme).palette.grey[50],
  backgroundColor: (theme.vars || theme).palette.grey[400]
} : {
  color: (theme.vars || theme).palette[ownerState.color].contrastText,
  backgroundColor: (theme.vars || theme).palette[ownerState.color].main
})), ownerState.variant === "outlined" && _extends({
  boxShadow: "none",
  backgroundColor: "transparent"
}, ownerState.color !== "inherit" && _extends({}, ownerState.color === "grey" ? {
  borderColor: (theme.vars || theme).palette.grey[400]
} : {
  borderColor: (theme.vars || theme).palette[ownerState.color].main
}))));
var TimelineDot = React42.forwardRef(function TimelineDot2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTimelineDot"
  });
  const {
    className,
    color = "grey",
    variant = "filled"
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded6);
  const ownerState = _extends({}, props, {
    color,
    variant
  });
  const classes = useUtilityClasses5(ownerState);
  return (0, import_jsx_runtime18.jsx)(TimelineDotRoot, _extends({
    className: clsx_default(classes.root, className),
    ownerState,
    ref
  }, other));
});
true ? TimelineDot.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types7.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types7.default.object,
  /**
   * @ignore
   */
  className: import_prop_types7.default.string,
  /**
   * The dot can have a different colors.
   * @default 'grey'
   */
  color: import_prop_types7.default.oneOfType([import_prop_types7.default.oneOf(["error", "grey", "info", "inherit", "primary", "secondary", "success", "warning"]), import_prop_types7.default.string]),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types7.default.oneOfType([import_prop_types7.default.arrayOf(import_prop_types7.default.oneOfType([import_prop_types7.default.func, import_prop_types7.default.object, import_prop_types7.default.bool])), import_prop_types7.default.func, import_prop_types7.default.object]),
  /**
   * The dot can appear filled or outlined.
   * @default 'filled'
   */
  variant: import_prop_types7.default.oneOfType([import_prop_types7.default.oneOf(["filled", "outlined"]), import_prop_types7.default.string])
} : void 0;
var TimelineDot_default = TimelineDot;

// node_modules/@mui/lab/TimelineItem/TimelineItem.js
var React44 = __toESM(require_react());
var import_prop_types9 = __toESM(require_prop_types());

// node_modules/@mui/lab/TimelineOppositeContent/TimelineOppositeContent.js
var React43 = __toESM(require_react());
var import_prop_types8 = __toESM(require_prop_types());

// node_modules/@mui/lab/TimelineOppositeContent/timelineOppositeContentClasses.js
function getTimelineOppositeContentUtilityClass(slot) {
  return generateUtilityClass("MuiTimelineOppositeContent", slot);
}
var timelineOppositeContentClasses = generateUtilityClasses("MuiTimelineOppositeContent", ["root", "positionLeft", "positionRight", "positionAlternate", "positionAlternateReverse"]);
var timelineOppositeContentClasses_default = timelineOppositeContentClasses;

// node_modules/@mui/lab/TimelineOppositeContent/TimelineOppositeContent.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime());
var _excluded7 = ["className"];
var useUtilityClasses6 = (ownerState) => {
  const {
    position,
    classes
  } = ownerState;
  const slots = {
    root: ["root", convertTimelinePositionToClass(position)]
  };
  return composeClasses(slots, getTimelineOppositeContentUtilityClass, classes);
};
var TimelineOppositeContentRoot = styled_default(Typography_default, {
  name: "MuiTimelineOppositeContent",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];
  }
})(({
  ownerState
}) => _extends({
  padding: "6px 16px",
  marginRight: "auto",
  textAlign: "right",
  flex: 1
}, ownerState.position === "left" && {
  textAlign: "left"
}));
var TimelineOppositeContent = React43.forwardRef(function TimelineOppositeContent2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTimelineOppositeContent"
  });
  const {
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded7);
  const {
    position: positionContext
  } = React43.useContext(TimelineContext_default);
  const ownerState = _extends({}, props, {
    position: positionContext || "left"
  });
  const classes = useUtilityClasses6(ownerState);
  return (0, import_jsx_runtime19.jsx)(TimelineOppositeContentRoot, _extends({
    component: "div",
    className: clsx_default(classes.root, className),
    ownerState,
    ref
  }, other));
});
true ? TimelineOppositeContent.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types8.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types8.default.object,
  /**
   * @ignore
   */
  className: import_prop_types8.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types8.default.oneOfType([import_prop_types8.default.arrayOf(import_prop_types8.default.oneOfType([import_prop_types8.default.func, import_prop_types8.default.object, import_prop_types8.default.bool])), import_prop_types8.default.func, import_prop_types8.default.object])
} : void 0;
TimelineOppositeContent.muiName = "TimelineOppositeContent";
var TimelineOppositeContent_default = TimelineOppositeContent;

// node_modules/@mui/lab/TimelineItem/timelineItemClasses.js
function getTimelineItemUtilityClass(slot) {
  return generateUtilityClass("MuiTimelineItem", slot);
}
var timelineItemClasses = generateUtilityClasses("MuiTimelineItem", ["root", "positionLeft", "positionRight", "positionAlternate", "positionAlternateReverse", "missingOppositeContent"]);
var timelineItemClasses_default = timelineItemClasses;

// node_modules/@mui/lab/TimelineItem/TimelineItem.js
var import_jsx_runtime20 = __toESM(require_jsx_runtime());
var _excluded8 = ["position", "className"];
var useUtilityClasses7 = (ownerState) => {
  const {
    position,
    classes,
    hasOppositeContent
  } = ownerState;
  const slots = {
    root: ["root", convertTimelinePositionToClass(position), !hasOppositeContent && "missingOppositeContent"]
  };
  return composeClasses(slots, getTimelineItemUtilityClass, classes);
};
var TimelineItemRoot = styled_default("li", {
  name: "MuiTimelineItem",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[convertTimelinePositionToClass(ownerState.position)]];
  }
})(({
  ownerState
}) => _extends({
  listStyle: "none",
  display: "flex",
  position: "relative",
  minHeight: 70
}, ownerState.position === "left" && {
  flexDirection: "row-reverse"
}, (ownerState.position === "alternate" || ownerState.position === "alternate-reverse") && {
  [`&:nth-of-type(${ownerState.position === "alternate" ? "even" : "odd"})`]: {
    flexDirection: "row-reverse",
    [`& .${timelineContentClasses_default.root}`]: {
      textAlign: "right"
    },
    [`& .${timelineOppositeContentClasses_default.root}`]: {
      textAlign: "left"
    }
  }
}, !ownerState.hasOppositeContent && {
  "&::before": {
    content: '""',
    flex: 1,
    padding: "6px 16px"
  }
}));
var TimelineItem = React44.forwardRef(function TimelineItem2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTimelineItem"
  });
  const {
    position: positionProp,
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded8);
  const {
    position: positionContext
  } = React44.useContext(TimelineContext_default);
  let hasOppositeContent = false;
  React44.Children.forEach(props.children, (child) => {
    if (isMuiElement_default(child, ["TimelineOppositeContent"])) {
      hasOppositeContent = true;
    }
  });
  const ownerState = _extends({}, props, {
    position: positionProp || positionContext || "right",
    hasOppositeContent
  });
  const classes = useUtilityClasses7(ownerState);
  const contextValue = React44.useMemo(() => ({
    position: ownerState.position
  }), [ownerState.position]);
  return (0, import_jsx_runtime20.jsx)(TimelineContext_default.Provider, {
    value: contextValue,
    children: (0, import_jsx_runtime20.jsx)(TimelineItemRoot, _extends({
      className: clsx_default(classes.root, className),
      ownerState,
      ref
    }, other))
  });
});
true ? TimelineItem.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types9.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types9.default.object,
  /**
   * @ignore
   */
  className: import_prop_types9.default.string,
  /**
   * The position where the timeline's item should appear.
   */
  position: import_prop_types9.default.oneOf(["alternate-reverse", "alternate", "left", "right"]),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types9.default.oneOfType([import_prop_types9.default.arrayOf(import_prop_types9.default.oneOfType([import_prop_types9.default.func, import_prop_types9.default.object, import_prop_types9.default.bool])), import_prop_types9.default.func, import_prop_types9.default.object])
} : void 0;
var TimelineItem_default = TimelineItem;

// node_modules/@mui/lab/TimelineSeparator/TimelineSeparator.js
var React45 = __toESM(require_react());
var import_prop_types10 = __toESM(require_prop_types());

// node_modules/@mui/lab/TimelineSeparator/timelineSeparatorClasses.js
function getTimelineSeparatorUtilityClass(slot) {
  return generateUtilityClass("MuiTimelineSeparator", slot);
}
var timelineSeparatorClasses = generateUtilityClasses("MuiTimelineSeparator", ["root"]);
var timelineSeparatorClasses_default = timelineSeparatorClasses;

// node_modules/@mui/lab/TimelineSeparator/TimelineSeparator.js
var import_jsx_runtime21 = __toESM(require_jsx_runtime());
var _excluded9 = ["className"];
var useUtilityClasses8 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getTimelineSeparatorUtilityClass, classes);
};
var TimelineSeparatorRoot = styled_default("div", {
  name: "MuiTimelineSeparator",
  slot: "Root",
  overridesResolver: (props, styles) => styles.root
})({
  display: "flex",
  flexDirection: "column",
  flex: 0,
  alignItems: "center"
});
var TimelineSeparator = React45.forwardRef(function TimelineSeparator2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiTimelineSeparator"
  });
  const {
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded9);
  const ownerState = props;
  const classes = useUtilityClasses8(ownerState);
  return (0, import_jsx_runtime21.jsx)(TimelineSeparatorRoot, _extends({
    className: clsx_default(classes.root, className),
    ownerState,
    ref
  }, other));
});
true ? TimelineSeparator.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types10.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types10.default.object,
  /**
   * @ignore
   */
  className: import_prop_types10.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types10.default.oneOfType([import_prop_types10.default.arrayOf(import_prop_types10.default.oneOfType([import_prop_types10.default.func, import_prop_types10.default.object, import_prop_types10.default.bool])), import_prop_types10.default.func, import_prop_types10.default.object])
} : void 0;
var TimelineSeparator_default = TimelineSeparator;

// node_modules/@mui/lab/ToggleButton/ToggleButton.js
var React46 = __toESM(require_react());
var import_jsx_runtime22 = __toESM(require_jsx_runtime());
var warnedOnce35 = false;
var ToggleButton_default2 = React46.forwardRef(function DeprecatedToggleButton(props, ref) {
  if (!warnedOnce35) {
    console.warn(["MUI: The ToggleButton component was moved from the lab to the core.", "", "You should use `import { ToggleButton } from '@mui/material'`", "or `import ToggleButton from '@mui/material/ToggleButton'`"].join("\n"));
    warnedOnce35 = true;
  }
  return (0, import_jsx_runtime22.jsx)(ToggleButton_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/ToggleButtonGroup/ToggleButtonGroup.js
var React47 = __toESM(require_react());
var import_jsx_runtime23 = __toESM(require_jsx_runtime());
var warnedOnce36 = false;
var ToggleButtonGroup_default2 = React47.forwardRef(function DeprecatedToggleButtonGroup(props, ref) {
  if (!warnedOnce36) {
    console.warn(["MUI: The ToggleButtonGroup component was moved from the lab to the core.", "", "You should use `import { ToggleButtonGroup } from '@mui/material'`", "or `import ToggleButtonGroup from '@mui/material/ToggleButtonGroup'`"].join("\n"));
    warnedOnce36 = true;
  }
  return (0, import_jsx_runtime23.jsx)(ToggleButtonGroup_default, _extends({
    ref
  }, props));
});

// node_modules/@mui/lab/TreeItem/TreeItem.js
var React48 = __toESM(require_react());
var warnedOnce37 = false;
var warn24 = () => {
  if (!warnedOnce37) {
    console.warn(["MUI: The TreeItem component was moved from `@mui/lab` to `@mui/x-tree-view`.", "", "You should use `import { TreeItem } from '@mui/x-tree-view'`", "or `import { TreeItem } from '@mui/x-tree-view/TreeItem'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-tree-view-to-mui-x/."].join("\n"));
    warnedOnce37 = true;
  }
};
var TreeItem = React48.forwardRef(function DeprecatedTreeItem() {
  warn24();
  return null;
});
var TreeItem_default = TreeItem;

// node_modules/@mui/lab/TreeView/TreeView.js
var React49 = __toESM(require_react());
var warnedOnce38 = false;
var warn25 = () => {
  if (!warnedOnce38) {
    console.warn(["MUI: The TreeView component was moved from `@mui/lab` to `@mui/x-tree-view`.", "", "You should use `import { TreeView } from '@mui/x-tree-view'`", "or `import { TreeView } from '@mui/x-tree-view/TreeView'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-tree-view-to-mui-x/."].join("\n"));
    warnedOnce38 = true;
  }
};
var TreeView = React49.forwardRef(function DeprecatedTreeView() {
  warn25();
  return null;
});
var TreeView_default = TreeView;

// node_modules/@mui/lab/YearPicker/YearPicker.js
var warnedOnce39 = false;
var warn26 = () => {
  if (!warnedOnce39) {
    console.warn(["MUI: The YearPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.", "", "You should use `import { YearPicker } from '@mui/x-date-pickers'`", "or `import { YearPicker } from '@mui/x-date-pickers/YearPicker'`", "", "More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/."].join("\n"));
    warnedOnce39 = true;
  }
};
var YearPicker = function DeprecatedYearPicker() {
  warn26();
  return null;
};
var YearPicker_default = YearPicker;
var yearPickerClasses = {};
var getYearPickerUtilityClass = (slot) => {
  warn26();
  return "";
};

// node_modules/@mui/lab/Masonry/Masonry.js
var ReactDOM = __toESM(require_react_dom());
var import_prop_types11 = __toESM(require_prop_types());
var React50 = __toESM(require_react());

// node_modules/@mui/lab/Masonry/masonryClasses.js
function getMasonryUtilityClass(slot) {
  return generateUtilityClass("MuiMasonry", slot);
}
var masonryClasses = generateUtilityClasses("MuiMasonry", ["root"]);
var masonryClasses_default = masonryClasses;

// node_modules/@mui/lab/Masonry/Masonry.js
var import_jsx_runtime24 = __toESM(require_jsx_runtime());
var _excluded10 = ["children", "className", "component", "columns", "spacing", "sequential", "defaultColumns", "defaultHeight", "defaultSpacing"];
var parseToNumber = (val) => {
  return Number(val.replace("px", ""));
};
var lineBreakStyle = {
  flexBasis: "100%",
  width: 0,
  margin: 0,
  padding: 0
};
var useUtilityClasses9 = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getMasonryUtilityClass, classes);
};
var getStyle = ({
  ownerState,
  theme
}) => {
  let styles = {
    width: "100%",
    display: "flex",
    flexFlow: "column wrap",
    alignContent: "flex-start",
    boxSizing: "border-box",
    "& > *": {
      boxSizing: "border-box"
    }
  };
  const stylesSSR = {};
  if (ownerState.isSSR) {
    const orderStyleSSR = {};
    const defaultSpacing = parseToNumber(theme.spacing(ownerState.defaultSpacing));
    for (let i = 1; i <= ownerState.defaultColumns; i += 1) {
      orderStyleSSR[`&:nth-of-type(${ownerState.defaultColumns}n+${i % ownerState.defaultColumns})`] = {
        order: i
      };
    }
    stylesSSR.height = ownerState.defaultHeight;
    stylesSSR.margin = -(defaultSpacing / 2);
    stylesSSR["& > *"] = _extends({}, styles["& > *"], orderStyleSSR, {
      margin: defaultSpacing / 2,
      width: `calc(${(100 / ownerState.defaultColumns).toFixed(2)}% - ${defaultSpacing}px)`
    });
    return _extends({}, styles, stylesSSR);
  }
  const spacingValues = resolveBreakpointValues({
    values: ownerState.spacing,
    breakpoints: theme.breakpoints.values
  });
  const transformer = createUnarySpacing(theme);
  const spacingStyleFromPropValue = (propValue) => {
    let spacing;
    if (typeof propValue === "string" && !Number.isNaN(Number(propValue)) || typeof propValue === "number") {
      const themeSpacingValue = Number(propValue);
      spacing = getValue(transformer, themeSpacingValue);
    } else {
      spacing = propValue;
    }
    return _extends({
      margin: `calc(0px - (${spacing} / 2))`,
      "& > *": {
        margin: `calc(${spacing} / 2)`
      }
    }, ownerState.maxColumnHeight && {
      height: typeof spacing === "number" ? Math.ceil(ownerState.maxColumnHeight + parseToNumber(spacing)) : `calc(${ownerState.maxColumnHeight}px + ${spacing})`
    });
  };
  styles = deepmerge(styles, handleBreakpoints({
    theme
  }, spacingValues, spacingStyleFromPropValue));
  const columnValues = resolveBreakpointValues({
    values: ownerState.columns,
    breakpoints: theme.breakpoints.values
  });
  const columnStyleFromPropValue = (propValue) => {
    const columnValue = Number(propValue);
    const width = `${(100 / columnValue).toFixed(2)}%`;
    const spacing = typeof spacingValues === "string" && !Number.isNaN(Number(spacingValues)) || typeof spacingValues === "number" ? getValue(transformer, Number(spacingValues)) : "0px";
    return {
      "& > *": {
        width: `calc(${width} - ${spacing})`
      }
    };
  };
  styles = deepmerge(styles, handleBreakpoints({
    theme
  }, columnValues, columnStyleFromPropValue));
  if (typeof spacingValues === "object") {
    styles = deepmerge(styles, handleBreakpoints({
      theme
    }, spacingValues, (propValue, breakpoint) => {
      if (breakpoint) {
        const themeSpacingValue = Number(propValue);
        const lastBreakpoint = Object.keys(columnValues).pop();
        const spacing = getValue(transformer, themeSpacingValue);
        const column = typeof columnValues === "object" ? columnValues[breakpoint] || columnValues[lastBreakpoint] : columnValues;
        const width = `${(100 / column).toFixed(2)}%`;
        return {
          "& > *": {
            width: `calc(${width} - ${spacing})`
          }
        };
      }
      return null;
    }));
  }
  return styles;
};
var MasonryRoot = styled_default("div", {
  name: "MuiMasonry",
  slot: "Root",
  overridesResolver: (props, styles) => {
    return [styles.root];
  }
})(getStyle);
var Masonry = React50.forwardRef(function Masonry2(inProps, ref) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiMasonry"
  });
  const {
    children,
    className,
    component = "div",
    columns = 4,
    spacing = 1,
    sequential = false,
    defaultColumns,
    defaultHeight,
    defaultSpacing
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded10);
  const masonryRef = React50.useRef();
  const [maxColumnHeight, setMaxColumnHeight] = React50.useState();
  const isSSR = !maxColumnHeight && defaultHeight && defaultColumns !== void 0 && defaultSpacing !== void 0;
  const [numberOfLineBreaks, setNumberOfLineBreaks] = React50.useState(isSSR ? defaultColumns - 1 : 0);
  const ownerState = _extends({}, props, {
    spacing,
    columns,
    maxColumnHeight,
    defaultColumns,
    defaultHeight,
    defaultSpacing,
    isSSR
  });
  const classes = useUtilityClasses9(ownerState);
  const handleResize = React50.useCallback((masonryChildren) => {
    if (!masonryRef.current || !masonryChildren || masonryChildren.length === 0) {
      return;
    }
    const masonry = masonryRef.current;
    const masonryFirstChild = masonryRef.current.firstChild;
    const parentWidth = masonry.clientWidth;
    const firstChildWidth = masonryFirstChild.clientWidth;
    if (parentWidth === 0 || firstChildWidth === 0) {
      return;
    }
    const firstChildComputedStyle = window.getComputedStyle(masonryFirstChild);
    const firstChildMarginLeft = parseToNumber(firstChildComputedStyle.marginLeft);
    const firstChildMarginRight = parseToNumber(firstChildComputedStyle.marginRight);
    const currentNumberOfColumns = Math.round(parentWidth / (firstChildWidth + firstChildMarginLeft + firstChildMarginRight));
    const columnHeights = new Array(currentNumberOfColumns).fill(0);
    let skip = false;
    let nextOrder = 1;
    masonry.childNodes.forEach((child) => {
      if (child.nodeType !== Node.ELEMENT_NODE || child.dataset.class === "line-break" || skip) {
        return;
      }
      const childComputedStyle = window.getComputedStyle(child);
      const childMarginTop = parseToNumber(childComputedStyle.marginTop);
      const childMarginBottom = parseToNumber(childComputedStyle.marginBottom);
      const childHeight = parseToNumber(childComputedStyle.height) ? Math.ceil(parseToNumber(childComputedStyle.height)) + childMarginTop + childMarginBottom : 0;
      if (childHeight === 0) {
        skip = true;
        return;
      }
      for (let i = 0; i < child.childNodes.length; i += 1) {
        const nestedChild = child.childNodes[i];
        if (nestedChild.tagName === "IMG" && nestedChild.clientHeight === 0) {
          skip = true;
          break;
        }
      }
      if (!skip) {
        if (sequential) {
          columnHeights[nextOrder - 1] += childHeight;
          child.style.order = nextOrder;
          nextOrder += 1;
          if (nextOrder > currentNumberOfColumns) {
            nextOrder = 1;
          }
        } else {
          const currentMinColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));
          columnHeights[currentMinColumnIndex] += childHeight;
          const order = currentMinColumnIndex + 1;
          child.style.order = order;
        }
      }
    });
    if (!skip) {
      ReactDOM.flushSync(() => {
        setMaxColumnHeight(Math.max(...columnHeights));
        setNumberOfLineBreaks(currentNumberOfColumns > 0 ? currentNumberOfColumns - 1 : 0);
      });
    }
  }, [sequential]);
  useEnhancedEffect_default(() => {
    if (typeof ResizeObserver === "undefined") {
      return void 0;
    }
    let animationFrame;
    const resizeObserver = new ResizeObserver(() => {
      animationFrame = requestAnimationFrame(handleResize);
    });
    if (masonryRef.current) {
      masonryRef.current.childNodes.forEach((childNode) => {
        resizeObserver.observe(childNode);
      });
    }
    return () => {
      if (animationFrame) {
        window.cancelAnimationFrame(animationFrame);
      }
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [columns, spacing, children, handleResize]);
  const handleRef = useForkRef(ref, masonryRef);
  const lineBreaks = new Array(numberOfLineBreaks).fill("").map((_, index) => (0, import_jsx_runtime24.jsx)("span", {
    "data-class": "line-break",
    style: _extends({}, lineBreakStyle, {
      order: index + 1
    })
  }, index));
  return (0, import_jsx_runtime24.jsxs)(MasonryRoot, _extends({
    as: component,
    className: clsx_default(classes.root, className),
    ref: handleRef,
    ownerState
  }, other, {
    children: [children, lineBreaks]
  }));
});
true ? Masonry.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types11.default.node.isRequired,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types11.default.object,
  /**
   * @ignore
   */
  className: import_prop_types11.default.string,
  /**
   * Number of columns.
   * @default 4
   */
  columns: import_prop_types11.default.oneOfType([import_prop_types11.default.arrayOf(import_prop_types11.default.oneOfType([import_prop_types11.default.number, import_prop_types11.default.string])), import_prop_types11.default.number, import_prop_types11.default.object, import_prop_types11.default.string]),
  /**
   * The component used for the root node.
   * Either a string to use a HTML element or a component.
   */
  component: import_prop_types11.default.elementType,
  /**
   * The default number of columns of the component. This is provided for server-side rendering.
   */
  defaultColumns: import_prop_types11.default.number,
  /**
   * The default height of the component in px. This is provided for server-side rendering.
   */
  defaultHeight: import_prop_types11.default.number,
  /**
   * The default spacing of the component. Like `spacing`, it is a factor of the theme's spacing. This is provided for server-side rendering.
   */
  defaultSpacing: import_prop_types11.default.number,
  /**
   * Allows using sequential order rather than adding to shortest column
   * @default false
   */
  sequential: import_prop_types11.default.bool,
  /**
   * Defines the space between children. It is a factor of the theme's spacing.
   * @default 1
   */
  spacing: import_prop_types11.default.oneOfType([import_prop_types11.default.arrayOf(import_prop_types11.default.oneOfType([import_prop_types11.default.number, import_prop_types11.default.string])), import_prop_types11.default.number, import_prop_types11.default.object, import_prop_types11.default.string]),
  /**
   * Allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types11.default.oneOfType([import_prop_types11.default.arrayOf(import_prop_types11.default.oneOfType([import_prop_types11.default.func, import_prop_types11.default.object, import_prop_types11.default.bool])), import_prop_types11.default.func, import_prop_types11.default.object])
} : void 0;
var Masonry_default = Masonry;
export {
  Alert_default2 as Alert,
  AlertTitle_default2 as AlertTitle,
  Autocomplete_default2 as Autocomplete,
  AvatarGroup_default2 as AvatarGroup,
  CalendarPicker_default as CalendarPicker,
  CalendarPickerSkeleton_default as CalendarPickerSkeleton,
  ClockPicker_default as ClockPicker,
  DatePicker_default as DatePicker,
  DateRangePicker_default as DateRangePicker,
  DateRangePickerDay_default as DateRangePickerDay,
  DateTimePicker_default as DateTimePicker,
  DesktopDatePicker_default as DesktopDatePicker,
  DesktopDateRangePicker_default as DesktopDateRangePicker,
  DesktopDateTimePicker_default as DesktopDateTimePicker,
  DesktopTimePicker_default as DesktopTimePicker,
  LoadingButton_default as LoadingButton,
  LocalizationProvider_default as LocalizationProvider,
  Masonry_default as Masonry,
  MobileDatePicker_default as MobileDatePicker,
  MobileDateRangePicker_default as MobileDateRangePicker,
  MobileDateTimePicker_default as MobileDateTimePicker,
  MobileTimePicker_default as MobileTimePicker,
  MonthPicker_default as MonthPicker,
  Pagination_default2 as Pagination,
  PaginationItem_default2 as PaginationItem,
  PickersDay_default as PickersDay,
  Rating_default2 as Rating,
  Skeleton_default2 as Skeleton,
  SpeedDial_default2 as SpeedDial,
  SpeedDialAction_default2 as SpeedDialAction,
  SpeedDialIcon_default2 as SpeedDialIcon,
  StaticDatePicker_default as StaticDatePicker,
  StaticDateRangePicker_default as StaticDateRangePicker,
  StaticDateTimePicker_default as StaticDateTimePicker,
  StaticTimePicker_default as StaticTimePicker,
  TabContext,
  TabList_default as TabList,
  TabPanel_default as TabPanel,
  TimePicker_default as TimePicker,
  Timeline_default as Timeline,
  TimelineConnector_default as TimelineConnector,
  TimelineContent_default as TimelineContent,
  TimelineDot_default as TimelineDot,
  TimelineItem_default as TimelineItem,
  TimelineOppositeContent_default as TimelineOppositeContent,
  TimelineSeparator_default as TimelineSeparator,
  ToggleButton_default2 as ToggleButton,
  ToggleButtonGroup_default2 as ToggleButtonGroup,
  TreeItem_default as TreeItem,
  TreeView_default as TreeView,
  YearPicker_default as YearPicker,
  calendarPickerClasses,
  calendarPickerSkeletonClasses,
  clockPickerClasses,
  getCalendarPickerSkeletonUtilityClass,
  getDateRangePickerDayUtilityClass,
  getLoadingButtonUtilityClass,
  getMasonryUtilityClass,
  getMonthPickerUtilityClass,
  getPanelId,
  getPickersDayUtilityClass,
  getTabId,
  getTabPanelUtilityClass,
  getTimelineConnectorUtilityClass,
  getTimelineContentUtilityClass,
  getTimelineDotUtilityClass,
  getTimelineItemUtilityClass,
  getTimelineOppositeContentUtilityClass,
  getTimelineSeparatorUtilityClass,
  getTimelineUtilityClass,
  getYearPickerUtilityClass,
  loadingButtonClasses_default as loadingButtonClasses,
  masonryClasses_default as masonryClasses,
  monthPickerClasses,
  pickersDayClasses,
  tabPanelClasses_default as tabPanelClasses,
  timelineClasses_default as timelineClasses,
  timelineConnectorClasses_default as timelineConnectorClasses,
  timelineContentClasses_default as timelineContentClasses,
  timelineDotClasses_default as timelineDotClasses,
  timelineItemClasses_default as timelineItemClasses,
  timelineOppositeContentClasses_default as timelineOppositeContentClasses,
  timelineSeparatorClasses_default as timelineSeparatorClasses,
  useAutocomplete,
  usePagination,
  useTabContext,
  yearPickerClasses
};
/*! Bundled license information:

@mui/lab/index.js:
  (**
   * @mui/lab v6.0.0-dev.240424162023-9968b4889d
   *
   * @license MIT
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=@mui_lab.js.map
