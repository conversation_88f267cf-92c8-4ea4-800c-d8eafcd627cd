import { CountryDetail } from 'src/location/types';
import { CAPABILITY_STATUS_ENUM, INTERNAL_CAPABILITY_STATUS_ENUM } from 'src/shared/enums';
export type SchedulerResponseDto = {
    users: string[];
    locationDetails: LocationDetail;
    capabilityDetails: CapabilitiesDetail[];
};
export type GroupType = {
    users: Set<string>;
    locationDetails: any;
    capabilityDetails: CapabilitiesDetail;
    types: GroupCapabilityStatusType;
};
export type LocationDetail = {
    id: number;
    locationName: string;
    entityId: number;
    entityTitle: string;
    entityCode: string;
    status: string;
    countryDetail: CountryDetail;
    statusDate: Date | null;
    coreSolution: {
        title: string;
    };
    locationType: {
        title: string;
    };
};
export type CapabilitiesDetail = {
    id: number;
    locationId: number;
    capabilityId: number;
    status: CAPABILITY_STATUS_ENUM;
    internalStatus: INTERNAL_CAPABILITY_STATUS_ENUM;
    statusDate: Date;
    capability: string;
    subCategory: string;
    product: string;
    capabilityType: string;
    level: string;
    category: string;
    type: CAPABILITY_STATUS_TYPE_ENUM;
    daysLeft: number;
    daysAgo: number;
};
export type GroupCapabilityStatusType = {
    nearExpiry: boolean;
    nearPlanned: boolean;
    expired: boolean;
};
export declare const thresholdDayLimit = 7;
export declare enum CAPABILITY_STATUS_TYPE_ENUM {
    NEAR_EXPIRY = "NEAR_EXPIRY",
    NEAR_PLANNED = "NEAR_PLANNED",
    EXPIRED = "EXPIRED",
    PLANNED_PASSED_AGO = "PLANNED_PASSED_AGO"
}
