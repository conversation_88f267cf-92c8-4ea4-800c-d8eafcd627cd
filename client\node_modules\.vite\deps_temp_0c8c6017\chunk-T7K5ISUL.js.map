{"version": 3, "sources": ["../../@mui/material/Breadcrumbs/Breadcrumbs.js", "../../@mui/material/Breadcrumbs/BreadcrumbCollapsed.js", "../../@mui/material/internal/svg-icons/MoreHoriz.js", "../../@mui/material/Breadcrumbs/breadcrumbsClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Typography from \"../Typography/index.js\";\nimport BreadcrumbCollapsed from \"./BreadcrumbCollapsed.js\";\nimport breadcrumbsClasses, { getBreadcrumbsUtilityClass } from \"./breadcrumbsClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    li: ['li'],\n    ol: ['ol'],\n    separator: ['separator']\n  };\n  return composeClasses(slots, getBreadcrumbsUtilityClass, classes);\n};\nconst BreadcrumbsRoot = styled(Typography, {\n  name: 'MuiBreadcrumbs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [{\n      [`& .${breadcrumbsClasses.li}`]: styles.li\n    }, styles.root];\n  }\n})({});\nconst BreadcrumbsOl = styled('ol', {\n  name: 'MuiBreadcrumbs',\n  slot: 'Ol',\n  overridesResolver: (props, styles) => styles.ol\n})({\n  display: 'flex',\n  flexWrap: 'wrap',\n  alignItems: 'center',\n  padding: 0,\n  margin: 0,\n  listStyle: 'none'\n});\nconst BreadcrumbsSeparator = styled('li', {\n  name: 'MuiBreadcrumbs',\n  slot: 'Separator',\n  overridesResolver: (props, styles) => styles.separator\n})({\n  display: 'flex',\n  userSelect: 'none',\n  marginLeft: 8,\n  marginRight: 8\n});\nfunction insertSeparators(items, className, separator, ownerState) {\n  return items.reduce((acc, current, index) => {\n    if (index < items.length - 1) {\n      acc = acc.concat(current, /*#__PURE__*/_jsx(BreadcrumbsSeparator, {\n        \"aria-hidden\": true,\n        className: className,\n        ownerState: ownerState,\n        children: separator\n      }, `separator-${index}`));\n    } else {\n      acc.push(current);\n    }\n    return acc;\n  }, []);\n}\nconst Breadcrumbs = /*#__PURE__*/React.forwardRef(function Breadcrumbs(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBreadcrumbs'\n  });\n  const {\n    children,\n    className,\n    component = 'nav',\n    slots = {},\n    slotProps = {},\n    expandText = 'Show path',\n    itemsAfterCollapse = 1,\n    itemsBeforeCollapse = 1,\n    maxItems = 8,\n    separator = '/',\n    ...other\n  } = props;\n  const [expanded, setExpanded] = React.useState(false);\n  const ownerState = {\n    ...props,\n    component,\n    expanded,\n    expandText,\n    itemsAfterCollapse,\n    itemsBeforeCollapse,\n    maxItems,\n    separator\n  };\n  const classes = useUtilityClasses(ownerState);\n  const collapsedIconSlotProps = useSlotProps({\n    elementType: slots.CollapsedIcon,\n    externalSlotProps: slotProps.collapsedIcon,\n    ownerState\n  });\n  const listRef = React.useRef(null);\n  const renderItemsBeforeAndAfter = allItems => {\n    const handleClickExpand = () => {\n      setExpanded(true);\n\n      // The clicked element received the focus but gets removed from the DOM.\n      // Let's keep the focus in the component after expanding.\n      // Moving it to the <ol> or <nav> does not cause any announcement in NVDA.\n      // By moving it to some link/button at least we have some announcement.\n      const focusable = listRef.current.querySelector('a[href],button,[tabindex]');\n      if (focusable) {\n        focusable.focus();\n      }\n    };\n\n    // This defends against someone passing weird input, to ensure that if all\n    // items would be shown anyway, we just show all items without the EllipsisItem\n    if (itemsBeforeCollapse + itemsAfterCollapse >= allItems.length) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error(['MUI: You have provided an invalid combination of props to the Breadcrumbs.', `itemsAfterCollapse={${itemsAfterCollapse}} + itemsBeforeCollapse={${itemsBeforeCollapse}} >= maxItems={${maxItems}}`].join('\\n'));\n      }\n      return allItems;\n    }\n    return [...allItems.slice(0, itemsBeforeCollapse), /*#__PURE__*/_jsx(BreadcrumbCollapsed, {\n      \"aria-label\": expandText,\n      slots: {\n        CollapsedIcon: slots.CollapsedIcon\n      },\n      slotProps: {\n        collapsedIcon: collapsedIconSlotProps\n      },\n      onClick: handleClickExpand\n    }, \"ellipsis\"), ...allItems.slice(allItems.length - itemsAfterCollapse, allItems.length)];\n  };\n  const allItems = React.Children.toArray(children).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Breadcrumbs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  }).map((child, index) => /*#__PURE__*/_jsx(\"li\", {\n    className: classes.li,\n    children: child\n  }, `child-${index}`));\n  return /*#__PURE__*/_jsx(BreadcrumbsRoot, {\n    ref: ref,\n    component: component,\n    color: \"textSecondary\",\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ...other,\n    children: /*#__PURE__*/_jsx(BreadcrumbsOl, {\n      className: classes.ol,\n      ref: listRef,\n      ownerState: ownerState,\n      children: insertSeparators(expanded || maxItems && allItems.length <= maxItems ? allItems : renderItemsBeforeAndAfter(allItems), classes.separator, separator, ownerState)\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Breadcrumbs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Override the default label for the expand button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Show path'\n   */\n  expandText: PropTypes.string,\n  /**\n   * If max items is exceeded, the number of items to show after the ellipsis.\n   * @default 1\n   */\n  itemsAfterCollapse: integerPropType,\n  /**\n   * If max items is exceeded, the number of items to show before the ellipsis.\n   * @default 1\n   */\n  itemsBeforeCollapse: integerPropType,\n  /**\n   * Specifies the maximum number of breadcrumbs to display. When there are more\n   * than the maximum number, only the first `itemsBeforeCollapse` and last `itemsAfterCollapse`\n   * will be shown, with an ellipsis in between.\n   * @default 8\n   */\n  maxItems: integerPropType,\n  /**\n   * Custom separator node.\n   * @default '/'\n   */\n  separator: PropTypes.node,\n  /**\n   * The props used for each slot inside the Breadcumb.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    collapsedIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Breadcumb.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    CollapsedIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Breadcrumbs;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport MoreHorizIcon from \"../internal/svg-icons/MoreHoriz.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst BreadcrumbCollapsedButton = styled(ButtonBase)(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  marginLeft: `calc(${theme.spacing(1)} * 0.5)`,\n  marginRight: `calc(${theme.spacing(1)} * 0.5)`,\n  ...(theme.palette.mode === 'light' ? {\n    backgroundColor: theme.palette.grey[100],\n    color: theme.palette.grey[700]\n  } : {\n    backgroundColor: theme.palette.grey[700],\n    color: theme.palette.grey[100]\n  }),\n  borderRadius: 2,\n  '&:hover, &:focus': {\n    ...(theme.palette.mode === 'light' ? {\n      backgroundColor: theme.palette.grey[200]\n    } : {\n      backgroundColor: theme.palette.grey[600]\n    })\n  },\n  '&:active': {\n    boxShadow: theme.shadows[0],\n    ...(theme.palette.mode === 'light' ? {\n      backgroundColor: emphasize(theme.palette.grey[200], 0.12)\n    } : {\n      backgroundColor: emphasize(theme.palette.grey[600], 0.12)\n    })\n  }\n})));\nconst BreadcrumbCollapsedIcon = styled(MoreHorizIcon)({\n  width: 24,\n  height: 16\n});\n\n/**\n * @ignore - internal component.\n */\nfunction BreadcrumbCollapsed(props) {\n  const {\n    slots = {},\n    slotProps = {},\n    ...otherProps\n  } = props;\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(\"li\", {\n    children: /*#__PURE__*/_jsx(BreadcrumbCollapsedButton, {\n      focusRipple: true,\n      ...otherProps,\n      ownerState: ownerState,\n      children: /*#__PURE__*/_jsx(BreadcrumbCollapsedIcon, {\n        as: slots.CollapsedIcon,\n        ownerState: ownerState,\n        ...slotProps.collapsedIcon\n      })\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? BreadcrumbCollapsed.propTypes = {\n  /**\n   * The props used for the CollapsedIcon slot.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    collapsedIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the BreadcumbCollapsed.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    CollapsedIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.object\n} : void 0;\nexport default BreadcrumbCollapsed;", "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z\"\n}), 'MoreHoriz');", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBreadcrumbsUtilityClass(slot) {\n  return generateUtilityClass('MuiBreadcrumbs', slot);\n}\nconst breadcrumbsClasses = generateUtilityClasses('MuiBreadcrumbs', ['root', 'ol', 'li', 'separator']);\nexport default breadcrumbsClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,SAAuB;AACvB,sBAA2B;AAC3B,IAAAC,qBAAsB;;;ACFtB,IAAAC,SAAuB;AACvB,wBAAsB;;;ACDtB,YAAuB;AAMvB,yBAA4B;AAC5B,IAAO,oBAAQ,kBAA2B,mBAAAC,KAAK,QAAQ;AAAA,EACrD,GAAG;AACL,CAAC,GAAG,WAAW;;;ADFf,IAAAC,sBAA4B;AAC5B,IAAM,4BAA4B,eAAO,kBAAU,EAAE,kBAAU,CAAC;AAAA,EAC9D;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,YAAY,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,EACpC,aAAa,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,EACrC,GAAI,MAAM,QAAQ,SAAS,UAAU;AAAA,IACnC,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,IACvC,OAAO,MAAM,QAAQ,KAAK,GAAG;AAAA,EAC/B,IAAI;AAAA,IACF,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,IACvC,OAAO,MAAM,QAAQ,KAAK,GAAG;AAAA,EAC/B;AAAA,EACA,cAAc;AAAA,EACd,oBAAoB;AAAA,IAClB,GAAI,MAAM,QAAQ,SAAS,UAAU;AAAA,MACnC,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,IACzC,IAAI;AAAA,MACF,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,IACzC;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,WAAW,MAAM,QAAQ,CAAC;AAAA,IAC1B,GAAI,MAAM,QAAQ,SAAS,UAAU;AAAA,MACnC,iBAAiB,UAAU,MAAM,QAAQ,KAAK,GAAG,GAAG,IAAI;AAAA,IAC1D,IAAI;AAAA,MACF,iBAAiB,UAAU,MAAM,QAAQ,KAAK,GAAG,GAAG,IAAI;AAAA,IAC1D;AAAA,EACF;AACF,EAAE,CAAC;AACH,IAAM,0BAA0B,eAAO,iBAAa,EAAE;AAAA,EACpD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AAKD,SAAS,oBAAoB,OAAO;AAClC,QAAM;AAAA,IACJ,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AACnB,aAAoB,oBAAAC,KAAK,MAAM;AAAA,IAC7B,cAAuB,oBAAAA,KAAK,2BAA2B;AAAA,MACrD,aAAa;AAAA,MACb,GAAG;AAAA,MACH;AAAA,MACA,cAAuB,oBAAAA,KAAK,yBAAyB;AAAA,QACnD,IAAI,MAAM;AAAA,QACV;AAAA,QACA,GAAG,UAAU;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH;AACA,OAAwC,oBAAoB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtE,WAAW,kBAAAC,QAAU,MAAM;AAAA,IACzB,eAAe,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,eAAe,kBAAAA,QAAU;AAAA,EAC3B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU;AAChB,IAAI;AACJ,IAAO,8BAAQ;;;AEvFR,SAAS,2BAA2B,MAAM;AAC/C,SAAO,qBAAqB,kBAAkB,IAAI;AACpD;AACA,IAAM,qBAAqB,uBAAuB,kBAAkB,CAAC,QAAQ,MAAM,MAAM,WAAW,CAAC;AACrG,IAAO,6BAAQ;;;AHQf,IAAAC,sBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,IAAI,CAAC,IAAI;AAAA,IACT,IAAI,CAAC,IAAI;AAAA,IACT,WAAW,CAAC,WAAW;AAAA,EACzB;AACA,SAAO,eAAe,OAAO,4BAA4B,OAAO;AAClE;AACA,IAAM,kBAAkB,eAAO,oBAAY;AAAA,EACzC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,2BAAmB,EAAE,EAAE,GAAG,OAAO;AAAA,IAC1C,GAAG,OAAO,IAAI;AAAA,EAChB;AACF,CAAC,EAAE,CAAC,CAAC;AACL,IAAM,gBAAgB,eAAO,MAAM;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AACb,CAAC;AACD,IAAM,uBAAuB,eAAO,MAAM;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AACf,CAAC;AACD,SAAS,iBAAiB,OAAO,WAAW,WAAW,YAAY;AACjE,SAAO,MAAM,OAAO,CAAC,KAAK,SAAS,UAAU;AAC3C,QAAI,QAAQ,MAAM,SAAS,GAAG;AAC5B,YAAM,IAAI,OAAO,aAAsB,oBAAAC,KAAK,sBAAsB;AAAA,QAChE,eAAe;AAAA,QACf;AAAA,QACA;AAAA,QACA,UAAU;AAAA,MACZ,GAAG,aAAa,KAAK,EAAE,CAAC;AAAA,IAC1B,OAAO;AACL,UAAI,KAAK,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAM,cAAiC,kBAAW,SAASC,aAAY,SAAS,KAAK;AACnF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,aAAa;AAAA,IACb,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,UAAU,WAAW,IAAU,gBAAS,KAAK;AACpD,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,yBAAyB,qBAAa;AAAA,IAC1C,aAAa,MAAM;AAAA,IACnB,mBAAmB,UAAU;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,QAAM,UAAgB,cAAO,IAAI;AACjC,QAAM,4BAA4B,CAAAC,cAAY;AAC5C,UAAM,oBAAoB,MAAM;AAC9B,kBAAY,IAAI;AAMhB,YAAM,YAAY,QAAQ,QAAQ,cAAc,2BAA2B;AAC3E,UAAI,WAAW;AACb,kBAAU,MAAM;AAAA,MAClB;AAAA,IACF;AAIA,QAAI,sBAAsB,sBAAsBA,UAAS,QAAQ;AAC/D,UAAI,MAAuC;AACzC,gBAAQ,MAAM,CAAC,8EAA8E,uBAAuB,kBAAkB,4BAA4B,mBAAmB,kBAAkB,QAAQ,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,MAChO;AACA,aAAOA;AAAA,IACT;AACA,WAAO,CAAC,GAAGA,UAAS,MAAM,GAAG,mBAAmB,OAAgB,oBAAAF,KAAK,6BAAqB;AAAA,MACxF,cAAc;AAAA,MACd,OAAO;AAAA,QACL,eAAe,MAAM;AAAA,MACvB;AAAA,MACA,WAAW;AAAA,QACT,eAAe;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,IACX,GAAG,UAAU,GAAG,GAAGE,UAAS,MAAMA,UAAS,SAAS,oBAAoBA,UAAS,MAAM,CAAC;AAAA,EAC1F;AACA,QAAM,WAAiB,gBAAS,QAAQ,QAAQ,EAAE,OAAO,WAAS;AAChE,QAAI,MAAuC;AACzC,cAAI,4BAAW,KAAK,GAAG;AACrB,gBAAQ,MAAM,CAAC,wEAAwE,sCAAsC,EAAE,KAAK,IAAI,CAAC;AAAA,MAC3I;AAAA,IACF;AACA,WAA0B,sBAAe,KAAK;AAAA,EAChD,CAAC,EAAE,IAAI,CAAC,OAAO,cAAuB,oBAAAF,KAAK,MAAM;AAAA,IAC/C,WAAW,QAAQ;AAAA,IACnB,UAAU;AAAA,EACZ,GAAG,SAAS,KAAK,EAAE,CAAC;AACpB,aAAoB,oBAAAA,KAAK,iBAAiB;AAAA,IACxC;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,GAAG;AAAA,IACH,cAAuB,oBAAAA,KAAK,eAAe;AAAA,MACzC,WAAW,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL;AAAA,MACA,UAAU,iBAAiB,YAAY,YAAY,SAAS,UAAU,WAAW,WAAW,0BAA0B,QAAQ,GAAG,QAAQ,WAAW,WAAW,UAAU;AAAA,IAC3K,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,YAAY,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrF,UAAU,mBAAAG,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,mBAAAA,QAAU,MAAM;AAAA,IACzB,eAAe,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACvE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,eAAe,mBAAAA,QAAU;AAAA,EAC3B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,sBAAQ;", "names": ["React", "import_prop_types", "React", "_jsx", "import_jsx_runtime", "_jsx", "PropTypes", "import_jsx_runtime", "_jsx", "Breadcrumbs", "allItems", "PropTypes"]}