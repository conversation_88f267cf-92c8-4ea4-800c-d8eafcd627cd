import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/RadioGroup/useRadioGroup.js
var React2 = __toESM(require_react());

// node_modules/@mui/material/RadioGroup/RadioGroupContext.js
var React = __toESM(require_react());
var RadioGroupContext = React.createContext(void 0);
if (true) {
  RadioGroupContext.displayName = "RadioGroupContext";
}
var RadioGroupContext_default = RadioGroupContext;

// node_modules/@mui/material/RadioGroup/useRadioGroup.js
function useRadioGroup() {
  return React2.useContext(RadioGroupContext_default);
}

export {
  RadioGroupContext_default,
  useRadioGroup
};
//# sourceMappingURL=chunk-MGBQDQ77.js.map
