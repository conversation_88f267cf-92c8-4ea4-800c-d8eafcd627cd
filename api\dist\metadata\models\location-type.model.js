"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationType = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const models_2 = require("./");
const helpers_1 = require("../../shared/helpers");
const enums_1 = require("../../shared/enums");
const models_3 = require("../../location/models");
let LocationType = class LocationType extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], LocationType.prototype, "title", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'code',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.LOCATION_TYPE_ENUM)),
        allowNull: false,
        unique: true
    }),
    __metadata("design:type", String)
], LocationType.prototype, "code", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => models_2.CoreSolution),
    (0, sequelize_typescript_1.Column)({ field: 'core_solution_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], LocationType.prototype, "coreSolutionId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => models_2.CoreSolution),
    __metadata("design:type", models_2.CoreSolution)
], LocationType.prototype, "coreSolution", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'can_acquire_capability', type: sequelize_typescript_1.DataType.BOOLEAN, allowNull: false }),
    __metadata("design:type", Boolean)
], LocationType.prototype, "canAcquireCapability", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'location_form_sections',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.LOCATION_FORM_SECTION)),
        allowNull: false
    }),
    __metadata("design:type", Array)
], LocationType.prototype, "locationFormSections", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => models_2.CommonDropdown),
    __metadata("design:type", Array)
], LocationType.prototype, "commonDropdowns", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => models_3.Location),
    __metadata("design:type", Array)
], LocationType.prototype, "locations", void 0);
LocationType = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'meta_location_types' })
], LocationType);
exports.LocationType = LocationType;
//# sourceMappingURL=location-type.model.js.map