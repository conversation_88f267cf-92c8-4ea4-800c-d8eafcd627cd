export enum CAPABILITY_TYPE_ENUM {
  OPERATIONAL_AND_CONTRACTUAL = 'OPERATIONAL_AND_CONTRACTUAL',
  SYSTEM_AND_TECHNOLOGY = 'SYSTEM_AND_TECHNOLOGY',
  CERTIFICATE_AND_LICENCE = 'CERTIFICATE_AND_LICENCE',
}

export enum CAPABILITY_TYPE_ENUM_DISPLAY {
  OPERATIONAL_AND_CONTRACTUAL = 'Operational & Contractual',
  SYSTEM_AND_TECHNOLOGY = 'System & Technology',
  CERTIFICATE_AND_LICENCE = 'Certificate & Licence',
}

export enum CAPABILITY_LEVEL_ENUM {
  BASIC = 'BASIC',
  ENHANCED = 'ENHANCED',
  PREMIUM = 'PREMIUM',
  MARKET_LEADING = 'MARKET_LEADING',
}

export enum CAPABILITY_LEVEL_ENUM_DISPLAY {
  BASIC = 'Basic',
  ENHANCED = 'Enhanced',
  PREMIUM = 'Premium',
  MARKET_LEADING = 'Market Leading',
}

export enum CAPABILITY_STATUS_ENUM {
  RETIRED = 'RETIRED',
  PLANNED = 'PLANNED',
  EXPIRED = 'EXPIRED',
  EXISTING = 'EXISTING',
}

export enum CAPABILITY_STATUS_ENUM_DISPLAY {
  RETIRED = 'Retired',
  PLANNED = 'Planned',
  EXPIRED = 'Expired',
  EXISTING = 'Existing',
}

export enum PROVIDER_ENUM {
  DPW = 'DPW',
  THIRD_PARTY = 'THIRD_PARTY',
  CUSTOMER = 'CUSTOMER',
}

export enum PROVIDER_ENUM_DISPLAY {
  DPW = 'DPW',
  THIRD_PARTY = 'Third party',
  CUSTOMER = 'Customer',
}

export enum EVIDENC_TYPE_ENUM {
  UPLOAD = 'UPLOAD',
  FREETEXT = 'FREETEXT',
}

export enum EVIDENC_TYPE_ENUM_DISPLAY {
  UPLOAD = 'Upload',
  FREETEXT = 'Free text',
}

export enum CAPABILITY_STATUS_COLOR_ENUM {
  RETIRED = '#E70000',
  PLANNED = '#FFC90C',
  EXPIRED = '#FF5F5F',
  EXISTING = '#6FD420',
}

export enum SYSTEM_AND_TECHNOLOGY_ENUM {
  DPWORLD = 'DPWORLD',
  EXTERNAL = 'EXTERNAL',
  CUSTOMER = 'CUSTOMER',
}

export enum SYSTEM_AND_TECHNOLOGY_ENUM_DISPLAY {
  DPWORLD = 'DP World',
  EXTERNAL = 'External',
  CUSTOMER = 'Customer',
}

export enum VIEW_TYPE {
  FLAT = 'Flat',
  HIERARCHICAL = 'Hierarchical',
}

export enum IS_EVIDENCE_MANDATORY_ENUM {
  YES = 'YES',
  NO = 'NO',
}

export enum IS_EVIDENCE_MANDATORY_ENUM_DISPLAY {
  NO = 'No',
  YES = 'Yes',
}

export enum METADATA_TYPE_ENUM {
  CATEGORY = 'CATEGORY',
  PRODUCT = 'PRODUCT',
  PRODUCT_FAMILY = 'PRODUCT_FAMILY',

  BRANDS = 'BRANDS',
  TIMEZONE = 'TIMEZONE',
  CURRENCY = 'CURRENCY',
  LANGUAGE = 'LANGUAGE',
  FF_LOCATION_ID_TYPES = 'FF_LOCATION_ID_TYPES',
  STRATEGIC_CLASSIFICATION = 'STRATEGIC_CLASSIFICATION',
}

export enum METADATA_TYPE_ENUM_DISPLAY {
  PRODUCT = 'Product',
  PRODUCT_FAMILY = 'Product Family',
  CATEGORY = 'Category',
  BRANDS = 'Brands',
  TIMEZONE = 'Timezone',
  CURRENCY = 'Currency',
  LANGUAGE = 'Language',
  FF_LOCATION_ID_TYPES = 'Ff location id type',
  STRATEGIC_CLASSIFICATION = 'Strategic classification',
}
