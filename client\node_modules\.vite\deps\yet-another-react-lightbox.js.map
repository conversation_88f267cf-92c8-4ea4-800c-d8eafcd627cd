{"version": 3, "sources": ["../../yet-another-react-lightbox/dist/index.js", "../../yet-another-react-lightbox/dist/types.js"], "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { IMAGE_FIT_COVER, IMAGE_FIT_CONTAIN, ACTION_CLOSE, MODULE_CONTROLLER, UNKNOWN_ACTION_TYPE, ELEMENT_BUTTON, ELEMENT_ICON, EVENT_ON_WHEEL, EVENT_ON_KEY_UP, EVENT_ON_KEY_DOWN, EVENT_ON_POINTER_CANCEL, EVENT_ON_POINTER_LEAVE, EVENT_ON_POINTER_UP, EVENT_ON_POINTER_MOVE, EVENT_ON_POINTER_DOWN, SLIDE_STATUS_LOADING, activeSlideStatus, SLIDE_STATUS_COMPLETE, SLIDE_STATUS_ERROR, SLIDE_STATUS_PLACEHOLDER, ACTION_PREV, ACTION_NEXT, ACTION_SWIPE, MODULE_PORTAL, <PERSON>LASS_FLEX_CENTER, <PERSON><PERSON><PERSON><PERSON>_CAROUSEL, <PERSON>LA<PERSON>_SLIDE_WRAPPER, V<PERSON>_ARROW_RIGHT, VK_ARROW_LEFT, V<PERSON>_ESCAPE, MODULE_NAVIGATION, CLASS_NO_SCROLL, C<PERSON>SS_NO_SCROLL_PADDING, MODULE_NO_SCROLL, MODULE_ROOT, MODULE_TOOLBAR } from './types.js';\nimport { createPortal } from 'react-dom';\nexport { ACTIVE_SLIDE_COMPLETE, ACTIVE_SLIDE_ERROR, ACTIVE_SLIDE_LOADING, ACTIVE_SLIDE_PLAYING, CLASS_FULLSIZE, CLASS_SLIDE_WRAPPER_INTERACTIVE, PLUGIN_CAPTIONS, PLUGIN_COUNTER, PLUGIN_DOWNLOAD, PLUGIN_FULLSCREEN, PLUGIN_INLINE, PLUGIN_SHARE, PLUGIN_SLIDESHOW, PLUGIN_THUMBNAILS, PLUGIN_ZOOM, SLIDE_STATUS_PLAYING } from './types.js';\n\nconst cssPrefix$3 = \"yarl__\";\nfunction clsx(...classes) {\n    return [...classes].filter(Boolean).join(\" \");\n}\nfunction cssClass(name) {\n    return `${cssPrefix$3}${name}`;\n}\nfunction cssVar(name) {\n    return `--${cssPrefix$3}${name}`;\n}\nfunction composePrefix(base, prefix) {\n    return `${base}${prefix ? `_${prefix}` : \"\"}`;\n}\nfunction makeComposePrefix(base) {\n    return (prefix) => composePrefix(base, prefix);\n}\nfunction label(labels, defaultLabel) {\n    var _a;\n    return (_a = labels === null || labels === void 0 ? void 0 : labels[defaultLabel]) !== null && _a !== void 0 ? _a : defaultLabel;\n}\nfunction cleanup(...cleaners) {\n    return () => {\n        cleaners.forEach((cleaner) => {\n            cleaner();\n        });\n    };\n}\nfunction makeUseContext(name, contextName, context) {\n    return () => {\n        const ctx = React.useContext(context);\n        if (!ctx) {\n            throw new Error(`${name} must be used within a ${contextName}.Provider`);\n        }\n        return ctx;\n    };\n}\nfunction hasWindow() {\n    return typeof window !== \"undefined\";\n}\nfunction round(value, decimals = 0) {\n    const factor = 10 ** decimals;\n    return Math.round((value + Number.EPSILON) * factor) / factor;\n}\nfunction isImageSlide(slide) {\n    return slide.type === undefined || slide.type === \"image\";\n}\nfunction isImageFitCover(image, imageFit) {\n    return image.imageFit === IMAGE_FIT_COVER || (image.imageFit !== IMAGE_FIT_CONTAIN && imageFit === IMAGE_FIT_COVER);\n}\nfunction parseInt(value) {\n    return typeof value === \"string\" ? Number.parseInt(value, 10) : value;\n}\nfunction parseLengthPercentage(input) {\n    if (typeof input === \"number\") {\n        return { pixel: input };\n    }\n    if (typeof input === \"string\") {\n        const value = parseInt(input);\n        return input.endsWith(\"%\") ? { percent: value } : { pixel: value };\n    }\n    return { pixel: 0 };\n}\nfunction computeSlideRect(containerRect, padding) {\n    const paddingValue = parseLengthPercentage(padding);\n    const paddingPixels = paddingValue.percent !== undefined ? (containerRect.width / 100) * paddingValue.percent : paddingValue.pixel;\n    return {\n        width: Math.max(containerRect.width - 2 * paddingPixels, 0),\n        height: Math.max(containerRect.height - 2 * paddingPixels, 0),\n    };\n}\nfunction devicePixelRatio() {\n    return (hasWindow() ? window === null || window === void 0 ? void 0 : window.devicePixelRatio : undefined) || 1;\n}\nfunction getSlideIndex(index, slidesCount) {\n    return slidesCount > 0 ? ((index % slidesCount) + slidesCount) % slidesCount : 0;\n}\nfunction hasSlides(slides) {\n    return slides.length > 0;\n}\nfunction getSlide(slides, index) {\n    return slides[getSlideIndex(index, slides.length)];\n}\nfunction getSlideIfPresent(slides, index) {\n    return hasSlides(slides) ? getSlide(slides, index) : undefined;\n}\nfunction getSlideKey(slide) {\n    return isImageSlide(slide) ? slide.src : undefined;\n}\nfunction addToolbarButton(toolbar, key, button) {\n    if (!button)\n        return toolbar;\n    const { buttons, ...restToolbar } = toolbar;\n    const index = buttons.findIndex((item) => item === key);\n    const buttonWithKey = React.isValidElement(button) ? React.cloneElement(button, { key }, null) : button;\n    if (index >= 0) {\n        const result = [...buttons];\n        result.splice(index, 1, buttonWithKey);\n        return { buttons: result, ...restToolbar };\n    }\n    return { buttons: [buttonWithKey, ...buttons], ...restToolbar };\n}\nfunction stopNavigationEventsPropagation() {\n    const stopPropagation = (event) => {\n        event.stopPropagation();\n    };\n    return { onPointerDown: stopPropagation, onKeyDown: stopPropagation, onWheel: stopPropagation };\n}\nfunction calculatePreload(carousel, slides, minimum = 0) {\n    return Math.min(carousel.preload, Math.max(carousel.finite ? slides.length - 1 : Math.floor(slides.length / 2), minimum));\n}\nconst isReact19 = Number(React.version.split(\".\")[0]) >= 19;\nfunction makeInertWhen(condition) {\n    const legacyValue = condition ? \"\" : undefined;\n    return { inert: isReact19 ? condition : legacyValue };\n}\n\nconst LightboxDefaultProps = {\n    open: false,\n    close: () => { },\n    index: 0,\n    slides: [],\n    render: {},\n    plugins: [],\n    toolbar: { buttons: [ACTION_CLOSE] },\n    labels: {},\n    animation: {\n        fade: 250,\n        swipe: 500,\n        easing: {\n            fade: \"ease\",\n            swipe: \"ease-out\",\n            navigation: \"ease-in-out\",\n        },\n    },\n    carousel: {\n        finite: false,\n        preload: 2,\n        padding: \"16px\",\n        spacing: \"30%\",\n        imageFit: IMAGE_FIT_CONTAIN,\n        imageProps: {},\n    },\n    controller: {\n        ref: null,\n        focus: true,\n        aria: false,\n        touchAction: \"none\",\n        closeOnPullUp: false,\n        closeOnPullDown: false,\n        closeOnBackdropClick: false,\n        preventDefaultWheelX: true,\n        preventDefaultWheelY: false,\n        disableSwipeNavigation: false,\n    },\n    portal: {},\n    noScroll: {\n        disabled: false,\n    },\n    on: {},\n    styles: {},\n    className: \"\",\n};\n\nfunction createModule(name, component) {\n    return { name, component };\n}\nfunction createNode(module, children) {\n    return { module, children };\n}\nfunction traverseNode(node, target, apply) {\n    if (node.module.name === target) {\n        return apply(node);\n    }\n    if (node.children) {\n        return [\n            createNode(node.module, node.children.flatMap((n) => { var _a; return (_a = traverseNode(n, target, apply)) !== null && _a !== void 0 ? _a : []; })),\n        ];\n    }\n    return [node];\n}\nfunction traverse(nodes, target, apply) {\n    return nodes.flatMap((node) => { var _a; return (_a = traverseNode(node, target, apply)) !== null && _a !== void 0 ? _a : []; });\n}\nfunction withPlugins(root, plugins = [], augmentations = []) {\n    let config = root;\n    const contains = (target) => {\n        const nodes = [...config];\n        while (nodes.length > 0) {\n            const node = nodes.pop();\n            if ((node === null || node === void 0 ? void 0 : node.module.name) === target)\n                return true;\n            if (node === null || node === void 0 ? void 0 : node.children)\n                nodes.push(...node.children);\n        }\n        return false;\n    };\n    const addParent = (target, module) => {\n        if (target === \"\") {\n            config = [createNode(module, config)];\n            return;\n        }\n        config = traverse(config, target, (node) => [createNode(module, [node])]);\n    };\n    const append = (target, module) => {\n        config = traverse(config, target, (node) => [createNode(node.module, [createNode(module, node.children)])]);\n    };\n    const addChild = (target, module, precede) => {\n        config = traverse(config, target, (node) => {\n            var _a;\n            return [\n                createNode(node.module, [\n                    ...(precede ? [createNode(module)] : []),\n                    ...((_a = node.children) !== null && _a !== void 0 ? _a : []),\n                    ...(!precede ? [createNode(module)] : []),\n                ]),\n            ];\n        });\n    };\n    const addSibling = (target, module, precede) => {\n        config = traverse(config, target, (node) => [\n            ...(precede ? [createNode(module)] : []),\n            node,\n            ...(!precede ? [createNode(module)] : []),\n        ]);\n    };\n    const addModule = (module) => {\n        append(MODULE_CONTROLLER, module);\n    };\n    const replace = (target, module) => {\n        config = traverse(config, target, (node) => [createNode(module, node.children)]);\n    };\n    const remove = (target) => {\n        config = traverse(config, target, (node) => node.children);\n    };\n    const augment = (augmentation) => {\n        augmentations.push(augmentation);\n    };\n    plugins.forEach((plugin) => {\n        plugin({\n            contains,\n            addParent,\n            append,\n            addChild,\n            addSibling,\n            addModule,\n            replace,\n            remove,\n            augment,\n        });\n    });\n    return {\n        config,\n        augmentation: (props) => augmentations.reduce((acc, augmentation) => augmentation(acc), props),\n    };\n}\n\nconst DocumentContext = React.createContext(null);\nconst useDocumentContext = makeUseContext(\"useDocument\", \"DocumentContext\", DocumentContext);\nfunction DocumentContextProvider({ nodeRef, children }) {\n    const context = React.useMemo(() => {\n        const getOwnerDocument = (node) => { var _a; return ((_a = (node || nodeRef.current)) === null || _a === void 0 ? void 0 : _a.ownerDocument) || document; };\n        const getOwnerWindow = (node) => { var _a; return ((_a = getOwnerDocument(node)) === null || _a === void 0 ? void 0 : _a.defaultView) || window; };\n        return { getOwnerDocument, getOwnerWindow };\n    }, [nodeRef]);\n    return React.createElement(DocumentContext.Provider, { value: context }, children);\n}\n\nconst EventsContext = React.createContext(null);\nconst useEvents = makeUseContext(\"useEvents\", \"EventsContext\", EventsContext);\nfunction EventsProvider({ children }) {\n    const [subscriptions] = React.useState({});\n    React.useEffect(() => () => {\n        Object.keys(subscriptions).forEach((topic) => delete subscriptions[topic]);\n    }, [subscriptions]);\n    const context = React.useMemo(() => {\n        const unsubscribe = (topic, callback) => {\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.splice(0, subscriptions[topic].length, ...subscriptions[topic].filter((cb) => cb !== callback));\n        };\n        const subscribe = (topic, callback) => {\n            if (!subscriptions[topic]) {\n                subscriptions[topic] = [];\n            }\n            subscriptions[topic].push(callback);\n            return () => unsubscribe(topic, callback);\n        };\n        const publish = (...[topic, event]) => {\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.forEach((callback) => callback(event));\n        };\n        return { publish, subscribe, unsubscribe };\n    }, [subscriptions]);\n    return React.createElement(EventsContext.Provider, { value: context }, children);\n}\n\nconst LightboxPropsContext = React.createContext(null);\nconst useLightboxProps = makeUseContext(\"useLightboxProps\", \"LightboxPropsContext\", LightboxPropsContext);\nfunction LightboxPropsProvider({ children, ...props }) {\n    return React.createElement(LightboxPropsContext.Provider, { value: props }, children);\n}\n\nconst LightboxStateContext = React.createContext(null);\nconst useLightboxState = makeUseContext(\"useLightboxState\", \"LightboxStateContext\", LightboxStateContext);\nconst LightboxDispatchContext = React.createContext(null);\nconst useLightboxDispatch = makeUseContext(\"useLightboxDispatch\", \"LightboxDispatchContext\", LightboxDispatchContext);\nfunction reducer(state, action) {\n    switch (action.type) {\n        case \"swipe\": {\n            const { slides } = state;\n            const increment = (action === null || action === void 0 ? void 0 : action.increment) || 0;\n            const globalIndex = state.globalIndex + increment;\n            const currentIndex = getSlideIndex(globalIndex, slides.length);\n            const currentSlide = getSlideIfPresent(slides, currentIndex);\n            const animation = increment || action.duration !== undefined\n                ? {\n                    increment,\n                    duration: action.duration,\n                    easing: action.easing,\n                }\n                : undefined;\n            return { slides, currentIndex, globalIndex, currentSlide, animation };\n        }\n        case \"update\":\n            if (action.slides !== state.slides || action.index !== state.currentIndex) {\n                return {\n                    slides: action.slides,\n                    currentIndex: action.index,\n                    globalIndex: action.index,\n                    currentSlide: getSlideIfPresent(action.slides, action.index),\n                };\n            }\n            return state;\n        default:\n            throw new Error(UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction LightboxStateProvider({ slides, index, children }) {\n    const [state, dispatch] = React.useReducer(reducer, {\n        slides,\n        currentIndex: index,\n        globalIndex: index,\n        currentSlide: getSlideIfPresent(slides, index),\n    });\n    React.useEffect(() => {\n        dispatch({ type: \"update\", slides, index });\n    }, [slides, index]);\n    const context = React.useMemo(() => ({ ...state, state, dispatch }), [state, dispatch]);\n    return (React.createElement(LightboxDispatchContext.Provider, { value: dispatch },\n        React.createElement(LightboxStateContext.Provider, { value: context }, children)));\n}\n\nconst TimeoutsContext = React.createContext(null);\nconst useTimeouts = makeUseContext(\"useTimeouts\", \"TimeoutsContext\", TimeoutsContext);\nfunction TimeoutsProvider({ children }) {\n    const [timeouts] = React.useState([]);\n    React.useEffect(() => () => {\n        timeouts.forEach((tid) => window.clearTimeout(tid));\n        timeouts.splice(0, timeouts.length);\n    }, [timeouts]);\n    const context = React.useMemo(() => {\n        const removeTimeout = (id) => {\n            timeouts.splice(0, timeouts.length, ...timeouts.filter((tid) => tid !== id));\n        };\n        const setTimeout = (fn, delay) => {\n            const id = window.setTimeout(() => {\n                removeTimeout(id);\n                fn();\n            }, delay);\n            timeouts.push(id);\n            return id;\n        };\n        const clearTimeout = (id) => {\n            if (id !== undefined) {\n                removeTimeout(id);\n                window.clearTimeout(id);\n            }\n        };\n        return { setTimeout, clearTimeout };\n    }, [timeouts]);\n    return React.createElement(TimeoutsContext.Provider, { value: context }, children);\n}\n\nconst IconButton = React.forwardRef(function IconButton({ label: label$1, className, icon: Icon, renderIcon, onClick, style, ...rest }, ref) {\n    const { styles, labels } = useLightboxProps();\n    const buttonLabel = label(labels, label$1);\n    return (React.createElement(\"button\", { ref: ref, type: \"button\", title: buttonLabel, \"aria-label\": buttonLabel, className: clsx(cssClass(ELEMENT_BUTTON), className), onClick: onClick, style: { ...style, ...styles.button }, ...rest }, renderIcon ? renderIcon() : React.createElement(Icon, { className: cssClass(ELEMENT_ICON), style: styles.icon })));\n});\n\nfunction svgIcon(name, children) {\n    const icon = (props) => (React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 24 24\", width: \"24\", height: \"24\", \"aria-hidden\": \"true\", focusable: \"false\", ...props }, children));\n    icon.displayName = name;\n    return icon;\n}\nfunction createIcon(name, glyph) {\n    return svgIcon(name, React.createElement(\"g\", { fill: \"currentColor\" },\n        React.createElement(\"path\", { d: \"M0 0h24v24H0z\", fill: \"none\" }),\n        glyph));\n}\nfunction createIconDisabled(name, glyph) {\n    return svgIcon(name, React.createElement(React.Fragment, null,\n        React.createElement(\"defs\", null,\n            React.createElement(\"mask\", { id: \"strike\" },\n                React.createElement(\"path\", { d: \"M0 0h24v24H0z\", fill: \"white\" }),\n                React.createElement(\"path\", { d: \"M0 0L24 24\", stroke: \"black\", strokeWidth: 4 }))),\n        React.createElement(\"path\", { d: \"M0.70707 2.121320L21.878680 23.292883\", stroke: \"currentColor\", strokeWidth: 2 }),\n        React.createElement(\"g\", { fill: \"currentColor\", mask: \"url(#strike)\" },\n            React.createElement(\"path\", { d: \"M0 0h24v24H0z\", fill: \"none\" }),\n            glyph)));\n}\nconst CloseIcon = createIcon(\"Close\", React.createElement(\"path\", { d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\" }));\nconst PreviousIcon = createIcon(\"Previous\", React.createElement(\"path\", { d: \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\" }));\nconst NextIcon = createIcon(\"Next\", React.createElement(\"path\", { d: \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\" }));\nconst LoadingIcon = createIcon(\"Loading\", React.createElement(React.Fragment, null, Array.from({ length: 8 }).map((_, index, array) => (React.createElement(\"line\", { key: index, x1: \"12\", y1: \"6.5\", x2: \"12\", y2: \"1.8\", strokeLinecap: \"round\", strokeWidth: \"2.6\", stroke: \"currentColor\", strokeOpacity: (1 / array.length) * (index + 1), transform: `rotate(${(360 / array.length) * index}, 12, 12)` })))));\nconst ErrorIcon = createIcon(\"Error\", React.createElement(\"path\", { d: \"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z\" }));\n\nconst useLayoutEffect = hasWindow() ? React.useLayoutEffect : React.useEffect;\n\nfunction useMotionPreference() {\n    const [reduceMotion, setReduceMotion] = React.useState(false);\n    React.useEffect(() => {\n        var _a, _b;\n        const mediaQuery = (_a = window.matchMedia) === null || _a === void 0 ? void 0 : _a.call(window, \"(prefers-reduced-motion: reduce)\");\n        setReduceMotion(mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.matches);\n        const listener = (event) => setReduceMotion(event.matches);\n        (_b = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.addEventListener) === null || _b === void 0 ? void 0 : _b.call(mediaQuery, \"change\", listener);\n        return () => { var _a; return (_a = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.removeEventListener) === null || _a === void 0 ? void 0 : _a.call(mediaQuery, \"change\", listener); };\n    }, []);\n    return reduceMotion;\n}\n\nfunction currentTransformation(node) {\n    let x = 0;\n    let y = 0;\n    let z = 0;\n    const matrix = window.getComputedStyle(node).transform;\n    const matcher = matrix.match(/matrix.*\\((.+)\\)/);\n    if (matcher) {\n        const values = matcher[1].split(\",\").map(parseInt);\n        if (values.length === 6) {\n            x = values[4];\n            y = values[5];\n        }\n        else if (values.length === 16) {\n            x = values[12];\n            y = values[13];\n            z = values[14];\n        }\n    }\n    return { x, y, z };\n}\nfunction useAnimation(nodeRef, computeAnimation) {\n    const snapshot = React.useRef(undefined);\n    const animation = React.useRef(undefined);\n    const reduceMotion = useMotionPreference();\n    useLayoutEffect(() => {\n        var _a, _b, _c;\n        if (nodeRef.current && snapshot.current !== undefined && !reduceMotion) {\n            const { keyframes, duration, easing, onfinish } = computeAnimation(snapshot.current, nodeRef.current.getBoundingClientRect(), currentTransformation(nodeRef.current)) || {};\n            if (keyframes && duration) {\n                (_a = animation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n                animation.current = undefined;\n                try {\n                    animation.current = (_c = (_b = nodeRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, keyframes, { duration, easing });\n                }\n                catch (err) {\n                    console.error(err);\n                }\n                if (animation.current) {\n                    animation.current.onfinish = () => {\n                        animation.current = undefined;\n                        onfinish === null || onfinish === void 0 ? void 0 : onfinish();\n                    };\n                }\n            }\n        }\n        snapshot.current = undefined;\n    });\n    return {\n        prepareAnimation: (currentSnapshot) => {\n            snapshot.current = currentSnapshot;\n        },\n        isAnimationPlaying: () => { var _a; return ((_a = animation.current) === null || _a === void 0 ? void 0 : _a.playState) === \"running\"; },\n    };\n}\n\nfunction useContainerRect() {\n    const containerRef = React.useRef(null);\n    const observerRef = React.useRef(undefined);\n    const [containerRect, setContainerRect] = React.useState();\n    const setContainerRef = React.useCallback((node) => {\n        containerRef.current = node;\n        if (observerRef.current) {\n            observerRef.current.disconnect();\n            observerRef.current = undefined;\n        }\n        const updateContainerRect = () => {\n            if (node) {\n                const styles = window.getComputedStyle(node);\n                const parse = (value) => parseFloat(value) || 0;\n                setContainerRect({\n                    width: Math.round(node.clientWidth - parse(styles.paddingLeft) - parse(styles.paddingRight)),\n                    height: Math.round(node.clientHeight - parse(styles.paddingTop) - parse(styles.paddingBottom)),\n                });\n            }\n            else {\n                setContainerRect(undefined);\n            }\n        };\n        updateContainerRect();\n        if (node && typeof ResizeObserver !== \"undefined\") {\n            observerRef.current = new ResizeObserver(updateContainerRect);\n            observerRef.current.observe(node);\n        }\n    }, []);\n    return { setContainerRef, containerRef, containerRect };\n}\n\nfunction useDelay() {\n    const timeoutId = React.useRef(undefined);\n    const { setTimeout, clearTimeout } = useTimeouts();\n    return React.useCallback((callback, delay) => {\n        clearTimeout(timeoutId.current);\n        timeoutId.current = setTimeout(callback, delay > 0 ? delay : 0);\n    }, [setTimeout, clearTimeout]);\n}\n\nfunction useEventCallback(fn) {\n    const ref = React.useRef(fn);\n    useLayoutEffect(() => {\n        ref.current = fn;\n    });\n    return React.useCallback((...args) => { var _a; return (_a = ref.current) === null || _a === void 0 ? void 0 : _a.call(ref, ...args); }, []);\n}\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n}\nfunction useForkRef(refA, refB) {\n    return React.useMemo(() => refA == null && refB == null\n        ? null\n        : (refValue) => {\n            setRef(refA, refValue);\n            setRef(refB, refValue);\n        }, [refA, refB]);\n}\n\nfunction useLoseFocus(focus, disabled = false) {\n    const focused = React.useRef(false);\n    useLayoutEffect(() => {\n        if (disabled && focused.current) {\n            focused.current = false;\n            focus();\n        }\n    }, [disabled, focus]);\n    const onFocus = React.useCallback(() => {\n        focused.current = true;\n    }, []);\n    const onBlur = React.useCallback(() => {\n        focused.current = false;\n    }, []);\n    return { onFocus, onBlur };\n}\n\nfunction useRTL() {\n    const [isRTL, setIsRTL] = React.useState(false);\n    useLayoutEffect(() => {\n        setIsRTL(window.getComputedStyle(window.document.documentElement).direction === \"rtl\");\n    }, []);\n    return isRTL;\n}\n\nfunction useSensors() {\n    const [subscribers] = React.useState({});\n    const notifySubscribers = React.useCallback((type, event) => {\n        var _a;\n        (_a = subscribers[type]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => {\n            if (!event.isPropagationStopped())\n                listener(event);\n        });\n    }, [subscribers]);\n    const registerSensors = React.useMemo(() => ({\n        onPointerDown: (event) => notifySubscribers(EVENT_ON_POINTER_DOWN, event),\n        onPointerMove: (event) => notifySubscribers(EVENT_ON_POINTER_MOVE, event),\n        onPointerUp: (event) => notifySubscribers(EVENT_ON_POINTER_UP, event),\n        onPointerLeave: (event) => notifySubscribers(EVENT_ON_POINTER_LEAVE, event),\n        onPointerCancel: (event) => notifySubscribers(EVENT_ON_POINTER_CANCEL, event),\n        onKeyDown: (event) => notifySubscribers(EVENT_ON_KEY_DOWN, event),\n        onKeyUp: (event) => notifySubscribers(EVENT_ON_KEY_UP, event),\n        onWheel: (event) => notifySubscribers(EVENT_ON_WHEEL, event),\n    }), [notifySubscribers]);\n    const subscribeSensors = React.useCallback((type, callback) => {\n        if (!subscribers[type]) {\n            subscribers[type] = [];\n        }\n        subscribers[type].unshift(callback);\n        return () => {\n            const listeners = subscribers[type];\n            if (listeners) {\n                listeners.splice(0, listeners.length, ...listeners.filter((el) => el !== callback));\n            }\n        };\n    }, [subscribers]);\n    return { registerSensors, subscribeSensors };\n}\n\nfunction useThrottle(callback, delay) {\n    const lastCallbackTime = React.useRef(0);\n    const delayCallback = useDelay();\n    const executeCallback = useEventCallback((...args) => {\n        lastCallbackTime.current = Date.now();\n        callback(args);\n    });\n    return React.useCallback((...args) => {\n        delayCallback(() => {\n            executeCallback(args);\n        }, delay - (Date.now() - lastCallbackTime.current));\n    }, [delay, executeCallback, delayCallback]);\n}\n\nconst slidePrefix = makeComposePrefix(\"slide\");\nconst slideImagePrefix = makeComposePrefix(\"slide_image\");\nfunction ImageSlide({ slide: image, offset, render, rect, imageFit, imageProps, onClick, onLoad, onError, style, }) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const [status, setStatus] = React.useState(SLIDE_STATUS_LOADING);\n    const { publish } = useEvents();\n    const { setTimeout } = useTimeouts();\n    const imageRef = React.useRef(null);\n    React.useEffect(() => {\n        if (offset === 0) {\n            publish(activeSlideStatus(status));\n        }\n    }, [offset, status, publish]);\n    const handleLoading = useEventCallback((img) => {\n        (\"decode\" in img ? img.decode() : Promise.resolve())\n            .catch(() => { })\n            .then(() => {\n            if (!img.parentNode) {\n                return;\n            }\n            setStatus(SLIDE_STATUS_COMPLETE);\n            setTimeout(() => {\n                onLoad === null || onLoad === void 0 ? void 0 : onLoad(img);\n            }, 0);\n        });\n    });\n    const setImageRef = React.useCallback((img) => {\n        imageRef.current = img;\n        if (img === null || img === void 0 ? void 0 : img.complete) {\n            handleLoading(img);\n        }\n    }, [handleLoading]);\n    const handleOnLoad = React.useCallback((event) => {\n        handleLoading(event.currentTarget);\n    }, [handleLoading]);\n    const handleOnError = useEventCallback(() => {\n        setStatus(SLIDE_STATUS_ERROR);\n        onError === null || onError === void 0 ? void 0 : onError();\n    });\n    const cover = isImageFitCover(image, imageFit);\n    const nonInfinite = (value, fallback) => (Number.isFinite(value) ? value : fallback);\n    const maxWidth = nonInfinite(Math.max(...((_b = (_a = image.srcSet) === null || _a === void 0 ? void 0 : _a.map((x) => x.width)) !== null && _b !== void 0 ? _b : []).concat(image.width ? [image.width] : []).filter(Boolean)), ((_c = imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalWidth) || 0);\n    const maxHeight = nonInfinite(Math.max(...((_e = (_d = image.srcSet) === null || _d === void 0 ? void 0 : _d.map((x) => x.height)) !== null && _e !== void 0 ? _e : []).concat(image.height ? [image.height] : []).filter(Boolean)), ((_f = imageRef.current) === null || _f === void 0 ? void 0 : _f.naturalHeight) || 0);\n    const defaultStyle = maxWidth && maxHeight\n        ? {\n            maxWidth: `min(${maxWidth}px, 100%)`,\n            maxHeight: `min(${maxHeight}px, 100%)`,\n        }\n        : {\n            maxWidth: \"100%\",\n            maxHeight: \"100%\",\n        };\n    const srcSet = (_g = image.srcSet) === null || _g === void 0 ? void 0 : _g.sort((a, b) => a.width - b.width).map((item) => `${item.src} ${item.width}w`).join(\", \");\n    const estimateActualWidth = () => rect && !cover && image.width && image.height ? (rect.height / image.height) * image.width : Number.MAX_VALUE;\n    const sizes = srcSet && rect && hasWindow() ? `${Math.round(Math.min(estimateActualWidth(), rect.width))}px` : undefined;\n    const { style: imagePropsStyle, className: imagePropsClassName, ...restImageProps } = imageProps || {};\n    return (React.createElement(React.Fragment, null,\n        React.createElement(\"img\", { ref: setImageRef, onLoad: handleOnLoad, onError: handleOnError, onClick: onClick, draggable: false, className: clsx(cssClass(slideImagePrefix()), cover && cssClass(slideImagePrefix(\"cover\")), status !== SLIDE_STATUS_COMPLETE && cssClass(slideImagePrefix(\"loading\")), imagePropsClassName), style: { ...defaultStyle, ...style, ...imagePropsStyle }, ...restImageProps, alt: image.alt, sizes: sizes, srcSet: srcSet, src: image.src }),\n        status !== SLIDE_STATUS_COMPLETE && (React.createElement(\"div\", { className: cssClass(slidePrefix(SLIDE_STATUS_PLACEHOLDER)) },\n            status === SLIDE_STATUS_LOADING &&\n                ((render === null || render === void 0 ? void 0 : render.iconLoading) ? (render.iconLoading()) : (React.createElement(LoadingIcon, { className: clsx(cssClass(ELEMENT_ICON), cssClass(slidePrefix(SLIDE_STATUS_LOADING))) }))),\n            status === SLIDE_STATUS_ERROR &&\n                ((render === null || render === void 0 ? void 0 : render.iconError) ? (render.iconError()) : (React.createElement(ErrorIcon, { className: clsx(cssClass(ELEMENT_ICON), cssClass(slidePrefix(SLIDE_STATUS_ERROR))) })))))));\n}\n\nconst LightboxRoot = React.forwardRef(function LightboxRoot({ className, children, ...rest }, ref) {\n    const nodeRef = React.useRef(null);\n    return (React.createElement(DocumentContextProvider, { nodeRef: nodeRef },\n        React.createElement(\"div\", { ref: useForkRef(ref, nodeRef), className: clsx(cssClass(\"root\"), className), ...rest }, children)));\n});\n\nvar SwipeState;\n(function (SwipeState) {\n    SwipeState[SwipeState[\"NONE\"] = 0] = \"NONE\";\n    SwipeState[SwipeState[\"SWIPE\"] = 1] = \"SWIPE\";\n    SwipeState[SwipeState[\"PULL\"] = 2] = \"PULL\";\n    SwipeState[SwipeState[\"ANIMATION\"] = 3] = \"ANIMATION\";\n})(SwipeState || (SwipeState = {}));\n\nfunction usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled) {\n    React.useEffect(() => !disabled\n        ? cleanup(subscribeSensors(EVENT_ON_POINTER_DOWN, onPointerDown), subscribeSensors(EVENT_ON_POINTER_MOVE, onPointerMove), subscribeSensors(EVENT_ON_POINTER_UP, onPointerUp), subscribeSensors(EVENT_ON_POINTER_LEAVE, onPointerUp), subscribeSensors(EVENT_ON_POINTER_CANCEL, onPointerUp))\n        : () => { }, [subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled]);\n}\n\nvar Gesture;\n(function (Gesture) {\n    Gesture[Gesture[\"NONE\"] = 0] = \"NONE\";\n    Gesture[Gesture[\"SWIPE\"] = 1] = \"SWIPE\";\n    Gesture[Gesture[\"PULL\"] = 2] = \"PULL\";\n})(Gesture || (Gesture = {}));\nconst SWIPE_THRESHOLD = 30;\nfunction usePointerSwipe({ disableSwipeNavigation }, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel, pullUpEnabled, pullDownEnabled, onPullStart, onPullProgress, onPullFinish, onPullCancel) {\n    const offset = React.useRef(0);\n    const pointers = React.useRef([]);\n    const activePointer = React.useRef(undefined);\n    const startTime = React.useRef(0);\n    const gesture = React.useRef(Gesture.NONE);\n    const clearPointer = React.useCallback((event) => {\n        if (activePointer.current === event.pointerId) {\n            activePointer.current = undefined;\n            gesture.current = Gesture.NONE;\n        }\n        const currentPointers = pointers.current;\n        currentPointers.splice(0, currentPointers.length, ...currentPointers.filter((p) => p.pointerId !== event.pointerId));\n    }, []);\n    const addPointer = React.useCallback((event) => {\n        clearPointer(event);\n        event.persist();\n        pointers.current.push(event);\n    }, [clearPointer]);\n    const onPointerDown = useEventCallback((event) => {\n        addPointer(event);\n    });\n    const exceedsPullThreshold = (value, threshold) => (pullDownEnabled && value > threshold) || (pullUpEnabled && value < -threshold);\n    const onPointerUp = useEventCallback((event) => {\n        if (pointers.current.find((x) => x.pointerId === event.pointerId) && activePointer.current === event.pointerId) {\n            const duration = Date.now() - startTime.current;\n            const currentOffset = offset.current;\n            if (gesture.current === Gesture.SWIPE) {\n                if (Math.abs(currentOffset) > 0.3 * containerWidth ||\n                    (Math.abs(currentOffset) > 5 && duration < swipeAnimationDuration)) {\n                    onSwipeFinish(currentOffset, duration);\n                }\n                else {\n                    onSwipeCancel(currentOffset);\n                }\n            }\n            else if (gesture.current === Gesture.PULL) {\n                if (exceedsPullThreshold(currentOffset, 2 * SWIPE_THRESHOLD)) {\n                    onPullFinish(currentOffset, duration);\n                }\n                else {\n                    onPullCancel(currentOffset);\n                }\n            }\n            offset.current = 0;\n            gesture.current = Gesture.NONE;\n        }\n        clearPointer(event);\n    });\n    const onPointerMove = useEventCallback((event) => {\n        const pointer = pointers.current.find((p) => p.pointerId === event.pointerId);\n        if (pointer) {\n            const isCurrentPointer = activePointer.current === event.pointerId;\n            if (event.buttons === 0) {\n                if (isCurrentPointer && offset.current !== 0) {\n                    onPointerUp(event);\n                }\n                else {\n                    clearPointer(pointer);\n                }\n                return;\n            }\n            const deltaX = event.clientX - pointer.clientX;\n            const deltaY = event.clientY - pointer.clientY;\n            if (activePointer.current === undefined) {\n                const startGesture = (newGesture) => {\n                    addPointer(event);\n                    activePointer.current = event.pointerId;\n                    startTime.current = Date.now();\n                    gesture.current = newGesture;\n                };\n                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > SWIPE_THRESHOLD && isSwipeValid(deltaX)) {\n                    if (!disableSwipeNavigation) {\n                        startGesture(Gesture.SWIPE);\n                        onSwipeStart();\n                    }\n                }\n                else if (Math.abs(deltaY) > Math.abs(deltaX) && exceedsPullThreshold(deltaY, SWIPE_THRESHOLD)) {\n                    startGesture(Gesture.PULL);\n                    onPullStart();\n                }\n            }\n            else if (isCurrentPointer) {\n                if (gesture.current === Gesture.SWIPE) {\n                    offset.current = deltaX;\n                    onSwipeProgress(deltaX);\n                }\n                else if (gesture.current === Gesture.PULL) {\n                    offset.current = deltaY;\n                    onPullProgress(deltaY);\n                }\n            }\n        }\n    });\n    usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp);\n}\n\nfunction usePreventWheelDefaults({ preventDefaultWheelX, preventDefaultWheelY, }) {\n    const ref = React.useRef(null);\n    const listener = useEventCallback((event) => {\n        const horizontal = Math.abs(event.deltaX) > Math.abs(event.deltaY);\n        if ((horizontal && preventDefaultWheelX) || (!horizontal && preventDefaultWheelY) || event.ctrlKey) {\n            event.preventDefault();\n        }\n    });\n    return React.useCallback((node) => {\n        var _a;\n        if (node) {\n            node.addEventListener(\"wheel\", listener, { passive: false });\n        }\n        else {\n            (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(\"wheel\", listener);\n        }\n        ref.current = node;\n    }, [listener]);\n}\n\nfunction useWheelSwipe(swipeState, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel) {\n    const offset = React.useRef(0);\n    const intent = React.useRef(0);\n    const intentCleanup = React.useRef(undefined);\n    const resetCleanup = React.useRef(undefined);\n    const wheelInertia = React.useRef(0);\n    const wheelInertiaCleanup = React.useRef(undefined);\n    const startTime = React.useRef(0);\n    const { setTimeout, clearTimeout } = useTimeouts();\n    const cancelSwipeIntentCleanup = React.useCallback(() => {\n        if (intentCleanup.current) {\n            clearTimeout(intentCleanup.current);\n            intentCleanup.current = undefined;\n        }\n    }, [clearTimeout]);\n    const cancelSwipeResetCleanup = React.useCallback(() => {\n        if (resetCleanup.current) {\n            clearTimeout(resetCleanup.current);\n            resetCleanup.current = undefined;\n        }\n    }, [clearTimeout]);\n    const handleCleanup = useEventCallback(() => {\n        if (swipeState !== SwipeState.SWIPE) {\n            offset.current = 0;\n            startTime.current = 0;\n            cancelSwipeIntentCleanup();\n            cancelSwipeResetCleanup();\n        }\n    });\n    React.useEffect(handleCleanup, [swipeState, handleCleanup]);\n    const handleCancelSwipe = useEventCallback((currentSwipeOffset) => {\n        resetCleanup.current = undefined;\n        if (offset.current === currentSwipeOffset) {\n            onSwipeCancel(offset.current);\n        }\n    });\n    const onWheel = useEventCallback((event) => {\n        if (event.ctrlKey) {\n            return;\n        }\n        if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n            return;\n        }\n        const setWheelInertia = (inertia) => {\n            wheelInertia.current = inertia;\n            clearTimeout(wheelInertiaCleanup.current);\n            wheelInertiaCleanup.current =\n                inertia > 0\n                    ? setTimeout(() => {\n                        wheelInertia.current = 0;\n                        wheelInertiaCleanup.current = undefined;\n                    }, 300)\n                    : undefined;\n        };\n        if (swipeState === SwipeState.NONE) {\n            if (Math.abs(event.deltaX) <= 1.2 * Math.abs(wheelInertia.current)) {\n                setWheelInertia(event.deltaX);\n                return;\n            }\n            if (!isSwipeValid(-event.deltaX)) {\n                return;\n            }\n            intent.current += event.deltaX;\n            cancelSwipeIntentCleanup();\n            if (Math.abs(intent.current) > 30) {\n                intent.current = 0;\n                setWheelInertia(0);\n                startTime.current = Date.now();\n                onSwipeStart();\n            }\n            else {\n                const currentSwipeIntent = intent.current;\n                intentCleanup.current = setTimeout(() => {\n                    intentCleanup.current = undefined;\n                    if (currentSwipeIntent === intent.current) {\n                        intent.current = 0;\n                    }\n                }, swipeAnimationDuration);\n            }\n        }\n        else if (swipeState === SwipeState.SWIPE) {\n            let newSwipeOffset = offset.current - event.deltaX;\n            newSwipeOffset = Math.min(Math.abs(newSwipeOffset), containerWidth) * Math.sign(newSwipeOffset);\n            offset.current = newSwipeOffset;\n            onSwipeProgress(newSwipeOffset);\n            cancelSwipeResetCleanup();\n            if (Math.abs(newSwipeOffset) > 0.2 * containerWidth) {\n                setWheelInertia(event.deltaX);\n                onSwipeFinish(newSwipeOffset, Date.now() - startTime.current);\n                return;\n            }\n            resetCleanup.current = setTimeout(() => handleCancelSwipe(newSwipeOffset), 2 * swipeAnimationDuration);\n        }\n        else {\n            setWheelInertia(event.deltaX);\n        }\n    });\n    React.useEffect(() => subscribeSensors(EVENT_ON_WHEEL, onWheel), [subscribeSensors, onWheel]);\n}\n\nconst cssContainerPrefix = makeComposePrefix(\"container\");\nconst ControllerContext = React.createContext(null);\nconst useController = makeUseContext(\"useController\", \"ControllerContext\", ControllerContext);\nfunction Controller({ children, ...props }) {\n    var _a;\n    const { carousel, animation, controller, on, styles, render } = props;\n    const { closeOnPullUp, closeOnPullDown, preventDefaultWheelX, preventDefaultWheelY } = controller;\n    const [toolbarWidth, setToolbarWidth] = React.useState();\n    const state = useLightboxState();\n    const dispatch = useLightboxDispatch();\n    const [swipeState, setSwipeState] = React.useState(SwipeState.NONE);\n    const swipeOffset = React.useRef(0);\n    const pullOffset = React.useRef(0);\n    const pullOpacity = React.useRef(1);\n    const { registerSensors, subscribeSensors } = useSensors();\n    const { subscribe, publish } = useEvents();\n    const cleanupAnimationIncrement = useDelay();\n    const cleanupSwipeOffset = useDelay();\n    const cleanupPullOffset = useDelay();\n    const { containerRef, setContainerRef, containerRect } = useContainerRect();\n    const handleContainerRef = useForkRef(usePreventWheelDefaults({ preventDefaultWheelX, preventDefaultWheelY }), setContainerRef);\n    const carouselRef = React.useRef(null);\n    const setCarouselRef = useForkRef(carouselRef, undefined);\n    const { getOwnerDocument } = useDocumentContext();\n    const isRTL = useRTL();\n    const rtl = (value) => (isRTL ? -1 : 1) * (typeof value === \"number\" ? value : 1);\n    const focus = useEventCallback(() => { var _a; return (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.focus(); });\n    const getLightboxProps = useEventCallback(() => props);\n    const getLightboxState = useEventCallback(() => state);\n    const prev = React.useCallback((params) => publish(ACTION_PREV, params), [publish]);\n    const next = React.useCallback((params) => publish(ACTION_NEXT, params), [publish]);\n    const close = React.useCallback(() => publish(ACTION_CLOSE), [publish]);\n    const isSwipeValid = (offset) => !(carousel.finite &&\n        ((rtl(offset) > 0 && state.currentIndex === 0) ||\n            (rtl(offset) < 0 && state.currentIndex === state.slides.length - 1)));\n    const setSwipeOffset = (offset) => {\n        var _a;\n        swipeOffset.current = offset;\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"swipe_offset\"), `${Math.round(offset)}px`);\n    };\n    const setPullOffset = (offset) => {\n        var _a, _b;\n        pullOffset.current = offset;\n        pullOpacity.current = (() => {\n            const threshold = 60;\n            const minOpacity = 0.5;\n            const offsetValue = (() => {\n                if (closeOnPullDown && offset > 0)\n                    return offset;\n                if (closeOnPullUp && offset < 0)\n                    return -offset;\n                return 0;\n            })();\n            return Math.min(Math.max(round(1 - (offsetValue / threshold) * (1 - minOpacity), 2), minOpacity), 1);\n        })();\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"pull_offset\"), `${Math.round(offset)}px`);\n        (_b = containerRef.current) === null || _b === void 0 ? void 0 : _b.style.setProperty(cssVar(\"pull_opacity\"), `${pullOpacity.current}`);\n    };\n    const { prepareAnimation: preparePullAnimation } = useAnimation(carouselRef, (snapshot, rect, translate) => {\n        if (carouselRef.current && containerRect) {\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(0, ${snapshot.rect.y - rect.y + translate.y}px)`,\n                        opacity: snapshot.opacity,\n                    },\n                    { transform: \"translate(0, 0)\", opacity: 1 },\n                ],\n                duration: snapshot.duration,\n                easing: animation.easing.fade,\n            };\n        }\n        return undefined;\n    });\n    const pull = (offset, cancel) => {\n        if (closeOnPullUp || closeOnPullDown) {\n            setPullOffset(offset);\n            let duration = 0;\n            if (carouselRef.current) {\n                duration = animation.fade * (cancel ? 2 : 1);\n                preparePullAnimation({\n                    rect: carouselRef.current.getBoundingClientRect(),\n                    opacity: pullOpacity.current,\n                    duration,\n                });\n            }\n            cleanupPullOffset(() => {\n                setPullOffset(0);\n                setSwipeState(SwipeState.NONE);\n            }, duration);\n            setSwipeState(SwipeState.ANIMATION);\n            if (!cancel) {\n                close();\n            }\n        }\n    };\n    const { prepareAnimation, isAnimationPlaying } = useAnimation(carouselRef, (snapshot, rect, translate) => {\n        var _a;\n        if (carouselRef.current && containerRect && ((_a = state.animation) === null || _a === void 0 ? void 0 : _a.duration)) {\n            const parsedSpacing = parseLengthPercentage(carousel.spacing);\n            const spacingValue = (parsedSpacing.percent ? (parsedSpacing.percent * containerRect.width) / 100 : parsedSpacing.pixel) || 0;\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(${rtl(state.globalIndex - snapshot.index) * (containerRect.width + spacingValue) +\n                            snapshot.rect.x -\n                            rect.x +\n                            translate.x}px, 0)`,\n                    },\n                    { transform: \"translate(0, 0)\" },\n                ],\n                duration: state.animation.duration,\n                easing: state.animation.easing,\n            };\n        }\n        return undefined;\n    });\n    const swipe = useEventCallback((action) => {\n        var _a, _b;\n        const currentSwipeOffset = action.offset || 0;\n        const swipeDuration = !currentSwipeOffset ? ((_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe) : animation.swipe;\n        const swipeEasing = !currentSwipeOffset && !isAnimationPlaying() ? animation.easing.navigation : animation.easing.swipe;\n        let { direction } = action;\n        const count = (_b = action.count) !== null && _b !== void 0 ? _b : 1;\n        let newSwipeState = SwipeState.ANIMATION;\n        let newSwipeAnimationDuration = swipeDuration * count;\n        if (!direction) {\n            const containerWidth = containerRect === null || containerRect === void 0 ? void 0 : containerRect.width;\n            const elapsedTime = action.duration || 0;\n            const expectedTime = containerWidth\n                ? (swipeDuration / containerWidth) * Math.abs(currentSwipeOffset)\n                : swipeDuration;\n            if (count !== 0) {\n                if (elapsedTime < expectedTime) {\n                    newSwipeAnimationDuration =\n                        (newSwipeAnimationDuration / expectedTime) * Math.max(elapsedTime, expectedTime / 5);\n                }\n                else if (containerWidth) {\n                    newSwipeAnimationDuration =\n                        (swipeDuration / containerWidth) * (containerWidth - Math.abs(currentSwipeOffset));\n                }\n                direction = rtl(currentSwipeOffset) > 0 ? ACTION_PREV : ACTION_NEXT;\n            }\n            else {\n                newSwipeAnimationDuration = swipeDuration / 2;\n            }\n        }\n        let increment = 0;\n        if (direction === ACTION_PREV) {\n            if (isSwipeValid(rtl(1))) {\n                increment = -count;\n            }\n            else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        }\n        else if (direction === ACTION_NEXT) {\n            if (isSwipeValid(rtl(-1))) {\n                increment = count;\n            }\n            else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        }\n        newSwipeAnimationDuration = Math.round(newSwipeAnimationDuration);\n        cleanupSwipeOffset(() => {\n            setSwipeOffset(0);\n            setSwipeState(SwipeState.NONE);\n        }, newSwipeAnimationDuration);\n        if (carouselRef.current) {\n            prepareAnimation({\n                rect: carouselRef.current.getBoundingClientRect(),\n                index: state.globalIndex,\n            });\n        }\n        setSwipeState(newSwipeState);\n        publish(ACTION_SWIPE, {\n            type: \"swipe\",\n            increment,\n            duration: newSwipeAnimationDuration,\n            easing: swipeEasing,\n        });\n    });\n    React.useEffect(() => {\n        var _a, _b;\n        if (((_a = state.animation) === null || _a === void 0 ? void 0 : _a.increment) && ((_b = state.animation) === null || _b === void 0 ? void 0 : _b.duration)) {\n            cleanupAnimationIncrement(() => dispatch({ type: \"swipe\", increment: 0 }), state.animation.duration);\n        }\n    }, [state.animation, dispatch, cleanupAnimationIncrement]);\n    const swipeParams = [\n        subscribeSensors,\n        isSwipeValid,\n        (containerRect === null || containerRect === void 0 ? void 0 : containerRect.width) || 0,\n        animation.swipe,\n        () => setSwipeState(SwipeState.SWIPE),\n        (offset) => setSwipeOffset(offset),\n        (offset, duration) => swipe({ offset, duration, count: 1 }),\n        (offset) => swipe({ offset, count: 0 }),\n    ];\n    const pullParams = [\n        () => {\n            if (closeOnPullDown) {\n                setSwipeState(SwipeState.PULL);\n            }\n        },\n        (offset) => setPullOffset(offset),\n        (offset) => pull(offset),\n        (offset) => pull(offset, true),\n    ];\n    usePointerSwipe(controller, ...swipeParams, closeOnPullUp, closeOnPullDown, ...pullParams);\n    useWheelSwipe(swipeState, ...swipeParams);\n    const focusOnMount = useEventCallback(() => {\n        if (controller.focus &&\n            getOwnerDocument().querySelector(`.${cssClass(MODULE_PORTAL)} .${cssClass(cssContainerPrefix())}`)) {\n            focus();\n        }\n    });\n    React.useEffect(focusOnMount, [focusOnMount]);\n    const onViewCallback = useEventCallback(() => {\n        var _a;\n        (_a = on.view) === null || _a === void 0 ? void 0 : _a.call(on, { index: state.currentIndex });\n    });\n    React.useEffect(onViewCallback, [state.globalIndex, onViewCallback]);\n    React.useEffect(() => cleanup(subscribe(ACTION_PREV, (action) => swipe({ direction: ACTION_PREV, ...action })), subscribe(ACTION_NEXT, (action) => swipe({ direction: ACTION_NEXT, ...action })), subscribe(ACTION_SWIPE, (action) => dispatch(action))), [subscribe, swipe, dispatch]);\n    const context = React.useMemo(() => ({\n        prev,\n        next,\n        close,\n        focus,\n        slideRect: containerRect ? computeSlideRect(containerRect, carousel.padding) : { width: 0, height: 0 },\n        containerRect: containerRect || { width: 0, height: 0 },\n        subscribeSensors,\n        containerRef,\n        setCarouselRef,\n        toolbarWidth,\n        setToolbarWidth,\n    }), [\n        prev,\n        next,\n        close,\n        focus,\n        subscribeSensors,\n        containerRect,\n        containerRef,\n        setCarouselRef,\n        toolbarWidth,\n        setToolbarWidth,\n        carousel.padding,\n    ]);\n    React.useImperativeHandle(controller.ref, () => ({\n        prev,\n        next,\n        close,\n        focus,\n        getLightboxProps,\n        getLightboxState,\n    }), [prev, next, close, focus, getLightboxProps, getLightboxState]);\n    return (React.createElement(\"div\", { ref: handleContainerRef, className: clsx(cssClass(cssContainerPrefix()), cssClass(CLASS_FLEX_CENTER)), style: {\n            ...(swipeState === SwipeState.SWIPE\n                ? { [cssVar(\"swipe_offset\")]: `${Math.round(swipeOffset.current)}px` }\n                : null),\n            ...(swipeState === SwipeState.PULL\n                ? {\n                    [cssVar(\"pull_offset\")]: `${Math.round(pullOffset.current)}px`,\n                    [cssVar(\"pull_opacity\")]: `${pullOpacity.current}`,\n                }\n                : null),\n            ...(controller.touchAction !== \"none\" ? { [cssVar(\"controller_touch_action\")]: controller.touchAction } : null),\n            ...styles.container,\n        }, ...(controller.aria ? { role: \"region\", \"aria-live\": \"polite\", \"aria-roledescription\": \"carousel\" } : null), tabIndex: -1, ...registerSensors }, containerRect && (React.createElement(ControllerContext.Provider, { value: context },\n        children, (_a = render.controls) === null || _a === void 0 ? void 0 :\n        _a.call(render)))));\n}\nconst ControllerModule = createModule(MODULE_CONTROLLER, Controller);\n\nfunction cssPrefix$2(value) {\n    return composePrefix(MODULE_CAROUSEL, value);\n}\nfunction cssSlidePrefix(value) {\n    return composePrefix(\"slide\", value);\n}\nfunction CarouselSlide({ slide, offset }) {\n    const containerRef = React.useRef(null);\n    const { currentIndex } = useLightboxState();\n    const { slideRect, close, focus } = useController();\n    const { render, carousel: { imageFit, imageProps }, on: { click: onClick }, controller: { closeOnBackdropClick }, styles: { slide: style }, } = useLightboxProps();\n    const { getOwnerDocument } = useDocumentContext();\n    const offscreen = offset !== 0;\n    React.useEffect(() => {\n        var _a;\n        if (offscreen && ((_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.contains(getOwnerDocument().activeElement))) {\n            focus();\n        }\n    }, [offscreen, focus, getOwnerDocument]);\n    const renderSlide = () => {\n        var _a, _b, _c, _d;\n        let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, { slide, offset, rect: slideRect });\n        if (!rendered && isImageSlide(slide)) {\n            rendered = (React.createElement(ImageSlide, { slide: slide, offset: offset, render: render, rect: slideRect, imageFit: imageFit, imageProps: imageProps, onClick: !offscreen ? () => onClick === null || onClick === void 0 ? void 0 : onClick({ index: currentIndex }) : undefined }));\n        }\n        return rendered ? (React.createElement(React.Fragment, null, (_b = render.slideHeader) === null || _b === void 0 ? void 0 :\n            _b.call(render, { slide }),\n            ((_c = render.slideContainer) !== null && _c !== void 0 ? _c : (({ children }) => children))({ slide, children: rendered }), (_d = render.slideFooter) === null || _d === void 0 ? void 0 :\n            _d.call(render, { slide }))) : null;\n    };\n    const handleBackdropClick = (event) => {\n        const container = containerRef.current;\n        const target = event.target instanceof HTMLElement ? event.target : undefined;\n        if (closeOnBackdropClick &&\n            target &&\n            container &&\n            (target === container ||\n                (Array.from(container.children).find((x) => x === target) &&\n                    target.classList.contains(cssClass(CLASS_SLIDE_WRAPPER))))) {\n            close();\n        }\n    };\n    return (React.createElement(\"div\", { ref: containerRef, className: clsx(cssClass(cssSlidePrefix()), !offscreen && cssClass(cssSlidePrefix(\"current\")), cssClass(CLASS_FLEX_CENTER)), ...makeInertWhen(offscreen), onClick: handleBackdropClick, style: style, role: \"region\", \"aria-roledescription\": \"slide\" }, renderSlide()));\n}\nfunction Placeholder() {\n    const style = useLightboxProps().styles.slide;\n    return React.createElement(\"div\", { className: cssClass(\"slide\"), style: style });\n}\nfunction Carousel({ carousel }) {\n    const { slides, currentIndex, globalIndex } = useLightboxState();\n    const { setCarouselRef } = useController();\n    const spacingValue = parseLengthPercentage(carousel.spacing);\n    const paddingValue = parseLengthPercentage(carousel.padding);\n    const preload = calculatePreload(carousel, slides, 1);\n    const items = [];\n    if (hasSlides(slides)) {\n        for (let index = currentIndex - preload; index <= currentIndex + preload; index += 1) {\n            const slide = getSlide(slides, index);\n            const key = globalIndex - currentIndex + index;\n            const placeholder = carousel.finite && (index < 0 || index > slides.length - 1);\n            items.push(!placeholder\n                ? {\n                    key: [`${key}`, getSlideKey(slide)].filter(Boolean).join(\"|\"),\n                    offset: index - currentIndex,\n                    slide,\n                }\n                : { key });\n        }\n    }\n    return (React.createElement(\"div\", { ref: setCarouselRef, className: clsx(cssClass(cssPrefix$2()), items.length > 0 && cssClass(cssPrefix$2(\"with_slides\"))), style: {\n            [`${cssVar(cssPrefix$2(\"slides_count\"))}`]: items.length,\n            [`${cssVar(cssPrefix$2(\"spacing_px\"))}`]: spacingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"spacing_percent\"))}`]: spacingValue.percent || 0,\n            [`${cssVar(cssPrefix$2(\"padding_px\"))}`]: paddingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"padding_percent\"))}`]: paddingValue.percent || 0,\n        } }, items.map(({ key, slide, offset }) => slide ? React.createElement(CarouselSlide, { key: key, slide: slide, offset: offset }) : React.createElement(Placeholder, { key: key }))));\n}\nconst CarouselModule = createModule(MODULE_CAROUSEL, Carousel);\n\nfunction useNavigationState() {\n    const { carousel } = useLightboxProps();\n    const { slides, currentIndex } = useLightboxState();\n    const prevDisabled = slides.length === 0 || (carousel.finite && currentIndex === 0);\n    const nextDisabled = slides.length === 0 || (carousel.finite && currentIndex === slides.length - 1);\n    return { prevDisabled, nextDisabled };\n}\n\nfunction useKeyboardNavigation(subscribeSensors) {\n    var _a;\n    const isRTL = useRTL();\n    const { publish } = useEvents();\n    const { animation } = useLightboxProps();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    const throttle = ((_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe) / 2;\n    const prev = useThrottle(() => publish(ACTION_PREV), throttle);\n    const next = useThrottle(() => publish(ACTION_NEXT), throttle);\n    const handleKeyDown = useEventCallback((event) => {\n        switch (event.key) {\n            case VK_ESCAPE:\n                publish(ACTION_CLOSE);\n                break;\n            case VK_ARROW_LEFT:\n                if (!(isRTL ? nextDisabled : prevDisabled))\n                    (isRTL ? next : prev)();\n                break;\n            case VK_ARROW_RIGHT:\n                if (!(isRTL ? prevDisabled : nextDisabled))\n                    (isRTL ? prev : next)();\n                break;\n            default:\n        }\n    });\n    React.useEffect(() => subscribeSensors(EVENT_ON_KEY_DOWN, handleKeyDown), [subscribeSensors, handleKeyDown]);\n}\n\nfunction NavigationButton({ label, icon, renderIcon, action, onClick, disabled, style }) {\n    return (React.createElement(IconButton, { label: label, icon: icon, renderIcon: renderIcon, className: cssClass(`navigation_${action}`), disabled: disabled, onClick: onClick, style: style, ...useLoseFocus(useController().focus, disabled) }));\n}\nfunction Navigation({ render: { buttonPrev, buttonNext, iconPrev, iconNext }, styles }) {\n    const { prev, next, subscribeSensors } = useController();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    useKeyboardNavigation(subscribeSensors);\n    return (React.createElement(React.Fragment, null,\n        buttonPrev ? (buttonPrev()) : (React.createElement(NavigationButton, { label: \"Previous\", action: ACTION_PREV, icon: PreviousIcon, renderIcon: iconPrev, style: styles.navigationPrev, disabled: prevDisabled, onClick: prev })),\n        buttonNext ? (buttonNext()) : (React.createElement(NavigationButton, { label: \"Next\", action: ACTION_NEXT, icon: NextIcon, renderIcon: iconNext, style: styles.navigationNext, disabled: nextDisabled, onClick: next }))));\n}\nconst NavigationModule = createModule(MODULE_NAVIGATION, Navigation);\n\nconst noScroll = cssClass(CLASS_NO_SCROLL);\nconst noScrollPadding = cssClass(CLASS_NO_SCROLL_PADDING);\nfunction isHTMLElement(element) {\n    return \"style\" in element;\n}\nfunction padScrollbar(element, padding, rtl) {\n    const styles = window.getComputedStyle(element);\n    const property = rtl ? \"padding-left\" : \"padding-right\";\n    const computedValue = rtl ? styles.paddingLeft : styles.paddingRight;\n    const originalValue = element.style.getPropertyValue(property);\n    element.style.setProperty(property, `${(parseInt(computedValue) || 0) + padding}px`);\n    return () => {\n        if (originalValue) {\n            element.style.setProperty(property, originalValue);\n        }\n        else {\n            element.style.removeProperty(property);\n        }\n    };\n}\nfunction NoScroll({ noScroll: { disabled }, children }) {\n    const rtl = useRTL();\n    const { getOwnerDocument, getOwnerWindow } = useDocumentContext();\n    React.useEffect(() => {\n        if (disabled)\n            return () => { };\n        const cleanup = [];\n        const ownerWindow = getOwnerWindow();\n        const { body, documentElement } = getOwnerDocument();\n        const scrollbar = Math.round(ownerWindow.innerWidth - documentElement.clientWidth);\n        if (scrollbar > 0) {\n            cleanup.push(padScrollbar(body, scrollbar, rtl));\n            const elements = body.getElementsByTagName(\"*\");\n            for (let i = 0; i < elements.length; i += 1) {\n                const element = elements[i];\n                if (isHTMLElement(element) &&\n                    ownerWindow.getComputedStyle(element).getPropertyValue(\"position\") === \"fixed\" &&\n                    !element.classList.contains(noScrollPadding)) {\n                    cleanup.push(padScrollbar(element, scrollbar, rtl));\n                }\n            }\n        }\n        body.classList.add(noScroll);\n        return () => {\n            body.classList.remove(noScroll);\n            cleanup.forEach((clean) => clean());\n        };\n    }, [rtl, disabled, getOwnerDocument, getOwnerWindow]);\n    return React.createElement(React.Fragment, null, children);\n}\nconst NoScrollModule = createModule(MODULE_NO_SCROLL, NoScroll);\n\nfunction cssPrefix$1(value) {\n    return composePrefix(MODULE_PORTAL, value);\n}\nfunction setAttribute(element, attribute, value) {\n    const previousValue = element.getAttribute(attribute);\n    element.setAttribute(attribute, value);\n    return () => {\n        if (previousValue) {\n            element.setAttribute(attribute, previousValue);\n        }\n        else {\n            element.removeAttribute(attribute);\n        }\n    };\n}\nfunction Portal({ children, animation, styles, className, on, portal, close }) {\n    const [mounted, setMounted] = React.useState(false);\n    const [visible, setVisible] = React.useState(false);\n    const cleanup = React.useRef([]);\n    const restoreFocus = React.useRef(null);\n    const { setTimeout } = useTimeouts();\n    const { subscribe } = useEvents();\n    const reduceMotion = useMotionPreference();\n    const animationDuration = !reduceMotion ? animation.fade : 0;\n    React.useEffect(() => {\n        setMounted(true);\n        return () => {\n            setMounted(false);\n            setVisible(false);\n        };\n    }, []);\n    const handleCleanup = useEventCallback(() => {\n        cleanup.current.forEach((clean) => clean());\n        cleanup.current = [];\n    });\n    const handleClose = useEventCallback(() => {\n        var _a;\n        setVisible(false);\n        handleCleanup();\n        (_a = on.exiting) === null || _a === void 0 ? void 0 : _a.call(on);\n        setTimeout(() => {\n            var _a;\n            (_a = on.exited) === null || _a === void 0 ? void 0 : _a.call(on);\n            close();\n        }, animationDuration);\n    });\n    React.useEffect(() => subscribe(ACTION_CLOSE, handleClose), [subscribe, handleClose]);\n    const handleEnter = useEventCallback((node) => {\n        var _a, _b, _c;\n        node.scrollTop;\n        setVisible(true);\n        (_a = on.entering) === null || _a === void 0 ? void 0 : _a.call(on);\n        const elements = (_c = (_b = node.parentNode) === null || _b === void 0 ? void 0 : _b.children) !== null && _c !== void 0 ? _c : [];\n        for (let i = 0; i < elements.length; i += 1) {\n            const element = elements[i];\n            if ([\"TEMPLATE\", \"SCRIPT\", \"STYLE\"].indexOf(element.tagName) === -1 && element !== node) {\n                cleanup.current.push(setAttribute(element, \"inert\", \"\"));\n                cleanup.current.push(setAttribute(element, \"aria-hidden\", \"true\"));\n            }\n        }\n        cleanup.current.push(() => {\n            var _a, _b;\n            (_b = (_a = restoreFocus.current) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n        setTimeout(() => {\n            var _a;\n            (_a = on.entered) === null || _a === void 0 ? void 0 : _a.call(on);\n        }, animationDuration);\n    });\n    const handleRef = React.useCallback((node) => {\n        if (node) {\n            handleEnter(node);\n        }\n        else {\n            handleCleanup();\n        }\n    }, [handleEnter, handleCleanup]);\n    return mounted\n        ? createPortal(React.createElement(LightboxRoot, { ref: handleRef, className: clsx(className, cssClass(cssPrefix$1()), cssClass(CLASS_NO_SCROLL_PADDING), visible && cssClass(cssPrefix$1(\"open\"))), \"aria-modal\": true, role: \"dialog\", \"aria-live\": \"polite\", \"aria-roledescription\": \"lightbox\", style: {\n                ...(animation.fade !== LightboxDefaultProps.animation.fade\n                    ? { [cssVar(\"fade_animation_duration\")]: `${animationDuration}ms` }\n                    : null),\n                ...(animation.easing.fade !== LightboxDefaultProps.animation.easing.fade\n                    ? { [cssVar(\"fade_animation_timing_function\")]: animation.easing.fade }\n                    : null),\n                ...styles.root,\n            }, onFocus: (event) => {\n                if (!restoreFocus.current) {\n                    restoreFocus.current = event.relatedTarget;\n                }\n            } }, children), portal.root || document.body)\n        : null;\n}\nconst PortalModule = createModule(MODULE_PORTAL, Portal);\n\nfunction Root({ children }) {\n    return React.createElement(React.Fragment, null, children);\n}\nconst RootModule = createModule(MODULE_ROOT, Root);\n\nfunction cssPrefix(value) {\n    return composePrefix(MODULE_TOOLBAR, value);\n}\nfunction Toolbar({ toolbar: { buttons }, render: { buttonClose, iconClose }, styles }) {\n    const { close, setToolbarWidth } = useController();\n    const { setContainerRef, containerRect } = useContainerRect();\n    useLayoutEffect(() => {\n        setToolbarWidth(containerRect === null || containerRect === void 0 ? void 0 : containerRect.width);\n    }, [setToolbarWidth, containerRect === null || containerRect === void 0 ? void 0 : containerRect.width]);\n    const renderCloseButton = () => {\n        if (buttonClose)\n            return buttonClose();\n        return React.createElement(IconButton, { key: ACTION_CLOSE, label: \"Close\", icon: CloseIcon, renderIcon: iconClose, onClick: close });\n    };\n    return (React.createElement(\"div\", { ref: setContainerRef, style: styles.toolbar, className: cssClass(cssPrefix()) }, buttons === null || buttons === void 0 ? void 0 : buttons.map((button) => (button === ACTION_CLOSE ? renderCloseButton() : button))));\n}\nconst ToolbarModule = createModule(MODULE_TOOLBAR, Toolbar);\n\nfunction renderNode(node, props) {\n    var _a;\n    return React.createElement(node.module.component, { key: node.module.name, ...props }, (_a = node.children) === null || _a === void 0 ? void 0 : _a.map((child) => renderNode(child, props)));\n}\nfunction mergeAnimation(defaultAnimation, animation = {}) {\n    const { easing: defaultAnimationEasing, ...restDefaultAnimation } = defaultAnimation;\n    const { easing, ...restAnimation } = animation;\n    return {\n        easing: { ...defaultAnimationEasing, ...easing },\n        ...restDefaultAnimation,\n        ...restAnimation,\n    };\n}\nfunction Lightbox({ carousel, animation, render, toolbar, controller, noScroll, on, plugins, slides, index, ...restProps }) {\n    const { animation: defaultAnimation, carousel: defaultCarousel, render: defaultRender, toolbar: defaultToolbar, controller: defaultController, noScroll: defaultNoScroll, on: defaultOn, slides: defaultSlides, index: defaultIndex, plugins: defaultPlugins, ...restDefaultProps } = LightboxDefaultProps;\n    const { config, augmentation } = withPlugins([\n        createNode(PortalModule, [\n            createNode(NoScrollModule, [\n                createNode(ControllerModule, [\n                    createNode(CarouselModule),\n                    createNode(ToolbarModule),\n                    createNode(NavigationModule),\n                ]),\n            ]),\n        ]),\n    ], plugins || defaultPlugins);\n    const props = augmentation({\n        animation: mergeAnimation(defaultAnimation, animation),\n        carousel: { ...defaultCarousel, ...carousel },\n        render: { ...defaultRender, ...render },\n        toolbar: { ...defaultToolbar, ...toolbar },\n        controller: { ...defaultController, ...controller },\n        noScroll: { ...defaultNoScroll, ...noScroll },\n        on: { ...defaultOn, ...on },\n        ...restDefaultProps,\n        ...restProps,\n    });\n    if (!props.open)\n        return null;\n    return (React.createElement(LightboxPropsProvider, { ...props },\n        React.createElement(LightboxStateProvider, { slides: slides || defaultSlides, index: parseInt(index || defaultIndex) },\n            React.createElement(TimeoutsProvider, null,\n                React.createElement(EventsProvider, null, renderNode(createNode(RootModule, config), props))))));\n}\n\nexport { ACTION_CLOSE, ACTION_NEXT, ACTION_PREV, ACTION_SWIPE, CLASS_FLEX_CENTER, CLASS_NO_SCROLL, CLASS_NO_SCROLL_PADDING, CLASS_SLIDE_WRAPPER, Carousel, CarouselModule, CloseIcon, Controller, ControllerContext, ControllerModule, DocumentContext, DocumentContextProvider, ELEMENT_BUTTON, ELEMENT_ICON, EVENT_ON_KEY_DOWN, EVENT_ON_KEY_UP, EVENT_ON_POINTER_CANCEL, EVENT_ON_POINTER_DOWN, EVENT_ON_POINTER_LEAVE, EVENT_ON_POINTER_MOVE, EVENT_ON_POINTER_UP, EVENT_ON_WHEEL, ErrorIcon, EventsContext, EventsProvider, IMAGE_FIT_CONTAIN, IMAGE_FIT_COVER, IconButton, ImageSlide, Lightbox, LightboxDefaultProps, LightboxDispatchContext, LightboxPropsContext, LightboxPropsProvider, LightboxRoot, LightboxStateContext, LightboxStateProvider, LoadingIcon, MODULE_CAROUSEL, MODULE_CONTROLLER, MODULE_NAVIGATION, MODULE_NO_SCROLL, MODULE_PORTAL, MODULE_ROOT, MODULE_TOOLBAR, Navigation, NavigationButton, NavigationModule, NextIcon, NoScroll, NoScrollModule, Portal, PortalModule, PreviousIcon, Root, RootModule, SLIDE_STATUS_COMPLETE, SLIDE_STATUS_ERROR, SLIDE_STATUS_LOADING, SLIDE_STATUS_PLACEHOLDER, SwipeState, TimeoutsContext, TimeoutsProvider, Toolbar, ToolbarModule, UNKNOWN_ACTION_TYPE, VK_ARROW_LEFT, VK_ARROW_RIGHT, VK_ESCAPE, activeSlideStatus, addToolbarButton, calculatePreload, cleanup, clsx, composePrefix, computeSlideRect, createIcon, createIconDisabled, createModule, createNode, cssClass, cssVar, Lightbox as default, devicePixelRatio, getSlide, getSlideIfPresent, getSlideIndex, getSlideKey, hasSlides, hasWindow, isImageFitCover, isImageSlide, label, makeComposePrefix, makeInertWhen, makeUseContext, parseInt, parseLengthPercentage, round, setRef, stopNavigationEventsPropagation, useAnimation, useContainerRect, useController, useDelay, useDocumentContext, useEventCallback, useEvents, useForkRef, useKeyboardNavigation, useLayoutEffect, useLightboxDispatch, useLightboxProps, useLightboxState, useLoseFocus, useMotionPreference, useNavigationState, usePointerEvents, usePointerSwipe, usePreventWheelDefaults, useRTL, useSensors, useThrottle, useTimeouts, useWheelSwipe, withPlugins };", "const MODULE_CAROUSEL = \"carousel\";\nconst MODULE_CONTROLLER = \"controller\";\nconst MODULE_NAVIGATION = \"navigation\";\nconst MODULE_NO_SCROLL = \"no-scroll\";\nconst MODULE_PORTAL = \"portal\";\nconst MODULE_ROOT = \"root\";\nconst MODULE_TOOLBAR = \"toolbar\";\nconst PLUGIN_CAPTIONS = \"captions\";\nconst PLUGIN_COUNTER = \"counter\";\nconst PLUGIN_DOWNLOAD = \"download\";\nconst PLUGIN_FULLSCREEN = \"fullscreen\";\nconst PLUGIN_INLINE = \"inline\";\nconst PLUGIN_SHARE = \"share\";\nconst PLUGIN_SLIDESHOW = \"slideshow\";\nconst PLUGIN_THUMBNAILS = \"thumbnails\";\nconst PLUGIN_ZOOM = \"zoom\";\nconst SLIDE_STATUS_LOADING = \"loading\";\nconst SLIDE_STATUS_PLAYING = \"playing\";\nconst SLIDE_STATUS_ERROR = \"error\";\nconst SLIDE_STATUS_COMPLETE = \"complete\";\nconst SLIDE_STATUS_PLACEHOLDER = \"placeholder\";\nconst activeSlideStatus = (status) => `active-slide-${status}`;\nconst ACTIVE_SLIDE_LOADING = activeSlideStatus(SLIDE_STATUS_LOADING);\nconst ACTIVE_SLIDE_PLAYING = activeSlideStatus(SLIDE_STATUS_PLAYING);\nconst ACTIVE_SLIDE_ERROR = activeSlideStatus(SLIDE_STATUS_ERROR);\nconst ACTIVE_SLIDE_COMPLETE = activeSlideStatus(SLIDE_STATUS_COMPLETE);\nconst CLASS_FULLSIZE = \"fullsize\";\nconst CLASS_FLEX_CENTER = \"flex_center\";\nconst CLASS_NO_SCROLL = \"no_scroll\";\nconst CLASS_NO_SCROLL_PADDING = \"no_scroll_padding\";\nconst CLASS_SLIDE_WRAPPER = \"slide_wrapper\";\nconst CLASS_SLIDE_WRAPPER_INTERACTIVE = \"slide_wrapper_interactive\";\nconst ACTION_PREV = \"prev\";\nconst ACTION_NEXT = \"next\";\nconst ACTION_SWIPE = \"swipe\";\nconst ACTION_CLOSE = \"close\";\nconst EVENT_ON_POINTER_DOWN = \"onPointerDown\";\nconst EVENT_ON_POINTER_MOVE = \"onPointerMove\";\nconst EVENT_ON_POINTER_UP = \"onPointerUp\";\nconst EVENT_ON_POINTER_LEAVE = \"onPointerLeave\";\nconst EVENT_ON_POINTER_CANCEL = \"onPointerCancel\";\nconst EVENT_ON_KEY_DOWN = \"onKeyDown\";\nconst EVENT_ON_KEY_UP = \"onKeyUp\";\nconst EVENT_ON_WHEEL = \"onWheel\";\nconst VK_ESCAPE = \"Escape\";\nconst VK_ARROW_LEFT = \"ArrowLeft\";\nconst VK_ARROW_RIGHT = \"ArrowRight\";\nconst ELEMENT_BUTTON = \"button\";\nconst ELEMENT_ICON = \"icon\";\nconst IMAGE_FIT_CONTAIN = \"contain\";\nconst IMAGE_FIT_COVER = \"cover\";\nconst UNKNOWN_ACTION_TYPE = \"Unknown action type\";\n\nexport { ACTION_CLOSE, ACTION_NEXT, ACTION_PREV, ACTION_SWIPE, ACTIVE_SLIDE_COMPLETE, ACTIVE_SLIDE_ERROR, ACTIVE_SLIDE_LOADING, ACTIVE_SLIDE_PLAYING, CLASS_FLEX_CENTER, CLASS_FULLSIZE, CLASS_NO_SCROLL, CLASS_NO_SCROLL_PADDING, CLASS_SLIDE_WRAPPER, CLASS_SLIDE_WRAPPER_INTERACTIVE, ELEMENT_BUTTON, ELEMENT_ICON, EVENT_ON_KEY_DOWN, EVENT_ON_KEY_UP, EVENT_ON_POINTER_CANCEL, EVENT_ON_POINTER_DOWN, EVENT_ON_POINTER_LEAVE, EVENT_ON_POINTER_MOVE, EVENT_ON_POINTER_UP, EVENT_ON_WHEEL, IMAGE_FIT_CONTAIN, IMAGE_FIT_COVER, MODULE_CAROUSEL, MODULE_CONTROLLER, MODULE_NAVIGATION, MODULE_NO_SCROLL, MODULE_PORTAL, MODULE_ROOT, MODULE_TOOLBAR, PLUGIN_CAPTIONS, PLUGIN_COUNTER, PLUGIN_DOWNLOAD, PLUGIN_FULLSCREEN, PLUGIN_INLINE, PLUGIN_SHARE, PLUGIN_SLIDESHOW, PLUGIN_THUMBNAILS, PLUGIN_ZOOM, SLIDE_STATUS_COMPLETE, SLIDE_STATUS_ERROR, SLIDE_STATUS_LOADING, SLIDE_STATUS_PLACEHOLDER, SLIDE_STATUS_PLAYING, UNKNOWN_ACTION_TYPE, VK_ARROW_LEFT, VK_ARROW_RIGHT, VK_ESCAPE, activeSlideStatus };\n"], "mappings": ";;;;;;;;;;;;AACA,YAAuB;;;ACDvB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB;AACtB,IAAM,eAAe;AACrB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,uBAAuB;AAC7B,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;AAC3B,IAAM,wBAAwB;AAC9B,IAAM,2BAA2B;AACjC,IAAM,oBAAoB,CAAC,WAAW,gBAAgB,MAAM;AAC5D,IAAM,uBAAuB,kBAAkB,oBAAoB;AACnE,IAAM,uBAAuB,kBAAkB,oBAAoB;AACnE,IAAM,qBAAqB,kBAAkB,kBAAkB;AAC/D,IAAM,wBAAwB,kBAAkB,qBAAqB;AACrE,IAAM,iBAAiB;AACvB,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB;AACxB,IAAM,0BAA0B;AAChC,IAAM,sBAAsB;AAC5B,IAAM,kCAAkC;AACxC,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB;AAC9B,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,0BAA0B;AAChC,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AACvB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB;AACxB,IAAM,sBAAsB;;;ADhD5B,uBAA6B;AAG7B,IAAM,cAAc;AACpB,SAAS,QAAQ,SAAS;AACtB,SAAO,CAAC,GAAG,OAAO,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAChD;AACA,SAAS,SAAS,MAAM;AACpB,SAAO,GAAG,WAAW,GAAG,IAAI;AAChC;AACA,SAAS,OAAO,MAAM;AAClB,SAAO,KAAK,WAAW,GAAG,IAAI;AAClC;AACA,SAAS,cAAc,MAAM,QAAQ;AACjC,SAAO,GAAG,IAAI,GAAG,SAAS,IAAI,MAAM,KAAK,EAAE;AAC/C;AACA,SAAS,kBAAkB,MAAM;AAC7B,SAAO,CAAC,WAAW,cAAc,MAAM,MAAM;AACjD;AACA,SAAS,MAAM,QAAQ,cAAc;AACjC,MAAI;AACJ,UAAQ,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK;AACxH;AACA,SAAS,WAAW,UAAU;AAC1B,SAAO,MAAM;AACT,aAAS,QAAQ,CAAC,YAAY;AAC1B,cAAQ;AAAA,IACZ,CAAC;AAAA,EACL;AACJ;AACA,SAAS,eAAe,MAAM,aAAa,SAAS;AAChD,SAAO,MAAM;AACT,UAAM,MAAY,iBAAW,OAAO;AACpC,QAAI,CAAC,KAAK;AACN,YAAM,IAAI,MAAM,GAAG,IAAI,0BAA0B,WAAW,WAAW;AAAA,IAC3E;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,YAAY;AACjB,SAAO,OAAO,WAAW;AAC7B;AACA,SAAS,MAAM,OAAO,WAAW,GAAG;AAChC,QAAM,SAAS,MAAM;AACrB,SAAO,KAAK,OAAO,QAAQ,OAAO,WAAW,MAAM,IAAI;AAC3D;AACA,SAAS,aAAa,OAAO;AACzB,SAAO,MAAM,SAAS,UAAa,MAAM,SAAS;AACtD;AACA,SAAS,gBAAgB,OAAO,UAAU;AACtC,SAAO,MAAM,aAAa,mBAAoB,MAAM,aAAa,qBAAqB,aAAa;AACvG;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,WAAW,OAAO,SAAS,OAAO,EAAE,IAAI;AACpE;AACA,SAAS,sBAAsB,OAAO;AAClC,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,EAAE,OAAO,MAAM;AAAA,EAC1B;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,QAAQ,SAAS,KAAK;AAC5B,WAAO,MAAM,SAAS,GAAG,IAAI,EAAE,SAAS,MAAM,IAAI,EAAE,OAAO,MAAM;AAAA,EACrE;AACA,SAAO,EAAE,OAAO,EAAE;AACtB;AACA,SAAS,iBAAiB,eAAe,SAAS;AAC9C,QAAM,eAAe,sBAAsB,OAAO;AAClD,QAAM,gBAAgB,aAAa,YAAY,SAAa,cAAc,QAAQ,MAAO,aAAa,UAAU,aAAa;AAC7H,SAAO;AAAA,IACH,OAAO,KAAK,IAAI,cAAc,QAAQ,IAAI,eAAe,CAAC;AAAA,IAC1D,QAAQ,KAAK,IAAI,cAAc,SAAS,IAAI,eAAe,CAAC;AAAA,EAChE;AACJ;AACA,SAAS,mBAAmB;AACxB,UAAQ,UAAU,IAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,mBAAmB,WAAc;AAClH;AACA,SAAS,cAAc,OAAO,aAAa;AACvC,SAAO,cAAc,KAAM,QAAQ,cAAe,eAAe,cAAc;AACnF;AACA,SAAS,UAAU,QAAQ;AACvB,SAAO,OAAO,SAAS;AAC3B;AACA,SAAS,SAAS,QAAQ,OAAO;AAC7B,SAAO,OAAO,cAAc,OAAO,OAAO,MAAM,CAAC;AACrD;AACA,SAAS,kBAAkB,QAAQ,OAAO;AACtC,SAAO,UAAU,MAAM,IAAI,SAAS,QAAQ,KAAK,IAAI;AACzD;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,aAAa,KAAK,IAAI,MAAM,MAAM;AAC7C;AACA,SAAS,iBAAiB,SAAS,KAAK,QAAQ;AAC5C,MAAI,CAAC;AACD,WAAO;AACX,QAAM,EAAE,SAAS,GAAG,YAAY,IAAI;AACpC,QAAM,QAAQ,QAAQ,UAAU,CAAC,SAAS,SAAS,GAAG;AACtD,QAAM,gBAAsB,qBAAe,MAAM,IAAU,mBAAa,QAAQ,EAAE,IAAI,GAAG,IAAI,IAAI;AACjG,MAAI,SAAS,GAAG;AACZ,UAAM,SAAS,CAAC,GAAG,OAAO;AAC1B,WAAO,OAAO,OAAO,GAAG,aAAa;AACrC,WAAO,EAAE,SAAS,QAAQ,GAAG,YAAY;AAAA,EAC7C;AACA,SAAO,EAAE,SAAS,CAAC,eAAe,GAAG,OAAO,GAAG,GAAG,YAAY;AAClE;AACA,SAAS,kCAAkC;AACvC,QAAM,kBAAkB,CAAC,UAAU;AAC/B,UAAM,gBAAgB;AAAA,EAC1B;AACA,SAAO,EAAE,eAAe,iBAAiB,WAAW,iBAAiB,SAAS,gBAAgB;AAClG;AACA,SAAS,iBAAiB,UAAU,QAAQ,UAAU,GAAG;AACrD,SAAO,KAAK,IAAI,SAAS,SAAS,KAAK,IAAI,SAAS,SAAS,OAAO,SAAS,IAAI,KAAK,MAAM,OAAO,SAAS,CAAC,GAAG,OAAO,CAAC;AAC5H;AACA,IAAM,YAAY,OAAa,cAAQ,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK;AACzD,SAAS,cAAc,WAAW;AAC9B,QAAM,cAAc,YAAY,KAAK;AACrC,SAAO,EAAE,OAAO,YAAY,YAAY,YAAY;AACxD;AAEA,IAAM,uBAAuB;AAAA,EACzB,MAAM;AAAA,EACN,OAAO,MAAM;AAAA,EAAE;AAAA,EACf,OAAO;AAAA,EACP,QAAQ,CAAC;AAAA,EACT,QAAQ,CAAC;AAAA,EACT,SAAS,CAAC;AAAA,EACV,SAAS,EAAE,SAAS,CAAC,YAAY,EAAE;AAAA,EACnC,QAAQ,CAAC;AAAA,EACT,WAAW;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,YAAY,CAAC;AAAA,EACjB;AAAA,EACA,YAAY;AAAA,IACR,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,EAC5B;AAAA,EACA,QAAQ,CAAC;AAAA,EACT,UAAU;AAAA,IACN,UAAU;AAAA,EACd;AAAA,EACA,IAAI,CAAC;AAAA,EACL,QAAQ,CAAC;AAAA,EACT,WAAW;AACf;AAEA,SAAS,aAAa,MAAM,WAAW;AACnC,SAAO,EAAE,MAAM,UAAU;AAC7B;AACA,SAAS,WAAW,QAAQ,UAAU;AAClC,SAAO,EAAE,QAAQ,SAAS;AAC9B;AACA,SAAS,aAAa,MAAM,QAAQ,OAAO;AACvC,MAAI,KAAK,OAAO,SAAS,QAAQ;AAC7B,WAAO,MAAM,IAAI;AAAA,EACrB;AACA,MAAI,KAAK,UAAU;AACf,WAAO;AAAA,MACH,WAAW,KAAK,QAAQ,KAAK,SAAS,QAAQ,CAAC,MAAM;AAAE,YAAI;AAAI,gBAAQ,KAAK,aAAa,GAAG,QAAQ,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,MAAG,CAAC,CAAC;AAAA,IACvJ;AAAA,EACJ;AACA,SAAO,CAAC,IAAI;AAChB;AACA,SAAS,SAAS,OAAO,QAAQ,OAAO;AACpC,SAAO,MAAM,QAAQ,CAAC,SAAS;AAAE,QAAI;AAAI,YAAQ,KAAK,aAAa,MAAM,QAAQ,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,EAAG,CAAC;AACnI;AACA,SAAS,YAAY,MAAM,UAAU,CAAC,GAAG,gBAAgB,CAAC,GAAG;AACzD,MAAI,SAAS;AACb,QAAM,WAAW,CAAC,WAAW;AACzB,UAAM,QAAQ,CAAC,GAAG,MAAM;AACxB,WAAO,MAAM,SAAS,GAAG;AACrB,YAAM,OAAO,MAAM,IAAI;AACvB,WAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,OAAO,UAAU;AACnE,eAAO;AACX,UAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AACjD,cAAM,KAAK,GAAG,KAAK,QAAQ;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AACA,QAAM,YAAY,CAAC,QAAQ,WAAW;AAClC,QAAI,WAAW,IAAI;AACf,eAAS,CAAC,WAAW,QAAQ,MAAM,CAAC;AACpC;AAAA,IACJ;AACA,aAAS,SAAS,QAAQ,QAAQ,CAAC,SAAS,CAAC,WAAW,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAAA,EAC5E;AACA,QAAM,SAAS,CAAC,QAAQ,WAAW;AAC/B,aAAS,SAAS,QAAQ,QAAQ,CAAC,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,WAAW,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,EAC9G;AACA,QAAM,WAAW,CAAC,QAAQ,QAAQ,YAAY;AAC1C,aAAS,SAAS,QAAQ,QAAQ,CAAC,SAAS;AACxC,UAAI;AACJ,aAAO;AAAA,QACH,WAAW,KAAK,QAAQ;AAAA,UACpB,GAAI,UAAU,CAAC,WAAW,MAAM,CAAC,IAAI,CAAC;AAAA,UACtC,IAAK,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,UAC3D,GAAI,CAAC,UAAU,CAAC,WAAW,MAAM,CAAC,IAAI,CAAC;AAAA,QAC3C,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AACA,QAAM,aAAa,CAAC,QAAQ,QAAQ,YAAY;AAC5C,aAAS,SAAS,QAAQ,QAAQ,CAAC,SAAS;AAAA,MACxC,GAAI,UAAU,CAAC,WAAW,MAAM,CAAC,IAAI,CAAC;AAAA,MACtC;AAAA,MACA,GAAI,CAAC,UAAU,CAAC,WAAW,MAAM,CAAC,IAAI,CAAC;AAAA,IAC3C,CAAC;AAAA,EACL;AACA,QAAM,YAAY,CAAC,WAAW;AAC1B,WAAO,mBAAmB,MAAM;AAAA,EACpC;AACA,QAAM,UAAU,CAAC,QAAQ,WAAW;AAChC,aAAS,SAAS,QAAQ,QAAQ,CAAC,SAAS,CAAC,WAAW,QAAQ,KAAK,QAAQ,CAAC,CAAC;AAAA,EACnF;AACA,QAAM,SAAS,CAAC,WAAW;AACvB,aAAS,SAAS,QAAQ,QAAQ,CAAC,SAAS,KAAK,QAAQ;AAAA,EAC7D;AACA,QAAM,UAAU,CAAC,iBAAiB;AAC9B,kBAAc,KAAK,YAAY;AAAA,EACnC;AACA,UAAQ,QAAQ,CAAC,WAAW;AACxB,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA,cAAc,CAAC,UAAU,cAAc,OAAO,CAAC,KAAK,iBAAiB,aAAa,GAAG,GAAG,KAAK;AAAA,EACjG;AACJ;AAEA,IAAM,kBAAwB,oBAAc,IAAI;AAChD,IAAM,qBAAqB,eAAe,eAAe,mBAAmB,eAAe;AAC3F,SAAS,wBAAwB,EAAE,SAAS,SAAS,GAAG;AACpD,QAAM,UAAgB,cAAQ,MAAM;AAChC,UAAM,mBAAmB,CAAC,SAAS;AAAE,UAAI;AAAI,eAAS,KAAM,QAAQ,QAAQ,aAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB;AAAA,IAAU;AAC1J,UAAM,iBAAiB,CAAC,SAAS;AAAE,UAAI;AAAI,eAAS,KAAK,iBAAiB,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,IAAQ;AACjJ,WAAO,EAAE,kBAAkB,eAAe;AAAA,EAC9C,GAAG,CAAC,OAAO,CAAC;AACZ,SAAa,oBAAc,gBAAgB,UAAU,EAAE,OAAO,QAAQ,GAAG,QAAQ;AACrF;AAEA,IAAM,gBAAsB,oBAAc,IAAI;AAC9C,IAAM,YAAY,eAAe,aAAa,iBAAiB,aAAa;AAC5E,SAAS,eAAe,EAAE,SAAS,GAAG;AAClC,QAAM,CAAC,aAAa,IAAU,eAAS,CAAC,CAAC;AACzC,EAAM,gBAAU,MAAM,MAAM;AACxB,WAAO,KAAK,aAAa,EAAE,QAAQ,CAAC,UAAU,OAAO,cAAc,KAAK,CAAC;AAAA,EAC7E,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,UAAgB,cAAQ,MAAM;AAChC,UAAM,cAAc,CAAC,OAAO,aAAa;AACrC,UAAI;AACJ,OAAC,KAAK,cAAc,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,GAAG,cAAc,KAAK,EAAE,QAAQ,GAAG,cAAc,KAAK,EAAE,OAAO,CAAC,OAAO,OAAO,QAAQ,CAAC;AAAA,IACtK;AACA,UAAM,YAAY,CAAC,OAAO,aAAa;AACnC,UAAI,CAAC,cAAc,KAAK,GAAG;AACvB,sBAAc,KAAK,IAAI,CAAC;AAAA,MAC5B;AACA,oBAAc,KAAK,EAAE,KAAK,QAAQ;AAClC,aAAO,MAAM,YAAY,OAAO,QAAQ;AAAA,IAC5C;AACA,UAAM,UAAU,IAAI,CAAC,OAAO,KAAK,MAAM;AACnC,UAAI;AACJ,OAAC,KAAK,cAAc,KAAK,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC;AAAA,IAC7G;AACA,WAAO,EAAE,SAAS,WAAW,YAAY;AAAA,EAC7C,GAAG,CAAC,aAAa,CAAC;AAClB,SAAa,oBAAc,cAAc,UAAU,EAAE,OAAO,QAAQ,GAAG,QAAQ;AACnF;AAEA,IAAM,uBAA6B,oBAAc,IAAI;AACrD,IAAM,mBAAmB,eAAe,oBAAoB,wBAAwB,oBAAoB;AACxG,SAAS,sBAAsB,EAAE,UAAU,GAAG,MAAM,GAAG;AACnD,SAAa,oBAAc,qBAAqB,UAAU,EAAE,OAAO,MAAM,GAAG,QAAQ;AACxF;AAEA,IAAM,uBAA6B,oBAAc,IAAI;AACrD,IAAM,mBAAmB,eAAe,oBAAoB,wBAAwB,oBAAoB;AACxG,IAAM,0BAAgC,oBAAc,IAAI;AACxD,IAAM,sBAAsB,eAAe,uBAAuB,2BAA2B,uBAAuB;AACpH,SAAS,QAAQ,OAAO,QAAQ;AAC5B,UAAQ,OAAO,MAAM;AAAA,IACjB,KAAK,SAAS;AACV,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,aAAa,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,cAAc;AACxF,YAAM,cAAc,MAAM,cAAc;AACxC,YAAM,eAAe,cAAc,aAAa,OAAO,MAAM;AAC7D,YAAM,eAAe,kBAAkB,QAAQ,YAAY;AAC3D,YAAM,YAAY,aAAa,OAAO,aAAa,SAC7C;AAAA,QACE;AAAA,QACA,UAAU,OAAO;AAAA,QACjB,QAAQ,OAAO;AAAA,MACnB,IACE;AACN,aAAO,EAAE,QAAQ,cAAc,aAAa,cAAc,UAAU;AAAA,IACxE;AAAA,IACA,KAAK;AACD,UAAI,OAAO,WAAW,MAAM,UAAU,OAAO,UAAU,MAAM,cAAc;AACvE,eAAO;AAAA,UACH,QAAQ,OAAO;AAAA,UACf,cAAc,OAAO;AAAA,UACrB,aAAa,OAAO;AAAA,UACpB,cAAc,kBAAkB,OAAO,QAAQ,OAAO,KAAK;AAAA,QAC/D;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACI,YAAM,IAAI,MAAM,mBAAmB;AAAA,EAC3C;AACJ;AACA,SAAS,sBAAsB,EAAE,QAAQ,OAAO,SAAS,GAAG;AACxD,QAAM,CAAC,OAAO,QAAQ,IAAU,iBAAW,SAAS;AAAA,IAChD;AAAA,IACA,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc,kBAAkB,QAAQ,KAAK;AAAA,EACjD,CAAC;AACD,EAAM,gBAAU,MAAM;AAClB,aAAS,EAAE,MAAM,UAAU,QAAQ,MAAM,CAAC;AAAA,EAC9C,GAAG,CAAC,QAAQ,KAAK,CAAC;AAClB,QAAM,UAAgB,cAAQ,OAAO,EAAE,GAAG,OAAO,OAAO,SAAS,IAAI,CAAC,OAAO,QAAQ,CAAC;AACtF,SAAc;AAAA,IAAc,wBAAwB;AAAA,IAAU,EAAE,OAAO,SAAS;AAAA,IACtE,oBAAc,qBAAqB,UAAU,EAAE,OAAO,QAAQ,GAAG,QAAQ;AAAA,EAAC;AACxF;AAEA,IAAM,kBAAwB,oBAAc,IAAI;AAChD,IAAM,cAAc,eAAe,eAAe,mBAAmB,eAAe;AACpF,SAAS,iBAAiB,EAAE,SAAS,GAAG;AACpC,QAAM,CAAC,QAAQ,IAAU,eAAS,CAAC,CAAC;AACpC,EAAM,gBAAU,MAAM,MAAM;AACxB,aAAS,QAAQ,CAAC,QAAQ,OAAO,aAAa,GAAG,CAAC;AAClD,aAAS,OAAO,GAAG,SAAS,MAAM;AAAA,EACtC,GAAG,CAAC,QAAQ,CAAC;AACb,QAAM,UAAgB,cAAQ,MAAM;AAChC,UAAM,gBAAgB,CAAC,OAAO;AAC1B,eAAS,OAAO,GAAG,SAAS,QAAQ,GAAG,SAAS,OAAO,CAAC,QAAQ,QAAQ,EAAE,CAAC;AAAA,IAC/E;AACA,UAAM,aAAa,CAAC,IAAI,UAAU;AAC9B,YAAM,KAAK,OAAO,WAAW,MAAM;AAC/B,sBAAc,EAAE;AAChB,WAAG;AAAA,MACP,GAAG,KAAK;AACR,eAAS,KAAK,EAAE;AAChB,aAAO;AAAA,IACX;AACA,UAAM,eAAe,CAAC,OAAO;AACzB,UAAI,OAAO,QAAW;AAClB,sBAAc,EAAE;AAChB,eAAO,aAAa,EAAE;AAAA,MAC1B;AAAA,IACJ;AACA,WAAO,EAAE,YAAY,aAAa;AAAA,EACtC,GAAG,CAAC,QAAQ,CAAC;AACb,SAAa,oBAAc,gBAAgB,UAAU,EAAE,OAAO,QAAQ,GAAG,QAAQ;AACrF;AAEA,IAAM,aAAmB,iBAAW,SAASA,YAAW,EAAE,OAAO,SAAS,WAAW,MAAM,MAAM,YAAY,SAAS,OAAO,GAAG,KAAK,GAAG,KAAK;AACzI,QAAM,EAAE,QAAQ,OAAO,IAAI,iBAAiB;AAC5C,QAAM,cAAc,MAAM,QAAQ,OAAO;AACzC,SAAc,oBAAc,UAAU,EAAE,KAAU,MAAM,UAAU,OAAO,aAAa,cAAc,aAAa,WAAW,KAAK,SAAS,cAAc,GAAG,SAAS,GAAG,SAAkB,OAAO,EAAE,GAAG,OAAO,GAAG,OAAO,OAAO,GAAG,GAAG,KAAK,GAAG,aAAa,WAAW,IAAU,oBAAc,MAAM,EAAE,WAAW,SAAS,YAAY,GAAG,OAAO,OAAO,KAAK,CAAC,CAAC;AAC/V,CAAC;AAED,SAAS,QAAQ,MAAM,UAAU;AAC7B,QAAM,OAAO,CAAC,UAAiB,oBAAc,OAAO,EAAE,OAAO,8BAA8B,SAAS,aAAa,OAAO,MAAM,QAAQ,MAAM,eAAe,QAAQ,WAAW,SAAS,GAAG,MAAM,GAAG,QAAQ;AAC3M,OAAK,cAAc;AACnB,SAAO;AACX;AACA,SAAS,WAAW,MAAM,OAAO;AAC7B,SAAO,QAAQ,MAAY;AAAA,IAAc;AAAA,IAAK,EAAE,MAAM,eAAe;AAAA,IAC3D,oBAAc,QAAQ,EAAE,GAAG,iBAAiB,MAAM,OAAO,CAAC;AAAA,IAChE;AAAA,EAAK,CAAC;AACd;AACA,SAAS,mBAAmB,MAAM,OAAO;AACrC,SAAO,QAAQ,MAAY;AAAA,IAAoB;AAAA,IAAU;AAAA,IAC/C;AAAA,MAAc;AAAA,MAAQ;AAAA,MAClB;AAAA,QAAc;AAAA,QAAQ,EAAE,IAAI,SAAS;AAAA,QACjC,oBAAc,QAAQ,EAAE,GAAG,iBAAiB,MAAM,QAAQ,CAAC;AAAA,QAC3D,oBAAc,QAAQ,EAAE,GAAG,cAAc,QAAQ,SAAS,aAAa,EAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IACpF,oBAAc,QAAQ,EAAE,GAAG,yCAAyC,QAAQ,gBAAgB,aAAa,EAAE,CAAC;AAAA,IAC5G;AAAA,MAAc;AAAA,MAAK,EAAE,MAAM,gBAAgB,MAAM,eAAe;AAAA,MAC5D,oBAAc,QAAQ,EAAE,GAAG,iBAAiB,MAAM,OAAO,CAAC;AAAA,MAChE;AAAA,IAAK;AAAA,EAAC,CAAC;AACnB;AACA,IAAM,YAAY,WAAW,SAAe,oBAAc,QAAQ,EAAE,GAAG,wGAAwG,CAAC,CAAC;AACjL,IAAM,eAAe,WAAW,YAAkB,oBAAc,QAAQ,EAAE,GAAG,gDAAgD,CAAC,CAAC;AAC/H,IAAM,WAAW,WAAW,QAAc,oBAAc,QAAQ,EAAE,GAAG,iDAAiD,CAAC,CAAC;AACxH,IAAM,cAAc,WAAW,WAAiB,oBAAoB,gBAAU,MAAM,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,OAAO,UAAiB,oBAAc,QAAQ,EAAE,KAAK,OAAO,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,eAAe,SAAS,aAAa,OAAO,QAAQ,gBAAgB,eAAgB,IAAI,MAAM,UAAW,QAAQ,IAAI,WAAW,UAAW,MAAM,MAAM,SAAU,KAAK,YAAY,CAAC,CAAE,CAAC,CAAC;AACnZ,IAAM,YAAY,WAAW,SAAe,oBAAc,QAAQ,EAAE,GAAG,oMAAoM,CAAC,CAAC;AAE7Q,IAAMC,mBAAkB,UAAU,IAAU,wBAAwB;AAEpE,SAAS,sBAAsB;AAC3B,QAAM,CAAC,cAAc,eAAe,IAAU,eAAS,KAAK;AAC5D,EAAM,gBAAU,MAAM;AAClB,QAAI,IAAI;AACR,UAAM,cAAc,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,kCAAkC;AACnI,oBAAgB,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,OAAO;AAC1F,UAAM,WAAW,CAAC,UAAU,gBAAgB,MAAM,OAAO;AACzD,KAAC,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,YAAY,UAAU,QAAQ;AACtK,WAAO,MAAM;AAAE,UAAIC;AAAI,cAAQA,MAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,yBAAyB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,YAAY,UAAU,QAAQ;AAAA,IAAG;AAAA,EAC9M,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AAEA,SAAS,sBAAsB,MAAM;AACjC,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,QAAM,SAAS,OAAO,iBAAiB,IAAI,EAAE;AAC7C,QAAM,UAAU,OAAO,MAAM,kBAAkB;AAC/C,MAAI,SAAS;AACT,UAAM,SAAS,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,QAAQ;AACjD,QAAI,OAAO,WAAW,GAAG;AACrB,UAAI,OAAO,CAAC;AACZ,UAAI,OAAO,CAAC;AAAA,IAChB,WACS,OAAO,WAAW,IAAI;AAC3B,UAAI,OAAO,EAAE;AACb,UAAI,OAAO,EAAE;AACb,UAAI,OAAO,EAAE;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,EAAE,GAAG,GAAG,EAAE;AACrB;AACA,SAAS,aAAa,SAAS,kBAAkB;AAC7C,QAAM,WAAiB,aAAO,MAAS;AACvC,QAAM,YAAkB,aAAO,MAAS;AACxC,QAAM,eAAe,oBAAoB;AACzC,EAAAD,iBAAgB,MAAM;AAClB,QAAI,IAAI,IAAI;AACZ,QAAI,QAAQ,WAAW,SAAS,YAAY,UAAa,CAAC,cAAc;AACpE,YAAM,EAAE,WAAW,UAAU,QAAQ,SAAS,IAAI,iBAAiB,SAAS,SAAS,QAAQ,QAAQ,sBAAsB,GAAG,sBAAsB,QAAQ,OAAO,CAAC,KAAK,CAAC;AAC1K,UAAI,aAAa,UAAU;AACvB,SAAC,KAAK,UAAU,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AACxE,kBAAU,UAAU;AACpB,YAAI;AACA,oBAAU,WAAW,MAAM,KAAK,QAAQ,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,WAAW,EAAE,UAAU,OAAO,CAAC;AAAA,QAC9I,SACO,KAAK;AACR,kBAAQ,MAAM,GAAG;AAAA,QACrB;AACA,YAAI,UAAU,SAAS;AACnB,oBAAU,QAAQ,WAAW,MAAM;AAC/B,sBAAU,UAAU;AACpB,yBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAAA,UACjE;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,UAAU;AAAA,EACvB,CAAC;AACD,SAAO;AAAA,IACH,kBAAkB,CAAC,oBAAoB;AACnC,eAAS,UAAU;AAAA,IACvB;AAAA,IACA,oBAAoB,MAAM;AAAE,UAAI;AAAI,eAAS,KAAK,UAAU,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe;AAAA,IAAW;AAAA,EAC3I;AACJ;AAEA,SAAS,mBAAmB;AACxB,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,cAAoB,aAAO,MAAS;AAC1C,QAAM,CAAC,eAAe,gBAAgB,IAAU,eAAS;AACzD,QAAM,kBAAwB,kBAAY,CAAC,SAAS;AAChD,iBAAa,UAAU;AACvB,QAAI,YAAY,SAAS;AACrB,kBAAY,QAAQ,WAAW;AAC/B,kBAAY,UAAU;AAAA,IAC1B;AACA,UAAM,sBAAsB,MAAM;AAC9B,UAAI,MAAM;AACN,cAAM,SAAS,OAAO,iBAAiB,IAAI;AAC3C,cAAM,QAAQ,CAAC,UAAU,WAAW,KAAK,KAAK;AAC9C,yBAAiB;AAAA,UACb,OAAO,KAAK,MAAM,KAAK,cAAc,MAAM,OAAO,WAAW,IAAI,MAAM,OAAO,YAAY,CAAC;AAAA,UAC3F,QAAQ,KAAK,MAAM,KAAK,eAAe,MAAM,OAAO,UAAU,IAAI,MAAM,OAAO,aAAa,CAAC;AAAA,QACjG,CAAC;AAAA,MACL,OACK;AACD,yBAAiB,MAAS;AAAA,MAC9B;AAAA,IACJ;AACA,wBAAoB;AACpB,QAAI,QAAQ,OAAO,mBAAmB,aAAa;AAC/C,kBAAY,UAAU,IAAI,eAAe,mBAAmB;AAC5D,kBAAY,QAAQ,QAAQ,IAAI;AAAA,IACpC;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,SAAO,EAAE,iBAAiB,cAAc,cAAc;AAC1D;AAEA,SAAS,WAAW;AAChB,QAAM,YAAkB,aAAO,MAAS;AACxC,QAAM,EAAE,YAAY,aAAa,IAAI,YAAY;AACjD,SAAa,kBAAY,CAAC,UAAU,UAAU;AAC1C,iBAAa,UAAU,OAAO;AAC9B,cAAU,UAAU,WAAW,UAAU,QAAQ,IAAI,QAAQ,CAAC;AAAA,EAClE,GAAG,CAAC,YAAY,YAAY,CAAC;AACjC;AAEA,SAAS,iBAAiB,IAAI;AAC1B,QAAM,MAAY,aAAO,EAAE;AAC3B,EAAAA,iBAAgB,MAAM;AAClB,QAAI,UAAU;AAAA,EAClB,CAAC;AACD,SAAa,kBAAY,IAAI,SAAS;AAAE,QAAI;AAAI,YAAQ,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,GAAG,IAAI;AAAA,EAAG,GAAG,CAAC,CAAC;AAC/I;AAEA,SAAS,OAAO,KAAK,OAAO;AACxB,MAAI,OAAO,QAAQ,YAAY;AAC3B,QAAI,KAAK;AAAA,EACb,WACS,KAAK;AACV,QAAI,UAAU;AAAA,EAClB;AACJ;AACA,SAAS,WAAW,MAAM,MAAM;AAC5B,SAAa,cAAQ,MAAM,QAAQ,QAAQ,QAAQ,OAC7C,OACA,CAAC,aAAa;AACZ,WAAO,MAAM,QAAQ;AACrB,WAAO,MAAM,QAAQ;AAAA,EACzB,GAAG,CAAC,MAAM,IAAI,CAAC;AACvB;AAEA,SAAS,aAAa,OAAO,WAAW,OAAO;AAC3C,QAAM,UAAgB,aAAO,KAAK;AAClC,EAAAA,iBAAgB,MAAM;AAClB,QAAI,YAAY,QAAQ,SAAS;AAC7B,cAAQ,UAAU;AAClB,YAAM;AAAA,IACV;AAAA,EACJ,GAAG,CAAC,UAAU,KAAK,CAAC;AACpB,QAAM,UAAgB,kBAAY,MAAM;AACpC,YAAQ,UAAU;AAAA,EACtB,GAAG,CAAC,CAAC;AACL,QAAM,SAAe,kBAAY,MAAM;AACnC,YAAQ,UAAU;AAAA,EACtB,GAAG,CAAC,CAAC;AACL,SAAO,EAAE,SAAS,OAAO;AAC7B;AAEA,SAAS,SAAS;AACd,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,KAAK;AAC9C,EAAAA,iBAAgB,MAAM;AAClB,aAAS,OAAO,iBAAiB,OAAO,SAAS,eAAe,EAAE,cAAc,KAAK;AAAA,EACzF,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AAEA,SAAS,aAAa;AAClB,QAAM,CAAC,WAAW,IAAU,eAAS,CAAC,CAAC;AACvC,QAAM,oBAA0B,kBAAY,CAAC,MAAM,UAAU;AACzD,QAAI;AACJ,KAAC,KAAK,YAAY,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,CAAC,aAAa;AACnF,UAAI,CAAC,MAAM,qBAAqB;AAC5B,iBAAS,KAAK;AAAA,IACtB,CAAC;AAAA,EACL,GAAG,CAAC,WAAW,CAAC;AAChB,QAAM,kBAAwB,cAAQ,OAAO;AAAA,IACzC,eAAe,CAAC,UAAU,kBAAkB,uBAAuB,KAAK;AAAA,IACxE,eAAe,CAAC,UAAU,kBAAkB,uBAAuB,KAAK;AAAA,IACxE,aAAa,CAAC,UAAU,kBAAkB,qBAAqB,KAAK;AAAA,IACpE,gBAAgB,CAAC,UAAU,kBAAkB,wBAAwB,KAAK;AAAA,IAC1E,iBAAiB,CAAC,UAAU,kBAAkB,yBAAyB,KAAK;AAAA,IAC5E,WAAW,CAAC,UAAU,kBAAkB,mBAAmB,KAAK;AAAA,IAChE,SAAS,CAAC,UAAU,kBAAkB,iBAAiB,KAAK;AAAA,IAC5D,SAAS,CAAC,UAAU,kBAAkB,gBAAgB,KAAK;AAAA,EAC/D,IAAI,CAAC,iBAAiB,CAAC;AACvB,QAAM,mBAAyB,kBAAY,CAAC,MAAM,aAAa;AAC3D,QAAI,CAAC,YAAY,IAAI,GAAG;AACpB,kBAAY,IAAI,IAAI,CAAC;AAAA,IACzB;AACA,gBAAY,IAAI,EAAE,QAAQ,QAAQ;AAClC,WAAO,MAAM;AACT,YAAM,YAAY,YAAY,IAAI;AAClC,UAAI,WAAW;AACX,kBAAU,OAAO,GAAG,UAAU,QAAQ,GAAG,UAAU,OAAO,CAAC,OAAO,OAAO,QAAQ,CAAC;AAAA,MACtF;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,WAAW,CAAC;AAChB,SAAO,EAAE,iBAAiB,iBAAiB;AAC/C;AAEA,SAAS,YAAY,UAAU,OAAO;AAClC,QAAM,mBAAyB,aAAO,CAAC;AACvC,QAAM,gBAAgB,SAAS;AAC/B,QAAM,kBAAkB,iBAAiB,IAAI,SAAS;AAClD,qBAAiB,UAAU,KAAK,IAAI;AACpC,aAAS,IAAI;AAAA,EACjB,CAAC;AACD,SAAa,kBAAY,IAAI,SAAS;AAClC,kBAAc,MAAM;AAChB,sBAAgB,IAAI;AAAA,IACxB,GAAG,SAAS,KAAK,IAAI,IAAI,iBAAiB,QAAQ;AAAA,EACtD,GAAG,CAAC,OAAO,iBAAiB,aAAa,CAAC;AAC9C;AAEA,IAAM,cAAc,kBAAkB,OAAO;AAC7C,IAAM,mBAAmB,kBAAkB,aAAa;AACxD,SAAS,WAAW,EAAE,OAAO,OAAO,QAAQ,QAAQ,MAAM,UAAU,YAAY,SAAS,QAAQ,SAAS,MAAO,GAAG;AAChH,MAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC5B,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,oBAAoB;AAC/D,QAAM,EAAE,QAAQ,IAAI,UAAU;AAC9B,QAAM,EAAE,WAAW,IAAI,YAAY;AACnC,QAAM,WAAiB,aAAO,IAAI;AAClC,EAAM,gBAAU,MAAM;AAClB,QAAI,WAAW,GAAG;AACd,cAAQ,kBAAkB,MAAM,CAAC;AAAA,IACrC;AAAA,EACJ,GAAG,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAC5B,QAAM,gBAAgB,iBAAiB,CAAC,QAAQ;AAC5C,KAAC,YAAY,MAAM,IAAI,OAAO,IAAI,QAAQ,QAAQ,GAC7C,MAAM,MAAM;AAAA,IAAE,CAAC,EACf,KAAK,MAAM;AACZ,UAAI,CAAC,IAAI,YAAY;AACjB;AAAA,MACJ;AACA,gBAAU,qBAAqB;AAC/B,iBAAW,MAAM;AACb,mBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,GAAG;AAAA,MAC9D,GAAG,CAAC;AAAA,IACR,CAAC;AAAA,EACL,CAAC;AACD,QAAM,cAAoB,kBAAY,CAAC,QAAQ;AAC3C,aAAS,UAAU;AACnB,QAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,UAAU;AACxD,oBAAc,GAAG;AAAA,IACrB;AAAA,EACJ,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,eAAqB,kBAAY,CAAC,UAAU;AAC9C,kBAAc,MAAM,aAAa;AAAA,EACrC,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,gBAAgB,iBAAiB,MAAM;AACzC,cAAU,kBAAkB;AAC5B,gBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,EAC9D,CAAC;AACD,QAAM,QAAQ,gBAAgB,OAAO,QAAQ;AAC7C,QAAM,cAAc,CAAC,OAAO,aAAc,OAAO,SAAS,KAAK,IAAI,QAAQ;AAC3E,QAAM,WAAW,YAAY,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG,OAAO,MAAM,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,KAAK,KAAK,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,CAAC;AACpT,QAAM,YAAY,YAAY,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG,OAAO,MAAM,SAAS,CAAC,MAAM,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,KAAK,KAAK,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,CAAC;AACzT,QAAM,eAAe,YAAY,YAC3B;AAAA,IACE,UAAU,OAAO,QAAQ;AAAA,IACzB,WAAW,OAAO,SAAS;AAAA,EAC/B,IACE;AAAA,IACE,UAAU;AAAA,IACV,WAAW;AAAA,EACf;AACJ,QAAM,UAAU,KAAK,MAAM,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,KAAK,IAAI;AAClK,QAAM,sBAAsB,MAAM,QAAQ,CAAC,SAAS,MAAM,SAAS,MAAM,SAAU,KAAK,SAAS,MAAM,SAAU,MAAM,QAAQ,OAAO;AACtI,QAAM,QAAQ,UAAU,QAAQ,UAAU,IAAI,GAAG,KAAK,MAAM,KAAK,IAAI,oBAAoB,GAAG,KAAK,KAAK,CAAC,CAAC,OAAO;AAC/G,QAAM,EAAE,OAAO,iBAAiB,WAAW,qBAAqB,GAAG,eAAe,IAAI,cAAc,CAAC;AACrG,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IAClC,oBAAc,OAAO,EAAE,KAAK,aAAa,QAAQ,cAAc,SAAS,eAAe,SAAkB,WAAW,OAAO,WAAW,KAAK,SAAS,iBAAiB,CAAC,GAAG,SAAS,SAAS,iBAAiB,OAAO,CAAC,GAAG,WAAW,yBAAyB,SAAS,iBAAiB,SAAS,CAAC,GAAG,mBAAmB,GAAG,OAAO,EAAE,GAAG,cAAc,GAAG,OAAO,GAAG,gBAAgB,GAAG,GAAG,gBAAgB,KAAK,MAAM,KAAK,OAAc,QAAgB,KAAK,MAAM,IAAI,CAAC;AAAA,IACzc,WAAW,yBAAgC;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,SAAS,YAAY,wBAAwB,CAAC,EAAE;AAAA,MACzH,WAAW,0BACL,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,eAAgB,OAAO,YAAY,IAAY,oBAAc,aAAa,EAAE,WAAW,KAAK,SAAS,YAAY,GAAG,SAAS,YAAY,oBAAoB,CAAC,CAAC,EAAE,CAAC;AAAA,MAC/N,WAAW,wBACL,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,aAAc,OAAO,UAAU,IAAY,oBAAc,WAAW,EAAE,WAAW,KAAK,SAAS,YAAY,GAAG,SAAS,YAAY,kBAAkB,CAAC,CAAC,EAAE,CAAC;AAAA,IAAG;AAAA,EAAE;AACxO;AAEA,IAAM,eAAqB,iBAAW,SAASE,cAAa,EAAE,WAAW,UAAU,GAAG,KAAK,GAAG,KAAK;AAC/F,QAAM,UAAgB,aAAO,IAAI;AACjC,SAAc;AAAA,IAAc;AAAA,IAAyB,EAAE,QAAiB;AAAA,IAC9D,oBAAc,OAAO,EAAE,KAAK,WAAW,KAAK,OAAO,GAAG,WAAW,KAAK,SAAS,MAAM,GAAG,SAAS,GAAG,GAAG,KAAK,GAAG,QAAQ;AAAA,EAAC;AACtI,CAAC;AAED,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AACrC,EAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AACrC,EAAAA,YAAWA,YAAW,WAAW,IAAI,CAAC,IAAI;AAC9C,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,SAAS,iBAAiB,kBAAkB,eAAe,eAAe,aAAa,UAAU;AAC7F,EAAM,gBAAU,MAAM,CAAC,WACjB,QAAQ,iBAAiB,uBAAuB,aAAa,GAAG,iBAAiB,uBAAuB,aAAa,GAAG,iBAAiB,qBAAqB,WAAW,GAAG,iBAAiB,wBAAwB,WAAW,GAAG,iBAAiB,yBAAyB,WAAW,CAAC,IACzR,MAAM;AAAA,EAAE,GAAG,CAAC,kBAAkB,eAAe,eAAe,aAAa,QAAQ,CAAC;AAC5F;AAEA,IAAI;AAAA,CACH,SAAUC,UAAS;AAChB,EAAAA,SAAQA,SAAQ,MAAM,IAAI,CAAC,IAAI;AAC/B,EAAAA,SAAQA,SAAQ,OAAO,IAAI,CAAC,IAAI;AAChC,EAAAA,SAAQA,SAAQ,MAAM,IAAI,CAAC,IAAI;AACnC,GAAG,YAAY,UAAU,CAAC,EAAE;AAC5B,IAAM,kBAAkB;AACxB,SAAS,gBAAgB,EAAE,uBAAuB,GAAG,kBAAkB,cAAc,gBAAgB,wBAAwB,cAAc,iBAAiB,eAAe,eAAe,eAAe,iBAAiB,aAAa,gBAAgB,cAAc,cAAc;AAC/Q,QAAM,SAAe,aAAO,CAAC;AAC7B,QAAM,WAAiB,aAAO,CAAC,CAAC;AAChC,QAAM,gBAAsB,aAAO,MAAS;AAC5C,QAAM,YAAkB,aAAO,CAAC;AAChC,QAAM,UAAgB,aAAO,QAAQ,IAAI;AACzC,QAAM,eAAqB,kBAAY,CAAC,UAAU;AAC9C,QAAI,cAAc,YAAY,MAAM,WAAW;AAC3C,oBAAc,UAAU;AACxB,cAAQ,UAAU,QAAQ;AAAA,IAC9B;AACA,UAAM,kBAAkB,SAAS;AACjC,oBAAgB,OAAO,GAAG,gBAAgB,QAAQ,GAAG,gBAAgB,OAAO,CAAC,MAAM,EAAE,cAAc,MAAM,SAAS,CAAC;AAAA,EACvH,GAAG,CAAC,CAAC;AACL,QAAM,aAAmB,kBAAY,CAAC,UAAU;AAC5C,iBAAa,KAAK;AAClB,UAAM,QAAQ;AACd,aAAS,QAAQ,KAAK,KAAK;AAAA,EAC/B,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,gBAAgB,iBAAiB,CAAC,UAAU;AAC9C,eAAW,KAAK;AAAA,EACpB,CAAC;AACD,QAAM,uBAAuB,CAAC,OAAO,cAAe,mBAAmB,QAAQ,aAAe,iBAAiB,QAAQ,CAAC;AACxH,QAAM,cAAc,iBAAiB,CAAC,UAAU;AAC5C,QAAI,SAAS,QAAQ,KAAK,CAAC,MAAM,EAAE,cAAc,MAAM,SAAS,KAAK,cAAc,YAAY,MAAM,WAAW;AAC5G,YAAM,WAAW,KAAK,IAAI,IAAI,UAAU;AACxC,YAAM,gBAAgB,OAAO;AAC7B,UAAI,QAAQ,YAAY,QAAQ,OAAO;AACnC,YAAI,KAAK,IAAI,aAAa,IAAI,MAAM,kBAC/B,KAAK,IAAI,aAAa,IAAI,KAAK,WAAW,wBAAyB;AACpE,wBAAc,eAAe,QAAQ;AAAA,QACzC,OACK;AACD,wBAAc,aAAa;AAAA,QAC/B;AAAA,MACJ,WACS,QAAQ,YAAY,QAAQ,MAAM;AACvC,YAAI,qBAAqB,eAAe,IAAI,eAAe,GAAG;AAC1D,uBAAa,eAAe,QAAQ;AAAA,QACxC,OACK;AACD,uBAAa,aAAa;AAAA,QAC9B;AAAA,MACJ;AACA,aAAO,UAAU;AACjB,cAAQ,UAAU,QAAQ;AAAA,IAC9B;AACA,iBAAa,KAAK;AAAA,EACtB,CAAC;AACD,QAAM,gBAAgB,iBAAiB,CAAC,UAAU;AAC9C,UAAM,UAAU,SAAS,QAAQ,KAAK,CAAC,MAAM,EAAE,cAAc,MAAM,SAAS;AAC5E,QAAI,SAAS;AACT,YAAM,mBAAmB,cAAc,YAAY,MAAM;AACzD,UAAI,MAAM,YAAY,GAAG;AACrB,YAAI,oBAAoB,OAAO,YAAY,GAAG;AAC1C,sBAAY,KAAK;AAAA,QACrB,OACK;AACD,uBAAa,OAAO;AAAA,QACxB;AACA;AAAA,MACJ;AACA,YAAM,SAAS,MAAM,UAAU,QAAQ;AACvC,YAAM,SAAS,MAAM,UAAU,QAAQ;AACvC,UAAI,cAAc,YAAY,QAAW;AACrC,cAAM,eAAe,CAAC,eAAe;AACjC,qBAAW,KAAK;AAChB,wBAAc,UAAU,MAAM;AAC9B,oBAAU,UAAU,KAAK,IAAI;AAC7B,kBAAQ,UAAU;AAAA,QACtB;AACA,YAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,MAAM,IAAI,mBAAmB,aAAa,MAAM,GAAG;AACnG,cAAI,CAAC,wBAAwB;AACzB,yBAAa,QAAQ,KAAK;AAC1B,yBAAa;AAAA,UACjB;AAAA,QACJ,WACS,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,KAAK,qBAAqB,QAAQ,eAAe,GAAG;AAC3F,uBAAa,QAAQ,IAAI;AACzB,sBAAY;AAAA,QAChB;AAAA,MACJ,WACS,kBAAkB;AACvB,YAAI,QAAQ,YAAY,QAAQ,OAAO;AACnC,iBAAO,UAAU;AACjB,0BAAgB,MAAM;AAAA,QAC1B,WACS,QAAQ,YAAY,QAAQ,MAAM;AACvC,iBAAO,UAAU;AACjB,yBAAe,MAAM;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,mBAAiB,kBAAkB,eAAe,eAAe,WAAW;AAChF;AAEA,SAAS,wBAAwB,EAAE,sBAAsB,qBAAsB,GAAG;AAC9E,QAAM,MAAY,aAAO,IAAI;AAC7B,QAAM,WAAW,iBAAiB,CAAC,UAAU;AACzC,UAAM,aAAa,KAAK,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,MAAM,MAAM;AACjE,QAAK,cAAc,wBAA0B,CAAC,cAAc,wBAAyB,MAAM,SAAS;AAChG,YAAM,eAAe;AAAA,IACzB;AAAA,EACJ,CAAC;AACD,SAAa,kBAAY,CAAC,SAAS;AAC/B,QAAI;AACJ,QAAI,MAAM;AACN,WAAK,iBAAiB,SAAS,UAAU,EAAE,SAAS,MAAM,CAAC;AAAA,IAC/D,OACK;AACD,OAAC,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,oBAAoB,SAAS,QAAQ;AAAA,IACpG;AACA,QAAI,UAAU;AAAA,EAClB,GAAG,CAAC,QAAQ,CAAC;AACjB;AAEA,SAAS,cAAc,YAAY,kBAAkB,cAAc,gBAAgB,wBAAwB,cAAc,iBAAiB,eAAe,eAAe;AACpK,QAAM,SAAe,aAAO,CAAC;AAC7B,QAAM,SAAe,aAAO,CAAC;AAC7B,QAAM,gBAAsB,aAAO,MAAS;AAC5C,QAAM,eAAqB,aAAO,MAAS;AAC3C,QAAM,eAAqB,aAAO,CAAC;AACnC,QAAM,sBAA4B,aAAO,MAAS;AAClD,QAAM,YAAkB,aAAO,CAAC;AAChC,QAAM,EAAE,YAAY,aAAa,IAAI,YAAY;AACjD,QAAM,2BAAiC,kBAAY,MAAM;AACrD,QAAI,cAAc,SAAS;AACvB,mBAAa,cAAc,OAAO;AAClC,oBAAc,UAAU;AAAA,IAC5B;AAAA,EACJ,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,0BAAgC,kBAAY,MAAM;AACpD,QAAI,aAAa,SAAS;AACtB,mBAAa,aAAa,OAAO;AACjC,mBAAa,UAAU;AAAA,IAC3B;AAAA,EACJ,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,gBAAgB,iBAAiB,MAAM;AACzC,QAAI,eAAe,WAAW,OAAO;AACjC,aAAO,UAAU;AACjB,gBAAU,UAAU;AACpB,+BAAyB;AACzB,8BAAwB;AAAA,IAC5B;AAAA,EACJ,CAAC;AACD,EAAM,gBAAU,eAAe,CAAC,YAAY,aAAa,CAAC;AAC1D,QAAM,oBAAoB,iBAAiB,CAAC,uBAAuB;AAC/D,iBAAa,UAAU;AACvB,QAAI,OAAO,YAAY,oBAAoB;AACvC,oBAAc,OAAO,OAAO;AAAA,IAChC;AAAA,EACJ,CAAC;AACD,QAAM,UAAU,iBAAiB,CAAC,UAAU;AACxC,QAAI,MAAM,SAAS;AACf;AAAA,IACJ;AACA,QAAI,KAAK,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,MAAM,MAAM,GAAG;AACjD;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC,YAAY;AACjC,mBAAa,UAAU;AACvB,mBAAa,oBAAoB,OAAO;AACxC,0BAAoB,UAChB,UAAU,IACJ,WAAW,MAAM;AACf,qBAAa,UAAU;AACvB,4BAAoB,UAAU;AAAA,MAClC,GAAG,GAAG,IACJ;AAAA,IACd;AACA,QAAI,eAAe,WAAW,MAAM;AAChC,UAAI,KAAK,IAAI,MAAM,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa,OAAO,GAAG;AAChE,wBAAgB,MAAM,MAAM;AAC5B;AAAA,MACJ;AACA,UAAI,CAAC,aAAa,CAAC,MAAM,MAAM,GAAG;AAC9B;AAAA,MACJ;AACA,aAAO,WAAW,MAAM;AACxB,+BAAyB;AACzB,UAAI,KAAK,IAAI,OAAO,OAAO,IAAI,IAAI;AAC/B,eAAO,UAAU;AACjB,wBAAgB,CAAC;AACjB,kBAAU,UAAU,KAAK,IAAI;AAC7B,qBAAa;AAAA,MACjB,OACK;AACD,cAAM,qBAAqB,OAAO;AAClC,sBAAc,UAAU,WAAW,MAAM;AACrC,wBAAc,UAAU;AACxB,cAAI,uBAAuB,OAAO,SAAS;AACvC,mBAAO,UAAU;AAAA,UACrB;AAAA,QACJ,GAAG,sBAAsB;AAAA,MAC7B;AAAA,IACJ,WACS,eAAe,WAAW,OAAO;AACtC,UAAI,iBAAiB,OAAO,UAAU,MAAM;AAC5C,uBAAiB,KAAK,IAAI,KAAK,IAAI,cAAc,GAAG,cAAc,IAAI,KAAK,KAAK,cAAc;AAC9F,aAAO,UAAU;AACjB,sBAAgB,cAAc;AAC9B,8BAAwB;AACxB,UAAI,KAAK,IAAI,cAAc,IAAI,MAAM,gBAAgB;AACjD,wBAAgB,MAAM,MAAM;AAC5B,sBAAc,gBAAgB,KAAK,IAAI,IAAI,UAAU,OAAO;AAC5D;AAAA,MACJ;AACA,mBAAa,UAAU,WAAW,MAAM,kBAAkB,cAAc,GAAG,IAAI,sBAAsB;AAAA,IACzG,OACK;AACD,sBAAgB,MAAM,MAAM;AAAA,IAChC;AAAA,EACJ,CAAC;AACD,EAAM,gBAAU,MAAM,iBAAiB,gBAAgB,OAAO,GAAG,CAAC,kBAAkB,OAAO,CAAC;AAChG;AAEA,IAAM,qBAAqB,kBAAkB,WAAW;AACxD,IAAM,oBAA0B,oBAAc,IAAI;AAClD,IAAM,gBAAgB,eAAe,iBAAiB,qBAAqB,iBAAiB;AAC5F,SAAS,WAAW,EAAE,UAAU,GAAG,MAAM,GAAG;AACxC,MAAI;AACJ,QAAM,EAAE,UAAU,WAAW,YAAY,IAAI,QAAQ,OAAO,IAAI;AAChE,QAAM,EAAE,eAAe,iBAAiB,sBAAsB,qBAAqB,IAAI;AACvF,QAAM,CAAC,cAAc,eAAe,IAAU,eAAS;AACvD,QAAM,QAAQ,iBAAiB;AAC/B,QAAM,WAAW,oBAAoB;AACrC,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,WAAW,IAAI;AAClE,QAAM,cAAoB,aAAO,CAAC;AAClC,QAAM,aAAmB,aAAO,CAAC;AACjC,QAAM,cAAoB,aAAO,CAAC;AAClC,QAAM,EAAE,iBAAiB,iBAAiB,IAAI,WAAW;AACzD,QAAM,EAAE,WAAW,QAAQ,IAAI,UAAU;AACzC,QAAM,4BAA4B,SAAS;AAC3C,QAAM,qBAAqB,SAAS;AACpC,QAAM,oBAAoB,SAAS;AACnC,QAAM,EAAE,cAAc,iBAAiB,cAAc,IAAI,iBAAiB;AAC1E,QAAM,qBAAqB,WAAW,wBAAwB,EAAE,sBAAsB,qBAAqB,CAAC,GAAG,eAAe;AAC9H,QAAM,cAAoB,aAAO,IAAI;AACrC,QAAM,iBAAiB,WAAW,aAAa,MAAS;AACxD,QAAM,EAAE,iBAAiB,IAAI,mBAAmB;AAChD,QAAM,QAAQ,OAAO;AACrB,QAAM,MAAM,CAAC,WAAW,QAAQ,KAAK,MAAM,OAAO,UAAU,WAAW,QAAQ;AAC/E,QAAM,QAAQ,iBAAiB,MAAM;AAAE,QAAIH;AAAI,YAAQA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM;AAAA,EAAG,CAAC;AACpI,QAAM,mBAAmB,iBAAiB,MAAM,KAAK;AACrD,QAAM,mBAAmB,iBAAiB,MAAM,KAAK;AACrD,QAAM,OAAa,kBAAY,CAAC,WAAW,QAAQ,aAAa,MAAM,GAAG,CAAC,OAAO,CAAC;AAClF,QAAM,OAAa,kBAAY,CAAC,WAAW,QAAQ,aAAa,MAAM,GAAG,CAAC,OAAO,CAAC;AAClF,QAAM,QAAc,kBAAY,MAAM,QAAQ,YAAY,GAAG,CAAC,OAAO,CAAC;AACtE,QAAM,eAAe,CAAC,WAAW,EAAE,SAAS,WACtC,IAAI,MAAM,IAAI,KAAK,MAAM,iBAAiB,KACvC,IAAI,MAAM,IAAI,KAAK,MAAM,iBAAiB,MAAM,OAAO,SAAS;AACzE,QAAM,iBAAiB,CAAC,WAAW;AAC/B,QAAIA;AACJ,gBAAY,UAAU;AACtB,KAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM,YAAY,OAAO,cAAc,GAAG,GAAG,KAAK,MAAM,MAAM,CAAC,IAAI;AAAA,EAC3I;AACA,QAAM,gBAAgB,CAAC,WAAW;AAC9B,QAAIA,KAAI;AACR,eAAW,UAAU;AACrB,gBAAY,WAAW,MAAM;AACzB,YAAM,YAAY;AAClB,YAAM,aAAa;AACnB,YAAM,eAAe,MAAM;AACvB,YAAI,mBAAmB,SAAS;AAC5B,iBAAO;AACX,YAAI,iBAAiB,SAAS;AAC1B,iBAAO,CAAC;AACZ,eAAO;AAAA,MACX,GAAG;AACH,aAAO,KAAK,IAAI,KAAK,IAAI,MAAM,IAAK,cAAc,aAAc,IAAI,aAAa,CAAC,GAAG,UAAU,GAAG,CAAC;AAAA,IACvG,GAAG;AACH,KAACA,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM,YAAY,OAAO,aAAa,GAAG,GAAG,KAAK,MAAM,MAAM,CAAC,IAAI;AACtI,KAAC,KAAK,aAAa,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,YAAY,OAAO,cAAc,GAAG,GAAG,YAAY,OAAO,EAAE;AAAA,EAC1I;AACA,QAAM,EAAE,kBAAkB,qBAAqB,IAAI,aAAa,aAAa,CAAC,UAAU,MAAM,cAAc;AACxG,QAAI,YAAY,WAAW,eAAe;AACtC,aAAO;AAAA,QACH,WAAW;AAAA,UACP;AAAA,YACI,WAAW,gBAAgB,SAAS,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC;AAAA,YACjE,SAAS,SAAS;AAAA,UACtB;AAAA,UACA,EAAE,WAAW,mBAAmB,SAAS,EAAE;AAAA,QAC/C;AAAA,QACA,UAAU,SAAS;AAAA,QACnB,QAAQ,UAAU,OAAO;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACD,QAAM,OAAO,CAAC,QAAQ,WAAW;AAC7B,QAAI,iBAAiB,iBAAiB;AAClC,oBAAc,MAAM;AACpB,UAAI,WAAW;AACf,UAAI,YAAY,SAAS;AACrB,mBAAW,UAAU,QAAQ,SAAS,IAAI;AAC1C,6BAAqB;AAAA,UACjB,MAAM,YAAY,QAAQ,sBAAsB;AAAA,UAChD,SAAS,YAAY;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL;AACA,wBAAkB,MAAM;AACpB,sBAAc,CAAC;AACf,sBAAc,WAAW,IAAI;AAAA,MACjC,GAAG,QAAQ;AACX,oBAAc,WAAW,SAAS;AAClC,UAAI,CAAC,QAAQ;AACT,cAAM;AAAA,MACV;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,EAAE,kBAAkB,mBAAmB,IAAI,aAAa,aAAa,CAAC,UAAU,MAAM,cAAc;AACtG,QAAIA;AACJ,QAAI,YAAY,WAAW,mBAAmBA,MAAK,MAAM,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW;AACnH,YAAM,gBAAgB,sBAAsB,SAAS,OAAO;AAC5D,YAAM,gBAAgB,cAAc,UAAW,cAAc,UAAU,cAAc,QAAS,MAAM,cAAc,UAAU;AAC5H,aAAO;AAAA,QACH,WAAW;AAAA,UACP;AAAA,YACI,WAAW,aAAa,IAAI,MAAM,cAAc,SAAS,KAAK,KAAK,cAAc,QAAQ,gBACrF,SAAS,KAAK,IACd,KAAK,IACL,UAAU,CAAC;AAAA,UACnB;AAAA,UACA,EAAE,WAAW,kBAAkB;AAAA,QACnC;AAAA,QACA,UAAU,MAAM,UAAU;AAAA,QAC1B,QAAQ,MAAM,UAAU;AAAA,MAC5B;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACD,QAAM,QAAQ,iBAAiB,CAAC,WAAW;AACvC,QAAIA,KAAI;AACR,UAAM,qBAAqB,OAAO,UAAU;AAC5C,UAAM,gBAAgB,CAAC,sBAAuBA,MAAK,UAAU,gBAAgB,QAAQA,QAAO,SAASA,MAAK,UAAU,QAAS,UAAU;AACvI,UAAM,cAAc,CAAC,sBAAsB,CAAC,mBAAmB,IAAI,UAAU,OAAO,aAAa,UAAU,OAAO;AAClH,QAAI,EAAE,UAAU,IAAI;AACpB,UAAM,SAAS,KAAK,OAAO,WAAW,QAAQ,OAAO,SAAS,KAAK;AACnE,QAAI,gBAAgB,WAAW;AAC/B,QAAI,4BAA4B,gBAAgB;AAChD,QAAI,CAAC,WAAW;AACZ,YAAM,iBAAiB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc;AACnG,YAAM,cAAc,OAAO,YAAY;AACvC,YAAM,eAAe,iBACd,gBAAgB,iBAAkB,KAAK,IAAI,kBAAkB,IAC9D;AACN,UAAI,UAAU,GAAG;AACb,YAAI,cAAc,cAAc;AAC5B,sCACK,4BAA4B,eAAgB,KAAK,IAAI,aAAa,eAAe,CAAC;AAAA,QAC3F,WACS,gBAAgB;AACrB,sCACK,gBAAgB,kBAAmB,iBAAiB,KAAK,IAAI,kBAAkB;AAAA,QACxF;AACA,oBAAY,IAAI,kBAAkB,IAAI,IAAI,cAAc;AAAA,MAC5D,OACK;AACD,oCAA4B,gBAAgB;AAAA,MAChD;AAAA,IACJ;AACA,QAAI,YAAY;AAChB,QAAI,cAAc,aAAa;AAC3B,UAAI,aAAa,IAAI,CAAC,CAAC,GAAG;AACtB,oBAAY,CAAC;AAAA,MACjB,OACK;AACD,wBAAgB,WAAW;AAC3B,oCAA4B;AAAA,MAChC;AAAA,IACJ,WACS,cAAc,aAAa;AAChC,UAAI,aAAa,IAAI,EAAE,CAAC,GAAG;AACvB,oBAAY;AAAA,MAChB,OACK;AACD,wBAAgB,WAAW;AAC3B,oCAA4B;AAAA,MAChC;AAAA,IACJ;AACA,gCAA4B,KAAK,MAAM,yBAAyB;AAChE,uBAAmB,MAAM;AACrB,qBAAe,CAAC;AAChB,oBAAc,WAAW,IAAI;AAAA,IACjC,GAAG,yBAAyB;AAC5B,QAAI,YAAY,SAAS;AACrB,uBAAiB;AAAA,QACb,MAAM,YAAY,QAAQ,sBAAsB;AAAA,QAChD,OAAO,MAAM;AAAA,MACjB,CAAC;AAAA,IACL;AACA,kBAAc,aAAa;AAC3B,YAAQ,cAAc;AAAA,MAClB,MAAM;AAAA,MACN;AAAA,MACA,UAAU;AAAA,MACV,QAAQ;AAAA,IACZ,CAAC;AAAA,EACL,CAAC;AACD,EAAM,gBAAU,MAAM;AAClB,QAAIA,KAAI;AACR,UAAMA,MAAK,MAAM,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,gBAAgB,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AACzJ,gCAA0B,MAAM,SAAS,EAAE,MAAM,SAAS,WAAW,EAAE,CAAC,GAAG,MAAM,UAAU,QAAQ;AAAA,IACvG;AAAA,EACJ,GAAG,CAAC,MAAM,WAAW,UAAU,yBAAyB,CAAC;AACzD,QAAM,cAAc;AAAA,IAChB;AAAA,IACA;AAAA,KACC,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,UAAU;AAAA,IACvF,UAAU;AAAA,IACV,MAAM,cAAc,WAAW,KAAK;AAAA,IACpC,CAAC,WAAW,eAAe,MAAM;AAAA,IACjC,CAAC,QAAQ,aAAa,MAAM,EAAE,QAAQ,UAAU,OAAO,EAAE,CAAC;AAAA,IAC1D,CAAC,WAAW,MAAM,EAAE,QAAQ,OAAO,EAAE,CAAC;AAAA,EAC1C;AACA,QAAM,aAAa;AAAA,IACf,MAAM;AACF,UAAI,iBAAiB;AACjB,sBAAc,WAAW,IAAI;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,CAAC,WAAW,cAAc,MAAM;AAAA,IAChC,CAAC,WAAW,KAAK,MAAM;AAAA,IACvB,CAAC,WAAW,KAAK,QAAQ,IAAI;AAAA,EACjC;AACA,kBAAgB,YAAY,GAAG,aAAa,eAAe,iBAAiB,GAAG,UAAU;AACzF,gBAAc,YAAY,GAAG,WAAW;AACxC,QAAM,eAAe,iBAAiB,MAAM;AACxC,QAAI,WAAW,SACX,iBAAiB,EAAE,cAAc,IAAI,SAAS,aAAa,CAAC,KAAK,SAAS,mBAAmB,CAAC,CAAC,EAAE,GAAG;AACpG,YAAM;AAAA,IACV;AAAA,EACJ,CAAC;AACD,EAAM,gBAAU,cAAc,CAAC,YAAY,CAAC;AAC5C,QAAM,iBAAiB,iBAAiB,MAAM;AAC1C,QAAIA;AACJ,KAACA,MAAK,GAAG,UAAU,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,IAAI,EAAE,OAAO,MAAM,aAAa,CAAC;AAAA,EACjG,CAAC;AACD,EAAM,gBAAU,gBAAgB,CAAC,MAAM,aAAa,cAAc,CAAC;AACnE,EAAM,gBAAU,MAAM,QAAQ,UAAU,aAAa,CAAC,WAAW,MAAM,EAAE,WAAW,aAAa,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,aAAa,CAAC,WAAW,MAAM,EAAE,WAAW,aAAa,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,cAAc,CAAC,WAAW,SAAS,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,OAAO,QAAQ,CAAC;AACtR,QAAM,UAAgB,cAAQ,OAAO;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,gBAAgB,iBAAiB,eAAe,SAAS,OAAO,IAAI,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,IACrG,eAAe,iBAAiB,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,IACtD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACb,CAAC;AACD,EAAM,0BAAoB,WAAW,KAAK,OAAO;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAI,CAAC,MAAM,MAAM,OAAO,OAAO,kBAAkB,gBAAgB,CAAC;AAClE,SAAc,oBAAc,OAAO,EAAE,KAAK,oBAAoB,WAAW,KAAK,SAAS,mBAAmB,CAAC,GAAG,SAAS,iBAAiB,CAAC,GAAG,OAAO;AAAA,IAC3I,GAAI,eAAe,WAAW,QACxB,EAAE,CAAC,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,MAAM,YAAY,OAAO,CAAC,KAAK,IACnE;AAAA,IACN,GAAI,eAAe,WAAW,OACxB;AAAA,MACE,CAAC,OAAO,aAAa,CAAC,GAAG,GAAG,KAAK,MAAM,WAAW,OAAO,CAAC;AAAA,MAC1D,CAAC,OAAO,cAAc,CAAC,GAAG,GAAG,YAAY,OAAO;AAAA,IACpD,IACE;AAAA,IACN,GAAI,WAAW,gBAAgB,SAAS,EAAE,CAAC,OAAO,yBAAyB,CAAC,GAAG,WAAW,YAAY,IAAI;AAAA,IAC1G,GAAG,OAAO;AAAA,EACd,GAAG,GAAI,WAAW,OAAO,EAAE,MAAM,UAAU,aAAa,UAAU,wBAAwB,WAAW,IAAI,MAAO,UAAU,IAAI,GAAG,gBAAgB,GAAG,iBAAwB;AAAA,IAAc,kBAAkB;AAAA,IAAU,EAAE,OAAO,QAAQ;AAAA,IACvO;AAAA,KAAW,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,SAC7D,GAAG,KAAK,MAAM;AAAA,EAAC,CAAE;AACzB;AACA,IAAM,mBAAmB,aAAa,mBAAmB,UAAU;AAEnE,SAAS,YAAY,OAAO;AACxB,SAAO,cAAc,iBAAiB,KAAK;AAC/C;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO,cAAc,SAAS,KAAK;AACvC;AACA,SAAS,cAAc,EAAE,OAAO,OAAO,GAAG;AACtC,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,EAAE,aAAa,IAAI,iBAAiB;AAC1C,QAAM,EAAE,WAAW,OAAO,MAAM,IAAI,cAAc;AAClD,QAAM,EAAE,QAAQ,UAAU,EAAE,UAAU,WAAW,GAAG,IAAI,EAAE,OAAO,QAAQ,GAAG,YAAY,EAAE,qBAAqB,GAAG,QAAQ,EAAE,OAAO,MAAM,EAAG,IAAI,iBAAiB;AACjK,QAAM,EAAE,iBAAiB,IAAI,mBAAmB;AAChD,QAAM,YAAY,WAAW;AAC7B,EAAM,gBAAU,MAAM;AAClB,QAAI;AACJ,QAAI,eAAe,KAAK,aAAa,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,iBAAiB,EAAE,aAAa,IAAI;AAC/H,YAAM;AAAA,IACV;AAAA,EACJ,GAAG,CAAC,WAAW,OAAO,gBAAgB,CAAC;AACvC,QAAM,cAAc,MAAM;AACtB,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,YAAY,KAAK,OAAO,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ,EAAE,OAAO,QAAQ,MAAM,UAAU,CAAC;AAC1H,QAAI,CAAC,YAAY,aAAa,KAAK,GAAG;AAClC,iBAAkB,oBAAc,YAAY,EAAE,OAAc,QAAgB,QAAgB,MAAM,WAAW,UAAoB,YAAwB,SAAS,CAAC,YAAY,MAAM,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,EAAE,OAAO,aAAa,CAAC,IAAI,OAAU,CAAC;AAAA,IACzR;AACA,WAAO,WAAkB;AAAA,MAAoB;AAAA,MAAU;AAAA,OAAO,KAAK,OAAO,iBAAiB,QAAQ,OAAO,SAAS,SAC/G,GAAG,KAAK,QAAQ,EAAE,MAAM,CAAC;AAAA,QACvB,KAAK,OAAO,oBAAoB,QAAQ,OAAO,SAAS,KAAM,CAAC,EAAE,SAAS,MAAM,UAAW,EAAE,OAAO,UAAU,SAAS,CAAC;AAAA,OAAI,KAAK,OAAO,iBAAiB,QAAQ,OAAO,SAAS,SACnL,GAAG,KAAK,QAAQ,EAAE,MAAM,CAAC;AAAA,IAAC,IAAK;AAAA,EACvC;AACA,QAAM,sBAAsB,CAAC,UAAU;AACnC,UAAM,YAAY,aAAa;AAC/B,UAAM,SAAS,MAAM,kBAAkB,cAAc,MAAM,SAAS;AACpE,QAAI,wBACA,UACA,cACC,WAAW,aACP,MAAM,KAAK,UAAU,QAAQ,EAAE,KAAK,CAAC,MAAM,MAAM,MAAM,KACpD,OAAO,UAAU,SAAS,SAAS,mBAAmB,CAAC,IAAK;AACpE,YAAM;AAAA,IACV;AAAA,EACJ;AACA,SAAc,oBAAc,OAAO,EAAE,KAAK,cAAc,WAAW,KAAK,SAAS,eAAe,CAAC,GAAG,CAAC,aAAa,SAAS,eAAe,SAAS,CAAC,GAAG,SAAS,iBAAiB,CAAC,GAAG,GAAG,cAAc,SAAS,GAAG,SAAS,qBAAqB,OAAc,MAAM,UAAU,wBAAwB,QAAQ,GAAG,YAAY,CAAC;AAClU;AACA,SAAS,cAAc;AACnB,QAAM,QAAQ,iBAAiB,EAAE,OAAO;AACxC,SAAa,oBAAc,OAAO,EAAE,WAAW,SAAS,OAAO,GAAG,MAAa,CAAC;AACpF;AACA,SAAS,SAAS,EAAE,SAAS,GAAG;AAC5B,QAAM,EAAE,QAAQ,cAAc,YAAY,IAAI,iBAAiB;AAC/D,QAAM,EAAE,eAAe,IAAI,cAAc;AACzC,QAAM,eAAe,sBAAsB,SAAS,OAAO;AAC3D,QAAM,eAAe,sBAAsB,SAAS,OAAO;AAC3D,QAAM,UAAU,iBAAiB,UAAU,QAAQ,CAAC;AACpD,QAAM,QAAQ,CAAC;AACf,MAAI,UAAU,MAAM,GAAG;AACnB,aAAS,QAAQ,eAAe,SAAS,SAAS,eAAe,SAAS,SAAS,GAAG;AAClF,YAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,YAAM,MAAM,cAAc,eAAe;AACzC,YAAM,cAAc,SAAS,WAAW,QAAQ,KAAK,QAAQ,OAAO,SAAS;AAC7E,YAAM,KAAK,CAAC,cACN;AAAA,QACE,KAAK,CAAC,GAAG,GAAG,IAAI,YAAY,KAAK,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAA,QAC5D,QAAQ,QAAQ;AAAA,QAChB;AAAA,MACJ,IACE,EAAE,IAAI,CAAC;AAAA,IACjB;AAAA,EACJ;AACA,SAAc,oBAAc,OAAO,EAAE,KAAK,gBAAgB,WAAW,KAAK,SAAS,YAAY,CAAC,GAAG,MAAM,SAAS,KAAK,SAAS,YAAY,aAAa,CAAC,CAAC,GAAG,OAAO;AAAA,IAC7J,CAAC,GAAG,OAAO,YAAY,cAAc,CAAC,CAAC,EAAE,GAAG,MAAM;AAAA,IAClD,CAAC,GAAG,OAAO,YAAY,YAAY,CAAC,CAAC,EAAE,GAAG,aAAa,SAAS;AAAA,IAChE,CAAC,GAAG,OAAO,YAAY,iBAAiB,CAAC,CAAC,EAAE,GAAG,aAAa,WAAW;AAAA,IACvE,CAAC,GAAG,OAAO,YAAY,YAAY,CAAC,CAAC,EAAE,GAAG,aAAa,SAAS;AAAA,IAChE,CAAC,GAAG,OAAO,YAAY,iBAAiB,CAAC,CAAC,EAAE,GAAG,aAAa,WAAW;AAAA,EAC3E,EAAE,GAAG,MAAM,IAAI,CAAC,EAAE,KAAK,OAAO,OAAO,MAAM,QAAc,oBAAc,eAAe,EAAE,KAAU,OAAc,OAAe,CAAC,IAAU,oBAAc,aAAa,EAAE,IAAS,CAAC,CAAC,CAAC;AAC3L;AACA,IAAM,iBAAiB,aAAa,iBAAiB,QAAQ;AAE7D,SAAS,qBAAqB;AAC1B,QAAM,EAAE,SAAS,IAAI,iBAAiB;AACtC,QAAM,EAAE,QAAQ,aAAa,IAAI,iBAAiB;AAClD,QAAM,eAAe,OAAO,WAAW,KAAM,SAAS,UAAU,iBAAiB;AACjF,QAAM,eAAe,OAAO,WAAW,KAAM,SAAS,UAAU,iBAAiB,OAAO,SAAS;AACjG,SAAO,EAAE,cAAc,aAAa;AACxC;AAEA,SAAS,sBAAsB,kBAAkB;AAC7C,MAAI;AACJ,QAAM,QAAQ,OAAO;AACrB,QAAM,EAAE,QAAQ,IAAI,UAAU;AAC9B,QAAM,EAAE,UAAU,IAAI,iBAAiB;AACvC,QAAM,EAAE,cAAc,aAAa,IAAI,mBAAmB;AAC1D,QAAM,aAAa,KAAK,UAAU,gBAAgB,QAAQ,OAAO,SAAS,KAAK,UAAU,SAAS;AAClG,QAAM,OAAO,YAAY,MAAM,QAAQ,WAAW,GAAG,QAAQ;AAC7D,QAAM,OAAO,YAAY,MAAM,QAAQ,WAAW,GAAG,QAAQ;AAC7D,QAAM,gBAAgB,iBAAiB,CAAC,UAAU;AAC9C,YAAQ,MAAM,KAAK;AAAA,MACf,KAAK;AACD,gBAAQ,YAAY;AACpB;AAAA,MACJ,KAAK;AACD,YAAI,EAAE,QAAQ,eAAe;AACzB,WAAC,QAAQ,OAAO,MAAM;AAC1B;AAAA,MACJ,KAAK;AACD,YAAI,EAAE,QAAQ,eAAe;AACzB,WAAC,QAAQ,OAAO,MAAM;AAC1B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,EAAM,gBAAU,MAAM,iBAAiB,mBAAmB,aAAa,GAAG,CAAC,kBAAkB,aAAa,CAAC;AAC/G;AAEA,SAAS,iBAAiB,EAAE,OAAAI,QAAO,MAAM,YAAY,QAAQ,SAAS,UAAU,MAAM,GAAG;AACrF,SAAc,oBAAc,YAAY,EAAE,OAAOA,QAAO,MAAY,YAAwB,WAAW,SAAS,cAAc,MAAM,EAAE,GAAG,UAAoB,SAAkB,OAAc,GAAG,aAAa,cAAc,EAAE,OAAO,QAAQ,EAAE,CAAC;AACnP;AACA,SAAS,WAAW,EAAE,QAAQ,EAAE,YAAY,YAAY,UAAU,SAAS,GAAG,OAAO,GAAG;AACpF,QAAM,EAAE,MAAM,MAAM,iBAAiB,IAAI,cAAc;AACvD,QAAM,EAAE,cAAc,aAAa,IAAI,mBAAmB;AAC1D,wBAAsB,gBAAgB;AACtC,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,aAAc,WAAW,IAAY,oBAAc,kBAAkB,EAAE,OAAO,YAAY,QAAQ,aAAa,MAAM,cAAc,YAAY,UAAU,OAAO,OAAO,gBAAgB,UAAU,cAAc,SAAS,KAAK,CAAC;AAAA,IAC9N,aAAc,WAAW,IAAY,oBAAc,kBAAkB,EAAE,OAAO,QAAQ,QAAQ,aAAa,MAAM,UAAU,YAAY,UAAU,OAAO,OAAO,gBAAgB,UAAU,cAAc,SAAS,KAAK,CAAC;AAAA,EAAE;AAChO;AACA,IAAM,mBAAmB,aAAa,mBAAmB,UAAU;AAEnE,IAAM,WAAW,SAAS,eAAe;AACzC,IAAM,kBAAkB,SAAS,uBAAuB;AACxD,SAAS,cAAc,SAAS;AAC5B,SAAO,WAAW;AACtB;AACA,SAAS,aAAa,SAAS,SAAS,KAAK;AACzC,QAAM,SAAS,OAAO,iBAAiB,OAAO;AAC9C,QAAM,WAAW,MAAM,iBAAiB;AACxC,QAAM,gBAAgB,MAAM,OAAO,cAAc,OAAO;AACxD,QAAM,gBAAgB,QAAQ,MAAM,iBAAiB,QAAQ;AAC7D,UAAQ,MAAM,YAAY,UAAU,IAAI,SAAS,aAAa,KAAK,KAAK,OAAO,IAAI;AACnF,SAAO,MAAM;AACT,QAAI,eAAe;AACf,cAAQ,MAAM,YAAY,UAAU,aAAa;AAAA,IACrD,OACK;AACD,cAAQ,MAAM,eAAe,QAAQ;AAAA,IACzC;AAAA,EACJ;AACJ;AACA,SAAS,SAAS,EAAE,UAAU,EAAE,SAAS,GAAG,SAAS,GAAG;AACpD,QAAM,MAAM,OAAO;AACnB,QAAM,EAAE,kBAAkB,eAAe,IAAI,mBAAmB;AAChE,EAAM,gBAAU,MAAM;AAClB,QAAI;AACA,aAAO,MAAM;AAAA,MAAE;AACnB,UAAMC,WAAU,CAAC;AACjB,UAAM,cAAc,eAAe;AACnC,UAAM,EAAE,MAAM,gBAAgB,IAAI,iBAAiB;AACnD,UAAM,YAAY,KAAK,MAAM,YAAY,aAAa,gBAAgB,WAAW;AACjF,QAAI,YAAY,GAAG;AACf,MAAAA,SAAQ,KAAK,aAAa,MAAM,WAAW,GAAG,CAAC;AAC/C,YAAM,WAAW,KAAK,qBAAqB,GAAG;AAC9C,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AACzC,cAAM,UAAU,SAAS,CAAC;AAC1B,YAAI,cAAc,OAAO,KACrB,YAAY,iBAAiB,OAAO,EAAE,iBAAiB,UAAU,MAAM,WACvE,CAAC,QAAQ,UAAU,SAAS,eAAe,GAAG;AAC9C,UAAAA,SAAQ,KAAK,aAAa,SAAS,WAAW,GAAG,CAAC;AAAA,QACtD;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,UAAU,IAAI,QAAQ;AAC3B,WAAO,MAAM;AACT,WAAK,UAAU,OAAO,QAAQ;AAC9B,MAAAA,SAAQ,QAAQ,CAAC,UAAU,MAAM,CAAC;AAAA,IACtC;AAAA,EACJ,GAAG,CAAC,KAAK,UAAU,kBAAkB,cAAc,CAAC;AACpD,SAAa,oBAAoB,gBAAU,MAAM,QAAQ;AAC7D;AACA,IAAM,iBAAiB,aAAa,kBAAkB,QAAQ;AAE9D,SAAS,YAAY,OAAO;AACxB,SAAO,cAAc,eAAe,KAAK;AAC7C;AACA,SAAS,aAAa,SAAS,WAAW,OAAO;AAC7C,QAAM,gBAAgB,QAAQ,aAAa,SAAS;AACpD,UAAQ,aAAa,WAAW,KAAK;AACrC,SAAO,MAAM;AACT,QAAI,eAAe;AACf,cAAQ,aAAa,WAAW,aAAa;AAAA,IACjD,OACK;AACD,cAAQ,gBAAgB,SAAS;AAAA,IACrC;AAAA,EACJ;AACJ;AACA,SAAS,OAAO,EAAE,UAAU,WAAW,QAAQ,WAAW,IAAI,QAAQ,MAAM,GAAG;AAC3E,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAClD,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAClD,QAAMA,WAAgB,aAAO,CAAC,CAAC;AAC/B,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,EAAE,WAAW,IAAI,YAAY;AACnC,QAAM,EAAE,UAAU,IAAI,UAAU;AAChC,QAAM,eAAe,oBAAoB;AACzC,QAAM,oBAAoB,CAAC,eAAe,UAAU,OAAO;AAC3D,EAAM,gBAAU,MAAM;AAClB,eAAW,IAAI;AACf,WAAO,MAAM;AACT,iBAAW,KAAK;AAChB,iBAAW,KAAK;AAAA,IACpB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,gBAAgB,iBAAiB,MAAM;AACzC,IAAAA,SAAQ,QAAQ,QAAQ,CAAC,UAAU,MAAM,CAAC;AAC1C,IAAAA,SAAQ,UAAU,CAAC;AAAA,EACvB,CAAC;AACD,QAAM,cAAc,iBAAiB,MAAM;AACvC,QAAI;AACJ,eAAW,KAAK;AAChB,kBAAc;AACd,KAAC,KAAK,GAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AACjE,eAAW,MAAM;AACb,UAAIL;AACJ,OAACA,MAAK,GAAG,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,EAAE;AAChE,YAAM;AAAA,IACV,GAAG,iBAAiB;AAAA,EACxB,CAAC;AACD,EAAM,gBAAU,MAAM,UAAU,cAAc,WAAW,GAAG,CAAC,WAAW,WAAW,CAAC;AACpF,QAAM,cAAc,iBAAiB,CAAC,SAAS;AAC3C,QAAI,IAAI,IAAI;AACZ,SAAK;AACL,eAAW,IAAI;AACf,KAAC,KAAK,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAClE,UAAM,YAAY,MAAM,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,KAAK,CAAC;AAClI,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AACzC,YAAM,UAAU,SAAS,CAAC;AAC1B,UAAI,CAAC,YAAY,UAAU,OAAO,EAAE,QAAQ,QAAQ,OAAO,MAAM,MAAM,YAAY,MAAM;AACrF,QAAAK,SAAQ,QAAQ,KAAK,aAAa,SAAS,SAAS,EAAE,CAAC;AACvD,QAAAA,SAAQ,QAAQ,KAAK,aAAa,SAAS,eAAe,MAAM,CAAC;AAAA,MACrE;AAAA,IACJ;AACA,IAAAA,SAAQ,QAAQ,KAAK,MAAM;AACvB,UAAIL,KAAIM;AACR,OAACA,OAAMN,MAAK,aAAa,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQM,QAAO,SAAS,SAASA,IAAG,KAAKN,GAAE;AAAA,IACpI,CAAC;AACD,eAAW,MAAM;AACb,UAAIA;AACJ,OAACA,MAAK,GAAG,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,EAAE;AAAA,IACrE,GAAG,iBAAiB;AAAA,EACxB,CAAC;AACD,QAAM,YAAkB,kBAAY,CAAC,SAAS;AAC1C,QAAI,MAAM;AACN,kBAAY,IAAI;AAAA,IACpB,OACK;AACD,oBAAc;AAAA,IAClB;AAAA,EACJ,GAAG,CAAC,aAAa,aAAa,CAAC;AAC/B,SAAO,cACD,+BAAmB,oBAAc,cAAc,EAAE,KAAK,WAAW,WAAW,KAAK,WAAW,SAAS,YAAY,CAAC,GAAG,SAAS,uBAAuB,GAAG,WAAW,SAAS,YAAY,MAAM,CAAC,CAAC,GAAG,cAAc,MAAM,MAAM,UAAU,aAAa,UAAU,wBAAwB,YAAY,OAAO;AAAA,IACnS,GAAI,UAAU,SAAS,qBAAqB,UAAU,OAChD,EAAE,CAAC,OAAO,yBAAyB,CAAC,GAAG,GAAG,iBAAiB,KAAK,IAChE;AAAA,IACN,GAAI,UAAU,OAAO,SAAS,qBAAqB,UAAU,OAAO,OAC9D,EAAE,CAAC,OAAO,gCAAgC,CAAC,GAAG,UAAU,OAAO,KAAK,IACpE;AAAA,IACN,GAAG,OAAO;AAAA,EACd,GAAG,SAAS,CAAC,UAAU;AACnB,QAAI,CAAC,aAAa,SAAS;AACvB,mBAAa,UAAU,MAAM;AAAA,IACjC;AAAA,EACJ,EAAE,GAAG,QAAQ,GAAG,OAAO,QAAQ,SAAS,IAAI,IAC9C;AACV;AACA,IAAM,eAAe,aAAa,eAAe,MAAM;AAEvD,SAAS,KAAK,EAAE,SAAS,GAAG;AACxB,SAAa,oBAAoB,gBAAU,MAAM,QAAQ;AAC7D;AACA,IAAM,aAAa,aAAa,aAAa,IAAI;AAEjD,SAAS,UAAU,OAAO;AACtB,SAAO,cAAc,gBAAgB,KAAK;AAC9C;AACA,SAAS,QAAQ,EAAE,SAAS,EAAE,QAAQ,GAAG,QAAQ,EAAE,aAAa,UAAU,GAAG,OAAO,GAAG;AACnF,QAAM,EAAE,OAAO,gBAAgB,IAAI,cAAc;AACjD,QAAM,EAAE,iBAAiB,cAAc,IAAI,iBAAiB;AAC5D,EAAAD,iBAAgB,MAAM;AAClB,oBAAgB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,KAAK;AAAA,EACrG,GAAG,CAAC,iBAAiB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,KAAK,CAAC;AACvG,QAAM,oBAAoB,MAAM;AAC5B,QAAI;AACA,aAAO,YAAY;AACvB,WAAa,oBAAc,YAAY,EAAE,KAAK,cAAc,OAAO,SAAS,MAAM,WAAW,YAAY,WAAW,SAAS,MAAM,CAAC;AAAA,EACxI;AACA,SAAc,oBAAc,OAAO,EAAE,KAAK,iBAAiB,OAAO,OAAO,SAAS,WAAW,SAAS,UAAU,CAAC,EAAE,GAAG,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI,CAAC,WAAY,WAAW,eAAe,kBAAkB,IAAI,MAAO,CAAC;AAC7P;AACA,IAAM,gBAAgB,aAAa,gBAAgB,OAAO;AAE1D,SAAS,WAAW,MAAM,OAAO;AAC7B,MAAI;AACJ,SAAa,oBAAc,KAAK,OAAO,WAAW,EAAE,KAAK,KAAK,OAAO,MAAM,GAAG,MAAM,IAAI,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,CAAC,UAAU,WAAW,OAAO,KAAK,CAAC,CAAC;AAChM;AACA,SAAS,eAAe,kBAAkB,YAAY,CAAC,GAAG;AACtD,QAAM,EAAE,QAAQ,wBAAwB,GAAG,qBAAqB,IAAI;AACpE,QAAM,EAAE,QAAQ,GAAG,cAAc,IAAI;AACrC,SAAO;AAAA,IACH,QAAQ,EAAE,GAAG,wBAAwB,GAAG,OAAO;AAAA,IAC/C,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AACJ;AACA,SAAS,SAAS,EAAE,UAAU,WAAW,QAAQ,SAAS,YAAY,UAAAQ,WAAU,IAAI,SAAS,QAAQ,OAAO,GAAG,UAAU,GAAG;AACxH,QAAM,EAAE,WAAW,kBAAkB,UAAU,iBAAiB,QAAQ,eAAe,SAAS,gBAAgB,YAAY,mBAAmB,UAAU,iBAAiB,IAAI,WAAW,QAAQ,eAAe,OAAO,cAAc,SAAS,gBAAgB,GAAG,iBAAiB,IAAI;AACtR,QAAM,EAAE,QAAQ,aAAa,IAAI,YAAY;AAAA,IACzC,WAAW,cAAc;AAAA,MACrB,WAAW,gBAAgB;AAAA,QACvB,WAAW,kBAAkB;AAAA,UACzB,WAAW,cAAc;AAAA,UACzB,WAAW,aAAa;AAAA,UACxB,WAAW,gBAAgB;AAAA,QAC/B,CAAC;AAAA,MACL,CAAC;AAAA,IACL,CAAC;AAAA,EACL,GAAG,WAAW,cAAc;AAC5B,QAAM,QAAQ,aAAa;AAAA,IACvB,WAAW,eAAe,kBAAkB,SAAS;AAAA,IACrD,UAAU,EAAE,GAAG,iBAAiB,GAAG,SAAS;AAAA,IAC5C,QAAQ,EAAE,GAAG,eAAe,GAAG,OAAO;AAAA,IACtC,SAAS,EAAE,GAAG,gBAAgB,GAAG,QAAQ;AAAA,IACzC,YAAY,EAAE,GAAG,mBAAmB,GAAG,WAAW;AAAA,IAClD,UAAU,EAAE,GAAG,iBAAiB,GAAGA,UAAS;AAAA,IAC5C,IAAI,EAAE,GAAG,WAAW,GAAG,GAAG;AAAA,IAC1B,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACD,MAAI,CAAC,MAAM;AACP,WAAO;AACX,SAAc;AAAA,IAAc;AAAA,IAAuB,EAAE,GAAG,MAAM;AAAA,IACpD;AAAA,MAAc;AAAA,MAAuB,EAAE,QAAQ,UAAU,eAAe,OAAO,SAAS,SAAS,YAAY,EAAE;AAAA,MAC3G;AAAA,QAAc;AAAA,QAAkB;AAAA,QAC5B,oBAAc,gBAAgB,MAAM,WAAW,WAAW,YAAY,MAAM,GAAG,KAAK,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAC9G;", "names": ["IconButton", "useLayoutEffect", "_a", "LightboxRoot", "SwipeState", "Gesture", "label", "cleanup", "_b", "noScroll"]}