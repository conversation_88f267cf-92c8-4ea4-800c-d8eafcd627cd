{"version": 3, "sources": ["../../@mui/material/ButtonGroup/ButtonGroupContext.js", "../../@mui/material/ButtonGroup/ButtonGroupButtonContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupContext.displayName = 'ButtonGroupContext';\n}\nexport default ButtonGroupContext;", "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ButtonGroupButtonContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ButtonGroupButtonContext.displayName = 'ButtonGroupButtonContext';\n}\nexport default ButtonGroupButtonContext;"], "mappings": ";;;;;;;;AAEA,YAAuB;AAIvB,IAAM,qBAAwC,oBAAc,CAAC,CAAC;AAC9D,IAAI,MAAuC;AACzC,qBAAmB,cAAc;AACnC;AACA,IAAO,6BAAQ;;;ACRf,IAAAA,SAAuB;AAIvB,IAAM,2BAA8C,qBAAc,MAAS;AAC3E,IAAI,MAAuC;AACzC,2BAAyB,cAAc;AACzC;AACA,IAAO,mCAAQ;", "names": ["React"]}