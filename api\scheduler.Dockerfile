###################
# BUILD FOR LOCAL DEVELOPMENT
###################

FROM node:22.17.0-alpine3.22  As development

WORKDIR /usr/src/app

COPY --chown=node:node package*.json ./

# Note: this installs the necessary libs to make the bundled version of Chromium that Puppeteer
# installs, work.
RUN apk update && apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true
ENV PUPPETEER_EXECUTABLE_PATH /usr/bin/chromium-browser

RUN npm ci --legacy-peer-deps

COPY --chown=node:node . .

USER node

###################
# BUILD FOR PRODUCTION
###################

FROM node:22.17.0-alpine3.22  As build

WORKDIR /usr/src/app

COPY --chown=node:node package*.json ./

COPY --chown=node:node --from=development /usr/src/app/node_modules ./node_modules

COPY --chown=node:node . .

RUN npm install -g @nestjs/cli

RUN npm run build

ENV NODE_ENV production

# Note: this installs the necessary libs to make the bundled version of Chromium that Puppeteer
# installs, work.
RUN apk update && apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true
ENV PUPPETEER_EXECUTABLE_PATH /usr/bin/chromium-browser

RUN npm ci --only=production --legacy-peer-deps && npm cache clean --force

USER node

###################
# PRODUCTION
###################

FROM node:22.17.0-alpine3.22 As production

COPY --chown=node:node --from=build /usr/src/app/node_modules ./node_modules
COPY --chown=node:node --from=build /usr/src/app/dist ./dist

# Note: this installs the necessary libs to make the bundled version of Chromium that Puppeteer
# installs, work.
RUN apk update && apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD true
ENV PUPPETEER_EXECUTABLE_PATH /usr/bin/chromium-browser

CMD [ "node", "dist/scheduler.js" ]