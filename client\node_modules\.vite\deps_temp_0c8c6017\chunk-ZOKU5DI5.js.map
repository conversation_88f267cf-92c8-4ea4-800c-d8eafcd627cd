{"version": 3, "sources": ["../../@mui/material/transitions/utils.js"], "sourcesContent": ["export const reflow = node => node.scrollTop;\nexport function getTransitionProps(props, options) {\n  const {\n    timeout,\n    easing,\n    style = {}\n  } = props;\n  return {\n    duration: style.transitionDuration ?? (typeof timeout === 'number' ? timeout : timeout[options.mode] || 0),\n    easing: style.transitionTimingFunction ?? (typeof easing === 'object' ? easing[options.mode] : easing),\n    delay: style.transitionDelay\n  };\n}"], "mappings": ";AAAO,IAAM,SAAS,UAAQ,KAAK;AAC5B,SAAS,mBAAmB,OAAO,SAAS;AACjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,QAAQ,CAAC;AAAA,EACX,IAAI;AACJ,SAAO;AAAA,IACL,UAAU,MAAM,uBAAuB,OAAO,YAAY,WAAW,UAAU,QAAQ,QAAQ,IAAI,KAAK;AAAA,IACxG,QAAQ,MAAM,6BAA6B,OAAO,WAAW,WAAW,OAAO,QAAQ,IAAI,IAAI;AAAA,IAC/F,OAAO,MAAM;AAAA,EACf;AACF;", "names": []}