"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactDetailModule = void 0;
const common_1 = require("@nestjs/common");
const controllers_1 = require("./controllers");
const repositories_1 = require("./repositories");
const services_1 = require("./services");
const services_2 = require("../business-entity/services");
const clients_1 = require("../shared/clients");
const repositories_2 = require("../metadata/repositories");
const services_3 = require("../shared/services");
const repositories_3 = require("../location/repositories");
const repositories_4 = require("../permission/repositories");
const helpers_1 = require("../shared/helpers");
const repositories = [
    repositories_1.ContactDetailRepository,
    repositories_2.CoreSolutionRepository,
    repositories_3.CountryRepository,
    repositories_3.LocationRepository,
    repositories_4.UserPermissionRepository
];
let ContactDetailModule = class ContactDetailModule {
};
ContactDetailModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.ContactDetailController],
        providers: [
            ...repositories,
            services_1.ContactDetailService,
            services_2.BusinessEntityService,
            clients_1.AdminApiClient,
            services_3.SharedPermissionService,
            clients_1.HistoryApiClient,
            helpers_1.DatabaseHelper
        ],
    })
], ContactDetailModule);
exports.ContactDetailModule = ContactDetailModule;
//# sourceMappingURL=contact-detail.module.js.map