"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreModule = void 0;
const common_1 = require("@nestjs/common");
const config_service_1 = require("../config/config.service");
const constants_1 = require("../shared/constants");
const providers_1 = require("./providers");
const interceptors_1 = require("./interceptors");
const services_1 = require("./services");
const repositories_1 = require("../permission/repositories");
let CoreModule = class CoreModule {
};
CoreModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [
            interceptors_1.LoggingInterceptor,
            services_1.LoggerService,
            common_1.ConsoleLogger,
            config_service_1.ConfigService,
            ...providers_1.InternalApiProviders,
            ...providers_1.MSGraphProviders,
            repositories_1.UserPermissionRepository
        ],
        exports: [
            services_1.LoggerService,
            constants_1.INTERNAL_API.ADMIN_API_PROVIDER,
            constants_1.INTERNAL_API.REQUEST_API_PROVIDER,
            constants_1.INTERNAL_API.NOTIFICATION_API_PROVIDER,
            constants_1.MS_GRAPH_API.MS_GRAPH_API_PROVIDER,
            constants_1.MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER,
        ],
    })
], CoreModule);
exports.CoreModule = CoreModule;
//# sourceMappingURL=core.module.js.map