"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonDropdownRepository = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../shared/repositories");
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
const enums_1 = require("../../shared/enums");
let CommonDropdownRepository = class CommonDropdownRepository extends repositories_1.BaseRepository {
    constructor() {
        super(models_1.CommonDropdown);
    }
    setupNewMetadataOfCommonTypes(payload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const newMetadata = new models_1.CommonDropdown(payload);
            return this.save(newMetadata, currentContext);
        });
    }
    getDropdownList(filter) {
        const typeWithCoreSolution = filter.type.includes(enums_1.COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES)
            ? [enums_1.COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES]
            : [];
        let listWithOnlyType = filter.type.filter(type => ![
            enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION,
            enums_1.COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES,
        ].includes(type));
        const orConditions = [];
        if (typeWithCoreSolution.length > 0 && filter.coreSolutionId) {
            orConditions.push({
                type: { [sequelize_1.Op.in]: typeWithCoreSolution },
                coreSolutionId: filter.coreSolutionId,
            });
        }
        if (listWithOnlyType.length > 0) {
            if (listWithOnlyType.includes(enums_1.COMMON_DROPDOWN_TYPE.ALL_STRATEGIC_CLASSIFICATION)) {
                listWithOnlyType = listWithOnlyType.filter(type => type !== enums_1.COMMON_DROPDOWN_TYPE.ALL_STRATEGIC_CLASSIFICATION);
                listWithOnlyType.push(enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION);
            }
            orConditions.push({
                type: { [sequelize_1.Op.in]: listWithOnlyType },
            });
        }
        return this.findAll({
            where: {
                [sequelize_1.Op.or]: orConditions,
            },
            order: [['title', 'asc']],
        });
    }
    getStrategicClassificationType(coreSolutionId, locationTypeId) {
        return __awaiter(this, void 0, void 0, function* () {
            const strategicClassificationData = yield this.findAll({
                where: {
                    type: enums_1.COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION,
                    coreSolutionId: { [sequelize_1.Op.or]: [coreSolutionId, null] },
                    locationTypeId: { [sequelize_1.Op.or]: [locationTypeId, null] },
                },
                order: [['title', 'asc']],
            });
            const locationOverridenData = strategicClassificationData.filter(item => item.locationTypeId === locationTypeId);
            const commonList = strategicClassificationData.filter(item => item.coreSolutionId === null);
            if (locationOverridenData.length > 0) {
                return [...locationOverridenData, ...commonList];
            }
            return strategicClassificationData.filter(item => item.locationTypeId === null);
        });
    }
    isDropdownRecordExists(condition) {
        return this.isRecordExist({ where: condition });
    }
};
CommonDropdownRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], CommonDropdownRepository);
exports.CommonDropdownRepository = CommonDropdownRepository;
//# sourceMappingURL=common-dropdown.repository.js.map