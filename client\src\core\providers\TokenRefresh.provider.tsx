import React, { useContext, useEffect } from 'react';
import { useTokenRefresh } from '@/hooks/use-token-refresh';
import { useMsal } from '@azure/msal-react';
import { AppConfigContext } from '../contexts';

interface TokenRefreshProviderProps {
  children: React.ReactNode;
}

const TokenRefreshProvider: React.FC<TokenRefreshProviderProps> = ({ children }) => {
  const appConfig = useContext(AppConfigContext);
  const { accounts } = useMsal();

  // Initialize token refresh service
  const tokenRefresh = useTokenRefresh({
    scopes: appConfig.msDetail?.scope || [],
    renewalOffsetMinutes: 5, // Refresh 5 minutes before expiry
    maxRetryAttempts: 3,
    retryDelayMs: 2000,
    autoStart: true,
  });

  // Log token refresh events for debugging
  useEffect(() => {
    if (tokenRefresh.lastRefreshTime) {
      console.log('Token refreshed at:', tokenRefresh.lastRefreshTime);
    }
  }, [tokenRefresh.lastRefreshTime]);

  // Log when token is near expiry
  useEffect(() => {
    if (tokenRefresh.isTokenNearExpiry && !tokenRefresh.isRefreshing) {
      console.warn('Token is near expiry:', tokenRefresh.formattedTimeUntilExpiry);
    }
  }, [tokenRefresh.isTokenNearExpiry, tokenRefresh.isRefreshing, tokenRefresh.formattedTimeUntilExpiry]);

  // Handle account changes
  useEffect(() => {
    if (accounts.length === 0) {
      console.log('No accounts available, stopping token refresh monitoring');
      tokenRefresh.stopMonitoring();
    } else {
      console.log('Account available, ensuring token refresh monitoring is active');
      tokenRefresh.startMonitoring();
    }
  }, [accounts.length, tokenRefresh]);

  return <>{children}</>;
};

export default TokenRefreshProvider;
