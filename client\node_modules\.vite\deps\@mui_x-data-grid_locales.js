import {
  arSD,
  bgBG,
  bnBD,
  csCZ,
  daDK,
  deDE,
  elGR,
  enUS,
  esES,
  faIR,
  fiFI,
  frFR,
  heIL,
  hrHR,
  huHU,
  hyAM,
  isIS,
  itIT,
  jaJP,
  koKR,
  nbNO,
  nlNL,
  nnNO,
  plPL,
  ptBR,
  ptPT,
  roRO,
  ruRU,
  skSK,
  svSE,
  trTR,
  ukUA,
  viVN,
  zhCN,
  zhHK,
  zhTW
} from "./chunk-KJPZKBUI.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import "./chunk-EWTE5DHJ.js";

// node_modules/@mui/x-data-grid/utils/getGridLocalization.js
var getGridLocalization = (gridTranslations, coreTranslations) => {
  var _a, _b;
  return {
    components: {
      MuiDataGrid: {
        defaultProps: {
          localeText: _extends({}, gridTranslations, {
            MuiTablePagination: ((_b = (_a = coreTranslations == null ? void 0 : coreTranslations.components) == null ? void 0 : _a.MuiTablePagination) == null ? void 0 : _b.defaultProps) || {}
          })
        }
      }
    }
  };
};

// node_modules/@mui/x-data-grid/locales/arSD.js
var arSDGrid = {
  // Root
  noRowsLabel: "لا توجد صفوف",
  noResultsOverlayLabel: "لم يتم العثور على نتائج.",
  // Density selector toolbar button text
  toolbarDensity: "الكثافة",
  toolbarDensityLabel: "الكثافة",
  toolbarDensityCompact: "مضغوط",
  toolbarDensityStandard: "قياسي",
  toolbarDensityComfortable: "مريح",
  // Columns selector toolbar button text
  toolbarColumns: "الأعمدة",
  toolbarColumnsLabel: "حدد أعمدة",
  // Filters toolbar button text
  toolbarFilters: "المُرشِحات",
  toolbarFiltersLabel: "إظهار المرشِحات",
  toolbarFiltersTooltipHide: "إخفاء المرشِحات",
  toolbarFiltersTooltipShow: "اظهر المرشِحات",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} من المرشِحات النشطة` : `مرشِح نشط`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "بحث...",
  toolbarQuickFilterLabel: "بحث",
  toolbarQuickFilterDeleteIconLabel: "أزال",
  // Export selector toolbar button text
  toolbarExport: "تصدير",
  toolbarExportLabel: "تصدير",
  toolbarExportCSV: "تنزيل كملف CSV",
  toolbarExportPrint: "طباعة",
  toolbarExportExcel: "تحميل كملف الإكسل",
  // Columns management text
  // columnsManagementSearchTitle: 'Search',
  // columnsManagementNoColumns: 'No columns',
  // columnsManagementShowHideAllText: 'Show/Hide All',
  // columnsManagementReset: 'Reset',
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "إضافة مرشِح",
  filterPanelRemoveAll: "حذف الكل",
  filterPanelDeleteIconLabel: "حذف",
  filterPanelLogicOperator: "عامل منطقي",
  filterPanelOperator: "عامل",
  filterPanelOperatorAnd: "و",
  filterPanelOperatorOr: "أو",
  filterPanelColumns: "الأعمدة",
  filterPanelInputLabel: "القيمة",
  filterPanelInputPlaceholder: "ترشِيح قيمة",
  // Filter operators text
  filterOperatorContains: "يحتوي",
  // filterOperatorDoesNotContain: 'does not contain',
  filterOperatorEquals: "يساوي",
  // filterOperatorDoesNotEqual: 'does not equal',
  filterOperatorStartsWith: "يبدأ بـ",
  filterOperatorEndsWith: "ينتهي بـ",
  filterOperatorIs: "يكون",
  filterOperatorNot: "ليس",
  filterOperatorAfter: "بعد",
  filterOperatorOnOrAfter: "عند أو بعد",
  filterOperatorBefore: "قبل",
  filterOperatorOnOrBefore: "عند أو قبل",
  filterOperatorIsEmpty: "خالي",
  filterOperatorIsNotEmpty: "غير خالي",
  filterOperatorIsAnyOf: "أي من",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "يحتوي على",
  // headerFilterOperatorDoesNotContain: 'Does not contain',
  headerFilterOperatorEquals: "يساوي",
  // headerFilterOperatorDoesNotEqual: 'Does not equal',
  headerFilterOperatorStartsWith: "يبدأ ب",
  headerFilterOperatorEndsWith: "ينتهي ب",
  headerFilterOperatorIs: "هو",
  headerFilterOperatorNot: "هو ليس",
  headerFilterOperatorAfter: "يقع بعد",
  headerFilterOperatorOnOrAfter: "هو على او بعد",
  headerFilterOperatorBefore: "يقع قبل",
  headerFilterOperatorOnOrBefore: "هو على او بعد",
  headerFilterOperatorIsEmpty: "هو فارغ",
  headerFilterOperatorIsNotEmpty: "هو ليس فارغ",
  headerFilterOperatorIsAnyOf: "هو أي من",
  "headerFilterOperator=": "يساوي",
  "headerFilterOperator!=": "لا يساوي",
  "headerFilterOperator>": "أكبر من",
  "headerFilterOperator>=": "أكبر من او يساوي",
  "headerFilterOperator<": "اصغر من",
  "headerFilterOperator<=": "اصغر من او يساوي",
  // Filter values text
  filterValueAny: "أي",
  filterValueTrue: "صائب",
  filterValueFalse: "خاطئ",
  // Column menu text
  columnMenuLabel: "القائمة",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "إظهار الأعمدة",
  columnMenuManageColumns: "إدارة الأعمدة",
  columnMenuFilter: "المرشِح",
  columnMenuHideColumn: "إخفاء",
  columnMenuUnsort: "الغاء الفرز",
  columnMenuSortAsc: "الفرز تصاعدياً",
  columnMenuSortDesc: "الفرز تنازلياً",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} من المرشِحات النشطة` : `مرشِح نشط`,
  columnHeaderFiltersLabel: "إظهار المرشحات",
  columnHeaderSortIconLabel: "فرز",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `تم تحديد ${count.toLocaleString()} من الصفوف` : `تم تحديد صف واحد`,
  // Total row amount footer text
  footerTotalRows: "إجمالي الصفوف:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} من ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "تحديد خانة الاختيار",
  checkboxSelectionSelectAllRows: "تحديد كل الصفوف",
  checkboxSelectionUnselectAllRows: "الغاء تحديد كل الصفوف",
  checkboxSelectionSelectRow: "تحديد صف",
  checkboxSelectionUnselectRow: "الغاء تحديد الصف",
  // Boolean cell text
  booleanCellTrueLabel: "نعم",
  booleanCellFalseLabel: "لا",
  // Actions cell more text
  actionsCellMore: "المزيد",
  // Column pinning text
  pinToLeft: "التدبيس يميناً",
  pinToRight: "التدبيس يساراً",
  unpin: "الغاء التدبيس",
  // Tree Data
  treeDataGroupingHeaderName: "تجميع",
  treeDataExpand: "رؤية الأبناء",
  treeDataCollapse: "إخفاء الأبناء",
  // Grouping columns
  groupingColumnHeaderName: "تجميع",
  groupColumn: (name) => `تجميع حسب ${name}`,
  unGroupColumn: (name) => `إيقاف التجميع حسب ${name}`,
  // Master/detail
  detailPanelToggle: "اظهار/اخفاء لوحة التفاصيل",
  expandDetailPanel: "توسيع",
  collapseDetailPanel: "طوي",
  // Row reordering text
  rowReorderingHeaderName: "أعادة ترتيب الصفوف",
  // Aggregation
  aggregationMenuItemHeader: "الدلالات الحسابية",
  aggregationFunctionLabelSum: "مجموع",
  aggregationFunctionLabelAvg: "معدل",
  aggregationFunctionLabelMin: "الحد الادنى",
  aggregationFunctionLabelMax: "الحد الاقصى",
  aggregationFunctionLabelSize: "الحجم"
};
var arSD2 = getGridLocalization(arSDGrid, arSD);

// node_modules/@mui/x-data-grid/locales/coreLocales.js
var beBYCore = {
  components: {
    MuiTablePagination: {
      defaultProps: {
        getItemAriaLabel: (type) => {
          if (type === "first") {
            return "Перайсці на першую старонку";
          }
          if (type === "last") {
            return "Перайсці на апошнюю старонку";
          }
          if (type === "next") {
            return "Перайсці на наступную старонку";
          }
          return "Перайсці на папярэднюю старонку";
        },
        labelRowsPerPage: "Радкоў на старонцы:",
        labelDisplayedRows: ({
          from,
          to,
          count
        }) => `${from}–${to} з ${count !== -1 ? count : `больш чым ${to}`}`
      }
    }
  }
};
var urPKCore = {
  components: {
    MuiTablePagination: {
      defaultProps: {
        getItemAriaLabel: (type) => {
          if (type === "first") {
            return "پہلے صفحے پر جائیں";
          }
          if (type === "last") {
            return "آخری صفحے پر جائیں";
          }
          if (type === "next") {
            return "اگلے صفحے پر جائیں";
          }
          return "پچھلے صفحے پر جائیں";
        },
        labelRowsPerPage: "ایک صفحے پر قطاریں:",
        labelDisplayedRows: ({
          from,
          to,
          count
        }) => `${count !== -1 ? `${count} میں سے` : `${to} سے ذیادہ میں سے`} ${from} سے ${to} قطاریں`
      }
    }
  }
};

// node_modules/@mui/x-data-grid/locales/beBY.js
var getPluralForm = (count, options) => {
  let pluralForm = options.many;
  const lastDigit = count % 10;
  if (lastDigit > 1 && lastDigit < 5 && (count < 10 || count > 20)) {
    pluralForm = options.few;
  } else if (lastDigit === 1 && count % 100 !== 11) {
    pluralForm = options.one;
  }
  return `${count} ${pluralForm}`;
};
var beBYGrid = {
  // Root
  noRowsLabel: "Няма радкоў",
  noResultsOverlayLabel: "Дадзеныя не знойдзены.",
  // Density selector toolbar button text
  toolbarDensity: "Вышыня радка",
  toolbarDensityLabel: "Вышыня радка",
  toolbarDensityCompact: "Кампактны",
  toolbarDensityStandard: "Стандартны",
  toolbarDensityComfortable: "Камфортны",
  // Columns selector toolbar button text
  toolbarColumns: "Слупкі",
  toolbarColumnsLabel: "Выберыце слупкі",
  // Filters toolbar button text
  toolbarFilters: "Фільтры",
  toolbarFiltersLabel: "Паказаць фільтры",
  toolbarFiltersTooltipHide: "Схаваць фільтры",
  toolbarFiltersTooltipShow: "Паказаць фільтры",
  toolbarFiltersTooltipActive: (count) => getPluralForm(count, {
    one: "актыўны фільтр",
    few: "актыўных фільтра",
    many: "актыўных фільтраў"
  }),
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Пошук…",
  toolbarQuickFilterLabel: "Пошук",
  toolbarQuickFilterDeleteIconLabel: "Ачысціць",
  // Export selector toolbar button text
  toolbarExport: "Экспарт",
  toolbarExportLabel: "Экспарт",
  toolbarExportCSV: "Спампаваць у фармаце CSV",
  toolbarExportPrint: "Друк",
  toolbarExportExcel: "Спампаваць у фармаце Excel",
  // Columns management text
  // columnsManagementSearchTitle: 'Search',
  // columnsManagementNoColumns: 'No columns',
  // columnsManagementShowHideAllText: 'Show/Hide All',
  // columnsManagementReset: 'Reset',
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Дадаць фільтр",
  // filterPanelRemoveAll: 'Remove all',
  filterPanelDeleteIconLabel: "Выдаліць",
  filterPanelLogicOperator: "Лагічныя аператары",
  filterPanelOperator: "Аператары",
  filterPanelOperatorAnd: "І",
  filterPanelOperatorOr: "Або",
  filterPanelColumns: "Слупкі",
  filterPanelInputLabel: "Значэнне",
  filterPanelInputPlaceholder: "Значэнне фільтра",
  // Filter operators text
  filterOperatorContains: "змяшчае",
  // filterOperatorDoesNotContain: 'does not contain',
  filterOperatorEquals: "роўны",
  // filterOperatorDoesNotEqual: 'does not equal',
  filterOperatorStartsWith: "пачынаецца з",
  filterOperatorEndsWith: "скончваецца на",
  filterOperatorIs: "роўны",
  filterOperatorNot: "не роўны",
  filterOperatorAfter: "больш чым",
  filterOperatorOnOrAfter: "больш ці роўны",
  filterOperatorBefore: "меньш чым",
  filterOperatorOnOrBefore: "меньш ці роўны",
  filterOperatorIsEmpty: "пусты",
  filterOperatorIsNotEmpty: "не пусты",
  filterOperatorIsAnyOf: "усякі з",
  // 'filterOperator=': '=',
  // 'filterOperator!=': '!=',
  // 'filterOperator>': '>',
  // 'filterOperator>=': '>=',
  // 'filterOperator<': '<',
  // 'filterOperator<=': '<=',
  // Header filter operators text
  // headerFilterOperatorContains: 'Contains',
  // headerFilterOperatorDoesNotContain: 'Does not contain',
  // headerFilterOperatorEquals: 'Equals',
  // headerFilterOperatorDoesNotEqual: 'Does not equal',
  // headerFilterOperatorStartsWith: 'Starts with',
  // headerFilterOperatorEndsWith: 'Ends with',
  // headerFilterOperatorIs: 'Is',
  // headerFilterOperatorNot: 'Is not',
  // headerFilterOperatorAfter: 'Is after',
  // headerFilterOperatorOnOrAfter: 'Is on or after',
  // headerFilterOperatorBefore: 'Is before',
  // headerFilterOperatorOnOrBefore: 'Is on or before',
  // headerFilterOperatorIsEmpty: 'Is empty',
  // headerFilterOperatorIsNotEmpty: 'Is not empty',
  // headerFilterOperatorIsAnyOf: 'Is any of',
  // 'headerFilterOperator=': 'Equals',
  // 'headerFilterOperator!=': 'Not equals',
  // 'headerFilterOperator>': 'Greater than',
  // 'headerFilterOperator>=': 'Greater than or equal to',
  // 'headerFilterOperator<': 'Less than',
  // 'headerFilterOperator<=': 'Less than or equal to',
  // Filter values text
  filterValueAny: "усякі",
  filterValueTrue: "праўда",
  filterValueFalse: "няпраўда",
  // Column menu text
  columnMenuLabel: "Меню",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Паказаць слупкі",
  columnMenuManageColumns: "Кіраваць слупкамі",
  columnMenuFilter: "Фільтр",
  columnMenuHideColumn: "Схаваць",
  columnMenuUnsort: "Скасаваць сартыроўку",
  columnMenuSortAsc: "Сартыраваць па нарастанню",
  columnMenuSortDesc: "Сартыраваць па спаданню",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => getPluralForm(count, {
    one: "актыўны фільтр",
    few: "актыўных фільтра",
    many: "актыўных фільтраў"
  }),
  columnHeaderFiltersLabel: "Паказаць фільтры",
  columnHeaderSortIconLabel: "Сартыраваць",
  // Rows selected footer text
  footerRowSelected: (count) => getPluralForm(count, {
    one: "абраны радок",
    few: "абраных радка",
    many: "абраных радкоў"
  }),
  // Total row amount footer text
  footerTotalRows: "Усяго радкоў:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} з ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Выбар сцяжка",
  checkboxSelectionSelectAllRows: "Абраць усе радкі",
  checkboxSelectionUnselectAllRows: "Скасаваць выбар усіх радкоў",
  checkboxSelectionSelectRow: "Абраць радок",
  checkboxSelectionUnselectRow: "Скасаваць выбар радка",
  // Boolean cell text
  booleanCellTrueLabel: "праўда",
  booleanCellFalseLabel: "няпраўда",
  // Actions cell more text
  actionsCellMore: "больш",
  // Column pinning text
  pinToLeft: "Замацаваць злева",
  pinToRight: "Замацаваць справа",
  unpin: "Адмацаваць",
  // Tree Data
  treeDataGroupingHeaderName: "Група",
  treeDataExpand: "паказаць даччыныя элементы",
  treeDataCollapse: "схаваць даччыныя элементы",
  // Grouping columns
  groupingColumnHeaderName: "Група",
  groupColumn: (name) => `Групаваць па ${name}`,
  unGroupColumn: (name) => `Разгрупаваць па ${name}`,
  // Master/detail
  detailPanelToggle: "Дэталі",
  expandDetailPanel: "Разгарнуць",
  collapseDetailPanel: "Згарнуць",
  // Row reordering text
  rowReorderingHeaderName: "Змяненне чарговасці радкоў",
  // Aggregation
  aggregationMenuItemHeader: "Аб'яднанне дадзеных",
  aggregationFunctionLabelSum: "сума",
  aggregationFunctionLabelAvg: "сярэдняе",
  aggregationFunctionLabelMin: "мінімум",
  aggregationFunctionLabelMax: "максімум",
  aggregationFunctionLabelSize: "памер"
};
var beBY = getGridLocalization(beBYGrid, beBYCore);

// node_modules/@mui/x-data-grid/locales/bgBG.js
var bgBGGrid = {
  // Root
  noRowsLabel: "Няма редове",
  noResultsOverlayLabel: "Няма намерени резултати.",
  // Density selector toolbar button text
  toolbarDensity: "Гъстота",
  toolbarDensityLabel: "Гъстота",
  toolbarDensityCompact: "Компактна",
  toolbarDensityStandard: "Стандартна",
  toolbarDensityComfortable: "Комфортна",
  // Columns selector toolbar button text
  toolbarColumns: "Колони",
  toolbarColumnsLabel: "Покажи селектора на колони",
  // Filters toolbar button text
  toolbarFilters: "Филтри",
  toolbarFiltersLabel: "Покажи Филтрите",
  toolbarFiltersTooltipHide: "Скрий Филтрите",
  toolbarFiltersTooltipShow: "Покажи Филтрите",
  toolbarFiltersTooltipActive: (count) => `${count} активни филтри`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Търси…",
  toolbarQuickFilterLabel: "Търсене",
  toolbarQuickFilterDeleteIconLabel: "Изчисти",
  // Export selector toolbar button text
  toolbarExport: "Изтегли",
  toolbarExportLabel: "Изтегли",
  toolbarExportCSV: "Изтегли като CSV",
  toolbarExportPrint: "Принтиране",
  toolbarExportExcel: "Изтегли като Excel",
  // Columns management text
  columnsManagementSearchTitle: "Търсене",
  columnsManagementNoColumns: "Няма колони",
  columnsManagementShowHideAllText: "Покажи/Скрий Всичко",
  columnsManagementReset: "Нулирай",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Добави Филтър",
  filterPanelRemoveAll: "Премахни всички",
  filterPanelDeleteIconLabel: "Изтрий",
  filterPanelLogicOperator: "Логически оператор",
  filterPanelOperator: "Оператори",
  filterPanelOperatorAnd: "И",
  filterPanelOperatorOr: "Или",
  filterPanelColumns: "Колони",
  filterPanelInputLabel: "Стойност",
  filterPanelInputPlaceholder: "Стойност на филтъра",
  // Filter operators text
  filterOperatorContains: "съдържа",
  filterOperatorDoesNotContain: "не съдържа",
  filterOperatorEquals: "равно",
  filterOperatorDoesNotEqual: "не е равно",
  filterOperatorStartsWith: "започва с",
  filterOperatorEndsWith: "завършва с",
  filterOperatorIs: "е",
  filterOperatorNot: "не е",
  filterOperatorAfter: "е след",
  filterOperatorOnOrAfter: "е на или след",
  filterOperatorBefore: "е преди",
  filterOperatorOnOrBefore: "е на или преди",
  filterOperatorIsEmpty: "е празен",
  filterOperatorIsNotEmpty: "не е празен",
  filterOperatorIsAnyOf: "е някой от",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Съдържа",
  headerFilterOperatorDoesNotContain: "Не съдържа",
  headerFilterOperatorEquals: "Равнo",
  headerFilterOperatorDoesNotEqual: "Не е равно",
  headerFilterOperatorStartsWith: "Започва с",
  headerFilterOperatorEndsWith: "Завършва с",
  headerFilterOperatorIs: "Равно е на",
  headerFilterOperatorNot: "Не се равнява на",
  headerFilterOperatorAfter: "След",
  headerFilterOperatorOnOrAfter: "След (включително)",
  headerFilterOperatorBefore: "Преди",
  headerFilterOperatorOnOrBefore: "Преди (включително)",
  headerFilterOperatorIsEmpty: "Празен",
  headerFilterOperatorIsNotEmpty: "Не е празен",
  headerFilterOperatorIsAnyOf: "Всичко от",
  "headerFilterOperator=": "Равно",
  "headerFilterOperator!=": "Различно",
  "headerFilterOperator>": "По-голямо от",
  "headerFilterOperator>=": "По-голямо или равно на",
  "headerFilterOperator<": "По-малко от",
  "headerFilterOperator<=": "По-малко или равно на",
  // Filter values text
  filterValueAny: "всякакви",
  filterValueTrue: "вярно",
  filterValueFalse: "невярно",
  // Column menu text
  columnMenuLabel: "Меню",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Покажи колоните",
  columnMenuManageColumns: "Управление на колони",
  columnMenuFilter: "Филтри",
  columnMenuHideColumn: "Скрий",
  columnMenuUnsort: "Отмени сортирането",
  columnMenuSortAsc: "Сортирай по възходящ ред",
  columnMenuSortDesc: "Сортирай по низходящ ред",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `${count} активни филтри`,
  columnHeaderFiltersLabel: "Покажи Филтрите",
  columnHeaderSortIconLabel: "Сортирай",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} избрани редове` : `${count.toLocaleString()} избран ред`,
  // Total row amount footer text
  footerTotalRows: "Общо Редове:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} от ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Избор на квадратче",
  checkboxSelectionSelectAllRows: "Избери всички редове",
  checkboxSelectionUnselectAllRows: "Отмени избора на всички редове",
  checkboxSelectionSelectRow: "Избери ред",
  checkboxSelectionUnselectRow: "Отмени избора на ред",
  // Boolean cell text
  booleanCellTrueLabel: "да",
  booleanCellFalseLabel: "не",
  // Actions cell more text
  actionsCellMore: "още",
  // Column pinning text
  pinToLeft: "Закачи в ляво",
  pinToRight: "Закачи в дясно",
  unpin: "Откачи",
  // Tree Data
  treeDataGroupingHeaderName: "Група",
  treeDataExpand: "виж деца",
  treeDataCollapse: "скрий децата",
  // Grouping columns
  groupingColumnHeaderName: "Група",
  groupColumn: (name) => `Групирай по ${name}`,
  unGroupColumn: (name) => `Спри групиране по ${name}`,
  // Master/detail
  detailPanelToggle: "Превключване на панела с детайли",
  expandDetailPanel: "Разгъване",
  collapseDetailPanel: "Свиване",
  // Row reordering text
  rowReorderingHeaderName: "Подредба на редове",
  // Aggregation
  aggregationMenuItemHeader: "Агрегиране",
  aggregationFunctionLabelSum: "сума",
  aggregationFunctionLabelAvg: "срст",
  aggregationFunctionLabelMin: "мин",
  aggregationFunctionLabelMax: "макс",
  aggregationFunctionLabelSize: "размер"
};
var bgBG2 = getGridLocalization(bgBGGrid, bgBG);

// node_modules/@mui/x-data-grid/locales/bnBD.js
var bnBDGrid = {
  // Root
  noRowsLabel: "কোনো সারি নেই",
  noResultsOverlayLabel: "কোনো ফলাফল পাওয়া যায়নি।",
  // Density selector toolbar button text
  toolbarDensity: "ঘনত্ব",
  toolbarDensityLabel: "ঘনত্ব",
  toolbarDensityCompact: "সংকুচিত",
  toolbarDensityStandard: "মানক",
  toolbarDensityComfortable: "স্বাচ্ছন্দ্যদায়ক",
  // Columns selector toolbar button text
  toolbarColumns: "কলাম",
  toolbarColumnsLabel: "কলাম নির্বাচন করুন",
  // Filters toolbar button text
  toolbarFilters: "ফিল্টার",
  toolbarFiltersLabel: "ফিল্টার দেখান",
  toolbarFiltersTooltipHide: "ফিল্টার লুকান",
  toolbarFiltersTooltipShow: "ফিল্টার দেখান",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} টি সক্রিয় ফিল্টার` : `${count} টি সক্রিয় ফিল্টার`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "অনুসন্ধান করুন…",
  toolbarQuickFilterLabel: "অনুসন্ধান",
  toolbarQuickFilterDeleteIconLabel: "পরিষ্কার করুন",
  // Export selector toolbar button text
  toolbarExport: "এক্সপোর্ট",
  toolbarExportLabel: "এক্সপোর্ট",
  toolbarExportCSV: "CSV হিসাবে ডাউনলোড করুন",
  toolbarExportPrint: "প্রিন্ট করুন",
  toolbarExportExcel: "Excel হিসাবে ডাউনলোড করুন",
  // Columns management text
  columnsManagementSearchTitle: "অনুসন্ধান",
  columnsManagementNoColumns: "কোনো কলাম নেই",
  columnsManagementShowHideAllText: "সব দেখান/লুকান",
  columnsManagementReset: "রিসেট",
  columnsManagementDeleteIconLabel: "পরিষ্কার",
  // Filter panel text
  filterPanelAddFilter: "ফিল্টার যোগ করুন",
  filterPanelRemoveAll: "সব সরান",
  filterPanelDeleteIconLabel: "মুছুন",
  filterPanelLogicOperator: "লজিক অপারেটর",
  filterPanelOperator: "অপারেটর",
  filterPanelOperatorAnd: "এবং",
  filterPanelOperatorOr: "অথবা",
  filterPanelColumns: "কলাম",
  filterPanelInputLabel: "মান",
  filterPanelInputPlaceholder: "ফিল্টার মান",
  // Filter operators text
  filterOperatorContains: "অন্তর্ভুক্ত",
  filterOperatorDoesNotContain: "অন্তর্ভুক্ত নয়",
  filterOperatorEquals: "সমান",
  filterOperatorDoesNotEqual: "সমান নয়",
  filterOperatorStartsWith: "দিয়ে শুরু হয়",
  filterOperatorEndsWith: "দিয়ে শেষ হয়",
  filterOperatorIs: "হচ্ছে",
  filterOperatorNot: "হচ্ছে না",
  filterOperatorAfter: "পরবর্তী",
  filterOperatorOnOrAfter: "এই তারিখ বা পরবর্তী",
  filterOperatorBefore: "পূর্ববর্তী",
  filterOperatorOnOrBefore: "এই তারিখ বা পূর্ববর্তী",
  filterOperatorIsEmpty: "খালি",
  filterOperatorIsNotEmpty: "খালি নয়",
  filterOperatorIsAnyOf: "এর যেকোনো একটি",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "অন্তর্ভুক্ত",
  headerFilterOperatorDoesNotContain: "অন্তর্ভুক্ত নয়",
  headerFilterOperatorEquals: "সমান",
  headerFilterOperatorDoesNotEqual: "সমান নয়",
  headerFilterOperatorStartsWith: "দিয়ে শুরু হয়",
  headerFilterOperatorEndsWith: "দিয়ে শেষ হয়",
  headerFilterOperatorIs: "হচ্ছে",
  headerFilterOperatorNot: "হচ্ছে না",
  headerFilterOperatorAfter: "পরবর্তী",
  headerFilterOperatorOnOrAfter: "এই তারিখ বা পরবর্তী",
  headerFilterOperatorBefore: "পূর্ববর্তী",
  headerFilterOperatorOnOrBefore: "এই তারিখ বা পূর্ববর্তী",
  headerFilterOperatorIsEmpty: "খালি",
  headerFilterOperatorIsNotEmpty: "খালি নয়",
  headerFilterOperatorIsAnyOf: "এর যেকোনো একটি",
  "headerFilterOperator=": "সমান",
  "headerFilterOperator!=": "সমান নয়",
  "headerFilterOperator>": "বড়",
  "headerFilterOperator>=": "বড় বা সমান",
  "headerFilterOperator<": "ছোট",
  "headerFilterOperator<=": "ছোট বা সমান",
  // Filter values text
  filterValueAny: "যেকোনো",
  filterValueTrue: "সত্য",
  filterValueFalse: "মিথ্যা",
  // Column menu text
  columnMenuLabel: "মেনু",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "কলাম দেখান",
  columnMenuManageColumns: "কলাম পরিচালনা করুন",
  columnMenuFilter: "ফিল্টার",
  columnMenuHideColumn: "কলাম লুকান",
  columnMenuUnsort: "সাজানো বাতিল করুন",
  columnMenuSortAsc: "ASC অনুযায়ী সাজান",
  columnMenuSortDesc: "DESC অনুযায়ী সাজান",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} টি সক্রিয় ফিল্টার` : `${count} টি সক্রিয় ফিল্টার`,
  columnHeaderFiltersLabel: "ফিল্টার দেখান",
  columnHeaderSortIconLabel: "সাজান",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} টি সারি নির্বাচিত` : `${count.toLocaleString()} টি সারি নির্বাচিত`,
  // Total row amount footer text
  footerTotalRows: "মোট সারি:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} of ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "চেকবক্স নির্বাচন",
  checkboxSelectionSelectAllRows: "সব সারি নির্বাচন করুন",
  checkboxSelectionUnselectAllRows: "সব সারি নির্বাচন বাতিল করুন",
  checkboxSelectionSelectRow: "সারি নির্বাচন করুন",
  checkboxSelectionUnselectRow: "সারি নির্বাচন বাতিল করুন",
  // Boolean cell text
  booleanCellTrueLabel: "হ্যাঁ",
  booleanCellFalseLabel: "না",
  // Actions cell more text
  actionsCellMore: "আরও",
  // Column pinning text
  pinToLeft: "বাঁ দিকে পিন করুন",
  pinToRight: "ডান দিকে পিন করুন",
  unpin: "আনপিন করুন",
  // Tree Data
  treeDataGroupingHeaderName: "গ্রুপ",
  // treeDataExpand: 'see children',
  // treeDataCollapse: 'hide children',
  // Grouping columns
  groupingColumnHeaderName: "গ্রুপ",
  groupColumn: (name) => `${name} অনুসারে গ্রুপ করুন`,
  unGroupColumn: (name) => `${name} অনুসারে গ্রুপ বন্ধ করুন`,
  // Master/detail
  detailPanelToggle: "বিস্তারিত প্যানেল টগল করুন",
  expandDetailPanel: "সম্প্রসারিত করুন",
  collapseDetailPanel: "সংকুচিত করুন",
  // Row reordering text
  rowReorderingHeaderName: "সারি পুনর্বিন্যাস",
  // Aggregation
  aggregationMenuItemHeader: "সংকলন",
  aggregationFunctionLabelSum: "যোগফল",
  aggregationFunctionLabelAvg: "গড়",
  aggregationFunctionLabelMin: "সর্বনিম্ন",
  aggregationFunctionLabelMax: "সর্বোচ্চ",
  aggregationFunctionLabelSize: "মাপ"
};
var bnBD2 = getGridLocalization(bnBDGrid, bnBD);

// node_modules/@mui/x-data-grid/locales/csCZ.js
var csCZGrid = {
  // Root
  noRowsLabel: "Žádné záznamy",
  noResultsOverlayLabel: "Nenašly se žadné výsledky.",
  // Density selector toolbar button text
  toolbarDensity: "Zobrazení",
  toolbarDensityLabel: "Zobrazení",
  toolbarDensityCompact: "Kompaktní",
  toolbarDensityStandard: "Standartní",
  toolbarDensityComfortable: "Komfortní",
  // Columns selector toolbar button text
  toolbarColumns: "Sloupce",
  toolbarColumnsLabel: "Vybrat sloupec",
  // Filters toolbar button text
  toolbarFilters: "Filtry",
  toolbarFiltersLabel: "Zobrazit filtry",
  toolbarFiltersTooltipHide: "Skrýt filtry",
  toolbarFiltersTooltipShow: "Zobrazit filtry",
  toolbarFiltersTooltipActive: (count) => {
    let pluralForm = "aktivních filtrů";
    if (count > 1 && count < 5) {
      pluralForm = "aktivní filtry";
    } else if (count === 1) {
      pluralForm = "aktivní filtr";
    }
    return `${count} ${pluralForm}`;
  },
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Hledat…",
  toolbarQuickFilterLabel: "Hledat",
  toolbarQuickFilterDeleteIconLabel: "Vymazat",
  // Export selector toolbar button text
  toolbarExport: "Export",
  toolbarExportLabel: "Export",
  toolbarExportCSV: "Stáhnout jako CSV",
  toolbarExportPrint: "Vytisknout",
  toolbarExportExcel: "Stáhnout jako Excel",
  // Columns management text
  columnsManagementSearchTitle: "Hledat sloupce",
  columnsManagementNoColumns: "Žádné sloupce",
  columnsManagementShowHideAllText: "Zobrazit/skrýt vše",
  columnsManagementReset: "Resetovat",
  columnsManagementDeleteIconLabel: "Vyčistit",
  // Filter panel text
  filterPanelAddFilter: "Přidat filtr",
  filterPanelRemoveAll: "Odstranit vše",
  filterPanelDeleteIconLabel: "Odstranit",
  filterPanelLogicOperator: "Logický operátor",
  filterPanelOperator: "Operátory",
  filterPanelOperatorAnd: "A",
  filterPanelOperatorOr: "Nebo",
  filterPanelColumns: "Sloupce",
  filterPanelInputLabel: "Hodnota",
  filterPanelInputPlaceholder: "Hodnota filtru",
  // Filter operators text
  filterOperatorContains: "obsahuje",
  filterOperatorDoesNotContain: "neobsahuje",
  filterOperatorEquals: "rovná se",
  filterOperatorDoesNotEqual: "nerovná se",
  filterOperatorStartsWith: "začíná na",
  filterOperatorEndsWith: "končí na",
  filterOperatorIs: "je",
  filterOperatorNot: "není",
  filterOperatorAfter: "je po",
  filterOperatorOnOrAfter: "je po včetně",
  filterOperatorBefore: "je před",
  filterOperatorOnOrBefore: "je před včetně",
  filterOperatorIsEmpty: "je prázdný",
  filterOperatorIsNotEmpty: "není prázdný",
  filterOperatorIsAnyOf: "je jeden z",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Obsahuje",
  headerFilterOperatorDoesNotContain: "Neobsahuje",
  headerFilterOperatorEquals: "Rovná se",
  headerFilterOperatorDoesNotEqual: "Nerovná se",
  headerFilterOperatorStartsWith: "Začíná na",
  headerFilterOperatorEndsWith: "Končí na",
  headerFilterOperatorIs: "Je",
  headerFilterOperatorNot: "Není",
  headerFilterOperatorAfter: "Je po",
  headerFilterOperatorOnOrAfter: "Je po včetně",
  headerFilterOperatorBefore: "Je před",
  headerFilterOperatorOnOrBefore: "Je před včetně",
  headerFilterOperatorIsEmpty: "Je prázdný",
  headerFilterOperatorIsNotEmpty: "Není prázdný",
  headerFilterOperatorIsAnyOf: "Je jeden z",
  "headerFilterOperator=": "Rovná se",
  "headerFilterOperator!=": "Nerovná se",
  "headerFilterOperator>": "Větší než",
  "headerFilterOperator>=": "Větší než nebo rovno",
  "headerFilterOperator<": "Menší než",
  "headerFilterOperator<=": "Menší než nebo rovno",
  // Filter values text
  filterValueAny: "jakýkoliv",
  filterValueTrue: "ano",
  filterValueFalse: "ne",
  // Column menu text
  columnMenuLabel: "Menu",
  columnMenuAriaLabel: (columnName) => `Možnosti sloupce ${columnName}`,
  columnMenuShowColumns: "Zobrazit sloupce",
  columnMenuManageColumns: "Spravovat sloupce",
  columnMenuFilter: "Filtr",
  columnMenuHideColumn: "Skrýt",
  columnMenuUnsort: "Zrušit filtry",
  columnMenuSortAsc: "Seřadit vzestupně",
  columnMenuSortDesc: "Seřadit sestupně",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => {
    let pluralForm = "aktivních filtrů";
    if (count > 1 && count < 5) {
      pluralForm = "aktivní filtry";
    } else if (count === 1) {
      pluralForm = "aktivní filtr";
    }
    return `${count} ${pluralForm}`;
  },
  columnHeaderFiltersLabel: "Zobrazit filtry",
  columnHeaderSortIconLabel: "Filtrovat",
  // Rows selected footer text
  footerRowSelected: (count) => {
    let pluralForm = "vybraných záznamů";
    if (count > 1 && count < 5) {
      pluralForm = "vybrané záznamy";
    } else if (count === 1) {
      pluralForm = "vybraný záznam";
    }
    return `${count} ${pluralForm}`;
  },
  // Total row amount footer text
  footerTotalRows: "Celkem řádků:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => {
    const str = totalCount.toString();
    const firstDigit = str[0];
    const op = ["4", "6", "7"].includes(firstDigit) || firstDigit === "1" && str.length % 3 === 0 ? "ze" : "z";
    return `${visibleCount.toLocaleString()} ${op} ${totalCount.toLocaleString()}`;
  },
  // Checkbox selection text
  checkboxSelectionHeaderName: "Výběr řádku",
  checkboxSelectionSelectAllRows: "Označit všechny řádky",
  checkboxSelectionUnselectAllRows: "Odznačit všechny řádky",
  checkboxSelectionSelectRow: "Označit řádek",
  checkboxSelectionUnselectRow: "Odznačit řádek",
  // Boolean cell text
  booleanCellTrueLabel: "ano",
  booleanCellFalseLabel: "ne",
  // Actions cell more text
  actionsCellMore: "více",
  // Column pinning text
  pinToLeft: "Připnout vlevo",
  pinToRight: "Připnout vpravo",
  unpin: "Odepnout",
  // Tree Data
  treeDataGroupingHeaderName: "Skupina",
  treeDataExpand: "zobrazit potomky",
  treeDataCollapse: "skrýt potomky",
  // Grouping columns
  groupingColumnHeaderName: "Skupina",
  groupColumn: (name) => `Seskupit podle ${name}`,
  unGroupColumn: (name) => `Přestat seskupovat podle ${name}`,
  // Master/detail
  detailPanelToggle: "Přepnout detail panelu",
  expandDetailPanel: "Rozbalit",
  collapseDetailPanel: "Sbalit",
  // Row reordering text
  rowReorderingHeaderName: "Přeuspořádávání řádků",
  // Aggregation
  aggregationMenuItemHeader: "Seskupování",
  aggregationFunctionLabelSum: "součet",
  aggregationFunctionLabelAvg: "průměr",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "počet"
};
var csCZ2 = getGridLocalization(csCZGrid, csCZ);

// node_modules/@mui/x-data-grid/locales/daDK.js
var daDKGrid = {
  // Root
  noRowsLabel: "Ingen rækker",
  noResultsOverlayLabel: "Ingen resultater",
  // Density selector toolbar button text
  toolbarDensity: "Tæthed",
  toolbarDensityLabel: "Tæthed",
  toolbarDensityCompact: "Kompakt",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Luftig",
  // Columns selector toolbar button text
  toolbarColumns: "Kolonner",
  toolbarColumnsLabel: "Vælg kolonner",
  // Filters toolbar button text
  toolbarFilters: "Filtre",
  toolbarFiltersLabel: "Vis filtre",
  toolbarFiltersTooltipHide: "Skjul filtre",
  toolbarFiltersTooltipShow: "Vis filtre",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive filtre` : `${count} aktivt filter`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Søg…",
  toolbarQuickFilterLabel: "Søg",
  toolbarQuickFilterDeleteIconLabel: "Ryd",
  // Export selector toolbar button text
  toolbarExport: "Eksport",
  toolbarExportLabel: "Eksporter",
  toolbarExportCSV: "Download som CSV",
  toolbarExportPrint: "Print",
  toolbarExportExcel: "Download som Excel",
  // Columns management text
  columnsManagementSearchTitle: "Søg",
  columnsManagementNoColumns: "Ingen søjler",
  columnsManagementShowHideAllText: "Vis/Skjul Alle",
  columnsManagementReset: "Nulstil",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Tilføj filter",
  filterPanelRemoveAll: "Fjern alle",
  filterPanelDeleteIconLabel: "Slet",
  filterPanelLogicOperator: "Logisk operator",
  filterPanelOperator: "Operator",
  filterPanelOperatorAnd: "Og",
  filterPanelOperatorOr: "Eller",
  filterPanelColumns: "Kolonner",
  filterPanelInputLabel: "Værdi",
  filterPanelInputPlaceholder: "Filterværdi",
  // Filter operators text
  filterOperatorContains: "indeholder",
  filterOperatorDoesNotContain: "indeholder ikke",
  filterOperatorEquals: "lig med",
  filterOperatorDoesNotEqual: "ikke lig med",
  filterOperatorStartsWith: "begynder med",
  filterOperatorEndsWith: "ender med",
  filterOperatorIs: "er lig med",
  filterOperatorNot: "er ikke lig med",
  filterOperatorAfter: "efter",
  filterOperatorOnOrAfter: "på eller efter",
  filterOperatorBefore: "før",
  filterOperatorOnOrBefore: "på eller før",
  filterOperatorIsEmpty: "indeholder ikke data",
  filterOperatorIsNotEmpty: "indeholder data",
  filterOperatorIsAnyOf: "indeholder en af",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Indeholder",
  headerFilterOperatorDoesNotContain: "Indeholder ikke",
  headerFilterOperatorEquals: "Lig med",
  headerFilterOperatorDoesNotEqual: "Ikke lig med",
  headerFilterOperatorStartsWith: "Begynder med",
  headerFilterOperatorEndsWith: "Ender med",
  headerFilterOperatorIs: "Er lig med",
  headerFilterOperatorNot: "Er ikke lig med",
  headerFilterOperatorAfter: "Efter",
  headerFilterOperatorOnOrAfter: "På eller efter",
  headerFilterOperatorBefore: "Før",
  headerFilterOperatorOnOrBefore: "På eller før",
  headerFilterOperatorIsEmpty: "Indeholder ikke data",
  headerFilterOperatorIsNotEmpty: "Indeholder data",
  headerFilterOperatorIsAnyOf: "Indeholder en af",
  "headerFilterOperator=": "Lig med",
  "headerFilterOperator!=": "Ikke lig med",
  "headerFilterOperator>": "Større end",
  "headerFilterOperator>=": "Større end eller lig med",
  "headerFilterOperator<": "Mindre end",
  "headerFilterOperator<=": "Mindre end eller lig med",
  // Filter values text
  filterValueAny: "hvilken som helst",
  filterValueTrue: "positiv",
  filterValueFalse: "negativ",
  // Column menu text
  columnMenuLabel: "Menu",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Vis kolonner",
  columnMenuManageColumns: "Administrer kolonner",
  columnMenuFilter: "Filtrer",
  columnMenuHideColumn: "Skjul kolonne",
  columnMenuUnsort: "Fjern sortering",
  columnMenuSortAsc: "Sorter stigende",
  columnMenuSortDesc: "Sorter faldende",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive filtre` : `Ét aktivt filter`,
  columnHeaderFiltersLabel: "Vis filtre",
  columnHeaderSortIconLabel: "Sorter",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} rækker valgt` : `Én række valgt`,
  // Total row amount footer text
  footerTotalRows: "Antal rækker i alt:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} af ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Afkrydsningsvalg",
  checkboxSelectionSelectAllRows: "Vælg alle rækker",
  checkboxSelectionUnselectAllRows: "Fravælg alle rækker",
  checkboxSelectionSelectRow: "Vælg række",
  checkboxSelectionUnselectRow: "Fravælg række",
  // Boolean cell text
  booleanCellTrueLabel: "ja",
  booleanCellFalseLabel: "nej",
  // Actions cell more text
  actionsCellMore: "mere",
  // Column pinning text
  pinToLeft: "Fastgør til venstre",
  pinToRight: "Fastgør til højre",
  unpin: "Frigiv",
  // Tree Data
  treeDataGroupingHeaderName: "Gruppe",
  treeDataExpand: "Vis underelementer",
  treeDataCollapse: "Skjul underelementer",
  // Grouping columns
  groupingColumnHeaderName: "Gruppe",
  groupColumn: (name) => `Gruppér efter ${name}`,
  unGroupColumn: (name) => `Fjern gruppering efter ${name}`,
  // Master/detail
  detailPanelToggle: "Udvid/kollaps detaljepanel",
  expandDetailPanel: "Udvid",
  collapseDetailPanel: "Kollaps",
  // Row reordering text
  rowReorderingHeaderName: "Omarrangering af rækker",
  // Aggregation
  aggregationMenuItemHeader: "Aggregering",
  aggregationFunctionLabelSum: "sum",
  aggregationFunctionLabelAvg: "gns",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "størrelse"
};
var daDK2 = getGridLocalization(daDKGrid, daDK);

// node_modules/@mui/x-data-grid/locales/deDE.js
var deDEGrid = {
  // Root
  noRowsLabel: "Keine Einträge",
  noResultsOverlayLabel: "Keine Ergebnisse gefunden.",
  // Density selector toolbar button text
  toolbarDensity: "Zeilenhöhe",
  toolbarDensityLabel: "Zeilenhöhe",
  toolbarDensityCompact: "Kompakt",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Breit",
  // Columns selector toolbar button text
  toolbarColumns: "Spalten",
  toolbarColumnsLabel: "Zeige Spaltenauswahl",
  // Filters toolbar button text
  toolbarFilters: "Filter",
  toolbarFiltersLabel: "Zeige Filter",
  toolbarFiltersTooltipHide: "Verberge Filter",
  toolbarFiltersTooltipShow: "Zeige Filter",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive Filter` : `${count} aktiver Filter`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Suchen…",
  toolbarQuickFilterLabel: "Suchen",
  toolbarQuickFilterDeleteIconLabel: "Löschen",
  // Export selector toolbar button text
  toolbarExport: "Exportieren",
  toolbarExportLabel: "Exportieren",
  toolbarExportCSV: "Download als CSV",
  toolbarExportPrint: "Drucken",
  toolbarExportExcel: "Download als Excel",
  // Columns management text
  columnsManagementSearchTitle: "Suche",
  columnsManagementNoColumns: "Keine Spalten",
  columnsManagementShowHideAllText: "Alle anzeigen/verbergen",
  columnsManagementReset: "Zurücksetzen",
  columnsManagementDeleteIconLabel: "Löschen",
  // Filter panel text
  filterPanelAddFilter: "Filter hinzufügen",
  filterPanelRemoveAll: "Alle entfernen",
  filterPanelDeleteIconLabel: "Löschen",
  filterPanelLogicOperator: "Logische Operatoren",
  filterPanelOperator: "Operatoren",
  filterPanelOperatorAnd: "Und",
  filterPanelOperatorOr: "Oder",
  filterPanelColumns: "Spalten",
  filterPanelInputLabel: "Wert",
  filterPanelInputPlaceholder: "Wert filtern",
  // Filter operators text
  filterOperatorContains: "enthält",
  filterOperatorDoesNotContain: "enthält nicht",
  filterOperatorEquals: "ist gleich",
  filterOperatorDoesNotEqual: "ist ungleich",
  filterOperatorStartsWith: "beginnt mit",
  filterOperatorEndsWith: "endet mit",
  filterOperatorIs: "ist",
  filterOperatorNot: "ist nicht",
  filterOperatorAfter: "ist nach",
  filterOperatorOnOrAfter: "ist am oder nach",
  filterOperatorBefore: "ist vor",
  filterOperatorOnOrBefore: "ist am oder vor",
  filterOperatorIsEmpty: "ist leer",
  filterOperatorIsNotEmpty: "ist nicht leer",
  filterOperatorIsAnyOf: "ist einer der Werte",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Enthält",
  headerFilterOperatorDoesNotContain: "Enthält nicht",
  headerFilterOperatorEquals: "Gleich",
  headerFilterOperatorDoesNotEqual: "Ungleich",
  headerFilterOperatorStartsWith: "Beginnt mit",
  headerFilterOperatorEndsWith: "Endet mit",
  headerFilterOperatorIs: "Ist",
  headerFilterOperatorNot: "Ist nicht",
  headerFilterOperatorAfter: "Ist nach",
  headerFilterOperatorOnOrAfter: "Ist am oder nach",
  headerFilterOperatorBefore: "Ist vor",
  headerFilterOperatorOnOrBefore: "Ist am oder vor",
  headerFilterOperatorIsEmpty: "Ist leer",
  headerFilterOperatorIsNotEmpty: "Ist nicht leer",
  headerFilterOperatorIsAnyOf: "Ist eines von",
  "headerFilterOperator=": "Gleich",
  "headerFilterOperator!=": "Ungleich",
  "headerFilterOperator>": "Größer als",
  "headerFilterOperator>=": "Größer als oder gleich",
  "headerFilterOperator<": "Kleiner als",
  "headerFilterOperator<=": "Kleiner als oder gleich",
  // Filter values text
  filterValueAny: "Beliebig",
  filterValueTrue: "Ja",
  filterValueFalse: "Nein",
  // Column menu text
  columnMenuLabel: "Menü",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Zeige alle Spalten",
  columnMenuManageColumns: "Spalten verwalten",
  columnMenuFilter: "Filter",
  columnMenuHideColumn: "Verbergen",
  columnMenuUnsort: "Sortierung deaktivieren",
  columnMenuSortAsc: "Sortiere aufsteigend",
  columnMenuSortDesc: "Sortiere absteigend",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive Filter` : `${count} aktiver Filter`,
  columnHeaderFiltersLabel: "Zeige Filter",
  columnHeaderSortIconLabel: "Sortieren",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} Einträge ausgewählt` : `${count.toLocaleString()} Eintrag ausgewählt`,
  // Total row amount footer text
  footerTotalRows: "Gesamt:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} von ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Checkbox Auswahl",
  checkboxSelectionSelectAllRows: "Alle Zeilen auswählen",
  checkboxSelectionUnselectAllRows: "Alle Zeilen abwählen",
  checkboxSelectionSelectRow: "Zeile auswählen",
  checkboxSelectionUnselectRow: "Zeile abwählen",
  // Boolean cell text
  booleanCellTrueLabel: "Ja",
  booleanCellFalseLabel: "Nein",
  // Actions cell more text
  actionsCellMore: "Mehr",
  // Column pinning text
  pinToLeft: "Links anheften",
  pinToRight: "Rechts anheften",
  unpin: "Loslösen",
  // Tree Data
  treeDataGroupingHeaderName: "Gruppe",
  treeDataExpand: "Kinder einblenden",
  treeDataCollapse: "Kinder ausblenden",
  // Grouping columns
  groupingColumnHeaderName: "Gruppierung",
  groupColumn: (name) => `Gruppieren nach ${name}`,
  unGroupColumn: (name) => `Gruppierung nach ${name} aufheben`,
  // Master/detail
  detailPanelToggle: "Detailansicht Kippschalter",
  expandDetailPanel: "Aufklappen",
  collapseDetailPanel: "Zuklappen",
  // Row reordering text
  rowReorderingHeaderName: "Reihen neu ordnen",
  // Aggregation
  aggregationMenuItemHeader: "Aggregation",
  aggregationFunctionLabelSum: "Summe",
  aggregationFunctionLabelAvg: "Mittelwert",
  aggregationFunctionLabelMin: "Minimum",
  aggregationFunctionLabelMax: "Maximum",
  aggregationFunctionLabelSize: "Anzahl"
};
var deDE2 = getGridLocalization(deDEGrid, deDE);

// node_modules/@mui/x-data-grid/locales/elGR.js
var elGRGrid = {
  // Root
  noRowsLabel: "Δεν υπάρχουν καταχωρήσεις",
  noResultsOverlayLabel: "Δεν βρέθηκαν αποτελέσματα.",
  // Density selector toolbar button text
  toolbarDensity: "Ύψος σειράς",
  toolbarDensityLabel: "Ύψος σειράς",
  toolbarDensityCompact: "Συμπαγής",
  toolbarDensityStandard: "Προκαθορισμένο",
  toolbarDensityComfortable: "Πλατύ",
  // Columns selector toolbar button text
  toolbarColumns: "Στήλες",
  toolbarColumnsLabel: "Επιλέξτε στήλες",
  // Filters toolbar button text
  toolbarFilters: "Φίλτρα",
  toolbarFiltersLabel: "Εμφάνιση φίλτρων",
  toolbarFiltersTooltipHide: "Απόκρυψη φίλτρων",
  toolbarFiltersTooltipShow: "Εμφάνιση φίλτρων",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} ενεργά φίλτρα` : `${count} ενεργό φίλτρο`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Αναζήτηση…",
  toolbarQuickFilterLabel: "Αναζήτηση",
  toolbarQuickFilterDeleteIconLabel: "Καθαρισμός",
  // Export selector toolbar button text
  toolbarExport: "Εξαγωγή",
  toolbarExportLabel: "Εξαγωγή",
  toolbarExportCSV: "Λήψη ως CSV",
  toolbarExportPrint: "Εκτύπωση",
  toolbarExportExcel: "Λήψη ως Excel",
  // Columns management text
  // columnsManagementSearchTitle: 'Search',
  // columnsManagementNoColumns: 'No columns',
  // columnsManagementShowHideAllText: 'Show/Hide All',
  // columnsManagementReset: 'Reset',
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Προσθήκη φίλτρου",
  filterPanelRemoveAll: "Αφαίρεση όλων",
  filterPanelDeleteIconLabel: "Διαγραφή",
  filterPanelLogicOperator: "Λογικός τελεστής",
  filterPanelOperator: "Τελεστές",
  filterPanelOperatorAnd: "Καί",
  filterPanelOperatorOr: "Ή",
  filterPanelColumns: "Στήλες",
  filterPanelInputLabel: "Τιμή",
  filterPanelInputPlaceholder: "Τιμή φίλτρου",
  // Filter operators text
  filterOperatorContains: "περιέχει",
  // filterOperatorDoesNotContain: 'does not contain',
  filterOperatorEquals: "ισούται",
  // filterOperatorDoesNotEqual: 'does not equal',
  filterOperatorStartsWith: "ξεκινάει με",
  filterOperatorEndsWith: "τελειώνει με",
  filterOperatorIs: "είναι",
  filterOperatorNot: "δεν είναι",
  filterOperatorAfter: "είναι μετά",
  filterOperatorOnOrAfter: "είναι ίσο ή μετά",
  filterOperatorBefore: "είναι πριν",
  filterOperatorOnOrBefore: "είναι ίσο ή πριν",
  filterOperatorIsEmpty: "είναι κενό",
  filterOperatorIsNotEmpty: "δεν είναι κενό",
  filterOperatorIsAnyOf: "είναι οποιοδήποτε από",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Περιέχει",
  // headerFilterOperatorDoesNotContain: 'Does not contain',
  headerFilterOperatorEquals: "Ισούται",
  // headerFilterOperatorDoesNotEqual: 'Does not equal',
  headerFilterOperatorStartsWith: "Ξεκινάει με",
  headerFilterOperatorEndsWith: "Τελειώνει με",
  headerFilterOperatorIs: "Είναι",
  headerFilterOperatorNot: "Δεν είναι",
  headerFilterOperatorAfter: "Είναι μετά",
  headerFilterOperatorOnOrAfter: "Είναι ίσο ή μετά",
  headerFilterOperatorBefore: "Είναι πριν",
  headerFilterOperatorOnOrBefore: "Είναι ίσο ή πριν",
  headerFilterOperatorIsEmpty: "Είναι κενό",
  headerFilterOperatorIsNotEmpty: "Δεν είναι κενό",
  headerFilterOperatorIsAnyOf: "Είναι οποιοδήποτε από",
  "headerFilterOperator=": "Ισούται",
  "headerFilterOperator!=": "Δεν ισούται",
  "headerFilterOperator>": "Μεγαλύτερο από",
  "headerFilterOperator>=": "Μεγαλύτερο ή ίσο με",
  "headerFilterOperator<": "Μικρότερο από",
  "headerFilterOperator<=": "Μικρότερο ή ίσο με",
  // Filter values text
  filterValueAny: "οποιοδήποτε",
  filterValueTrue: "αληθές",
  filterValueFalse: "ψευδές",
  // Column menu text
  columnMenuLabel: "Μενού",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Εμφάνιση στηλών",
  columnMenuManageColumns: "Διαχείριση στηλών",
  columnMenuFilter: "Φίλτρο",
  columnMenuHideColumn: "Απόκρυψη",
  columnMenuUnsort: "Απενεργοποίηση ταξινόμησης",
  columnMenuSortAsc: "Ταξινόμηση σε αύξουσα σειρά",
  columnMenuSortDesc: "Ταξινόμηση σε φθίνουσα σειρά",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} ενεργά φίλτρα` : `${count} ενεργό φίλτρο`,
  columnHeaderFiltersLabel: "Εμφάνιση φίλτρων",
  columnHeaderSortIconLabel: "Ταξινόμηση",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} επιλεγμένες γραμμές` : `${count.toLocaleString()} επιλεγμένη γραμμή`,
  // Total row amount footer text
  footerTotalRows: "Σύνολο Γραμμών:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} από ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Επιλογή πλαισίου ελέγχου",
  checkboxSelectionSelectAllRows: "Επιλέξτε όλες τις σειρές",
  checkboxSelectionUnselectAllRows: "Καταργήση επιλογής όλων των σειρών",
  checkboxSelectionSelectRow: "Επιλογή γραμμής",
  checkboxSelectionUnselectRow: "Καταργήση επιλογής γραμμής",
  // Boolean cell text
  booleanCellTrueLabel: "ναί",
  booleanCellFalseLabel: "όχι",
  // Actions cell more text
  actionsCellMore: "περισσότερα",
  // Column pinning text
  pinToLeft: "Καρφιτσώμα στα αριστερά",
  pinToRight: "Καρφιτσώμα στα δεξιά",
  unpin: "Ξεκαρφίτσωμα",
  // Tree Data
  treeDataGroupingHeaderName: "Ομαδοποίηση",
  treeDataExpand: "εμφάνιση περιεχομένων",
  treeDataCollapse: "απόκρυψη περιεχομένων",
  // Grouping columns
  groupingColumnHeaderName: "Ομαδοποίηση",
  groupColumn: (name) => `Ομαδοποίηση κατά ${name}`,
  unGroupColumn: (name) => `Διακοπή ομαδοποίησης κατά ${name}`,
  // Master/detail
  detailPanelToggle: "Εναλλαγή πίνακα λεπτομερειών",
  expandDetailPanel: "Ανάπτυξη",
  collapseDetailPanel: "Σύμπτυξη",
  // Row reordering text
  rowReorderingHeaderName: "Αναδιάταξη γραμμών",
  // Aggregation
  aggregationMenuItemHeader: "Συσσωμάτωση",
  aggregationFunctionLabelSum: "άθροισμα",
  aggregationFunctionLabelAvg: "μέση τιμή",
  aggregationFunctionLabelMin: "ελάχιστο",
  aggregationFunctionLabelMax: "μέγιστο",
  aggregationFunctionLabelSize: "μέγεθος"
};
var elGR2 = getGridLocalization(elGRGrid, elGR);

// node_modules/@mui/x-data-grid/constants/localeTextConstants.js
var GRID_DEFAULT_LOCALE_TEXT = {
  // Root
  noRowsLabel: "No rows",
  noResultsOverlayLabel: "No results found.",
  // Density selector toolbar button text
  toolbarDensity: "Density",
  toolbarDensityLabel: "Density",
  toolbarDensityCompact: "Compact",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Comfortable",
  // Columns selector toolbar button text
  toolbarColumns: "Columns",
  toolbarColumnsLabel: "Select columns",
  // Filters toolbar button text
  toolbarFilters: "Filters",
  toolbarFiltersLabel: "Show filters",
  toolbarFiltersTooltipHide: "Hide filters",
  toolbarFiltersTooltipShow: "Show filters",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} active filters` : `${count} active filter`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Search…",
  toolbarQuickFilterLabel: "Search",
  toolbarQuickFilterDeleteIconLabel: "Clear",
  // Export selector toolbar button text
  toolbarExport: "Export",
  toolbarExportLabel: "Export",
  toolbarExportCSV: "Download as CSV",
  toolbarExportPrint: "Print",
  toolbarExportExcel: "Download as Excel",
  // Columns management text
  columnsManagementSearchTitle: "Search",
  columnsManagementNoColumns: "No columns",
  columnsManagementShowHideAllText: "Show/Hide All",
  columnsManagementReset: "Reset",
  columnsManagementDeleteIconLabel: "Clear",
  // Filter panel text
  filterPanelAddFilter: "Add filter",
  filterPanelRemoveAll: "Remove all",
  filterPanelDeleteIconLabel: "Delete",
  filterPanelLogicOperator: "Logic operator",
  filterPanelOperator: "Operator",
  filterPanelOperatorAnd: "And",
  filterPanelOperatorOr: "Or",
  filterPanelColumns: "Columns",
  filterPanelInputLabel: "Value",
  filterPanelInputPlaceholder: "Filter value",
  // Filter operators text
  filterOperatorContains: "contains",
  filterOperatorDoesNotContain: "does not contain",
  filterOperatorEquals: "equals",
  filterOperatorDoesNotEqual: "does not equal",
  filterOperatorStartsWith: "starts with",
  filterOperatorEndsWith: "ends with",
  filterOperatorIs: "is",
  filterOperatorNot: "is not",
  filterOperatorAfter: "is after",
  filterOperatorOnOrAfter: "is on or after",
  filterOperatorBefore: "is before",
  filterOperatorOnOrBefore: "is on or before",
  filterOperatorIsEmpty: "is empty",
  filterOperatorIsNotEmpty: "is not empty",
  filterOperatorIsAnyOf: "is any of",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Contains",
  headerFilterOperatorDoesNotContain: "Does not contain",
  headerFilterOperatorEquals: "Equals",
  headerFilterOperatorDoesNotEqual: "Does not equal",
  headerFilterOperatorStartsWith: "Starts with",
  headerFilterOperatorEndsWith: "Ends with",
  headerFilterOperatorIs: "Is",
  headerFilterOperatorNot: "Is not",
  headerFilterOperatorAfter: "Is after",
  headerFilterOperatorOnOrAfter: "Is on or after",
  headerFilterOperatorBefore: "Is before",
  headerFilterOperatorOnOrBefore: "Is on or before",
  headerFilterOperatorIsEmpty: "Is empty",
  headerFilterOperatorIsNotEmpty: "Is not empty",
  headerFilterOperatorIsAnyOf: "Is any of",
  "headerFilterOperator=": "Equals",
  "headerFilterOperator!=": "Not equals",
  "headerFilterOperator>": "Greater than",
  "headerFilterOperator>=": "Greater than or equal to",
  "headerFilterOperator<": "Less than",
  "headerFilterOperator<=": "Less than or equal to",
  // Filter values text
  filterValueAny: "any",
  filterValueTrue: "true",
  filterValueFalse: "false",
  // Column menu text
  columnMenuLabel: "Menu",
  columnMenuAriaLabel: (columnName) => `${columnName} column menu`,
  columnMenuShowColumns: "Show columns",
  columnMenuManageColumns: "Manage columns",
  columnMenuFilter: "Filter",
  columnMenuHideColumn: "Hide column",
  columnMenuUnsort: "Unsort",
  columnMenuSortAsc: "Sort by ASC",
  columnMenuSortDesc: "Sort by DESC",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} active filters` : `${count} active filter`,
  columnHeaderFiltersLabel: "Show filters",
  columnHeaderSortIconLabel: "Sort",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} rows selected` : `${count.toLocaleString()} row selected`,
  // Total row amount footer text
  footerTotalRows: "Total Rows:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} of ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Checkbox selection",
  checkboxSelectionSelectAllRows: "Select all rows",
  checkboxSelectionUnselectAllRows: "Unselect all rows",
  checkboxSelectionSelectRow: "Select row",
  checkboxSelectionUnselectRow: "Unselect row",
  // Boolean cell text
  booleanCellTrueLabel: "yes",
  booleanCellFalseLabel: "no",
  // Actions cell more text
  actionsCellMore: "more",
  // Column pinning text
  pinToLeft: "Pin to left",
  pinToRight: "Pin to right",
  unpin: "Unpin",
  // Tree Data
  treeDataGroupingHeaderName: "Group",
  treeDataExpand: "see children",
  treeDataCollapse: "hide children",
  // Grouping columns
  groupingColumnHeaderName: "Group",
  groupColumn: (name) => `Group by ${name}`,
  unGroupColumn: (name) => `Stop grouping by ${name}`,
  // Master/detail
  detailPanelToggle: "Detail panel toggle",
  expandDetailPanel: "Expand",
  collapseDetailPanel: "Collapse",
  // Used core components translation keys
  MuiTablePagination: {},
  // Row reordering text
  rowReorderingHeaderName: "Row reordering",
  // Aggregation
  aggregationMenuItemHeader: "Aggregation",
  aggregationFunctionLabelSum: "sum",
  aggregationFunctionLabelAvg: "avg",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "size"
};

// node_modules/@mui/x-data-grid/locales/enUS.js
var enUS2 = getGridLocalization(GRID_DEFAULT_LOCALE_TEXT, enUS);

// node_modules/@mui/x-data-grid/locales/esES.js
var esESGrid = {
  // Root
  noRowsLabel: "Sin filas",
  noResultsOverlayLabel: "Resultados no encontrados",
  // Density selector toolbar button text
  toolbarDensity: "Densidad",
  toolbarDensityLabel: "Densidad",
  toolbarDensityCompact: "Compacta",
  toolbarDensityStandard: "Estándar",
  toolbarDensityComfortable: "Cómoda",
  // Columns selector toolbar button text
  toolbarColumns: "Columnas",
  toolbarColumnsLabel: "Seleccionar columnas",
  // Filters toolbar button text
  toolbarFilters: "Filtros",
  toolbarFiltersLabel: "Mostrar filtros",
  toolbarFiltersTooltipHide: "Ocultar filtros",
  toolbarFiltersTooltipShow: "Mostrar filtros",
  toolbarFiltersTooltipActive: (count) => count > 1 ? `${count} filtros activos` : `${count} filtro activo`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Buscar…",
  toolbarQuickFilterLabel: "Buscar",
  toolbarQuickFilterDeleteIconLabel: "Limpiar",
  // Export selector toolbar button text
  toolbarExport: "Exportar",
  toolbarExportLabel: "Exportar",
  toolbarExportCSV: "Descargar como CSV",
  toolbarExportPrint: "Imprimir",
  toolbarExportExcel: "Descargar como Excel",
  // Columns management text
  columnsManagementSearchTitle: "Buscar",
  columnsManagementNoColumns: "Sin columnas",
  columnsManagementShowHideAllText: "Mostrar/Ocultar todas",
  columnsManagementReset: "Restablecer",
  columnsManagementDeleteIconLabel: "Limpiar",
  // Filter panel text
  filterPanelAddFilter: "Agregar filtro",
  filterPanelRemoveAll: "Remover todos",
  filterPanelDeleteIconLabel: "Borrar",
  filterPanelLogicOperator: "Operador lógico",
  filterPanelOperator: "Operadores",
  filterPanelOperatorAnd: "Y",
  filterPanelOperatorOr: "O",
  filterPanelColumns: "Columnas",
  filterPanelInputLabel: "Valor",
  filterPanelInputPlaceholder: "Valor de filtro",
  // Filter operators text
  filterOperatorContains: "contiene",
  filterOperatorDoesNotContain: "no contiene",
  filterOperatorEquals: "es igual",
  filterOperatorDoesNotEqual: "es diferente a",
  filterOperatorStartsWith: "comienza con",
  filterOperatorEndsWith: "termina con",
  filterOperatorIs: "es",
  filterOperatorNot: "no es",
  filterOperatorAfter: "es posterior",
  filterOperatorOnOrAfter: "es en o posterior",
  filterOperatorBefore: "es anterior",
  filterOperatorOnOrBefore: "es en o anterior",
  filterOperatorIsEmpty: "esta vacío",
  filterOperatorIsNotEmpty: "no esta vacío",
  filterOperatorIsAnyOf: "es cualquiera de",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Contiene",
  headerFilterOperatorDoesNotContain: "No contiene",
  headerFilterOperatorEquals: "Es igual a",
  headerFilterOperatorDoesNotEqual: "Es diferente a",
  headerFilterOperatorStartsWith: "Comienza con",
  headerFilterOperatorEndsWith: "Termina con",
  headerFilterOperatorIs: "Es",
  headerFilterOperatorNot: "No es",
  headerFilterOperatorAfter: "Esta después de",
  headerFilterOperatorOnOrAfter: "Esta en o después de",
  headerFilterOperatorBefore: "Esta antes de",
  headerFilterOperatorOnOrBefore: "Esta en o antes de",
  headerFilterOperatorIsEmpty: "Esta vacío",
  headerFilterOperatorIsNotEmpty: "No esta vacío",
  headerFilterOperatorIsAnyOf: "Es cualquiera de",
  "headerFilterOperator=": "Es igual a",
  "headerFilterOperator!=": "Es diferente a",
  "headerFilterOperator>": "Es mayor que",
  "headerFilterOperator>=": "Es mayor o igual que",
  "headerFilterOperator<": "Es menor que",
  "headerFilterOperator<=": "Es menor o igual que",
  // Filter values text
  filterValueAny: "cualquiera",
  filterValueTrue: "verdadero",
  filterValueFalse: "falso",
  // Column menu text
  columnMenuLabel: "Menú",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Mostrar columnas",
  columnMenuManageColumns: "Administrar columnas",
  columnMenuFilter: "Filtro",
  columnMenuHideColumn: "Ocultar",
  columnMenuUnsort: "Desordenar",
  columnMenuSortAsc: "Ordenar ASC",
  columnMenuSortDesc: "Ordenar DESC",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count > 1 ? `${count} filtros activos` : `${count} filtro activo`,
  columnHeaderFiltersLabel: "Mostrar filtros",
  columnHeaderSortIconLabel: "Ordenar",
  // Rows selected footer text
  footerRowSelected: (count) => count > 1 ? `${count.toLocaleString()} filas seleccionadas` : `${count.toLocaleString()} fila seleccionada`,
  // Total row amount footer text
  footerTotalRows: "Filas Totales:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} de ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Seleccionar casilla",
  checkboxSelectionSelectAllRows: "Seleccionar todas las filas",
  checkboxSelectionUnselectAllRows: "Deseleccionar todas las filas",
  checkboxSelectionSelectRow: "Seleccionar fila",
  checkboxSelectionUnselectRow: "Deseleccionar fila",
  // Boolean cell text
  booleanCellTrueLabel: "si",
  booleanCellFalseLabel: "no",
  // Actions cell more text
  actionsCellMore: "más",
  // Column pinning text
  pinToLeft: "Anclar a la izquierda",
  pinToRight: "Anclar a la derecha",
  unpin: "Desanclar",
  // Tree Data
  treeDataGroupingHeaderName: "Grupo",
  treeDataExpand: "mostrar hijos",
  treeDataCollapse: "ocultar hijos",
  // Grouping columns
  groupingColumnHeaderName: "Grupo",
  groupColumn: (name) => `Agrupar por ${name}`,
  unGroupColumn: (name) => `No agrupar por ${name}`,
  // Master/detail
  detailPanelToggle: "Alternar detalle",
  expandDetailPanel: "Expandir",
  collapseDetailPanel: "Contraer",
  // Row reordering text
  rowReorderingHeaderName: "Reordenar filas",
  // Aggregation
  aggregationMenuItemHeader: "Agregación",
  aggregationFunctionLabelSum: "suma",
  aggregationFunctionLabelAvg: "promedio",
  aggregationFunctionLabelMin: "mínimo",
  aggregationFunctionLabelMax: "máximo",
  aggregationFunctionLabelSize: "tamaño"
};
var esES2 = getGridLocalization(esESGrid, esES);

// node_modules/@mui/x-data-grid/locales/faIR.js
var faIRGrid = {
  // Root
  noRowsLabel: "بدون سطر",
  noResultsOverlayLabel: "نتیجه‌ای پیدا نشد.",
  // Density selector toolbar button text
  toolbarDensity: "تراکم",
  toolbarDensityLabel: "تراکم",
  toolbarDensityCompact: "فشرده",
  toolbarDensityStandard: "استاندارد",
  toolbarDensityComfortable: "راحت",
  // Columns selector toolbar button text
  toolbarColumns: "ستون‌ها",
  toolbarColumnsLabel: "ستون‌ها را انتخاب کنید",
  // Filters toolbar button text
  toolbarFilters: "فیلترها",
  toolbarFiltersLabel: "نمایش فیلترها",
  toolbarFiltersTooltipHide: "مخفی کردن فیلترها",
  toolbarFiltersTooltipShow: "نمایش فیلترها",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} فیلترهای فعال` : `${count} فیلتر فعال`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "جستجو...",
  toolbarQuickFilterLabel: "جستجو",
  toolbarQuickFilterDeleteIconLabel: "حذف",
  // Export selector toolbar button text
  toolbarExport: "خروجی",
  toolbarExportLabel: "خروجی",
  toolbarExportCSV: "دانلود به صورت CSV",
  toolbarExportPrint: "چاپ",
  toolbarExportExcel: "دانلود به صورت اکسل",
  // Columns management text
  columnsManagementSearchTitle: "جستجو",
  columnsManagementNoColumns: "بدون سطر",
  columnsManagementShowHideAllText: "نمایش/مخفی کردن همه",
  columnsManagementReset: "بازنشانی",
  columnsManagementDeleteIconLabel: "پاک کردن",
  // Filter panel text
  filterPanelAddFilter: "افزودن فیلتر",
  filterPanelRemoveAll: "حذف همه",
  filterPanelDeleteIconLabel: "حذف",
  filterPanelLogicOperator: "عملگر منطقی",
  filterPanelOperator: "عملگرها",
  filterPanelOperatorAnd: "و",
  filterPanelOperatorOr: "یا",
  filterPanelColumns: "ستون‌ها",
  filterPanelInputLabel: "مقدار",
  filterPanelInputPlaceholder: "فیلتر مقدار",
  // Filter operators text
  filterOperatorContains: "شامل",
  filterOperatorDoesNotContain: "شامل نمیشود",
  filterOperatorEquals: "مساوی",
  filterOperatorDoesNotEqual: "برابر نیست",
  filterOperatorStartsWith: "شروع با",
  filterOperatorEndsWith: "پایان با",
  filterOperatorIs: "هست",
  filterOperatorNot: "نیست",
  filterOperatorAfter: "بعد از",
  filterOperatorOnOrAfter: "معادل یا بعدش",
  filterOperatorBefore: "قبلش",
  filterOperatorOnOrBefore: "معادل یا قبلش",
  filterOperatorIsEmpty: "خالی است",
  filterOperatorIsNotEmpty: "خالی نیست",
  filterOperatorIsAnyOf: "هر یک از",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "شامل",
  headerFilterOperatorDoesNotContain: "شامل نمیشود",
  headerFilterOperatorEquals: "مساوی",
  headerFilterOperatorDoesNotEqual: "برابر نیست",
  headerFilterOperatorStartsWith: "شروع با",
  headerFilterOperatorEndsWith: "پایان با",
  headerFilterOperatorIs: "هست",
  headerFilterOperatorNot: "نیست",
  headerFilterOperatorAfter: "بعد از",
  headerFilterOperatorOnOrAfter: "معادل یا بعد از",
  headerFilterOperatorBefore: "قبل از",
  headerFilterOperatorOnOrBefore: "معادل یا قبل از",
  headerFilterOperatorIsEmpty: "خالی است",
  headerFilterOperatorIsNotEmpty: "خالی نیست",
  headerFilterOperatorIsAnyOf: "هر یک از",
  "headerFilterOperator=": "مساوی",
  "headerFilterOperator!=": "نامساوی",
  "headerFilterOperator>": "بزرگتر",
  "headerFilterOperator>=": "بزرگتر یا مساوی",
  "headerFilterOperator<": "کوچکتر",
  "headerFilterOperator<=": "کوچکتر یا مساوی",
  // Filter values text
  filterValueAny: "هرچیزی",
  filterValueTrue: "صحیح",
  filterValueFalse: "غلط",
  // Column menu text
  columnMenuLabel: "فهرست",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "نمایش ستون‌ها",
  columnMenuManageColumns: "مدیریت ستون‌ها",
  columnMenuFilter: "فیلتر",
  columnMenuHideColumn: "مخفی",
  columnMenuUnsort: "نامرتب‌کردن",
  columnMenuSortAsc: "مرتب‌سازی صعودی",
  columnMenuSortDesc: "مرتب‌سازی نزولی",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} فیلتر‌های فعال` : `${count} فیلتر فعال`,
  columnHeaderFiltersLabel: "نمایش فیلترها",
  columnHeaderSortIconLabel: "مرتب‌سازی",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} سطرهای انتخاب شده` : `${count.toLocaleString()} سطر انتخاب شده`,
  // Total row amount footer text
  footerTotalRows: "مجموع سطرها:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} از ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "چک‌باکس انتخاب",
  checkboxSelectionSelectAllRows: "انتخاب همه‌ی ردیف‌ها",
  checkboxSelectionUnselectAllRows: "لغو انتخاب همه‌ی ردیف‌ها",
  checkboxSelectionSelectRow: "انتخاب ردیف",
  checkboxSelectionUnselectRow: "لغو انتخاب ردیف",
  // Boolean cell text
  booleanCellTrueLabel: "صحیح",
  booleanCellFalseLabel: "غلط",
  // Actions cell more text
  actionsCellMore: "بیشتر",
  // Column pinning text
  pinToLeft: "سنجاق کردن به چپ",
  pinToRight: "سنجاق کردن به راست",
  unpin: "برداشتن سنجاق",
  // Tree Data
  treeDataGroupingHeaderName: "گروه‌بندی",
  treeDataExpand: "نمایش فرزندان",
  treeDataCollapse: "پنهان‌سازی فرزندان",
  // Grouping columns
  groupingColumnHeaderName: "گروه‌بندی",
  groupColumn: (name) => `گروه‌بندی براساس ${name}`,
  unGroupColumn: (name) => `لغو گروه‌بندی براساس ${name}`,
  // Master/detail
  detailPanelToggle: "پنل جزئیات",
  expandDetailPanel: "بازکردن پنل جزئیات",
  collapseDetailPanel: "بستن پنل جزئیات",
  // Row reordering text
  rowReorderingHeaderName: "ترتیب مجدد سطر",
  // Aggregation
  aggregationMenuItemHeader: "تجمیع",
  aggregationFunctionLabelSum: "جمع",
  aggregationFunctionLabelAvg: "میانگین",
  aggregationFunctionLabelMin: "حداقل",
  aggregationFunctionLabelMax: "حداکثر",
  aggregationFunctionLabelSize: "اندازه"
};
var faIR2 = getGridLocalization(faIRGrid, faIR);

// node_modules/@mui/x-data-grid/locales/fiFI.js
var fiFIGrid = {
  // Root
  noRowsLabel: "Ei rivejä",
  noResultsOverlayLabel: "Ei tuloksia.",
  // Density selector toolbar button text
  toolbarDensity: "Tiiveys",
  toolbarDensityLabel: "Tiiveys",
  toolbarDensityCompact: "Kompakti",
  toolbarDensityStandard: "Vakio",
  toolbarDensityComfortable: "Mukava",
  // Columns selector toolbar button text
  toolbarColumns: "Sarakkeet",
  toolbarColumnsLabel: "Valitse sarakkeet",
  // Filters toolbar button text
  toolbarFilters: "Suodattimet",
  toolbarFiltersLabel: "Näytä suodattimet",
  toolbarFiltersTooltipHide: "Piilota suodattimet",
  toolbarFiltersTooltipShow: "Näytä suodattimet",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} aktiivista suodatinta` : `${count} aktiivinen suodatin`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Hae…",
  toolbarQuickFilterLabel: "Hae",
  toolbarQuickFilterDeleteIconLabel: "Tyhjennä",
  // Export selector toolbar button text
  toolbarExport: "Vie",
  toolbarExportLabel: "Vie",
  toolbarExportCSV: "Lataa CSV-muodossa",
  toolbarExportPrint: "Tulosta",
  toolbarExportExcel: "Lataa Excel-muodossa",
  // Columns management text
  columnsManagementSearchTitle: "Hae",
  columnsManagementNoColumns: "Ei sarakkeita näytettäväksi",
  columnsManagementShowHideAllText: "Näytä/Piilota kaikki",
  columnsManagementReset: "Palauta",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Lisää suodatin",
  filterPanelRemoveAll: "Poista kaikki",
  filterPanelDeleteIconLabel: "Poista",
  filterPanelLogicOperator: "Logiikkaoperaattori",
  filterPanelOperator: "Operaattorit",
  filterPanelOperatorAnd: "Ja",
  filterPanelOperatorOr: "Tai",
  filterPanelColumns: "Sarakkeet",
  filterPanelInputLabel: "Arvo",
  filterPanelInputPlaceholder: "Suodattimen arvo",
  // Filter operators text
  filterOperatorContains: "sisältää",
  // filterOperatorDoesNotContain: 'does not contain',
  filterOperatorEquals: "on yhtä suuri kuin",
  // filterOperatorDoesNotEqual: 'does not equal',
  filterOperatorStartsWith: "alkaa",
  filterOperatorEndsWith: "päättyy",
  filterOperatorIs: "on",
  filterOperatorNot: "ei ole",
  filterOperatorAfter: "on jälkeen",
  filterOperatorOnOrAfter: "on sama tai jälkeen",
  filterOperatorBefore: "on ennen",
  filterOperatorOnOrBefore: "on sama tai ennen",
  filterOperatorIsEmpty: "on tyhjä",
  filterOperatorIsNotEmpty: "ei ole tyhjä",
  filterOperatorIsAnyOf: "on mikä tahansa seuraavista",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Sisältää",
  // headerFilterOperatorDoesNotContain: 'Does not contain',
  headerFilterOperatorEquals: "On yhtä suuri kuin",
  // headerFilterOperatorDoesNotEqual: 'Does not equal',
  headerFilterOperatorStartsWith: "Alkaa",
  headerFilterOperatorEndsWith: "Päättyy",
  headerFilterOperatorIs: "On",
  headerFilterOperatorNot: "Ei ole",
  headerFilterOperatorAfter: "On jälkeen",
  headerFilterOperatorOnOrAfter: "On sama tai jälkeen",
  headerFilterOperatorBefore: "On ennen",
  headerFilterOperatorOnOrBefore: "On sama tai ennen",
  headerFilterOperatorIsEmpty: "On tyhjä",
  headerFilterOperatorIsNotEmpty: "Ei ole tyhjä",
  headerFilterOperatorIsAnyOf: "On mikä tahansa seuraavista",
  "headerFilterOperator=": "On yhtä suuri kuin",
  "headerFilterOperator!=": "Ei ole yhtä suuri kuin",
  "headerFilterOperator>": "Enemmän kuin",
  "headerFilterOperator>=": "Enemmän tai yhtä paljon kuin",
  "headerFilterOperator<": "Vähemmän kuin",
  "headerFilterOperator<=": "Vähemmän tai yhtä paljon kuin",
  // Filter values text
  filterValueAny: "mikä tahansa",
  filterValueTrue: "tosi",
  filterValueFalse: "epätosi",
  // Column menu text
  columnMenuLabel: "Valikko",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Näytä sarakkeet",
  columnMenuManageColumns: "Hallitse sarakkeita",
  columnMenuFilter: "Suodata",
  columnMenuHideColumn: "Piilota",
  columnMenuUnsort: "Poista järjestys",
  columnMenuSortAsc: "Järjestä nousevasti",
  columnMenuSortDesc: "Järjestä laskevasti",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} aktiivista suodatinta` : `${count} aktiivinen suodatin`,
  columnHeaderFiltersLabel: "Näytä suodattimet",
  columnHeaderSortIconLabel: "Järjestä",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} riviä valittu` : `${count.toLocaleString()} rivi valittu`,
  // Total row amount footer text
  footerTotalRows: "Rivejä yhteensä:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Valintaruutu",
  checkboxSelectionSelectAllRows: "Valitse kaikki rivit",
  checkboxSelectionUnselectAllRows: "Poista kaikkien rivien valinta",
  checkboxSelectionSelectRow: "Valitse rivi",
  checkboxSelectionUnselectRow: "Poista rivin valinta",
  // Boolean cell text
  booleanCellTrueLabel: "tosi",
  booleanCellFalseLabel: "epätosi",
  // Actions cell more text
  actionsCellMore: "lisää",
  // Column pinning text
  pinToLeft: "Kiinnitä vasemmalle",
  pinToRight: "Kiinnitä oikealle",
  unpin: "Irrota kiinnitys",
  // Tree Data
  treeDataGroupingHeaderName: "Ryhmä",
  treeDataExpand: "Laajenna",
  treeDataCollapse: "Supista",
  // Grouping columns
  groupingColumnHeaderName: "Ryhmä",
  groupColumn: (name) => `Ryhmittelyperuste ${name}`,
  unGroupColumn: (name) => `Poista ryhmittelyperuste ${name}`,
  // Master/detail
  detailPanelToggle: "Yksityiskohtapaneelin vaihto",
  expandDetailPanel: "Laajenna",
  collapseDetailPanel: "Tiivistä",
  // Row reordering text
  rowReorderingHeaderName: "Rivien uudelleenjärjestely",
  // Aggregation
  aggregationMenuItemHeader: "Koostaminen",
  aggregationFunctionLabelSum: "summa",
  aggregationFunctionLabelAvg: "ka.",
  aggregationFunctionLabelMin: "min.",
  aggregationFunctionLabelMax: "maks.",
  aggregationFunctionLabelSize: "koko"
};
var fiFI2 = getGridLocalization(fiFIGrid, fiFI);

// node_modules/@mui/x-data-grid/locales/frFR.js
var frFRGrid = {
  // Root
  noRowsLabel: "Pas de résultats",
  noResultsOverlayLabel: "Aucun résultat.",
  // Density selector toolbar button text
  toolbarDensity: "Densité",
  toolbarDensityLabel: "Densité",
  toolbarDensityCompact: "Compacte",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Confortable",
  // Columns selector toolbar button text
  toolbarColumns: "Colonnes",
  toolbarColumnsLabel: "Choisir les colonnes",
  // Filters toolbar button text
  toolbarFilters: "Filtres",
  toolbarFiltersLabel: "Afficher les filtres",
  toolbarFiltersTooltipHide: "Masquer les filtres",
  toolbarFiltersTooltipShow: "Afficher les filtres",
  toolbarFiltersTooltipActive: (count) => count > 1 ? `${count} filtres actifs` : `${count} filtre actif`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Rechercher…",
  toolbarQuickFilterLabel: "Recherche",
  toolbarQuickFilterDeleteIconLabel: "Supprimer",
  // Export selector toolbar button text
  toolbarExport: "Exporter",
  toolbarExportLabel: "Exporter",
  toolbarExportCSV: "Télécharger en CSV",
  toolbarExportPrint: "Imprimer",
  toolbarExportExcel: "Télécharger pour Excel",
  // Columns management text
  columnsManagementSearchTitle: "Rechercher",
  columnsManagementNoColumns: "Pas de colonnes",
  columnsManagementShowHideAllText: "Afficher/masquer toutes",
  columnsManagementReset: "Réinitialiser",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Ajouter un filtre",
  filterPanelRemoveAll: "Tout supprimer",
  filterPanelDeleteIconLabel: "Supprimer",
  filterPanelLogicOperator: "Opérateur logique",
  filterPanelOperator: "Opérateur",
  filterPanelOperatorAnd: "Et",
  filterPanelOperatorOr: "Ou",
  filterPanelColumns: "Colonne",
  filterPanelInputLabel: "Valeur",
  filterPanelInputPlaceholder: "Filtrer la valeur",
  // Filter operators text
  filterOperatorContains: "contient",
  filterOperatorDoesNotContain: "ne contient pas",
  filterOperatorEquals: "est égal à",
  filterOperatorDoesNotEqual: "n'est pas égal à",
  filterOperatorStartsWith: "commence par",
  filterOperatorEndsWith: "se termine par",
  filterOperatorIs: "est",
  filterOperatorNot: "n'est pas",
  filterOperatorAfter: "postérieur",
  filterOperatorOnOrAfter: "égal ou postérieur",
  filterOperatorBefore: "antérieur",
  filterOperatorOnOrBefore: "égal ou antérieur",
  filterOperatorIsEmpty: "est vide",
  filterOperatorIsNotEmpty: "n'est pas vide",
  filterOperatorIsAnyOf: "fait partie de",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Contient",
  headerFilterOperatorDoesNotContain: "Ne contient pas",
  headerFilterOperatorEquals: "Est égal à",
  headerFilterOperatorDoesNotEqual: "N'est pas égal à",
  headerFilterOperatorStartsWith: "Commence par",
  headerFilterOperatorEndsWith: "Se termine par",
  headerFilterOperatorIs: "Est",
  headerFilterOperatorNot: "N'est pas",
  headerFilterOperatorAfter: "Postérieur",
  headerFilterOperatorOnOrAfter: "Égal ou postérieur",
  headerFilterOperatorBefore: "Antérieur",
  headerFilterOperatorOnOrBefore: "Égal ou antérieur",
  headerFilterOperatorIsEmpty: "Est vide",
  headerFilterOperatorIsNotEmpty: "N'est pas vide",
  headerFilterOperatorIsAnyOf: "Fait partie de",
  "headerFilterOperator=": "Est égal à",
  "headerFilterOperator!=": "N'est pas égal à",
  "headerFilterOperator>": "Est supérieur à",
  "headerFilterOperator>=": "Est supérieur ou égal à",
  "headerFilterOperator<": "Est inférieur à",
  "headerFilterOperator<=": "Est inférieur ou égal à",
  // Filter values text
  filterValueAny: "tous",
  filterValueTrue: "vrai",
  filterValueFalse: "faux",
  // Column menu text
  columnMenuLabel: "Menu",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Afficher les colonnes",
  columnMenuManageColumns: "Gérer les colonnes",
  columnMenuFilter: "Filtrer",
  columnMenuHideColumn: "Masquer",
  columnMenuUnsort: "Annuler le tri",
  columnMenuSortAsc: "Tri ascendant",
  columnMenuSortDesc: "Tri descendant",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count > 1 ? `${count} filtres actifs` : `${count} filtre actif`,
  columnHeaderFiltersLabel: "Afficher les filtres",
  columnHeaderSortIconLabel: "Trier",
  // Rows selected footer text
  footerRowSelected: (count) => count > 1 ? `${count.toLocaleString()} lignes sélectionnées` : `${count.toLocaleString()} ligne sélectionnée`,
  // Total row amount footer text
  footerTotalRows: "Total de lignes :",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} sur ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Sélection",
  checkboxSelectionSelectAllRows: "Sélectionner toutes les lignes",
  checkboxSelectionUnselectAllRows: "Désélectionner toutes les lignes",
  checkboxSelectionSelectRow: "Sélectionner la ligne",
  checkboxSelectionUnselectRow: "Désélectionner la ligne",
  // Boolean cell text
  booleanCellTrueLabel: "vrai",
  booleanCellFalseLabel: "faux",
  // Actions cell more text
  actionsCellMore: "Plus",
  // Column pinning text
  pinToLeft: "Épingler à gauche",
  pinToRight: "Épingler à droite",
  unpin: "Désépingler",
  // Tree Data
  treeDataGroupingHeaderName: "Groupe",
  treeDataExpand: "afficher les enfants",
  treeDataCollapse: "masquer les enfants",
  // Grouping columns
  groupingColumnHeaderName: "Groupe",
  groupColumn: (name) => `Grouper par ${name}`,
  unGroupColumn: (name) => `Arrêter de grouper par ${name}`,
  // Master/detail
  detailPanelToggle: "Afficher/masquer les détails",
  expandDetailPanel: "Afficher",
  collapseDetailPanel: "Masquer",
  // Row reordering text
  rowReorderingHeaderName: "Positionnement des lignes",
  // Aggregation
  aggregationMenuItemHeader: "Agrégation",
  aggregationFunctionLabelSum: "Somme",
  aggregationFunctionLabelAvg: "Moyenne",
  aggregationFunctionLabelMin: "Minimum",
  aggregationFunctionLabelMax: "Maximum",
  aggregationFunctionLabelSize: "Nombre d'éléments"
};
var frFR2 = getGridLocalization(frFRGrid, frFR);

// node_modules/@mui/x-data-grid/locales/heIL.js
var heILGrid = {
  // Root
  noRowsLabel: "אין שורות",
  noResultsOverlayLabel: "לא נמצאו תוצאות.",
  // Density selector toolbar button text
  toolbarDensity: "צפיפות",
  toolbarDensityLabel: "צפיפות",
  toolbarDensityCompact: "דחוסה",
  toolbarDensityStandard: "רגילה",
  toolbarDensityComfortable: "אוורירית",
  // Columns selector toolbar button text
  toolbarColumns: "עמודות",
  toolbarColumnsLabel: "בחר עמודות",
  // Filters toolbar button text
  toolbarFilters: "סינון",
  toolbarFiltersLabel: "הצג מסננים",
  toolbarFiltersTooltipHide: "הסתר מסננים",
  toolbarFiltersTooltipShow: "הצג מסננים",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} מסננים פעילים` : `מסנן אחד פעיל`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "חיפוש…",
  toolbarQuickFilterLabel: "חיפוש",
  toolbarQuickFilterDeleteIconLabel: "ניקוי",
  // Export selector toolbar button text
  toolbarExport: "ייצוא",
  toolbarExportLabel: "ייצוא",
  toolbarExportCSV: "ייצוא ל- CSV",
  toolbarExportPrint: "הדפסה",
  toolbarExportExcel: "ייצוא ל- Excel",
  // Columns management text
  columnsManagementSearchTitle: "חיפוש",
  columnsManagementNoColumns: "אין עמודות",
  columnsManagementShowHideAllText: "הצג/הסתר הכל",
  columnsManagementReset: "אתחול",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "הוסף מסנן",
  filterPanelRemoveAll: "מחק הכל",
  filterPanelDeleteIconLabel: "מחק",
  filterPanelLogicOperator: "אופרטור לוגי",
  filterPanelOperator: "אופרטור",
  filterPanelOperatorAnd: "וגם",
  filterPanelOperatorOr: "או",
  filterPanelColumns: "עמודות",
  filterPanelInputLabel: "ערך",
  filterPanelInputPlaceholder: "ערך מסנן",
  // Filter operators text
  filterOperatorContains: "מכיל",
  filterOperatorDoesNotContain: "לא מכיל",
  filterOperatorEquals: "שווה",
  filterOperatorDoesNotEqual: "שונה",
  filterOperatorStartsWith: "מתחיל ב-",
  filterOperatorEndsWith: "נגמר ב-",
  filterOperatorIs: "הינו",
  filterOperatorNot: "אינו",
  filterOperatorAfter: "אחרי",
  filterOperatorOnOrAfter: "ב- או אחרי",
  filterOperatorBefore: "לפני",
  filterOperatorOnOrBefore: "ב- או לפני",
  filterOperatorIsEmpty: "ריק",
  filterOperatorIsNotEmpty: "אינו ריק",
  filterOperatorIsAnyOf: "הוא אחד מ-",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "מכיל",
  headerFilterOperatorDoesNotContain: "לא מכיל",
  headerFilterOperatorEquals: "שווה",
  headerFilterOperatorDoesNotEqual: "שונה",
  headerFilterOperatorStartsWith: "מתחיל ב-",
  headerFilterOperatorEndsWith: "נגמר ב-",
  headerFilterOperatorIs: "הינו",
  headerFilterOperatorNot: "אינו",
  headerFilterOperatorAfter: "אחרי",
  headerFilterOperatorOnOrAfter: "ב- או אחרי",
  headerFilterOperatorBefore: "לפני",
  headerFilterOperatorOnOrBefore: "ב- או לפני",
  headerFilterOperatorIsEmpty: "ריק",
  headerFilterOperatorIsNotEmpty: "אינו ריק",
  headerFilterOperatorIsAnyOf: "הוא אחד מ-",
  "headerFilterOperator=": "שווה",
  "headerFilterOperator!=": "אינו שווה",
  "headerFilterOperator>": "גדול מ-",
  "headerFilterOperator>=": "גדול שווה ל-",
  "headerFilterOperator<": "קטן מ-",
  "headerFilterOperator<=": "קטן שווה ל-",
  // Filter values text
  filterValueAny: "כל ערך",
  filterValueTrue: "כן",
  filterValueFalse: "לא",
  // Column menu text
  columnMenuLabel: "תפריט",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "הצג עמודות",
  columnMenuManageColumns: "נהל עמודות",
  columnMenuFilter: "סנן",
  columnMenuHideColumn: "הסתר",
  columnMenuUnsort: "בטל מיון",
  columnMenuSortAsc: "מיין בסדר עולה",
  columnMenuSortDesc: "מיין בסדר יורד",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} מסננים פעילים` : `מסנן אחד פעיל`,
  columnHeaderFiltersLabel: "הצג מסננים",
  columnHeaderSortIconLabel: "מיין",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} שורות נבחרו` : `שורה אחת נבחרה`,
  // Total row amount footer text
  footerTotalRows: "סך הכל:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} מתוך ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "בחירה",
  checkboxSelectionSelectAllRows: "בחר הכל",
  checkboxSelectionUnselectAllRows: "בטל הכל",
  checkboxSelectionSelectRow: "בחר שורה",
  checkboxSelectionUnselectRow: "בטל בחירת שורה",
  // Boolean cell text
  booleanCellTrueLabel: "כן",
  booleanCellFalseLabel: "לא",
  // Actions cell more text
  actionsCellMore: "עוד",
  // Column pinning text
  pinToLeft: "נעץ משמאל",
  pinToRight: "נעץ מימין",
  unpin: "הסר נעיצה",
  // Tree Data
  treeDataGroupingHeaderName: "קבוצה",
  treeDataExpand: "הרחב",
  treeDataCollapse: "כווץ",
  // Grouping columns
  groupingColumnHeaderName: "קבוצה",
  groupColumn: (name) => `קבץ לפי ${name}`,
  unGroupColumn: (name) => `הפסק לקבץ לפי ${name}`,
  // Master/detail
  detailPanelToggle: "הצג/הסתר פרטים",
  expandDetailPanel: "הרחב",
  collapseDetailPanel: "כווץ",
  // Row reordering text
  rowReorderingHeaderName: "סידור שורות",
  // Aggregation
  aggregationMenuItemHeader: "צבירה",
  aggregationFunctionLabelSum: "סכום",
  aggregationFunctionLabelAvg: "ממוצע",
  aggregationFunctionLabelMin: "מינימום",
  aggregationFunctionLabelMax: "מקסימום",
  aggregationFunctionLabelSize: "גודל"
};
var heIL2 = getGridLocalization(heILGrid, heIL);

// node_modules/@mui/x-data-grid/locales/huHU.js
var huHUGrid = {
  // Root
  noRowsLabel: "Nincsenek sorok",
  noResultsOverlayLabel: "Nincs találat.",
  // Density selector toolbar button text
  toolbarDensity: "Sormagasság",
  toolbarDensityLabel: "Sormagasság",
  toolbarDensityCompact: "Kompakt",
  toolbarDensityStandard: "Normál",
  toolbarDensityComfortable: "Kényelmes",
  // Columns selector toolbar button text
  toolbarColumns: "Oszlopok",
  toolbarColumnsLabel: "Oszlopok kiválasztása",
  // Filters toolbar button text
  toolbarFilters: "Szűrők",
  toolbarFiltersLabel: "Szűrők megjelenítése",
  toolbarFiltersTooltipHide: "Szűrők elrejtése",
  toolbarFiltersTooltipShow: "Szűrők megjelenítése",
  toolbarFiltersTooltipActive: (count) => `${count} aktív szűrő`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Keresés…",
  toolbarQuickFilterLabel: "Keresés",
  toolbarQuickFilterDeleteIconLabel: "Törlés",
  // Export selector toolbar button text
  toolbarExport: "Exportálás",
  toolbarExportLabel: "Exportálás",
  toolbarExportCSV: "Mentés CSV fájlként",
  toolbarExportPrint: "Nyomtatás",
  toolbarExportExcel: "Mentés Excel fájlként",
  // Columns management text
  columnsManagementSearchTitle: "Keresés",
  columnsManagementNoColumns: "Nincsenek oszlopok",
  columnsManagementShowHideAllText: "Összes",
  columnsManagementReset: "Visszavon",
  columnsManagementDeleteIconLabel: "Törlés",
  // Filter panel text
  filterPanelAddFilter: "Szűrő hozzáadása",
  filterPanelRemoveAll: "Összes törlése",
  filterPanelDeleteIconLabel: "Törlés",
  filterPanelLogicOperator: "Logikai operátor",
  filterPanelOperator: "Operátorok",
  filterPanelOperatorAnd: "És",
  filterPanelOperatorOr: "Vagy",
  filterPanelColumns: "Oszlopok",
  filterPanelInputLabel: "Érték",
  filterPanelInputPlaceholder: "Érték szűrése",
  // Filter operators text
  filterOperatorContains: "tartalmazza:",
  filterOperatorDoesNotContain: "nem tartalmazza",
  filterOperatorEquals: "egyenlő ezzel:",
  filterOperatorDoesNotEqual: "nem egyenlő",
  filterOperatorStartsWith: "ezzel kezdődik:",
  filterOperatorEndsWith: "ezzel végződik:",
  filterOperatorIs: "a következő:",
  filterOperatorNot: "nem a következő:",
  filterOperatorAfter: "ezutáni:",
  filterOperatorOnOrAfter: "ekkori vagy ezutáni:",
  filterOperatorBefore: "ezelőtti:",
  filterOperatorOnOrBefore: "ekkori vagy ezelőtti:",
  filterOperatorIsEmpty: "üres",
  filterOperatorIsNotEmpty: "nem üres",
  filterOperatorIsAnyOf: "a következők egyike:",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Tartalmazza:",
  headerFilterOperatorDoesNotContain: "Nem tartalmazza",
  headerFilterOperatorEquals: "Egyenlő ezzel:",
  headerFilterOperatorDoesNotEqual: "Nem egyenlő",
  headerFilterOperatorStartsWith: "Ezzel kezdődik:",
  headerFilterOperatorEndsWith: "Ezzel végződik:",
  headerFilterOperatorIs: "Megegyezik",
  headerFilterOperatorNot: "Nem egyezik meg",
  headerFilterOperatorAfter: "Ezutáni:",
  headerFilterOperatorOnOrAfter: "Ekkori vagy ezutáni:",
  headerFilterOperatorBefore: "Ezelőtti:",
  headerFilterOperatorOnOrBefore: "Ekkori vagy ezelőtti:",
  headerFilterOperatorIsEmpty: "Üres",
  headerFilterOperatorIsNotEmpty: "Nem üres",
  headerFilterOperatorIsAnyOf: "A következők egyike:",
  "headerFilterOperator=": "Egyenlő",
  "headerFilterOperator!=": "Nem egyenlő",
  "headerFilterOperator>": "Nagyobb mint",
  "headerFilterOperator>=": "Nagyobb vagy egyenlő",
  "headerFilterOperator<": "Kisebb mint",
  "headerFilterOperator<=": "Kisebb vagy egyenlő",
  // Filter values text
  filterValueAny: "bármilyen",
  filterValueTrue: "igaz",
  filterValueFalse: "hamis",
  // Column menu text
  columnMenuLabel: "Menü",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Oszlopok megjelenítése",
  columnMenuManageColumns: "Oszlopok kezelése",
  columnMenuFilter: "Szűrők",
  columnMenuHideColumn: "Elrejtés",
  columnMenuUnsort: "Sorrend visszaállítása",
  columnMenuSortAsc: "Növekvő sorrendbe",
  columnMenuSortDesc: "Csökkenő sorrendbe",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `${count} aktív szűrő`,
  columnHeaderFiltersLabel: "Szűrők megjelenítése",
  columnHeaderSortIconLabel: "Átrendezés",
  // Rows selected footer text
  footerRowSelected: (count) => `${count.toLocaleString()} sor kiválasztva`,
  // Total row amount footer text
  footerTotalRows: "Összesen:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} (összesen: ${totalCount.toLocaleString()})`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Jelölőnégyzetes kijelölés",
  checkboxSelectionSelectAllRows: "Minden sor kijelölése",
  checkboxSelectionUnselectAllRows: "Minden sor kijelölésének törlése",
  checkboxSelectionSelectRow: "Sor kijelölése",
  checkboxSelectionUnselectRow: "Sor kijelölésének törlése",
  // Boolean cell text
  booleanCellTrueLabel: "igen",
  booleanCellFalseLabel: "nem",
  // Actions cell more text
  actionsCellMore: "további",
  // Column pinning text
  pinToLeft: "Rögzítés balra",
  pinToRight: "Rögzítés jobbra",
  unpin: "Rögzítés törlése",
  // Tree Data
  treeDataGroupingHeaderName: "Csoport",
  treeDataExpand: "gyermekek megjelenítése",
  treeDataCollapse: "gyermekek elrejtése",
  // Grouping columns
  groupingColumnHeaderName: "Csoportosítás",
  groupColumn: (name) => `Csoportosítás ${name} szerint`,
  unGroupColumn: (name) => `${name} szerinti csoportosítás törlése`,
  // Master/detail
  detailPanelToggle: "Részletek panel váltása",
  expandDetailPanel: "Kibontás",
  collapseDetailPanel: "Összecsukás",
  // Row reordering text
  rowReorderingHeaderName: "Sorok újrarendezése",
  // Aggregation
  aggregationMenuItemHeader: "Összesítés",
  aggregationFunctionLabelSum: "Összeg",
  aggregationFunctionLabelAvg: "Átlag",
  aggregationFunctionLabelMin: "Minimum",
  aggregationFunctionLabelMax: "Maximum",
  aggregationFunctionLabelSize: "Darabszám"
};
var huHU2 = getGridLocalization(huHUGrid, huHU);

// node_modules/@mui/x-data-grid/locales/hyAM.js
var hyAMGrid = {
  // Root
  noRowsLabel: "Տվյալներ չկան",
  noResultsOverlayLabel: "Արդյունքներ չեն գտնվել։",
  // Density selector toolbar button text
  toolbarDensity: "Խտություն",
  toolbarDensityLabel: "Խտություն",
  toolbarDensityCompact: "Կոմպակտ",
  toolbarDensityStandard: "Ստանդարտ",
  toolbarDensityComfortable: "Հարմարավետ",
  // Columns selector toolbar button text
  toolbarColumns: "Սյունակներ",
  toolbarColumnsLabel: "Ընտրել սյունակներ",
  // Filters toolbar button text
  toolbarFilters: "Զտիչներ",
  toolbarFiltersLabel: "Ցուցադրել զտիչները",
  toolbarFiltersTooltipHide: "Թաքցնել զտիչները",
  toolbarFiltersTooltipShow: "Ցուցադրել զտիչները",
  toolbarFiltersTooltipActive: (count) => {
    let pluralForm = "ակտիվ զտիչ";
    if (count === 1) {
      pluralForm = "ակտիվ զտիչ";
    } else {
      pluralForm = "ակտիվ զտիչներ";
    }
    return `${count} ${pluralForm}`;
  },
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Որոնել…",
  toolbarQuickFilterLabel: "Որոնել",
  toolbarQuickFilterDeleteIconLabel: "Մաքրել",
  // Export selector toolbar button text
  toolbarExport: "Արտահանում",
  toolbarExportLabel: "Արտահանում",
  toolbarExportCSV: "Ներբեռնել CSV-ով",
  toolbarExportPrint: "Տպել",
  toolbarExportExcel: "Ներբեռնել Excel-ով",
  // Columns management text
  columnsManagementSearchTitle: "Որոնել",
  columnsManagementNoColumns: "Սյունակներ չկան",
  columnsManagementShowHideAllText: "Ցուցադրել/Թաքցնել բոլորը",
  columnsManagementReset: "Վերակայել",
  columnsManagementDeleteIconLabel: "Հեռացնել",
  // Filter panel text
  filterPanelAddFilter: "Ավելացնել զտիչ",
  filterPanelRemoveAll: "Հեռացնել բոլորը",
  filterPanelDeleteIconLabel: "Հեռացնել",
  filterPanelLogicOperator: "Տրամաբանական օպերատոր",
  filterPanelOperator: "Օպերատոր",
  filterPanelOperatorAnd: "Եվ",
  filterPanelOperatorOr: "Կամ",
  filterPanelColumns: "Սյունակներ",
  filterPanelInputLabel: "Արժեք",
  filterPanelInputPlaceholder: "Զտիչի արժեք",
  // Filter operators text
  filterOperatorContains: "պարունակում է",
  filterOperatorDoesNotContain: "չի պարունակում",
  filterOperatorEquals: "հավասար է",
  filterOperatorDoesNotEqual: "հավասար չէ",
  filterOperatorStartsWith: "սկսվում է",
  filterOperatorEndsWith: "վերջանում է",
  filterOperatorIs: "է",
  filterOperatorNot: "չէ",
  filterOperatorAfter: "հետո է",
  filterOperatorOnOrAfter: "այդ օրը կամ հետո է",
  filterOperatorBefore: "մինչ է",
  filterOperatorOnOrBefore: "այդ օրը կամ առաջ է",
  filterOperatorIsEmpty: "դատարկ է",
  filterOperatorIsNotEmpty: "դատարկ չէ",
  filterOperatorIsAnyOf: "որևէ մեկը",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Պարունակում է",
  headerFilterOperatorDoesNotContain: "Չի պարունակում",
  headerFilterOperatorEquals: "Հավասար է",
  headerFilterOperatorDoesNotEqual: "Հավասար չէ",
  headerFilterOperatorStartsWith: "Սկսվում է",
  headerFilterOperatorEndsWith: "Վերջանում է",
  headerFilterOperatorIs: "Է",
  headerFilterOperatorNot: "Չէ",
  headerFilterOperatorAfter: "Հետո է",
  headerFilterOperatorOnOrAfter: "Այդ օրը կամ հետո է",
  headerFilterOperatorBefore: "Մինչ է",
  headerFilterOperatorOnOrBefore: "Այդ օրը կամ առաջ է",
  headerFilterOperatorIsEmpty: "Դատարկ է",
  headerFilterOperatorIsNotEmpty: "Դատարկ չէ",
  headerFilterOperatorIsAnyOf: "Որևէ մեկը",
  "headerFilterOperator=": "Հավասար է",
  "headerFilterOperator!=": "Հավասար չէ",
  "headerFilterOperator>": "Ավելի մեծ է",
  "headerFilterOperator>=": "Ավելի մեծ կամ հավասար է",
  "headerFilterOperator<": "Ավելի փոքր է",
  "headerFilterOperator<=": "Ավելի փոքր կամ հավասար է",
  // Filter values text
  filterValueAny: "ցանկացած",
  filterValueTrue: "այո",
  filterValueFalse: "ոչ",
  // Column menu text
  columnMenuLabel: "Մենյու",
  columnMenuAriaLabel: (columnName) => `${columnName} սյունակի մենյու`,
  columnMenuShowColumns: "Ցուցադրել սյունակները",
  columnMenuManageColumns: "Կառավարել սյունակները",
  columnMenuFilter: "Զտիչ",
  columnMenuHideColumn: "Թաքցնել",
  columnMenuUnsort: "Մաքրել դասավորումը",
  columnMenuSortAsc: "Աճման կարգով դասավորել",
  columnMenuSortDesc: "Նվազման կարգով դասավորել",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => {
    let pluralForm = "ակտիվ զտիչներ";
    if (count === 1) {
      pluralForm = "ակտիվ զտիչ";
    }
    return `${count} ${pluralForm}`;
  },
  columnHeaderFiltersLabel: "Ցուցադրել զտիչները",
  columnHeaderSortIconLabel: "Դասավորել",
  // Rows selected footer text
  footerRowSelected: (count) => {
    let pluralForm = "ընտրված տող";
    if (count === 1) {
      pluralForm = "ընտրված տող";
    } else {
      pluralForm = "ընտրված տողեր";
    }
    return `${count} ${pluralForm}`;
  },
  // Total row amount footer text
  footerTotalRows: "Ընդամենը տողեր:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => {
    return `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`;
  },
  // Checkbox selection text
  checkboxSelectionHeaderName: "Տողի ընտրություն",
  checkboxSelectionSelectAllRows: "Ընտրել բոլոր տողերը",
  checkboxSelectionUnselectAllRows: "Չընտրել բոլոր տողերը",
  checkboxSelectionSelectRow: "Ընտրել տողը",
  checkboxSelectionUnselectRow: "Չընտրել տողը",
  // Boolean cell text
  booleanCellTrueLabel: "այո",
  booleanCellFalseLabel: "ոչ",
  // Actions cell more text
  actionsCellMore: "ավելին",
  // Column pinning text
  pinToLeft: "Կցել ձախ",
  pinToRight: "Կցել աջ",
  unpin: "Անջատել",
  // Tree Data
  treeDataGroupingHeaderName: "Խումբ",
  treeDataExpand: "Բացել ենթատողերը",
  treeDataCollapse: "Փակել ենթատողերը",
  // Grouping columns
  groupingColumnHeaderName: "Խմբավորում",
  groupColumn: (name) => `Խմբավորել ըստ ${name}`,
  unGroupColumn: (name) => `Չխմբավորել ըստ ${name}`,
  // Master/detail
  detailPanelToggle: "Փոխարկել մանրամասն տեսքը",
  expandDetailPanel: "Բացել",
  collapseDetailPanel: "Փակել",
  // Row reordering text
  rowReorderingHeaderName: "Տողերի վերադասավորում",
  // Aggregation
  aggregationMenuItemHeader: "Ագրեգացում",
  aggregationFunctionLabelSum: "գումար",
  aggregationFunctionLabelAvg: "միջին",
  aggregationFunctionLabelMin: "մինիմում",
  aggregationFunctionLabelMax: "մաքսիմում",
  aggregationFunctionLabelSize: "քանակ"
};
var hyAM2 = getGridLocalization(hyAMGrid, hyAM);

// node_modules/@mui/x-data-grid/locales/itIT.js
var itITGrid = {
  // Root
  noRowsLabel: "Nessun record",
  noResultsOverlayLabel: "Nessun record trovato.",
  // Density selector toolbar button text
  toolbarDensity: "Densità",
  toolbarDensityLabel: "Densità",
  toolbarDensityCompact: "Compatta",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Comoda",
  // Columns selector toolbar button text
  toolbarColumns: "Colonne",
  toolbarColumnsLabel: "Seleziona le colonne",
  // Filters toolbar button text
  toolbarFilters: "Filtri",
  toolbarFiltersLabel: "Mostra i filtri",
  toolbarFiltersTooltipHide: "Nascondi i filtri",
  toolbarFiltersTooltipShow: "Mostra i filtri",
  toolbarFiltersTooltipActive: (count) => count > 1 ? `${count} filtri attivi` : `${count} filtro attivo`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Cerca…",
  toolbarQuickFilterLabel: "Cerca",
  toolbarQuickFilterDeleteIconLabel: "Resetta",
  // Export selector toolbar button text
  toolbarExport: "Esporta",
  toolbarExportLabel: "Esporta",
  toolbarExportCSV: "Esporta in CSV",
  toolbarExportPrint: "Stampa",
  toolbarExportExcel: "Scarica come Excel",
  // Columns management text
  columnsManagementSearchTitle: "Cerca",
  columnsManagementNoColumns: "Nessuna colonna",
  columnsManagementShowHideAllText: "Mostra/Nascondi Tutto",
  columnsManagementReset: "Resetta",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Aggiungi un filtro",
  filterPanelRemoveAll: "Rimuovi filtri",
  filterPanelDeleteIconLabel: "Rimuovi",
  filterPanelLogicOperator: "Operatore logico",
  filterPanelOperator: "Operatori",
  filterPanelOperatorAnd: "E (and)",
  filterPanelOperatorOr: "O (or)",
  filterPanelColumns: "Colonne",
  filterPanelInputLabel: "Valore",
  filterPanelInputPlaceholder: "Filtra il valore",
  // Filter operators text
  filterOperatorContains: "contiene",
  filterOperatorDoesNotContain: "non contiene",
  filterOperatorEquals: "uguale a",
  filterOperatorDoesNotEqual: "diverso da",
  filterOperatorStartsWith: "comincia per",
  filterOperatorEndsWith: "termina per",
  filterOperatorIs: "uguale a",
  filterOperatorNot: "diverso da",
  filterOperatorAfter: "dopo il",
  filterOperatorOnOrAfter: "a partire dal",
  filterOperatorBefore: "prima del",
  filterOperatorOnOrBefore: "fino al",
  filterOperatorIsEmpty: "è vuoto",
  filterOperatorIsNotEmpty: "non è vuoto",
  filterOperatorIsAnyOf: "è uno tra",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Contiene",
  headerFilterOperatorDoesNotContain: "Non contiene",
  headerFilterOperatorEquals: "Uguale a",
  headerFilterOperatorDoesNotEqual: "Diverso da",
  headerFilterOperatorStartsWith: "Comincia per",
  headerFilterOperatorEndsWith: "Termina per",
  headerFilterOperatorIs: "Uguale a",
  headerFilterOperatorNot: "Diverso da",
  headerFilterOperatorAfter: "Dopo il",
  headerFilterOperatorOnOrAfter: "A partire dal",
  headerFilterOperatorBefore: "Prima del",
  headerFilterOperatorOnOrBefore: "Fino al",
  headerFilterOperatorIsEmpty: "È vuoto",
  headerFilterOperatorIsNotEmpty: "Non è vuoto",
  headerFilterOperatorIsAnyOf: "È uno tra",
  "headerFilterOperator=": "Uguale a",
  "headerFilterOperator!=": "Diverso da",
  "headerFilterOperator>": "Maggiore di",
  "headerFilterOperator>=": "Maggiore o uguale a",
  "headerFilterOperator<": "Minore di",
  "headerFilterOperator<=": "Minore o uguale a",
  // Filter values text
  filterValueAny: "qualunque",
  filterValueTrue: "vero",
  filterValueFalse: "falso",
  // Column menu text
  columnMenuLabel: "Menu",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Mostra le colonne",
  columnMenuManageColumns: "Gestisci colonne",
  columnMenuFilter: "Filtra",
  columnMenuHideColumn: "Nascondi",
  columnMenuUnsort: "Annulla l'ordinamento",
  columnMenuSortAsc: "Ordinamento crescente",
  columnMenuSortDesc: "Ordinamento decrescente",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count > 1 ? `${count} filtri attivi` : `${count} filtro attivo`,
  columnHeaderFiltersLabel: "Mostra i filtri",
  columnHeaderSortIconLabel: "Ordina",
  // Rows selected footer text
  footerRowSelected: (count) => count > 1 ? `${count.toLocaleString()} record selezionati` : `${count.toLocaleString()} record selezionato`,
  // Total row amount footer text
  footerTotalRows: "Record totali:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} di ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Seleziona",
  checkboxSelectionSelectAllRows: "Seleziona tutte le righe",
  checkboxSelectionUnselectAllRows: "Deseleziona tutte le righe",
  checkboxSelectionSelectRow: "Seleziona riga",
  checkboxSelectionUnselectRow: "Deseleziona riga",
  // Boolean cell text
  booleanCellTrueLabel: "vero",
  booleanCellFalseLabel: "falso",
  // Actions cell more text
  actionsCellMore: "più",
  // Column pinning text
  pinToLeft: "Blocca a sinistra",
  pinToRight: "Blocca a destra",
  unpin: "Sblocca",
  // Tree Data
  treeDataGroupingHeaderName: "Gruppo",
  treeDataExpand: "mostra figli",
  treeDataCollapse: "nascondi figli",
  // Grouping columns
  groupingColumnHeaderName: "Gruppo",
  groupColumn: (name) => `Raggruppa per ${name}`,
  unGroupColumn: (name) => `Annulla raggruppamento per ${name}`,
  // Master/detail
  detailPanelToggle: "Abilita pannello dettagli",
  expandDetailPanel: "Espandi",
  collapseDetailPanel: "Comprimi",
  // Row reordering text
  rowReorderingHeaderName: "Riordinamento righe",
  // Aggregation
  aggregationMenuItemHeader: "aggregazione",
  aggregationFunctionLabelSum: "somma",
  aggregationFunctionLabelAvg: "media",
  aggregationFunctionLabelMin: "minimo",
  aggregationFunctionLabelMax: "massimo",
  aggregationFunctionLabelSize: "numero di elementi"
};
var itIT2 = getGridLocalization(itITGrid, itIT);

// node_modules/@mui/x-data-grid/locales/jaJP.js
var jaJPGrid = {
  // Root
  noRowsLabel: "行がありません。",
  noResultsOverlayLabel: "結果がありません。",
  // Density selector toolbar button text
  toolbarDensity: "行間隔",
  toolbarDensityLabel: "行間隔",
  toolbarDensityCompact: "コンパクト",
  toolbarDensityStandard: "標準",
  toolbarDensityComfortable: "広め",
  // Columns selector toolbar button text
  toolbarColumns: "列一覧",
  toolbarColumnsLabel: "列選択",
  // Filters toolbar button text
  toolbarFilters: "フィルター",
  toolbarFiltersLabel: "フィルター表示",
  toolbarFiltersTooltipHide: "フィルター非表示",
  toolbarFiltersTooltipShow: "フィルター表示",
  toolbarFiltersTooltipActive: (count) => `${count}件のフィルターを適用中`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "検索…",
  toolbarQuickFilterLabel: "検索",
  toolbarQuickFilterDeleteIconLabel: "クリア",
  // Export selector toolbar button text
  toolbarExport: "エクスポート",
  toolbarExportLabel: "エクスポート",
  toolbarExportCSV: "CSVダウンロード",
  toolbarExportPrint: "印刷",
  toolbarExportExcel: "Excelダウンロード",
  // Columns management text
  columnsManagementSearchTitle: "検索",
  columnsManagementNoColumns: "カラムなし",
  columnsManagementShowHideAllText: "すべて表示/非表示",
  columnsManagementReset: "リセット",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "フィルター追加",
  filterPanelRemoveAll: "すべて削除",
  filterPanelDeleteIconLabel: "削除",
  filterPanelLogicOperator: "論理演算子",
  filterPanelOperator: "演算子",
  filterPanelOperatorAnd: "And",
  filterPanelOperatorOr: "Or",
  filterPanelColumns: "列",
  filterPanelInputLabel: "値",
  filterPanelInputPlaceholder: "値を入力…",
  // Filter operators text
  filterOperatorContains: "...を含む",
  filterOperatorDoesNotContain: "...を含まない",
  filterOperatorEquals: "...に等しい",
  filterOperatorDoesNotEqual: "...に等しくない",
  filterOperatorStartsWith: "...で始まる",
  filterOperatorEndsWith: "...で終わる",
  filterOperatorIs: "...である",
  filterOperatorNot: "...でない",
  filterOperatorAfter: "...より後ろ",
  filterOperatorOnOrAfter: "...以降",
  filterOperatorBefore: "...より前",
  filterOperatorOnOrBefore: "...以前",
  filterOperatorIsEmpty: "...空である",
  filterOperatorIsNotEmpty: "...空でない",
  filterOperatorIsAnyOf: "...のいずれか",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "含む",
  headerFilterOperatorDoesNotContain: "含まない",
  headerFilterOperatorEquals: "等しい",
  headerFilterOperatorDoesNotEqual: "等しくない",
  headerFilterOperatorStartsWith: "で始まる",
  headerFilterOperatorEndsWith: "で終わる",
  headerFilterOperatorIs: "である",
  headerFilterOperatorNot: "ではない",
  headerFilterOperatorAfter: "...より後ろ",
  headerFilterOperatorOnOrAfter: "...以降",
  headerFilterOperatorBefore: "...より前",
  headerFilterOperatorOnOrBefore: "...以前",
  headerFilterOperatorIsEmpty: "空白",
  headerFilterOperatorIsNotEmpty: "空白ではない",
  headerFilterOperatorIsAnyOf: "いずれか",
  "headerFilterOperator=": "等しい",
  "headerFilterOperator!=": "等しくない",
  "headerFilterOperator>": "より大きい",
  "headerFilterOperator>=": "以上",
  "headerFilterOperator<": "未満",
  "headerFilterOperator<=": "以下",
  // Filter values text
  filterValueAny: "いずれか",
  filterValueTrue: "真",
  filterValueFalse: "偽",
  // Column menu text
  columnMenuLabel: "メニュー",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "列表示",
  columnMenuManageColumns: "列管理",
  columnMenuFilter: "フィルター",
  columnMenuHideColumn: "列非表示",
  columnMenuUnsort: "ソート解除",
  columnMenuSortAsc: "昇順ソート",
  columnMenuSortDesc: "降順ソート",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `${count}件のフィルターを適用中`,
  columnHeaderFiltersLabel: "フィルター表示",
  columnHeaderSortIconLabel: "ソート",
  // Rows selected footer text
  footerRowSelected: (count) => `${count}行を選択中`,
  // Total row amount footer text
  footerTotalRows: "総行数:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "チェックボックス",
  checkboxSelectionSelectAllRows: "すべての行を選択",
  checkboxSelectionUnselectAllRows: "すべての行選択を解除",
  checkboxSelectionSelectRow: "行を選択",
  checkboxSelectionUnselectRow: "行選択を解除",
  // Boolean cell text
  booleanCellTrueLabel: "真",
  booleanCellFalseLabel: "偽",
  // Actions cell more text
  actionsCellMore: "もっと見る",
  // Column pinning text
  pinToLeft: "左側に固定",
  pinToRight: "右側に固定",
  unpin: "固定解除",
  // Tree Data
  treeDataGroupingHeaderName: "グループ",
  treeDataExpand: "展開",
  treeDataCollapse: "折りたたみ",
  // Grouping columns
  groupingColumnHeaderName: "グループ",
  groupColumn: (name) => `${name}でグループ化`,
  unGroupColumn: (name) => `${name}のグループを解除`,
  // Master/detail
  detailPanelToggle: "詳細パネルの切り替え",
  expandDetailPanel: "展開",
  collapseDetailPanel: "折りたたみ",
  // Row reordering text
  rowReorderingHeaderName: "行並び替え",
  // Aggregation
  aggregationMenuItemHeader: "合計",
  aggregationFunctionLabelSum: "和",
  aggregationFunctionLabelAvg: "平均",
  aggregationFunctionLabelMin: "最小値",
  aggregationFunctionLabelMax: "最大値",
  aggregationFunctionLabelSize: "サイズ"
};
var jaJP2 = getGridLocalization(jaJPGrid, jaJP);

// node_modules/@mui/x-data-grid/locales/koKR.js
var koKRGrid = {
  // Root
  noRowsLabel: "행이 없습니다.",
  noResultsOverlayLabel: "결과값이 없습니다.",
  // Density selector toolbar button text
  toolbarDensity: "행 간격",
  toolbarDensityLabel: "행 간격",
  toolbarDensityCompact: "좁게",
  toolbarDensityStandard: "기본",
  toolbarDensityComfortable: "넓게",
  // Columns selector toolbar button text
  toolbarColumns: "열 목록",
  toolbarColumnsLabel: "열 선택",
  // Filters toolbar button text
  toolbarFilters: "필터",
  toolbarFiltersLabel: "필터 표시",
  toolbarFiltersTooltipHide: "필터 숨기기",
  toolbarFiltersTooltipShow: "필터 표시",
  toolbarFiltersTooltipActive: (count) => `${count}건의 필터를 적용중`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "검색…",
  toolbarQuickFilterLabel: "검색",
  toolbarQuickFilterDeleteIconLabel: "초기화",
  // Export selector toolbar button text
  toolbarExport: "내보내기",
  toolbarExportLabel: "내보내기",
  toolbarExportCSV: "CSV로 내보내기",
  toolbarExportPrint: "프린트",
  toolbarExportExcel: "Excel로 내보내기",
  // Columns management text
  columnsManagementSearchTitle: "검색",
  columnsManagementNoColumns: "열이 없습니다.",
  columnsManagementShowHideAllText: "모두 보기/숨기기",
  columnsManagementReset: "초기화",
  columnsManagementDeleteIconLabel: "제거",
  // Filter panel text
  filterPanelAddFilter: "필터 추가",
  filterPanelRemoveAll: "모두 삭제",
  filterPanelDeleteIconLabel: "삭제",
  filterPanelLogicOperator: "논리 연산자",
  filterPanelOperator: "연산자",
  filterPanelOperatorAnd: "그리고",
  filterPanelOperatorOr: "또는",
  filterPanelColumns: "목록",
  filterPanelInputLabel: "값",
  filterPanelInputPlaceholder: "값 입력",
  // Filter operators text
  filterOperatorContains: "포함하는",
  filterOperatorDoesNotContain: "포함하지 않는",
  filterOperatorEquals: "값이 같은",
  filterOperatorDoesNotEqual: "값이 다른",
  filterOperatorStartsWith: "시작하는",
  filterOperatorEndsWith: "끝나는",
  filterOperatorIs: "~인",
  filterOperatorNot: "~아닌",
  filterOperatorAfter: "더 이후",
  filterOperatorOnOrAfter: "이후",
  filterOperatorBefore: "더 이전",
  filterOperatorOnOrBefore: "이전",
  filterOperatorIsEmpty: "값이 없는",
  filterOperatorIsNotEmpty: "값이 있는",
  filterOperatorIsAnyOf: "값 중 하나인",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "포함하는",
  headerFilterOperatorDoesNotContain: "포함하지 않는",
  headerFilterOperatorEquals: "값이 같은",
  headerFilterOperatorDoesNotEqual: "값이 다른",
  headerFilterOperatorStartsWith: "시작하는",
  headerFilterOperatorEndsWith: "끝나는",
  headerFilterOperatorIs: "~인",
  headerFilterOperatorNot: "~아닌",
  headerFilterOperatorAfter: "더 이후",
  headerFilterOperatorOnOrAfter: "이후",
  headerFilterOperatorBefore: "더 이전",
  headerFilterOperatorOnOrBefore: "이전",
  headerFilterOperatorIsEmpty: "값이 없는",
  headerFilterOperatorIsNotEmpty: "값이 있는",
  headerFilterOperatorIsAnyOf: "값 중 하나인",
  "headerFilterOperator=": "값이 같은",
  "headerFilterOperator!=": "값이 다른",
  "headerFilterOperator>": "더 큰",
  "headerFilterOperator>=": "같거나 더 큰",
  "headerFilterOperator<": "더 작은",
  "headerFilterOperator<=": "같거나 더 작은",
  // Filter values text
  filterValueAny: "아무값",
  filterValueTrue: "참",
  filterValueFalse: "거짓",
  // Column menu text
  columnMenuLabel: "메뉴",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "열 표시",
  columnMenuManageColumns: "열 관리",
  columnMenuFilter: "필터",
  columnMenuHideColumn: "열 숨기기",
  columnMenuUnsort: "정렬 해제",
  columnMenuSortAsc: "오름차순 정렬",
  columnMenuSortDesc: "내림차순 정렬",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `${count}건의 필터를 적용중`,
  columnHeaderFiltersLabel: "필터 표시",
  columnHeaderSortIconLabel: "정렬",
  // Rows selected footer text
  footerRowSelected: (count) => `${count}행 선택중`,
  // Total row amount footer text
  footerTotalRows: "총 행수:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "선택",
  checkboxSelectionSelectAllRows: "모든 행 선택",
  checkboxSelectionUnselectAllRows: "모든 행 선택 해제",
  checkboxSelectionSelectRow: "행 선택",
  checkboxSelectionUnselectRow: "행 선택 해제",
  // Boolean cell text
  booleanCellTrueLabel: "참",
  booleanCellFalseLabel: "거짓",
  // Actions cell more text
  actionsCellMore: "더보기",
  // Column pinning text
  pinToLeft: "왼쪽에 고정",
  pinToRight: "오른쪽에 고정",
  unpin: "고정 해제",
  // Tree Data
  treeDataGroupingHeaderName: "그룹",
  treeDataExpand: "하위노드 펼치기",
  treeDataCollapse: "하위노드 접기",
  // Grouping columns
  groupingColumnHeaderName: "그룹",
  groupColumn: (name) => `${name} 값으로 그룹 생성`,
  unGroupColumn: (name) => `${name} 값으로 그룹 해제`,
  // Master/detail
  detailPanelToggle: "상세 패널 토글",
  expandDetailPanel: "열기",
  collapseDetailPanel: "접기",
  // Row reordering text
  rowReorderingHeaderName: "행 재배치",
  // Aggregation
  aggregationMenuItemHeader: "집계",
  aggregationFunctionLabelSum: "합",
  aggregationFunctionLabelAvg: "평균",
  aggregationFunctionLabelMin: "최소값",
  aggregationFunctionLabelMax: "최대값",
  aggregationFunctionLabelSize: "크기"
};
var koKR2 = getGridLocalization(koKRGrid, koKR);

// node_modules/@mui/x-data-grid/locales/nbNO.js
var nbNOGrid = {
  // Root
  noRowsLabel: "Ingen rader",
  noResultsOverlayLabel: "Fant ingen resultat.",
  // Density selector toolbar button text
  toolbarDensity: "Tetthet",
  toolbarDensityLabel: "Tetthet",
  toolbarDensityCompact: "Kompakt",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Komfortabelt",
  // Columns selector toolbar button text
  toolbarColumns: "Kolonner",
  toolbarColumnsLabel: "Velg kolonner",
  // Filters toolbar button text
  toolbarFilters: "Filter",
  toolbarFiltersLabel: "Vis filter",
  toolbarFiltersTooltipHide: "Skjul filter",
  toolbarFiltersTooltipShow: "Vis filter",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Søk…",
  toolbarQuickFilterLabel: "Søk",
  toolbarQuickFilterDeleteIconLabel: "Slett",
  // Export selector toolbar button text
  toolbarExport: "Eksporter",
  toolbarExportLabel: "Eksporter",
  toolbarExportCSV: "Last ned som CSV",
  toolbarExportPrint: "Skriv ut",
  toolbarExportExcel: "Last ned som Excel",
  // Columns management text
  columnsManagementSearchTitle: "Søk",
  columnsManagementNoColumns: "Ingen kolonner",
  columnsManagementShowHideAllText: "Vis/skjul alle",
  columnsManagementReset: "Nullstill",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Legg til filter",
  filterPanelRemoveAll: "Fjern alle",
  filterPanelDeleteIconLabel: "Slett",
  filterPanelLogicOperator: "Logisk operator",
  filterPanelOperator: "Operator",
  filterPanelOperatorAnd: "Og",
  filterPanelOperatorOr: "Eller",
  filterPanelColumns: "Kolonner",
  filterPanelInputLabel: "Verdi",
  filterPanelInputPlaceholder: "Filter verdi",
  // Filter operators text
  filterOperatorContains: "inneholder",
  // filterOperatorDoesNotContain: 'does not contain',
  filterOperatorEquals: "er lik",
  // filterOperatorDoesNotEqual: 'does not equal',
  filterOperatorStartsWith: "starter med",
  filterOperatorEndsWith: "slutter med",
  filterOperatorIs: "er",
  filterOperatorNot: "er ikke",
  filterOperatorAfter: "er etter",
  filterOperatorOnOrAfter: "er på eller etter",
  filterOperatorBefore: "er før",
  filterOperatorOnOrBefore: "er på eller før",
  filterOperatorIsEmpty: "er tom",
  filterOperatorIsNotEmpty: "er ikke tom",
  filterOperatorIsAnyOf: "er en av",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Inneholder",
  // headerFilterOperatorDoesNotContain: 'Does not contain',
  headerFilterOperatorEquals: "Lik",
  // headerFilterOperatorDoesNotEqual: 'Does not equal',
  headerFilterOperatorStartsWith: "Starter på",
  headerFilterOperatorEndsWith: "Slutter på",
  headerFilterOperatorIs: "Er",
  headerFilterOperatorNot: "Er ikke",
  headerFilterOperatorAfter: "Er etter",
  headerFilterOperatorOnOrAfter: "Er på eller etter",
  headerFilterOperatorBefore: "Er før",
  headerFilterOperatorOnOrBefore: "Er på eller før",
  headerFilterOperatorIsEmpty: "Er tom",
  headerFilterOperatorIsNotEmpty: "Er ikke tom",
  headerFilterOperatorIsAnyOf: "Er en av",
  "headerFilterOperator=": "Lik",
  "headerFilterOperator!=": "Ikke lik",
  "headerFilterOperator>": "Større enn",
  "headerFilterOperator>=": "Større enn eller lik",
  "headerFilterOperator<": "Mindre enn",
  "headerFilterOperator<=": "Mindre enn eller lik",
  // Filter values text
  filterValueAny: "noen",
  filterValueTrue: "sant",
  filterValueFalse: "usant",
  // Column menu text
  columnMenuLabel: "Meny",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Vis kolonner",
  columnMenuManageColumns: "Administrer kolonner",
  columnMenuFilter: "Filter",
  columnMenuHideColumn: "Skjul",
  columnMenuUnsort: "Usorter",
  columnMenuSortAsc: "Sorter ØKENDE",
  columnMenuSortDesc: "Sorter SYNKENDE",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,
  columnHeaderFiltersLabel: "Vis filter",
  columnHeaderSortIconLabel: "Sorter",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} rader valgt` : `${count.toLocaleString()} rad valgt`,
  // Total row amount footer text
  footerTotalRows: "Totalt antall rader:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} av ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Avmerkingsboks",
  checkboxSelectionSelectAllRows: "Velg alle rader",
  checkboxSelectionUnselectAllRows: "Velg bort alle rader",
  checkboxSelectionSelectRow: "Velg rad",
  checkboxSelectionUnselectRow: "Velg bort rad",
  // Boolean cell text
  booleanCellTrueLabel: "sant",
  booleanCellFalseLabel: "usant",
  // Actions cell more text
  actionsCellMore: "mer",
  // Column pinning text
  pinToLeft: "Fest til venstre",
  pinToRight: "Fest til høyre",
  unpin: "Løsne",
  // Tree Data
  treeDataGroupingHeaderName: "Grupper",
  treeDataExpand: "se barn",
  treeDataCollapse: "skjul barn",
  // Grouping columns
  groupingColumnHeaderName: "Grupper",
  groupColumn: (name) => `Grupper på ${name}`,
  unGroupColumn: (name) => `Slutt å grupper på ${name}`,
  // Master/detail
  detailPanelToggle: "Utvid/kollaps detalj panel",
  expandDetailPanel: "Utvid",
  collapseDetailPanel: "Kollaps",
  // Row reordering text
  rowReorderingHeaderName: "Radreorganisering",
  // Aggregation
  aggregationMenuItemHeader: "Aggregering",
  aggregationFunctionLabelSum: "sum",
  aggregationFunctionLabelAvg: "snitt",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "maks",
  aggregationFunctionLabelSize: "størrelse"
};
var nbNO2 = getGridLocalization(nbNOGrid, nbNO);

// node_modules/@mui/x-data-grid/locales/nlNL.js
var nlNLGrid = {
  // Root
  noRowsLabel: "Geen resultaten.",
  noResultsOverlayLabel: "Geen resultaten gevonden.",
  // Density selector toolbar button text
  toolbarDensity: "Grootte",
  toolbarDensityLabel: "Grootte",
  toolbarDensityCompact: "Compact",
  toolbarDensityStandard: "Normaal",
  toolbarDensityComfortable: "Breed",
  // Columns selector toolbar button text
  toolbarColumns: "Kolommen",
  toolbarColumnsLabel: "Kies kolommen",
  // Filters toolbar button text
  toolbarFilters: "Filters",
  toolbarFiltersLabel: "Toon filters",
  toolbarFiltersTooltipHide: "Verberg filters",
  toolbarFiltersTooltipShow: "Toon filters",
  toolbarFiltersTooltipActive: (count) => count > 1 ? `${count} actieve filters` : `${count} filter actief`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Zoeken…",
  toolbarQuickFilterLabel: "Zoeken",
  toolbarQuickFilterDeleteIconLabel: "Wissen",
  // Export selector toolbar button text
  toolbarExport: "Exporteren",
  toolbarExportLabel: "Exporteren",
  toolbarExportCSV: "Exporteer naar CSV",
  toolbarExportPrint: "Print",
  toolbarExportExcel: "Downloaden als Excel-bestand",
  // Columns management text
  columnsManagementSearchTitle: "Zoeken",
  columnsManagementNoColumns: "Geen kolommen",
  columnsManagementShowHideAllText: "Toon/Verberg Alle",
  columnsManagementReset: "Reset",
  columnsManagementDeleteIconLabel: "Verwijderen",
  // Filter panel text
  filterPanelAddFilter: "Filter toevoegen",
  filterPanelRemoveAll: "Alles verwijderen",
  filterPanelDeleteIconLabel: "Verwijderen",
  filterPanelLogicOperator: "Logische operator",
  filterPanelOperator: "Operatoren",
  filterPanelOperatorAnd: "En",
  filterPanelOperatorOr: "Of",
  filterPanelColumns: "Kolommen",
  filterPanelInputLabel: "Waarde",
  filterPanelInputPlaceholder: "Filter waarde",
  // Filter operators text
  filterOperatorContains: "bevat",
  filterOperatorDoesNotContain: "bevat niet",
  filterOperatorEquals: "gelijk aan",
  filterOperatorDoesNotEqual: "niet gelijk aan",
  filterOperatorStartsWith: "begint met",
  filterOperatorEndsWith: "eindigt met",
  filterOperatorIs: "is",
  filterOperatorNot: "is niet",
  filterOperatorAfter: "is na",
  filterOperatorOnOrAfter: "is gelijk of er na",
  filterOperatorBefore: "is voor",
  filterOperatorOnOrBefore: "is gelijk of er voor",
  filterOperatorIsEmpty: "is leeg",
  filterOperatorIsNotEmpty: "is niet leeg",
  filterOperatorIsAnyOf: "is een van",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Bevat",
  headerFilterOperatorDoesNotContain: "Bevat niet",
  headerFilterOperatorEquals: "Gelijk aan",
  headerFilterOperatorDoesNotEqual: "Niet gelijk aan",
  headerFilterOperatorStartsWith: "Begint met",
  headerFilterOperatorEndsWith: "Eindigt met",
  headerFilterOperatorIs: "Is",
  headerFilterOperatorNot: "Is niet",
  headerFilterOperatorAfter: "Is na",
  headerFilterOperatorOnOrAfter: "Is op of na",
  headerFilterOperatorBefore: "Is voor",
  headerFilterOperatorOnOrBefore: "Is op of voor",
  headerFilterOperatorIsEmpty: "Is leeg",
  headerFilterOperatorIsNotEmpty: "Is niet leeg",
  headerFilterOperatorIsAnyOf: "Is een van",
  "headerFilterOperator=": "Gelijk aan",
  "headerFilterOperator!=": "Niet gelijk aan",
  "headerFilterOperator>": "Is groter dan",
  "headerFilterOperator>=": "Is groter dan of gelijk aan",
  "headerFilterOperator<": "Is kleiner dan",
  "headerFilterOperator<=": "Is kleiner dan of gelijk aan",
  // Filter values text
  filterValueAny: "alles",
  filterValueTrue: "waar",
  filterValueFalse: "onwaar",
  // Column menu text
  columnMenuLabel: "Menu",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Toon kolommen",
  columnMenuManageColumns: "Kolommen beheren",
  columnMenuFilter: "Filteren",
  columnMenuHideColumn: "Verbergen",
  columnMenuUnsort: "Annuleer sortering",
  columnMenuSortAsc: "Oplopend sorteren",
  columnMenuSortDesc: "Aflopend sorteren",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count > 1 ? `${count} actieve filters` : `${count} filter actief`,
  columnHeaderFiltersLabel: "Toon filters",
  columnHeaderSortIconLabel: "Sorteren",
  // Rows selected footer text
  footerRowSelected: (count) => count > 1 ? `${count.toLocaleString()} rijen geselecteerd` : `${count.toLocaleString()} rij geselecteerd`,
  // Total row amount footer text
  footerTotalRows: "Totaal:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} van ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Checkbox selectie",
  checkboxSelectionSelectAllRows: "Alle rijen selecteren",
  checkboxSelectionUnselectAllRows: "Alle rijen de-selecteren",
  checkboxSelectionSelectRow: "Rij selecteren",
  checkboxSelectionUnselectRow: "Rij de-selecteren",
  // Boolean cell text
  booleanCellTrueLabel: "waar",
  booleanCellFalseLabel: "onwaar",
  // Actions cell more text
  actionsCellMore: "meer",
  // Column pinning text
  pinToLeft: "Links vastzetten",
  pinToRight: "Rechts vastzetten",
  unpin: "Losmaken",
  // Tree Data
  treeDataGroupingHeaderName: "Groep",
  treeDataExpand: "Uitvouwen",
  treeDataCollapse: "Inklappen",
  // Grouping columns
  groupingColumnHeaderName: "Groep",
  groupColumn: (name) => `Groepeer op ${name}`,
  unGroupColumn: (name) => `Stop groeperen op ${name}`,
  // Master/detail
  detailPanelToggle: "Detailmenu in- of uitklappen",
  expandDetailPanel: "Uitklappen",
  collapseDetailPanel: "Inklappen",
  // Row reordering text
  rowReorderingHeaderName: "Rijen hersorteren",
  // Aggregation
  aggregationMenuItemHeader: "Aggregatie",
  aggregationFunctionLabelSum: "som",
  aggregationFunctionLabelAvg: "gem",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "grootte"
};
var nlNL2 = getGridLocalization(nlNLGrid, nlNL);

// node_modules/@mui/x-data-grid/locales/nnNO.js
var nnNOGrid = {
  // Root
  noRowsLabel: "Ingen rader",
  noResultsOverlayLabel: "Fann ingen resultat.",
  // Density selector toolbar button text
  toolbarDensity: "Tettheit",
  toolbarDensityLabel: "Tettheit",
  toolbarDensityCompact: "Kompakt",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Komfortabelt",
  // Columns selector toolbar button text
  toolbarColumns: "Kolonner",
  toolbarColumnsLabel: "Vel kolonner",
  // Filters toolbar button text
  toolbarFilters: "Filter",
  toolbarFiltersLabel: "Vis filter",
  toolbarFiltersTooltipHide: "Skjul filter",
  toolbarFiltersTooltipShow: "Vis filter",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Søk…",
  toolbarQuickFilterLabel: "Søk",
  toolbarQuickFilterDeleteIconLabel: "Slett",
  // Export selector toolbar button text
  toolbarExport: "Eksporter",
  toolbarExportLabel: "Eksporter",
  toolbarExportCSV: "Last ned som CSV",
  toolbarExportPrint: "Skriv ut",
  toolbarExportExcel: "Last ned som Excel",
  // Columns management text
  columnsManagementSearchTitle: "Søk",
  columnsManagementNoColumns: "Ingen kolonner",
  columnsManagementShowHideAllText: "Vis/skjul alle",
  columnsManagementReset: "Nullstill",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Legg til filter",
  filterPanelRemoveAll: "Fjern alle",
  filterPanelDeleteIconLabel: "Slett",
  filterPanelLogicOperator: "Logisk operator",
  filterPanelOperator: "Operator",
  filterPanelOperatorAnd: "Og",
  filterPanelOperatorOr: "Eller",
  filterPanelColumns: "Kolonner",
  filterPanelInputLabel: "Verdi",
  filterPanelInputPlaceholder: "Filter verdi",
  // Filter operators text
  filterOperatorContains: "inneheld",
  // filterOperatorDoesNotContain: 'does not contain',
  filterOperatorEquals: "er lik",
  // filterOperatorDoesNotEqual: 'does not equal',
  filterOperatorStartsWith: "startar med",
  filterOperatorEndsWith: "sluttar med",
  filterOperatorIs: "er",
  filterOperatorNot: "er ikkje",
  filterOperatorAfter: "er etter",
  filterOperatorOnOrAfter: "er på eller etter",
  filterOperatorBefore: "er før",
  filterOperatorOnOrBefore: "er på eller før",
  filterOperatorIsEmpty: "er tom",
  filterOperatorIsNotEmpty: "er ikkje tom",
  filterOperatorIsAnyOf: "er ein av",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Inneheld",
  // headerFilterOperatorDoesNotContain: 'Does not contain',
  headerFilterOperatorEquals: "Lik",
  // headerFilterOperatorDoesNotEqual: 'Does not equal',
  headerFilterOperatorStartsWith: "Startar på",
  headerFilterOperatorEndsWith: "Sluttar på",
  headerFilterOperatorIs: "Er",
  headerFilterOperatorNot: "Er ikkje",
  headerFilterOperatorAfter: "Er etter",
  headerFilterOperatorOnOrAfter: "Er på eller etter",
  headerFilterOperatorBefore: "Er før",
  headerFilterOperatorOnOrBefore: "Er på eller før",
  headerFilterOperatorIsEmpty: "Er tom",
  headerFilterOperatorIsNotEmpty: "Er ikkje tom",
  headerFilterOperatorIsAnyOf: "Er ein av",
  "headerFilterOperator=": "Lik",
  "headerFilterOperator!=": "Ikkje lik",
  "headerFilterOperator>": "Større enn",
  "headerFilterOperator>=": "Større enn eller lik",
  "headerFilterOperator<": "Mindre enn",
  "headerFilterOperator<=": "Mindre enn eller lik",
  // Filter values text
  filterValueAny: "nokon",
  filterValueTrue: "sant",
  filterValueFalse: "usant",
  // Column menu text
  columnMenuLabel: "Meny",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Vis kolonner",
  columnMenuManageColumns: "Administrer kolonner",
  columnMenuFilter: "Filter",
  columnMenuHideColumn: "Skjul",
  columnMenuUnsort: "Usorter",
  columnMenuSortAsc: "Sorter AUKANDE",
  columnMenuSortDesc: "Sorter SYNKANDE",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,
  columnHeaderFiltersLabel: "Vis filter",
  columnHeaderSortIconLabel: "Sorter",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} rader valt` : `${count.toLocaleString()} rad valt`,
  // Total row amount footer text
  footerTotalRows: "Totalt tal rader:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} av ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Avmerkingsboks",
  checkboxSelectionSelectAllRows: "Vel alle rader",
  checkboxSelectionUnselectAllRows: "Vel vekk alle rader",
  checkboxSelectionSelectRow: "Vel rad",
  checkboxSelectionUnselectRow: "Vel vekk rad",
  // Boolean cell text
  booleanCellTrueLabel: "sant",
  booleanCellFalseLabel: "usant",
  // Actions cell more text
  actionsCellMore: "meir",
  // Column pinning text
  pinToLeft: "Fest til venstre",
  pinToRight: "Fest til høgre",
  unpin: "Lausne",
  // Tree Data
  treeDataGroupingHeaderName: "Grupper",
  treeDataExpand: "vis barn",
  treeDataCollapse: "skjul barn",
  // Grouping columns
  groupingColumnHeaderName: "Grupper",
  groupColumn: (name) => `Grupper på ${name}`,
  unGroupColumn: (name) => `Slutt å grupper på ${name}`,
  // Master/detail
  detailPanelToggle: "Utvid/kollaps detalj panel",
  expandDetailPanel: "Utvid",
  collapseDetailPanel: "Kolaps",
  // Row reordering text
  rowReorderingHeaderName: "Radreorganisering",
  // Aggregation
  aggregationMenuItemHeader: "Aggregering",
  aggregationFunctionLabelSum: "sum",
  aggregationFunctionLabelAvg: "snitt",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "maks",
  aggregationFunctionLabelSize: "størrelse"
};
var nnNO2 = getGridLocalization(nnNOGrid, nnNO);

// node_modules/@mui/x-data-grid/locales/plPL.js
var plPLGrid = {
  // Root
  noRowsLabel: "Brak danych",
  noResultsOverlayLabel: "Nie znaleziono wyników.",
  // Density selector toolbar button text
  toolbarDensity: "Wysokość rzędu",
  toolbarDensityLabel: "Wysokość rzędu",
  toolbarDensityCompact: "Kompakt",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Komfort",
  // Columns selector toolbar button text
  toolbarColumns: "Kolumny",
  toolbarColumnsLabel: "Zaznacz kolumny",
  // Filters toolbar button text
  toolbarFilters: "Filtry",
  toolbarFiltersLabel: "Pokaż filtry",
  toolbarFiltersTooltipHide: "Ukryj filtry",
  toolbarFiltersTooltipShow: "Pokaż filtry",
  toolbarFiltersTooltipActive: (count) => `Liczba aktywnych filtrów: ${count}`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Wyszukaj…",
  toolbarQuickFilterLabel: "Szukaj",
  toolbarQuickFilterDeleteIconLabel: "Wyczyść",
  // Export selector toolbar button text
  toolbarExport: "Eksportuj",
  toolbarExportLabel: "Eksportuj",
  toolbarExportCSV: "Pobierz jako plik CSV",
  toolbarExportPrint: "Drukuj",
  toolbarExportExcel: "Pobierz jako plik Excel",
  // Columns management text
  columnsManagementSearchTitle: "Szukaj",
  columnsManagementNoColumns: "Brak kolumn",
  columnsManagementShowHideAllText: "Wyświetl/Ukryj wszystkie",
  columnsManagementReset: "Resetuj",
  columnsManagementDeleteIconLabel: "Wyczyść",
  // Filter panel text
  filterPanelAddFilter: "Dodaj filtr",
  filterPanelRemoveAll: "Usuń wszystkie",
  filterPanelDeleteIconLabel: "Usuń",
  filterPanelLogicOperator: "Operator logiczny",
  filterPanelOperator: "Operator",
  filterPanelOperatorAnd: "I",
  filterPanelOperatorOr: "Lub",
  filterPanelColumns: "Kolumny",
  filterPanelInputLabel: "Wartość",
  filterPanelInputPlaceholder: "Filtrowana wartość",
  // Filter operators text
  filterOperatorContains: "zawiera",
  filterOperatorDoesNotContain: "nie zawiera",
  filterOperatorEquals: "równa się",
  filterOperatorDoesNotEqual: "nie równa się",
  filterOperatorStartsWith: "zaczyna się od",
  filterOperatorEndsWith: "kończy się na",
  filterOperatorIs: "równa się",
  filterOperatorNot: "różne",
  filterOperatorAfter: "większe niż",
  filterOperatorOnOrAfter: "większe lub równe",
  filterOperatorBefore: "mniejsze niż",
  filterOperatorOnOrBefore: "mniejsze lub równe",
  filterOperatorIsEmpty: "jest pusty",
  filterOperatorIsNotEmpty: "nie jest pusty",
  filterOperatorIsAnyOf: "jest jednym z",
  "filterOperator=": "równa się",
  "filterOperator!=": "nie równa się",
  "filterOperator>": "większy niż",
  "filterOperator>=": "większy lub równy",
  "filterOperator<": "mniejszy niż",
  "filterOperator<=": "mniejszy lub równy",
  // Header filter operators text
  headerFilterOperatorContains: "Zawiera",
  headerFilterOperatorDoesNotContain: "Nie zawiera",
  headerFilterOperatorEquals: "Równa się",
  headerFilterOperatorDoesNotEqual: "Nie równa się",
  headerFilterOperatorStartsWith: "Zaczyna się od",
  headerFilterOperatorEndsWith: "Kończy się na",
  headerFilterOperatorIs: "Jest",
  headerFilterOperatorNot: "Niepuste",
  headerFilterOperatorAfter: "Jest po",
  headerFilterOperatorOnOrAfter: "Jest w lub po",
  headerFilterOperatorBefore: "Jest przed",
  headerFilterOperatorOnOrBefore: "Jest w lub przed",
  headerFilterOperatorIsEmpty: "Jest pusty",
  headerFilterOperatorIsNotEmpty: "Nie jest pusty",
  headerFilterOperatorIsAnyOf: "Jest jednym z",
  "headerFilterOperator=": "Równa się",
  "headerFilterOperator!=": "Nie równa się",
  "headerFilterOperator>": "Większy niż",
  "headerFilterOperator>=": "Większy lub równy",
  "headerFilterOperator<": "Mniejszy niż",
  "headerFilterOperator<=": "Mniejszy lub równy",
  // Filter values text
  filterValueAny: "dowolny",
  filterValueTrue: "prawda",
  filterValueFalse: "fałsz",
  // Column menu text
  columnMenuLabel: "Menu",
  columnMenuAriaLabel: (columnName) => `Menu kolumny: ${columnName}`,
  columnMenuShowColumns: "Pokaż wszystkie kolumny",
  columnMenuManageColumns: "Zarządzaj kolumnami",
  columnMenuFilter: "Filtr",
  columnMenuHideColumn: "Ukryj",
  columnMenuUnsort: "Anuluj sortowanie",
  columnMenuSortAsc: "Sortuj rosnąco",
  columnMenuSortDesc: "Sortuj malejąco",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `Liczba aktywnych filtrów: ${count}`,
  columnHeaderFiltersLabel: "Pokaż filtry",
  columnHeaderSortIconLabel: "Sortuj",
  // Rows selected footer text
  footerRowSelected: (count) => `Liczba wybranych wierszy: ${count.toLocaleString()}`,
  // Total row amount footer text
  footerTotalRows: "Łączna liczba wierszy:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} z ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Pole wyboru",
  checkboxSelectionSelectAllRows: "Zaznacz wszystkie wiersze",
  checkboxSelectionUnselectAllRows: "Odznacz wszystkie wiersze",
  checkboxSelectionSelectRow: "Zaznacz wiersz",
  checkboxSelectionUnselectRow: "Odznacz wiersz",
  // Boolean cell text
  booleanCellTrueLabel: "tak",
  booleanCellFalseLabel: "nie",
  // Actions cell more text
  actionsCellMore: "więcej",
  // Column pinning text
  pinToLeft: "Przypnij do lewej",
  pinToRight: "Przypnij do prawej",
  unpin: "Odepnij",
  // Tree Data
  treeDataGroupingHeaderName: "Grupa",
  treeDataExpand: "pokaż elementy potomne",
  treeDataCollapse: "ukryj elementy potomne",
  // Grouping columns
  groupingColumnHeaderName: "Grupa",
  groupColumn: (name) => `Grupuj według ${name}`,
  unGroupColumn: (name) => `Rozgrupuj ${name}`,
  // Master/detail
  // detailPanelToggle: 'Detail panel toggle',
  expandDetailPanel: "Rozwiń",
  collapseDetailPanel: "Zwiń",
  // Row reordering text
  rowReorderingHeaderName: "Porządkowanie wierszy",
  // Aggregation
  aggregationMenuItemHeader: "Agregacja",
  aggregationFunctionLabelSum: "suma",
  aggregationFunctionLabelAvg: "średnia",
  aggregationFunctionLabelMin: "minimum",
  aggregationFunctionLabelMax: "maximum",
  aggregationFunctionLabelSize: "rozmiar"
};
var plPL2 = getGridLocalization(plPLGrid, plPL);

// node_modules/@mui/x-data-grid/locales/ptBR.js
var ptBRGrid = {
  // Root
  noRowsLabel: "Nenhuma linha",
  noResultsOverlayLabel: "Nenhum resultado encontrado.",
  // Density selector toolbar button text
  toolbarDensity: "Densidade",
  toolbarDensityLabel: "Densidade",
  toolbarDensityCompact: "Compacto",
  toolbarDensityStandard: "Padrão",
  toolbarDensityComfortable: "Confortável",
  // Columns selector toolbar button text
  toolbarColumns: "Colunas",
  toolbarColumnsLabel: "Exibir seletor de colunas",
  // Filters toolbar button text
  toolbarFilters: "Filtros",
  toolbarFiltersLabel: "Exibir filtros",
  toolbarFiltersTooltipHide: "Ocultar filtros",
  toolbarFiltersTooltipShow: "Exibir filtros",
  toolbarFiltersTooltipActive: (count) => `${count} ${count !== 1 ? "filtros" : "filtro"} ${count !== 1 ? "ativos" : "ativo"}`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Procurar…",
  toolbarQuickFilterLabel: "Procurar",
  toolbarQuickFilterDeleteIconLabel: "Limpar",
  // Export selector toolbar button text
  toolbarExport: "Exportar",
  toolbarExportLabel: "Exportar",
  toolbarExportCSV: "Baixar como CSV",
  toolbarExportPrint: "Imprimir",
  toolbarExportExcel: "Baixar como Excel",
  // Columns management text
  columnsManagementSearchTitle: "Buscar",
  columnsManagementNoColumns: "Nenhuma coluna",
  columnsManagementShowHideAllText: "Mostrar/Ocultar Todas",
  columnsManagementReset: "Redefinir",
  columnsManagementDeleteIconLabel: "Limpar",
  // Filter panel text
  filterPanelAddFilter: "Adicionar filtro",
  filterPanelRemoveAll: "Remover todos",
  filterPanelDeleteIconLabel: "Excluir",
  filterPanelLogicOperator: "Operador lógico",
  filterPanelOperator: "Operador",
  filterPanelOperatorAnd: "E",
  filterPanelOperatorOr: "Ou",
  filterPanelColumns: "Colunas",
  filterPanelInputLabel: "Valor",
  filterPanelInputPlaceholder: "Filtrar valor",
  // Filter operators text
  filterOperatorContains: "contém",
  filterOperatorDoesNotContain: "não contém",
  filterOperatorEquals: "é igual a",
  filterOperatorDoesNotEqual: "não é igual a",
  filterOperatorStartsWith: "começa com",
  filterOperatorEndsWith: "termina com",
  filterOperatorIs: "é",
  filterOperatorNot: "não é",
  filterOperatorAfter: "após",
  filterOperatorOnOrAfter: "em ou após",
  filterOperatorBefore: "antes de",
  filterOperatorOnOrBefore: "em ou antes de",
  filterOperatorIsEmpty: "está vazio",
  filterOperatorIsNotEmpty: "não está vazio",
  filterOperatorIsAnyOf: "é qualquer um dos",
  "filterOperator=": "igual à",
  "filterOperator!=": "diferente de",
  "filterOperator>": "maior que",
  "filterOperator>=": "maior ou igual que",
  "filterOperator<": "menor que",
  "filterOperator<=": "menor ou igual que",
  // Header filter operators text
  headerFilterOperatorContains: "Contém",
  headerFilterOperatorDoesNotContain: "Não contém",
  headerFilterOperatorEquals: "Igual",
  headerFilterOperatorDoesNotEqual: "Não é igual a",
  headerFilterOperatorStartsWith: "Começa com",
  headerFilterOperatorEndsWith: "Termina com",
  headerFilterOperatorIs: "É",
  headerFilterOperatorNot: "Não é",
  headerFilterOperatorAfter: "Depois de",
  headerFilterOperatorOnOrAfter: "Está entre ou depois de",
  headerFilterOperatorBefore: "Antes de",
  headerFilterOperatorOnOrBefore: "Está entre ou antes de",
  headerFilterOperatorIsEmpty: "É vazio",
  headerFilterOperatorIsNotEmpty: "Não é vazio",
  headerFilterOperatorIsAnyOf: "É algum",
  "headerFilterOperator=": "Igual",
  "headerFilterOperator!=": "Não igual",
  "headerFilterOperator>": "Maior que",
  "headerFilterOperator>=": "Maior que ou igual a",
  "headerFilterOperator<": "Menor que",
  "headerFilterOperator<=": "Menor que ou igual a",
  // Filter values text
  filterValueAny: "qualquer",
  filterValueTrue: "verdadeiro",
  filterValueFalse: "falso",
  // Column menu text
  columnMenuLabel: "Menu",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Exibir colunas",
  columnMenuManageColumns: "Gerir colunas",
  columnMenuFilter: "Filtrar",
  columnMenuHideColumn: "Ocultar",
  columnMenuUnsort: "Desfazer ordenação",
  columnMenuSortAsc: "Ordenar do menor para o maior",
  columnMenuSortDesc: "Ordenar do maior para o menor",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `${count} ${count !== 1 ? "filtros" : "filtro"} ${count !== 1 ? "ativos" : "ativo"}`,
  columnHeaderFiltersLabel: "Exibir Filtros",
  columnHeaderSortIconLabel: "Ordenar",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} linhas selecionadas` : `${count.toLocaleString()} linha selecionada`,
  // Total row amount footer text
  footerTotalRows: "Total de linhas:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} de ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Seleção",
  checkboxSelectionSelectAllRows: "Selecionar todas linhas",
  checkboxSelectionUnselectAllRows: "Deselecionar todas linhas",
  checkboxSelectionSelectRow: "Selecionar linha",
  checkboxSelectionUnselectRow: "Deselecionar linha",
  // Boolean cell text
  booleanCellTrueLabel: "sim",
  booleanCellFalseLabel: "não",
  // Actions cell more text
  actionsCellMore: "mais",
  // Column pinning text
  pinToLeft: "Fixar à esquerda",
  pinToRight: "Fixar à direita",
  unpin: "Desafixar",
  // Tree Data
  treeDataGroupingHeaderName: "Grupo",
  treeDataExpand: "mostrar filhos",
  treeDataCollapse: "esconder filhos",
  // Grouping columns
  groupingColumnHeaderName: "Grupo",
  groupColumn: (name) => `Agrupar por ${name}`,
  unGroupColumn: (name) => `Parar agrupamento por ${name}`,
  // Master/detail
  detailPanelToggle: "Painel de detalhes",
  expandDetailPanel: "Expandir",
  collapseDetailPanel: "Esconder",
  // Row reordering text
  rowReorderingHeaderName: "Reorganizar linhas",
  // Aggregation
  aggregationMenuItemHeader: "Agrupar",
  aggregationFunctionLabelSum: "soma",
  aggregationFunctionLabelAvg: "média",
  aggregationFunctionLabelMin: "mín",
  aggregationFunctionLabelMax: "máx",
  aggregationFunctionLabelSize: "tamanho"
};
var ptBR2 = getGridLocalization(ptBRGrid, ptBR);

// node_modules/@mui/x-data-grid/locales/roRO.js
var roROGrid = {
  // Root
  noRowsLabel: "Lipsă date",
  noResultsOverlayLabel: "Nu au fost găsite rezultate.",
  // Density selector toolbar button text
  toolbarDensity: "Înălțime rând",
  toolbarDensityLabel: "Înălțime rând",
  toolbarDensityCompact: "Compact",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Lat",
  // Columns selector toolbar button text
  toolbarColumns: "Coloane",
  toolbarColumnsLabel: "Afișează selecție coloane",
  // Filters toolbar button text
  toolbarFilters: "Filtru",
  toolbarFiltersLabel: "Afișează filtru",
  toolbarFiltersTooltipHide: "Ascunde filtru",
  toolbarFiltersTooltipShow: "Afișează filtru",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} filtru activ` : `${count} filtru activ`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Căutare…",
  toolbarQuickFilterLabel: "Căutare",
  toolbarQuickFilterDeleteIconLabel: "Ștergere",
  // Export selector toolbar button text
  toolbarExport: "Export",
  toolbarExportLabel: "Export",
  toolbarExportCSV: "Download în format CSV",
  toolbarExportPrint: "Printare",
  toolbarExportExcel: "Download în format Excel",
  // Columns management text
  columnsManagementSearchTitle: "Caută",
  columnsManagementNoColumns: "Nicio coloană",
  columnsManagementShowHideAllText: "Arată/Ascunde tot",
  columnsManagementReset: "Resetează",
  columnsManagementDeleteIconLabel: "Șterge",
  // Filter panel text
  filterPanelAddFilter: "Adăugare filtru",
  filterPanelRemoveAll: "Șterge tot",
  filterPanelDeleteIconLabel: "Ștergere",
  filterPanelLogicOperator: "Operatori logici",
  filterPanelOperator: "Operatori",
  filterPanelOperatorAnd: "Și",
  filterPanelOperatorOr: "Sau",
  filterPanelColumns: "Coloane",
  filterPanelInputLabel: "Valoare",
  filterPanelInputPlaceholder: "Filtrare valoare",
  // Filter operators text
  filterOperatorContains: "conține",
  filterOperatorDoesNotContain: "nu conține",
  filterOperatorEquals: "este egal cu",
  filterOperatorDoesNotEqual: "nu este egal cu",
  filterOperatorStartsWith: "începe cu",
  filterOperatorEndsWith: "se termină cu",
  filterOperatorIs: "este",
  filterOperatorNot: "nu este",
  filterOperatorAfter: "este după",
  filterOperatorOnOrAfter: "este la sau după",
  filterOperatorBefore: "este înainte de",
  filterOperatorOnOrBefore: "este la sau înainte de",
  filterOperatorIsEmpty: "este gol",
  filterOperatorIsNotEmpty: "nu este gol",
  filterOperatorIsAnyOf: "este una din valori",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Conține",
  headerFilterOperatorDoesNotContain: "Nu conține",
  headerFilterOperatorEquals: "Egal cu",
  headerFilterOperatorDoesNotEqual: "Nu este egal cu",
  headerFilterOperatorStartsWith: "Începe cu",
  headerFilterOperatorEndsWith: "Se termină cu",
  headerFilterOperatorIs: "Este",
  headerFilterOperatorNot: "Nu este",
  headerFilterOperatorAfter: "Este după",
  headerFilterOperatorOnOrAfter: "Este la sau după",
  headerFilterOperatorBefore: "Este înainte de",
  headerFilterOperatorOnOrBefore: "este la sau înainte de",
  headerFilterOperatorIsEmpty: "Este gol",
  headerFilterOperatorIsNotEmpty: "Nu este gol",
  headerFilterOperatorIsAnyOf: "Este una din valori",
  "headerFilterOperator=": "Egal cu",
  "headerFilterOperator!=": "Nu este egal cu",
  "headerFilterOperator>": "Mai mare decât",
  "headerFilterOperator>=": "Mai mare sau egal cu",
  "headerFilterOperator<": "Mai mic decât",
  "headerFilterOperator<=": "Mai mic sau egal cu",
  // Filter values text
  filterValueAny: "Aleatoriu",
  filterValueTrue: "Da",
  filterValueFalse: "Nu",
  // Column menu text
  columnMenuLabel: "Meniu",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Afișează toate coloanele",
  columnMenuManageColumns: "Gestionează coloane",
  columnMenuFilter: "Filtru",
  columnMenuHideColumn: "Ascunde",
  columnMenuUnsort: "Dezactivare sortare",
  columnMenuSortAsc: "Sortează crescător",
  columnMenuSortDesc: "Sortează descrescător",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} filtru activ` : `${count} filtru activ`,
  columnHeaderFiltersLabel: "Afișează filtru",
  columnHeaderSortIconLabel: "Sortare",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} Înregistrări selectate` : `${count.toLocaleString()} Înregistrare selectată`,
  // Total row amount footer text
  footerTotalRows: "Total:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} din ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Checkbox Selecție",
  checkboxSelectionSelectAllRows: "Selectare toate rândurile",
  checkboxSelectionUnselectAllRows: "Deselectare toate rândurile",
  checkboxSelectionSelectRow: "Selectare rând",
  checkboxSelectionUnselectRow: "Deselectare rând",
  // Boolean cell text
  booleanCellTrueLabel: "Da",
  booleanCellFalseLabel: "Nu",
  // Actions cell more text
  actionsCellMore: "Mai multe",
  // Column pinning text
  pinToLeft: "Fixare în stânga",
  pinToRight: "Fixare în dreapta",
  unpin: "Anulare fixare",
  // Tree Data
  treeDataGroupingHeaderName: "Grup",
  treeDataExpand: "Afișare copii",
  treeDataCollapse: "Ascundere copii",
  // Grouping columns
  groupingColumnHeaderName: "Grupare",
  groupColumn: (name) => `Grupare după ${name}`,
  unGroupColumn: (name) => `Anulare Grupare după ${name}`,
  // Master/detail
  detailPanelToggle: "Comutare panou detalii",
  expandDetailPanel: "Extindere",
  collapseDetailPanel: "Restrângere",
  // Row reordering text
  rowReorderingHeaderName: "Reordonare rânduri",
  // Aggregation
  aggregationMenuItemHeader: "Agregare",
  aggregationFunctionLabelSum: "Sumă",
  aggregationFunctionLabelAvg: "Medie",
  aggregationFunctionLabelMin: "Minim",
  aggregationFunctionLabelMax: "Maxim",
  aggregationFunctionLabelSize: "Numărul elementelor"
};
var roRO2 = getGridLocalization(roROGrid, roRO);

// node_modules/@mui/x-data-grid/locales/ruRU.js
function getPluralForm2(count, options) {
  const penultimateDigit = Math.floor(count / 10) % 10;
  const lastDigit = count % 10;
  let pluralForm = options.many;
  if (penultimateDigit !== 1 && lastDigit > 1 && lastDigit < 5) {
    pluralForm = options.few;
  } else if (penultimateDigit !== 1 && lastDigit === 1) {
    pluralForm = options.one;
  }
  return `${count} ${pluralForm}`;
}
var ruRUGrid = {
  // Root
  noRowsLabel: "Нет строк",
  noResultsOverlayLabel: "Данные не найдены.",
  // Density selector toolbar button text
  toolbarDensity: "Высота строки",
  toolbarDensityLabel: "Высота строки",
  toolbarDensityCompact: "Компактная",
  toolbarDensityStandard: "Стандартная",
  toolbarDensityComfortable: "Комфортная",
  // Columns selector toolbar button text
  toolbarColumns: "Столбцы",
  toolbarColumnsLabel: "Выделите столбцы",
  // Filters toolbar button text
  toolbarFilters: "Фильтры",
  toolbarFiltersLabel: "Показать фильтры",
  toolbarFiltersTooltipHide: "Скрыть фильтры",
  toolbarFiltersTooltipShow: "Показать фильтры",
  toolbarFiltersTooltipActive: (count) => getPluralForm2(count, {
    one: "активный фильтр",
    few: "активных фильтра",
    many: "активных фильтров"
  }),
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Поиск…",
  toolbarQuickFilterLabel: "Поиск",
  toolbarQuickFilterDeleteIconLabel: "Очистить",
  // Export selector toolbar button text
  toolbarExport: "Экспорт",
  toolbarExportLabel: "Экспорт",
  toolbarExportCSV: "Скачать в формате CSV",
  toolbarExportPrint: "Печать",
  toolbarExportExcel: "Скачать в формате Excel",
  // Columns management text
  columnsManagementSearchTitle: "Поиск",
  columnsManagementNoColumns: "Нет столбцов",
  columnsManagementShowHideAllText: "Показать/Скрыть Всё",
  columnsManagementReset: "Сбросить",
  columnsManagementDeleteIconLabel: "Очистить",
  // Filter panel text
  filterPanelAddFilter: "Добавить фильтр",
  filterPanelRemoveAll: "Очистить фильтр",
  filterPanelDeleteIconLabel: "Удалить",
  filterPanelLogicOperator: "Логические операторы",
  filterPanelOperator: "Операторы",
  filterPanelOperatorAnd: "И",
  filterPanelOperatorOr: "Или",
  filterPanelColumns: "Столбцы",
  filterPanelInputLabel: "Значение",
  filterPanelInputPlaceholder: "Значение фильтра",
  // Filter operators text
  filterOperatorContains: "содержит",
  filterOperatorDoesNotContain: "не содержит",
  filterOperatorEquals: "равен",
  filterOperatorDoesNotEqual: "не равен",
  filterOperatorStartsWith: "начинается с",
  filterOperatorEndsWith: "заканчивается на",
  filterOperatorIs: "равен",
  filterOperatorNot: "не равен",
  filterOperatorAfter: "больше чем",
  filterOperatorOnOrAfter: "больше или равно",
  filterOperatorBefore: "меньше чем",
  filterOperatorOnOrBefore: "меньше или равно",
  filterOperatorIsEmpty: "пустой",
  filterOperatorIsNotEmpty: "не пустой",
  filterOperatorIsAnyOf: "любой из",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "содержит",
  headerFilterOperatorDoesNotContain: "не содержит",
  headerFilterOperatorEquals: "равен",
  headerFilterOperatorDoesNotEqual: "не равен",
  headerFilterOperatorStartsWith: "начинается с",
  headerFilterOperatorEndsWith: "заканчивается на",
  headerFilterOperatorIs: "равен",
  headerFilterOperatorNot: "не равен",
  headerFilterOperatorAfter: "больше чем",
  headerFilterOperatorOnOrAfter: "больше или равно",
  headerFilterOperatorBefore: "меньше чем",
  headerFilterOperatorOnOrBefore: "меньше или равно",
  headerFilterOperatorIsEmpty: "пустой",
  headerFilterOperatorIsNotEmpty: "не пустой",
  headerFilterOperatorIsAnyOf: "любой из",
  "headerFilterOperator=": "содержит",
  "headerFilterOperator!=": "не содержит",
  "headerFilterOperator>": "больше чем",
  "headerFilterOperator>=": "больше или равно",
  "headerFilterOperator<": "меньше чем",
  "headerFilterOperator<=": "меньше или равно",
  // Filter values text
  filterValueAny: "любой",
  filterValueTrue: "истина",
  filterValueFalse: "ложь",
  // Column menu text
  columnMenuLabel: "Меню",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Показать столбцы",
  columnMenuManageColumns: "Управление колонками",
  columnMenuFilter: "Фильтр",
  columnMenuHideColumn: "Скрыть",
  columnMenuUnsort: "Отменить сортировку",
  columnMenuSortAsc: "Сортировать по возрастанию",
  columnMenuSortDesc: "Сортировать по убыванию",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => getPluralForm2(count, {
    one: "активный фильтр",
    few: "активных фильтра",
    many: "активных фильтров"
  }),
  columnHeaderFiltersLabel: "Показать фильтры",
  columnHeaderSortIconLabel: "Сортировать",
  // Rows selected footer text
  footerRowSelected: (count) => getPluralForm2(count, {
    one: "строка выбрана",
    few: "строки выбраны",
    many: "строк выбрано"
  }),
  // Total row amount footer text
  footerTotalRows: "Всего строк:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} из ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Выбор флажка",
  checkboxSelectionSelectAllRows: "Выбрать все строки",
  checkboxSelectionUnselectAllRows: "Отменить выбор всех строк",
  checkboxSelectionSelectRow: "Выбрать строку",
  checkboxSelectionUnselectRow: "Отменить выбор строки",
  // Boolean cell text
  booleanCellTrueLabel: "истина",
  booleanCellFalseLabel: "ложь",
  // Actions cell more text
  actionsCellMore: "ещё",
  // Column pinning text
  pinToLeft: "Закрепить слева",
  pinToRight: "Закрепить справа",
  unpin: "Открепить",
  // Tree Data
  treeDataGroupingHeaderName: "Группа",
  treeDataExpand: "показать дочерние элементы",
  treeDataCollapse: "скрыть дочерние элементы",
  // Grouping columns
  groupingColumnHeaderName: "Группа",
  groupColumn: (name) => `Сгруппировать по ${name}`,
  unGroupColumn: (name) => `Разгруппировать по ${name}`,
  // Master/detail
  detailPanelToggle: "Детали",
  expandDetailPanel: "Развернуть",
  collapseDetailPanel: "Свернуть",
  // Row reordering text
  rowReorderingHeaderName: "Изменение порядка строк",
  // Aggregation
  aggregationMenuItemHeader: "Объединение данных",
  aggregationFunctionLabelSum: "сумм",
  aggregationFunctionLabelAvg: "срзнач",
  aggregationFunctionLabelMin: "мин",
  aggregationFunctionLabelMax: "макс",
  aggregationFunctionLabelSize: "счет"
};
var ruRU2 = getGridLocalization(ruRUGrid, ruRU);

// node_modules/@mui/x-data-grid/locales/skSK.js
var skSKGrid = {
  // Root
  noRowsLabel: "Žiadne záznamy",
  noResultsOverlayLabel: "Nenašli sa žadne výsledky.",
  // Density selector toolbar button text
  toolbarDensity: "Hustota",
  toolbarDensityLabel: "Hustota",
  toolbarDensityCompact: "Kompaktná",
  toolbarDensityStandard: "Štandartná",
  toolbarDensityComfortable: "Komfortná",
  // Columns selector toolbar button text
  toolbarColumns: "Stĺpce",
  toolbarColumnsLabel: "Vybrať stĺpce",
  // Filters toolbar button text
  toolbarFilters: "Filtre",
  toolbarFiltersLabel: "Zobraziť filtre",
  toolbarFiltersTooltipHide: "Skryť filtre ",
  toolbarFiltersTooltipShow: "Zobraziť filtre",
  toolbarFiltersTooltipActive: (count) => {
    let pluralForm = "aktívnych filtrov";
    if (count > 1 && count < 5) {
      pluralForm = "aktívne filtre";
    } else if (count === 1) {
      pluralForm = "aktívny filter";
    }
    return `${count} ${pluralForm}`;
  },
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Vyhľadať…",
  toolbarQuickFilterLabel: "Vyhľadať",
  toolbarQuickFilterDeleteIconLabel: "Vymazať",
  // Export selector toolbar button text
  toolbarExport: "Export",
  toolbarExportLabel: "Export",
  toolbarExportCSV: "Stiahnuť ako CSV",
  toolbarExportPrint: "Vytlačiť",
  toolbarExportExcel: "Stiahnuť ako Excel",
  // Columns management text
  columnsManagementSearchTitle: "Vyhľadať",
  columnsManagementNoColumns: "Žiadne stĺpce",
  columnsManagementShowHideAllText: "Zobraziť/Skryť všetko",
  columnsManagementReset: "Resetovať",
  columnsManagementDeleteIconLabel: "Vymazať",
  // Filter panel text
  filterPanelAddFilter: "Pridať filter",
  filterPanelRemoveAll: "Odstrániť všetky",
  filterPanelDeleteIconLabel: "Odstrániť",
  filterPanelLogicOperator: "Logický operátor",
  filterPanelOperator: "Operátory",
  filterPanelOperatorAnd: "A",
  filterPanelOperatorOr: "Alebo",
  filterPanelColumns: "Stĺpce",
  filterPanelInputLabel: "Hodnota",
  filterPanelInputPlaceholder: "Hodnota filtra",
  // Filter operators text
  filterOperatorContains: "obsahuje",
  filterOperatorDoesNotContain: "neobsahuje",
  filterOperatorEquals: "rovná sa",
  filterOperatorDoesNotEqual: "nerovná sa",
  filterOperatorStartsWith: "začína s",
  filterOperatorEndsWith: "končí na",
  filterOperatorIs: "je",
  filterOperatorNot: "nie je",
  filterOperatorAfter: "je po",
  filterOperatorOnOrAfter: "je na alebo po",
  filterOperatorBefore: "je pred",
  filterOperatorOnOrBefore: "je na alebo skôr",
  filterOperatorIsEmpty: "je prázdny",
  filterOperatorIsNotEmpty: "nie je prázdny",
  filterOperatorIsAnyOf: "je jeden z",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Obsahuje",
  headerFilterOperatorDoesNotContain: "Neobsahuje",
  headerFilterOperatorEquals: "Rovná sa",
  headerFilterOperatorDoesNotEqual: "Nerovná sa",
  headerFilterOperatorStartsWith: "Začína s",
  headerFilterOperatorEndsWith: "Končí na",
  headerFilterOperatorIs: "Je",
  headerFilterOperatorNot: "Nie je",
  headerFilterOperatorAfter: "Je po",
  headerFilterOperatorOnOrAfter: "Je na alebo po",
  headerFilterOperatorBefore: "Je pred",
  headerFilterOperatorOnOrBefore: "Je na alebo skôr",
  headerFilterOperatorIsEmpty: "Je prázdny",
  headerFilterOperatorIsNotEmpty: "Nie je prázdny",
  headerFilterOperatorIsAnyOf: "Je jeden z",
  "headerFilterOperator=": "Rovná sa",
  "headerFilterOperator!=": "Nerovná sa",
  "headerFilterOperator>": "Väčší ako",
  "headerFilterOperator>=": "Väčší ako alebo rovný",
  "headerFilterOperator<": "Menší ako",
  "headerFilterOperator<=": "Menší ako alebo rovný",
  // Filter values text
  filterValueAny: "akýkoľvek",
  filterValueTrue: "áno",
  filterValueFalse: "nie",
  // Column menu text
  columnMenuLabel: "Menu",
  columnMenuAriaLabel: (columnName) => `Ponuka stĺpca ${columnName}`,
  columnMenuShowColumns: "Zobraziť stĺpce",
  columnMenuManageColumns: "Spravovať stĺpce",
  columnMenuFilter: "Filter",
  columnMenuHideColumn: "Skryť",
  columnMenuUnsort: "Zrušiť filtre",
  columnMenuSortAsc: "Zoradiť vzostupne",
  columnMenuSortDesc: "Zoradiť zostupne",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => {
    let pluralForm = "aktívnych filtrov";
    if (count > 1 && count < 5) {
      pluralForm = "aktívne filtre";
    } else if (count === 1) {
      pluralForm = "aktívny filter";
    }
    return `${count} ${pluralForm}`;
  },
  columnHeaderFiltersLabel: "Zobraziť filtre",
  columnHeaderSortIconLabel: "Filtrovať",
  // Rows selected footer text
  footerRowSelected: (count) => {
    let pluralForm = "vybraných záznamov";
    if (count > 1 && count < 5) {
      pluralForm = "vybrané záznamy";
    } else if (count === 1) {
      pluralForm = "vybraný záznam";
    }
    return `${count} ${pluralForm}`;
  },
  // Total row amount footer text
  footerTotalRows: "Riadkov spolu:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => {
    const str = totalCount.toString();
    const firstDigit = str[0];
    const op = ["4", "6", "7"].includes(firstDigit) || firstDigit === "1" && str.length % 3 === 0 ? "zo" : "z";
    return `${visibleCount.toLocaleString()} ${op} ${totalCount.toLocaleString()}`;
  },
  // Checkbox selection text
  checkboxSelectionHeaderName: "Výber riadku",
  checkboxSelectionSelectAllRows: "Vybrať všetky riadky",
  checkboxSelectionUnselectAllRows: "Zrušiť výber všetkých riadkov",
  checkboxSelectionSelectRow: "Vyber riadok",
  checkboxSelectionUnselectRow: "Zruš výber riadku",
  // Boolean cell text
  booleanCellTrueLabel: "áno",
  booleanCellFalseLabel: "nie",
  // Actions cell more text
  actionsCellMore: "viac",
  // Column pinning text
  pinToLeft: "Pripnúť na ľavo",
  pinToRight: "Pripnúť na pravo",
  unpin: "Odopnúť",
  // Tree Data
  treeDataGroupingHeaderName: "Skupina",
  treeDataExpand: "zobraziť potomkov",
  treeDataCollapse: "skryť potomkov",
  // Grouping columns
  groupingColumnHeaderName: "Skupina",
  groupColumn: (name) => `Zoskupiť podľa ${name}`,
  unGroupColumn: (name) => `Prestať zoskupovať podľa ${name}`,
  // Master/detail
  detailPanelToggle: "Prepnúť detail panelu",
  expandDetailPanel: "Rozbaliť",
  collapseDetailPanel: "Zbaliť",
  // Row reordering text
  rowReorderingHeaderName: "Preusporiadávanie riadkov",
  // Aggregation
  aggregationMenuItemHeader: "Agregácia",
  aggregationFunctionLabelSum: "suma",
  aggregationFunctionLabelAvg: "priemer",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "počet"
};
var skSK2 = getGridLocalization(skSKGrid, skSK);

// node_modules/@mui/x-data-grid/locales/svSE.js
var svSEGrid = {
  // Root
  noRowsLabel: "Inga rader",
  noResultsOverlayLabel: "Inga resultat funna.",
  // Density selector toolbar button text
  toolbarDensity: "Densitet",
  toolbarDensityLabel: "Densitet",
  toolbarDensityCompact: "Kompakt",
  toolbarDensityStandard: "Standard",
  toolbarDensityComfortable: "Luftig",
  // Columns selector toolbar button text
  toolbarColumns: "Kolumner",
  toolbarColumnsLabel: "Välj kolumner",
  // Filters toolbar button text
  toolbarFilters: "Filter",
  toolbarFiltersLabel: "Visa filter",
  toolbarFiltersTooltipHide: "Dölj filter",
  toolbarFiltersTooltipShow: "Visa filter",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} aktiva filter` : `${count} aktivt filter`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Sök…",
  toolbarQuickFilterLabel: "Sök",
  toolbarQuickFilterDeleteIconLabel: "Rensa",
  // Export selector toolbar button text
  toolbarExport: "Exportera",
  toolbarExportLabel: "Exportera",
  toolbarExportCSV: "Ladda ner som CSV",
  toolbarExportPrint: "Skriv ut",
  toolbarExportExcel: "Ladda ner som Excel",
  // Columns management text
  columnsManagementSearchTitle: "Sök",
  columnsManagementNoColumns: "Inga kolumner",
  columnsManagementShowHideAllText: "Visa/Dölj alla",
  columnsManagementReset: "Återställ",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Lägg till filter",
  filterPanelRemoveAll: "Ta bort alla",
  filterPanelDeleteIconLabel: "Ta bort",
  filterPanelLogicOperator: "Logisk operator",
  filterPanelOperator: "Operator",
  filterPanelOperatorAnd: "Och",
  filterPanelOperatorOr: "Eller",
  filterPanelColumns: "Kolumner",
  filterPanelInputLabel: "Värde",
  filterPanelInputPlaceholder: "Filtervärde",
  // Filter operators text
  filterOperatorContains: "innehåller",
  filterOperatorDoesNotContain: "innehåller inte",
  filterOperatorEquals: "lika med",
  filterOperatorDoesNotEqual: "inte lika med",
  filterOperatorStartsWith: "börjar med",
  filterOperatorEndsWith: "slutar med",
  filterOperatorIs: "är",
  filterOperatorNot: "är inte",
  filterOperatorAfter: "är efter",
  filterOperatorOnOrAfter: "är på eller efter",
  filterOperatorBefore: "är innan",
  filterOperatorOnOrBefore: "är på eller innan",
  filterOperatorIsEmpty: "är tom",
  filterOperatorIsNotEmpty: "är inte tom",
  filterOperatorIsAnyOf: "är någon av",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Innehåller",
  headerFilterOperatorDoesNotContain: "Innehåller inte",
  headerFilterOperatorEquals: "Lika med",
  headerFilterOperatorDoesNotEqual: "Inte lika med",
  headerFilterOperatorStartsWith: "Börjar med",
  headerFilterOperatorEndsWith: "Slutar med",
  headerFilterOperatorIs: "Är",
  headerFilterOperatorNot: "Är inte",
  headerFilterOperatorAfter: "Är efter",
  headerFilterOperatorOnOrAfter: "Är på eller efter",
  headerFilterOperatorBefore: "Är innan",
  headerFilterOperatorOnOrBefore: "Är på eller innan",
  headerFilterOperatorIsEmpty: "Är tom",
  headerFilterOperatorIsNotEmpty: "Är inte tom",
  headerFilterOperatorIsAnyOf: "Innehåller någon av",
  "headerFilterOperator=": "Lika med",
  "headerFilterOperator!=": "Inte lika med",
  "headerFilterOperator>": "Större än",
  "headerFilterOperator>=": "Större eller lika med",
  "headerFilterOperator<": "Mindre än",
  "headerFilterOperator<=": "Mindre eller lika med",
  // Filter values text
  filterValueAny: "något",
  filterValueTrue: "sant",
  filterValueFalse: "falskt",
  // Column menu text
  columnMenuLabel: "Meny",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Visa kolumner",
  columnMenuManageColumns: "Hantera kolumner",
  columnMenuFilter: "Filtrera",
  columnMenuHideColumn: "Dölj",
  columnMenuUnsort: "Ta bort sortering",
  columnMenuSortAsc: "Sortera stigande",
  columnMenuSortDesc: "Sortera fallande",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} aktiva filter` : `${count} aktivt filter`,
  columnHeaderFiltersLabel: "Visa filter",
  columnHeaderSortIconLabel: "Sortera",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} rader markerade` : `${count.toLocaleString()} rad markerad`,
  // Total row amount footer text
  footerTotalRows: "Totalt antal rader:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} av ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Markering med kryssruta",
  checkboxSelectionSelectAllRows: "Markera alla rader",
  checkboxSelectionUnselectAllRows: "Avmarkera alla rader",
  checkboxSelectionSelectRow: "Markera rad",
  checkboxSelectionUnselectRow: "Avmarkera rad",
  // Boolean cell text
  booleanCellTrueLabel: "ja",
  booleanCellFalseLabel: "nej",
  // Actions cell more text
  actionsCellMore: "mer",
  // Column pinning text
  pinToLeft: "Lås till vänster",
  pinToRight: "Lås till höger",
  unpin: "Lås upp",
  // Tree Data
  treeDataGroupingHeaderName: "Grupp",
  treeDataExpand: "visa underordnade",
  treeDataCollapse: "dölj underordnade",
  // Grouping columns
  groupingColumnHeaderName: "Grupp",
  groupColumn: (name) => `Gruppera efter ${name}`,
  unGroupColumn: (name) => `Sluta gruppera efter ${name}`,
  // Master/detail
  detailPanelToggle: "Växla detaljpanel",
  expandDetailPanel: "Expandera",
  collapseDetailPanel: "Kollapsa",
  // Row reordering text
  rowReorderingHeaderName: "Ordna om rader",
  // Aggregation
  aggregationMenuItemHeader: "Aggregering",
  aggregationFunctionLabelSum: "summa",
  aggregationFunctionLabelAvg: "medel",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "antal"
};
var svSE2 = getGridLocalization(svSEGrid, svSE);

// node_modules/@mui/x-data-grid/locales/trTR.js
var trTRGrid = {
  // Root
  noRowsLabel: "Satır yok",
  noResultsOverlayLabel: "Sonuç bulunamadı.",
  // Density selector toolbar button text
  toolbarDensity: "Yoğunluk",
  toolbarDensityLabel: "Yoğunluk",
  toolbarDensityCompact: "Sıkı",
  toolbarDensityStandard: "Standart",
  toolbarDensityComfortable: "Rahat",
  // Columns selector toolbar button text
  toolbarColumns: "Sütunlar",
  toolbarColumnsLabel: "Sütun seç",
  // Filters toolbar button text
  toolbarFilters: "Filtreler",
  toolbarFiltersLabel: "Filtreleri göster",
  toolbarFiltersTooltipHide: "Filtreleri gizle",
  toolbarFiltersTooltipShow: "Filtreleri göster",
  toolbarFiltersTooltipActive: (count) => `${count} aktif filtre`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Ara…",
  toolbarQuickFilterLabel: "Ara",
  toolbarQuickFilterDeleteIconLabel: "Temizle",
  // Export selector toolbar button text
  toolbarExport: "Dışa aktar",
  toolbarExportLabel: "Dışa aktar",
  toolbarExportCSV: "CSV olarak aktar",
  toolbarExportPrint: "Yazdır",
  toolbarExportExcel: "Excel olarak aktar",
  // Columns management text
  columnsManagementSearchTitle: "Arama",
  columnsManagementNoColumns: "Kolon yok",
  columnsManagementShowHideAllText: "Hepsini Göster/Gizle",
  columnsManagementReset: "Sıfırla",
  columnsManagementDeleteIconLabel: "Temizle",
  // Filter panel text
  filterPanelAddFilter: "Filtre Ekle",
  filterPanelRemoveAll: "Hepsini kaldır",
  filterPanelDeleteIconLabel: "Kaldır",
  filterPanelLogicOperator: "Mantıksal operatörler",
  filterPanelOperator: "Operatör",
  filterPanelOperatorAnd: "Ve",
  filterPanelOperatorOr: "Veya",
  filterPanelColumns: "Sütunlar",
  filterPanelInputLabel: "Değer",
  filterPanelInputPlaceholder: "Filtre değeri",
  // Filter operators text
  filterOperatorContains: "içerir",
  filterOperatorDoesNotContain: "içermiyor",
  filterOperatorEquals: "eşittir",
  filterOperatorDoesNotEqual: "eşit değil",
  filterOperatorStartsWith: "ile başlar",
  filterOperatorEndsWith: "ile biter",
  filterOperatorIs: "eşittir",
  filterOperatorNot: "eşit değildir",
  filterOperatorAfter: "büyük",
  filterOperatorOnOrAfter: "büyük eşit",
  filterOperatorBefore: "küçük",
  filterOperatorOnOrBefore: "küçük eşit",
  filterOperatorIsEmpty: "boş",
  filterOperatorIsNotEmpty: "dolu",
  filterOperatorIsAnyOf: "herhangi biri",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Şunu içerir",
  headerFilterOperatorDoesNotContain: "İçermez",
  headerFilterOperatorEquals: "Şuna eşittir",
  headerFilterOperatorDoesNotEqual: "Eşit değildir",
  headerFilterOperatorStartsWith: "Şununla başlar",
  headerFilterOperatorEndsWith: "Şununla biter",
  headerFilterOperatorIs: "Eşittir",
  headerFilterOperatorNot: "Eşit değil",
  headerFilterOperatorAfter: "Sonra",
  headerFilterOperatorOnOrAfter: "Sonra veya eşit",
  headerFilterOperatorBefore: "Önce",
  headerFilterOperatorOnOrBefore: "Önce veya eşit",
  headerFilterOperatorIsEmpty: "Boş",
  headerFilterOperatorIsNotEmpty: "Boş değil",
  headerFilterOperatorIsAnyOf: "Herhangi biri",
  "headerFilterOperator=": "Eşittir",
  "headerFilterOperator!=": "Eşit değil",
  "headerFilterOperator>": "Büyüktür",
  "headerFilterOperator>=": "Büyük veya eşit",
  "headerFilterOperator<": "Küçüktür",
  "headerFilterOperator<=": "Küçük veya eşit",
  // Filter values text
  filterValueAny: "herhangi",
  filterValueTrue: "doğru",
  filterValueFalse: "yanlış",
  // Column menu text
  columnMenuLabel: "Menü",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Sütunları göster",
  columnMenuManageColumns: "Sütunları yönet",
  columnMenuFilter: "Filtre Ekle",
  columnMenuHideColumn: "Gizle",
  columnMenuUnsort: "Varsayılan Sıralama",
  columnMenuSortAsc: "Sırala - Artan",
  columnMenuSortDesc: "Sırala - Azalan",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `${count} filtre aktif`,
  columnHeaderFiltersLabel: "Filtreleri göster",
  columnHeaderSortIconLabel: "Sırala",
  // Rows selected footer text
  footerRowSelected: (count) => `${count.toLocaleString()} satır seçildi`,
  // Total row amount footer text
  footerTotalRows: "Toplam Satır:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Seçim",
  checkboxSelectionSelectAllRows: "Tüm satırları seç",
  checkboxSelectionUnselectAllRows: "Tüm satırların seçimini kaldır",
  checkboxSelectionSelectRow: "Satırı seç",
  checkboxSelectionUnselectRow: "Satır seçimini bırak",
  // Boolean cell text
  booleanCellTrueLabel: "Evet",
  booleanCellFalseLabel: "Hayır",
  // Actions cell more text
  actionsCellMore: "daha fazla",
  // Column pinning text
  pinToLeft: "Sola sabitle",
  pinToRight: "Sağa sabitle",
  unpin: "Sabitlemeyi kaldır",
  // Tree Data
  treeDataGroupingHeaderName: "Grup",
  treeDataExpand: "göster",
  treeDataCollapse: "gizle",
  // Grouping columns
  groupingColumnHeaderName: "Grup",
  groupColumn: (name) => `${name} için grupla`,
  unGroupColumn: (name) => `${name} için gruplamayı kaldır`,
  // Master/detail
  detailPanelToggle: "Detay görünümüne geçiş",
  expandDetailPanel: "Genişlet",
  collapseDetailPanel: "Gizle",
  // Row reordering text
  rowReorderingHeaderName: "Satırı yeniden sırala",
  // Aggregation
  aggregationMenuItemHeader: "Toplama",
  aggregationFunctionLabelSum: "top",
  aggregationFunctionLabelAvg: "ort",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "maks",
  aggregationFunctionLabelSize: "boyut"
};
var trTR2 = getGridLocalization(trTRGrid, trTR);

// node_modules/@mui/x-data-grid/locales/ukUA.js
function getPluralForm3(count, options) {
  const penultimateDigit = Math.floor(count / 10) % 10;
  const lastDigit = count % 10;
  let pluralForm = options.many;
  if (penultimateDigit !== 1 && lastDigit > 1 && lastDigit < 5) {
    pluralForm = options.few;
  } else if (penultimateDigit !== 1 && lastDigit === 1) {
    pluralForm = options.one;
  }
  return `${count} ${pluralForm}`;
}
var ukUAGrid = {
  // Root
  noRowsLabel: "Немає рядків",
  noResultsOverlayLabel: "Дані не знайдено.",
  // Density selector toolbar button text
  toolbarDensity: "Висота рядка",
  toolbarDensityLabel: "Висота рядка",
  toolbarDensityCompact: "Компактний",
  toolbarDensityStandard: "Стандартний",
  toolbarDensityComfortable: "Комфортний",
  // Columns selector toolbar button text
  toolbarColumns: "Стовпці",
  toolbarColumnsLabel: "Виділіть стовпці",
  // Filters toolbar button text
  toolbarFilters: "Фільтри",
  toolbarFiltersLabel: "Показати фільтри",
  toolbarFiltersTooltipHide: "Приховати фільтри",
  toolbarFiltersTooltipShow: "Показати фільтри",
  toolbarFiltersTooltipActive: (count) => getPluralForm3(count, {
    one: "активний фільтр",
    few: "активні фільтри",
    many: "активних фільтрів"
  }),
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Пошук…",
  toolbarQuickFilterLabel: "Пошук",
  toolbarQuickFilterDeleteIconLabel: "Очистити",
  // Export selector toolbar button text
  toolbarExport: "Експорт",
  toolbarExportLabel: "Експорт",
  toolbarExportCSV: "Завантажити у форматі CSV",
  toolbarExportPrint: "Друк",
  toolbarExportExcel: "Завантажити у форматі Excel",
  // Columns management text
  columnsManagementSearchTitle: "Пошук",
  columnsManagementNoColumns: "Немає стовпців",
  columnsManagementShowHideAllText: "Показати/Приховати всі",
  columnsManagementReset: "Скинути",
  columnsManagementDeleteIconLabel: "Очистити",
  // Filter panel text
  filterPanelAddFilter: "Додати фільтр",
  filterPanelRemoveAll: "Видалити всі",
  filterPanelDeleteIconLabel: "Видалити",
  filterPanelLogicOperator: "Логічна функція",
  filterPanelOperator: "Оператори",
  filterPanelOperatorAnd: "І",
  filterPanelOperatorOr: "Або",
  filterPanelColumns: "Стовпці",
  filterPanelInputLabel: "Значення",
  filterPanelInputPlaceholder: "Значення фільтра",
  // Filter operators text
  filterOperatorContains: "містить",
  filterOperatorDoesNotContain: "не містить",
  filterOperatorEquals: "дорівнює",
  filterOperatorDoesNotEqual: "не дорівнює",
  filterOperatorStartsWith: "починається з",
  filterOperatorEndsWith: "закінчується на",
  filterOperatorIs: "дорівнює",
  filterOperatorNot: "не дорівнює",
  filterOperatorAfter: "більше ніж",
  filterOperatorOnOrAfter: "більше або дорівнює",
  filterOperatorBefore: "менше ніж",
  filterOperatorOnOrBefore: "менше або дорівнює",
  filterOperatorIsEmpty: "порожній",
  filterOperatorIsNotEmpty: "не порожній",
  filterOperatorIsAnyOf: "будь-що із",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Містить",
  headerFilterOperatorDoesNotContain: "Не містить",
  headerFilterOperatorEquals: "Дорівнює",
  headerFilterOperatorDoesNotEqual: "Не дорівнює",
  headerFilterOperatorStartsWith: "Починається з",
  headerFilterOperatorEndsWith: "Закінчується на",
  headerFilterOperatorIs: "Дорівнює",
  headerFilterOperatorNot: "Не дорівнює",
  headerFilterOperatorAfter: "Після",
  headerFilterOperatorOnOrAfter: "Після (включаючи)",
  headerFilterOperatorBefore: "Раніше",
  headerFilterOperatorOnOrBefore: "Раніше (включаючи)",
  headerFilterOperatorIsEmpty: "Порожнє",
  headerFilterOperatorIsNotEmpty: "Не порожнє",
  headerFilterOperatorIsAnyOf: "Будь-що із",
  "headerFilterOperator=": "Дорівнює",
  "headerFilterOperator!=": "Не дорівнює",
  "headerFilterOperator>": "Більше ніж",
  "headerFilterOperator>=": "Більше або дорівнює",
  "headerFilterOperator<": "Менше ніж",
  "headerFilterOperator<=": "Менше або дорівнює",
  // Filter values text
  filterValueAny: "будь-який",
  filterValueTrue: "так",
  filterValueFalse: "ні",
  // Column menu text
  columnMenuLabel: "Меню",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Показати стовпці",
  columnMenuManageColumns: "Керування стовпцями",
  columnMenuFilter: "Фільтр",
  columnMenuHideColumn: "Приховати",
  columnMenuUnsort: "Скасувати сортування",
  columnMenuSortAsc: "Сортувати за зростанням",
  columnMenuSortDesc: "Сортувати за спаданням",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => getPluralForm3(count, {
    one: "активний фільтр",
    few: "активні фільтри",
    many: "активних фільтрів"
  }),
  columnHeaderFiltersLabel: "Показати фільтри",
  columnHeaderSortIconLabel: "Сортувати",
  // Rows selected footer text
  footerRowSelected: (count) => getPluralForm3(count, {
    one: "вибраний рядок",
    few: "вибрані рядки",
    many: "вибраних рядків"
  }),
  // Total row amount footer text
  footerTotalRows: "Усього рядків:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} з ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Вибір прапорця",
  checkboxSelectionSelectAllRows: "Вибрати всі рядки",
  checkboxSelectionUnselectAllRows: "Скасувати вибір всіх рядків",
  checkboxSelectionSelectRow: "Вибрати рядок",
  checkboxSelectionUnselectRow: "Скасувати вибір рядка",
  // Boolean cell text
  booleanCellTrueLabel: "так",
  booleanCellFalseLabel: "ні",
  // Actions cell more text
  actionsCellMore: "більше",
  // Column pinning text
  pinToLeft: "Закріпити ліворуч",
  pinToRight: "Закріпити праворуч",
  unpin: "Відкріпити",
  // Tree Data
  treeDataGroupingHeaderName: "Група",
  treeDataExpand: "показати дочірні елементи",
  treeDataCollapse: "приховати дочірні елементи",
  // Grouping columns
  groupingColumnHeaderName: "Група",
  groupColumn: (name) => `Групувати за ${name}`,
  unGroupColumn: (name) => `Відмінити групування за ${name}`,
  // Master/detail
  detailPanelToggle: "Перемикач панелі деталей",
  expandDetailPanel: "Показати",
  collapseDetailPanel: "Приховати",
  // Row reordering text
  rowReorderingHeaderName: "Порядок рядків",
  // Aggregation
  aggregationMenuItemHeader: "Агрегація",
  aggregationFunctionLabelSum: "сума",
  aggregationFunctionLabelAvg: "сер",
  aggregationFunctionLabelMin: "мін",
  aggregationFunctionLabelMax: "макс",
  aggregationFunctionLabelSize: "кількість"
};
var ukUA2 = getGridLocalization(ukUAGrid, ukUA);

// node_modules/@mui/x-data-grid/locales/urPK.js
var urPKGrid = {
  // Root
  noRowsLabel: "کوئی قطاریں نہیں",
  noResultsOverlayLabel: "کوئی نتائج نہیں",
  // Density selector toolbar button text
  toolbarDensity: "کثافت",
  toolbarDensityLabel: "کثافت",
  toolbarDensityCompact: "تنگ",
  toolbarDensityStandard: "درمیانہ",
  toolbarDensityComfortable: "مناسب",
  // Columns selector toolbar button text
  toolbarColumns: "کالمز",
  toolbarColumnsLabel: "کالمز کو منتخب کریں",
  // Filters toolbar button text
  toolbarFilters: "فلٹرز",
  toolbarFiltersLabel: "فلٹرز دکھائیں",
  toolbarFiltersTooltipHide: "فلٹرز چھپائیں",
  toolbarFiltersTooltipShow: "فلٹرز دکھائیں",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} فعال فلٹرز` : `${count} فلٹرز فعال`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "تلاش کریں۔۔۔",
  toolbarQuickFilterLabel: "تلاش کریں",
  toolbarQuickFilterDeleteIconLabel: "کلئیر کریں",
  // Export selector toolbar button text
  toolbarExport: "ایکسپورٹ",
  toolbarExportLabel: "ایکسپورٹ",
  toolbarExportCSV: "CSV کے طور پر ڈاوٴنلوڈ کریں",
  toolbarExportPrint: "پرنٹ کریں",
  toolbarExportExcel: "ایکسل کے طور پر ڈاوٴنلوڈ کریں",
  // Columns management text
  columnsManagementSearchTitle: "تلاش",
  columnsManagementNoColumns: "کوئی کالم نہیں",
  columnsManagementShowHideAllText: "تمام دکھائیں/چھپائیں",
  columnsManagementReset: "ریسیٹ",
  columnsManagementDeleteIconLabel: "کلئیر",
  // Filter panel text
  filterPanelAddFilter: "نیا فلٹر",
  filterPanelRemoveAll: "سارے ختم کریں",
  filterPanelDeleteIconLabel: "ختم کریں",
  filterPanelLogicOperator: "لاجک آپریٹر",
  filterPanelOperator: "آپریٹر",
  filterPanelOperatorAnd: "اور",
  filterPanelOperatorOr: "یا",
  filterPanelColumns: "کالمز",
  filterPanelInputLabel: "ویلیو",
  filterPanelInputPlaceholder: "ویلیو کو فلٹر کریں",
  // Filter operators text
  filterOperatorContains: "شامل ہے",
  filterOperatorDoesNotContain: "موجود نہیں ہے",
  filterOperatorEquals: "برابر ہے",
  filterOperatorDoesNotEqual: "برابر نہیں ہے",
  filterOperatorStartsWith: "شروع ہوتا ہے",
  filterOperatorEndsWith: "ختم ہوتا ہے",
  filterOperatorIs: "ہے",
  filterOperatorNot: "نہیں",
  filterOperatorAfter: "بعد میں ہے",
  filterOperatorOnOrAfter: "پر یا بعد میں ہے",
  filterOperatorBefore: "پہلے ہے",
  filterOperatorOnOrBefore: "پر یا پہلے ہے",
  filterOperatorIsEmpty: "خالی ہے",
  filterOperatorIsNotEmpty: "خالی نہیں ہے",
  filterOperatorIsAnyOf: "ان میں سے کوئی ہے",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "شامل ہے",
  headerFilterOperatorDoesNotContain: "موجود نہیں ہے",
  headerFilterOperatorEquals: "برابر ہے",
  headerFilterOperatorDoesNotEqual: "برابر نہیں ہے",
  headerFilterOperatorStartsWith: "شروع ہوتا ہے",
  headerFilterOperatorEndsWith: "ختم ہوتا ہے",
  headerFilterOperatorIs: "ہے",
  headerFilterOperatorNot: "نہیں ہے",
  headerFilterOperatorAfter: "بعد میں ہے",
  headerFilterOperatorOnOrAfter: "پر یا بعد میں ہے",
  headerFilterOperatorBefore: "پہلے ہے",
  headerFilterOperatorOnOrBefore: "پر یا پہلے ہے",
  headerFilterOperatorIsEmpty: "خالی ہے",
  headerFilterOperatorIsNotEmpty: "خالی نہیں ہے",
  headerFilterOperatorIsAnyOf: "ان میں سے کوئی ہے",
  "headerFilterOperator=": "برابر ہے",
  "headerFilterOperator!=": "برابر نہیں ہے",
  "headerFilterOperator>": "ذیادہ ہے",
  "headerFilterOperator>=": "ذیادہ یا برابر ہے",
  "headerFilterOperator<": "کم ہے",
  "headerFilterOperator<=": "کم یا برابر ہے",
  // Filter values text
  filterValueAny: "کوئی بھی",
  filterValueTrue: "صحیح",
  filterValueFalse: "غلط",
  // Column menu text
  columnMenuLabel: "مینیو",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "کالم دکھائیں",
  columnMenuManageColumns: "کالم مینج کریں",
  columnMenuFilter: "فلٹر",
  columnMenuHideColumn: "چھپائیں",
  columnMenuUnsort: "sort ختم کریں",
  columnMenuSortAsc: "ترتیب صعودی",
  columnMenuSortDesc: "ترتیب نزولی",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} فعال فلٹرز` : `${count} فلٹرز فعال`,
  columnHeaderFiltersLabel: "فلٹرز دکھائیں",
  columnHeaderSortIconLabel: "Sort",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} منتخب قطاریں` : `${count.toLocaleString()} منتخب قطار`,
  // Total row amount footer text
  footerTotalRows: "کل قطاریں:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${totalCount.toLocaleString()} میں سے ${visibleCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "چیک باکس منتخب کریں",
  checkboxSelectionSelectAllRows: "تمام قطاریں منتخب کریں",
  checkboxSelectionUnselectAllRows: "تمام قطاریں نامنتخب کریں ",
  checkboxSelectionSelectRow: "قطار منتخب کریں",
  checkboxSelectionUnselectRow: "قطار نامنتخب کریں",
  // Boolean cell text
  booleanCellTrueLabel: "ہاں",
  booleanCellFalseLabel: "نہیں",
  // Actions cell more text
  actionsCellMore: "ذیادہ",
  // Column pinning text
  pinToLeft: "بائیں جانب pin کریں",
  pinToRight: "دائیں جانب pin کریں",
  unpin: "pin ختم کریں",
  // Tree Data
  treeDataGroupingHeaderName: "گروپ",
  treeDataExpand: "شاخیں دیکھیں",
  treeDataCollapse: "شاخیں چھپائیں",
  // Grouping columns
  groupingColumnHeaderName: "گروپ",
  groupColumn: (name) => `${name} سے گروپ کریں`,
  unGroupColumn: (name) => `${name} سے گروپ ختم کریں`,
  // Master/detail
  detailPanelToggle: "ڈیٹیل پینل کھولیں / بند کریں",
  expandDetailPanel: "پھیلائیں",
  collapseDetailPanel: "تنگ کریں",
  // Row reordering text
  rowReorderingHeaderName: "قطاروں کی ترتیب تبدیل کریں",
  // Aggregation
  aggregationMenuItemHeader: "ایگریگیشن",
  aggregationFunctionLabelSum: "کل",
  aggregationFunctionLabelAvg: "اوسط",
  aggregationFunctionLabelMin: "کم از کم",
  aggregationFunctionLabelMax: "زیادہ سے زیادہ",
  aggregationFunctionLabelSize: "سائز"
};
var urPK = getGridLocalization(urPKGrid, urPKCore);

// node_modules/@mui/x-data-grid/locales/viVN.js
var viVNGrid = {
  // Root
  noRowsLabel: "Không có dữ liệu",
  noResultsOverlayLabel: "Không tìm thấy kết quả.",
  // Density selector toolbar button text
  toolbarDensity: "Độ giãn",
  toolbarDensityLabel: "Độ giãn",
  toolbarDensityCompact: "Trung bình",
  toolbarDensityStandard: "Tiêu chuẩn",
  toolbarDensityComfortable: "Rộng",
  // Columns selector toolbar button text
  toolbarColumns: "Cột",
  toolbarColumnsLabel: "Chọn cột",
  // Filters toolbar button text
  toolbarFilters: "Bộ lọc",
  toolbarFiltersLabel: "Hiển thị bộ lọc",
  toolbarFiltersTooltipHide: "Ẩn",
  toolbarFiltersTooltipShow: "Hiện",
  toolbarFiltersTooltipActive: (count) => count > 1 ? `${count} bộ lọc hoạt động` : `${count} bộ lọc hoạt động`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Tìm kiếm…",
  toolbarQuickFilterLabel: "Tìm kiếm",
  toolbarQuickFilterDeleteIconLabel: "Xóa tìm kiếm",
  // Export selector toolbar button text
  toolbarExport: "Xuất",
  toolbarExportLabel: "Xuất",
  toolbarExportCSV: "Xuất CSV",
  toolbarExportPrint: "In",
  toolbarExportExcel: "Xuất Excel",
  // Columns management text
  columnsManagementSearchTitle: "Tìm kiếm",
  columnsManagementNoColumns: "Không có cột",
  columnsManagementShowHideAllText: "Hiện/Ẩn Tất cả",
  columnsManagementReset: "Đặt lại",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Thêm bộ lọc",
  filterPanelRemoveAll: "Xóa tất cả",
  filterPanelDeleteIconLabel: "Xóa",
  filterPanelLogicOperator: "Toán tử logic",
  filterPanelOperator: "Toán tử",
  filterPanelOperatorAnd: "Và",
  filterPanelOperatorOr: "Hoặc",
  filterPanelColumns: "Cột",
  filterPanelInputLabel: "Giá trị",
  filterPanelInputPlaceholder: "Lọc giá trị",
  // Filter operators text
  filterOperatorContains: "chứa",
  filterOperatorDoesNotContain: "không chứa",
  filterOperatorEquals: "bằng",
  filterOperatorDoesNotEqual: "không bằng",
  filterOperatorStartsWith: "bắt đầu với",
  filterOperatorEndsWith: "kết thúc với",
  filterOperatorIs: "là",
  filterOperatorNot: "không phải là",
  filterOperatorAfter: "sau",
  filterOperatorOnOrAfter: "bằng hoặc sau",
  filterOperatorBefore: "trước",
  filterOperatorOnOrBefore: "bằng hoặc trước",
  filterOperatorIsEmpty: "rỗng",
  filterOperatorIsNotEmpty: "khác rỗng",
  filterOperatorIsAnyOf: "là một trong",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Chứa",
  headerFilterOperatorDoesNotContain: "Không chứa",
  headerFilterOperatorEquals: "Bằng",
  headerFilterOperatorDoesNotEqual: "Không bằng",
  headerFilterOperatorStartsWith: "Bắt đầu với",
  headerFilterOperatorEndsWith: "Kết thúc với",
  headerFilterOperatorIs: "Là",
  headerFilterOperatorNot: "Không phải là",
  headerFilterOperatorAfter: "Sau",
  headerFilterOperatorOnOrAfter: "Bằng hoặc sau",
  headerFilterOperatorBefore: "Trước",
  headerFilterOperatorOnOrBefore: "Bằng hoặc trước",
  headerFilterOperatorIsEmpty: "Rỗng",
  headerFilterOperatorIsNotEmpty: "Khác rỗng",
  headerFilterOperatorIsAnyOf: "Là một trong",
  "headerFilterOperator=": "Bằng",
  "headerFilterOperator!=": "Khác",
  "headerFilterOperator>": "Lớn hơn",
  "headerFilterOperator>=": "Lớn hơn hoặc bằng",
  "headerFilterOperator<": "Nhỏ hơn",
  "headerFilterOperator<=": "Nhỏ hơn hoặc bằng",
  // Filter values text
  filterValueAny: "bất kỳ giá trị nào",
  filterValueTrue: "Có",
  filterValueFalse: "Không",
  // Column menu text
  columnMenuLabel: "Danh mục",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Danh sách cột",
  columnMenuManageColumns: "Quản lý cột",
  columnMenuFilter: "Bộ lọc",
  columnMenuHideColumn: "Ẩn cột",
  columnMenuUnsort: "Bỏ sắp xếp",
  columnMenuSortAsc: "Sắp xếp tăng dần",
  columnMenuSortDesc: "Sắp xếp giảm dần",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count > 1 ? `${count} bộ lọc hoạt động` : `${count} bộ lọc hoạt động`,
  columnHeaderFiltersLabel: "Bộ lọc",
  columnHeaderSortIconLabel: "Sắp xếp",
  // Rows selected footer text
  footerRowSelected: (count) => count > 1 ? `${count.toLocaleString()} hàng đã chọn` : `${count.toLocaleString()} hàng đã chọn`,
  // Total row amount footer text
  footerTotalRows: "Tổng:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Tích vào ô trống",
  checkboxSelectionSelectAllRows: "Chọn tất cả hàng",
  checkboxSelectionUnselectAllRows: "Bỏ chọn tất cả hàng",
  checkboxSelectionSelectRow: "Chọn hàng",
  checkboxSelectionUnselectRow: "Bỏ chọn hàng",
  // Boolean cell text
  booleanCellTrueLabel: "Có",
  booleanCellFalseLabel: "Không",
  // Actions cell more text
  actionsCellMore: "Thêm",
  // Column pinning text
  pinToLeft: "Ghim cột bên trái",
  pinToRight: "Ghim cột bên phải",
  unpin: "Bỏ ghim",
  // Tree Data
  treeDataGroupingHeaderName: "Nhóm",
  treeDataExpand: "mở rộng",
  treeDataCollapse: "ẩn đi",
  // Grouping columns
  groupingColumnHeaderName: "Nhóm",
  groupColumn: (name) => `Nhóm theo ${name}`,
  unGroupColumn: (name) => `Hủy nhóm theo ${name}`,
  // Master/detail
  detailPanelToggle: "Ẩn/hiện chi tiết",
  expandDetailPanel: "Mở rộng",
  collapseDetailPanel: "Thu nhỏ",
  // Row reordering text
  rowReorderingHeaderName: "Sắp xếp hàng",
  // Aggregation
  aggregationMenuItemHeader: "Tổng hợp",
  aggregationFunctionLabelSum: "Tổng",
  aggregationFunctionLabelAvg: "Trung bình",
  aggregationFunctionLabelMin: "Tối thiểu",
  aggregationFunctionLabelMax: "Tối đa",
  aggregationFunctionLabelSize: "Kích cỡ"
};
var viVN2 = getGridLocalization(viVNGrid, viVN);

// node_modules/@mui/x-data-grid/locales/zhCN.js
var zhCNGrid = {
  // Root
  noRowsLabel: "没有数据。",
  noResultsOverlayLabel: "未找到数据。",
  // Density selector toolbar button text
  toolbarDensity: "表格密度",
  toolbarDensityLabel: "表格密度",
  toolbarDensityCompact: "紧密",
  toolbarDensityStandard: "标准",
  toolbarDensityComfortable: "稀疏",
  // Columns selector toolbar button text
  toolbarColumns: "列",
  toolbarColumnsLabel: "选择列",
  // Filters toolbar button text
  toolbarFilters: "筛选器",
  toolbarFiltersLabel: "显示筛选器",
  toolbarFiltersTooltipHide: "隐藏筛选器",
  toolbarFiltersTooltipShow: "显示筛选器",
  toolbarFiltersTooltipActive: (count) => `${count} 个筛选器`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "搜索…",
  toolbarQuickFilterLabel: "搜索",
  toolbarQuickFilterDeleteIconLabel: "清除",
  // Export selector toolbar button text
  toolbarExport: "导出",
  toolbarExportLabel: "导出",
  toolbarExportCSV: "导出至CSV",
  toolbarExportPrint: "打印",
  toolbarExportExcel: "导出至Excel",
  // Columns management text
  columnsManagementSearchTitle: "搜索",
  columnsManagementNoColumns: "没有列",
  columnsManagementShowHideAllText: "显示/隐藏所有",
  columnsManagementReset: "重置",
  columnsManagementDeleteIconLabel: "清除",
  // Filter panel text
  filterPanelAddFilter: "添加筛选器",
  filterPanelRemoveAll: "清除全部",
  filterPanelDeleteIconLabel: "删除",
  filterPanelLogicOperator: "逻辑操作器",
  filterPanelOperator: "操作器",
  filterPanelOperatorAnd: "与",
  filterPanelOperatorOr: "或",
  filterPanelColumns: "列",
  filterPanelInputLabel: "值",
  filterPanelInputPlaceholder: "筛选值",
  // Filter operators text
  filterOperatorContains: "包含",
  filterOperatorDoesNotContain: "不包含",
  filterOperatorEquals: "等于",
  filterOperatorDoesNotEqual: "不等于",
  filterOperatorStartsWith: "开始于",
  filterOperatorEndsWith: "结束于",
  filterOperatorIs: "是",
  filterOperatorNot: "不是",
  filterOperatorAfter: "在后面",
  filterOperatorOnOrAfter: "正在后面",
  filterOperatorBefore: "在前面",
  filterOperatorOnOrBefore: "正在前面",
  filterOperatorIsEmpty: "为空",
  filterOperatorIsNotEmpty: "不为空",
  filterOperatorIsAnyOf: "属于",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "包含",
  headerFilterOperatorDoesNotContain: "不包含",
  headerFilterOperatorEquals: "等于",
  headerFilterOperatorDoesNotEqual: "不等于",
  headerFilterOperatorStartsWith: "开始于",
  headerFilterOperatorEndsWith: "结束于",
  headerFilterOperatorIs: "是",
  headerFilterOperatorNot: "不是",
  headerFilterOperatorAfter: "在后面",
  headerFilterOperatorOnOrAfter: "在当前或后面",
  headerFilterOperatorBefore: "在前面",
  headerFilterOperatorOnOrBefore: "在当前或前面",
  headerFilterOperatorIsEmpty: "为空",
  headerFilterOperatorIsNotEmpty: "不为空",
  headerFilterOperatorIsAnyOf: "属于",
  "headerFilterOperator=": "等于",
  "headerFilterOperator!=": "不等于",
  "headerFilterOperator>": "大于",
  "headerFilterOperator>=": "大于或等于",
  "headerFilterOperator<": "小于",
  "headerFilterOperator<=": "小于或等于",
  // Filter values text
  filterValueAny: "任何",
  filterValueTrue: "真",
  filterValueFalse: "假",
  // Column menu text
  columnMenuLabel: "菜单",
  columnMenuAriaLabel: (columnName) => `${columnName} 列菜单`,
  columnMenuShowColumns: "显示",
  columnMenuManageColumns: "管理列",
  columnMenuFilter: "筛选器",
  columnMenuHideColumn: "隐藏",
  columnMenuUnsort: "恢复默认",
  columnMenuSortAsc: "升序",
  columnMenuSortDesc: "降序",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} 个筛选器` : `${count} 个筛选器`,
  columnHeaderFiltersLabel: "显示筛选器",
  columnHeaderSortIconLabel: "排序",
  // Rows selected footer text
  footerRowSelected: (count) => `共选中了${count.toLocaleString()}行`,
  // Total row amount footer text
  footerTotalRows: "所有行:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "多选框",
  checkboxSelectionSelectAllRows: "全选行",
  checkboxSelectionUnselectAllRows: "反选所有行",
  checkboxSelectionSelectRow: "选择行",
  checkboxSelectionUnselectRow: "反选行",
  // Boolean cell text
  booleanCellTrueLabel: "真",
  booleanCellFalseLabel: "假",
  // Actions cell more text
  actionsCellMore: "更多",
  // Column pinning text
  pinToLeft: "固定到左侧",
  pinToRight: "固定到右侧",
  unpin: "取消固定",
  // Tree Data
  treeDataGroupingHeaderName: "组",
  treeDataExpand: "查看子项目",
  treeDataCollapse: "隐藏子项目",
  // Grouping columns
  groupingColumnHeaderName: "组",
  groupColumn: (name) => `用${name}分组`,
  unGroupColumn: (name) => `不再用${name}分组`,
  // Master/detail
  detailPanelToggle: "详细信息",
  expandDetailPanel: "显示",
  collapseDetailPanel: "折叠",
  // Row reordering text
  rowReorderingHeaderName: "重新排列行",
  // Aggregation
  aggregationMenuItemHeader: "集合",
  aggregationFunctionLabelSum: "总数",
  aggregationFunctionLabelAvg: "平均",
  aggregationFunctionLabelMin: "最小",
  aggregationFunctionLabelMax: "最大",
  aggregationFunctionLabelSize: "大小"
};
var zhCN2 = getGridLocalization(zhCNGrid, zhCN);

// node_modules/@mui/x-data-grid/locales/zhTW.js
var zhTWGrid = {
  // Root
  noRowsLabel: "沒有資料",
  noResultsOverlayLabel: "沒有結果",
  // Density selector toolbar button text
  toolbarDensity: "表格密度",
  toolbarDensityLabel: "表格密度",
  toolbarDensityCompact: "緊湊",
  toolbarDensityStandard: "標準",
  toolbarDensityComfortable: "舒適",
  // Columns selector toolbar button text
  toolbarColumns: "欄位",
  toolbarColumnsLabel: "選擇欄位",
  // Filters toolbar button text
  toolbarFilters: "篩選器",
  toolbarFiltersLabel: "顯示篩選器",
  toolbarFiltersTooltipHide: "隱藏篩選器",
  toolbarFiltersTooltipShow: "顯示篩選器",
  toolbarFiltersTooltipActive: (count) => `${count} 個篩選器`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "搜尋…",
  toolbarQuickFilterLabel: "搜尋",
  toolbarQuickFilterDeleteIconLabel: "清除",
  // Export selector toolbar button text
  toolbarExport: "匯出",
  toolbarExportLabel: "匯出",
  toolbarExportCSV: "匯出 CSV",
  toolbarExportPrint: "列印",
  toolbarExportExcel: "匯出 Excel",
  // Columns management text
  columnsManagementSearchTitle: "搜尋",
  columnsManagementNoColumns: "沒有欄位",
  columnsManagementShowHideAllText: "顯示/隱藏所有",
  columnsManagementReset: "重置",
  columnsManagementDeleteIconLabel: "清除",
  // Filter panel text
  filterPanelAddFilter: "增加篩選器",
  filterPanelRemoveAll: "清除所有",
  filterPanelDeleteIconLabel: "刪除",
  filterPanelLogicOperator: "邏輯運算子",
  filterPanelOperator: "運算子",
  filterPanelOperatorAnd: "且",
  filterPanelOperatorOr: "或",
  filterPanelColumns: "欄位",
  filterPanelInputLabel: "值",
  filterPanelInputPlaceholder: "篩選值",
  // Filter operators text
  filterOperatorContains: "包含",
  filterOperatorDoesNotContain: "不包含",
  filterOperatorEquals: "等於",
  filterOperatorDoesNotEqual: "不等於",
  filterOperatorStartsWith: "以...開頭",
  filterOperatorEndsWith: "以...結束",
  filterOperatorIs: "為",
  filterOperatorNot: "不為",
  filterOperatorAfter: "...之後",
  filterOperatorOnOrAfter: "...(含)之後",
  filterOperatorBefore: "...之前",
  filterOperatorOnOrBefore: "...(含)之前",
  filterOperatorIsEmpty: "為空",
  filterOperatorIsNotEmpty: "不為空",
  filterOperatorIsAnyOf: "是其中之一",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "包含",
  headerFilterOperatorDoesNotContain: "不包含",
  headerFilterOperatorEquals: "等於",
  headerFilterOperatorDoesNotEqual: "不等於",
  headerFilterOperatorStartsWith: "以...開頭",
  headerFilterOperatorEndsWith: "以...結束",
  headerFilterOperatorIs: "為",
  headerFilterOperatorNot: "不為",
  headerFilterOperatorAfter: "...之後",
  headerFilterOperatorOnOrAfter: "...(含)之後",
  headerFilterOperatorBefore: "...之前",
  headerFilterOperatorOnOrBefore: "...(含)之前",
  headerFilterOperatorIsEmpty: "為空",
  headerFilterOperatorIsNotEmpty: "不為空",
  headerFilterOperatorIsAnyOf: "是其中之一",
  "headerFilterOperator=": "等於",
  "headerFilterOperator!=": "不等於",
  "headerFilterOperator>": "大於",
  "headerFilterOperator>=": "大於或等於",
  "headerFilterOperator<": "小於",
  "headerFilterOperator<=": "小於或等於",
  // Filter values text
  filterValueAny: "任何值",
  filterValueTrue: "真",
  filterValueFalse: "假",
  // Column menu text
  columnMenuLabel: "選單",
  columnMenuAriaLabel: (columnName) => `${columnName} 欄位選單`,
  columnMenuShowColumns: "顯示欄位",
  columnMenuManageColumns: "管理欄位",
  columnMenuFilter: "篩選器",
  columnMenuHideColumn: "隱藏",
  columnMenuUnsort: "預設排序",
  columnMenuSortAsc: "升序",
  columnMenuSortDesc: "降序",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => `${count} 個篩選器`,
  columnHeaderFiltersLabel: "顯示篩選器",
  columnHeaderSortIconLabel: "排序",
  // Rows selected footer text
  footerRowSelected: (count) => `已選取 ${count.toLocaleString()} 個`,
  // Total row amount footer text
  footerTotalRows: "總數:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "核取方塊",
  checkboxSelectionSelectAllRows: "全選",
  checkboxSelectionUnselectAllRows: "取消全選",
  checkboxSelectionSelectRow: "選取",
  checkboxSelectionUnselectRow: "取消選取",
  // Boolean cell text
  booleanCellTrueLabel: "真",
  booleanCellFalseLabel: "假",
  // Actions cell more text
  actionsCellMore: "查看更多",
  // Column pinning text
  pinToLeft: "釘選在左側",
  pinToRight: "釘選在右側",
  unpin: "取消釘選",
  // Tree Data
  treeDataGroupingHeaderName: "群組",
  treeDataExpand: "查看子項目",
  treeDataCollapse: "隱藏子項目",
  // Grouping columns
  groupingColumnHeaderName: "群組",
  groupColumn: (name) => `以 ${name} 分組`,
  unGroupColumn: (name) => `取消以 ${name} 分組`,
  // Master/detail
  detailPanelToggle: "切換顯示詳細資訊",
  expandDetailPanel: "展開",
  collapseDetailPanel: "摺疊",
  // Row reordering text
  rowReorderingHeaderName: "排序",
  // Aggregation
  aggregationMenuItemHeader: "集合",
  aggregationFunctionLabelSum: "總數",
  aggregationFunctionLabelAvg: "平均數",
  aggregationFunctionLabelMin: "最小",
  aggregationFunctionLabelMax: "最大",
  aggregationFunctionLabelSize: "尺寸"
};
var zhTW2 = getGridLocalization(zhTWGrid, zhTW);

// node_modules/@mui/x-data-grid/locales/hrHR.js
var hrHRGrid = {
  // Root
  noRowsLabel: "Nema redova",
  noResultsOverlayLabel: "Nema rezultata.",
  // Density selector toolbar button text
  toolbarDensity: "Gustoća",
  toolbarDensityLabel: "Gustoća",
  toolbarDensityCompact: "Kompaktno",
  toolbarDensityStandard: "Standardno",
  toolbarDensityComfortable: "Udobno",
  // Columns selector toolbar button text
  toolbarColumns: "Stupci",
  toolbarColumnsLabel: "Odaberite stupce",
  // Filters toolbar button text
  toolbarFilters: "Filteri",
  toolbarFiltersLabel: "Prikaži filtere",
  toolbarFiltersTooltipHide: "Sakrij filtere",
  toolbarFiltersTooltipShow: "Prikaži filtere",
  toolbarFiltersTooltipActive: (count) => {
    if (count === 1) {
      return `${count} aktivan filter`;
    }
    if (count < 5) {
      return `${count} aktivna filtera`;
    }
    return `${count} aktivnih filtera`;
  },
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Traži…",
  toolbarQuickFilterLabel: "traži",
  toolbarQuickFilterDeleteIconLabel: "Obriši",
  // Export selector toolbar button text
  toolbarExport: "Izvoz",
  toolbarExportLabel: "Izvoz",
  toolbarExportCSV: "Preuzmi kao CSV",
  toolbarExportPrint: "Štampaj",
  toolbarExportExcel: "Preuzmi kao Excel",
  // Columns management text
  columnsManagementSearchTitle: "Traži",
  columnsManagementNoColumns: "Nema stupaca",
  columnsManagementShowHideAllText: "Prikaži/Sakrij sve",
  columnsManagementReset: "Ponovno namjesti",
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Dodaj filter",
  filterPanelRemoveAll: "Ukloni sve",
  filterPanelDeleteIconLabel: "Obriši",
  filterPanelLogicOperator: "Logički operator",
  filterPanelOperator: "Operator",
  filterPanelOperatorAnd: "I",
  filterPanelOperatorOr: "Ili",
  filterPanelColumns: "Stupac",
  filterPanelInputLabel: "Vrijednost",
  filterPanelInputPlaceholder: "Vrijednost filtera",
  // Filter operators text
  filterOperatorContains: "sadrži",
  filterOperatorDoesNotContain: "ne sadrži",
  filterOperatorEquals: "je jednak",
  filterOperatorDoesNotEqual: "nije jednak",
  filterOperatorStartsWith: "počinje sa",
  filterOperatorEndsWith: "završava sa",
  filterOperatorIs: "je",
  filterOperatorNot: "nije",
  filterOperatorAfter: "je poslije",
  filterOperatorOnOrAfter: "je na ili poslije",
  filterOperatorBefore: "je prije",
  filterOperatorOnOrBefore: "je na ili prije",
  filterOperatorIsEmpty: "je prazno",
  filterOperatorIsNotEmpty: "nije prazno",
  filterOperatorIsAnyOf: "je bilo koji od",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Sadrži",
  headerFilterOperatorDoesNotContain: "Ne sadrži",
  headerFilterOperatorEquals: "Jednako",
  headerFilterOperatorDoesNotEqual: "Nije jednako",
  headerFilterOperatorStartsWith: "Počinje sa",
  headerFilterOperatorEndsWith: "Završava sa",
  headerFilterOperatorIs: "Je",
  headerFilterOperatorNot: "Nije",
  headerFilterOperatorAfter: "Je poslije",
  headerFilterOperatorOnOrAfter: "Je uključeno ili poslije",
  headerFilterOperatorBefore: "Je prije",
  headerFilterOperatorOnOrBefore: "Je uključeno ili prije",
  headerFilterOperatorIsEmpty: "Je prazno",
  headerFilterOperatorIsNotEmpty: "Nije prazno",
  headerFilterOperatorIsAnyOf: "Je bilo koji od",
  "headerFilterOperator=": "Jednako",
  "headerFilterOperator!=": "Nije jednako",
  "headerFilterOperator>": "Veći od",
  "headerFilterOperator>=": "Veće ili jednako",
  "headerFilterOperator<": "Manje od",
  "headerFilterOperator<=": "Manje od ili jednako",
  // Filter values text
  filterValueAny: "bilo koji",
  filterValueTrue: "tačno",
  filterValueFalse: "netačno",
  // Column menu text
  columnMenuLabel: "Izbornik",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Prikaži stupce",
  columnMenuManageColumns: "Upravljanje stupcima",
  columnMenuFilter: "Filter",
  columnMenuHideColumn: "Sakrij stupac",
  columnMenuUnsort: "Poništi sortiranje",
  columnMenuSortAsc: "Poredaj uzlazno",
  columnMenuSortDesc: "Poredaj silazno",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => {
    if (count === 1) {
      return `${count} aktivan filter`;
    }
    if (count < 5) {
      return `${count} aktivna filtera`;
    }
    return `${count} aktivnih filtera`;
  },
  columnHeaderFiltersLabel: "Prikaži filtere",
  columnHeaderSortIconLabel: "Poredaj",
  // Rows selected footer text
  footerRowSelected: (count) => {
    if (count === 1) {
      return `Odabran je ${count.toLocaleString()} redak`;
    }
    if (count < 5) {
      return `Odabrana su ${count.toLocaleString()} retka`;
    }
    return `Odabrano je ${count.toLocaleString()} redaka`;
  },
  // Total row amount footer text
  footerTotalRows: "Ukupno redaka:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} od ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Odabir redaka",
  checkboxSelectionSelectAllRows: "Odaberite sve retke",
  checkboxSelectionUnselectAllRows: "Poništi odabir svih redaka",
  checkboxSelectionSelectRow: "Odaberite redak",
  checkboxSelectionUnselectRow: "Poništi odabir retka",
  // Boolean cell text
  booleanCellTrueLabel: "Da",
  booleanCellFalseLabel: "Ne",
  // Actions cell more text
  actionsCellMore: "više",
  // Column pinning text
  pinToLeft: "Prikvači lijevo",
  pinToRight: "Prikvači desno",
  unpin: "Otkvači",
  // Tree Data
  treeDataGroupingHeaderName: "Skupina",
  treeDataExpand: "vidjeti djecu",
  treeDataCollapse: "sakriti djecu",
  // Grouping columns
  groupingColumnHeaderName: "Skupina",
  groupColumn: (name) => `Grupiraj prema ${name}`,
  unGroupColumn: (name) => `Zaustavi grupiranje prema ${name}`,
  // Master/detail
  detailPanelToggle: "Prebacivanje ploče s detaljima",
  expandDetailPanel: "Proširiti",
  collapseDetailPanel: "Skupiti",
  // Row reordering text
  rowReorderingHeaderName: "Promjena redoslijeda",
  // Aggregation
  aggregationMenuItemHeader: "Agregacija",
  aggregationFunctionLabelSum: "iznos",
  aggregationFunctionLabelAvg: "prosj",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "veličina"
};
var hrHR2 = getGridLocalization(hrHRGrid, hrHR);

// node_modules/@mui/x-data-grid/locales/ptPT.js
var ptPTGrid = {
  // Root
  noRowsLabel: "Nenhuma linha",
  noResultsOverlayLabel: "Nenhum resultado encontrado.",
  // Density selector toolbar button text
  toolbarDensity: "Densidade",
  toolbarDensityLabel: "Densidade",
  toolbarDensityCompact: "Compactar",
  toolbarDensityStandard: "Padrão",
  toolbarDensityComfortable: "Confortável",
  // Columns selector toolbar button text
  toolbarColumns: "Colunas",
  toolbarColumnsLabel: "Selecione colunas",
  // Filters toolbar button text
  toolbarFilters: "Filtros",
  toolbarFiltersLabel: "Mostrar filtros",
  toolbarFiltersTooltipHide: "Ocultar filtros",
  toolbarFiltersTooltipShow: "Mostrar filtros",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} filtros ativos` : `${count} filtro ativo`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Procurar…",
  toolbarQuickFilterLabel: "Procurar",
  toolbarQuickFilterDeleteIconLabel: "Claro",
  // Export selector toolbar button text
  toolbarExport: "Exportar",
  toolbarExportLabel: "Exportar",
  toolbarExportCSV: "Descarregar como CSV",
  toolbarExportPrint: "Imprimir",
  toolbarExportExcel: "Descarregar como Excel",
  // Columns management text
  columnsManagementSearchTitle: "Procurar",
  columnsManagementNoColumns: "Sem colunas",
  columnsManagementShowHideAllText: "Mostrar/Ocultar Todas",
  columnsManagementReset: "Repor",
  columnsManagementDeleteIconLabel: "Limpar",
  // Filter panel text
  filterPanelAddFilter: "Adicionar filtro",
  filterPanelRemoveAll: "Excluir todos",
  filterPanelDeleteIconLabel: "Excluir",
  filterPanelLogicOperator: "Operador lógico",
  filterPanelOperator: "Operador",
  filterPanelOperatorAnd: "E",
  filterPanelOperatorOr: "Ou",
  filterPanelColumns: "Colunas",
  filterPanelInputLabel: "Valor",
  filterPanelInputPlaceholder: "Valor do filtro",
  // Filter operators text
  filterOperatorContains: "contém",
  filterOperatorDoesNotContain: "não contém",
  filterOperatorEquals: "é igual a",
  filterOperatorDoesNotEqual: "não é igual a",
  filterOperatorStartsWith: "começa com",
  filterOperatorEndsWith: "termina com",
  filterOperatorIs: "é",
  filterOperatorNot: "não é",
  filterOperatorAfter: "está depois",
  filterOperatorOnOrAfter: "está ligado ou depois",
  filterOperatorBefore: "é antes",
  filterOperatorOnOrBefore: "está ligado ou antes",
  filterOperatorIsEmpty: "está vazia",
  filterOperatorIsNotEmpty: "não está vazio",
  filterOperatorIsAnyOf: "é qualquer um",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Contém",
  headerFilterOperatorDoesNotContain: "Não contém",
  headerFilterOperatorEquals: "É igual a",
  headerFilterOperatorDoesNotEqual: "Não é igual",
  headerFilterOperatorStartsWith: "Começa com",
  headerFilterOperatorEndsWith: "Termina com",
  headerFilterOperatorIs: "É",
  headerFilterOperatorNot: "Não é",
  headerFilterOperatorAfter: "Está depois",
  headerFilterOperatorOnOrAfter: "Está ligado ou depois",
  headerFilterOperatorBefore: "É antes",
  headerFilterOperatorOnOrBefore: "Está ligado ou antes",
  headerFilterOperatorIsEmpty: "Está vazia",
  headerFilterOperatorIsNotEmpty: "Não está vazio",
  headerFilterOperatorIsAnyOf: "Algum",
  "headerFilterOperator=": "É igual a",
  "headerFilterOperator!=": "Não é igual",
  "headerFilterOperator>": "Maior que",
  "headerFilterOperator>=": "Melhor que ou igual a",
  "headerFilterOperator<": "Menor que",
  "headerFilterOperator<=": "Menos que ou igual a",
  // Filter values text
  filterValueAny: "qualquer",
  filterValueTrue: "verdadeiro",
  filterValueFalse: "falso",
  // Column menu text
  columnMenuLabel: "Menu",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Mostrar colunas",
  columnMenuManageColumns: "Gerir colunas",
  columnMenuFilter: "Filtro",
  columnMenuHideColumn: "Ocultar coluna",
  columnMenuUnsort: "Desclassificar",
  columnMenuSortAsc: "Classificar por ordem crescente",
  columnMenuSortDesc: "Classificar por ordem decrescente",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} filtros ativos` : `${count} filtro ativo`,
  columnHeaderFiltersLabel: "Mostrar filtros",
  columnHeaderSortIconLabel: "Organizar",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} linhas selecionadas` : `${count.toLocaleString()} linha selecionada`,
  // Total row amount footer text
  footerTotalRows: "Total de linhas:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} de ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Seleção de caixa de seleção",
  checkboxSelectionSelectAllRows: "Selecione todas as linhas",
  checkboxSelectionUnselectAllRows: "Desmarque todas as linhas",
  checkboxSelectionSelectRow: "Selecione a linha",
  checkboxSelectionUnselectRow: "Desmarcar linha",
  // Boolean cell text
  booleanCellTrueLabel: "sim",
  booleanCellFalseLabel: "não",
  // Actions cell more text
  actionsCellMore: "mais",
  // Column pinning text
  pinToLeft: "Fixar à esquerda",
  pinToRight: "Fixar à direita",
  unpin: "Desafixar",
  // Tree Data
  treeDataGroupingHeaderName: "Grupo",
  treeDataExpand: "ver crianças",
  treeDataCollapse: "esconder crianças",
  // Grouping columns
  groupingColumnHeaderName: "Grupo",
  groupColumn: (name) => `Agrupar por ${name}`,
  unGroupColumn: (name) => `Pare de agrupar por ${name}`,
  // Master/detail
  detailPanelToggle: "Alternar painel de detalhes",
  expandDetailPanel: "Expandir",
  collapseDetailPanel: "Colapsar",
  // Row reordering text
  rowReorderingHeaderName: "Reordenação de linhas",
  // Aggregation
  aggregationMenuItemHeader: "Agregação",
  aggregationFunctionLabelSum: "soma",
  aggregationFunctionLabelAvg: "média",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "máx.",
  aggregationFunctionLabelSize: "tamanho"
};
var ptPT2 = getGridLocalization(ptPTGrid, ptPT);

// node_modules/@mui/x-data-grid/locales/zhHK.js
var zhHKGrid = {
  // Root
  noRowsLabel: "沒有行",
  noResultsOverlayLabel: "未找到結果。",
  // Density selector toolbar button text
  toolbarDensity: "密度",
  toolbarDensityLabel: "密度",
  toolbarDensityCompact: "袖珍的",
  toolbarDensityStandard: "標準",
  toolbarDensityComfortable: "舒服的",
  // Columns selector toolbar button text
  toolbarColumns: "列",
  toolbarColumnsLabel: "選擇列",
  // Filters toolbar button text
  toolbarFilters: "過濾器",
  toolbarFiltersLabel: "顯示過濾器",
  toolbarFiltersTooltipHide: "隱藏過濾器",
  toolbarFiltersTooltipShow: "顯示過濾器",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} 個有效過濾器` : `${count} 個活動過濾器`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "搜尋…",
  toolbarQuickFilterLabel: "搜尋",
  toolbarQuickFilterDeleteIconLabel: "清除",
  // Export selector toolbar button text
  toolbarExport: "出口",
  toolbarExportLabel: "出口",
  toolbarExportCSV: "下載為 CSV",
  toolbarExportPrint: "列印",
  toolbarExportExcel: "下載為 Excel",
  // Columns management text
  columnsManagementSearchTitle: "搜尋",
  columnsManagementNoColumns: "沒有列",
  columnsManagementShowHideAllText: "顯示/隱藏所有",
  columnsManagementReset: "重置",
  columnsManagementDeleteIconLabel: "清除",
  // Filter panel text
  filterPanelAddFilter: "新增過濾器",
  filterPanelRemoveAll: "移除所有",
  filterPanelDeleteIconLabel: "刪除",
  filterPanelLogicOperator: "邏輯運算符",
  filterPanelOperator: "操作員",
  filterPanelOperatorAnd: "和",
  filterPanelOperatorOr: "或者",
  filterPanelColumns: "列",
  filterPanelInputLabel: "價值",
  filterPanelInputPlaceholder: "過濾值",
  // Filter operators text
  filterOperatorContains: "包含",
  filterOperatorDoesNotContain: "不包含",
  filterOperatorEquals: "等於",
  filterOperatorDoesNotEqual: "不等於",
  filterOperatorStartsWith: "以。。開始",
  filterOperatorEndsWith: "以。。結束",
  filterOperatorIs: "是",
  filterOperatorNot: "不是",
  filterOperatorAfter: "是在之後",
  filterOperatorOnOrAfter: "是在或之後",
  filterOperatorBefore: "是在之前",
  filterOperatorOnOrBefore: "是在或之前",
  filterOperatorIsEmpty: "是空的",
  filterOperatorIsNotEmpty: "不為空",
  filterOperatorIsAnyOf: "是以下任一個",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "包含",
  headerFilterOperatorDoesNotContain: "不包含",
  headerFilterOperatorEquals: "等於",
  headerFilterOperatorDoesNotEqual: "不等於",
  headerFilterOperatorStartsWith: "以。。開始",
  headerFilterOperatorEndsWith: "以。。結束",
  headerFilterOperatorIs: "是",
  headerFilterOperatorNot: "不是",
  headerFilterOperatorAfter: "是在之後",
  headerFilterOperatorOnOrAfter: "是在或之後",
  headerFilterOperatorBefore: "是之前",
  headerFilterOperatorOnOrBefore: "是在或之前",
  headerFilterOperatorIsEmpty: "是空的",
  headerFilterOperatorIsNotEmpty: "不為空",
  headerFilterOperatorIsAnyOf: "是以下任一個",
  "headerFilterOperator=": "等於",
  "headerFilterOperator!=": "不等於",
  "headerFilterOperator>": "大於",
  "headerFilterOperator>=": "大於或等於",
  "headerFilterOperator<": "少於",
  "headerFilterOperator<=": "小於或等於",
  // Filter values text
  filterValueAny: "任何",
  filterValueTrue: "真的",
  filterValueFalse: "錯誤的",
  // Column menu text
  columnMenuLabel: "選單",
  columnMenuAriaLabel: (columnName) => `${columnName} 欄目選單`,
  columnMenuShowColumns: "顯示欄目",
  columnMenuManageColumns: "管理欄目",
  columnMenuFilter: "篩選",
  columnMenuHideColumn: "隱藏列",
  columnMenuUnsort: "取消排序",
  columnMenuSortAsc: "按升序排序",
  columnMenuSortDesc: "按降序排序",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} 個有效過濾器` : `${count} 個活動過濾器`,
  columnHeaderFiltersLabel: "顯示過濾器",
  columnHeaderSortIconLabel: "種類",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `已選擇 ${count.toLocaleString()} 行` : `已選擇 ${count.toLocaleString()} 行`,
  // Total row amount footer text
  footerTotalRows: "總行數：",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${totalCount.toLocaleString()} 的 ${visibleCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "複選框選擇",
  checkboxSelectionSelectAllRows: "選擇所有行",
  checkboxSelectionUnselectAllRows: "取消選擇所有行",
  checkboxSelectionSelectRow: "選擇行",
  checkboxSelectionUnselectRow: "取消選擇行",
  // Boolean cell text
  booleanCellTrueLabel: "是的",
  booleanCellFalseLabel: "不",
  // Actions cell more text
  actionsCellMore: "更多的",
  // Column pinning text
  pinToLeft: "固定到左側",
  pinToRight: "固定到右側",
  unpin: "取消固定",
  // Tree Data
  treeDataGroupingHeaderName: "Group",
  treeDataExpand: "看看孩子們",
  treeDataCollapse: "隱藏孩子",
  // Grouping columns
  groupingColumnHeaderName: "團體",
  groupColumn: (name) => `按 ${name} 分組`,
  unGroupColumn: (name) => `停止以 ${name} 分組`,
  // Master/detail
  detailPanelToggle: "詳細資訊面板切換",
  expandDetailPanel: "擴張",
  collapseDetailPanel: "坍塌",
  // Row reordering text
  rowReorderingHeaderName: "行重新排序",
  // Aggregation
  aggregationMenuItemHeader: "聚合",
  aggregationFunctionLabelSum: "和",
  aggregationFunctionLabelAvg: "平均",
  aggregationFunctionLabelMin: "分分鐘",
  aggregationFunctionLabelMax: "最大限度",
  aggregationFunctionLabelSize: "尺寸"
};
var zhHK2 = getGridLocalization(zhHKGrid, zhHK);

// node_modules/@mui/x-data-grid/locales/isIS.js
var isISGrid = {
  // Root
  noRowsLabel: "Engar raðir",
  noResultsOverlayLabel: "Engar niðurstöður",
  // Density selector toolbar button text
  toolbarDensity: "Þéttleiki",
  toolbarDensityLabel: "Þéttleiki",
  toolbarDensityCompact: "Þétt",
  toolbarDensityStandard: "Staðlað",
  toolbarDensityComfortable: "Rúmlegt",
  // Columns selector toolbar button text
  toolbarColumns: "Dálkar",
  toolbarColumnsLabel: "Veldu dálka",
  // Filters toolbar button text
  toolbarFilters: "Sía",
  toolbarFiltersLabel: "Sjá síur",
  toolbarFiltersTooltipHide: "Fela síur",
  toolbarFiltersTooltipShow: "Sjá síur",
  toolbarFiltersTooltipActive: (count) => count !== 1 ? `${count} virk sía` : `${count} virkar síur`,
  // Quick filter toolbar field
  toolbarQuickFilterPlaceholder: "Leita…",
  toolbarQuickFilterLabel: "Leita",
  toolbarQuickFilterDeleteIconLabel: "Eyða",
  // Export selector toolbar button text
  toolbarExport: "Flytja út",
  toolbarExportLabel: "Flytja út",
  toolbarExportCSV: "Hlaða niður sem CSV",
  toolbarExportPrint: "Prenta",
  toolbarExportExcel: "Hlaða niður sem Excel",
  // Columns management text
  // columnsManagementSearchTitle: 'Search',
  // columnsManagementNoColumns: 'No columns',
  // columnsManagementShowHideAllText: 'Show/Hide All',
  // columnsManagementReset: 'Reset',
  // columnsManagementDeleteIconLabel: 'Clear',
  // Filter panel text
  filterPanelAddFilter: "Bæta síu",
  filterPanelRemoveAll: "Fjarlægja alla",
  filterPanelDeleteIconLabel: "Eyða",
  filterPanelLogicOperator: "Rökvirkir",
  filterPanelOperator: "Virkir",
  filterPanelOperatorAnd: "Og",
  filterPanelOperatorOr: "Eða",
  filterPanelColumns: "Dálkar",
  filterPanelInputLabel: "Gildi",
  filterPanelInputPlaceholder: "Síu gildi",
  // Filter operators text
  filterOperatorContains: "inniheldur",
  // filterOperatorDoesNotContain: 'does not contain',
  filterOperatorEquals: "jafnt og",
  // filterOperatorDoesNotEqual: 'does not equal',
  filterOperatorStartsWith: "byrjar með",
  filterOperatorEndsWith: "endar með",
  filterOperatorIs: "er líka með",
  filterOperatorNot: "er ekki líka með",
  filterOperatorAfter: "eftir",
  filterOperatorOnOrAfter: "á eða eftir",
  filterOperatorBefore: "fyrir",
  filterOperatorOnOrBefore: "á eða fyrir",
  filterOperatorIsEmpty: "inniheldur ekki gögn",
  filterOperatorIsNotEmpty: "inniheldur gögn",
  filterOperatorIsAnyOf: "inniheldur einn af",
  "filterOperator=": "=",
  "filterOperator!=": "!=",
  "filterOperator>": ">",
  "filterOperator>=": ">=",
  "filterOperator<": "<",
  "filterOperator<=": "<=",
  // Header filter operators text
  headerFilterOperatorContains: "Inniheldur",
  // headerFilterOperatorDoesNotContain: 'Does not contain',
  headerFilterOperatorEquals: "Jafnt og",
  // headerFilterOperatorDoesNotEqual: 'Does not equal',
  headerFilterOperatorStartsWith: "Byrjar með",
  headerFilterOperatorEndsWith: "Endar með",
  headerFilterOperatorIs: "Er jafnt og",
  headerFilterOperatorNot: "Er ekki jafnt og",
  headerFilterOperatorAfter: "Eftir",
  headerFilterOperatorOnOrAfter: "Á eða eftir",
  headerFilterOperatorBefore: "Fyrir",
  headerFilterOperatorOnOrBefore: "Á eða fyrir",
  headerFilterOperatorIsEmpty: "Inniheldur ekki gögn",
  headerFilterOperatorIsNotEmpty: "Inniheldur gögn",
  headerFilterOperatorIsAnyOf: "Inniheldur einn af",
  "headerFilterOperator=": "Jafnt og",
  "headerFilterOperator!=": "Ekki jafnt og",
  "headerFilterOperator>": "Stærra en",
  "headerFilterOperator>=": "Stærra en eða jafnt og",
  "headerFilterOperator<": "Minna en",
  "headerFilterOperator<=": "Minna en eða jafnt og",
  // Filter values text
  filterValueAny: "hvað sem er",
  filterValueTrue: "satt",
  filterValueFalse: "falskt",
  // Column menu text
  columnMenuLabel: "Valmynd",
  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,
  columnMenuShowColumns: "Sýna dálka",
  columnMenuManageColumns: "Stjórna dálkum",
  columnMenuFilter: "Síur",
  columnMenuHideColumn: "Fela dálka",
  columnMenuUnsort: "Fjarlægja röðun",
  columnMenuSortAsc: "Raða hækkandi",
  columnMenuSortDesc: "Raða lækkandi",
  // Column header text
  columnHeaderFiltersTooltipActive: (count) => count !== 1 ? `${count} virkar síur` : `Ein virk sía`,
  columnHeaderFiltersLabel: "Sýna síur",
  columnHeaderSortIconLabel: "Raða",
  // Rows selected footer text
  footerRowSelected: (count) => count !== 1 ? `${count.toLocaleString()} raðir valdar` : `Ein röð valin`,
  // Total row amount footer text
  footerTotalRows: "Heildarfjöldi lína:",
  // Total visible row amount footer text
  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} af ${totalCount.toLocaleString()}`,
  // Checkbox selection text
  checkboxSelectionHeaderName: "Val á gátreit",
  checkboxSelectionSelectAllRows: "Velja allar raðir",
  checkboxSelectionUnselectAllRows: "Afvelja allar raðir",
  checkboxSelectionSelectRow: "Velja röð",
  checkboxSelectionUnselectRow: "Afvelja röð",
  // Boolean cell text
  booleanCellTrueLabel: "já",
  booleanCellFalseLabel: "nei",
  // Actions cell more text
  actionsCellMore: "meira",
  // Column pinning text
  pinToLeft: "Festa til vinstri",
  pinToRight: "Festa til hægri",
  unpin: "Losa um",
  // Tree Data
  treeDataGroupingHeaderName: "Hópur",
  treeDataExpand: "Sýna undirliði",
  treeDataCollapse: "Fela undirliði",
  // Grouping columns
  groupingColumnHeaderName: "Hópur",
  groupColumn: (name) => `Hópa eftir ${name}`,
  unGroupColumn: (name) => `Fjarlægja hópun eftir ${name}`,
  // Master/detail
  detailPanelToggle: "Stækka/minnka smáatriðaspjald",
  expandDetailPanel: "Stækka",
  collapseDetailPanel: "Minnka",
  // Row reordering text
  rowReorderingHeaderName: "Endurröðun raða",
  // Aggregation
  aggregationMenuItemHeader: "Samsafn",
  aggregationFunctionLabelSum: "sum",
  aggregationFunctionLabelAvg: "avg",
  aggregationFunctionLabelMin: "min",
  aggregationFunctionLabelMax: "max",
  aggregationFunctionLabelSize: "stærð"
};
var isIS2 = getGridLocalization(isISGrid, isIS);
export {
  arSD2 as arSD,
  beBY,
  bgBG2 as bgBG,
  bnBD2 as bnBD,
  csCZ2 as csCZ,
  daDK2 as daDK,
  deDE2 as deDE,
  elGR2 as elGR,
  enUS2 as enUS,
  esES2 as esES,
  faIR2 as faIR,
  fiFI2 as fiFI,
  frFR2 as frFR,
  heIL2 as heIL,
  hrHR2 as hrHR,
  huHU2 as huHU,
  hyAM2 as hyAM,
  isIS2 as isIS,
  itIT2 as itIT,
  jaJP2 as jaJP,
  koKR2 as koKR,
  nbNO2 as nbNO,
  nlNL2 as nlNL,
  nnNO2 as nnNO,
  plPL2 as plPL,
  ptBR2 as ptBR,
  ptPT2 as ptPT,
  roRO2 as roRO,
  ruRU2 as ruRU,
  skSK2 as skSK,
  svSE2 as svSE,
  trTR2 as trTR,
  ukUA2 as ukUA,
  urPK,
  viVN2 as viVN,
  zhCN2 as zhCN,
  zhHK2 as zhHK,
  zhTW2 as zhTW
};
//# sourceMappingURL=@mui_x-data-grid_locales.js.map
