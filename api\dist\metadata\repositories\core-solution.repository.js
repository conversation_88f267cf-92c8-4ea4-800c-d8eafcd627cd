"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreSolutionRepository = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../shared/repositories");
const models_1 = require("../models");
let CoreSolutionRepository = class CoreSolutionRepository extends repositories_1.BaseRepository {
    constructor() {
        super(models_1.CoreSolution);
    }
    getAllCoreSolutions() {
        return this.findAll({
            include: [
                {
                    model: models_1.LocationType,
                    attributes: ['id', 'title', 'code', 'canAcquireCapability', 'locationFormSections'],
                    where: {
                        active: true,
                        deleted: false,
                    },
                    required: false,
                },
            ],
            order: [
                ['id', 'ASC'],
                [{ model: models_1.LocationType, as: 'locationTypes' }, 'title', 'ASC'],
            ],
        });
    }
    getCoreSolutionById(id) {
        return this.findById(id);
    }
    getCoreSolutionWithLoTypeById(id) {
        return this.findOne({
            where: { id },
            include: [
                {
                    model: models_1.LocationType,
                    attributes: ['id', 'title', 'code', 'canAcquireCapability', 'locationFormSections'],
                    where: {
                        active: true,
                        deleted: false,
                    },
                    required: false,
                },
            ],
        });
    }
    isCoreSolutionExistById(id) {
        return this.isRecordExist({ where: { id } });
    }
    getCoreSolutionByPillar(pillar) {
        return this.findAll({ where: { pillar } });
    }
};
CoreSolutionRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], CoreSolutionRepository);
exports.CoreSolutionRepository = CoreSolutionRepository;
//# sourceMappingURL=core-solution.repository.js.map