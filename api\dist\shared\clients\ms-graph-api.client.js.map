{"version": 3, "file": "ms-graph-api.client.js", "sourceRoot": "", "sources": ["../../../src/shared/clients/ms-graph-api.client.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAAyC;AACzC,2CAAoD;AACpD,gEAA0D;AAC1D,4CAA4C;AAC5C,0CAA0C;AAInC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAI5B,YAEkB,kBAAsD,EAEtD,qBAAkC,EACX,aAA4B;QAHnD,uBAAkB,GAAlB,kBAAkB,CAAoC;QAEtD,0BAAqB,GAArB,qBAAqB,CAAa;QACX,kBAAa,GAAb,aAAa,CAAe;QAEpE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC;QAC/D,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG;YACnB,MAAM,EAAE,CAAC,GAAG,WAAW,WAAW,CAAC;SACnC,CAAC;IACH,CAAC;IAEa,QAAQ;;YACrB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxF,CAAC;KAAA;IAEa,UAAU;;YACvB,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9C,OAAO,EAAE,aAAa,EAAE,UAAU,WAAW,EAAE,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC;QACjF,CAAC;KAAA;IAEY,cAAc,CAAC,MAAc;;YACzC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,8GAA8G,MAAM,8BAA8B,MAAM,GAAG,CAC3J,CAAC;YACH,OAAO,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAI,IAAI,CAAC;QACnD,CAAC;KAAA;IAEY,qBAAqB,CAAC,KAAa;;YAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,2GAA2G,KAAK,GAAG,CACnH,CAAC;YACH,OAAO,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAI,IAAI,CAAC;QACnD,CAAC;KAAA;IAEY,eAAe,CAAC,OAAiB;;;YAC7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnD,MAAM,OAAO,GAAoB,EAAE,CAAC;YAEpC,IAAI,QAAQ,GAEF,+FAA+F,GAAG,8BAA8B,GAAG,GAAG,CAAC;YAEjJ,OAAO,QAAQ,EAAE;gBAChB,IAAI;oBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACrF,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;oBAE1B,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;oBAE5B,QAAQ;wBACP,CAAA,MAAA,IAAI,CAAC,iBAAiB,CAAC,0CAAE,OAAO,CAC/B,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,EACzE,EAAE,CACF,KAAI,IAAI,CAAC;iBACX;gBAAC,OAAO,KAAK,EAAE;oBACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;oBACjE,MAAM;iBACN;aACD;YAED,OAAO,OAAO,CAAC;;KACf;IAEY,WAAW,CAAC,UAAkB,EAAE,OAAe,EAAE,KAAc;;YAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,kHAAkH,kBAAkB,CACnI,UAAU,CACV,cAAc,kBAAkB,CAAC,UAAU,CAAC,2BAA2B,kBAAkB,CACzF,UAAU,CACV,cAAc,OAAO,WAAW,KAAK,0HAA0H,CAChK,CAAC;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAC7B,CAAC,CAAC,EAAE,eAAC,OAAA,CAAC,CAAA,MAAA,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,iBAAiB,0CAAE,WAAW,EAAE,0CAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAA,CAAA,EAAA,CACrE,CAAC;YACF,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAEY,0BAA0B,CAAC,MAAc;;YACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,8GAA8G,MAAM,8BAA8B,MAAM,GAAG,CAC3J,CAAC;YACH,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAEY,iCAAiC,CAAC,GAAW;;YACzD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,+FAA+F,GAAG,8BAA8B,GAAG,GAAG,CACtI,CAAC;YACH,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;CACD,CAAA;AAjHY,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAMV,WAAA,IAAA,eAAM,EAAC,wBAAY,CAAC,qBAAqB,CAAC,CAAA;IAE1C,WAAA,IAAA,eAAM,EAAC,wBAAY,CAAC,8BAA8B,CAAC,CAAA;IAEnD,WAAA,IAAA,eAAM,EAAC,8BAAa,CAAC,CAAA;qCAHe,IAAI,CAAC,6BAA6B,EAE/B,sBAAW;QACI,8BAAa;GATzD,gBAAgB,CAiH5B;AAjHY,4CAAgB"}