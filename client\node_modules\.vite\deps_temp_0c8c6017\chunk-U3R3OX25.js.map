{"version": 3, "sources": ["../../@mui/material/List/ListContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst ListContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ListContext.displayName = 'ListContext';\n}\nexport default ListContext;"], "mappings": ";;;;;;;;AAEA,YAAuB;AAKvB,IAAM,cAAiC,oBAAc,CAAC,CAAC;AACvD,IAAI,MAAuC;AACzC,cAAY,cAAc;AAC5B;AACA,IAAO,sBAAQ;", "names": []}