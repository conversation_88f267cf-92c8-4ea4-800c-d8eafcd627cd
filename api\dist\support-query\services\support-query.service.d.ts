import { AdminApiClient, MSGraphApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedNotificationService } from 'src/shared/services';
import { CurrentContext } from 'src/shared/types';
import { PaginatedSupportQueryListResponseDto, SupportQueryRequestDto } from '../dtos';
import { SupportQueryRepository } from '../repositories';
export declare class SupportQueryService {
    private readonly adminApiClient;
    private readonly supportQueryRepository;
    private readonly databaseHelper;
    private readonly sharedNotificationService;
    private readonly adminApiServie;
    private readonly mSGraphApiClient;
    constructor(adminApiClient: AdminApiClient, supportQueryRepository: SupportQueryRepository, databaseHelper: DatabaseHelper, sharedNotificationService: SharedNotificationService, adminApiServie: AdminApiClient, mSGraphApiClient: MSGraphApiClient);
    createUserSupportQuery(data: SupportQueryRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getQueryListByFilter(page: number, limit: number, filterDto?: any): Promise<PaginatedSupportQueryListResponseDto>;
}
