import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { SupportQueryRequestDto } from '../dtos';
import { SupportQueryRepository } from '../repositories';
export declare class SupportQueryService {
    private readonly supportQueryRepository;
    private readonly databaseHelper;
    constructor(supportQueryRepository: SupportQueryRepository, databaseHelper: DatabaseHelper);
    createUserSupportQuery(data: SupportQueryRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
}
