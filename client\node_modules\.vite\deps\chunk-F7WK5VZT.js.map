{"version": 3, "sources": ["../../@mui/material/Box/Box.js", "../../@mui/material/Box/boxClasses.js"], "sourcesContent": ["'use client';\n\nimport { createBox } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from \"../className/index.js\";\nimport { createTheme } from \"../styles/index.js\";\nimport THEME_ID from \"../styles/identifier.js\";\nimport boxClasses from \"./boxClasses.js\";\nconst defaultTheme = createTheme();\nconst Box = createBox({\n  themeId: THEME_ID,\n  defaultTheme,\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nconst boxClasses = generateUtilityClasses('MuiBox', ['root']);\nexport default boxClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,wBAAsB;;;ACFtB,IAAM,aAAa,uBAAuB,UAAU,CAAC,MAAM,CAAC;AAC5D,IAAO,qBAAQ;;;ADMf,IAAM,eAAe,YAAY;AACjC,IAAM,MAAM,UAAU;AAAA,EACpB,SAAS;AAAA,EACT;AAAA,EACA,kBAAkB,mBAAW;AAAA,EAC7B,mBAAmB,2BAAmB;AACxC,CAAC;AACD,OAAwC,IAAI,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7E,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,cAAQ;", "names": ["PropTypes"]}