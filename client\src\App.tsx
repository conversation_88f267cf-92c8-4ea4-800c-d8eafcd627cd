import './global.css';
// i18n
import '@/locales/i18n';
import { InteractionType } from '@azure/msal-browser';
import { AuthenticatedTemplate, MsalAuthenticationTemplate } from '@azure/msal-react';
import { AdAuthProvider } from '@/core/providers';
import RequestInterceptor from '@/core/interceptors/Request.interceptor';
import TokenRefreshProvider from '@/core/providers/TokenRefresh.provider';
import Router from '@/routes/routes';
import { useScrollToTop } from '@/hooks/use-scroll-to-top';
import PermissionProvider from './core/providers/Permission.provider';

function App() {
  useScrollToTop();

  return (
    <AdAuthProvider>
      <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
        <AuthenticatedTemplate>
          <TokenRefreshProvider>
            <RequestInterceptor>
              <PermissionProvider>
                <Router />
              </PermissionProvider>
            </RequestInterceptor>
          </TokenRefreshProvider>
        </AuthenticatedTemplate>
      </MsalAuthenticationTemplate>
    </AdAuthProvider>
  );
}

export default App;
