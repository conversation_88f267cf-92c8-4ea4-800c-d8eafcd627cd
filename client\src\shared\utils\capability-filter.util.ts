import {
  CAPABILITY_LEVEL_ENUM,
  CAPABILITY_STATUS_ENUM,
  CAPABILITY_TYPE_ENUM,
  CAPABILITY_TYPE_ENUM_DISPLAY,
  PROVIDER_ENUM,
} from '../enum/capability.enum';
import { CapabilityListFiltersModel } from '../models';
import { BingoCardCapabilityListFiltersModel } from '../models/bingo-card.model';
export const mapFiltersToApiPayload = (
  filters: Record<string, { id: string; label: string }[]>,
): Partial<CapabilityListFiltersModel> => {
  const result: Partial<CapabilityListFiltersModel> = {};

  // Handle hierarchical entity filters
  if (filters.hierarchyEntityId?.length) {
    result.hierarchyEntityId = Number(filters.hierarchyEntityId[0].id);
  } else {
    const entityIds = [
      ...((filters.group as any[]) || []),
      ...((filters.country as any[]) || []),
      ...((filters.area as any[]) || []),
      ...((filters.region as any[]) || []),
      ...((filters.cluster as any[]) || []),
    ]
      .map((item) => Number(item.id))
      .filter(Boolean);

    if (entityIds.length) result.entityIds = entityIds;
  }

  // Text filter for capability name
  if (Array.isArray(filters.entries) && filters.entries.length > 0) {
    result.entryIds = filters.entries.map((item) => parseInt(item.id));
  }

  // Owner login ID (single select or text input)
  if (Array.isArray(filters.owner) && filters.owner.length > 0) {
    result.ownerLoginId = filters.owner[0].id;
  }

  // Enum and multi-select filters
  const mapIds = (key: string) => ((filters[key] as any[]) || []).map((item) => item.id).filter(Boolean);

  const coreSolutionIds = mapIds('core_solution').map(Number);
  if (coreSolutionIds.length) result.coreSolutionIds = coreSolutionIds;

  const verticalIds = mapIds('verticals');
  if (verticalIds.length) result.verticalCodes = verticalIds;

  const statuses = mapIds('status') as CAPABILITY_STATUS_ENUM[];
  if (statuses.length) result.statuses = statuses;

  const providers = mapIds('provider') as PROVIDER_ENUM[];
  if (providers.length) result.providers = providers;

  const capabilityLevel = mapIds('entry_level') as CAPABILITY_LEVEL_ENUM[];
  if (capabilityLevel.length) result.capabilityLevel = capabilityLevel;

  const capabilityTypes = mapIds('entry_type') as CAPABILITY_TYPE_ENUM[];
  if (capabilityTypes.length) result.capabilityTypes = capabilityTypes;

  return result;
};

export function removeKeysFromFilters(obj: Record<string, any>, keysToRemove: string[]): Record<string, any> {
  const result: Record<string, any> = { ...obj };

  for (const key of keysToRemove) {
    const value = result[key];

    const hasValue =
      value !== undefined &&
      value !== null &&
      !(
        (typeof value === 'string' && value.trim() === '') ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
      );

    if (hasValue) {
      delete result[key];
    }
  }

  return result;
}

export const getCapabilityTypeOptions = (): { id: string; label: string }[] => {
  return Object.entries(CAPABILITY_TYPE_ENUM).map(([key, value]) => ({
    id: value,
    label: CAPABILITY_TYPE_ENUM_DISPLAY[key as keyof typeof CAPABILITY_TYPE_ENUM_DISPLAY] || value,
  }));
};

export const getCapabilityTabColor = (id: string): 'success' | 'warning' | 'error' | 'default' => {
  switch (id) {
    case CAPABILITY_TYPE_ENUM.OPERATIONAL_AND_CONTRACTUAL:
      return 'success';
    case CAPABILITY_TYPE_ENUM.SYSTEM_AND_TECHNOLOGY:
      return 'warning';
    case CAPABILITY_TYPE_ENUM.CERTIFICATE_AND_LICENCE:
      return 'error';
    default:
      return 'default';
  }
};

export function mapFiltersForExportApi(filters: Record<string, any>) {
  const mappedFilters: Record<string, any> = {};

  Object.entries(filters).forEach(([key, value]) => {
    // Skip keys with '[]' in them
    if (key.includes('[]')) return;

    // Handle capabilityTypes
    if (key === 'capabilityTypes') {
      if (typeof value === 'string' && value !== 'all' && value.trim()) {
        mappedFilters[key] = [value];
      }
      return;
    }

    // Handle searchTerm
    if (key === 'searchTerm') {
      if (typeof value === 'string' && value.trim()) {
        mappedFilters[key] = value.trim();
      }
      return;
    }

    // Skip null/undefined
    if (value == null) return;

    // Handle arrays
    if (Array.isArray(value)) {
      if (value.length === 0) return;

      // Handle arrays of { id, label }
      if (typeof value[0] === 'object' && value[0]?.id !== undefined) {
        const ids = value.map((v) => v.id).filter((id) => id != null);
        if (ids.length > 0) {
          mappedFilters[key] = ids;
        }
      } else {
        // Flat array — only include if it's not just an empty inner array
        const nonEmpty = value.filter((v) => {
          if (Array.isArray(v)) return v.length > 0;
          return v != null;
        });
        if (nonEmpty.length > 0) {
          mappedFilters[key] = nonEmpty;
        }
      }
      return;
    }

    // Add non-array, non-null, truthy scalar values
    if (value) {
      mappedFilters[key] = value;
    }
  });

  return mappedFilters;
}

export const mapBingoCardFiltersToApiPayload = (
  filters: Record<string, { id: string; label: string }[]>,
): Partial<BingoCardCapabilityListFiltersModel> => {
  const result: Partial<BingoCardCapabilityListFiltersModel> = {};

  const mapIds = (key: string) => ((filters[key] as any[]) || []).map((item) => item.id).filter(Boolean);

  const coreSolutionIds = mapIds('core_solution').map(Number);
  if (coreSolutionIds.length) result.coreSolutionIds = coreSolutionIds;

  const verticalIds = mapIds('verticals');
  if (verticalIds.length) result.verticalCodes = verticalIds;

  const statuses = mapIds('status') as CAPABILITY_STATUS_ENUM[];
  if (statuses.length) result.statuses = statuses;

  const entryIds = mapIds('entry').map(Number);
  if (entryIds.length) result.entryIds = entryIds;
  const capabilityLevel = mapIds('entry_level') as CAPABILITY_LEVEL_ENUM[];
  if (capabilityLevel.length) result.capabilityLevel = capabilityLevel;

  const capabilityTypes = mapIds('entry_type') as CAPABILITY_TYPE_ENUM[];
  if (capabilityTypes.length) result.capabilityTypes = capabilityTypes;

  return result;
};

export const getStatusTextColor = (status: string | null): string => {
  if (!status) return 'black';

  switch (status) {
    case CAPABILITY_STATUS_ENUM.EXISTING:
    case CAPABILITY_STATUS_ENUM.EXPIRED:
    case CAPABILITY_STATUS_ENUM.RETIRED:
      return 'white';
    case CAPABILITY_STATUS_ENUM.PLANNED:
      return 'black';
    default:
      return 'black';
  }
};

export const getStatusText = (status: string | null): string => {
  if (!status) return '';

  switch (status) {
    case CAPABILITY_STATUS_ENUM.PLANNED:
      return 'P';
    case CAPABILITY_STATUS_ENUM.EXISTING:
      return 'C';
    case CAPABILITY_STATUS_ENUM.EXPIRED:
      return 'E';
    case CAPABILITY_STATUS_ENUM.RETIRED:
      return 'R';
    default:
      return '';
  }
};
