"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegalEntityService = void 0;
const common_1 = require("@nestjs/common");
const dtos_1 = require("../dtos");
const repositories_1 = require("../repositories");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const clients_1 = require("../../shared/clients");
let LegalEntityService = class LegalEntityService {
    constructor(countryRepository, legalEntityRepository, locationRepository, sharedPermissionService, historyService, databaseHelper) {
        this.countryRepository = countryRepository;
        this.legalEntityRepository = legalEntityRepository;
        this.locationRepository = locationRepository;
        this.sharedPermissionService = sharedPermissionService;
        this.historyService = historyService;
        this.databaseHelper = databaseHelper;
    }
    upsertLegalEntity(legalEntitySetupRequestDto, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const { legalEntityId, entityId, name } = legalEntitySetupRequestDto, otherDetails = __rest(legalEntitySetupRequestDto, ["legalEntityId", "entityId", "name"]);
            const countryDetail = yield this.countryRepository.getCountryDetailByCondition({
                entityId
            });
            if (!countryDetail) {
                throw new common_1.HttpException('Country setup is required adding before legal entity.', common_1.HttpStatus.NOT_FOUND);
            }
            const hasLegalPermission = yield this.sharedPermissionService.checkGlobalPermission([enums_1.PERMISSIONS.GLOBAL_LEGAL], currentContext.user, entityId);
            if (!legalEntityId && !hasLegalPermission && ((otherDetails === null || otherDetails === void 0 ? void 0 : otherDetails.financeInformation) || (otherDetails === null || otherDetails === void 0 ? void 0 : otherDetails.additionalBusinessInformation) || (otherDetails === null || otherDetails === void 0 ? void 0 : otherDetails.nvoccRegistration) || ((_a = otherDetails === null || otherDetails === void 0 ? void 0 : otherDetails.additionalRegistrationNumbers) === null || _a === void 0 ? void 0 : _a.length))) {
                throw new common_1.HttpException('You do not have permission to add legal/finance information.', common_1.HttpStatus.FORBIDDEN);
            }
            if (legalEntityId) {
                const legalEntityDetail = yield this.legalEntityRepository.getLegalEntityDetailByCondition({
                    id: legalEntityId
                });
                if (!legalEntityDetail) {
                    throw new common_1.HttpException('Legal entity does\'t exist.', common_1.HttpStatus.NOT_FOUND);
                }
                const updatedData = {
                    name,
                    otherDetails
                };
                return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    yield this.legalEntityRepository.updateLegalEntityDetailsByCondition({ id: legalEntityId }, updatedData, currentContext);
                    yield this.historyService.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: legalEntityId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LEGAL_ENTITY,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        action_date: new Date(),
                        comments: 'Legal entity detail updated.',
                        additional_info: {
                            old: {
                                name: legalEntityDetail.name,
                                otherDetails: legalEntityDetail.otherDetails
                            },
                            new: {
                                name,
                                otherDetails
                            }
                        }
                    });
                    return { message: 'Legal entity updated successfully.' };
                }));
            }
            else {
                const legalEntityData = {
                    countryId: countryDetail.id,
                    name,
                    otherDetails
                };
                return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    const newEntry = yield this.legalEntityRepository.setupNewLegalEntity(legalEntityData, currentContext);
                    yield this.historyService.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: newEntry.id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LEGAL_ENTITY,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                        action_date: new Date(),
                        comments: 'New legal entity added.',
                        additional_info: {
                            legalEntityData
                        }
                    });
                    return { message: 'New legal entity setup successfully.', data: newEntry };
                }));
            }
        });
    }
    deleteLegalEntity(legalEntityId, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const legalEntityDetail = yield this.legalEntityRepository.getLegalEntityDetailWithCountryByCondition({
                id: legalEntityId
            });
            if (!legalEntityDetail) {
                throw new common_1.HttpException('Legal entity does\'t exist.', common_1.HttpStatus.NOT_FOUND);
            }
            const entityId = (_a = legalEntityDetail === null || legalEntityDetail === void 0 ? void 0 : legalEntityDetail.country) === null || _a === void 0 ? void 0 : _a.entityId;
            if (!entityId) {
                throw new common_1.HttpException('Country detail not found for this legal entity.', common_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.sharedPermissionService.checkGlobalPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE], currentContext.user, entityId);
            if (!hasUserPermission) {
                throw new common_1.HttpException('You do not have permission to delete this legal entity.', common_1.HttpStatus.FORBIDDEN);
            }
            const isLegalEntityInUse = yield this.locationRepository.isLegalEntityAddedInLocation(legalEntityId);
            if (isLegalEntityInUse) {
                throw new common_1.HttpException('Legal entity is added in locations. Please remove it from locations before deleting.', common_1.HttpStatus.FORBIDDEN);
            }
            yield this.legalEntityRepository.deleteById(legalEntityId, currentContext);
            yield this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: legalEntityId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.LEGAL_ENTITY,
                action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                action_date: new Date(),
                comments: 'Legal entity has been deleted.',
            });
            return { message: 'Legal entity has been deleted successfully.' };
        });
    }
    getLegalEntityList(countryId, searchTerm) {
        return __awaiter(this, void 0, void 0, function* () {
            const entityList = yield this.legalEntityRepository.getEntityListBySearchTerm(countryId, searchTerm);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.LegalEntityDropdownResponseDto, entityList);
        });
    }
};
LegalEntityService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.CountryRepository,
        repositories_1.LegalEntityRepository,
        repositories_1.LocationRepository,
        services_1.SharedPermissionService,
        clients_1.HistoryApiClient,
        helpers_1.DatabaseHelper])
], LegalEntityService);
exports.LegalEntityService = LegalEntityService;
//# sourceMappingURL=legal-entity.service.js.map