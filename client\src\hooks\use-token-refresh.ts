import { useEffect, useRef, useState } from 'react';
import { useMsal, useAccount } from '@azure/msal-react';
import { TokenRefreshService, TokenRefreshConfig } from '@/core/services/token-refresh.service';

interface UseTokenRefreshOptions {
  scopes: string[];
  renewalOffsetMinutes?: number;
  maxRetryAttempts?: number;
  retryDelayMs?: number;
  autoStart?: boolean;
}

interface TokenRefreshState {
  isRefreshing: boolean;
  lastRefreshTime: Date | null;
  timeUntilExpiry: number | null;
  refreshCount: number;
}

export function useTokenRefresh(options: UseTokenRefreshOptions) {
  const { instance, accounts } = useMsal();
  const account = useAccount(accounts[0]);
  const tokenRefreshServiceRef = useRef<TokenRefreshService | null>(null);
  
  const [state, setState] = useState<TokenRefreshState>({
    isRefreshing: false,
    lastRefreshTime: null,
    timeUntilExpiry: null,
    refreshCount: 0,
  });

  const {
    scopes,
    renewalOffsetMinutes = 5,
    maxRetryAttempts = 3,
    retryDelayMs = 1000,
    autoStart = true,
  } = options;

  // Initialize token refresh service
  useEffect(() => {
    if (!account) return;

    const config: TokenRefreshConfig = {
      scopes,
      renewalOffsetMinutes,
      maxRetryAttempts,
      retryDelayMs,
    };

    tokenRefreshServiceRef.current = new TokenRefreshService(instance, config);

    if (autoStart) {
      tokenRefreshServiceRef.current.startTokenRefreshMonitoring(account);
    }

    return () => {
      tokenRefreshServiceRef.current?.destroy();
    };
  }, [account, scopes, renewalOffsetMinutes, maxRetryAttempts, retryDelayMs, autoStart, instance]);

  // Update state periodically
  useEffect(() => {
    if (!account || !tokenRefreshServiceRef.current) return;

    const updateState = () => {
      const service = tokenRefreshServiceRef.current;
      if (!service) return;

      setState(prevState => ({
        ...prevState,
        isRefreshing: service.isCurrentlyRefreshing,
        timeUntilExpiry: service.getTimeUntilExpiry(account),
      }));
    };

    // Update immediately
    updateState();

    // Update every 30 seconds
    const interval = setInterval(updateState, 30000);

    return () => clearInterval(interval);
  }, [account]);

  // Manual refresh function
  const refreshToken = async (): Promise<boolean> => {
    if (!account || !tokenRefreshServiceRef.current) {
      console.warn('Cannot refresh token: no account or service available');
      return false;
    }

    setState(prev => ({ ...prev, isRefreshing: true }));

    try {
      const success = await tokenRefreshServiceRef.current.forceRefresh(account);
      
      setState(prev => ({
        ...prev,
        isRefreshing: false,
        lastRefreshTime: success ? new Date() : prev.lastRefreshTime,
        refreshCount: success ? prev.refreshCount + 1 : prev.refreshCount,
      }));

      return success;
    } catch (error) {
      console.error('Manual token refresh failed:', error);
      setState(prev => ({ ...prev, isRefreshing: false }));
      return false;
    }
  };

  // Start monitoring
  const startMonitoring = () => {
    if (account && tokenRefreshServiceRef.current) {
      tokenRefreshServiceRef.current.startTokenRefreshMonitoring(account);
    }
  };

  // Stop monitoring
  const stopMonitoring = () => {
    if (tokenRefreshServiceRef.current) {
      tokenRefreshServiceRef.current.stopTokenRefreshMonitoring();
    }
  };

  // Get formatted time until expiry
  const getFormattedTimeUntilExpiry = (): string => {
    if (state.timeUntilExpiry === null) return 'Unknown';
    
    const minutes = Math.floor(state.timeUntilExpiry / 60);
    const seconds = Math.floor(state.timeUntilExpiry % 60);
    
    if (minutes > 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}h ${remainingMinutes}m`;
    }
    
    return `${minutes}m ${seconds}s`;
  };

  // Check if token is close to expiry
  const isTokenNearExpiry = (): boolean => {
    if (state.timeUntilExpiry === null) return false;
    return state.timeUntilExpiry <= (renewalOffsetMinutes * 60);
  };

  return {
    // State
    isRefreshing: state.isRefreshing,
    lastRefreshTime: state.lastRefreshTime,
    timeUntilExpiry: state.timeUntilExpiry,
    refreshCount: state.refreshCount,
    
    // Actions
    refreshToken,
    startMonitoring,
    stopMonitoring,
    
    // Computed values
    formattedTimeUntilExpiry: getFormattedTimeUntilExpiry(),
    isTokenNearExpiry: isTokenNearExpiry(),
    
    // Service reference (for advanced usage)
    service: tokenRefreshServiceRef.current,
  };
}
