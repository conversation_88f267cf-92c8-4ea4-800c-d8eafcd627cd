{"version": 3, "sources": ["../../@mui/utils/esm/resolveProps/resolveProps.js", "../../@mui/system/esm/useThemeProps/getThemeProps.js", "../../@mui/system/esm/useThemeProps/useThemeProps.js", "../../@mui/system/esm/useMediaQuery/useMediaQuery.js", "../../@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js"], "sourcesContent": ["/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props) {\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName]);\n            }\n          }\n        }\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}", "import resolveProps from '@mui/utils/resolveProps';\nexport default function getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return resolveProps(theme.components[name].defaultProps, props);\n}", "'use client';\n\nimport getThemeProps from \"./getThemeProps.js\";\nimport useTheme from \"../useTheme/index.js\";\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  return getThemeProps({\n    theme,\n    name,\n    props\n  });\n}", "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getThemeProps } from \"../useThemeProps/index.js\";\nimport useTheme from \"../useThemeWithoutDefault/index.js\";\n// TODO React 17: Remove `useMediaQueryOld` once React 17 support is removed\nfunction useMediaQueryOld(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const [match, setMatch] = React.useState(() => {\n    if (noSsr && matchMedia) {\n      return matchMedia(query).matches;\n    }\n    if (ssrMatchMedia) {\n      return ssrMatchMedia(query).matches;\n    }\n\n    // Once the component is mounted, we rely on the\n    // event listeners to return the correct matches value.\n    return defaultMatches;\n  });\n  useEnhancedEffect(() => {\n    if (!matchMedia) {\n      return undefined;\n    }\n    const queryList = matchMedia(query);\n    const updateMatch = () => {\n      setMatch(queryList.matches);\n    };\n    updateMatch();\n    queryList.addEventListener('change', updateMatch);\n    return () => {\n      queryList.removeEventListener('change', updateMatch);\n    };\n  }, [query, matchMedia]);\n  return match;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseSyncExternalStore = safeReact.useSyncExternalStore;\nfunction useMediaQueryNew(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const getDefaultSnapshot = React.useCallback(() => defaultMatches, [defaultMatches]);\n  const getServerSnapshot = React.useMemo(() => {\n    if (noSsr && matchMedia) {\n      return () => matchMedia(query).matches;\n    }\n    if (ssrMatchMedia !== null) {\n      const {\n        matches\n      } = ssrMatchMedia(query);\n      return () => matches;\n    }\n    return getDefaultSnapshot;\n  }, [getDefaultSnapshot, query, ssrMatchMedia, noSsr, matchMedia]);\n  const [getSnapshot, subscribe] = React.useMemo(() => {\n    if (matchMedia === null) {\n      return [getDefaultSnapshot, () => () => {}];\n    }\n    const mediaQueryList = matchMedia(query);\n    return [() => mediaQueryList.matches, notify => {\n      mediaQueryList.addEventListener('change', notify);\n      return () => {\n        mediaQueryList.removeEventListener('change', notify);\n      };\n    }];\n  }, [getDefaultSnapshot, matchMedia, query]);\n  const match = maybeReactUseSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n  return match;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createUseMediaQuery(params = {}) {\n  const {\n    themeId\n  } = params;\n  return function useMediaQuery(queryInput, options = {}) {\n    let theme = useTheme();\n    if (theme && themeId) {\n      theme = theme[themeId] || theme;\n    }\n    // Wait for jsdom to support the match media feature.\n    // All the browsers MUI support have this built-in.\n    // This defensive check is here for simplicity.\n    // Most of the time, the match media logic isn't central to people tests.\n    const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';\n    const {\n      defaultMatches = false,\n      matchMedia = supportMatchMedia ? window.matchMedia : null,\n      ssrMatchMedia = null,\n      noSsr = false\n    } = getThemeProps({\n      name: 'MuiUseMediaQuery',\n      props: options,\n      theme\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof queryInput === 'function' && theme === null) {\n        console.error(['MUI: The `query` argument provided is invalid.', 'You are providing a function without a theme in the context.', 'One of the parent elements needs to use a ThemeProvider.'].join('\\n'));\n      }\n    }\n    let query = typeof queryInput === 'function' ? queryInput(theme) : queryInput;\n    query = query.replace(/^@media( ?)/m, '');\n    const useMediaQueryImplementation = maybeReactUseSyncExternalStore !== undefined ? useMediaQueryNew : useMediaQueryOld;\n    const match = useMediaQueryImplementation(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr);\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      React.useDebugValue({\n        query,\n        match\n      });\n    }\n    return match;\n  };\n}\nconst useMediaQuery = unstable_createUseMediaQuery();\nexport default useMediaQuery;", "'use client';\n\nimport * as React from 'react';\n\n/**\n * A version of `React.useLayoutEffect` that does not show a warning when server-side rendering.\n * This is useful for effects that are only needed for client-side rendering but not for SSR.\n *\n * Before you use this hook, make sure to read https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n * and confirm it doesn't apply to your use-case.\n */\nconst useEnhancedEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nexport default useEnhancedEffect;"], "mappings": ";;;;;;;;;;;;AAMe,SAAR,aAA8B,cAAc,OAAO;AACxD,QAAM,SAAS;AAAA,IACb,GAAG;AAAA,EACL;AACA,aAAW,OAAO,cAAc;AAC9B,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,GAAG,GAAG;AAC3D,YAAM,WAAW;AACjB,UAAI,aAAa,gBAAgB,aAAa,SAAS;AACrD,eAAO,QAAQ,IAAI;AAAA,UACjB,GAAG,aAAa,QAAQ;AAAA,UACxB,GAAG,OAAO,QAAQ;AAAA,QACpB;AAAA,MACF,WAAW,aAAa,qBAAqB,aAAa,aAAa;AACrE,cAAM,mBAAmB,aAAa,QAAQ;AAC9C,cAAM,YAAY,MAAM,QAAQ;AAChC,YAAI,CAAC,WAAW;AACd,iBAAO,QAAQ,IAAI,oBAAoB,CAAC;AAAA,QAC1C,WAAW,CAAC,kBAAkB;AAC5B,iBAAO,QAAQ,IAAI;AAAA,QACrB,OAAO;AACL,iBAAO,QAAQ,IAAI;AAAA,YACjB,GAAG;AAAA,UACL;AACA,qBAAW,WAAW,kBAAkB;AACtC,gBAAI,OAAO,UAAU,eAAe,KAAK,kBAAkB,OAAO,GAAG;AACnE,oBAAM,eAAe;AACrB,qBAAO,QAAQ,EAAE,YAAY,IAAI,aAAa,iBAAiB,YAAY,GAAG,UAAU,YAAY,CAAC;AAAA,YACvG;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAW,OAAO,QAAQ,MAAM,QAAW;AACzC,eAAO,QAAQ,IAAI,aAAa,QAAQ;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACzCe,SAAR,cAA+B,QAAQ;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,SAAS,CAAC,MAAM,cAAc,CAAC,MAAM,WAAW,IAAI,KAAK,CAAC,MAAM,WAAW,IAAI,EAAE,cAAc;AAClG,WAAO;AAAA,EACT;AACA,SAAO,aAAa,MAAM,WAAW,IAAI,EAAE,cAAc,KAAK;AAChE;;;ACPe,SAAR,cAA+B;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI,QAAQ,iBAAS,YAAY;AACjC,MAAI,SAAS;AACX,YAAQ,MAAM,OAAO,KAAK;AAAA,EAC5B;AACA,SAAO,cAAc;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACjBA,IAAAA,SAAuB;;;ACAvB,YAAuB;AASvB,IAAM,oBAAoB,OAAO,WAAW,cAAoB,wBAAwB;AACxF,IAAO,4BAAQ;;;ADLf,SAAS,iBAAiB,OAAO,gBAAgB,YAAY,eAAe,OAAO;AACjF,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAS,MAAM;AAC7C,QAAI,SAAS,YAAY;AACvB,aAAO,WAAW,KAAK,EAAE;AAAA,IAC3B;AACA,QAAI,eAAe;AACjB,aAAO,cAAc,KAAK,EAAE;AAAA,IAC9B;AAIA,WAAO;AAAA,EACT,CAAC;AACD,4BAAkB,MAAM;AACtB,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,UAAM,YAAY,WAAW,KAAK;AAClC,UAAM,cAAc,MAAM;AACxB,eAAS,UAAU,OAAO;AAAA,IAC5B;AACA,gBAAY;AACZ,cAAU,iBAAiB,UAAU,WAAW;AAChD,WAAO,MAAM;AACX,gBAAU,oBAAoB,UAAU,WAAW;AAAA,IACrD;AAAA,EACF,GAAG,CAAC,OAAO,UAAU,CAAC;AACtB,SAAO;AACT;AAGA,IAAM,YAAY;AAAA,EAChB,GAAGC;AACL;AACA,IAAM,iCAAiC,UAAU;AACjD,SAAS,iBAAiB,OAAO,gBAAgB,YAAY,eAAe,OAAO;AACjF,QAAM,qBAA2B,mBAAY,MAAM,gBAAgB,CAAC,cAAc,CAAC;AACnF,QAAM,oBAA0B,eAAQ,MAAM;AAC5C,QAAI,SAAS,YAAY;AACvB,aAAO,MAAM,WAAW,KAAK,EAAE;AAAA,IACjC;AACA,QAAI,kBAAkB,MAAM;AAC1B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,cAAc,KAAK;AACvB,aAAO,MAAM;AAAA,IACf;AACA,WAAO;AAAA,EACT,GAAG,CAAC,oBAAoB,OAAO,eAAe,OAAO,UAAU,CAAC;AAChE,QAAM,CAAC,aAAa,SAAS,IAAU,eAAQ,MAAM;AACnD,QAAI,eAAe,MAAM;AACvB,aAAO,CAAC,oBAAoB,MAAM,MAAM;AAAA,MAAC,CAAC;AAAA,IAC5C;AACA,UAAM,iBAAiB,WAAW,KAAK;AACvC,WAAO,CAAC,MAAM,eAAe,SAAS,YAAU;AAC9C,qBAAe,iBAAiB,UAAU,MAAM;AAChD,aAAO,MAAM;AACX,uBAAe,oBAAoB,UAAU,MAAM;AAAA,MACrD;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,oBAAoB,YAAY,KAAK,CAAC;AAC1C,QAAM,QAAQ,+BAA+B,WAAW,aAAa,iBAAiB;AACtF,SAAO;AACT;AAGO,SAAS,6BAA6B,SAAS,CAAC,GAAG;AACxD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,SAASC,eAAc,YAAY,UAAU,CAAC,GAAG;AACtD,QAAI,QAAQ,+BAAS;AACrB,QAAI,SAAS,SAAS;AACpB,cAAQ,MAAM,OAAO,KAAK;AAAA,IAC5B;AAKA,UAAM,oBAAoB,OAAO,WAAW,eAAe,OAAO,OAAO,eAAe;AACxF,UAAM;AAAA,MACJ,iBAAiB;AAAA,MACjB,aAAa,oBAAoB,OAAO,aAAa;AAAA,MACrD,gBAAgB;AAAA,MAChB,QAAQ;AAAA,IACV,IAAI,cAAc;AAAA,MAChB,MAAM;AAAA,MACN,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AACD,QAAI,MAAuC;AACzC,UAAI,OAAO,eAAe,cAAc,UAAU,MAAM;AACtD,gBAAQ,MAAM,CAAC,kDAAkD,gEAAgE,0DAA0D,EAAE,KAAK,IAAI,CAAC;AAAA,MACzM;AAAA,IACF;AACA,QAAI,QAAQ,OAAO,eAAe,aAAa,WAAW,KAAK,IAAI;AACnE,YAAQ,MAAM,QAAQ,gBAAgB,EAAE;AACxC,UAAM,8BAA8B,mCAAmC,SAAY,mBAAmB;AACtG,UAAM,QAAQ,4BAA4B,OAAO,gBAAgB,YAAY,eAAe,KAAK;AACjG,QAAI,MAAuC;AAEzC,MAAM,qBAAc;AAAA,QAClB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,gBAAgB,6BAA6B;AACnD,IAAO,wBAAQ;", "names": ["React", "React", "useMediaQuery"]}