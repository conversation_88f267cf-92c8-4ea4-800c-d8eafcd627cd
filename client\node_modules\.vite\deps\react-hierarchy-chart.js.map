{"version": 3, "sources": ["../../react-hierarchy-chart/node_modules/style-inject/dist/style-inject.es.js", "../../react-hierarchy-chart/src/components/HierarchyNode/HierarchyNode.tsx", "../../react-hierarchy-chart/src/components/ReactHierarchy/ReactHierarchy.tsx"], "sourcesContent": ["function styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nexport default styleInject;\n", null, null], "mappings": ";;;;;;;;;AAAA,SAAS,YAAY,KAAK,KAAK;AAC7B,MAAK,QAAQ,OAAS,OAAM,CAAA;AAC5B,MAAI,WAAW,IAAI;AAEnB,MAAI,CAAC,OAAO,OAAO,aAAa,aAAa;AAAE;EAAO;AAEtD,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,MAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,OAAO;AAEb,MAAI,aAAa,OAAO;AACtB,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa,OAAO,KAAK,UAAU;IAC9C,OAAW;AACL,WAAK,YAAY,KAAK;IAC5B;EACA,OAAS;AACL,SAAK,YAAY,KAAK;EAC1B;AAEE,MAAI,MAAM,YAAY;AACpB,UAAM,WAAW,UAAU;EAC/B,OAAS;AACL,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;EAClD;AACA;;;ACVO,IAAM,gBAAgB,SAAC,IAA8D;;MAA5D,OAAI,GAAA,MAAE,YAAS,GAAA,WAAE,aAAU,GAAA,YAAE,YAAS,GAAA;AAClE,SAAO,aAAAA,QAAK;IAAA;IAAA,EAAA,WAAW,iEAAA,OAAiE,SAAS,EAAE;IAC/F,aAAAA,QAAA;MAAA;MAAA,EAAK,WAAW,6CAA6C,OAAA,SAAS,EAAE;MAEhE,aAAa,aAAAA,QAAK,cAAA,OAAA,EAAA,WAAW,uBAAuB,OAAA,SAAS,EAAE,CAAQ;MAE3E,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAW,iCAAiC,OAAA,KAAK,QAAQ,EAAE,GAC3D,WAAW,IAAI,CAAC;QAGjB,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,UAAS,aAAAA,QAAK,cAAA,OAAA,EAAA,WAAW,uBAAA,OAAuB,SAAS,EAAE,CAAA,IAAY,aAAAA,QAAA,cAAA,aAAAA,QAAA,UAAA,IAAA;IAAK;MAI9F,KAAA,KAAK,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,WACb,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAW,qDAAqD,OAAA,SAAS,EAAE,GAExE,KAAK,OAAO,IAAI,SAAC,OAAO,OAAK;;AACZ,SAACC,MAAA,KAAK,YAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAA,WAAU,KAAK,MAAM;AAChD,aACI,aAAAD,QAAK;QAAA;QAAA,EAAA,WAAW,yCAAyC,OAAA,SAAS,GAC9D,KAAK,qBAAA,OAAqB,MAAM,OAAO,KAAK,GAAG,EAAE;QACjD,aAAAA,QAAA;UAAA;UAAA,EAAK,WAAW,mCAAmC,OAAA,SAAS,EAAE;UAC1D,aAAAA,QAAK,cAAA,OAAA,EAAA,WAAU,QAAO,CAAO;UAC7B,aAAAA,QAAK,cAAA,OAAA,EAAA,WAAU,SAAQ,CAAO;UAC9B,aAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,QAAO,CAAA;QAAO;QAEjC,aAAAA,QAAC,cAAA,eAAa,EAAC,MAAM,OACjB,WACA,YACA,WAAW,KAAI,CACjB;MAAA;KAEb,CAAC;EAEJ;AAGlB;;;AC1CM,IAAA,iBAAiB,SAAC,IAAqD;AAAnD,MAAA,QAAK,GAAA,OAAE,YAAS,GAAA,WAAE,aAAU,GAAA;AAClD,SACI,aAAAA,QAAK,cAAA,OAAA,EAAA,WAAW,6BAA6B,KAAK,KAAK,IAAG,EAAE,GAEpD,MAAM,IAAI,SAAC,MAAK,OAAK;AAAK,WAAA,aAAAA,QAAK;MAAA;MAAA,EAAA,WAAW,wBAAwB,OAAA,SAAS,GAAK,KAAK,iBAAA,OAAiB,KAAK,OAAO,KAAK,GAAG,EAAE;MACxH,aAAAA,QAAC,cAAA,eAAa,EAAC,MAAY,WAAsB,WAAsB,CAAA;IAAG;EACxE,CAAA,CAAC;AAIvB;", "names": ["React", "_a"]}