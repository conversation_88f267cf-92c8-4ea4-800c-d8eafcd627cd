import { DatabaseHelper } from 'src/shared/helpers';
import { MessageResponseDto } from 'src/shared/dtos';
import { CurrentContext } from 'src/shared/types';
import { ContactListFilterRequestDto, ContactListingResponseDto, DeleteHierarchyUserRequestDto, NewHierarchyUserRequestDto, PatchContactTitleRequestDto } from '../dtos';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { CoreSolutionRepository } from 'src/metadata/repositories';
import { ContactDetailRepository } from '../repositories';
import { CountryRepository } from 'src/location/repositories';
export declare class ContactDetailService {
    private readonly adminApiServie;
    private readonly coreSolutionRepository;
    private readonly contactDetailRepository;
    private readonly countryRepository;
    private readonly historyService;
    private readonly databaseHelper;
    constructor(adminApiServie: AdminApiClient, coreSolutionRepository: CoreSolutionRepository, contactDetailRepository: ContactDetailRepository, countryRepository: CountryRepository, historyService: HistoryApiClient, databaseHelper: DatabaseHelper);
    addHierarchyContact(newHierarchyUserRequestDto: NewHierarchyUserRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteUserFromContact(deleteHierarchyUserRequestDto: DeleteHierarchyUserRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    patchContactDetail(patchContactTitleRequestDto: PatchContactTitleRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getContactUserList(filterData: ContactListFilterRequestDto): Promise<ContactListingResponseDto[]>;
}
