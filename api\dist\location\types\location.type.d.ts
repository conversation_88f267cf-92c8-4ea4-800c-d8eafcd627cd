import { GOOGLE_LISTING_STATUS } from "src/shared/enums";
import { UserDetail } from "src/shared/interfaces";
export type FFLocationIds = {
    locationIdType: {
        id: number;
        code: string;
        title: string;
    };
    locationIdValue: string;
};
export type Brands = {
    id: number;
    code: string;
    title: string;
};
export type GoogleListing = {
    isGoogleListing: boolean;
    status?: GOOGLE_LISTING_STATUS;
    link?: string;
};
type LocationLifeCycleManagement = {
    code: string;
    title: string;
    description: string;
    rate: number;
};
type AdditionalRegistrationNumber = {
    name: string;
    number: string;
};
export type CountryDetail = {
    entityId: number;
    entityCode: string;
    entityTitle: string;
};
export type LocationOtherDetails = {
    basicInformation: {
        introduction?: string;
        leaseExpirationDate: Date;
        address: string;
    };
    competencyDetails?: {
        sustainabilityExperts?: UserDetail[];
    };
    agentDetail?: {
        website?: string;
        agreement?: string;
        agreementExpirationDate?: Date;
        vendorCode: string;
    };
    contractDetail?: {
        type?: string;
        locationSizeInSqft?: string;
        locationSizeInSqmt?: string;
        dpwHeadcount?: number;
        tempHeadcount?: number;
        totalHeadcount?: number;
        shiftPattern?: string;
    };
    locationLifecycleManagement?: {
        tiaB: LocationLifeCycleManagement;
        systemDetails: string;
        tiabSince: Date;
        activationStage: LocationLifeCycleManagement;
        minimumFfwFteInPlace: LocationLifeCycleManagement;
        tomFin: LocationLifeCycleManagement;
        tomOps: LocationLifeCycleManagement;
        additionalRegistrationNumber?: AdditionalRegistrationNumber[];
    };
    additionalDetails?: {
        customer?: string;
        usefulLinks?: string[];
    };
};
export {};
