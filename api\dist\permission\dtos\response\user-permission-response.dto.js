"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedUserPermissionResponseDto = exports.UserPermissionResponseDto = exports.LocationInfoDto = exports.PermissionResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const access_control_config_response_dto_1 = require("./access-control-config-response.dto");
class PermissionResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PermissionResponseDto.prototype, "permissionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], PermissionResponseDto.prototype, "locations", void 0);
exports.PermissionResponseDto = PermissionResponseDto;
class LocationInfoDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], LocationInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocationInfoDto.prototype, "locationName", void 0);
exports.LocationInfoDto = LocationInfoDto;
class UserPermissionResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserPermissionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserPermissionResponseDto.prototype, "loginId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], UserPermissionResponseDto.prototype, "manageLocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], UserPermissionResponseDto.prototype, "manageCapability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: access_control_config_response_dto_1.BasicAccessControlConfigResponseDto }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => access_control_config_response_dto_1.BasicAccessControlConfigResponseDto),
    __metadata("design:type", access_control_config_response_dto_1.BasicAccessControlConfigResponseDto)
], UserPermissionResponseDto.prototype, "accessControlConfig", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: LocationInfoDto }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => LocationInfoDto),
    __metadata("design:type", LocationInfoDto)
], UserPermissionResponseDto.prototype, "location", void 0);
exports.UserPermissionResponseDto = UserPermissionResponseDto;
class PaginatedUserPermissionResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PaginatedUserPermissionResponseDto.prototype, "pageTotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PaginatedUserPermissionResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ isArray: true, type: UserPermissionResponseDto }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => UserPermissionResponseDto),
    __metadata("design:type", Array)
], PaginatedUserPermissionResponseDto.prototype, "records", void 0);
exports.PaginatedUserPermissionResponseDto = PaginatedUserPermissionResponseDto;
//# sourceMappingURL=user-permission-response.dto.js.map