import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/Accordion/AccordionContext.js
var React = __toESM(require_react());
var AccordionContext = React.createContext({});
if (true) {
  AccordionContext.displayName = "AccordionContext";
}
var AccordionContext_default = AccordionContext;

export {
  AccordionContext_default
};
//# sourceMappingURL=chunk-M66V7E6W.js.map
