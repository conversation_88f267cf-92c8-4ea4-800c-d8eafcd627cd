"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InternalApiProviders = void 0;
const config_service_1 = require("../../config/config.service");
const constants_1 = require("../../shared/constants");
const http_service_1 = require("../../shared/services/http.service");
exports.InternalApiProviders = [
    {
        provide: constants_1.INTERNAL_API.ADMIN_API_PROVIDER,
        useFactory: (configService) => {
            const { microservices } = configService.getAppConfig();
            const { adminApi } = microservices;
            const { url, tennantId, appId } = adminApi;
            return new http_service_1.HttpService().withBaseUrl(url).withHeaders({
                tennantId: tennantId,
                applicationid: appId,
            });
        },
        inject: [config_service_1.ConfigService],
    },
    {
        provide: constants_1.INTERNAL_API.REQUEST_API_PROVIDER,
        useFactory: (configService) => {
            const { microservices } = configService.getAppConfig();
            const { requestApi } = microservices;
            const { url, tennantId, appId } = requestApi;
            return new http_service_1.HttpService().withBaseUrl(url).withHeaders({
                tennantId: tennantId,
                applicationid: appId,
            });
        },
        inject: [config_service_1.ConfigService],
    },
    {
        provide: constants_1.INTERNAL_API.NOTIFICATION_API_PROVIDER,
        useFactory: (configService) => {
            const { microservices } = configService.getAppConfig();
            const { notificationApi } = microservices;
            const { url, tennantId, appId } = notificationApi;
            return new http_service_1.HttpService().withBaseUrl(url).withHeaders({
                tennantId: tennantId,
                applicationid: appId,
            });
        },
        inject: [config_service_1.ConfigService],
    },
];
//# sourceMappingURL=internal-api.provider.js.map