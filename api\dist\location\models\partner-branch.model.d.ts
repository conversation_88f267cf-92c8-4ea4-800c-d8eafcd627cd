import { BaseModel } from 'src/shared/models';
import { Location } from './location.model';
import { PARTNER_BRANCH_STATUS_TYPE } from 'src/shared/enums';
export declare class PartnerBranch extends BaseModel<PartnerBranch> {
    locationId: number | null;
    location: Location;
    partnerLocationId: number | null;
    partnerLocation: Location;
    relationship: string;
    note: string;
    status: PARTNER_BRANCH_STATUS_TYPE;
}
