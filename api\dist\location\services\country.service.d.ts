import { CountryWithEntityResponseDto, NewCountrySetupRequestDto } from '../dtos';
import { CurrentContext } from 'src/shared/types';
import { CountryRepository } from '../repositories';
import { MessageResponseDto } from 'src/shared/dtos';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { DatabaseHelper } from 'src/shared/helpers';
import { ContactDetailRepository } from 'src/contact-details/repositories';
export declare class CountryService {
    private readonly countryRepository;
    private readonly adminApiClient;
    private readonly databaseHelper;
    private readonly contactDetailRepository;
    private readonly historyService;
    constructor(countryRepository: CountryRepository, adminApiClient: AdminApiClient, databaseHelper: DatabaseHelper, contactDetailRepository: ContactDetailRepository, historyService: HistoryApiClient);
    upsertCountry(newCountrySetupRequestDto: NewCountrySetupRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getCountryDetailWithLegalEntity(entityId: number): Promise<CountryWithEntityResponseDto>;
    private createCountrySetup;
    private updateCountrySetup;
}
