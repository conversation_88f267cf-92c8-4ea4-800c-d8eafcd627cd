"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CapabilityModule = void 0;
const common_1 = require("@nestjs/common");
const controllers_1 = require("./controllers");
const clients_1 = require("../shared/clients");
const services_1 = require("../shared/services");
const repositories_1 = require("./repositories");
const helpers_1 = require("../shared/helpers");
const services_2 = require("./services");
const repositories_2 = require("../location/repositories");
const services_3 = require("../business-entity/services");
const repositories_3 = require("../permission/repositories");
const repositories_4 = require("../metadata/repositories");
const repositories_5 = require("../bingo-card/repositories");
const repositories = [
    repositories_1.CapabilityRepository,
    repositories_2.LocationRepository,
    repositories_1.LocationWiseCapabilityRepository,
    repositories_3.UserPermissionRepository,
    repositories_1.CapabilityLegRepository,
    repositories_1.CapabilityCategoryRepository,
    repositories_1.MasterCapabilityDropdownRepository,
    repositories_1.MasterEvidenceRepository,
    repositories_4.CommonDropdownRepository,
    repositories_5.BingoCardConfigRepository,
];
let CapabilityModule = class CapabilityModule {
};
CapabilityModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.CapabilityController, controllers_1.MasterCapabilityController],
        providers: [
            ...repositories,
            clients_1.AdminApiClient,
            services_1.SharedPermissionService,
            helpers_1.DatabaseHelper,
            services_2.CapabilityService,
            services_1.SharedPermissionService,
            helpers_1.DatabaseHelper,
            clients_1.AttachmentApiClient,
            services_1.SharedAttachmentService,
            services_3.BusinessEntityService,
            services_1.ExcelSheetService,
            services_2.MasterCapabilityService,
            clients_1.HistoryApiClient,
        ],
    })
], CapabilityModule);
exports.CapabilityModule = CapabilityModule;
//# sourceMappingURL=capability.module.js.map