"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportLocationWiseCapabilityFilterRequestDto = exports.CapabilityFilterRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const enums_1 = require("../../../shared/enums");
class CapabilityFilterRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: [Number], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "entityIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [Number], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "entryIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CapabilityFilterRequestDto.prototype, "searchTerm", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.CAPABILITY_TYPE_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.CAPABILITY_TYPE_ENUM, { each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "capabilityTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.CAPABILITY_LEVEL_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.CAPABILITY_LEVEL_ENUM, { each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "capabilityLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [Number], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "coreSolutionIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.CAPABILITY_STATUS_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.CAPABILITY_STATUS_ENUM, { each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "statuses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.PROVIDER_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.PROVIDER_ENUM, { each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "providers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CapabilityFilterRequestDto.prototype, "verticalCodes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CapabilityFilterRequestDto.prototype, "ownerLoginId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CapabilityFilterRequestDto.prototype, "hierarchyEntityId", void 0);
exports.CapabilityFilterRequestDto = CapabilityFilterRequestDto;
class ExportLocationWiseCapabilityFilterRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExportLocationWiseCapabilityFilterRequestDto.prototype, "searchTerm", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.CAPABILITY_TYPE_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.CAPABILITY_TYPE_ENUM, { each: true }),
    __metadata("design:type", Array)
], ExportLocationWiseCapabilityFilterRequestDto.prototype, "capabilityTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.CAPABILITY_LEVEL_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.CAPABILITY_LEVEL_ENUM, { each: true }),
    __metadata("design:type", Array)
], ExportLocationWiseCapabilityFilterRequestDto.prototype, "capabilityLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.CAPABILITY_STATUS_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.CAPABILITY_STATUS_ENUM, { each: true }),
    __metadata("design:type", Array)
], ExportLocationWiseCapabilityFilterRequestDto.prototype, "statuses", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [enums_1.PROVIDER_ENUM], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(enums_1.PROVIDER_ENUM, { each: true }),
    __metadata("design:type", Array)
], ExportLocationWiseCapabilityFilterRequestDto.prototype, "providers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], isArray: true }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ExportLocationWiseCapabilityFilterRequestDto.prototype, "verticalCodes", void 0);
exports.ExportLocationWiseCapabilityFilterRequestDto = ExportLocationWiseCapabilityFilterRequestDto;
//# sourceMappingURL=capability-filter-request.dto.js.map