{"version": 3, "sources": ["../../@mui/material/Portal/Portal.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport { exactProp, HTMLElementType, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useForkRef as useForkRef, unstable_setRef as setRef, unstable_getReactElementRef as getReactElementRef } from '@mui/utils';\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\n\n/**\n * Portals provide a first-class way to render children into a DOM node\n * that exists outside the DOM hierarchy of the parent component.\n *\n * Demos:\n *\n * - [Portal](https://v6.mui.com/material-ui/react-portal/)\n *\n * API:\n *\n * - [Portal API](https://v6.mui.com/material-ui/api/portal/)\n */\nconst Portal = /*#__PURE__*/React.forwardRef(function Portal(props, forwardedRef) {\n  const {\n    children,\n    container,\n    disablePortal = false\n  } = props;\n  const [mountNode, setMountNode] = React.useState(null);\n  const handleRef = useForkRef(/*#__PURE__*/React.isValidElement(children) ? getReactElementRef(children) : null, forwardedRef);\n  useEnhancedEffect(() => {\n    if (!disablePortal) {\n      setMountNode(getContainer(container) || document.body);\n    }\n  }, [container, disablePortal]);\n  useEnhancedEffect(() => {\n    if (mountNode && !disablePortal) {\n      setRef(forwardedRef, mountNode);\n      return () => {\n        setRef(forwardedRef, null);\n      };\n    }\n    return undefined;\n  }, [forwardedRef, mountNode, disablePortal]);\n  if (disablePortal) {\n    if (/*#__PURE__*/React.isValidElement(children)) {\n      const newProps = {\n        ref: handleRef\n      };\n      return /*#__PURE__*/React.cloneElement(children, newProps);\n    }\n    return children;\n  }\n  return mountNode ? /*#__PURE__*/ReactDOM.createPortal(children, mountNode) : mountNode;\n});\nprocess.env.NODE_ENV !== \"production\" ? Portal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The children to render into the `container`.\n   */\n  children: PropTypes.node,\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Portal['propTypes' + ''] = exactProp(Portal.propTypes);\n}\nexport default Portal;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,eAA0B;AAC1B,wBAAsB;AAEtB,SAAS,aAAa,WAAW;AAC/B,SAAO,OAAO,cAAc,aAAa,UAAU,IAAI;AACzD;AAcA,IAAM,SAA4B,iBAAW,SAASA,QAAO,OAAO,cAAc;AAChF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,EAClB,IAAI;AACJ,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,IAAI;AACrD,QAAM,YAAY,WAA8B,qBAAe,QAAQ,IAAI,mBAAmB,QAAQ,IAAI,MAAM,YAAY;AAC5H,4BAAkB,MAAM;AACtB,QAAI,CAAC,eAAe;AAClB,mBAAa,aAAa,SAAS,KAAK,SAAS,IAAI;AAAA,IACvD;AAAA,EACF,GAAG,CAAC,WAAW,aAAa,CAAC;AAC7B,4BAAkB,MAAM;AACtB,QAAI,aAAa,CAAC,eAAe;AAC/B,aAAO,cAAc,SAAS;AAC9B,aAAO,MAAM;AACX,eAAO,cAAc,IAAI;AAAA,MAC3B;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,cAAc,WAAW,aAAa,CAAC;AAC3C,MAAI,eAAe;AACjB,QAAuB,qBAAe,QAAQ,GAAG;AAC/C,YAAM,WAAW;AAAA,QACf,KAAK;AAAA,MACP;AACA,aAA0B,mBAAa,UAAU,QAAQ;AAAA,IAC3D;AACA,WAAO;AAAA,EACT;AACA,SAAO,YAAkC,sBAAa,UAAU,SAAS,IAAI;AAC/E,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWpB,WAAW,kBAAAA,QAAgD,UAAU,CAAC,iBAAiB,kBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtG,eAAe,kBAAAA,QAAU;AAC3B,IAAI;AACJ,IAAI,MAAuC;AAEzC,SAAO,WAAgB,IAAI,UAAU,OAAO,SAAS;AACvD;AACA,IAAO,iBAAQ;", "names": ["Portal", "PropTypes"]}