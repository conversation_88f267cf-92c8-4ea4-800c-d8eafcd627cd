import { BaseRepository } from 'src/shared/repositories';
import { Location } from '../models';
import { CurrentContext } from 'src/shared/types';
import { LocationFilterRequestDto } from '../dtos/request/location-filter-request.dto';
import { FilterRequestDto } from 'src/bingo-card/dtos';
export declare class LocationRepository extends BaseRepository<Location> {
    constructor();
    getAllLocations(searchTerm: string): Promise<Location[]>;
    getLocationNameById(id: number): Promise<Location | null>;
    getLocationById(id: number): Promise<Location | null>;
    getLocationDetailForUpdate(locationId: number): Promise<Location | null>;
    getCompleteLocationDetailById(id: number): Promise<Location | null>;
    isLocationExit(id: number): Promise<boolean>;
    getLocationDetailById(id: number): Promise<Location | null>;
    isLocationNameExit(entityId: number, locationName: string, id?: number): Promise<boolean>;
    createLocation(payload: any, currentContext: CurrentContext): Promise<Location>;
    updateLocationDetailById(locationId: any, data: any, currentContext: CurrentContext): Promise<number | null>;
    getLocationsByFilter(filter: LocationFilterRequestDto, page: number, limit: number, orderBy: string, orderDirection: string): Promise<{
        rows: Location[];
        count: number;
    }>;
    private getOrderByColumn;
    private getOrderByDirection;
    private buildWhereClause;
    deleteLocation(id: number, currentContext: CurrentContext): Promise<boolean>;
    isLegalEntityAddedInLocation(legalEntityId: number): Promise<boolean>;
    getLocationsToExport(filter: LocationFilterRequestDto): Promise<Location[]>;
    getLocationsByCondition(condition: any): Promise<Location[]>;
    getLocationsIdsByEntityIds(entityIds: number[]): Promise<Location[]>;
    getAllLocationsWithCapabilities(capabilityIds: number[], filter: FilterRequestDto): Promise<any>;
}
