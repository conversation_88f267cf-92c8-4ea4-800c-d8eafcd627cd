"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../permission/repositories");
const clients_1 = require("./clients");
const attachment_api_client_1 = require("./clients/attachment-api.client");
const services_1 = require("./services");
const validators_1 = require("./validators");
const repositories = [repositories_1.UserPermissionRepository];
let SharedModule = class SharedModule {
};
SharedModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [
            clients_1.AdminApiClient,
            services_1.EntityService,
            clients_1.MSGraphApiClient,
            clients_1.HistoryApiClient,
            services_1.SharedPermissionService,
            services_1.SharedAttachmentService,
            services_1.SharedNotificationService,
            validators_1.WorkflowYearValidator,
            clients_1.NotificationApiClient,
            attachment_api_client_1.AttachmentApiClient,
            ...repositories,
        ],
    })
], SharedModule);
exports.SharedModule = SharedModule;
//# sourceMappingURL=shared.module.js.map