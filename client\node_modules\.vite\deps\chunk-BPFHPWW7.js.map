{"version": 3, "sources": ["../../ol/asserts.js", "../../ol/array.js", "../../ol/events/EventType.js", "../../ol/functions.js", "../../ol/events/Event.js", "../../ol/events.js", "../../ol/util.js", "../../ol/ObjectEventType.js", "../../ol/Disposable.js", "../../ol/events/Target.js", "../../ol/Observable.js", "../../ol/Object.js"], "sourcesContent": ["/**\n * @module ol/asserts\n */\n\n/**\n * @param {*} assertion Assertion we expected to be truthy.\n * @param {string} errorMessage Error message.\n */\nexport function assert(assertion, errorMessage) {\n  if (!assertion) {\n    throw new Error(errorMessage);\n  }\n}\n", "/**\n * @module ol/array\n */\n\n/**\n * Performs a binary search on the provided sorted list and returns the index of the item if found. If it can't be found it'll return -1.\n * https://github.com/darkskyapp/binary-search\n *\n * @param {Array<*>} haystack Items to search through.\n * @param {*} needle The item to look for.\n * @param {Function} [comparator] Comparator function.\n * @return {number} The index of the item if found, -1 if not.\n */\nexport function binarySearch(haystack, needle, comparator) {\n  let mid, cmp;\n  comparator = comparator || ascending;\n  let low = 0;\n  let high = haystack.length;\n  let found = false;\n\n  while (low < high) {\n    /* Note that \"(low + high) >>> 1\" may overflow, and results in a typecast\n     * to double (which gives the wrong results). */\n    mid = low + ((high - low) >> 1);\n    cmp = +comparator(haystack[mid], needle);\n\n    if (cmp < 0.0) {\n      /* Too low. */\n      low = mid + 1;\n    } else {\n      /* Key found or too high */\n      high = mid;\n      found = !cmp;\n    }\n  }\n\n  /* Key not found. */\n  return found ? low : ~low;\n}\n\n/**\n * Compare function sorting arrays in ascending order.  Safe to use for numeric values.\n * @param {*} a The first object to be compared.\n * @param {*} b The second object to be compared.\n * @return {number} A negative number, zero, or a positive number as the first\n *     argument is less than, equal to, or greater than the second.\n */\nexport function ascending(a, b) {\n  return a > b ? 1 : a < b ? -1 : 0;\n}\n\n/**\n * Compare function sorting arrays in descending order.  Safe to use for numeric values.\n * @param {*} a The first object to be compared.\n * @param {*} b The second object to be compared.\n * @return {number} A negative number, zero, or a positive number as the first\n *     argument is greater than, equal to, or less than the second.\n */\nexport function descending(a, b) {\n  return a < b ? 1 : a > b ? -1 : 0;\n}\n\n/**\n * {@link module:ol/tilegrid/TileGrid~TileGrid#getZForResolution} can use a function\n * of this type to determine which nearest resolution to use.\n *\n * This function takes a `{number}` representing a value between two array entries,\n * a `{number}` representing the value of the nearest higher entry and\n * a `{number}` representing the value of the nearest lower entry\n * as arguments and returns a `{number}`. If a negative number or zero is returned\n * the lower value will be used, if a positive number is returned the higher value\n * will be used.\n * @typedef {function(number, number, number): number} NearestDirectionFunction\n * @api\n */\n\n/**\n * @param {Array<number>} arr Array in descending order.\n * @param {number} target Target.\n * @param {number|NearestDirectionFunction} direction\n *    0 means return the nearest,\n *    > 0 means return the largest nearest,\n *    < 0 means return the smallest nearest.\n * @return {number} Index.\n */\nexport function linearFindNearest(arr, target, direction) {\n  if (arr[0] <= target) {\n    return 0;\n  }\n\n  const n = arr.length;\n  if (target <= arr[n - 1]) {\n    return n - 1;\n  }\n\n  if (typeof direction === 'function') {\n    for (let i = 1; i < n; ++i) {\n      const candidate = arr[i];\n      if (candidate === target) {\n        return i;\n      }\n      if (candidate < target) {\n        if (direction(target, arr[i - 1], candidate) > 0) {\n          return i - 1;\n        }\n        return i;\n      }\n    }\n    return n - 1;\n  }\n\n  if (direction > 0) {\n    for (let i = 1; i < n; ++i) {\n      if (arr[i] < target) {\n        return i - 1;\n      }\n    }\n    return n - 1;\n  }\n\n  if (direction < 0) {\n    for (let i = 1; i < n; ++i) {\n      if (arr[i] <= target) {\n        return i;\n      }\n    }\n    return n - 1;\n  }\n\n  for (let i = 1; i < n; ++i) {\n    if (arr[i] == target) {\n      return i;\n    }\n    if (arr[i] < target) {\n      if (arr[i - 1] - target < target - arr[i]) {\n        return i - 1;\n      }\n      return i;\n    }\n  }\n  return n - 1;\n}\n\n/**\n * @param {Array<*>} arr Array.\n * @param {number} begin Begin index.\n * @param {number} end End index.\n */\nexport function reverseSubArray(arr, begin, end) {\n  while (begin < end) {\n    const tmp = arr[begin];\n    arr[begin] = arr[end];\n    arr[end] = tmp;\n    ++begin;\n    --end;\n  }\n}\n\n/**\n * @param {Array<VALUE>} arr The array to modify.\n * @param {!Array<VALUE>|VALUE} data The elements or arrays of elements to add to arr.\n * @template VALUE\n */\nexport function extend(arr, data) {\n  const extension = Array.isArray(data) ? data : [data];\n  const length = extension.length;\n  for (let i = 0; i < length; i++) {\n    arr[arr.length] = extension[i];\n  }\n}\n\n/**\n * @param {Array<VALUE>} arr The array to modify.\n * @param {VALUE} obj The element to remove.\n * @template VALUE\n * @return {boolean} If the element was removed.\n */\nexport function remove(arr, obj) {\n  const i = arr.indexOf(obj);\n  const found = i > -1;\n  if (found) {\n    arr.splice(i, 1);\n  }\n  return found;\n}\n\n/**\n * @param {Array<any>|Uint8ClampedArray} arr1 The first array to compare.\n * @param {Array<any>|Uint8ClampedArray} arr2 The second array to compare.\n * @return {boolean} Whether the two arrays are equal.\n */\nexport function equals(arr1, arr2) {\n  const len1 = arr1.length;\n  if (len1 !== arr2.length) {\n    return false;\n  }\n  for (let i = 0; i < len1; i++) {\n    if (arr1[i] !== arr2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Sort the passed array such that the relative order of equal elements is preserved.\n * See https://en.wikipedia.org/wiki/Sorting_algorithm#Stability for details.\n * @param {Array<*>} arr The array to sort (modifies original).\n * @param {!function(*, *): number} compareFnc Comparison function.\n * @api\n */\nexport function stableSort(arr, compareFnc) {\n  const length = arr.length;\n  const tmp = Array(arr.length);\n  let i;\n  for (i = 0; i < length; i++) {\n    tmp[i] = {index: i, value: arr[i]};\n  }\n  tmp.sort(function (a, b) {\n    return compareFnc(a.value, b.value) || a.index - b.index;\n  });\n  for (i = 0; i < arr.length; i++) {\n    arr[i] = tmp[i].value;\n  }\n}\n\n/**\n * @param {Array<*>} arr The array to test.\n * @param {Function} [func] Comparison function.\n * @param {boolean} [strict] Strictly sorted (default false).\n * @return {boolean} Return index.\n */\nexport function isSorted(arr, func, strict) {\n  const compare = func || ascending;\n  return arr.every(function (currentVal, index) {\n    if (index === 0) {\n      return true;\n    }\n    const res = compare(arr[index - 1], currentVal);\n    return !(res > 0 || (strict && res === 0));\n  });\n}\n", "/**\n * @module ol/events/EventType\n */\n\n/**\n * @enum {string}\n * @const\n */\nexport default {\n  /**\n   * Generic change event. Triggered when the revision counter is increased.\n   * @event module:ol/events/Event~BaseEvent#change\n   * @api\n   */\n  CHANGE: 'change',\n\n  /**\n   * Generic error event. Triggered when an error occurs.\n   * @event module:ol/events/Event~BaseEvent#error\n   * @api\n   */\n  ERROR: 'error',\n\n  BLUR: 'blur',\n  CLEAR: 'clear',\n  CONTEXTMENU: 'contextmenu',\n  CLICK: 'click',\n  DBLCLICK: 'dblclick',\n  DRAGENTER: 'dragenter',\n  DRAGOVER: 'dragover',\n  DROP: 'drop',\n  FOCUS: 'focus',\n  KEYDOWN: 'keydown',\n  KEYPRESS: 'keypress',\n  LOAD: 'load',\n  RESIZE: 'resize',\n  TOUCHMOVE: 'touchmove',\n  WHEEL: 'wheel',\n};\n", "/**\n * @module ol/functions\n */\n\nimport {equals as arrayEquals} from './array.js';\n\n/**\n * Always returns true.\n * @return {boolean} true.\n */\nexport function TRUE() {\n  return true;\n}\n\n/**\n * Always returns false.\n * @return {boolean} false.\n */\nexport function FALSE() {\n  return false;\n}\n\n/**\n * A reusable function, used e.g. as a default for callbacks.\n *\n * @return {void} Nothing.\n */\nexport function VOID() {}\n\n/**\n * Wrap a function in another function that remembers the last return.  If the\n * returned function is called twice in a row with the same arguments and the same\n * this object, it will return the value from the first call in the second call.\n *\n * @param {function(...any): ReturnType} fn The function to memoize.\n * @return {function(...any): ReturnType} The memoized function.\n * @template ReturnType\n */\nexport function memoizeOne(fn) {\n  /** @type {ReturnType} */\n  let lastResult;\n\n  /** @type {Array<any>|undefined} */\n  let lastArgs;\n\n  let lastThis;\n\n  /**\n   * @this {*} Only need to know if `this` changed, don't care what type\n   * @return {ReturnType} Memoized value\n   */\n  return function () {\n    const nextArgs = Array.prototype.slice.call(arguments);\n    if (!lastArgs || this !== lastThis || !arrayEquals(nextArgs, lastArgs)) {\n      lastThis = this;\n      lastArgs = nextArgs;\n      lastResult = fn.apply(this, arguments);\n    }\n    return lastResult;\n  };\n}\n\n/**\n * @template T\n * @param {function(): (T | Promise<T>)} getter A function that returns a value or a promise for a value.\n * @return {Promise<T>} A promise for the value.\n */\nexport function toPromise(getter) {\n  function promiseGetter() {\n    let value;\n    try {\n      value = getter();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n    if (value instanceof Promise) {\n      return value;\n    }\n    return Promise.resolve(value);\n  }\n  return promiseGetter();\n}\n", "/**\n * @module ol/events/Event\n */\n\n/**\n * @classdesc\n * Stripped down implementation of the W3C DOM Level 2 Event interface.\n * See https://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-interface.\n *\n * This implementation only provides `type` and `target` properties, and\n * `stopPropagation` and `preventDefault` methods. It is meant as base class\n * for higher level events defined in the library, and works with\n * {@link module:ol/events/Target~Target}.\n */\nclass BaseEvent {\n  /**\n   * @param {string} type Type.\n   */\n  constructor(type) {\n    /**\n     * @type {boolean}\n     */\n    this.propagationStopped;\n\n    /**\n     * @type {boolean}\n     */\n    this.defaultPrevented;\n\n    /**\n     * The event type.\n     * @type {string}\n     * @api\n     */\n    this.type = type;\n\n    /**\n     * The event target.\n     * @type {Object}\n     * @api\n     */\n    this.target = null;\n  }\n\n  /**\n   * Prevent default. This means that no emulated `click`, `singleclick` or `doubleclick` events\n   * will be fired.\n   * @api\n   */\n  preventDefault() {\n    this.defaultPrevented = true;\n  }\n\n  /**\n   * Stop event propagation.\n   * @api\n   */\n  stopPropagation() {\n    this.propagationStopped = true;\n  }\n}\n\n/**\n * @param {Event|import(\"./Event.js\").default} evt Event\n */\nexport function stopPropagation(evt) {\n  evt.stopPropagation();\n}\n\n/**\n * @param {Event|import(\"./Event.js\").default} evt Event\n */\nexport function preventDefault(evt) {\n  evt.preventDefault();\n}\n\nexport default BaseEvent;\n", "/**\n * @module ol/events\n */\nimport {clear} from './obj.js';\n\n/**\n * Key to use with {@link module:ol/Observable.unByKey}.\n * @typedef {Object} EventsKey\n * @property {ListenerFunction} listener Listener.\n * @property {import(\"./events/Target.js\").EventTargetLike} target Target.\n * @property {string} type Type.\n * @api\n */\n\n/**\n * Listener function. This function is called with an event object as argument.\n * When the function returns `false`, event propagation will stop.\n *\n * @typedef {function((Event|import(\"./events/Event.js\").default)): (void|boolean)} ListenerFunction\n * @api\n */\n\n/**\n * @typedef {Object} ListenerObject\n * @property {ListenerFunction} handleEvent HandleEvent listener function.\n */\n\n/**\n * @typedef {ListenerFunction|ListenerObject} Listener\n */\n\n/**\n * Registers an event listener on an event target. Inspired by\n * https://google.github.io/closure-library/api/source/closure/goog/events/events.js.src.html\n *\n * This function efficiently binds a `listener` to a `this` object, and returns\n * a key for use with {@link module:ol/events.unlistenByKey}.\n *\n * @param {import(\"./events/Target.js\").EventTargetLike} target Event target.\n * @param {string} type Event type.\n * @param {ListenerFunction} listener Listener.\n * @param {Object} [thisArg] Object referenced by the `this` keyword in the\n *     listener. Default is the `target`.\n * @param {boolean} [once] If true, add the listener as one-off listener.\n * @return {EventsKey} Unique key for the listener.\n */\nexport function listen(target, type, listener, thisArg, once) {\n  if (once) {\n    const originalListener = listener;\n    /**\n     * @param {Event|import('./events/Event.js').default} event The event\n     * @return {void|boolean} When the function returns `false`, event propagation will stop.\n     * @this {typeof target}\n     */\n    listener = function (event) {\n      target.removeEventListener(type, listener);\n      return originalListener.call(thisArg ?? this, event);\n    };\n  } else if (thisArg && thisArg !== target) {\n    listener = listener.bind(thisArg);\n  }\n  const eventsKey = {\n    target: target,\n    type: type,\n    listener: listener,\n  };\n  target.addEventListener(type, listener);\n  return eventsKey;\n}\n\n/**\n * Registers a one-off event listener on an event target. Inspired by\n * https://google.github.io/closure-library/api/source/closure/goog/events/events.js.src.html\n *\n * This function efficiently binds a `listener` as self-unregistering listener\n * to a `this` object, and returns a key for use with\n * {@link module:ol/events.unlistenByKey} in case the listener needs to be\n * unregistered before it is called.\n *\n * When {@link module:ol/events.listen} is called with the same arguments after this\n * function, the self-unregistering listener will be turned into a permanent\n * listener.\n *\n * @param {import(\"./events/Target.js\").EventTargetLike} target Event target.\n * @param {string} type Event type.\n * @param {ListenerFunction} listener Listener.\n * @param {Object} [thisArg] Object referenced by the `this` keyword in the\n *     listener. Default is the `target`.\n * @return {EventsKey} Key for unlistenByKey.\n */\nexport function listenOnce(target, type, listener, thisArg) {\n  return listen(target, type, listener, thisArg, true);\n}\n\n/**\n * Unregisters event listeners on an event target. Inspired by\n * https://google.github.io/closure-library/api/source/closure/goog/events/events.js.src.html\n *\n * The argument passed to this function is the key returned from\n * {@link module:ol/events.listen} or {@link module:ol/events.listenOnce}.\n *\n * @param {EventsKey} key The key.\n */\nexport function unlistenByKey(key) {\n  if (key && key.target) {\n    key.target.removeEventListener(key.type, key.listener);\n    clear(key);\n  }\n}\n", "/**\n * @module ol/util\n */\n\n/**\n * @return {never} Any return.\n */\nexport function abstract() {\n  throw new Error('Unimplemented abstract method.');\n}\n\n/**\n * Counter for getUid.\n * @type {number}\n * @private\n */\nlet uidCounter_ = 0;\n\n/**\n * Gets a unique ID for an object. This mutates the object so that further calls\n * with the same object as a parameter returns the same value. Unique IDs are generated\n * as a strictly increasing sequence. Adapted from goog.getUid.\n *\n * @param {Object} obj The object to get the unique ID for.\n * @return {string} The unique ID for the object.\n * @api\n */\nexport function getUid(obj) {\n  return obj.ol_uid || (obj.ol_uid = String(++uidCounter_));\n}\n\n/**\n * OpenLayers version.\n * @type {string}\n */\nexport const VERSION = '10.5.0';\n", "/**\n * @module ol/ObjectEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered when a property is changed.\n   * @event module:ol/Object.ObjectEvent#propertychange\n   * @api\n   */\n  PROPERTYCHANGE: 'propertychange',\n};\n\n/**\n * @typedef {'propertychange'} Types\n */\n", "/**\n * @module ol/Disposable\n */\n\n/**\n * @classdesc\n * Objects that need to clean up after themselves.\n */\nclass Disposable {\n  constructor() {\n    /**\n     * The object has already been disposed.\n     * @type {boolean}\n     * @protected\n     */\n    this.disposed = false;\n  }\n\n  /**\n   * Clean up.\n   */\n  dispose() {\n    if (!this.disposed) {\n      this.disposed = true;\n      this.disposeInternal();\n    }\n  }\n\n  /**\n   * Extension point for disposable objects.\n   * @protected\n   */\n  disposeInternal() {}\n}\n\nexport default Disposable;\n", "/**\n * @module ol/events/Target\n */\nimport Disposable from '../Disposable.js';\nimport {VOID} from '../functions.js';\nimport {clear} from '../obj.js';\nimport Event from './Event.js';\n\n/**\n * @typedef {EventTarget|Target} EventTargetLike\n */\n\n/**\n * @classdesc\n * A simplified implementation of the W3C DOM Level 2 EventTarget interface.\n * See https://www.w3.org/TR/2000/REC-DOM-Level-2-Events-20001113/events.html#Events-EventTarget.\n *\n * There are two important simplifications compared to the specification:\n *\n * 1. The handling of `useCapture` in `addEventListener` and\n *    `removeEventListener`. There is no real capture model.\n * 2. The handling of `stopPropagation` and `preventDefault` on `dispatchEvent`.\n *    There is no event target hierarchy. When a listener calls\n *    `stopPropagation` or `preventDefault` on an event object, it means that no\n *    more listeners after this one will be called. Same as when the listener\n *    returns false.\n */\nclass Target extends Disposable {\n  /**\n   * @param {*} [target] Default event target for dispatched events.\n   */\n  constructor(target) {\n    super();\n\n    /**\n     * @private\n     * @type {*}\n     */\n    this.eventTarget_ = target;\n\n    /**\n     * @private\n     * @type {Object<string, number>|null}\n     */\n    this.pendingRemovals_ = null;\n\n    /**\n     * @private\n     * @type {Object<string, number>|null}\n     */\n    this.dispatching_ = null;\n\n    /**\n     * @private\n     * @type {Object<string, Array<import(\"../events.js\").Listener>>|null}\n     */\n    this.listeners_ = null;\n  }\n\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../events.js\").Listener} listener Listener.\n   */\n  addEventListener(type, listener) {\n    if (!type || !listener) {\n      return;\n    }\n    const listeners = this.listeners_ || (this.listeners_ = {});\n    const listenersForType = listeners[type] || (listeners[type] = []);\n    if (!listenersForType.includes(listener)) {\n      listenersForType.push(listener);\n    }\n  }\n\n  /**\n   * Dispatches an event and calls all listeners listening for events\n   * of this type. The event parameter can either be a string or an\n   * Object with a `type` property.\n   *\n   * @param {import(\"./Event.js\").default|string} event Event object.\n   * @return {boolean|undefined} `false` if anyone called preventDefault on the\n   *     event object or if any of the listeners returned false.\n   * @api\n   */\n  dispatchEvent(event) {\n    const isString = typeof event === 'string';\n    const type = isString ? event : event.type;\n    const listeners = this.listeners_ && this.listeners_[type];\n    if (!listeners) {\n      return;\n    }\n\n    const evt = isString ? new Event(event) : /** @type {Event} */ (event);\n    if (!evt.target) {\n      evt.target = this.eventTarget_ || this;\n    }\n    const dispatching = this.dispatching_ || (this.dispatching_ = {});\n    const pendingRemovals =\n      this.pendingRemovals_ || (this.pendingRemovals_ = {});\n    if (!(type in dispatching)) {\n      dispatching[type] = 0;\n      pendingRemovals[type] = 0;\n    }\n    ++dispatching[type];\n    let propagate;\n    for (let i = 0, ii = listeners.length; i < ii; ++i) {\n      if ('handleEvent' in listeners[i]) {\n        propagate = /** @type {import(\"../events.js\").ListenerObject} */ (\n          listeners[i]\n        ).handleEvent(evt);\n      } else {\n        propagate = /** @type {import(\"../events.js\").ListenerFunction} */ (\n          listeners[i]\n        ).call(this, evt);\n      }\n      if (propagate === false || evt.propagationStopped) {\n        propagate = false;\n        break;\n      }\n    }\n    if (--dispatching[type] === 0) {\n      let pr = pendingRemovals[type];\n      delete pendingRemovals[type];\n      while (pr--) {\n        this.removeEventListener(type, VOID);\n      }\n      delete dispatching[type];\n    }\n    return propagate;\n  }\n\n  /**\n   * Clean up.\n   * @override\n   */\n  disposeInternal() {\n    this.listeners_ && clear(this.listeners_);\n  }\n\n  /**\n   * Get the listeners for a specified event type. Listeners are returned in the\n   * order that they will be called in.\n   *\n   * @param {string} type Type.\n   * @return {Array<import(\"../events.js\").Listener>|undefined} Listeners.\n   */\n  getListeners(type) {\n    return (this.listeners_ && this.listeners_[type]) || undefined;\n  }\n\n  /**\n   * @param {string} [type] Type. If not provided,\n   *     `true` will be returned if this event target has any listeners.\n   * @return {boolean} Has listeners.\n   */\n  hasListener(type) {\n    if (!this.listeners_) {\n      return false;\n    }\n    return type\n      ? type in this.listeners_\n      : Object.keys(this.listeners_).length > 0;\n  }\n\n  /**\n   * @param {string} type Type.\n   * @param {import(\"../events.js\").Listener} listener Listener.\n   */\n  removeEventListener(type, listener) {\n    if (!this.listeners_) {\n      return;\n    }\n    const listeners = this.listeners_[type];\n    if (!listeners) {\n      return;\n    }\n    const index = listeners.indexOf(listener);\n    if (index !== -1) {\n      if (this.pendingRemovals_ && type in this.pendingRemovals_) {\n        // make listener a no-op, and remove later in #dispatchEvent()\n        listeners[index] = VOID;\n        ++this.pendingRemovals_[type];\n      } else {\n        listeners.splice(index, 1);\n        if (listeners.length === 0) {\n          delete this.listeners_[type];\n        }\n      }\n    }\n  }\n}\n\nexport default Target;\n", "/**\n * @module ol/Observable\n */\nimport EventType from './events/EventType.js';\nimport EventTarget from './events/Target.js';\nimport {listen, listenOnce, unlistenByKey} from './events.js';\n\n/***\n * @template {string} Type\n * @template {Event|import(\"./events/Event.js\").default} EventClass\n * @template Return\n * @typedef {(type: Type, listener: (event: EventClass) => ?) => Return} OnSignature\n */\n\n/***\n * @template {string} Type\n * @template Return\n * @typedef {(type: Type[], listener: (event: Event|import(\"./events/Event\").default) => ?) => Return extends void ? void : Return[]} CombinedOnSignature\n */\n\n/**\n * @typedef {'change'|'error'} EventTypes\n */\n\n/***\n * @template Return\n * @typedef {OnSignature<EventTypes, import(\"./events/Event.js\").default, Return> & CombinedOnSignature<EventTypes, Return>} ObservableOnSignature\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * An event target providing convenient methods for listener registration\n * and unregistration. A generic `change` event is always available through\n * {@link module:ol/Observable~Observable#changed}.\n *\n * @fires import(\"./events/Event.js\").default\n * @api\n */\nclass Observable extends EventTarget {\n  constructor() {\n    super();\n\n    this.on =\n      /** @type {ObservableOnSignature<import(\"./events\").EventsKey>} */ (\n        this.onInternal\n      );\n\n    this.once =\n      /** @type {ObservableOnSignature<import(\"./events\").EventsKey>} */ (\n        this.onceInternal\n      );\n\n    this.un = /** @type {ObservableOnSignature<void>} */ (this.unInternal);\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.revision_ = 0;\n  }\n\n  /**\n   * Increases the revision counter and dispatches a 'change' event.\n   * @api\n   */\n  changed() {\n    ++this.revision_;\n    this.dispatchEvent(EventType.CHANGE);\n  }\n\n  /**\n   * Get the version number for this object.  Each time the object is modified,\n   * its version number will be incremented.\n   * @return {number} Revision.\n   * @api\n   */\n  getRevision() {\n    return this.revision_;\n  }\n\n  /**\n   * @param {string|Array<string>} type Type.\n   * @param {function((Event|import(\"./events/Event\").default)): ?} listener Listener.\n   * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Event key.\n   * @protected\n   */\n  onInternal(type, listener) {\n    if (Array.isArray(type)) {\n      const len = type.length;\n      const keys = new Array(len);\n      for (let i = 0; i < len; ++i) {\n        keys[i] = listen(this, type[i], listener);\n      }\n      return keys;\n    }\n    return listen(this, /** @type {string} */ (type), listener);\n  }\n\n  /**\n   * @param {string|Array<string>} type Type.\n   * @param {function((Event|import(\"./events/Event\").default)): ?} listener Listener.\n   * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Event key.\n   * @protected\n   */\n  onceInternal(type, listener) {\n    let key;\n    if (Array.isArray(type)) {\n      const len = type.length;\n      key = new Array(len);\n      for (let i = 0; i < len; ++i) {\n        key[i] = listenOnce(this, type[i], listener);\n      }\n    } else {\n      key = listenOnce(this, /** @type {string} */ (type), listener);\n    }\n    /** @type {Object} */ (listener).ol_key = key;\n    return key;\n  }\n\n  /**\n   * Unlisten for a certain type of event.\n   * @param {string|Array<string>} type Type.\n   * @param {function((Event|import(\"./events/Event\").default)): ?} listener Listener.\n   * @protected\n   */\n  unInternal(type, listener) {\n    const key = /** @type {Object} */ (listener).ol_key;\n    if (key) {\n      unByKey(key);\n    } else if (Array.isArray(type)) {\n      for (let i = 0, ii = type.length; i < ii; ++i) {\n        this.removeEventListener(type[i], listener);\n      }\n    } else {\n      this.removeEventListener(type, listener);\n    }\n  }\n}\n\n/**\n * Listen for a certain type of event.\n * @function\n * @param {string|Array<string>} type The event type or array of event types.\n * @param {function((Event|import(\"./events/Event\").default)): ?} listener The listener function.\n * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Unique key for the listener. If\n *     called with an array of event types as the first argument, the return\n *     will be an array of keys.\n * @api\n */\nObservable.prototype.on;\n\n/**\n * Listen once for a certain type of event.\n * @function\n * @param {string|Array<string>} type The event type or array of event types.\n * @param {function((Event|import(\"./events/Event\").default)): ?} listener The listener function.\n * @return {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} Unique key for the listener. If\n *     called with an array of event types as the first argument, the return\n *     will be an array of keys.\n * @api\n */\nObservable.prototype.once;\n\n/**\n * Unlisten for a certain type of event.\n * @function\n * @param {string|Array<string>} type The event type or array of event types.\n * @param {function((Event|import(\"./events/Event\").default)): ?} listener The listener function.\n * @api\n */\nObservable.prototype.un;\n\n/**\n * Removes an event listener using the key returned by `on()` or `once()`.\n * @param {import(\"./events.js\").EventsKey|Array<import(\"./events.js\").EventsKey>} key The key returned by `on()`\n *     or `once()` (or an array of keys).\n * @api\n */\nexport function unByKey(key) {\n  if (Array.isArray(key)) {\n    for (let i = 0, ii = key.length; i < ii; ++i) {\n      unlistenByKey(key[i]);\n    }\n  } else {\n    unlistenByKey(/** @type {import(\"./events.js\").EventsKey} */ (key));\n  }\n}\n\nexport default Observable;\n", "/**\n * @module ol/Object\n */\nimport ObjectEventType from './ObjectEventType.js';\nimport Observable from './Observable.js';\nimport Event from './events/Event.js';\nimport {isEmpty} from './obj.js';\nimport {getUid} from './util.js';\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/Object~BaseObject} instances are instances of this type.\n */\nexport class ObjectEvent extends Event {\n  /**\n   * @param {string} type The event type.\n   * @param {string} key The property name.\n   * @param {*} oldValue The old value for `key`.\n   */\n  constructor(type, key, oldValue) {\n    super(type);\n\n    /**\n     * The name of the property whose value is changing.\n     * @type {string}\n     * @api\n     */\n    this.key = key;\n\n    /**\n     * The old value. To get the new value use `e.target.get(e.key)` where\n     * `e` is the event object.\n     * @type {*}\n     * @api\n     */\n    this.oldValue = oldValue;\n  }\n}\n\n/***\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *    import(\"./Observable\").OnSignature<import(\"./ObjectEventType\").Types, ObjectEvent, Return> &\n *    import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|import(\"./ObjectEventType\").Types, Return>} ObjectOnSignature\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Most non-trivial classes inherit from this.\n *\n * This extends {@link module:ol/Observable~Observable} with observable\n * properties, where each property is observable as well as the object as a\n * whole.\n *\n * Classes that inherit from this have pre-defined properties, to which you can\n * add your owns. The pre-defined properties are listed in this documentation as\n * 'Observable Properties', and have their own accessors; for example,\n * {@link module:ol/Map~Map} has a `target` property, accessed with\n * `getTarget()` and changed with `setTarget()`. Not all properties are however\n * settable. There are also general-purpose accessors `get()` and `set()`. For\n * example, `get('target')` is equivalent to `getTarget()`.\n *\n * The `set` accessors trigger a change event, and you can monitor this by\n * registering a listener. For example, {@link module:ol/View~View} has a\n * `center` property, so `view.on('change:center', function(evt) {...});` would\n * call the function whenever the value of the center property changes. Within\n * the function, `evt.target` would be the view, so `evt.target.getCenter()`\n * would return the new center.\n *\n * You can add your own observable properties with\n * `object.set('prop', 'value')`, and retrieve that with `object.get('prop')`.\n * You can listen for changes on that property value with\n * `object.on('change:prop', listener)`. You can get a list of all\n * properties with {@link module:ol/Object~BaseObject#getProperties}.\n *\n * Note that the observable properties are separate from standard JS properties.\n * You can, for example, give your map object a title with\n * `map.title='New title'` and with `map.set('title', 'Another title')`. The\n * first will be a `hasOwnProperty`; the second will appear in\n * `getProperties()`. Only the second is observable.\n *\n * Properties can be deleted by using the unset method. E.g.\n * object.unset('foo').\n *\n * @fires ObjectEvent\n * @api\n */\nclass BaseObject extends Observable {\n  /**\n   * @param {Object<string, *>} [values] An object with key-value pairs.\n   */\n  constructor(values) {\n    super();\n\n    /***\n     * @type {ObjectOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {ObjectOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {ObjectOnSignature<void>}\n     */\n    this.un;\n\n    // Call {@link module:ol/util.getUid} to ensure that the order of objects' ids is\n    // the same as the order in which they were created.  This also helps to\n    // ensure that object properties are always added in the same order, which\n    // helps many JavaScript engines generate faster code.\n    getUid(this);\n\n    /**\n     * @private\n     * @type {Object<string, *>|null}\n     */\n    this.values_ = null;\n\n    if (values !== undefined) {\n      this.setProperties(values);\n    }\n  }\n\n  /**\n   * Gets a value.\n   * @param {string} key Key name.\n   * @return {*} Value.\n   * @api\n   */\n  get(key) {\n    let value;\n    if (this.values_ && this.values_.hasOwnProperty(key)) {\n      value = this.values_[key];\n    }\n    return value;\n  }\n\n  /**\n   * Get a list of object property names.\n   * @return {Array<string>} List of property names.\n   * @api\n   */\n  getKeys() {\n    return (this.values_ && Object.keys(this.values_)) || [];\n  }\n\n  /**\n   * Get an object of all property names and values.\n   * @return {Object<string, *>} Object.\n   * @api\n   */\n  getProperties() {\n    return (this.values_ && Object.assign({}, this.values_)) || {};\n  }\n\n  /**\n   * Get an object of all property names and values.\n   * @return {Object<string, *>?} Object.\n   */\n  getPropertiesInternal() {\n    return this.values_;\n  }\n\n  /**\n   * @return {boolean} The object has properties.\n   */\n  hasProperties() {\n    return !!this.values_;\n  }\n\n  /**\n   * @param {string} key Key name.\n   * @param {*} oldValue Old value.\n   */\n  notify(key, oldValue) {\n    let eventType;\n    eventType = `change:${key}`;\n    if (this.hasListener(eventType)) {\n      this.dispatchEvent(new ObjectEvent(eventType, key, oldValue));\n    }\n    eventType = ObjectEventType.PROPERTYCHANGE;\n    if (this.hasListener(eventType)) {\n      this.dispatchEvent(new ObjectEvent(eventType, key, oldValue));\n    }\n  }\n\n  /**\n   * @param {string} key Key name.\n   * @param {import(\"./events.js\").Listener} listener Listener.\n   */\n  addChangeListener(key, listener) {\n    this.addEventListener(`change:${key}`, listener);\n  }\n\n  /**\n   * @param {string} key Key name.\n   * @param {import(\"./events.js\").Listener} listener Listener.\n   */\n  removeChangeListener(key, listener) {\n    this.removeEventListener(`change:${key}`, listener);\n  }\n\n  /**\n   * Sets a value.\n   * @param {string} key Key name.\n   * @param {*} value Value.\n   * @param {boolean} [silent] Update without triggering an event.\n   * @api\n   */\n  set(key, value, silent) {\n    const values = this.values_ || (this.values_ = {});\n    if (silent) {\n      values[key] = value;\n    } else {\n      const oldValue = values[key];\n      values[key] = value;\n      if (oldValue !== value) {\n        this.notify(key, oldValue);\n      }\n    }\n  }\n\n  /**\n   * Sets a collection of key-value pairs.  Note that this changes any existing\n   * properties and adds new ones (it does not remove any existing properties).\n   * @param {Object<string, *>} values Values.\n   * @param {boolean} [silent] Update without triggering an event.\n   * @api\n   */\n  setProperties(values, silent) {\n    for (const key in values) {\n      this.set(key, values[key], silent);\n    }\n  }\n\n  /**\n   * Apply any properties from another object without triggering events.\n   * @param {BaseObject} source The source object.\n   * @protected\n   */\n  applyProperties(source) {\n    if (!source.values_) {\n      return;\n    }\n    Object.assign(this.values_ || (this.values_ = {}), source.values_);\n  }\n\n  /**\n   * Unsets a property.\n   * @param {string} key Key name.\n   * @param {boolean} [silent] Unset without triggering an event.\n   * @api\n   */\n  unset(key, silent) {\n    if (this.values_ && key in this.values_) {\n      const oldValue = this.values_[key];\n      delete this.values_[key];\n      if (isEmpty(this.values_)) {\n        this.values_ = null;\n      }\n      if (!silent) {\n        this.notify(key, oldValue);\n      }\n    }\n  }\n}\n\nexport default BaseObject;\n"], "mappings": ";;;;;;AAQO,SAAS,OAAO,WAAW,cAAc;AAC9C,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,YAAY;AAAA,EAC9B;AACF;;;ACCO,SAAS,aAAa,UAAU,QAAQ,YAAY;AACzD,MAAI,KAAK;AACT,eAAa,cAAc;AAC3B,MAAI,MAAM;AACV,MAAI,OAAO,SAAS;AACpB,MAAI,QAAQ;AAEZ,SAAO,MAAM,MAAM;AAGjB,UAAM,OAAQ,OAAO,OAAQ;AAC7B,UAAM,CAAC,WAAW,SAAS,GAAG,GAAG,MAAM;AAEvC,QAAI,MAAM,GAAK;AAEb,YAAM,MAAM;AAAA,IACd,OAAO;AAEL,aAAO;AACP,cAAQ,CAAC;AAAA,IACX;AAAA,EACF;AAGA,SAAO,QAAQ,MAAM,CAAC;AACxB;AASO,SAAS,UAAU,GAAG,GAAG;AAC9B,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AASO,SAAS,WAAW,GAAG,GAAG;AAC/B,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AAyBO,SAAS,kBAAkB,KAAK,QAAQ,WAAW;AACxD,MAAI,IAAI,CAAC,KAAK,QAAQ;AACpB,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,IAAI;AACd,MAAI,UAAU,IAAI,IAAI,CAAC,GAAG;AACxB,WAAO,IAAI;AAAA,EACb;AAEA,MAAI,OAAO,cAAc,YAAY;AACnC,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAM,YAAY,IAAI,CAAC;AACvB,UAAI,cAAc,QAAQ;AACxB,eAAO;AAAA,MACT;AACA,UAAI,YAAY,QAAQ;AACtB,YAAI,UAAU,QAAQ,IAAI,IAAI,CAAC,GAAG,SAAS,IAAI,GAAG;AAChD,iBAAO,IAAI;AAAA,QACb;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,IAAI;AAAA,EACb;AAEA,MAAI,YAAY,GAAG;AACjB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAI,IAAI,CAAC,IAAI,QAAQ;AACnB,eAAO,IAAI;AAAA,MACb;AAAA,IACF;AACA,WAAO,IAAI;AAAA,EACb;AAEA,MAAI,YAAY,GAAG;AACjB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAI,IAAI,CAAC,KAAK,QAAQ;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,IAAI;AAAA,EACb;AAEA,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,QAAI,IAAI,CAAC,KAAK,QAAQ;AACpB,aAAO;AAAA,IACT;AACA,QAAI,IAAI,CAAC,IAAI,QAAQ;AACnB,UAAI,IAAI,IAAI,CAAC,IAAI,SAAS,SAAS,IAAI,CAAC,GAAG;AACzC,eAAO,IAAI;AAAA,MACb;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,IAAI;AACb;AAOO,SAAS,gBAAgB,KAAK,OAAO,KAAK;AAC/C,SAAO,QAAQ,KAAK;AAClB,UAAM,MAAM,IAAI,KAAK;AACrB,QAAI,KAAK,IAAI,IAAI,GAAG;AACpB,QAAI,GAAG,IAAI;AACX,MAAE;AACF,MAAE;AAAA,EACJ;AACF;AAOO,SAAS,OAAO,KAAK,MAAM;AAChC,QAAM,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AACpD,QAAM,SAAS,UAAU;AACzB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,QAAI,IAAI,MAAM,IAAI,UAAU,CAAC;AAAA,EAC/B;AACF;AAsBO,SAAS,OAAO,MAAM,MAAM;AACjC,QAAM,OAAO,KAAK;AAClB,MAAI,SAAS,KAAK,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,QAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AA8BO,SAAS,SAAS,KAAK,MAAM,QAAQ;AAC1C,QAAM,UAAU,QAAQ;AACxB,SAAO,IAAI,MAAM,SAAU,YAAY,OAAO;AAC5C,QAAI,UAAU,GAAG;AACf,aAAO;AAAA,IACT;AACA,UAAM,MAAM,QAAQ,IAAI,QAAQ,CAAC,GAAG,UAAU;AAC9C,WAAO,EAAE,MAAM,KAAM,UAAU,QAAQ;AAAA,EACzC,CAAC;AACH;;;ACzOA,IAAO,oBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR,OAAO;AAAA,EAEP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,OAAO;AACT;;;AC5BO,SAAS,OAAO;AACrB,SAAO;AACT;AAMO,SAAS,QAAQ;AACtB,SAAO;AACT;AAOO,SAAS,OAAO;AAAC;AAWjB,SAAS,WAAW,IAAI;AAE7B,MAAI;AAGJ,MAAI;AAEJ,MAAI;AAMJ,SAAO,WAAY;AACjB,UAAM,WAAW,MAAM,UAAU,MAAM,KAAK,SAAS;AACrD,QAAI,CAAC,YAAY,SAAS,YAAY,CAAC,OAAY,UAAU,QAAQ,GAAG;AACtE,iBAAW;AACX,iBAAW;AACX,mBAAa,GAAG,MAAM,MAAM,SAAS;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AAOO,SAAS,UAAU,QAAQ;AAChC,WAAS,gBAAgB;AACvB,QAAI;AACJ,QAAI;AACF,cAAQ,OAAO;AAAA,IACjB,SAAS,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;AAAA,IAC3B;AACA,QAAI,iBAAiB,SAAS;AAC5B,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAC9B;AACA,SAAO,cAAc;AACvB;;;ACnEA,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAId,YAAY,MAAM;AAIhB,SAAK;AAKL,SAAK;AAOL,SAAK,OAAO;AAOZ,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,SAAK,qBAAqB;AAAA,EAC5B;AACF;AAgBA,IAAO,gBAAQ;;;AC9BR,SAAS,OAAO,QAAQ,MAAM,UAAU,SAAS,MAAM;AAC5D,MAAI,MAAM;AACR,UAAM,mBAAmB;AAMzB,eAAW,SAAU,OAAO;AAC1B,aAAO,oBAAoB,MAAM,QAAQ;AACzC,aAAO,iBAAiB,KAAK,WAAW,MAAM,KAAK;AAAA,IACrD;AAAA,EACF,WAAW,WAAW,YAAY,QAAQ;AACxC,eAAW,SAAS,KAAK,OAAO;AAAA,EAClC;AACA,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,iBAAiB,MAAM,QAAQ;AACtC,SAAO;AACT;AAsBO,SAAS,WAAW,QAAQ,MAAM,UAAU,SAAS;AAC1D,SAAO,OAAO,QAAQ,MAAM,UAAU,SAAS,IAAI;AACrD;AAWO,SAAS,cAAc,KAAK;AACjC,MAAI,OAAO,IAAI,QAAQ;AACrB,QAAI,OAAO,oBAAoB,IAAI,MAAM,IAAI,QAAQ;AACrD,UAAM,GAAG;AAAA,EACX;AACF;;;ACrGO,SAAS,WAAW;AACzB,QAAM,IAAI,MAAM,gCAAgC;AAClD;AAOA,IAAI,cAAc;AAWX,SAAS,OAAO,KAAK;AAC1B,SAAO,IAAI,WAAW,IAAI,SAAS,OAAO,EAAE,WAAW;AACzD;;;ACtBA,IAAO,0BAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,gBAAgB;AAClB;;;ACNA,IAAM,aAAN,MAAiB;AAAA,EACf,cAAc;AAMZ,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAAA,EAAC;AACrB;AAEA,IAAO,qBAAQ;;;ACRf,IAAM,SAAN,cAAqB,mBAAW;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,QAAQ;AAClB,UAAM;AAMN,SAAK,eAAe;AAMpB,SAAK,mBAAmB;AAMxB,SAAK,eAAe;AAMpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,MAAM,UAAU;AAC/B,QAAI,CAAC,QAAQ,CAAC,UAAU;AACtB;AAAA,IACF;AACA,UAAM,YAAY,KAAK,eAAe,KAAK,aAAa,CAAC;AACzD,UAAM,mBAAmB,UAAU,IAAI,MAAM,UAAU,IAAI,IAAI,CAAC;AAChE,QAAI,CAAC,iBAAiB,SAAS,QAAQ,GAAG;AACxC,uBAAiB,KAAK,QAAQ;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,OAAO;AACnB,UAAM,WAAW,OAAO,UAAU;AAClC,UAAM,OAAO,WAAW,QAAQ,MAAM;AACtC,UAAM,YAAY,KAAK,cAAc,KAAK,WAAW,IAAI;AACzD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAEA,UAAM,MAAM,WAAW,IAAI,cAAM,KAAK;AAAA;AAAA,MAA0B;AAAA;AAChE,QAAI,CAAC,IAAI,QAAQ;AACf,UAAI,SAAS,KAAK,gBAAgB;AAAA,IACpC;AACA,UAAM,cAAc,KAAK,iBAAiB,KAAK,eAAe,CAAC;AAC/D,UAAM,kBACJ,KAAK,qBAAqB,KAAK,mBAAmB,CAAC;AACrD,QAAI,EAAE,QAAQ,cAAc;AAC1B,kBAAY,IAAI,IAAI;AACpB,sBAAgB,IAAI,IAAI;AAAA,IAC1B;AACA,MAAE,YAAY,IAAI;AAClB,QAAI;AACJ,aAAS,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,EAAE,GAAG;AAClD,UAAI,iBAAiB,UAAU,CAAC,GAAG;AACjC;AAAA,QACE,UAAU,CAAC,EACX,YAAY,GAAG;AAAA,MACnB,OAAO;AACL;AAAA,QACE,UAAU,CAAC,EACX,KAAK,MAAM,GAAG;AAAA,MAClB;AACA,UAAI,cAAc,SAAS,IAAI,oBAAoB;AACjD,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,EAAE,YAAY,IAAI,MAAM,GAAG;AAC7B,UAAI,KAAK,gBAAgB,IAAI;AAC7B,aAAO,gBAAgB,IAAI;AAC3B,aAAO,MAAM;AACX,aAAK,oBAAoB,MAAM,IAAI;AAAA,MACrC;AACA,aAAO,YAAY,IAAI;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,SAAK,cAAc,MAAM,KAAK,UAAU;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,MAAM;AACjB,WAAQ,KAAK,cAAc,KAAK,WAAW,IAAI,KAAM;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM;AAChB,QAAI,CAAC,KAAK,YAAY;AACpB,aAAO;AAAA,IACT;AACA,WAAO,OACH,QAAQ,KAAK,aACb,OAAO,KAAK,KAAK,UAAU,EAAE,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB,MAAM,UAAU;AAClC,QAAI,CAAC,KAAK,YAAY;AACpB;AAAA,IACF;AACA,UAAM,YAAY,KAAK,WAAW,IAAI;AACtC,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,UAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,QAAI,UAAU,IAAI;AAChB,UAAI,KAAK,oBAAoB,QAAQ,KAAK,kBAAkB;AAE1D,kBAAU,KAAK,IAAI;AACnB,UAAE,KAAK,iBAAiB,IAAI;AAAA,MAC9B,OAAO;AACL,kBAAU,OAAO,OAAO,CAAC;AACzB,YAAI,UAAU,WAAW,GAAG;AAC1B,iBAAO,KAAK,WAAW,IAAI;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,iBAAQ;;;ACxJf,IAAM,aAAN,cAAyB,eAAY;AAAA,EACnC,cAAc;AACZ,UAAM;AAEN,SAAK;AAAA,IAED,KAAK;AAGT,SAAK;AAAA,IAED,KAAK;AAGT,SAAK;AAAA,IAAiD,KAAK;AAM3D,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,MAAE,KAAK;AACP,SAAK,cAAc,kBAAU,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,MAAM,UAAU;AACzB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,KAAK;AACjB,YAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,aAAK,CAAC,IAAI,OAAO,MAAM,KAAK,CAAC,GAAG,QAAQ;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MAAO;AAAA;AAAA,MAA6B;AAAA,MAAO;AAAA,IAAQ;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,MAAM,UAAU;AAC3B,QAAI;AACJ,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,KAAK;AACjB,YAAM,IAAI,MAAM,GAAG;AACnB,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAI,CAAC,IAAI,WAAW,MAAM,KAAK,CAAC,GAAG,QAAQ;AAAA,MAC7C;AAAA,IACF,OAAO;AACL,YAAM;AAAA,QAAW;AAAA;AAAA,QAA6B;AAAA,QAAO;AAAA,MAAQ;AAAA,IAC/D;AACsB,IAAC,SAAU,SAAS;AAC1C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,MAAM,UAAU;AACzB,UAAM;AAAA;AAAA,MAA6B,SAAU;AAAA;AAC7C,QAAI,KAAK;AACP,cAAQ,GAAG;AAAA,IACb,WAAW,MAAM,QAAQ,IAAI,GAAG;AAC9B,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,aAAK,oBAAoB,KAAK,CAAC,GAAG,QAAQ;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,WAAK,oBAAoB,MAAM,QAAQ;AAAA,IACzC;AAAA,EACF;AACF;AAYA,WAAW,UAAU;AAYrB,WAAW,UAAU;AASrB,WAAW,UAAU;AAQd,SAAS,QAAQ,KAAK;AAC3B,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5C,oBAAc,IAAI,CAAC,CAAC;AAAA,IACtB;AAAA,EACF,OAAO;AACL;AAAA;AAAA,MAA8D;AAAA,IAAI;AAAA,EACpE;AACF;AAEA,IAAO,qBAAQ;;;ACjLR,IAAM,cAAN,cAA0B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrC,YAAY,MAAM,KAAK,UAAU;AAC/B,UAAM,IAAI;AAOV,SAAK,MAAM;AAQX,SAAK,WAAW;AAAA,EAClB;AACF;AAoDA,IAAM,aAAN,cAAyB,mBAAW;AAAA;AAAA;AAAA;AAAA,EAIlC,YAAY,QAAQ;AAClB,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,WAAO,IAAI;AAMX,SAAK,UAAU;AAEf,QAAI,WAAW,QAAW;AACxB,WAAK,cAAc,MAAM;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,KAAK;AACP,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,QAAQ,eAAe,GAAG,GAAG;AACpD,cAAQ,KAAK,QAAQ,GAAG;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAQ,KAAK,WAAW,OAAO,KAAK,KAAK,OAAO,KAAM,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAQ,KAAK,WAAW,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,KAAM,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK,UAAU;AACpB,QAAI;AACJ,gBAAY,UAAU,GAAG;AACzB,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,WAAK,cAAc,IAAI,YAAY,WAAW,KAAK,QAAQ,CAAC;AAAA,IAC9D;AACA,gBAAY,wBAAgB;AAC5B,QAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,WAAK,cAAc,IAAI,YAAY,WAAW,KAAK,QAAQ,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,KAAK,UAAU;AAC/B,SAAK,iBAAiB,UAAU,GAAG,IAAI,QAAQ;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,KAAK,UAAU;AAClC,SAAK,oBAAoB,UAAU,GAAG,IAAI,QAAQ;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,KAAK,OAAO,QAAQ;AACtB,UAAM,SAAS,KAAK,YAAY,KAAK,UAAU,CAAC;AAChD,QAAI,QAAQ;AACV,aAAO,GAAG,IAAI;AAAA,IAChB,OAAO;AACL,YAAM,WAAW,OAAO,GAAG;AAC3B,aAAO,GAAG,IAAI;AACd,UAAI,aAAa,OAAO;AACtB,aAAK,OAAO,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,QAAQ,QAAQ;AAC5B,eAAW,OAAO,QAAQ;AACxB,WAAK,IAAI,KAAK,OAAO,GAAG,GAAG,MAAM;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,QAAQ;AACtB,QAAI,CAAC,OAAO,SAAS;AACnB;AAAA,IACF;AACA,WAAO,OAAO,KAAK,YAAY,KAAK,UAAU,CAAC,IAAI,OAAO,OAAO;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,KAAK,QAAQ;AACjB,QAAI,KAAK,WAAW,OAAO,KAAK,SAAS;AACvC,YAAM,WAAW,KAAK,QAAQ,GAAG;AACjC,aAAO,KAAK,QAAQ,GAAG;AACvB,UAAI,QAAQ,KAAK,OAAO,GAAG;AACzB,aAAK,UAAU;AAAA,MACjB;AACA,UAAI,CAAC,QAAQ;AACX,aAAK,OAAO,KAAK,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,iBAAQ;", "names": []}