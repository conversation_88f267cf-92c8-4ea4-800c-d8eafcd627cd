{"version": 3, "sources": ["../../ol/transform.js", "../../ol/geom/flat/transform.js", "../../ol/geom/Geometry.js", "../../ol/geom/SimpleGeometry.js", "../../ol/geom/flat/deflate.js", "../../ol/geom/Point.js"], "sourcesContent": ["/**\n * @module ol/transform\n */\nimport {assert} from './asserts.js';\n\n/**\n * An array representing an affine 2d transformation for use with\n * {@link module:ol/transform} functions. The array has 6 elements.\n * @typedef {!Array<number>} Transform\n * @api\n */\n\n/**\n * Collection of affine 2d transformation functions. The functions work on an\n * array of 6 elements. The element order is compatible with the [SVGMatrix\n * interface](https://developer.mozilla.org/en-US/docs/Web/API/SVGMatrix) and is\n * a subset (elements a to f) of a 3×3 matrix:\n * ```\n * [ a c e ]\n * [ b d f ]\n * [ 0 0 1 ]\n * ```\n */\n\n/**\n * @private\n * @type {Transform}\n */\nconst tmp_ = new Array(6);\n\n/**\n * Create an identity transform.\n * @return {!Transform} Identity transform.\n */\nexport function create() {\n  return [1, 0, 0, 1, 0, 0];\n}\n\n/**\n * Resets the given transform to an identity transform.\n * @param {!Transform} transform Transform.\n * @return {!Transform} Transform.\n */\nexport function reset(transform) {\n  return set(transform, 1, 0, 0, 1, 0, 0);\n}\n\n/**\n * Multiply the underlying matrices of two transforms and return the result in\n * the first transform.\n * @param {!Transform} transform1 Transform parameters of matrix 1.\n * @param {!Transform} transform2 Transform parameters of matrix 2.\n * @return {!Transform} transform1 multiplied with transform2.\n */\nexport function multiply(transform1, transform2) {\n  const a1 = transform1[0];\n  const b1 = transform1[1];\n  const c1 = transform1[2];\n  const d1 = transform1[3];\n  const e1 = transform1[4];\n  const f1 = transform1[5];\n  const a2 = transform2[0];\n  const b2 = transform2[1];\n  const c2 = transform2[2];\n  const d2 = transform2[3];\n  const e2 = transform2[4];\n  const f2 = transform2[5];\n\n  transform1[0] = a1 * a2 + c1 * b2;\n  transform1[1] = b1 * a2 + d1 * b2;\n  transform1[2] = a1 * c2 + c1 * d2;\n  transform1[3] = b1 * c2 + d1 * d2;\n  transform1[4] = a1 * e2 + c1 * f2 + e1;\n  transform1[5] = b1 * e2 + d1 * f2 + f1;\n\n  return transform1;\n}\n\n/**\n * Set the transform components a-f on a given transform.\n * @param {!Transform} transform Transform.\n * @param {number} a The a component of the transform.\n * @param {number} b The b component of the transform.\n * @param {number} c The c component of the transform.\n * @param {number} d The d component of the transform.\n * @param {number} e The e component of the transform.\n * @param {number} f The f component of the transform.\n * @return {!Transform} Matrix with transform applied.\n */\nexport function set(transform, a, b, c, d, e, f) {\n  transform[0] = a;\n  transform[1] = b;\n  transform[2] = c;\n  transform[3] = d;\n  transform[4] = e;\n  transform[5] = f;\n  return transform;\n}\n\n/**\n * Set transform on one matrix from another matrix.\n * @param {!Transform} transform1 Matrix to set transform to.\n * @param {!Transform} transform2 Matrix to set transform from.\n * @return {!Transform} transform1 with transform from transform2 applied.\n */\nexport function setFromArray(transform1, transform2) {\n  transform1[0] = transform2[0];\n  transform1[1] = transform2[1];\n  transform1[2] = transform2[2];\n  transform1[3] = transform2[3];\n  transform1[4] = transform2[4];\n  transform1[5] = transform2[5];\n  return transform1;\n}\n\n/**\n * Transforms the given coordinate with the given transform returning the\n * resulting, transformed coordinate. The coordinate will be modified in-place.\n *\n * @param {Transform} transform The transformation.\n * @param {import(\"./coordinate.js\").Coordinate|import(\"./pixel.js\").Pixel} coordinate The coordinate to transform.\n * @return {import(\"./coordinate.js\").Coordinate|import(\"./pixel.js\").Pixel} return coordinate so that operations can be\n *     chained together.\n */\nexport function apply(transform, coordinate) {\n  const x = coordinate[0];\n  const y = coordinate[1];\n  coordinate[0] = transform[0] * x + transform[2] * y + transform[4];\n  coordinate[1] = transform[1] * x + transform[3] * y + transform[5];\n  return coordinate;\n}\n\n/**\n * Applies rotation to the given transform.\n * @param {!Transform} transform Transform.\n * @param {number} angle Angle in radians.\n * @return {!Transform} The rotated transform.\n */\nexport function rotate(transform, angle) {\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  return multiply(transform, set(tmp_, cos, sin, -sin, cos, 0, 0));\n}\n\n/**\n * Applies scale to a given transform.\n * @param {!Transform} transform Transform.\n * @param {number} x Scale factor x.\n * @param {number} y Scale factor y.\n * @return {!Transform} The scaled transform.\n */\nexport function scale(transform, x, y) {\n  return multiply(transform, set(tmp_, x, 0, 0, y, 0, 0));\n}\n\n/**\n * Creates a scale transform.\n * @param {!Transform} target Transform to overwrite.\n * @param {number} x Scale factor x.\n * @param {number} y Scale factor y.\n * @return {!Transform} The scale transform.\n */\nexport function makeScale(target, x, y) {\n  return set(target, x, 0, 0, y, 0, 0);\n}\n\n/**\n * Applies translation to the given transform.\n * @param {!Transform} transform Transform.\n * @param {number} dx Translation x.\n * @param {number} dy Translation y.\n * @return {!Transform} The translated transform.\n */\nexport function translate(transform, dx, dy) {\n  return multiply(transform, set(tmp_, 1, 0, 0, 1, dx, dy));\n}\n\n/**\n * Creates a composite transform given an initial translation, scale, rotation, and\n * final translation (in that order only, not commutative).\n * @param {!Transform} transform The transform (will be modified in place).\n * @param {number} dx1 Initial translation x.\n * @param {number} dy1 Initial translation y.\n * @param {number} sx Scale factor x.\n * @param {number} sy Scale factor y.\n * @param {number} angle Rotation (in counter-clockwise radians).\n * @param {number} dx2 Final translation x.\n * @param {number} dy2 Final translation y.\n * @return {!Transform} The composite transform.\n */\nexport function compose(transform, dx1, dy1, sx, sy, angle, dx2, dy2) {\n  const sin = Math.sin(angle);\n  const cos = Math.cos(angle);\n  transform[0] = sx * cos;\n  transform[1] = sy * sin;\n  transform[2] = -sx * sin;\n  transform[3] = sy * cos;\n  transform[4] = dx2 * sx * cos - dy2 * sx * sin + dx1;\n  transform[5] = dx2 * sy * sin + dy2 * sy * cos + dy1;\n  return transform;\n}\n\n/**\n * Creates a composite transform given an initial translation, scale, rotation, and\n * final translation (in that order only, not commutative). The resulting transform\n * string can be applied as `transform` property of an HTMLElement's style.\n * @param {number} dx1 Initial translation x.\n * @param {number} dy1 Initial translation y.\n * @param {number} sx Scale factor x.\n * @param {number} sy Scale factor y.\n * @param {number} angle Rotation (in counter-clockwise radians).\n * @param {number} dx2 Final translation x.\n * @param {number} dy2 Final translation y.\n * @return {string} The composite css transform.\n * @api\n */\nexport function composeCssTransform(dx1, dy1, sx, sy, angle, dx2, dy2) {\n  return toString(compose(create(), dx1, dy1, sx, sy, angle, dx2, dy2));\n}\n\n/**\n * Invert the given transform.\n * @param {!Transform} source The source transform to invert.\n * @return {!Transform} The inverted (source) transform.\n */\nexport function invert(source) {\n  return makeInverse(source, source);\n}\n\n/**\n * Invert the given transform.\n * @param {!Transform} target Transform to be set as the inverse of\n *     the source transform.\n * @param {!Transform} source The source transform to invert.\n * @return {!Transform} The inverted (target) transform.\n */\nexport function makeInverse(target, source) {\n  const det = determinant(source);\n  assert(det !== 0, 'Transformation matrix cannot be inverted');\n\n  const a = source[0];\n  const b = source[1];\n  const c = source[2];\n  const d = source[3];\n  const e = source[4];\n  const f = source[5];\n\n  target[0] = d / det;\n  target[1] = -b / det;\n  target[2] = -c / det;\n  target[3] = a / det;\n  target[4] = (c * f - d * e) / det;\n  target[5] = -(a * f - b * e) / det;\n\n  return target;\n}\n\n/**\n * Returns the determinant of the given matrix.\n * @param {!Transform} mat Matrix.\n * @return {number} Determinant.\n */\nexport function determinant(mat) {\n  return mat[0] * mat[3] - mat[1] * mat[2];\n}\n\n/**\n * @type {Array}\n */\nconst matrixPrecision = [1e5, 1e5, 1e5, 1e5, 2, 2];\n\n/**\n * A matrix string version of the transform.  This can be used\n * for CSS transforms.\n * @param {!Transform} mat Matrix.\n * @return {string} The transform as a string.\n */\nexport function toString(mat) {\n  const transformString = 'matrix(' + mat.join(', ') + ')';\n  return transformString;\n}\n\n/**\n * Create a transform from a CSS transform matrix string.\n * @param {string} cssTransform The CSS string to parse.\n * @return {!Transform} The transform.\n */\nfunction fromString(cssTransform) {\n  const values = cssTransform.substring(7, cssTransform.length - 1).split(',');\n  return values.map(parseFloat);\n}\n\n/**\n * Compare two matrices for equality.\n * @param {!string} cssTransform1 A CSS transform matrix string.\n * @param {!string} cssTransform2 A CSS transform matrix string.\n * @return {boolean} The two matrices are equal.\n */\nexport function equivalent(cssTransform1, cssTransform2) {\n  const mat1 = fromString(cssTransform1);\n  const mat2 = fromString(cssTransform2);\n  for (let i = 0; i < 6; ++i) {\n    if (Math.round((mat1[i] - mat2[i]) * matrixPrecision[i]) !== 0) {\n      return false;\n    }\n  }\n  return true;\n}\n", "/**\n * @module ol/geom/flat/transform\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../transform.js\").Transform} transform Transform.\n * @param {Array<number>} [dest] Destination.\n * @param {number} [destinationStride] Stride of destination coordinates; if unspecified, assumed to be 2.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function transform2D(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  transform,\n  dest,\n  destinationStride,\n) {\n  dest = dest ? dest : [];\n  destinationStride = destinationStride ? destinationStride : 2;\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    const x = flatCoordinates[j];\n    const y = flatCoordinates[j + 1];\n    dest[i++] = transform[0] * x + transform[2] * y + transform[4];\n    dest[i++] = transform[1] * x + transform[3] * y + transform[5];\n\n    for (let k = 2; k < destinationStride; k++) {\n      dest[i++] = flatCoordinates[j + k];\n    }\n  }\n\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} angle Angle.\n * @param {Array<number>} anchor Rotation anchor point.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function rotate(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  angle,\n  anchor,\n  dest,\n) {\n  dest = dest ? dest : [];\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  const anchorX = anchor[0];\n  const anchorY = anchor[1];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    const deltaX = flatCoordinates[j] - anchorX;\n    const deltaY = flatCoordinates[j + 1] - anchorY;\n    dest[i++] = anchorX + deltaX * cos - deltaY * sin;\n    dest[i++] = anchorY + deltaX * sin + deltaY * cos;\n    for (let k = j + 2; k < j + stride; ++k) {\n      dest[i++] = flatCoordinates[k];\n    }\n  }\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n\n/**\n * Scale the coordinates.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} sx Scale factor in the x-direction.\n * @param {number} sy Scale factor in the y-direction.\n * @param {Array<number>} anchor Scale anchor point.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function scale(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  sx,\n  sy,\n  anchor,\n  dest,\n) {\n  dest = dest ? dest : [];\n  const anchorX = anchor[0];\n  const anchorY = anchor[1];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    const deltaX = flatCoordinates[j] - anchorX;\n    const deltaY = flatCoordinates[j + 1] - anchorY;\n    dest[i++] = anchorX + sx * deltaX;\n    dest[i++] = anchorY + sy * deltaY;\n    for (let k = j + 2; k < j + stride; ++k) {\n      dest[i++] = flatCoordinates[k];\n    }\n  }\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} deltaX Delta X.\n * @param {number} deltaY Delta Y.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed coordinates.\n */\nexport function translate(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  deltaX,\n  deltaY,\n  dest,\n) {\n  dest = dest ? dest : [];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    dest[i++] = flatCoordinates[j] + deltaX;\n    dest[i++] = flatCoordinates[j + 1] + deltaY;\n    for (let k = j + 2; k < j + stride; ++k) {\n      dest[i++] = flatCoordinates[k];\n    }\n  }\n  if (dest && dest.length != i) {\n    dest.length = i;\n  }\n  return dest;\n}\n", "/**\n * @module ol/geom/Geometry\n */\nimport BaseObject from '../Object.js';\nimport {\n  createEmpty,\n  createOrUpdateEmpty,\n  getHeight,\n  returnOrUpdate,\n} from '../extent.js';\nimport {memoizeOne} from '../functions.js';\nimport {get as getProjection, getTransform} from '../proj.js';\nimport {\n  compose as composeTransform,\n  create as createTransform,\n} from '../transform.js';\nimport {abstract} from '../util.js';\nimport {transform2D} from './flat/transform.js';\n\n/**\n * @typedef {'XY' | 'XYZ' | 'XYM' | 'XYZM'} GeometryLayout\n * The coordinate layout for geometries, indicating whether a 3rd or 4th z ('Z')\n * or measure ('M') coordinate is available.\n */\n\n/**\n * @typedef {'Point' | 'LineString' | 'LinearRing' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon' | 'GeometryCollection' | 'Circle'} Type\n * The geometry type.  One of `'Point'`, `'LineString'`, `'LinearRing'`,\n * `'Polygon'`, `'MultiPoint'`, `'MultiLineString'`, `'MultiPolygon'`,\n * `'GeometryCollection'`, or `'Circle'`.\n */\n\n/**\n * @type {import(\"../transform.js\").Transform}\n */\nconst tmpTransform = createTransform();\n\n/** @type {import('../coordinate.js').Coordinate} */\nconst tmpPoint = [NaN, NaN];\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for vector geometries.\n *\n * To get notified of changes to the geometry, register a listener for the\n * generic `change` event on your geometry instance.\n *\n * @abstract\n * @api\n */\nclass Geometry extends BaseObject {\n  constructor() {\n    super();\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.extent_ = createEmpty();\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.extentRevision_ = -1;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.simplifiedGeometryMaxMinSquaredTolerance = 0;\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.simplifiedGeometryRevision = 0;\n\n    /**\n     * Get a transformed and simplified version of the geometry.\n     * @abstract\n     * @param {number} revision The geometry revision.\n     * @param {number} squaredTolerance Squared tolerance.\n     * @param {import(\"../proj.js\").TransformFunction} [transform] Optional transform function.\n     * @return {Geometry} Simplified geometry.\n     */\n    this.simplifyTransformedInternal = memoizeOne(\n      (revision, squaredTolerance, transform) => {\n        if (!transform) {\n          return this.getSimplifiedGeometry(squaredTolerance);\n        }\n        const clone = this.clone();\n        clone.applyTransform(transform);\n        return clone.getSimplifiedGeometry(squaredTolerance);\n      },\n    );\n  }\n\n  /**\n   * Get a transformed and simplified version of the geometry.\n   * @abstract\n   * @param {number} squaredTolerance Squared tolerance.\n   * @param {import(\"../proj.js\").TransformFunction} [transform] Optional transform function.\n   * @return {Geometry} Simplified geometry.\n   */\n  simplifyTransformed(squaredTolerance, transform) {\n    return this.simplifyTransformedInternal(\n      this.getRevision(),\n      squaredTolerance,\n      transform,\n    );\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @abstract\n   * @return {!Geometry} Clone.\n   */\n  clone() {\n    return abstract();\n  }\n\n  /**\n   * @abstract\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    return abstract();\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   */\n  containsXY(x, y) {\n    return this.closestPointXY(x, y, tmpPoint, Number.MIN_VALUE) === 0;\n  }\n\n  /**\n   * Return the closest point of the geometry to the passed point as\n   * {@link module:ol/coordinate~Coordinate coordinate}.\n   * @param {import(\"../coordinate.js\").Coordinate} point Point.\n   * @param {import(\"../coordinate.js\").Coordinate} [closestPoint] Closest point.\n   * @return {import(\"../coordinate.js\").Coordinate} Closest point.\n   * @api\n   */\n  getClosestPoint(point, closestPoint) {\n    closestPoint = closestPoint ? closestPoint : [NaN, NaN];\n    this.closestPointXY(point[0], point[1], closestPoint, Infinity);\n    return closestPoint;\n  }\n\n  /**\n   * Returns true if this geometry includes the specified coordinate. If the\n   * coordinate is on the boundary of the geometry, returns false.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @return {boolean} Contains coordinate.\n   * @api\n   */\n  intersectsCoordinate(coordinate) {\n    return this.containsXY(coordinate[0], coordinate[1]);\n  }\n\n  /**\n   * @abstract\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   */\n  computeExtent(extent) {\n    return abstract();\n  }\n\n  /**\n   * Get the extent of the geometry.\n   * @param {import(\"../extent.js\").Extent} [extent] Extent.\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   * @api\n   */\n  getExtent(extent) {\n    if (this.extentRevision_ != this.getRevision()) {\n      const extent = this.computeExtent(this.extent_);\n      if (isNaN(extent[0]) || isNaN(extent[1])) {\n        createOrUpdateEmpty(extent);\n      }\n      this.extentRevision_ = this.getRevision();\n    }\n    return returnOrUpdate(this.extent_, extent);\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @abstract\n   * @param {number} angle Rotation angle in radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   */\n  rotate(angle, anchor) {\n    abstract();\n  }\n\n  /**\n   * Scale the geometry (with an optional origin).  This modifies the geometry\n   * coordinates in place.\n   * @abstract\n   * @param {number} sx The scaling factor in the x-direction.\n   * @param {number} [sy] The scaling factor in the y-direction (defaults to sx).\n   * @param {import(\"../coordinate.js\").Coordinate} [anchor] The scale origin (defaults to the center\n   *     of the geometry extent).\n   * @api\n   */\n  scale(sx, sy, anchor) {\n    abstract();\n  }\n\n  /**\n   * Create a simplified version of this geometry.  For linestrings, this uses\n   * the [Douglas Peucker](https://en.wikipedia.org/wiki/Ramer-Douglas-Peucker_algorithm)\n   * algorithm.  For polygons, a quantization-based\n   * simplification is used to preserve topology.\n   * @param {number} tolerance The tolerance distance for simplification.\n   * @return {Geometry} A new, simplified version of the original geometry.\n   * @api\n   */\n  simplify(tolerance) {\n    return this.getSimplifiedGeometry(tolerance * tolerance);\n  }\n\n  /**\n   * Create a simplified version of this geometry using the Douglas Peucker\n   * algorithm.\n   * See https://en.wikipedia.org/wiki/Ramer-Douglas-Peucker_algorithm.\n   * @abstract\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {Geometry} Simplified geometry.\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    return abstract();\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @abstract\n   * @return {Type} Geometry type.\n   */\n  getType() {\n    return abstract();\n  }\n\n  /**\n   * Apply a transform function to the coordinates of the geometry.\n   * The geometry is modified in place.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   * @abstract\n   * @param {import(\"../proj.js\").TransformFunction} transformFn Transform function.\n   * Called with a flat array of geometry coordinates.\n   */\n  applyTransform(transformFn) {\n    abstract();\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @abstract\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   */\n  intersectsExtent(extent) {\n    return abstract();\n  }\n\n  /**\n   * Translate the geometry.  This modifies the geometry coordinates in place.  If\n   * instead you want a new geometry, first `clone()` this geometry.\n   * @abstract\n   * @param {number} deltaX Delta X.\n   * @param {number} deltaY Delta Y.\n   * @api\n   */\n  translate(deltaX, deltaY) {\n    abstract();\n  }\n\n  /**\n   * Transform each coordinate of the geometry from one coordinate reference\n   * system to another. The geometry is modified in place.\n   * For example, a line will be transformed to a line and a circle to a circle.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   *\n   * @param {import(\"../proj.js\").ProjectionLike} source The current projection.  Can be a\n   *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n   * @param {import(\"../proj.js\").ProjectionLike} destination The desired projection.  Can be a\n   *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n   * @return {this} This geometry.  Note that original geometry is\n   *     modified in place.\n   * @api\n   */\n  transform(source, destination) {\n    /** @type {import(\"../proj/Projection.js\").default} */\n    const sourceProj = getProjection(source);\n    const transformFn =\n      sourceProj.getUnits() == 'tile-pixels'\n        ? function (inCoordinates, outCoordinates, stride) {\n            const pixelExtent = sourceProj.getExtent();\n            const projectedExtent = sourceProj.getWorldExtent();\n            const scale = getHeight(projectedExtent) / getHeight(pixelExtent);\n            composeTransform(\n              tmpTransform,\n              projectedExtent[0],\n              projectedExtent[3],\n              scale,\n              -scale,\n              0,\n              0,\n              0,\n            );\n            const transformed = transform2D(\n              inCoordinates,\n              0,\n              inCoordinates.length,\n              stride,\n              tmpTransform,\n              outCoordinates,\n            );\n            const projTransform = getTransform(sourceProj, destination);\n            if (projTransform) {\n              return projTransform(transformed, transformed, stride);\n            }\n            return transformed;\n          }\n        : getTransform(sourceProj, destination);\n    this.applyTransform(transformFn);\n    return this;\n  }\n}\n\nexport default Geometry;\n", "/**\n * @module ol/geom/SimpleGeometry\n */\nimport {createOrUpdateFromFlatCoordinates, getCenter} from '../extent.js';\nimport {abstract} from '../util.js';\nimport Geometry from './Geometry.js';\nimport {rotate, scale, transform2D, translate} from './flat/transform.js';\n\n/**\n * @classdesc\n * Abstract base class; only used for creating subclasses; do not instantiate\n * in apps, as cannot be rendered.\n *\n * @abstract\n * @api\n */\nclass SimpleGeometry extends Geometry {\n  constructor() {\n    super();\n\n    /**\n     * @protected\n     * @type {import(\"./Geometry.js\").GeometryLayout}\n     */\n    this.layout = 'XY';\n\n    /**\n     * @protected\n     * @type {number}\n     */\n    this.stride = 2;\n\n    /**\n     * @protected\n     * @type {Array<number>}\n     */\n    this.flatCoordinates;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   * @override\n   */\n  computeExtent(extent) {\n    return createOrUpdateFromFlatCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      extent,\n    );\n  }\n\n  /**\n   * @abstract\n   * @return {Array<*> | null} Coordinates.\n   */\n  getCoordinates() {\n    return abstract();\n  }\n\n  /**\n   * Return the first coordinate of the geometry.\n   * @return {import(\"../coordinate.js\").Coordinate} First coordinate.\n   * @api\n   */\n  getFirstCoordinate() {\n    return this.flatCoordinates.slice(0, this.stride);\n  }\n\n  /**\n   * @return {Array<number>} Flat coordinates.\n   */\n  getFlatCoordinates() {\n    return this.flatCoordinates;\n  }\n\n  /**\n   * Return the last coordinate of the geometry.\n   * @return {import(\"../coordinate.js\").Coordinate} Last point.\n   * @api\n   */\n  getLastCoordinate() {\n    return this.flatCoordinates.slice(\n      this.flatCoordinates.length - this.stride,\n    );\n  }\n\n  /**\n   * Return the {@link import(\"./Geometry.js\").GeometryLayout layout} of the geometry.\n   * @return {import(\"./Geometry.js\").GeometryLayout} Layout.\n   * @api\n   */\n  getLayout() {\n    return this.layout;\n  }\n\n  /**\n   * Create a simplified version of this geometry using the Douglas Peucker algorithm.\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {SimpleGeometry} Simplified geometry.\n   * @override\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    if (this.simplifiedGeometryRevision !== this.getRevision()) {\n      this.simplifiedGeometryMaxMinSquaredTolerance = 0;\n      this.simplifiedGeometryRevision = this.getRevision();\n    }\n    // If squaredTolerance is negative or if we know that simplification will not\n    // have any effect then just return this.\n    if (\n      squaredTolerance < 0 ||\n      (this.simplifiedGeometryMaxMinSquaredTolerance !== 0 &&\n        squaredTolerance <= this.simplifiedGeometryMaxMinSquaredTolerance)\n    ) {\n      return this;\n    }\n\n    const simplifiedGeometry =\n      this.getSimplifiedGeometryInternal(squaredTolerance);\n    const simplifiedFlatCoordinates = simplifiedGeometry.getFlatCoordinates();\n    if (simplifiedFlatCoordinates.length < this.flatCoordinates.length) {\n      return simplifiedGeometry;\n    }\n    // Simplification did not actually remove any coordinates.  We now know\n    // that any calls to getSimplifiedGeometry with a squaredTolerance less\n    // than or equal to the current squaredTolerance will also not have any\n    // effect.  This allows us to short circuit simplification (saving CPU\n    // cycles) and prevents the cache of simplified geometries from filling\n    // up with useless identical copies of this geometry (saving memory).\n    this.simplifiedGeometryMaxMinSquaredTolerance = squaredTolerance;\n    return this;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {SimpleGeometry} Simplified geometry.\n   * @protected\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    return this;\n  }\n\n  /**\n   * @return {number} Stride.\n   */\n  getStride() {\n    return this.stride;\n  }\n\n  /**\n   * @param {import(\"./Geometry.js\").GeometryLayout} layout Layout.\n   * @param {Array<number>} flatCoordinates Flat coordinates.\n   */\n  setFlatCoordinates(layout, flatCoordinates) {\n    this.stride = getStrideForLayout(layout);\n    this.layout = layout;\n    this.flatCoordinates = flatCoordinates;\n  }\n\n  /**\n   * @abstract\n   * @param {!Array<*>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  setCoordinates(coordinates, layout) {\n    abstract();\n  }\n\n  /**\n   * @param {import(\"./Geometry.js\").GeometryLayout|undefined} layout Layout.\n   * @param {Array<*>} coordinates Coordinates.\n   * @param {number} nesting Nesting.\n   * @protected\n   */\n  setLayout(layout, coordinates, nesting) {\n    let stride;\n    if (layout) {\n      stride = getStrideForLayout(layout);\n    } else {\n      for (let i = 0; i < nesting; ++i) {\n        if (coordinates.length === 0) {\n          this.layout = 'XY';\n          this.stride = 2;\n          return;\n        }\n        coordinates = /** @type {Array<unknown>} */ (coordinates[0]);\n      }\n      stride = coordinates.length;\n      layout = getLayoutForStride(stride);\n    }\n    this.layout = layout;\n    this.stride = stride;\n  }\n\n  /**\n   * Apply a transform function to the coordinates of the geometry.\n   * The geometry is modified in place.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   * @param {import(\"../proj.js\").TransformFunction} transformFn Transform function.\n   * Called with a flat array of geometry coordinates.\n   * @api\n   * @override\n   */\n  applyTransform(transformFn) {\n    if (this.flatCoordinates) {\n      transformFn(\n        this.flatCoordinates,\n        this.flatCoordinates,\n        this.layout.startsWith('XYZ') ? 3 : 2,\n        this.stride,\n      );\n      this.changed();\n    }\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @param {number} angle Rotation angle in counter-clockwise radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   * @override\n   */\n  rotate(angle, anchor) {\n    const flatCoordinates = this.getFlatCoordinates();\n    if (flatCoordinates) {\n      const stride = this.getStride();\n      rotate(\n        flatCoordinates,\n        0,\n        flatCoordinates.length,\n        stride,\n        angle,\n        anchor,\n        flatCoordinates,\n      );\n      this.changed();\n    }\n  }\n\n  /**\n   * Scale the geometry (with an optional origin).  This modifies the geometry\n   * coordinates in place.\n   * @param {number} sx The scaling factor in the x-direction.\n   * @param {number} [sy] The scaling factor in the y-direction (defaults to sx).\n   * @param {import(\"../coordinate.js\").Coordinate} [anchor] The scale origin (defaults to the center\n   *     of the geometry extent).\n   * @api\n   * @override\n   */\n  scale(sx, sy, anchor) {\n    if (sy === undefined) {\n      sy = sx;\n    }\n    if (!anchor) {\n      anchor = getCenter(this.getExtent());\n    }\n    const flatCoordinates = this.getFlatCoordinates();\n    if (flatCoordinates) {\n      const stride = this.getStride();\n      scale(\n        flatCoordinates,\n        0,\n        flatCoordinates.length,\n        stride,\n        sx,\n        sy,\n        anchor,\n        flatCoordinates,\n      );\n      this.changed();\n    }\n  }\n\n  /**\n   * Translate the geometry.  This modifies the geometry coordinates in place.  If\n   * instead you want a new geometry, first `clone()` this geometry.\n   * @param {number} deltaX Delta X.\n   * @param {number} deltaY Delta Y.\n   * @api\n   * @override\n   */\n  translate(deltaX, deltaY) {\n    const flatCoordinates = this.getFlatCoordinates();\n    if (flatCoordinates) {\n      const stride = this.getStride();\n      translate(\n        flatCoordinates,\n        0,\n        flatCoordinates.length,\n        stride,\n        deltaX,\n        deltaY,\n        flatCoordinates,\n      );\n      this.changed();\n    }\n  }\n}\n\n/**\n * @param {number} stride Stride.\n * @return {import(\"./Geometry.js\").GeometryLayout} layout Layout.\n */\nexport function getLayoutForStride(stride) {\n  let layout;\n  if (stride == 2) {\n    layout = 'XY';\n  } else if (stride == 3) {\n    layout = 'XYZ';\n  } else if (stride == 4) {\n    layout = 'XYZM';\n  }\n  return /** @type {import(\"./Geometry.js\").GeometryLayout} */ (layout);\n}\n\n/**\n * @param {import(\"./Geometry.js\").GeometryLayout} layout Layout.\n * @return {number} Stride.\n */\nexport function getStrideForLayout(layout) {\n  let stride;\n  if (layout == 'XY') {\n    stride = 2;\n  } else if (layout == 'XYZ' || layout == 'XYM') {\n    stride = 3;\n  } else if (layout == 'XYZM') {\n    stride = 4;\n  }\n  return /** @type {number} */ (stride);\n}\n\n/**\n * @param {SimpleGeometry} simpleGeometry Simple geometry.\n * @param {import(\"../transform.js\").Transform} transform Transform.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Transformed flat coordinates.\n */\nexport function transformGeom2D(simpleGeometry, transform, dest) {\n  const flatCoordinates = simpleGeometry.getFlatCoordinates();\n  if (!flatCoordinates) {\n    return null;\n  }\n  const stride = simpleGeometry.getStride();\n  return transform2D(\n    flatCoordinates,\n    0,\n    flatCoordinates.length,\n    stride,\n    transform,\n    dest,\n  );\n}\n\nexport default SimpleGeometry;\n", "/**\n * @module ol/geom/flat/deflate\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {import(\"../../coordinate.js\").Coordinate} coordinate Coordinate.\n * @param {number} stride Stride.\n * @return {number} offset Offset.\n */\nexport function deflateCoordinate(flatCoordinates, offset, coordinate, stride) {\n  for (let i = 0, ii = coordinate.length; i < ii; ++i) {\n    flatCoordinates[offset++] = coordinate[i];\n  }\n  return offset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<import(\"../../coordinate.js\").Coordinate>} coordinates Coordinates.\n * @param {number} stride Stride.\n * @return {number} offset Offset.\n */\nexport function deflateCoordinates(\n  flatCoordinates,\n  offset,\n  coordinates,\n  stride,\n) {\n  for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n    const coordinate = coordinates[i];\n    for (let j = 0; j < stride; ++j) {\n      flatCoordinates[offset++] = coordinate[j];\n    }\n  }\n  return offset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<import(\"../../coordinate.js\").Coordinate>>} coordinatess Coordinatess.\n * @param {number} stride Stride.\n * @param {Array<number>} [ends] Ends.\n * @return {Array<number>} Ends.\n */\nexport function deflateCoordinatesArray(\n  flatCoordinates,\n  offset,\n  coordinatess,\n  stride,\n  ends,\n) {\n  ends = ends ? ends : [];\n  let i = 0;\n  for (let j = 0, jj = coordinatess.length; j < jj; ++j) {\n    const end = deflateCoordinates(\n      flatCoordinates,\n      offset,\n      coordinatess[j],\n      stride,\n    );\n    ends[i++] = end;\n    offset = end;\n  }\n  ends.length = i;\n  return ends;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<Array<import(\"../../coordinate.js\").Coordinate>>>} coordinatesss Coordinatesss.\n * @param {number} stride Stride.\n * @param {Array<Array<number>>} [endss] Endss.\n * @return {Array<Array<number>>} Endss.\n */\nexport function deflateMultiCoordinatesArray(\n  flatCoordinates,\n  offset,\n  coordinatesss,\n  stride,\n  endss,\n) {\n  endss = endss ? endss : [];\n  let i = 0;\n  for (let j = 0, jj = coordinatesss.length; j < jj; ++j) {\n    const ends = deflateCoordinatesArray(\n      flatCoordinates,\n      offset,\n      coordinatesss[j],\n      stride,\n      endss[i],\n    );\n    if (ends.length === 0) {\n      ends[0] = offset;\n    }\n    endss[i++] = ends;\n    offset = ends[ends.length - 1];\n  }\n  endss.length = i;\n  return endss;\n}\n", "/**\n * @module ol/geom/Point\n */\nimport {containsXY, createOrUpdateFromCoordinate} from '../extent.js';\nimport {squaredDistance as squaredDx} from '../math.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {deflateCoordinate} from './flat/deflate.js';\n\n/**\n * @classdesc\n * Point geometry.\n *\n * @api\n */\nclass Point extends SimpleGeometry {\n  /**\n   * @param {import(\"../coordinate.js\").Coordinate} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n    this.setCoordinates(coordinates, layout);\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!Point} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const point = new Point(this.flatCoordinates.slice(), this.layout);\n    point.applyProperties(this);\n    return point;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    const flatCoordinates = this.flatCoordinates;\n    const squaredDistance = squaredDx(\n      x,\n      y,\n      flatCoordinates[0],\n      flatCoordinates[1],\n    );\n    if (squaredDistance < minSquaredDistance) {\n      const stride = this.stride;\n      for (let i = 0; i < stride; ++i) {\n        closestPoint[i] = flatCoordinates[i];\n      }\n      closestPoint.length = stride;\n      return squaredDistance;\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * Return the coordinate of the point.\n   * @return {import(\"../coordinate.js\").Coordinate} Coordinates.\n   * @api\n   * @override\n   */\n  getCoordinates() {\n    return this.flatCoordinates.slice();\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   * @override\n   */\n  computeExtent(extent) {\n    return createOrUpdateFromCoordinate(this.flatCoordinates, extent);\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'Point';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    return containsXY(extent, this.flatCoordinates[0], this.flatCoordinates[1]);\n  }\n\n  /**\n   * @param {!Array<*>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   * @override\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 0);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinate(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n    );\n    this.changed();\n  }\n}\n\nexport default Point;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA4BA,IAAM,OAAO,IAAI,MAAM,CAAC;AAMjB,SAAS,SAAS;AACvB,SAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1B;AAOO,SAAS,MAAM,WAAW;AAC/B,SAAO,IAAI,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC;AASO,SAAS,SAAS,YAAY,YAAY;AAC/C,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AAEvB,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK;AAC/B,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AACpC,aAAW,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AAEpC,SAAO;AACT;AAaO,SAAS,IAAI,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/C,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACf,SAAO;AACT;AAQO,SAAS,aAAa,YAAY,YAAY;AACnD,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,aAAW,CAAC,IAAI,WAAW,CAAC;AAC5B,SAAO;AACT;AAWO,SAAS,MAAM,WAAW,YAAY;AAC3C,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,aAAW,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AACjE,aAAW,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AACjE,SAAO;AACT;AAQO,SAAS,OAAO,WAAW,OAAO;AACvC,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,SAAO,SAAS,WAAW,IAAI,MAAM,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;AACjE;AASO,SAAS,MAAM,WAAW,GAAG,GAAG;AACrC,SAAO,SAAS,WAAW,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACxD;AAoBO,SAAS,UAAU,WAAW,IAAI,IAAI;AAC3C,SAAO,SAAS,WAAW,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;AAC1D;AAeO,SAAS,QAAQ,WAAW,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK;AACpE,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,YAAU,CAAC,IAAI,KAAK;AACpB,YAAU,CAAC,IAAI,KAAK;AACpB,YAAU,CAAC,IAAI,CAAC,KAAK;AACrB,YAAU,CAAC,IAAI,KAAK;AACpB,YAAU,CAAC,IAAI,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM;AACjD,YAAU,CAAC,IAAI,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM;AACjD,SAAO;AACT;AAoCO,SAAS,YAAY,QAAQ,QAAQ;AAC1C,QAAM,MAAM,YAAY,MAAM;AAC9B,SAAO,QAAQ,GAAG,0CAA0C;AAE5D,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAElB,SAAO,CAAC,IAAI,IAAI;AAChB,SAAO,CAAC,IAAI,CAAC,IAAI;AACjB,SAAO,CAAC,IAAI,CAAC,IAAI;AACjB,SAAO,CAAC,IAAI,IAAI;AAChB,SAAO,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK;AAC9B,SAAO,CAAC,IAAI,EAAE,IAAI,IAAI,IAAI,KAAK;AAE/B,SAAO;AACT;AAOO,SAAS,YAAY,KAAK;AAC/B,SAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AACzC;AAKA,IAAM,kBAAkB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAQ1C,SAAS,SAAS,KAAK;AAC5B,QAAM,kBAAkB,YAAY,IAAI,KAAK,IAAI,IAAI;AACrD,SAAO;AACT;AAOA,SAAS,WAAW,cAAc;AAChC,QAAM,SAAS,aAAa,UAAU,GAAG,aAAa,SAAS,CAAC,EAAE,MAAM,GAAG;AAC3E,SAAO,OAAO,IAAI,UAAU;AAC9B;AAQO,SAAS,WAAW,eAAe,eAAe;AACvD,QAAM,OAAO,WAAW,aAAa;AACrC,QAAM,OAAO,WAAW,aAAa;AACrC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,QAAI,KAAK,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC,MAAM,GAAG;AAC9D,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACrSO,SAAS,YACd,iBACA,QACA,KACA,QACA,WACA,MACA,mBACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,sBAAoB,oBAAoB,oBAAoB;AAC5D,MAAI,IAAI;AACR,WAAS,IAAI,QAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,UAAM,IAAI,gBAAgB,CAAC;AAC3B,UAAM,IAAI,gBAAgB,IAAI,CAAC;AAC/B,SAAK,GAAG,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AAC7D,SAAK,GAAG,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC;AAE7D,aAAS,IAAI,GAAG,IAAI,mBAAmB,KAAK;AAC1C,WAAK,GAAG,IAAI,gBAAgB,IAAI,CAAC;AAAA,IACnC;AAAA,EACF;AAEA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;AAYO,SAASA,QACd,iBACA,QACA,KACA,QACA,OACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,UAAU,OAAO,CAAC;AACxB,QAAM,UAAU,OAAO,CAAC;AACxB,MAAI,IAAI;AACR,WAAS,IAAI,QAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,UAAM,SAAS,gBAAgB,CAAC,IAAI;AACpC,UAAM,SAAS,gBAAgB,IAAI,CAAC,IAAI;AACxC,SAAK,GAAG,IAAI,UAAU,SAAS,MAAM,SAAS;AAC9C,SAAK,GAAG,IAAI,UAAU,SAAS,MAAM,SAAS;AAC9C,aAAS,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACvC,WAAK,GAAG,IAAI,gBAAgB,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;AAcO,SAASC,OACd,iBACA,QACA,KACA,QACA,IACA,IACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,QAAM,UAAU,OAAO,CAAC;AACxB,QAAM,UAAU,OAAO,CAAC;AACxB,MAAI,IAAI;AACR,WAAS,IAAI,QAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,UAAM,SAAS,gBAAgB,CAAC,IAAI;AACpC,UAAM,SAAS,gBAAgB,IAAI,CAAC,IAAI;AACxC,SAAK,GAAG,IAAI,UAAU,KAAK;AAC3B,SAAK,GAAG,IAAI,UAAU,KAAK;AAC3B,aAAS,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACvC,WAAK,GAAG,IAAI,gBAAgB,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;AAYO,SAASC,WACd,iBACA,QACA,KACA,QACA,QACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,MAAI,IAAI;AACR,WAAS,IAAI,QAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,SAAK,GAAG,IAAI,gBAAgB,CAAC,IAAI;AACjC,SAAK,GAAG,IAAI,gBAAgB,IAAI,CAAC,IAAI;AACrC,aAAS,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACvC,WAAK,GAAG,IAAI,gBAAgB,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,QAAQ,KAAK,UAAU,GAAG;AAC5B,SAAK,SAAS;AAAA,EAChB;AACA,SAAO;AACT;;;ACzHA,IAAM,eAAe,OAAgB;AAGrC,IAAM,WAAW,CAAC,KAAK,GAAG;AAc1B,IAAM,WAAN,cAAuB,eAAW;AAAA,EAChC,cAAc;AACZ,UAAM;AAMN,SAAK,UAAU,YAAY;AAM3B,SAAK,kBAAkB;AAMvB,SAAK,2CAA2C;AAMhD,SAAK,6BAA6B;AAUlC,SAAK,8BAA8B;AAAA,MACjC,CAAC,UAAU,kBAAkB,cAAc;AACzC,YAAI,CAAC,WAAW;AACd,iBAAO,KAAK,sBAAsB,gBAAgB;AAAA,QACpD;AACA,cAAM,QAAQ,KAAK,MAAM;AACzB,cAAM,eAAe,SAAS;AAC9B,eAAO,MAAM,sBAAsB,gBAAgB;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,kBAAkB,WAAW;AAC/C,WAAO,KAAK;AAAA,MACV,KAAK,YAAY;AAAA,MACjB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACN,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,GAAG,GAAG;AACf,WAAO,KAAK,eAAe,GAAG,GAAG,UAAU,OAAO,SAAS,MAAM;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,gBAAgB,OAAO,cAAc;AACnC,mBAAe,eAAe,eAAe,CAAC,KAAK,GAAG;AACtD,SAAK,eAAe,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,cAAc,QAAQ;AAC9D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB,YAAY;AAC/B,WAAO,KAAK,WAAW,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,QAAQ;AACpB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,QAAI,KAAK,mBAAmB,KAAK,YAAY,GAAG;AAC9C,YAAMC,UAAS,KAAK,cAAc,KAAK,OAAO;AAC9C,UAAI,MAAMA,QAAO,CAAC,CAAC,KAAK,MAAMA,QAAO,CAAC,CAAC,GAAG;AACxC,4BAAoBA,OAAM;AAAA,MAC5B;AACA,WAAK,kBAAkB,KAAK,YAAY;AAAA,IAC1C;AACA,WAAO,eAAe,KAAK,SAAS,MAAM;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAO,QAAQ;AACpB,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,IAAI,IAAI,QAAQ;AACpB,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,WAAW;AAClB,WAAO,KAAK,sBAAsB,YAAY,SAAS;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,kBAAkB;AACtC,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,aAAa;AAC1B,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACvB,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,QAAQ,QAAQ;AACxB,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,UAAU,QAAQ,aAAa;AAE7B,UAAM,aAAa,IAAc,MAAM;AACvC,UAAM,cACJ,WAAW,SAAS,KAAK,gBACrB,SAAU,eAAe,gBAAgB,QAAQ;AAC/C,YAAM,cAAc,WAAW,UAAU;AACzC,YAAM,kBAAkB,WAAW,eAAe;AAClD,YAAMC,SAAQ,UAAU,eAAe,IAAI,UAAU,WAAW;AAChE;AAAA,QACE;AAAA,QACA,gBAAgB,CAAC;AAAA,QACjB,gBAAgB,CAAC;AAAA,QACjBA;AAAA,QACA,CAACA;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA,cAAc;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,gBAAgB,aAAa,YAAY,WAAW;AAC1D,UAAI,eAAe;AACjB,eAAO,cAAc,aAAa,aAAa,MAAM;AAAA,MACvD;AACA,aAAO;AAAA,IACT,IACA,aAAa,YAAY,WAAW;AAC1C,SAAK,eAAe,WAAW;AAC/B,WAAO;AAAA,EACT;AACF;AAEA,IAAO,mBAAQ;;;AC1Uf,IAAM,iBAAN,cAA6B,iBAAS;AAAA,EACpC,cAAc;AACZ,UAAM;AAMN,SAAK,SAAS;AAMd,SAAK,SAAS;AAMd,SAAK;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,QAAQ;AACpB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB;AACnB,WAAO,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAClB,WAAO,KAAK,gBAAgB;AAAA,MAC1B,KAAK,gBAAgB,SAAS,KAAK;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,kBAAkB;AACtC,QAAI,KAAK,+BAA+B,KAAK,YAAY,GAAG;AAC1D,WAAK,2CAA2C;AAChD,WAAK,6BAA6B,KAAK,YAAY;AAAA,IACrD;AAGA,QACE,mBAAmB,KAClB,KAAK,6CAA6C,KACjD,oBAAoB,KAAK,0CAC3B;AACA,aAAO;AAAA,IACT;AAEA,UAAM,qBACJ,KAAK,8BAA8B,gBAAgB;AACrD,UAAM,4BAA4B,mBAAmB,mBAAmB;AACxE,QAAI,0BAA0B,SAAS,KAAK,gBAAgB,QAAQ;AAClE,aAAO;AAAA,IACT;AAOA,SAAK,2CAA2C;AAChD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,kBAAkB;AAC9C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,QAAQ,iBAAiB;AAC1C,SAAK,SAAS,mBAAmB,MAAM;AACvC,SAAK,SAAS;AACd,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,aAAa,QAAQ;AAClC,aAAS;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ,aAAa,SAAS;AACtC,QAAI;AACJ,QAAI,QAAQ;AACV,eAAS,mBAAmB,MAAM;AAAA,IACpC,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,YAAI,YAAY,WAAW,GAAG;AAC5B,eAAK,SAAS;AACd,eAAK,SAAS;AACd;AAAA,QACF;AACA;AAAA,QAA6C,YAAY,CAAC;AAAA,MAC5D;AACA,eAAS,YAAY;AACrB,eAAS,mBAAmB,MAAM;AAAA,IACpC;AACA,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,eAAe,aAAa;AAC1B,QAAI,KAAK,iBAAiB;AACxB;AAAA,QACE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,OAAO,WAAW,KAAK,IAAI,IAAI;AAAA,QACpC,KAAK;AAAA,MACP;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAO,QAAQ;AACpB,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,iBAAiB;AACnB,YAAM,SAAS,KAAK,UAAU;AAC9B,MAAAC;AAAA,QACE;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,IAAI,IAAI,QAAQ;AACpB,QAAI,OAAO,QAAW;AACpB,WAAK;AAAA,IACP;AACA,QAAI,CAAC,QAAQ;AACX,eAAS,UAAU,KAAK,UAAU,CAAC;AAAA,IACrC;AACA,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,iBAAiB;AACnB,YAAM,SAAS,KAAK,UAAU;AAC9B,MAAAC;AAAA,QACE;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,QAAQ,QAAQ;AACxB,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,QAAI,iBAAiB;AACnB,YAAM,SAAS,KAAK,UAAU;AAC9B,MAAAC;AAAA,QACE;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;AAMO,SAAS,mBAAmB,QAAQ;AACzC,MAAI;AACJ,MAAI,UAAU,GAAG;AACf,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS;AAAA,EACX,WAAW,UAAU,GAAG;AACtB,aAAS;AAAA,EACX;AACA;AAAA;AAAA,IAA8D;AAAA;AAChE;AAMO,SAAS,mBAAmB,QAAQ;AACzC,MAAI;AACJ,MAAI,UAAU,MAAM;AAClB,aAAS;AAAA,EACX,WAAW,UAAU,SAAS,UAAU,OAAO;AAC7C,aAAS;AAAA,EACX,WAAW,UAAU,QAAQ;AAC3B,aAAS;AAAA,EACX;AACA;AAAA;AAAA,IAA8B;AAAA;AAChC;AAQO,SAAS,gBAAgB,gBAAgB,WAAW,MAAM;AAC/D,QAAM,kBAAkB,eAAe,mBAAmB;AAC1D,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,eAAe,UAAU;AACxC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAO,yBAAQ;;;AC3VR,SAAS,kBAAkB,iBAAiB,QAAQ,YAAY,QAAQ;AAC7E,WAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,oBAAgB,QAAQ,IAAI,WAAW,CAAC;AAAA,EAC1C;AACA,SAAO;AACT;AASO,SAAS,mBACd,iBACA,QACA,aACA,QACA;AACA,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,UAAM,aAAa,YAAY,CAAC;AAChC,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,sBAAgB,QAAQ,IAAI,WAAW,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,SAAO;AACT;AAUO,SAAS,wBACd,iBACA,QACA,cACA,QACA,MACA;AACA,SAAO,OAAO,OAAO,CAAC;AACtB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrD,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA,aAAa,CAAC;AAAA,MACd;AAAA,IACF;AACA,SAAK,GAAG,IAAI;AACZ,aAAS;AAAA,EACX;AACA,OAAK,SAAS;AACd,SAAO;AACT;AAUO,SAAS,6BACd,iBACA,QACA,eACA,QACA,OACA;AACA,UAAQ,QAAQ,QAAQ,CAAC;AACzB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AACtD,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA,cAAc,CAAC;AAAA,MACf;AAAA,MACA,MAAM,CAAC;AAAA,IACT;AACA,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,CAAC,IAAI;AAAA,IACZ;AACA,UAAM,GAAG,IAAI;AACb,aAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,QAAM,SAAS;AACf,SAAO;AACT;;;AC1FA,IAAM,QAAN,MAAM,eAAc,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,YAAY,aAAa,QAAQ;AAC/B,UAAM;AACN,SAAK,eAAe,aAAa,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,QAAQ,IAAI,OAAM,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AACjE,UAAM,gBAAgB,IAAI;AAC1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,UAAM,kBAAkB,KAAK;AAC7B,UAAMC,mBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA,gBAAgB,CAAC;AAAA,MACjB,gBAAgB,CAAC;AAAA,IACnB;AACA,QAAIA,mBAAkB,oBAAoB;AACxC,YAAM,SAAS,KAAK;AACpB,eAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,qBAAa,CAAC,IAAI,gBAAgB,CAAC;AAAA,MACrC;AACA,mBAAa,SAAS;AACtB,aAAOA;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB;AACf,WAAO,KAAK,gBAAgB,MAAM;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,QAAQ;AACpB,WAAO,6BAA6B,KAAK,iBAAiB,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,WAAO,WAAW,QAAQ,KAAK,gBAAgB,CAAC,GAAG,KAAK,gBAAgB,CAAC,CAAC;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,aAAa,QAAQ;AAClC,SAAK,UAAU,QAAQ,aAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,gBAAQ;", "names": ["rotate", "scale", "translate", "extent", "scale", "rotate", "scale", "translate", "squaredDistance"]}