import { useTranslate } from '@/locales/use-locales';
import { PlaceholderText } from '@/modules/home/<USER>/search-by-capability/enums/searchEnum';
import { LocationRecord } from '@/shared/models';
import { getIcon } from '@/shared/utils/get-icon';
import { Autocomplete, Box, CircularProgress, InputAdornment, TextField, Typography } from '@mui/material';
import React from 'react';
import SvgColor from '../svg-color';
import CustomDropdownLocations from './custom-dropdown-components/custom-dropdown-locations';
import { CustomAutocompleteLocationSearchDropdownProps } from './models/searchModel';

const LocationSearchAutocomplete: React.FC<CustomAutocompleteLocationSearchDropdownProps> = ({
  searchResults,
  handleSearch,
  handleSelect,
  searching,
  inputValue,
  openDropdown,
  setOpenDropdown,
  statusColors,
  // only above props are receiving
  maxHeight = '300px',
  iconSize = 35,
  chipLabelStyle = {
    borderRadius: '5px',
    px: 1,
    fontSize: 12,
  },
  textFieldStyle = {
    background: '#fff',
    width: { xs: '90%', sm: 500, md: 700 }, // Prevents excessive shrinking
    minWidth: 300, // Ensures it doesn't get too small
    borderRadius: '50px',
    '& .MuiOutlinedInput-root': {
      borderRadius: '50px',
      paddingLeft: '25px',
      paddingY: '17px',
    },
  },
  paperStyle = {
    width: { xs: '90%', sm: 500, md: 700 },
    minWidth: 300, // Ensures dropdown width matches input field
    borderRadius: 2,
    boxShadow: 3,
    p: 1,
  },
  listItemStyle = { p: 1.5, boxShadow: 1 },
  noResultsTextStyle = { textAlign: 'center', p: 2, color: 'text.secondary' },
  loadingContainer = { display: 'flex', justifyContent: 'center', alignItems: 'center', p: 2 },
}) => {
  const { t } = useTranslate();

  return (
    <Autocomplete
      freeSolo
      options={searchResults}
      getOptionLabel={(option: any) => option?.locationName} // Dynamically get the label
      onInputChange={(_, newValue) => handleSearch(newValue)}
      // inputValue={inputValue}
      open={inputValue ? openDropdown : false}
      onOpen={() => setOpenDropdown(true)}
      onClose={() => setOpenDropdown(false)}
      onChange={(_, value) => {
        handleSelect(value as any);
      }}
      disableClearable
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder={t(PlaceholderText.Location)}
          variant="outlined"
          fullWidth
          sx={{ ...textFieldStyle }}
          slotProps={{
            input: {
              ...params.InputProps, // Preserve original behavior
              startAdornment: (
                <InputAdornment position="start">
                  <SvgColor
                    sx={{
                      width: 35,
                      height: 35,
                      opacity: 1,
                    }}
                    src={getIcon('searchIcon')}
                  />
                </InputAdornment>
              ),
              onKeyDown: (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                if (e.key === 'Enter') {
                  e.stopPropagation(); //  Prevents Enter from closing dropdown
                }
              },
            },
          }}
        />
      )}
      slots={{
        paper: (props) => (
          <Box {...props} sx={{ ...paperStyle }}>
            {searching ? (
              <Box sx={{ ...loadingContainer }}>
                <CircularProgress size={24} />
                <Typography sx={{ marginLeft: 1 }}>{t('label.searching')}</Typography>
              </Box>
            ) : inputValue && searchResults.length === 0 ? (
              <Typography sx={{ ...noResultsTextStyle }}>{t(PlaceholderText.LocationNotFound)}</Typography>
            ) : (
              props.children
            )}
          </Box>
        ),
      }}
      filterOptions={(options, { inputValue }) =>
        options.filter((option) => {
          const searchableText = [option.locationName, option?.countryDetail?.entityTitle, option?.countryDetail?.entityCode]
            .filter(Boolean)
            .join(' ')
            .toLowerCase();

          return searchableText.includes(inputValue.toLowerCase());
        })
      }
      renderOption={(props, option: LocationRecord) => (
        // Custom Dropdown Component can be Rendered Here
        <CustomDropdownLocations
          props={props}
          option={option}
          key={option.id}
          getIconPath={getIcon}
          iconSize={iconSize}
          listItemStyle={listItemStyle}
          chipLabelStyle={chipLabelStyle}
          statusColors={statusColors}
          showChips={true}
        />
      )}
    />
  );
};

export default LocationSearchAutocomplete;
