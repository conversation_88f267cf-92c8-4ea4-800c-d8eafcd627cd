import { lazy } from 'react';
import { Route, Routes } from 'react-router-dom';

const PeopleProfile = lazy(() => import('../views/people-profile'));
const PageNotFound = lazy(() => import('@/components/error/not-found-view'));

const PeopleProfileRoutes = () => {
  return (
    <Routes>
      <Route path="" element={<PeopleProfile />} />
      <Route path="/*" element={<PageNotFound />} />
    </Routes>
  );
};

export default PeopleProfileRoutes;
