import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { CountryWithEntityResponseDto, LegalEntityDropdownResponseDto, LegalEntitySetupRequestDto, NewCountrySetupRequestDto } from '../dtos';
import { CountryService, LegalEntityService } from '../services';
export declare class CountryController {
    private readonly countryService;
    private readonly legalEntityService;
    constructor(countryService: CountryService, legalEntityService: LegalEntityService);
    getCountryDetail(entityId: number): Promise<CountryWithEntityResponseDto>;
    upsertCountry(request: RequestContext, newCountrySetupRequestDto: NewCountrySetupRequestDto): Promise<MessageResponseDto>;
    upsertLegalEntity(request: RequestContext, legalEntitySetupRequestDto: LegalEntitySetupRequestDto): Promise<MessageResponseDto>;
    deleteLegalEntity(request: RequestContext, legalEntityId: number): Promise<MessageResponseDto>;
    getAllLegalEntity(countryId: number, searchTerm?: string): Promise<LegalEntityDropdownResponseDto[]>;
}
