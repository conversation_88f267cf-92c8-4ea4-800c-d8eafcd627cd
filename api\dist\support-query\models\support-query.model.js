"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupportQuery = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
let SupportQuery = class SupportQuery extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'url', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], SupportQuery.prototype, "url", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'query', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], SupportQuery.prototype, "query", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'status',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.SUPPORT_QUERY_STATUS_ENUM)),
        allowNull: false,
        defaultValue: enums_1.SUPPORT_QUERY_STATUS_ENUM.NEW,
    }),
    __metadata("design:type", String)
], SupportQuery.prototype, "status", void 0);
SupportQuery = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'data_support_queries',
    })
], SupportQuery);
exports.SupportQuery = SupportQuery;
//# sourceMappingURL=support-query.model.js.map