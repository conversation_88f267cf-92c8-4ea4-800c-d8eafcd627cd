"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HISTORY_ENTITY_TYPE = void 0;
var HISTORY_ENTITY_TYPE;
(function (HISTORY_ENTITY_TYPE) {
    HISTORY_ENTITY_TYPE["HIERARCHY_CONTACT"] = "CONTACT";
    HISTORY_ENTITY_TYPE["COUNTRY"] = "COUNTRY";
    HISTORY_ENTITY_TYPE["LEGAL_ENTITY"] = "LEGAL_ENTITY";
    HISTORY_ENTITY_TYPE["LOCATION"] = "LOCATION";
    HISTORY_ENTITY_TYPE["CAPABILITY"] = "CAPABILITY";
    HISTORY_ENTITY_TYPE["MASTER_CAPABILITY"] = "MASTER_CAPABILITY";
    HISTORY_ENTITY_TYPE["MASTER_EVIDENCE"] = "MASTER_EVIDENCE";
    HISTORY_ENTITY_TYPE["EXPORT"] = "EXPORT";
    HISTORY_ENTITY_TYPE["METADATA"] = "METADATA";
    HISTORY_ENTITY_TYPE["REPORT"] = "REPORT";
})(HISTORY_ENTITY_TYPE = exports.HISTORY_ENTITY_TYPE || (exports.HISTORY_ENTITY_TYPE = {}));
//# sourceMappingURL=history-entity-type.enum.js.map