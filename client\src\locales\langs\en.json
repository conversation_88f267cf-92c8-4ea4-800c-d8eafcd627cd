{"project_name": "Project Name", "role_management": "Role Management", "dashboard": "Dashboard", "updating": "Updating", "contact": "Contact", "branches": "Branches", "country_detail": "Country Detail", "contact_person": "Contact Person", "country_setup": "Country Setup", "location_information": "Location Information", "hierarchy": "Hierarchy", "geographical_hierarchy": "Geographical Hierarchy", "geographical_levels": "Geographical Levels", "location_hierarchy": "Location", "geostructure": "Geostructure", "location_type": "Location Type", "location_setup": "Geographical Hierarchy", "core_solutions": "Capabilities", "core_solution": "Capability", "legal_entity": "Legal Entity", "location_life_cycle": "Location Lifecycle Management", "submitting": "Submitting", "downloading": "Downloading", "loading": "Loading", "deleting": "Deleting", "general_information": "General Information", "finance_information": "Finance Information", "basic_information": "Basic Information", "contact_details": "Contact Details", "partner_branches": "Partner Branches", "agent_details": "Agent Details", "hr_details": "HR Detaills", "competence_detail": "Competency Details", "user_access": "User Access", "other_details": "Other Details", "additional_details": "Additional Details", "contract_details": "Contract Details", "additional_registration_numbers": "Additional Registration Numbers", "nvocc_registration": "NVOCC Registration(s)", "country_management_detail": "Country Management Detail", "industry_verticle": "Industry Vertical", "additional_business_information": "Additional Business Information", "exporting": "Exporting", "capability_export_success_msg": "Entry list exported successfully!", "location_export_success_msg": "Location list exported successfully!", "location_capability_export_success_msg": "Location Entry list exported successfully!", "demo": {"title": "English"}, "dropdowns": "dropdowns", "label": {"global": "Global Report", "custom": "Personal Report", "reports": "Reports", "report": "Report", "map": "Map", "partner": "Partner", "update_location_status": "Update Status", "add_new_partner": "Add New Partner", "country": "Country", "confirm": "Confirm", "basic_information": "Basic Information", "location_information": "Location Information", "contact_details": "Contact Details", "competency_details": "Competency Details", "agent_details": "Agent Details", "contract_details": "Contract Details", "llfc": "Location Life Cycle Management", "additional_details": "Additional Details", "search": "Search", "contact": "Contact", "expert_users": "Expert Users", "experts": "Experts", "search_by_core_solutions": "Search By Capabilities", "search_by_verticals": "Search By Verticals", "additional": "Additional", "compound_rate": "Compound Rate", "add": "Add", "partnerLocation": "Partner Location", "partnerRelationship": "Partner Relationship", "brands": "Brands", "sustainability_experts": "Sustainability Experts", "no_results_found": "No result found", "searching": "Searching...", "search_by_capability_name": "Search By Entry Name", "search_by_location": "Search By Location", "capability_not_found": "Entry not found", "location_not_found": "Location not found", "sustainability_contacts": "Sustainability Contacts", "sustainabilty": "Sustainability", "industry_verticle": "Industry Vertical", "location_id_type": "Location ID Type", "location_id_value": "Location ID Value", "google_listing_status": "Google Listing Status", "google_listing_link": "Google Listing Link", "tiab": "TiaB", "tom_ops": "TOM (OPS)", "tom_fin": "TOM (FIN)", "ffw_FTE": "Minimum 3 FFW FTE in Place", "tiabSince": "TiaB Since", "wave": "Wave", "activation_stage": "Activation Stage", "customer": "Customer", "system_details": "System Details", "current": "Current", "branch_archtype": "Branch Archetype", "modify": "Modify", "currency": "<PERSON><PERSON><PERSON><PERSON>", "business_currency": "Business Currency", "title": "Title", "dpw_id": "DPW ID", "id": "ID", "loginId": "Login ID", "note": "Note", "partner_location": "Partner Location", "partner_relationship": "Partner Relationship", "contract_type": "Contract Type", "contract_expiration_date": "Contract/Lease Expiration Date", "cost_center": "Cost Center", "terminal_code": "Terminal Code", "lo_code": "Lo Code", "label_side": "Label Side", "company_code": "Company Code", "longitude": "Longitude", "latitude": "Latitude", "is_google_listing": "Is Google Listing", "is_ad_search": "Is active directory search", "enable_escalation": "Enable Escalation", "unlocode": "UNLOCODE", "yes": "Yes", "no": "No", "location_name": "Location Name", "user_summary": "User Summary", "roles": "Roles", "escalation_level": "Escalation Level", "primary_contact": "Primary Contact", "role": "Role", "phone": "Phone", "search_user": "Search User", "created_on": "Created On", "updated_on": "Updated On", "created_by": "Created By", "updated_by": "Updated By", "email": "Email", "user_list": "User List", "delete": "Delete", "app": "app", "user": "user", "list": "List", "name": "Name", "edit": "Edit", "view": "View", "post": "post", "lists": "lists", "create": "Create", "new": "New", "update": "Update", "detail": "Detail", "profile": "profile", "account": "account", "details": "details", "home": "Home", "master_setup": "Geographical Hierarchy", "firstName": "First Name", "lastName": "Last Name", "capability_search": "Entries Search", "locations": "Locations", "jobTitle": "Job Title", "description": "Description", "required": "Required", "status": "Status", "status_date": "Status Date", "owner": "Owner Email <PERSON>", "co_owner": "Co-owner Email IDs", "evidence_description": "Evidence Description", "technology_level": "Technology Level", "provider": "Provider", "provider_name": "Provider Name", "software_provider_name": "Software Provider Name", "system_technology_version_number": "Version Number", "system_technology_name": "System/Technology Name", "system_technology_owner": "System/Technology Owner", "contract_agreement_license": "Contract/Agreement/License/Subscription", "capabilities": "Entries", "add_capabilities": "Add Entries", "add_capability": "Add Entry", "address": "Address", "number": "Number", "currency_conversion": "Currency Conversion", "currency_detail": "<PERSON><PERSON><PERSON><PERSON>", "billing_address": "Billing Address", "legal_entity_names": "Name", "legal_entity_name": "Legal Entity Name", "lease_ownership_status": "Lease Ownership Status", "strategic_classification": "Strategic Classification", "location_status": "Location Status", "comments": "Comments", "comment": "Comment", "status_comment": "Status Comment", "planned_date": "Planned Date", "operation_start_date": "Operation Start Date", "branch": "Branch", "product": "Product Family", "poc": "POC", "vendor_code": "Vendor Code", "agent_agreement_expiration_date": "Agent Agreement Expiration Date", "agent_website": "Agent Website", "agent_agreement": "Agent Agreements", "uploaded_agreements": "Uploaded Agreements", "dpw_headcount": "DPW Headcount", "ad_search": "Active Directory", "manual": "Manual", "primary": "Primary", "agency_headcount": "Agency/Temp Headcount", "total_headcount": "Total Headcount", "shift_pattern": "Shift Pattern", "language": "Language(s)", "billing_contact": "Billing Contact / Account Name", "bank_name": "Bank Name", "location_size": "Location Size", "sqm": "(SQM)", "sqft": "(SQFT)", "juristiction": "Juristiction of Incorporation", "bank_address": "Bank Address", "vat_code": "VAT Code", "registration_date": "Registration Date", "business_registration_numbers": "Business Registration Number(s)", "bic_code": "SWIFT/BIC Code", "additional_accounts": "Additional Accounts", "accounts": "Accounts", "IBAN": "IBAN", "account_type": "Account Type", "additional_IBAN": "Additional IBAN/Account Number", "primary_IBAN": "Primary IBAN/Account Number", "additional_currencies": "Additional Currencies", "business_currencies": "Business Currencies", "useful_links": "Useful Links", "added_useful_links": "Added Useful Links", "entity_type": "Entity Type", "tags": "Tags", "timezones": "Timezone(s)", "introduction": "Introduction", "documents_upload": "Documents Upload", "uploaded_documents": "Documents Uploaded", "location": "Location", "location_type": "Location Type", "capability_type": "Entry Type", "date": "Date", "core_solution": "Capability", "operational_level": "Operational Level", "document": "Document", "documents": "Documents", "capability_status": "Entry Status", "issuing_authority": "Issuing Authority", "expiration_date": "Expiration Date", "instructions": "Instructions", "mandatory_documents": "Mandatory Documents (Name List)", "handling_procedures": "Handling Procedures", "rules_and_regulations": "Rules and Regulations", "comments_notes": "Comments/Notes", "additional_documents": "Additional Documents", "addtional_contact_details": "Additional Contact Details", "setup": "Setup", "action": "Action", "evidence_upload": "Evidence Upload", "uploaded_files": "Uploaded Files", "sop_upload": "SOP Upload", "sop_documents": "SOP Documents", "location_setup": "Geographical Hierarchy", "user_access": "User Access", "location_detail": "Location Detail", "group_name": "Group Name", "search_location": "Search location by address", "capability_can_not_be_added_on_this_location": "Location is not allowed to acquire entry.", "col_location": "Location", "col_user_name": "User Name", "col_group_name": "Group Name", "col_action": "Action", "category": "Category", "sub_category": "Product", "level": "Level", "browse": "browse", "through_your_machine": "through your machine", "upload": "Upload", "remove_all": "Remove All", "drop_documnet_here_or_click": "Drop document here or click", "drop_or_select_document": "Drop or Select document", "list_per_page": "List per page", "copied": "<PERSON>pied", "copy_to_clipboard": "Copy to clipboard", "evidence_value": "Evidence value", "additional_contact_details": "Additional Contact Details", "click_to_download": "Click to download", "details_with_cap_d": "Details", "lease_expiration_date": "Lease Expiration Date", "branch_archetype": "Branch Archetype", "industry_verticals": "Industry Verticals", "filters": "Filters", "clear_filters": "Clear Filters", "show_filter": "Show Filter", "hide_filter": "<PERSON><PERSON>lter", "applied_filters": "Applied Filters", "code": "Code", "select_options": "Select an Option", "capability_name": "Entry Name", "capability_level": "Entry Level", "verticals": "Verticals", "view_capability": "Entry Detail", "view_location": "Location Detail", "edit_capability": "Edit Details", "edit_country": "Edit Country Details", "all": "All", "delete_capability": "Delete Entry", "view_details": "Location Detail", "edit_location": "Edit Details", "available_capabilities": "Available Entries", "delete_location": "Delete Location", "evidence": "Evidence", "all_capabilities": "All Entries", "type": "Type", "lease_ownership": "Lease Ownership", "bingo_card": "Bingo Card", "flat_view": "Flat View", "heirarchical_view": "Heirarchical View", "legs": "Legs", "analytics": "Analytics", "quick_access": "Quick Access", "show_available": "Show Available", "status_overview": "Status Overview", "notifications": "Notifications", "update_status": "Update Status", "view_history": "View History", "workflow": "Workflow", "edit_status": "Edit Status", "history": "History", "by": "By", "not_available": "Not Available", "master_capabilities": "Master Entry", "setup_evidence": "New Evidence", "isEvidenceMandatory": "Is Evidence Man<PERSON>tory? *", "entry_name": "Entry Name", "setup_master_capability": "Setup New Entry", "evidence_name": "Evidence Name", "evidence_type": "Evidence Type", "dismiss_notifications": "Dismiss Notifications", "dismiss": "<PERSON><PERSON><PERSON>", "setup_metadata": "<PERSON><PERSON>", "offset": "Offset", "query": "Query", "raise_a_query": "Raise a Query"}, "placeholder": {"select": "Select", "select_core_solution": "Select capability", "select_business_entity": "Select business entity", "filter": "Filter", "search": "Search", "search_by_capability_name": "Search By Entry Name", "please_enter": "Please enter", "your_query": "your query", "enter_title": "Enter the title of the person", "enter_jobTitle": "Enter the Job title of the person", "enter_email": "Enter the email of the person", "enter_role": "Enter the role of the person", "enter_phone": "Enter the phone of the person", "enter_first_name": "Enter the First name of the person", "enter_last_name": "Enter the Last name of the person", "please_find_legal_entity_name": "Please find a legal entity names", "location": "Location", "owner": "Owner", "co_owner": "Co-Owner"}, "btn_name": {"new_report": "New Report", "submit": "Submit", "next": "Next", "edit_details": "Edit Details", "confirm": "Confirm", "previous": "Previous", "processing": "Processing...", "save": "Save", "cancel": "Cancel", "update": "Update", "close": "Close", "setup_now": "Setup Now", "update_location_status": "Update Status", "setup_country_now": "Setup Country now", "new_user": "New User", "add_more": "Add More", "new_connection": "New Connection", "add_contact": "Add Contact", "back": "Back", "edit": "Edit", "add_legal_entity": "Add Legal Entity", "search": "Search", "reset": "Reset", "add_user_access": "Add User Access", "add_capability": "Add Entry", "export": "Export", "location_setup": "Location Setup", "accept": "Accept", "decline": "Decline", "clear_all": "Clear All", "send": "Send"}, "error_messages": {"missing_required_fields": "Missing required fields. Check and try again", "permission_error": "Access Denied", "contact_selected": "Contact needs to be selected", "default_language_required": "Default language value is required", "something_went_wrong": "An unexpected error occurred. Please try again later.", "something_went_wrong_business_units": "Something went wrong while fetching Business Units!", "something_went_wrong_capabilities": "Something went wrong while fetching Capabilities", "something_went_wrong_location_capabilities": "Something went wrong while location Capabilities", "no_analytics": "Analytics Not Found", "email_required": "Email is required", "valid_email": "Enter valid email address", "query_required": "Enter your query to submit"}, "messages": {"report_updated_successfully": "Report Updated successfully!", "keep_existing_entries": "Do you want to keep the existing entries linked to this location?", "only_unique_tags": "Only unique tags are allowed", "view_industry_experts": "View industry experts", "location_not_found": "Location not found", "entity_not_found": "No data available", "tiab_greater_then_oct_2023": "TiaB Since must be greater than 1 Oct,2023.", "enter_addition_number": "Please enter additional account IBAN number", "select_additional_currency": "Please Select Additional Currency", "select_escalation_level": "Please Select Escalation Level", "contact_added": "Contact added succesfully", "select_primary_currency": "Please Select Primary Currency", "enter_registration_number": "Additional registration number is required", "enter_registration_name": "Additional registration name is required", "core_solution_required": "Capability is required", "brand_required": "Atleast 1 brand is required", "customer_required": "Customer is required", "setup_country_first": "Setup Country First", "set_country_before_location": "You need to set up a country before setting up a location.", "no_contact_types_found": "No Contact types found", "no_additional_accounts_setup": "No additional accounts setup", "no_additional_registration_numbers_setup": "No additional registration numbers setup", "please_configure_contact_types": "Please Configure contact types to add contacts", "atleast_1_contact_needs_to_be_assigned": "Atleast 1 contacts needs to be added", "cannot_update_legal_entity": "Cannot update already configure capability for legal entity", "invalid_phone_format": "Invalid phone format", "location_relationship_required": "Location relationship is required", "partner_location_required": "Partner location is required", "permission_error_location": "You don't have permission to access", "no_location_access": "You don't have access to any location", "partner_relationship_required": "Partner relationship is required", "select_location": "Select a location to proceed", "use_treeview": "Use the tree view to make your selection.", "delete_confirmation": "Are you sure want to delete?", "longitude_required": "Longitude is required", "latitude_required": "Latitude is required", "location_id_value_required": "Location ID is required", "location_id_type_required": "location ID Type is required", "contact_detail_required": "Atleast 1 contact is required", "vendor_code_required": "Vendor code is required", "compound_required": "Compound rate is required", "tiab_required": "TiaB is required", "system_details_required": "System details is required", "tiab_since_required": "TiaB since is required", "wave_required": "Wave is required", "tom_fin_required": "TOM FIN is required", "tom_ops_required": "TOM OPS is required", "fte_in_place_required": "Minumum 3 FFW FTE in Place is required", "activation_stage_required": "Activation stage is required", "locationtype_required": "Location type is required", "address_required": "Address is required", "google_listing_link_required": "Google Listing Link is required", "operation_start_date_required": "Operation start date is required", "planned_date_required": "Planned date is required", "location_status_comment_required": "Location Status comment is required", "location_status_required": "Location status is required", "strategic_classification_required": "Strategic Classification is required", "lease_expiration_date_required": "Lease Expiration date is required", "locationname_required": "Location name is required", "leaseownership_required": "Lease Ownership status is required", "legalentityanem_required": "Legal entity name is required", "branch_archetype_required": "Branch Archetype is required", "multiple_delete_confirmation": "Are you sure want to delete {{count}} items?", "are_you_sure_want_to_delete": "Are you sure want to delete?", "delete_success": "Delete success!", "warning": "Warning & Confirmation", "form_reset_title": "Confirmation", "do_you_wish_to_continue": "Do you wish to continue?", "confirm_data_deletion": "Changing the location type from {{currentLocationType}} to {{newLocationType}} will permanently delete {{currentLocationType}} specific data associated with.", "entries_deletion": "You are changing the location type to {{newLocationType}}, which is not eligible to acquire capabilities. As a result, all currently available entries will be deleted.", "are_you_sure_want_to_reset_form": "This action will reset the form and discard any unsaved changes. Do you want to continue?", "setup_country": "Country is not set up yet, please set it up now.", "country_detail_not_found": "Country detail not setup", "setup_partner": "No connection available", "click_button_below": "Click button below to get started", "legalname_required": "Legal Entity Name is required", "coreSolution_required": "Capability is required", "no_files_upload": "No files uploaded", "no_contacts_found": "No contacts found", "field_is_required": "Field is required", "min_1_needs_selection": "At least one selection is required", "account_number_required": "Account Number is required", "account_currency_required": "Account Currency is required", "no_management_found": "No management found", "no_legal_entity_found": "No Legal Entity Found", "title_required": "Title is required", "role_required": "Role is required", "name_required": "Name is required", "number_required": "Number is required", "introduction_required": "Introduction is required", "language_required": "Atleast 1 language is required", "timezone_required": "Atleast 1 timezone is required", "business_currency_required": "Business currency is required", "phone_required": "Phone is required", "escalation_required": "Escalation is required", "user_required": "User is required", "email_required": "Email is required", "first_name_required": "First name is required", "last_name_required": "Last name is required", "jobTitle_required": "Job title is required", "capability_status_required": "Entry status is required", "status_date_required": "Status date is required", "owner_email_required": "Owner email is required", "description_required": "Description is required", "provider_required": "Provider is required", "provider_name_required": "Provider name is required", "expiration_date_required": "Expiration date is required", "software_provider_name_required": "Software provider name is required", "system_technology_required": "Syestem technology owner is required", "contract_agreement_license": "Contract/Agrrement/License/Subscription is required", "file_size_error": "is too large. Maximum file size allowed is 25 MB.", "file_format_error": "does not support", "access_location_required": "Location is Required", "group_name_required": "Group Name is required", "user_name_required": "User name is required", "type_something_to_search": "Type something to search", "evidence_documents_required": "Evidence document required", "evidence_value_required": "Evidence value is required", "evidence_description_required": "Evidence description is required", "are_you_sure_want_to_dismiss_notifications": "Are you sure, you want to dismiss all notifications?", "industry_expert": "Industry expert", "in": "in", "for": "for", "of": "of", "sustainability_expert": "Sustainability Expert", "location": "location", "co_owner": "Co-owner", "owner": "Owner", "contact_person": "Contact person", "entry": "entry"}, "empty_state_messages": {"no_user_found": "No User Found", "no_user_access_found": "No User Access Found", "no_document_uploaded": "No document uploaded", "no_location_found": "No Location Found", "no_location_access": "No Location access granted", "no_match_found": "No Match Found", "no_capabilities_found": "No Entries Found", "location_not_allowed_acquire_capability": "Location is not allowed to acquire entry", "location_not_found": "Location not found", "no_data_found": "No Data Found", "no_notifications_found": "No new notifications", "no_history_found": "No history found", "no_evidence_found": "No evidence found", "search_user_to_view_result": "Search user to view result", "user_has_no_assignment": "User has no assignments in the application"}, "navigations": {"check_user_access": "Check User Access"}, "headings": {"location_details": "Location Details", "filters": "Filters", "basic_info": "Basic Information", "evidence_details": "Evidence Details", "capability_details": "Entry Details", "operational_details": "Operational Details", "additional_details": "Additional Details", "capability_type_detail": "Entry Type Detail", "add_capability_search": "Setup New Entry", "master_capabilities": "Master Entries", "people_profile": "People Profile", "profile_details": "Profile Details"}}