export type CountryInfo = {
    basicInformation: {
        introduction: string;
        languages: string[];
        timezones: string[];
        usefulLinks?: string[];
    };
    currencyDetail: {
        businessCurrency: string;
        additionalCurrencies?: string[];
    };
};
export type LegalEntityOtherInfo = {
    generalInformation?: {
        billingAccountName?: string;
        billingAccountAddress?: string;
        companyCode?: string;
        registrationDate?: Date;
        juristiction?: string;
    };
    financeInformation?: {
        bankName?: string;
        bankAddress?: string;
        swiftBicCode?: string;
        primaryAccountNumber?: string;
        primaryAccountCurrency?: string;
        additionalAccountNumber?: string;
        additionalAccountCurrency?: string;
    };
    additionalBusinessInformation?: {
        businessRegistrationNumber?: string;
        vatCode?: string;
    };
    nvoccRegistration?: {
        nvocRegistrationLegalEntityName?: string;
        nvocRegistrationAddress?: string;
        nvocRegistrationPoc?: string;
        nvocRegistrationEmail?: string;
    };
    additionalRegistrationNumbers?: {
        name: string;
        number: string;
    }[];
};
