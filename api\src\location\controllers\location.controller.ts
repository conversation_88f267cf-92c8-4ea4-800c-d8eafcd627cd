import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, Res, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { PERMISSIONS } from 'src/shared/enums';
import { Permissions } from 'src/core/decorators';
import { PermissionsGuard } from 'src/core/guards';
import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { LocationService } from '../services';
import { CreateLocationRequestDto, LocationBasicDetailResponseDto, LocationCompleteDetailResponseDto, LocationDropdownResponseDto, PaginatedLocationListResponseDto, UpdateLocationStatusRequestDto } from '../dtos';
import { LocationFilterRequestDto } from '../dtos/request/location-filter-request.dto';
import { LocationCapabilitiesResponseDto } from '../dtos/response/location-capabilities.response.dto';
import { Response } from 'express';
import { ExportLocationWiseCapabilityFilterRequestDto } from 'src/capability/dtos';

@ApiTags('Location Management APIs')
@ApiBearerAuth()
@Controller('location')
export class LocationController {
    constructor(
        private readonly locationService: LocationService
    ) { }

    @Permissions(PERMISSIONS.ANY)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get location list for dropdown by location name search.',
        type: [LocationDropdownResponseDto]
    })
    @ApiQuery({
        name: 'searchTerm',
        type: String,
        description: 'Provide search term to search location by name.',
        required: true
    })
    @Get('/dropdown')
    async getAllLocations(
        @Query('searchTerm') searchTerm?: string
    ): Promise<LocationDropdownResponseDto[]> {
        return this.locationService.getAllLocations(searchTerm);
    }

    @UseGuards(AuthGuard('oauth-bearer'))
    @ApiResponse({
        status: 201,
        description: 'Add New or Update Existing location',
        type: MessageResponseDto
    })
    @Post('')
    async upsertLocation(
        @Req() request: RequestContext,
        @Body() createLocationDto: CreateLocationRequestDto
    ): Promise<MessageResponseDto> {
        return this.locationService.upsertLocation(createLocationDto, request.currentContext);
    }

    @Permissions(PERMISSIONS.ANY)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get Basic Detail of location by ID for Side Section of detail pages',
        type: LocationBasicDetailResponseDto
    })
    @Get(':id/basic-detail')
    async getBasicLocationDetailById(@Param('id') id: number): Promise<LocationBasicDetailResponseDto> {
        return this.locationService.getBasicLocationDetailById(id);
    }

    @Permissions(PERMISSIONS.ANY)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get complete detail of location by ID for detail page.',
        type: LocationCompleteDetailResponseDto
    })
    @Get(':id/complete-detail')
    async getCompleteLocationDetailById(@Param('id') id: number): Promise<LocationCompleteDetailResponseDto> {
        return this.locationService.getCompleteLocationDetailById(id);
    }

    @Permissions(PERMISSIONS.ANY)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get paginated locations by filter criteria',
        type: PaginatedLocationListResponseDto
    })
    @Post('/list')
    async getLocationsByFilter(
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 10,
        @Query('orderBy') orderBy: string = 'updatedOn',
        @Query('orderDirection') orderDirection: string = 'DESC',
        @Body() filterDto?: LocationFilterRequestDto,
    ): Promise<PaginatedLocationListResponseDto> {
        return this.locationService.getLocationListByFilter(page, limit, orderBy, orderDirection, filterDto);
    }

    @Permissions(PERMISSIONS.ANY)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Export locations to excel',
    })
    @Post('/download')
    async exportLocations(
        @Res() res: Response,
        @Req() request: RequestContext,
        @Body() filterDto?: LocationFilterRequestDto,
    ) {

        const { report, filename } = await this.locationService.exportLocations(request, filterDto);

        res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
        res.header('Access-Control-Expose-Headers', 'Content-Disposition');
        res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        return res.send(report);
    }

    @UseGuards(AuthGuard('oauth-bearer'))
    @ApiResponse({
        status: 200,
        description: 'Delete location by ID',
        type: MessageResponseDto
    })
    @Delete(':id')
    async deleteLocation(
        @Req() request: RequestContext,
        @Param('id') id: number
    ): Promise<MessageResponseDto> {
        return this.locationService.deleteLocation(id, request.currentContext);
    }

    @UseGuards(AuthGuard('oauth-bearer'))
    @ApiResponse({
        status: 200,
        description: 'Update Existing Location Status.',
        type: MessageResponseDto,
    })
    @Patch('/:id')
    public async updateLocationStatus(
        @Req() request: RequestContext,
        @Param('id') id: number,
        @Body() updateStatusRequestDto: UpdateLocationStatusRequestDto,
    ): Promise<MessageResponseDto> {
        return this.locationService.updateLocationStatus(id, updateStatusRequestDto, request.currentContext);
    }

    @Permissions(PERMISSIONS.ANY)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get capabilities (entries) list of location by ID.',
        type: LocationCapabilitiesResponseDto
    })
    @Get(':locationId/capabilities')
    async getLocationWiseCapabilities(@Param('locationId') locationId: number): Promise<LocationCapabilitiesResponseDto> {
        return this.locationService.getLocationWiseCapabilities(locationId);
    }

    @Permissions(PERMISSIONS.ANY)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Export available capabilities (entries) list of location.',
    })
    @Post(':locationId/capabilities/download')
    async exportLocationWiseCapabilities(
        @Param('locationId') locationId: number,
        @Res() res: Response,
        @Req() request: RequestContext,
        @Body() filterDto?: ExportLocationWiseCapabilityFilterRequestDto,
    ) {
        const { report, filename } = await this.locationService.exportLocationWiseCapabilities(locationId, request, filterDto);

        res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
        res.header('Access-Control-Expose-Headers', 'Content-Disposition');
        res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        return res.send(report);
    }
}
