"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationModule = void 0;
const common_1 = require("@nestjs/common");
const controllers_1 = require("./controllers");
const clients_1 = require("../shared/clients");
const services_1 = require("../shared/services");
const services_2 = require("./services");
const repositories_1 = require("./repositories");
const helpers_1 = require("../shared/helpers");
const repositories_2 = require("../contact-details/repositories");
const repositories_3 = require("../metadata/repositories");
const validators_1 = require("./validators");
const location_wise_capability_repository_1 = require("../capability/repositories/location-wise-capability.repository");
const services_3 = require("../business-entity/services");
const repositories_4 = require("../permission/repositories");
const repositories = [
    repositories_1.CountryRepository,
    repositories_2.ContactDetailRepository,
    repositories_1.LegalEntityRepository,
    repositories_3.CoreSolutionRepository,
    repositories_1.LocationIndustryVerticalRepository,
    repositories_3.LocationLifecycleManagementsTypeRepository,
    location_wise_capability_repository_1.LocationWiseCapabilityRepository,
    repositories_1.LocationRepository,
    repositories_1.PartnerBranchRepository,
    repositories_4.UserPermissionRepository,
    repositories_3.LocationTypeRepository
];
let LocationModule = class LocationModule {
};
LocationModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.CountryController, controllers_1.LocationController, controllers_1.PartnerBranchController],
        providers: [
            ...repositories,
            clients_1.AdminApiClient,
            services_1.SharedPermissionService,
            services_2.CountryService,
            services_2.LocationService,
            services_2.LegalEntityService,
            services_2.PartnerBranchService,
            helpers_1.DatabaseHelper,
            validators_1.SetupNewLocationRequest,
            services_1.SharedAttachmentService,
            clients_1.AttachmentApiClient,
            services_3.BusinessEntityService,
            services_1.ExcelSheetService,
            clients_1.HistoryApiClient
        ],
    })
], LocationModule);
exports.LocationModule = LocationModule;
//# sourceMappingURL=location.module.js.map