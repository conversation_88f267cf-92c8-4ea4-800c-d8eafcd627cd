export declare function jsonToHtmlTable(options?: {
    data?: any[];
    header?: string[];
    border?: number;
    cellspacing?: number;
    cellpadding?: number;
    table_id?: string;
    table_class?: string;
    header_mapping?: {
        [key: string]: string;
    };
    pretty?: boolean;
    css?: string;
}): string;
export declare function filterTableData(data: any, placeholderFields: string[]): any;
export declare const ReplaceUrlVariable: (url: string, variables: any) => string;
export declare const getTableCSS: () => string;
