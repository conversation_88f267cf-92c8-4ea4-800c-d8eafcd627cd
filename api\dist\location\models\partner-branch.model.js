"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerBranch = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const location_model_1 = require("./location.model");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
let PartnerBranch = class PartnerBranch extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => location_model_1.Location),
    (0, sequelize_typescript_1.Column)({ field: 'location_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], PartnerBranch.prototype, "locationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => location_model_1.Location, { foreignKey: 'locationId', targetKey: 'id' }),
    __metadata("design:type", location_model_1.Location)
], PartnerBranch.prototype, "location", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => location_model_1.Location),
    (0, sequelize_typescript_1.Column)({ field: 'partner_location_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], PartnerBranch.prototype, "partnerLocationId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => location_model_1.Location, { foreignKey: 'partnerLocationId', targetKey: 'id' }),
    __metadata("design:type", location_model_1.Location)
], PartnerBranch.prototype, "partnerLocation", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'relationship', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], PartnerBranch.prototype, "relationship", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'note', type: sequelize_typescript_1.DataType.TEXT, allowNull: true }),
    __metadata("design:type", String)
], PartnerBranch.prototype, "note", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'status',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.PARTNER_BRANCH_STATUS_TYPE)),
        allowNull: false,
        defaultValue: enums_1.PARTNER_BRANCH_STATUS_TYPE.PENDING
    }),
    __metadata("design:type", String)
], PartnerBranch.prototype, "status", void 0);
PartnerBranch = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'map_partner_branches' })
], PartnerBranch);
exports.PartnerBranch = PartnerBranch;
//# sourceMappingURL=partner-branch.model.js.map