import Box from '@mui/material/Box';
import { SxProps, Theme } from '@mui/material/styles';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import { Stack } from '@mui/system';
import SvgColor from '../svg-color';
import { ExtentionEnum, getIcon } from '@/shared/utils/get-icon';

// ----------------------------------------------------------------------

// const visuallyHidden = {
//   border: 0,
//   margin: -1,
//   padding: 0,
//   width: '1px',
//   height: '1px',
//   overflow: 'hidden',
//   position: 'absolute',
//   whiteSpace: 'nowrap',
//   clip: 'rect(0 0 0 0)',
// } as const;

// ----------------------------------------------------------------------

type Props = {
  order?: 'asc' | 'desc';
  orderBy?: string;
  headLabel: any[];
  rowCount?: number;
  numSelected?: number;
  onSort?: (id: string) => void;
  onSelectAllRows?: (checked: boolean) => void;
  sx?: SxProps<Theme>;
};

export default function TableHeadCustom({ order, orderBy, headLabel, onSort, sx }: Props) {
  return (
    <TableHead sx={sx}>
      <TableRow>
        {headLabel.map((headCell) => (
          <TableCell
            key={headCell.id}
            align={headCell.id === 'action' ? headCell.align || 'center' : headCell.align || 'left'}
            {...(orderBy == headCell.id && headCell.id != '' ? { sortDirection: order } : { sortDirection: false })}
            sx={{ width: headCell.width, minWidth: headCell.minWidth, color: '#765959' }}
          >
            {Boolean(onSort) && headCell?.enableSort ? (
              <TableSortLabel
                IconComponent={() => {
                  return order == 'asc' ? (
                    <SvgColor
                      src={getIcon('sortDown', ExtentionEnum.PNG)}
                      sx={{ width: '14px', height: '14px', cursor: 'pointer', ml: 1 }}
                      onClick={() => {}}
                    />
                  ) : (
                    <SvgColor
                      src={getIcon('sortUp', ExtentionEnum.PNG)}
                      sx={{ width: '14px', height: '14px', cursor: 'pointer', ml: 1 }}
                      onClick={() => {}}
                    />
                  );
                }}
                hideSortIcon
                active={orderBy === headCell.id}
                {...(orderBy == headCell.id && headCell.id != '' ? { direction: order } : {})}
                onClick={() => onSort && onSort(headCell.id)}
              >
                <Stack direction="row" spacing={1}>
                  <p>{headCell.label}</p>
                  {orderBy !== headCell.id && headCell.id !== '' && (
                    <Box mt={1} alignContent={'center'}>
                      <SvgColor
                        src={getIcon('SortIcon', ExtentionEnum.PNG)}
                        sx={{ width: '14px', height: '14px', cursor: 'pointer' }}
                        onClick={() => {}}
                      />
                    </Box>
                  )}
                </Stack>
              </TableSortLabel>
            ) : (
              headCell.label
            )}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}
