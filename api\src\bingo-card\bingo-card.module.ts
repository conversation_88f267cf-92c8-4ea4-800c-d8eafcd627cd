import { Modu<PERSON> } from '@nestjs/common';
import { BingoCardConfigRepository } from './repositories';
import { BingoCardService } from './services';
import { BingoCardController } from './controllers';
import { CapabilityLegRepository, CapabilityRepository } from 'src/capability/repositories';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { SharedPermissionService } from 'src/shared/services';
import { UserPermissionRepository } from 'src/permission/repositories';
import { LocationRepository } from 'src/location/repositories';

const repositories = [
	BingoCardConfigRepository,
	CapabilityRepository,
	CapabilityLegRepository,
	LocationRepository,
];

@Module({
	controllers: [BingoCardController],
	providers: [
		...repositories,
		BingoCardService,
		AdminApiClient,
		HistoryApiClient,
		SharedPermissionService,
		UserPermissionRepository,
	],
})
export class BingoCardModule {}
