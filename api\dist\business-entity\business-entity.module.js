"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessEntityModule = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../shared/clients");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
const repositories_1 = require("../location/repositories");
const repositories = [
    repositories_1.LocationRepository
];
let BusinessEntityModule = class BusinessEntityModule {
};
BusinessEntityModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.BusinessEntityController],
        providers: [services_1.BusinessEntityService, clients_1.AdminApiClient, ...repositories],
    })
], BusinessEntityModule);
exports.BusinessEntityModule = BusinessEntityModule;
//# sourceMappingURL=business-entity.module.js.map