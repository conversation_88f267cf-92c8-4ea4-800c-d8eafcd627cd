import * as msal from '@azure/msal-node';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import { MS_GRAPH_API } from '../constants';
import { HttpService } from '../services';
import { ADUserDetails, AzureADConfig, ResponseHeaders } from '../types';

@Injectable()
export class MSGraphApiClient {
	private tokenRequest: { scopes: string[] };
	private azureADConfig: AzureADConfig;

	constructor(
		@Inject(MS_GRAPH_API.MS_GRAPH_API_PROVIDER)
		private readonly msGraphApiProvider: msal.ConfidentialClientApplication,
		@Inject(MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER)
		private readonly msGraphApiHttpService: HttpService,
		@Inject(ConfigService) private readonly configService: ConfigService,
	) {
		this.azureADConfig = this.configService.getAppConfig().azureAD;
		const { graphApiUrl } = this.azureADConfig;
		this.tokenRequest = {
			scopes: [`${graphApiUrl}/.default`],
		};
	}

	private async getToken(): Promise<msal.AuthenticationResult> {
		return await this.msGraphApiProvider.acquireTokenByClientCredential(this.tokenRequest);
	}

	private async getHeaders(): Promise<ResponseHeaders> {
		const { accessToken } = await this.getToken();
		return { Authorization: `Bearer ${accessToken}`, ConsistencyLevel: 'eventual' };
	}

	public async getUserDetails(userId: string): Promise<ADUserDetails> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${userId}' OR userPrincipalName eq '${userId}'`,
			);
		return data?.value[data.value.length - 1] || null;
	}

	public async getUserDetailsByEmail(email: string): Promise<ADUserDetails> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${email}'`,
			);
		return data?.value[data.value.length - 1] || null;
	}

	public async getUsersDetails(userIds: string[]): Promise<ADUserDetails[]> {
		const headers = await this.getHeaders();
		const ids = userIds.map(id => `'${id}'`).join(',');
		const results: ADUserDetails[] = [];

		let nextLink:
			| string
			| null = `/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`;

		while (nextLink) {
			try {
				const response = await this.msGraphApiHttpService.withHeaders(headers).get(nextLink);
				const { data } = response;

				results.push(...data.value);

				nextLink =
					data['@odata.nextLink']?.replace(
						`${this.azureADConfig.graphApiUrl}/${this.azureADConfig.graphApiVersion}`,
						'',
					) || null;
			} catch (error) {
				console.error(`Failed to fetch users: ${error?.message}`, error);
				break; // Exit loop on error
			}
		}

		return results;
	}

	public async searchUsers(searchText: string, orderBy: string, count: boolean): Promise<any> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$search="displayName:${encodeURIComponent(
					searchText,
				)}" OR "mail:${encodeURIComponent(searchText)}" OR "userPrincipalName:${encodeURIComponent(
					searchText,
				)}"&$orderby=${orderBy}&$count=${count}&$filter=onPremisesExtensionAttributes%2FextensionAttribute9%20eq%20'DP%20World%20Guest'%20OR%20userType%20eq%20'Member'`,
			);
		data.value = data.value.filter(
			d => !d?.userPrincipalName?.toLowerCase()?.endsWith('au.dpworld.com'),
		);
		return data;
	}

	public async getUserDetailsInMsResponse(userId: string): Promise<any> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${userId}' OR userPrincipalName eq '${userId}'`,
			);
		return data;
	}

	public async getUsersDetailsFromAdInMsResponse(ids: string): Promise<any> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`,
			);
		return data;
	}
}
