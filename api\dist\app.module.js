"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const sequelize_1 = require("@nestjs/sequelize");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const attachments_module_1 = require("./attachment/attachments.module");
const auth_module_1 = require("./auth/auth.module");
const bingo_card_module_1 = require("./bingo-card/bingo-card.module");
const business_entity_module_1 = require("./business-entity/business-entity.module");
const capability_module_1 = require("./capability/capability.module");
const config_module_1 = require("./config/config.module");
const config_service_1 = require("./config/config.service");
const contact_detail_module_1 = require("./contact-details/contact-detail.module");
const core_module_1 = require("./core/core.module");
const interceptors_1 = require("./core/interceptors");
const logger_interceptor_1 = require("./core/interceptors/logger.interceptor");
const database_module_1 = require("./database/database.module");
const orm_config_1 = require("./database/orm-config");
const graph_user_module_1 = require("./graph-user/graph-user.module");
const history_module_1 = require("./history/history.module");
const location_module_1 = require("./location/location.module");
const metadata_modules_1 = require("./metadata/metadata.modules");
const permission_module_1 = require("./permission/permission.module");
const scheduler_module_1 = require("./scheduler/scheduler.module");
const shared_module_1 = require("./shared/shared.module");
const support_query_module_1 = require("./support-query/support-query.module");
let AppModule = class AppModule {
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            database_module_1.DatabaseModule,
            config_module_1.ConfigModule,
            core_module_1.CoreModule,
            auth_module_1.AuthModule,
            shared_module_1.SharedModule,
            business_entity_module_1.BusinessEntityModule,
            permission_module_1.PermissionModule,
            graph_user_module_1.GraphUserModule,
            attachments_module_1.AttachmentModule,
            metadata_modules_1.MetadataModule,
            contact_detail_module_1.ContactDetailModule,
            location_module_1.LocationModule,
            capability_module_1.CapabilityModule,
            history_module_1.HistoryModule,
            bingo_card_module_1.BingoCardModule,
            support_query_module_1.SupportQueryModule,
            scheduler_module_1.SchedulerModule,
            sequelize_1.SequelizeModule.forRootAsync({
                imports: [config_module_1.ConfigModule],
                useFactory: (configService) => __awaiter(void 0, void 0, void 0, function* () {
                    const { database } = configService.getAppConfig();
                    const { dialect, host, password, db, port, username, schema, enableSSL } = database;
                    return Object.assign({ dialect: dialect, host,
                        port, database: db, username,
                        password,
                        schema, logging: false }, (0, orm_config_1.getSequelizeOrmConfig)(enableSSL));
                }),
                inject: [config_service_1.ConfigService],
            }),
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            common_1.Logger,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: logger_interceptor_1.LoggingInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: interceptors_1.HttpRequestInterceptor,
            },
        ],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map