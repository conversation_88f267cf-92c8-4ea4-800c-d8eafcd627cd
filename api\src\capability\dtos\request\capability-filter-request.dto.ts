import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsEnum, IsNumber, IsOptional, IsString } from "class-validator";
import { CAPABILITY_LEVEL_ENUM, CAPABILITY_STATUS_ENUM, CAPABILITY_TYPE_ENUM, PROVIDER_ENUM } from "src/shared/enums";

export class CapabilityFilterRequestDto {

    @ApiProperty({ type: [Number], isArray: true })
    @IsArray()
    @IsOptional()
    @IsNumber({}, { each: true })
    public entityIds?: number[];

    @ApiProperty({ type: [Number], isArray: true })
    @IsArray()
    @IsOptional()
    @IsNumber({}, { each: true })
    public entryIds?: number[];

    @ApiProperty()
    @IsOptional()
    @IsString()
    public searchTerm?: string;

    @ApiProperty({ type: [CAPABILITY_TYPE_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(CAPABILITY_TYPE_ENUM, { each: true })
    public capabilityTypes?: CAPABILITY_TYPE_ENUM[];

    @ApiProperty({ type: [CAPABILITY_LEVEL_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(CAPABILITY_LEVEL_ENUM, { each: true })
    public capabilityLevel?: CAPABILITY_LEVEL_ENUM[];

    @ApiProperty({ type: [Number], isArray: true })
    @IsArray()
    @IsOptional()
    @IsNumber({}, { each: true })
    public coreSolutionIds?: number[];

    @ApiProperty({ type: [CAPABILITY_STATUS_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(CAPABILITY_STATUS_ENUM, { each: true })
    public statuses?: CAPABILITY_STATUS_ENUM[];

    @ApiProperty({ type: [PROVIDER_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(PROVIDER_ENUM, { each: true })
    public providers?: PROVIDER_ENUM[];

    @ApiProperty({ type: [String], isArray: true })
    @IsArray()
    @IsOptional()
    @IsString({ each: true })
    public verticalCodes?: string[];

    @ApiProperty()
    @IsOptional()
    @IsString()
    public ownerLoginId?: string;

    @ApiProperty({ type: Number, nullable: true })
    @IsOptional()
    @IsNumber()
    public hierarchyEntityId?: number;
}


export class ExportLocationWiseCapabilityFilterRequestDto {
    @ApiProperty()
    @IsOptional()
    @IsString()
    public searchTerm?: string;

    @ApiProperty({ type: [CAPABILITY_TYPE_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(CAPABILITY_TYPE_ENUM, { each: true })
    public capabilityTypes?: CAPABILITY_TYPE_ENUM[];

    @ApiProperty({ type: [CAPABILITY_LEVEL_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(CAPABILITY_LEVEL_ENUM, { each: true })
    public capabilityLevel?: CAPABILITY_LEVEL_ENUM[];

    @ApiProperty({ type: [CAPABILITY_STATUS_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(CAPABILITY_STATUS_ENUM, { each: true })
    public statuses?: CAPABILITY_STATUS_ENUM[];

    @ApiProperty({ type: [PROVIDER_ENUM], isArray: true })
    @IsArray()
    @IsOptional()
    @IsEnum(PROVIDER_ENUM, { each: true })
    public providers?: PROVIDER_ENUM[];

    @ApiProperty({ type: [String], isArray: true })
    @IsArray()
    @IsOptional()
    @IsString({ each: true })
    public verticalCodes?: string[];
}

