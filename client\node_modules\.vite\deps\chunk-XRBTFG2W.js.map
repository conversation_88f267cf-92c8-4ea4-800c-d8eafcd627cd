{"version": 3, "sources": ["../../@mui/material/TabScrollButton/tabScrollButtonClasses.js", "../../@mui/material/TabScrollButton/TabScrollButton.js", "../../@mui/material/Tabs/tabsClasses.js", "../../@mui/material/Tabs/Tabs.js", "../../@mui/material/internal/animate.js", "../../@mui/material/Tabs/ScrollbarSize.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabScrollButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiTabScrollButton', slot);\n}\nconst tabScrollButtonClasses = generateUtilityClasses('MuiTabScrollButton', ['root', 'vertical', 'horizontal', 'disabled']);\nexport default tabScrollButtonClasses;", "'use client';\n\n/* eslint-disable jsx-a11y/aria-role */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport KeyboardArrowLeft from \"../internal/svg-icons/KeyboardArrowLeft.js\";\nimport KeyboardArrowRight from \"../internal/svg-icons/KeyboardArrowRight.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport tabScrollButtonClasses, { getTabScrollButtonUtilityClass } from \"./tabScrollButtonClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    orientation,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, disabled && 'disabled']\n  };\n  return composeClasses(slots, getTabScrollButtonUtilityClass, classes);\n};\nconst TabScrollButtonRoot = styled(ButtonBase, {\n  name: 'MuiTabScrollButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.orientation && styles[ownerState.orientation]];\n  }\n})({\n  width: 40,\n  flexShrink: 0,\n  opacity: 0.8,\n  [`&.${tabScrollButtonClasses.disabled}`]: {\n    opacity: 0\n  },\n  variants: [{\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      width: '100%',\n      height: 40,\n      '& svg': {\n        transform: 'var(--TabScrollButton-svgRotate)'\n      }\n    }\n  }]\n});\nconst TabScrollButton = /*#__PURE__*/React.forwardRef(function TabScrollButton(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabScrollButton'\n  });\n  const {\n    className,\n    slots = {},\n    slotProps = {},\n    direction,\n    orientation,\n    disabled,\n    ...other\n  } = props;\n  const isRtl = useRtl();\n  const ownerState = {\n    isRtl,\n    ...props\n  };\n  const classes = useUtilityClasses(ownerState);\n  const StartButtonIcon = slots.StartScrollButtonIcon ?? KeyboardArrowLeft;\n  const EndButtonIcon = slots.EndScrollButtonIcon ?? KeyboardArrowRight;\n  const startButtonIconProps = useSlotProps({\n    elementType: StartButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    additionalProps: {\n      fontSize: 'small'\n    },\n    ownerState\n  });\n  const endButtonIconProps = useSlotProps({\n    elementType: EndButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    additionalProps: {\n      fontSize: 'small'\n    },\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(TabScrollButtonRoot, {\n    component: \"div\",\n    className: clsx(classes.root, className),\n    ref: ref,\n    role: null,\n    ownerState: ownerState,\n    tabIndex: null,\n    ...other,\n    style: {\n      ...other.style,\n      ...(orientation === 'vertical' && {\n        '--TabScrollButton-svgRotate': `rotate(${isRtl ? -90 : 90}deg)`\n      })\n    },\n    children: direction === 'left' ? /*#__PURE__*/_jsx(StartButtonIcon, {\n      ...startButtonIconProps\n    }) : /*#__PURE__*/_jsx(EndButtonIcon, {\n      ...endButtonIconProps\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TabScrollButton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The direction the button should indicate.\n   */\n  direction: PropTypes.oneOf(['left', 'right']).isRequired,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The component orientation (layout flow direction).\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']).isRequired,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    EndScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TabScrollButton;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass('MuiTabs', slot);\n}\nconst tabsClasses = generateUtilityClasses('MuiTabs', ['root', 'vertical', 'list', 'flexContainer', 'flexContainerVertical', 'centered', 'scroller', 'fixed', 'scrollableX', 'scrollableY', 'hideScrollbar', 'scrollButtons', 'scrollButtonsHideMobile', 'indicator']);\nexport default tabsClasses;", "'use client';\n\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport animate from \"../internal/animate.js\";\nimport ScrollbarSize from \"./ScrollbarSize.js\";\nimport TabScrollButton from \"../TabScrollButton/index.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport tabsClasses, { getTabsUtilityClass } from \"./tabsClasses.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst nextItem = (list, item) => {\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    }\n\n    // Same logic as useAutocomplete.js\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    vertical,\n    fixed,\n    hideScrollbar,\n    scrollableX,\n    scrollableY,\n    centered,\n    scrollButtonsHideMobile,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', vertical && 'vertical'],\n    scroller: ['scroller', fixed && 'fixed', hideScrollbar && 'hideScrollbar', scrollableX && 'scrollableX', scrollableY && 'scrollableY'],\n    list: ['list', 'flexContainer', vertical && 'flexContainerVertical', vertical && 'vertical', centered && 'centered'],\n    indicator: ['indicator'],\n    scrollButtons: ['scrollButtons', scrollButtonsHideMobile && 'scrollButtonsHideMobile'],\n    scrollableX: [scrollableX && 'scrollableX'],\n    hideScrollbar: [hideScrollbar && 'hideScrollbar']\n  };\n  return composeClasses(slots, getTabsUtilityClass, classes);\n};\nconst TabsRoot = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${tabsClasses.scrollButtons}`]: styles.scrollButtons\n    }, {\n      [`& .${tabsClasses.scrollButtons}`]: ownerState.scrollButtonsHideMobile && styles.scrollButtonsHideMobile\n    }, styles.root, ownerState.vertical && styles.vertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minHeight: 48,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollButtonsHideMobile,\n    style: {\n      [`& .${tabsClasses.scrollButtons}`]: {\n        [theme.breakpoints.down('sm')]: {\n          display: 'none'\n        }\n      }\n    }\n  }]\n})));\nconst TabsScroller = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Scroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.scroller, ownerState.fixed && styles.fixed, ownerState.hideScrollbar && styles.hideScrollbar, ownerState.scrollableX && styles.scrollableX, ownerState.scrollableY && styles.scrollableY];\n  }\n})({\n  position: 'relative',\n  display: 'inline-block',\n  flex: '1 1 auto',\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.fixed,\n    style: {\n      overflowX: 'hidden',\n      width: '100%'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hideScrollbar,\n    style: {\n      // Hide dimensionless scrollbar on macOS\n      scrollbarWidth: 'none',\n      // Firefox\n      '&::-webkit-scrollbar': {\n        display: 'none' // Safari + Chrome\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableX,\n    style: {\n      overflowX: 'auto',\n      overflowY: 'hidden'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableY,\n    style: {\n      overflowY: 'auto',\n      overflowX: 'hidden'\n    }\n  }]\n});\nconst List = styled('div', {\n  name: 'MuiTabs',\n  slot: 'List',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.list, styles.flexContainer, ownerState.vertical && styles.flexContainerVertical, ownerState.centered && styles.centered];\n  }\n})({\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.centered,\n    style: {\n      justifyContent: 'center'\n    }\n  }]\n});\nconst TabsIndicator = styled('span', {\n  name: 'MuiTabs',\n  slot: 'Indicator',\n  overridesResolver: (props, styles) => styles.indicator\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  height: 2,\n  bottom: 0,\n  width: '100%',\n  transition: theme.transitions.create(),\n  variants: [{\n    props: {\n      indicatorColor: 'primary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.primary.main\n    }\n  }, {\n    props: {\n      indicatorColor: 'secondary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.secondary.main\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      height: '100%',\n      width: 2,\n      right: 0\n    }\n  }]\n})));\nconst TabsScrollbarSize = styled(ScrollbarSize)({\n  overflowX: 'auto',\n  overflowY: 'hidden',\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n});\nconst defaultIndicatorStyle = {};\nlet warnedOnceTabPresent = false;\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabs'\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    action,\n    centered = false,\n    children: childrenProp,\n    className,\n    component = 'div',\n    allowScrollButtonsMobile = false,\n    indicatorColor = 'primary',\n    onChange,\n    orientation = 'horizontal',\n    ScrollButtonComponent,\n    // TODO: remove in v7 (deprecated in v6)\n    scrollButtons = 'auto',\n    selectionFollowsFocus,\n    slots = {},\n    slotProps = {},\n    TabIndicatorProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    TabScrollButtonProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    textColor = 'primary',\n    value,\n    variant = 'standard',\n    visibleScrollbar = false,\n    ...other\n  } = props;\n  const scrollable = variant === 'scrollable';\n  const vertical = orientation === 'vertical';\n  const scrollStart = vertical ? 'scrollTop' : 'scrollLeft';\n  const start = vertical ? 'top' : 'left';\n  const end = vertical ? 'bottom' : 'right';\n  const clientSize = vertical ? 'clientHeight' : 'clientWidth';\n  const size = vertical ? 'height' : 'width';\n  const ownerState = {\n    ...props,\n    component,\n    allowScrollButtonsMobile,\n    indicatorColor,\n    orientation,\n    vertical,\n    scrollButtons,\n    textColor,\n    variant,\n    visibleScrollbar,\n    fixed: !scrollable,\n    hideScrollbar: scrollable && !visibleScrollbar,\n    scrollableX: scrollable && !vertical,\n    scrollableY: scrollable && vertical,\n    centered: centered && !scrollable,\n    scrollButtonsHideMobile: !allowScrollButtonsMobile\n  };\n  const classes = useUtilityClasses(ownerState);\n  const startScrollButtonIconProps = useSlotProps({\n    elementType: slots.StartScrollButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    ownerState\n  });\n  const endScrollButtonIconProps = useSlotProps({\n    elementType: slots.EndScrollButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    ownerState\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (centered && scrollable) {\n      console.error('MUI: You can not use the `centered={true}` and `variant=\"scrollable\"` properties ' + 'at the same time on a `Tabs` component.');\n    }\n  }\n  const [mounted, setMounted] = React.useState(false);\n  const [indicatorStyle, setIndicatorStyle] = React.useState(defaultIndicatorStyle);\n  const [displayStartScroll, setDisplayStartScroll] = React.useState(false);\n  const [displayEndScroll, setDisplayEndScroll] = React.useState(false);\n  const [updateScrollObserver, setUpdateScrollObserver] = React.useState(false);\n  const [scrollerStyle, setScrollerStyle] = React.useState({\n    overflow: 'hidden',\n    scrollbarWidth: 0\n  });\n  const valueToIndex = new Map();\n  const tabsRef = React.useRef(null);\n  const tabListRef = React.useRef(null);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      indicator: TabIndicatorProps,\n      scrollButton: TabScrollButtonProps,\n      ...slotProps\n    }\n  };\n  const getTabsMeta = () => {\n    const tabsNode = tabsRef.current;\n    let tabsMeta;\n    if (tabsNode) {\n      const rect = tabsNode.getBoundingClientRect();\n      // create a new object with ClientRect class props + scrollLeft\n      tabsMeta = {\n        clientWidth: tabsNode.clientWidth,\n        scrollLeft: tabsNode.scrollLeft,\n        scrollTop: tabsNode.scrollTop,\n        scrollWidth: tabsNode.scrollWidth,\n        top: rect.top,\n        bottom: rect.bottom,\n        left: rect.left,\n        right: rect.right\n      };\n    }\n    let tabMeta;\n    if (tabsNode && value !== false) {\n      const children = tabListRef.current.children;\n      if (children.length > 0) {\n        const tab = children[valueToIndex.get(value)];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!tab) {\n            console.error([`MUI: The \\`value\\` provided to the Tabs component is invalid.`, `None of the Tabs' children match with \"${value}\".`, valueToIndex.keys ? `You can provide one of the following values: ${Array.from(valueToIndex.keys()).join(', ')}.` : null].join('\\n'));\n          }\n        }\n        tabMeta = tab ? tab.getBoundingClientRect() : null;\n        if (process.env.NODE_ENV !== 'production') {\n          if (process.env.NODE_ENV !== 'test' && !warnedOnceTabPresent && tabMeta && tabMeta.width === 0 && tabMeta.height === 0 &&\n          // if the whole Tabs component is hidden, don't warn\n          tabsMeta.clientWidth !== 0) {\n            tabsMeta = null;\n            console.error(['MUI: The `value` provided to the Tabs component is invalid.', `The Tab with this \\`value\\` (\"${value}\") is not part of the document layout.`, \"Make sure the tab item is present in the document or that it's not `display: none`.\"].join('\\n'));\n            warnedOnceTabPresent = true;\n          }\n        }\n      }\n    }\n    return {\n      tabsMeta,\n      tabMeta\n    };\n  };\n  const updateIndicatorState = useEventCallback(() => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    let startValue = 0;\n    let startIndicator;\n    if (vertical) {\n      startIndicator = 'top';\n      if (tabMeta && tabsMeta) {\n        startValue = tabMeta.top - tabsMeta.top + tabsMeta.scrollTop;\n      }\n    } else {\n      startIndicator = isRtl ? 'right' : 'left';\n      if (tabMeta && tabsMeta) {\n        startValue = (isRtl ? -1 : 1) * (tabMeta[startIndicator] - tabsMeta[startIndicator] + tabsMeta.scrollLeft);\n      }\n    }\n    const newIndicatorStyle = {\n      [startIndicator]: startValue,\n      // May be wrong until the font is loaded.\n      [size]: tabMeta ? tabMeta[size] : 0\n    };\n    if (typeof indicatorStyle[startIndicator] !== 'number' || typeof indicatorStyle[size] !== 'number') {\n      setIndicatorStyle(newIndicatorStyle);\n    } else {\n      const dStart = Math.abs(indicatorStyle[startIndicator] - newIndicatorStyle[startIndicator]);\n      const dSize = Math.abs(indicatorStyle[size] - newIndicatorStyle[size]);\n      if (dStart >= 1 || dSize >= 1) {\n        setIndicatorStyle(newIndicatorStyle);\n      }\n    }\n  });\n  const scroll = (scrollValue, {\n    animation = true\n  } = {}) => {\n    if (animation) {\n      animate(scrollStart, tabsRef.current, scrollValue, {\n        duration: theme.transitions.duration.standard\n      });\n    } else {\n      tabsRef.current[scrollStart] = scrollValue;\n    }\n  };\n  const moveTabsScroll = delta => {\n    let scrollValue = tabsRef.current[scrollStart];\n    if (vertical) {\n      scrollValue += delta;\n    } else {\n      scrollValue += delta * (isRtl ? -1 : 1);\n    }\n    scroll(scrollValue);\n  };\n  const getScrollSize = () => {\n    const containerSize = tabsRef.current[clientSize];\n    let totalSize = 0;\n    const children = Array.from(tabListRef.current.children);\n    for (let i = 0; i < children.length; i += 1) {\n      const tab = children[i];\n      if (totalSize + tab[clientSize] > containerSize) {\n        // If the first item is longer than the container size, then only scroll\n        // by the container size.\n        if (i === 0) {\n          totalSize = containerSize;\n        }\n        break;\n      }\n      totalSize += tab[clientSize];\n    }\n    return totalSize;\n  };\n  const handleStartScrollClick = () => {\n    moveTabsScroll(-1 * getScrollSize());\n  };\n  const handleEndScrollClick = () => {\n    moveTabsScroll(getScrollSize());\n  };\n  const [ScrollbarSlot, {\n    onChange: scrollbarOnChange,\n    ...scrollbarSlotProps\n  }] = useSlot('scrollbar', {\n    className: clsx(classes.scrollableX, classes.hideScrollbar),\n    elementType: TabsScrollbarSize,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // TODO Remove <ScrollbarSize /> as browser support for hiding the scrollbar\n  // with CSS improves.\n  const handleScrollbarSizeChange = React.useCallback(scrollbarWidth => {\n    scrollbarOnChange?.(scrollbarWidth);\n    setScrollerStyle({\n      overflow: null,\n      scrollbarWidth\n    });\n  }, [scrollbarOnChange]);\n  const [ScrollButtonsSlot, scrollButtonSlotProps] = useSlot('scrollButtons', {\n    className: clsx(classes.scrollButtons, TabScrollButtonProps.className),\n    elementType: TabScrollButton,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      orientation,\n      slots: {\n        StartScrollButtonIcon: slots.startScrollButtonIcon || slots.StartScrollButtonIcon,\n        EndScrollButtonIcon: slots.endScrollButtonIcon || slots.EndScrollButtonIcon\n      },\n      slotProps: {\n        startScrollButtonIcon: startScrollButtonIconProps,\n        endScrollButtonIcon: endScrollButtonIconProps\n      }\n    }\n  });\n  const getConditionalElements = () => {\n    const conditionalElements = {};\n    conditionalElements.scrollbarSizeListener = scrollable ? /*#__PURE__*/_jsx(ScrollbarSlot, {\n      ...scrollbarSlotProps,\n      onChange: handleScrollbarSizeChange\n    }) : null;\n    const scrollButtonsActive = displayStartScroll || displayEndScroll;\n    const showScrollButtons = scrollable && (scrollButtons === 'auto' && scrollButtonsActive || scrollButtons === true);\n    conditionalElements.scrollButtonStart = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonsSlot, {\n      direction: isRtl ? 'right' : 'left',\n      onClick: handleStartScrollClick,\n      disabled: !displayStartScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    conditionalElements.scrollButtonEnd = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonsSlot, {\n      direction: isRtl ? 'left' : 'right',\n      onClick: handleEndScrollClick,\n      disabled: !displayEndScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    return conditionalElements;\n  };\n  const scrollSelectedIntoView = useEventCallback(animation => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    if (!tabMeta || !tabsMeta) {\n      return;\n    }\n    if (tabMeta[start] < tabsMeta[start]) {\n      // left side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[start] - tabsMeta[start]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    } else if (tabMeta[end] > tabsMeta[end]) {\n      // right side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[end] - tabsMeta[end]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    }\n  });\n  const updateScrollButtonState = useEventCallback(() => {\n    if (scrollable && scrollButtons !== false) {\n      setUpdateScrollObserver(!updateScrollObserver);\n    }\n  });\n  React.useEffect(() => {\n    const handleResize = debounce(() => {\n      // If the Tabs component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/33276\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n      if (tabsRef.current) {\n        updateIndicatorState();\n      }\n    });\n    let resizeObserver;\n\n    /**\n     * @type {MutationCallback}\n     */\n    const handleMutation = records => {\n      records.forEach(record => {\n        record.removedNodes.forEach(item => {\n          resizeObserver?.unobserve(item);\n        });\n        record.addedNodes.forEach(item => {\n          resizeObserver?.observe(item);\n        });\n      });\n      handleResize();\n      updateScrollButtonState();\n    };\n    const win = ownerWindow(tabsRef.current);\n    win.addEventListener('resize', handleResize);\n    let mutationObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      Array.from(tabListRef.current.children).forEach(child => {\n        resizeObserver.observe(child);\n      });\n    }\n    if (typeof MutationObserver !== 'undefined') {\n      mutationObserver = new MutationObserver(handleMutation);\n      mutationObserver.observe(tabListRef.current, {\n        childList: true\n      });\n    }\n    return () => {\n      handleResize.clear();\n      win.removeEventListener('resize', handleResize);\n      mutationObserver?.disconnect();\n      resizeObserver?.disconnect();\n    };\n  }, [updateIndicatorState, updateScrollButtonState]);\n\n  /**\n   * Toggle visibility of start and end scroll buttons\n   * Using IntersectionObserver on first and last Tabs.\n   */\n  React.useEffect(() => {\n    const tabListChildren = Array.from(tabListRef.current.children);\n    const length = tabListChildren.length;\n    if (typeof IntersectionObserver !== 'undefined' && length > 0 && scrollable && scrollButtons !== false) {\n      const firstTab = tabListChildren[0];\n      const lastTab = tabListChildren[length - 1];\n      const observerOptions = {\n        root: tabsRef.current,\n        threshold: 0.99\n      };\n      const handleScrollButtonStart = entries => {\n        setDisplayStartScroll(!entries[0].isIntersecting);\n      };\n      const firstObserver = new IntersectionObserver(handleScrollButtonStart, observerOptions);\n      firstObserver.observe(firstTab);\n      const handleScrollButtonEnd = entries => {\n        setDisplayEndScroll(!entries[0].isIntersecting);\n      };\n      const lastObserver = new IntersectionObserver(handleScrollButtonEnd, observerOptions);\n      lastObserver.observe(lastTab);\n      return () => {\n        firstObserver.disconnect();\n        lastObserver.disconnect();\n      };\n    }\n    return undefined;\n  }, [scrollable, scrollButtons, updateScrollObserver, childrenProp?.length]);\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    updateIndicatorState();\n  });\n  React.useEffect(() => {\n    // Don't animate on the first render.\n    scrollSelectedIntoView(defaultIndicatorStyle !== indicatorStyle);\n  }, [scrollSelectedIntoView, indicatorStyle]);\n  React.useImperativeHandle(action, () => ({\n    updateIndicator: updateIndicatorState,\n    updateScrollButtons: updateScrollButtonState\n  }), [updateIndicatorState, updateScrollButtonState]);\n  const [IndicatorSlot, indicatorSlotProps] = useSlot('indicator', {\n    className: clsx(classes.indicator, TabIndicatorProps.className),\n    elementType: TabsIndicator,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: indicatorStyle\n    }\n  });\n  const indicator = /*#__PURE__*/_jsx(IndicatorSlot, {\n    ...indicatorSlotProps\n  });\n  let childIndex = 0;\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    const childValue = child.props.value === undefined ? childIndex : child.props.value;\n    valueToIndex.set(childValue, childIndex);\n    const selected = childValue === value;\n    childIndex += 1;\n    return /*#__PURE__*/React.cloneElement(child, {\n      fullWidth: variant === 'fullWidth',\n      indicator: selected && !mounted && indicator,\n      selected,\n      selectionFollowsFocus,\n      onChange,\n      textColor,\n      value: childValue,\n      ...(childIndex === 1 && value === false && !child.props.tabIndex ? {\n        tabIndex: 0\n      } : {})\n    });\n  });\n  const handleKeyDown = event => {\n    // Check if a modifier key (Alt, Shift, Ctrl, Meta) is pressed\n    if (event.altKey || event.shiftKey || event.ctrlKey || event.metaKey) {\n      return;\n    }\n    const list = tabListRef.current;\n    const currentFocus = ownerDocument(list).activeElement;\n    // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n    const role = currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const conditionalElements = getConditionalElements();\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: TabsRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    ownerState\n  });\n  const [ScrollerSlot, scrollerSlotProps] = useSlot('scroller', {\n    ref: tabsRef,\n    className: classes.scroller,\n    elementType: TabsScroller,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: {\n        overflow: scrollerStyle.overflow,\n        [vertical ? `margin${isRtl ? 'Left' : 'Right'}` : 'marginBottom']: visibleScrollbar ? undefined : -scrollerStyle.scrollbarWidth\n      }\n    }\n  });\n  const [ListSlot, listSlotProps] = useSlot('list', {\n    ref: tabListRef,\n    className: clsx(classes.list, classes.flexContainer),\n    elementType: List,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onKeyDown: event => {\n        handleKeyDown(event);\n        handlers.onKeyDown?.(event);\n      }\n    })\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [conditionalElements.scrollButtonStart, conditionalElements.scrollbarSizeListener, /*#__PURE__*/_jsxs(ScrollerSlot, {\n      ...scrollerSlotProps,\n      children: [/*#__PURE__*/_jsx(ListSlot, {\n        \"aria-label\": ariaLabel,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-orientation\": orientation === 'vertical' ? 'vertical' : null,\n        role: \"tablist\",\n        ...listSlotProps,\n        children: children\n      }), mounted && indicator]\n    }), conditionalElements.scrollButtonEnd]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Callback fired when the component mounts.\n   * This is useful when you want to trigger an action programmatically.\n   * It supports two actions: `updateIndicator()` and `updateScrollButtons()`\n   *\n   * @param {object} actions This object contains all possible actions\n   * that can be triggered programmatically.\n   */\n  action: refType,\n  /**\n   * If `true`, the scroll buttons aren't forced hidden on mobile.\n   * By default the scroll buttons are hidden on mobile and takes precedence over `scrollButtons`.\n   * @default false\n   */\n  allowScrollButtonsMobile: PropTypes.bool,\n  /**\n   * The label for the Tabs as a string.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * An id or list of ids separated by a space that label the Tabs.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the tabs are centered.\n   * This prop is intended for large views.\n   * @default false\n   */\n  centered: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Determines the color of the indicator.\n   * @default 'primary'\n   */\n  indicatorColor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary']), PropTypes.string]),\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child (number)\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The component used to render the scroll buttons.\n   * @deprecated use the `slots.scrollButtons` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default TabScrollButton\n   */\n  ScrollButtonComponent: PropTypes.elementType,\n  /**\n   * Determine behavior of scroll buttons when tabs are set to scroll:\n   *\n   * - `auto` will only present them when not all the items are visible.\n   * - `true` will always present them.\n   * - `false` will never present them.\n   *\n   * By default the scroll buttons are hidden on mobile.\n   * This behavior can be disabled with `allowScrollButtonsMobile`.\n   * @default 'auto'\n   */\n  scrollButtons: PropTypes /* @typescript-to-proptypes-ignore */.oneOf(['auto', false, true]),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    indicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    list: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scrollbar: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scrollButtons: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scroller: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.elementType,\n    EndScrollButtonIcon: PropTypes.elementType,\n    indicator: PropTypes.elementType,\n    list: PropTypes.elementType,\n    root: PropTypes.elementType,\n    scrollbar: PropTypes.elementType,\n    scrollButtons: PropTypes.elementType,\n    scroller: PropTypes.elementType,\n    startScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Props applied to the tab indicator element.\n   * @deprecated use the `slotProps.indicator` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default  {}\n   */\n  TabIndicatorProps: PropTypes.object,\n  /**\n   * Props applied to the [`TabScrollButton`](https://mui.com/material-ui/api/tab-scroll-button/) element.\n   * @deprecated use the `slotProps.scrollButtons` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TabScrollButtonProps: PropTypes.object,\n  /**\n   * Determines the color of the `Tab`.\n   * @default 'primary'\n   */\n  textColor: PropTypes.oneOf(['inherit', 'primary', 'secondary']),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.any,\n  /**\n   * Determines additional display behavior of the tabs:\n   *\n   *  - `scrollable` will invoke scrolling properties and allow for horizontally\n   *  scrolling (or swiping) of the tab bar.\n   *  - `fullWidth` will make the tabs grow to use all the available space,\n   *  which should be used for small views, like on mobile.\n   *  - `standard` will render the default state.\n   * @default 'standard'\n   */\n  variant: PropTypes.oneOf(['fullWidth', 'scrollable', 'standard']),\n  /**\n   * If `true`, the scrollbar is visible. It can be useful when displaying\n   * a long vertical list of tabs.\n   * @default false\n   */\n  visibleScrollbar: PropTypes.bool\n} : void 0;\nexport default Tabs;", "function easeInOutSin(time) {\n  return (1 + Math.sin(Math.PI * time - Math.PI / 2)) / 2;\n}\nexport default function animate(property, element, to, options = {}, cb = () => {}) {\n  const {\n    ease = easeInOutSin,\n    duration = 300 // standard\n  } = options;\n  let start = null;\n  const from = element[property];\n  let cancelled = false;\n  const cancel = () => {\n    cancelled = true;\n  };\n  const step = timestamp => {\n    if (cancelled) {\n      cb(new Error('Animation cancelled'));\n      return;\n    }\n    if (start === null) {\n      start = timestamp;\n    }\n    const time = Math.min(1, (timestamp - start) / duration);\n    element[property] = ease(time) * (to - from) + from;\n    if (time >= 1) {\n      requestAnimationFrame(() => {\n        cb(null);\n      });\n      return;\n    }\n    requestAnimationFrame(step);\n  };\n  if (from === to) {\n    cb(new Error('Element already at target position'));\n    return cancel;\n  }\n  requestAnimationFrame(step);\n  return cancel;\n}", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport debounce from \"../utils/debounce.js\";\nimport { ownerWindow, unstable_useEnhancedEffect as useEnhancedEffect } from \"../utils/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  width: 99,\n  height: 99,\n  position: 'absolute',\n  top: -9999,\n  overflow: 'scroll'\n};\n\n/**\n * @ignore - internal component.\n * The component originates from https://github.com/STORIS/react-scrollbar-size.\n * It has been moved into the core in order to minimize the bundle size.\n */\nexport default function ScrollbarSize(props) {\n  const {\n    onChange,\n    ...other\n  } = props;\n  const scrollbarHeight = React.useRef();\n  const nodeRef = React.useRef(null);\n  const setMeasurements = () => {\n    scrollbarHeight.current = nodeRef.current.offsetHeight - nodeRef.current.clientHeight;\n  };\n  useEnhancedEffect(() => {\n    const handleResize = debounce(() => {\n      const prevHeight = scrollbarHeight.current;\n      setMeasurements();\n      if (prevHeight !== scrollbarHeight.current) {\n        onChange(scrollbarHeight.current);\n      }\n    });\n    const containerWindow = ownerWindow(nodeRef.current);\n    containerWindow.addEventListener('resize', handleResize);\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n    };\n  }, [onChange]);\n  React.useEffect(() => {\n    setMeasurements();\n    onChange(scrollbarHeight.current);\n  }, [onChange]);\n  return /*#__PURE__*/_jsx(\"div\", {\n    style: styles,\n    ...other,\n    ref: nodeRef\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ScrollbarSize.propTypes = {\n  onChange: PropTypes.func.isRequired\n} : void 0;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,+BAA+B,MAAM;AACnD,SAAO,qBAAqB,sBAAsB,IAAI;AACxD;AACA,IAAM,yBAAyB,uBAAuB,sBAAsB,CAAC,QAAQ,YAAY,cAAc,UAAU,CAAC;AAC1H,IAAO,iCAAQ;;;ACHf,YAAuB;AACvB,wBAAsB;AAWtB,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,aAAa,YAAY,UAAU;AAAA,EACpD;AACA,SAAO,eAAe,OAAO,gCAAgC,OAAO;AACtE;AACA,IAAM,sBAAsB,eAAO,oBAAY;AAAA,EAC7C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOA,YAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAACA,QAAO,MAAM,WAAW,eAAeA,QAAO,WAAW,WAAW,CAAC;AAAA,EAC/E;AACF,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,CAAC,KAAK,+BAAuB,QAAQ,EAAE,GAAG;AAAA,IACxC,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,kBAAqC,iBAAW,SAASC,iBAAgB,SAAS,KAAK;AAC3F,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,QAAQ,OAAO;AACrB,QAAM,aAAa;AAAA,IACjB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,kBAAkB,MAAM,yBAAyB;AACvD,QAAM,gBAAgB,MAAM,uBAAuB;AACnD,QAAM,uBAAuB,qBAAa;AAAA,IACxC,aAAa;AAAA,IACb,mBAAmB,UAAU;AAAA,IAC7B,iBAAiB;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,qBAAqB,qBAAa;AAAA,IACtC,aAAa;AAAA,IACb,mBAAmB,UAAU;AAAA,IAC7B,iBAAiB;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,mBAAAC,KAAK,qBAAqB;AAAA,IAC5C,WAAW;AAAA,IACX,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA,UAAU;AAAA,IACV,GAAG;AAAA,IACH,OAAO;AAAA,MACL,GAAG,MAAM;AAAA,MACT,GAAI,gBAAgB,cAAc;AAAA,QAChC,+BAA+B,UAAU,QAAQ,MAAM,EAAE;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,UAAU,cAAc,aAAsB,mBAAAA,KAAK,iBAAiB;AAAA,MAClE,GAAG;AAAA,IACL,CAAC,QAAiB,mBAAAA,KAAK,eAAe;AAAA,MACpC,GAAG;AAAA,IACL,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,gBAAgB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,WAAW,kBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9C,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,aAAa,kBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzD,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,qBAAqB,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC3E,uBAAuB,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,qBAAqB,kBAAAA,QAAU;AAAA,IAC/B,uBAAuB,kBAAAA,QAAU;AAAA,EACnC,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,0BAAQ;;;ACzKR,SAAS,oBAAoB,MAAM;AACxC,SAAO,qBAAqB,WAAW,IAAI;AAC7C;AACA,IAAM,cAAc,uBAAuB,WAAW,CAAC,QAAQ,YAAY,QAAQ,iBAAiB,yBAAyB,YAAY,YAAY,SAAS,eAAe,eAAe,iBAAiB,iBAAiB,2BAA2B,WAAW,CAAC;AACrQ,IAAO,sBAAQ;;;ACJf,IAAAC,SAAuB;AACvB,sBAA2B;AAC3B,IAAAC,qBAAsB;;;ACJtB,SAAS,aAAa,MAAM;AAC1B,UAAQ,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC,KAAK;AACxD;AACe,SAAR,QAAyB,UAAU,SAAS,IAAI,UAAU,CAAC,GAAG,KAAK,MAAM;AAAC,GAAG;AAClF,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,WAAW;AAAA;AAAA,EACb,IAAI;AACJ,MAAI,QAAQ;AACZ,QAAM,OAAO,QAAQ,QAAQ;AAC7B,MAAI,YAAY;AAChB,QAAM,SAAS,MAAM;AACnB,gBAAY;AAAA,EACd;AACA,QAAM,OAAO,eAAa;AACxB,QAAI,WAAW;AACb,SAAG,IAAI,MAAM,qBAAqB,CAAC;AACnC;AAAA,IACF;AACA,QAAI,UAAU,MAAM;AAClB,cAAQ;AAAA,IACV;AACA,UAAM,OAAO,KAAK,IAAI,IAAI,YAAY,SAAS,QAAQ;AACvD,YAAQ,QAAQ,IAAI,KAAK,IAAI,KAAK,KAAK,QAAQ;AAC/C,QAAI,QAAQ,GAAG;AACb,4BAAsB,MAAM;AAC1B,WAAG,IAAI;AAAA,MACT,CAAC;AACD;AAAA,IACF;AACA,0BAAsB,IAAI;AAAA,EAC5B;AACA,MAAI,SAAS,IAAI;AACf,OAAG,IAAI,MAAM,oCAAoC,CAAC;AAClD,WAAO;AAAA,EACT;AACA,wBAAsB,IAAI;AAC1B,SAAO;AACT;;;ACpCA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AAGtB,IAAAC,sBAA4B;AAC5B,IAAM,SAAS;AAAA,EACb,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,KAAK;AAAA,EACL,UAAU;AACZ;AAOe,SAAR,cAA+B,OAAO;AAC3C,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,kBAAwB,cAAO;AACrC,QAAM,UAAgB,cAAO,IAAI;AACjC,QAAM,kBAAkB,MAAM;AAC5B,oBAAgB,UAAU,QAAQ,QAAQ,eAAe,QAAQ,QAAQ;AAAA,EAC3E;AACA,4BAAkB,MAAM;AACtB,UAAM,eAAe,iBAAS,MAAM;AAClC,YAAM,aAAa,gBAAgB;AACnC,sBAAgB;AAChB,UAAI,eAAe,gBAAgB,SAAS;AAC1C,iBAAS,gBAAgB,OAAO;AAAA,MAClC;AAAA,IACF,CAAC;AACD,UAAM,kBAAkB,oBAAY,QAAQ,OAAO;AACnD,oBAAgB,iBAAiB,UAAU,YAAY;AACvD,WAAO,MAAM;AACX,mBAAa,MAAM;AACnB,sBAAgB,oBAAoB,UAAU,YAAY;AAAA,IAC5D;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,EAAM,iBAAU,MAAM;AACpB,oBAAgB;AAChB,aAAS,gBAAgB,OAAO;AAAA,EAClC,GAAG,CAAC,QAAQ,CAAC;AACb,aAAoB,oBAAAC,KAAK,OAAO;AAAA,IAC9B,OAAO;AAAA,IACP,GAAG;AAAA,IACH,KAAK;AAAA,EACP,CAAC;AACH;AACA,OAAwC,cAAc,YAAY;AAAA,EAChE,UAAU,mBAAAC,QAAU,KAAK;AAC3B,IAAI;;;AFnCJ,IAAAC,sBAA2C;AAC3C,IAAM,WAAW,CAAC,MAAM,SAAS;AAC/B,MAAI,SAAS,MAAM;AACjB,WAAO,KAAK;AAAA,EACd;AACA,MAAI,QAAQ,KAAK,oBAAoB;AACnC,WAAO,KAAK;AAAA,EACd;AACA,SAAO,KAAK;AACd;AACA,IAAM,eAAe,CAAC,MAAM,SAAS;AACnC,MAAI,SAAS,MAAM;AACjB,WAAO,KAAK;AAAA,EACd;AACA,MAAI,QAAQ,KAAK,wBAAwB;AACvC,WAAO,KAAK;AAAA,EACd;AACA,SAAO,KAAK;AACd;AACA,IAAM,YAAY,CAAC,MAAM,cAAc,sBAAsB;AAC3D,MAAI,cAAc;AAClB,MAAI,YAAY,kBAAkB,MAAM,YAAY;AACpD,SAAO,WAAW;AAEhB,QAAI,cAAc,KAAK,YAAY;AACjC,UAAI,aAAa;AACf;AAAA,MACF;AACA,oBAAc;AAAA,IAChB;AAGA,UAAM,oBAAoB,UAAU,YAAY,UAAU,aAAa,eAAe,MAAM;AAC5F,QAAI,CAAC,UAAU,aAAa,UAAU,KAAK,mBAAmB;AAE5D,kBAAY,kBAAkB,MAAM,SAAS;AAAA,IAC/C,OAAO;AACL,gBAAU,MAAM;AAChB;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,UAAU;AAAA,IACrC,UAAU,CAAC,YAAY,SAAS,SAAS,iBAAiB,iBAAiB,eAAe,eAAe,eAAe,aAAa;AAAA,IACrI,MAAM,CAAC,QAAQ,iBAAiB,YAAY,yBAAyB,YAAY,YAAY,YAAY,UAAU;AAAA,IACnH,WAAW,CAAC,WAAW;AAAA,IACvB,eAAe,CAAC,iBAAiB,2BAA2B,yBAAyB;AAAA,IACrF,aAAa,CAAC,eAAe,aAAa;AAAA,IAC1C,eAAe,CAAC,iBAAiB,eAAe;AAAA,EAClD;AACA,SAAO,eAAe,OAAO,qBAAqB,OAAO;AAC3D;AACA,IAAM,WAAW,eAAO,OAAO;AAAA,EAC7B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOC,YAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,oBAAY,aAAa,EAAE,GAAGA,QAAO;AAAA,IAC9C,GAAG;AAAA,MACD,CAAC,MAAM,oBAAY,aAAa,EAAE,GAAG,WAAW,2BAA2BA,QAAO;AAAA,IACpF,GAAGA,QAAO,MAAM,WAAW,YAAYA,QAAO,QAAQ;AAAA,EACxD;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,WAAW;AAAA;AAAA,EAEX,yBAAyB;AAAA,EACzB,SAAS;AAAA,EACT,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,CAAC,MAAM,oBAAY,aAAa,EAAE,GAAG;AAAA,QACnC,CAAC,MAAM,YAAY,KAAK,IAAI,CAAC,GAAG;AAAA,UAC9B,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,eAAe,eAAO,OAAO;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOA,YAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAACA,QAAO,UAAU,WAAW,SAASA,QAAO,OAAO,WAAW,iBAAiBA,QAAO,eAAe,WAAW,eAAeA,QAAO,aAAa,WAAW,eAAeA,QAAO,WAAW;AAAA,EACzM;AACF,CAAC,EAAE;AAAA,EACD,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA;AAAA,MAEL,gBAAgB;AAAA;AAAA,MAEhB,wBAAwB;AAAA,QACtB,SAAS;AAAA;AAAA,MACX;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,OAAO,eAAO,OAAO;AAAA,EACzB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOA,YAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAACA,QAAO,MAAMA,QAAO,eAAe,WAAW,YAAYA,QAAO,uBAAuB,WAAW,YAAYA,QAAO,QAAQ;AAAA,EACxI;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,gBAAgB,eAAO,QAAQ;AAAA,EACnC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOA,YAAWA,QAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,YAAY,MAAM,YAAY,OAAO;AAAA,EACrC,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACL,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IACzD;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACL,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,UAAU;AAAA,IAC3D;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,oBAAoB,eAAO,aAAa,EAAE;AAAA,EAC9C,WAAW;AAAA,EACX,WAAW;AAAA;AAAA,EAEX,gBAAgB;AAAA;AAAA,EAEhB,wBAAwB;AAAA,IACtB,SAAS;AAAA;AAAA,EACX;AACF,CAAC;AACD,IAAM,wBAAwB,CAAC;AAC/B,IAAI,uBAAuB;AAC3B,IAAM,OAA0B,kBAAW,SAASC,MAAK,SAAS,KAAK;AACrE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM,QAAQ,SAAS;AACvB,QAAM,QAAQ,OAAO;AACrB,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB;AAAA,IACA,WAAW;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ,2BAA2B;AAAA,IAC3B,iBAAiB;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,IACd;AAAA;AAAA,IAEA,gBAAgB;AAAA,IAChB;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,oBAAoB,CAAC;AAAA;AAAA,IAErB,uBAAuB,CAAC;AAAA;AAAA,IAExB,YAAY;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa,YAAY;AAC/B,QAAM,WAAW,gBAAgB;AACjC,QAAM,cAAc,WAAW,cAAc;AAC7C,QAAM,QAAQ,WAAW,QAAQ;AACjC,QAAM,MAAM,WAAW,WAAW;AAClC,QAAM,aAAa,WAAW,iBAAiB;AAC/C,QAAM,OAAO,WAAW,WAAW;AACnC,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,eAAe,cAAc,CAAC;AAAA,IAC9B,aAAa,cAAc,CAAC;AAAA,IAC5B,aAAa,cAAc;AAAA,IAC3B,UAAU,YAAY,CAAC;AAAA,IACvB,yBAAyB,CAAC;AAAA,EAC5B;AACA,QAAM,UAAUF,mBAAkB,UAAU;AAC5C,QAAM,6BAA6B,qBAAa;AAAA,IAC9C,aAAa,MAAM;AAAA,IACnB,mBAAmB,UAAU;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,QAAM,2BAA2B,qBAAa;AAAA,IAC5C,aAAa,MAAM;AAAA,IACnB,mBAAmB,UAAU;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,MAAI,MAAuC;AACzC,QAAI,YAAY,YAAY;AAC1B,cAAQ,MAAM,0HAA+H;AAAA,IAC/I;AAAA,EACF;AACA,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAS,KAAK;AAClD,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,gBAAS,qBAAqB;AAChF,QAAM,CAAC,oBAAoB,qBAAqB,IAAU,gBAAS,KAAK;AACxE,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,gBAAS,KAAK;AACpE,QAAM,CAAC,sBAAsB,uBAAuB,IAAU,gBAAS,KAAK;AAC5E,QAAM,CAAC,eAAe,gBAAgB,IAAU,gBAAS;AAAA,IACvD,UAAU;AAAA,IACV,gBAAgB;AAAA,EAClB,CAAC;AACD,QAAM,eAAe,oBAAI,IAAI;AAC7B,QAAM,UAAgB,cAAO,IAAI;AACjC,QAAM,aAAmB,cAAO,IAAI;AACpC,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,cAAc,MAAM;AACxB,UAAM,WAAW,QAAQ;AACzB,QAAI;AACJ,QAAI,UAAU;AACZ,YAAM,OAAO,SAAS,sBAAsB;AAE5C,iBAAW;AAAA,QACT,aAAa,SAAS;AAAA,QACtB,YAAY,SAAS;AAAA,QACrB,WAAW,SAAS;AAAA,QACpB,aAAa,SAAS;AAAA,QACtB,KAAK,KAAK;AAAA,QACV,QAAQ,KAAK;AAAA,QACb,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,QAAI;AACJ,QAAI,YAAY,UAAU,OAAO;AAC/B,YAAMG,YAAW,WAAW,QAAQ;AACpC,UAAIA,UAAS,SAAS,GAAG;AACvB,cAAM,MAAMA,UAAS,aAAa,IAAI,KAAK,CAAC;AAC5C,YAAI,MAAuC;AACzC,cAAI,CAAC,KAAK;AACR,oBAAQ,MAAM,CAAC,iEAAiE,0CAA0C,KAAK,MAAM,aAAa,OAAO,gDAAgD,MAAM,KAAK,aAAa,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,IAAI,EAAE,KAAK,IAAI,CAAC;AAAA,UAC3Q;AAAA,QACF;AACA,kBAAU,MAAM,IAAI,sBAAsB,IAAI;AAC9C,YAAI,MAAuC;AACzC,cAAuC,CAAC,wBAAwB,WAAW,QAAQ,UAAU,KAAK,QAAQ,WAAW;AAAA,UAErH,SAAS,gBAAgB,GAAG;AAC1B,uBAAW;AACX,oBAAQ,MAAM,CAAC,+DAA+D,iCAAiC,KAAK,0CAA0C,qFAAqF,EAAE,KAAK,IAAI,CAAC;AAC/P,mCAAuB;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,uBAAuB,yBAAiB,MAAM;AAClD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,YAAY;AAChB,QAAI,aAAa;AACjB,QAAI;AACJ,QAAI,UAAU;AACZ,uBAAiB;AACjB,UAAI,WAAW,UAAU;AACvB,qBAAa,QAAQ,MAAM,SAAS,MAAM,SAAS;AAAA,MACrD;AAAA,IACF,OAAO;AACL,uBAAiB,QAAQ,UAAU;AACnC,UAAI,WAAW,UAAU;AACvB,sBAAc,QAAQ,KAAK,MAAM,QAAQ,cAAc,IAAI,SAAS,cAAc,IAAI,SAAS;AAAA,MACjG;AAAA,IACF;AACA,UAAM,oBAAoB;AAAA,MACxB,CAAC,cAAc,GAAG;AAAA;AAAA,MAElB,CAAC,IAAI,GAAG,UAAU,QAAQ,IAAI,IAAI;AAAA,IACpC;AACA,QAAI,OAAO,eAAe,cAAc,MAAM,YAAY,OAAO,eAAe,IAAI,MAAM,UAAU;AAClG,wBAAkB,iBAAiB;AAAA,IACrC,OAAO;AACL,YAAM,SAAS,KAAK,IAAI,eAAe,cAAc,IAAI,kBAAkB,cAAc,CAAC;AAC1F,YAAM,QAAQ,KAAK,IAAI,eAAe,IAAI,IAAI,kBAAkB,IAAI,CAAC;AACrE,UAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,0BAAkB,iBAAiB;AAAA,MACrC;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,SAAS,CAAC,aAAa;AAAA,IAC3B,YAAY;AAAA,EACd,IAAI,CAAC,MAAM;AACT,QAAI,WAAW;AACb,cAAQ,aAAa,QAAQ,SAAS,aAAa;AAAA,QACjD,UAAU,MAAM,YAAY,SAAS;AAAA,MACvC,CAAC;AAAA,IACH,OAAO;AACL,cAAQ,QAAQ,WAAW,IAAI;AAAA,IACjC;AAAA,EACF;AACA,QAAM,iBAAiB,WAAS;AAC9B,QAAI,cAAc,QAAQ,QAAQ,WAAW;AAC7C,QAAI,UAAU;AACZ,qBAAe;AAAA,IACjB,OAAO;AACL,qBAAe,SAAS,QAAQ,KAAK;AAAA,IACvC;AACA,WAAO,WAAW;AAAA,EACpB;AACA,QAAM,gBAAgB,MAAM;AAC1B,UAAM,gBAAgB,QAAQ,QAAQ,UAAU;AAChD,QAAI,YAAY;AAChB,UAAMA,YAAW,MAAM,KAAK,WAAW,QAAQ,QAAQ;AACvD,aAAS,IAAI,GAAG,IAAIA,UAAS,QAAQ,KAAK,GAAG;AAC3C,YAAM,MAAMA,UAAS,CAAC;AACtB,UAAI,YAAY,IAAI,UAAU,IAAI,eAAe;AAG/C,YAAI,MAAM,GAAG;AACX,sBAAY;AAAA,QACd;AACA;AAAA,MACF;AACA,mBAAa,IAAI,UAAU;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AACA,QAAM,yBAAyB,MAAM;AACnC,mBAAe,KAAK,cAAc,CAAC;AAAA,EACrC;AACA,QAAM,uBAAuB,MAAM;AACjC,mBAAe,cAAc,CAAC;AAAA,EAChC;AACA,QAAM,CAAC,eAAe;AAAA,IACpB,UAAU;AAAA,IACV,GAAG;AAAA,EACL,CAAC,IAAI,QAAQ,aAAa;AAAA,IACxB,WAAW,aAAK,QAAQ,aAAa,QAAQ,aAAa;AAAA,IAC1D,aAAa;AAAA,IACb,4BAA4B;AAAA,IAC5B;AAAA,IACA;AAAA,EACF,CAAC;AAID,QAAM,4BAAkC,mBAAY,oBAAkB;AACpE,2DAAoB;AACpB,qBAAiB;AAAA,MACf,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,iBAAiB,CAAC;AACtB,QAAM,CAAC,mBAAmB,qBAAqB,IAAI,QAAQ,iBAAiB;AAAA,IAC1E,WAAW,aAAK,QAAQ,eAAe,qBAAqB,SAAS;AAAA,IACrE,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,MACA,OAAO;AAAA,QACL,uBAAuB,MAAM,yBAAyB,MAAM;AAAA,QAC5D,qBAAqB,MAAM,uBAAuB,MAAM;AAAA,MAC1D;AAAA,MACA,WAAW;AAAA,QACT,uBAAuB;AAAA,QACvB,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,yBAAyB,MAAM;AACnC,UAAMC,uBAAsB,CAAC;AAC7B,IAAAA,qBAAoB,wBAAwB,iBAA0B,oBAAAC,KAAK,eAAe;AAAA,MACxF,GAAG;AAAA,MACH,UAAU;AAAA,IACZ,CAAC,IAAI;AACL,UAAM,sBAAsB,sBAAsB;AAClD,UAAM,oBAAoB,eAAe,kBAAkB,UAAU,uBAAuB,kBAAkB;AAC9G,IAAAD,qBAAoB,oBAAoB,wBAAiC,oBAAAC,KAAK,mBAAmB;AAAA,MAC/F,WAAW,QAAQ,UAAU;AAAA,MAC7B,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,MACX,GAAG;AAAA,IACL,CAAC,IAAI;AACL,IAAAD,qBAAoB,kBAAkB,wBAAiC,oBAAAC,KAAK,mBAAmB;AAAA,MAC7F,WAAW,QAAQ,SAAS;AAAA,MAC5B,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,MACX,GAAG;AAAA,IACL,CAAC,IAAI;AACL,WAAOD;AAAA,EACT;AACA,QAAM,yBAAyB,yBAAiB,eAAa;AAC3D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,YAAY;AAChB,QAAI,CAAC,WAAW,CAAC,UAAU;AACzB;AAAA,IACF;AACA,QAAI,QAAQ,KAAK,IAAI,SAAS,KAAK,GAAG;AAEpC,YAAM,kBAAkB,SAAS,WAAW,KAAK,QAAQ,KAAK,IAAI,SAAS,KAAK;AAChF,aAAO,iBAAiB;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH,WAAW,QAAQ,GAAG,IAAI,SAAS,GAAG,GAAG;AAEvC,YAAM,kBAAkB,SAAS,WAAW,KAAK,QAAQ,GAAG,IAAI,SAAS,GAAG;AAC5E,aAAO,iBAAiB;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,0BAA0B,yBAAiB,MAAM;AACrD,QAAI,cAAc,kBAAkB,OAAO;AACzC,8BAAwB,CAAC,oBAAoB;AAAA,IAC/C;AAAA,EACF,CAAC;AACD,EAAM,iBAAU,MAAM;AACpB,UAAM,eAAe,iBAAS,MAAM;AAOlC,UAAI,QAAQ,SAAS;AACnB,6BAAqB;AAAA,MACvB;AAAA,IACF,CAAC;AACD,QAAI;AAKJ,UAAM,iBAAiB,aAAW;AAChC,cAAQ,QAAQ,YAAU;AACxB,eAAO,aAAa,QAAQ,UAAQ;AAClC,2DAAgB,UAAU;AAAA,QAC5B,CAAC;AACD,eAAO,WAAW,QAAQ,UAAQ;AAChC,2DAAgB,QAAQ;AAAA,QAC1B,CAAC;AAAA,MACH,CAAC;AACD,mBAAa;AACb,8BAAwB;AAAA,IAC1B;AACA,UAAM,MAAM,oBAAY,QAAQ,OAAO;AACvC,QAAI,iBAAiB,UAAU,YAAY;AAC3C,QAAI;AACJ,QAAI,OAAO,mBAAmB,aAAa;AACzC,uBAAiB,IAAI,eAAe,YAAY;AAChD,YAAM,KAAK,WAAW,QAAQ,QAAQ,EAAE,QAAQ,WAAS;AACvD,uBAAe,QAAQ,KAAK;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,QAAI,OAAO,qBAAqB,aAAa;AAC3C,yBAAmB,IAAI,iBAAiB,cAAc;AACtD,uBAAiB,QAAQ,WAAW,SAAS;AAAA,QAC3C,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AACA,WAAO,MAAM;AACX,mBAAa,MAAM;AACnB,UAAI,oBAAoB,UAAU,YAAY;AAC9C,2DAAkB;AAClB,uDAAgB;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,sBAAsB,uBAAuB,CAAC;AAMlD,EAAM,iBAAU,MAAM;AACpB,UAAM,kBAAkB,MAAM,KAAK,WAAW,QAAQ,QAAQ;AAC9D,UAAM,SAAS,gBAAgB;AAC/B,QAAI,OAAO,yBAAyB,eAAe,SAAS,KAAK,cAAc,kBAAkB,OAAO;AACtG,YAAM,WAAW,gBAAgB,CAAC;AAClC,YAAM,UAAU,gBAAgB,SAAS,CAAC;AAC1C,YAAM,kBAAkB;AAAA,QACtB,MAAM,QAAQ;AAAA,QACd,WAAW;AAAA,MACb;AACA,YAAM,0BAA0B,aAAW;AACzC,8BAAsB,CAAC,QAAQ,CAAC,EAAE,cAAc;AAAA,MAClD;AACA,YAAM,gBAAgB,IAAI,qBAAqB,yBAAyB,eAAe;AACvF,oBAAc,QAAQ,QAAQ;AAC9B,YAAM,wBAAwB,aAAW;AACvC,4BAAoB,CAAC,QAAQ,CAAC,EAAE,cAAc;AAAA,MAChD;AACA,YAAM,eAAe,IAAI,qBAAqB,uBAAuB,eAAe;AACpF,mBAAa,QAAQ,OAAO;AAC5B,aAAO,MAAM;AACX,sBAAc,WAAW;AACzB,qBAAa,WAAW;AAAA,MAC1B;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,YAAY,eAAe,sBAAsB,6CAAc,MAAM,CAAC;AAC1E,EAAM,iBAAU,MAAM;AACpB,eAAW,IAAI;AAAA,EACjB,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,MAAM;AACpB,yBAAqB;AAAA,EACvB,CAAC;AACD,EAAM,iBAAU,MAAM;AAEpB,2BAAuB,0BAA0B,cAAc;AAAA,EACjE,GAAG,CAAC,wBAAwB,cAAc,CAAC;AAC3C,EAAM,2BAAoB,QAAQ,OAAO;AAAA,IACvC,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB,IAAI,CAAC,sBAAsB,uBAAuB,CAAC;AACnD,QAAM,CAAC,eAAe,kBAAkB,IAAI,QAAQ,aAAa;AAAA,IAC/D,WAAW,aAAK,QAAQ,WAAW,kBAAkB,SAAS;AAAA,IAC9D,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,QAAM,gBAAyB,oBAAAC,KAAK,eAAe;AAAA,IACjD,GAAG;AAAA,EACL,CAAC;AACD,MAAI,aAAa;AACjB,QAAM,WAAiB,gBAAS,IAAI,cAAc,WAAS;AACzD,QAAI,CAAqB,sBAAe,KAAK,GAAG;AAC9C,aAAO;AAAA,IACT;AACA,QAAI,MAAuC;AACzC,cAAI,4BAAW,KAAK,GAAG;AACrB,gBAAQ,MAAM,CAAC,iEAAiE,sCAAsC,EAAE,KAAK,IAAI,CAAC;AAAA,MACpI;AAAA,IACF;AACA,UAAM,aAAa,MAAM,MAAM,UAAU,SAAY,aAAa,MAAM,MAAM;AAC9E,iBAAa,IAAI,YAAY,UAAU;AACvC,UAAM,WAAW,eAAe;AAChC,kBAAc;AACd,WAA0B,oBAAa,OAAO;AAAA,MAC5C,WAAW,YAAY;AAAA,MACvB,WAAW,YAAY,CAAC,WAAW;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,GAAI,eAAe,KAAK,UAAU,SAAS,CAAC,MAAM,MAAM,WAAW;AAAA,QACjE,UAAU;AAAA,MACZ,IAAI,CAAC;AAAA,IACP,CAAC;AAAA,EACH,CAAC;AACD,QAAM,gBAAgB,WAAS;AAE7B,QAAI,MAAM,UAAU,MAAM,YAAY,MAAM,WAAW,MAAM,SAAS;AACpE;AAAA,IACF;AACA,UAAM,OAAO,WAAW;AACxB,UAAM,eAAe,sBAAc,IAAI,EAAE;AAIzC,UAAM,OAAO,aAAa,aAAa,MAAM;AAC7C,QAAI,SAAS,OAAO;AAClB;AAAA,IACF;AACA,QAAI,kBAAkB,gBAAgB,eAAe,cAAc;AACnE,QAAI,cAAc,gBAAgB,eAAe,eAAe;AAChE,QAAI,gBAAgB,gBAAgB,OAAO;AAEzC,wBAAkB;AAClB,oBAAc;AAAA,IAChB;AACA,YAAQ,MAAM,KAAK;AAAA,MACjB,KAAK;AACH,cAAM,eAAe;AACrB,kBAAU,MAAM,cAAc,YAAY;AAC1C;AAAA,MACF,KAAK;AACH,cAAM,eAAe;AACrB,kBAAU,MAAM,cAAc,QAAQ;AACtC;AAAA,MACF,KAAK;AACH,cAAM,eAAe;AACrB,kBAAU,MAAM,MAAM,QAAQ;AAC9B;AAAA,MACF,KAAK;AACH,cAAM,eAAe;AACrB,kBAAU,MAAM,MAAM,YAAY;AAClC;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF;AACA,QAAM,sBAAsB,uBAAuB;AACnD,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,IACF;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,cAAc,iBAAiB,IAAI,QAAQ,YAAY;AAAA,IAC5D,KAAK;AAAA,IACL,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf,OAAO;AAAA,QACL,UAAU,cAAc;AAAA,QACxB,CAAC,WAAW,SAAS,QAAQ,SAAS,OAAO,KAAK,cAAc,GAAG,mBAAmB,SAAY,CAAC,cAAc;AAAA,MACnH;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD,KAAK;AAAA,IACL,WAAW,aAAK,QAAQ,MAAM,QAAQ,aAAa;AAAA,IACnD,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,cAAc,eAAa;AAAA,MACzB,GAAG;AAAA,MACH,WAAW,WAAS;AAjwB1B;AAkwBQ,sBAAc,KAAK;AACnB,uBAAS,cAAT,kCAAqB;AAAA,MACvB;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAC,MAAM,UAAU;AAAA,IAClC,GAAG;AAAA,IACH,UAAU,CAAC,oBAAoB,mBAAmB,oBAAoB,2BAAoC,oBAAAA,MAAM,cAAc;AAAA,MAC5H,GAAG;AAAA,MACH,UAAU,KAAc,oBAAAD,KAAK,UAAU;AAAA,QACrC,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,oBAAoB,gBAAgB,aAAa,aAAa;AAAA,QAC9D,MAAM;AAAA,QACN,GAAG;AAAA,QACH;AAAA,MACF,CAAC,GAAG,WAAW,SAAS;AAAA,IAC1B,CAAC,GAAG,oBAAoB,eAAe;AAAA,EACzC,CAAC;AACH,CAAC;AACD,OAAwC,KAAK,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAa9E,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,0BAA0B,mBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpC,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIxB,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,gBAAgB,mBAAAA,QAAgD,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvI,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,aAAa,mBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvD,uBAAuB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYjC,eAAe,mBAAAA,QAAgD,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1F,uBAAuB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,WAAW,mBAAAA,QAAU,MAAM;AAAA,IACzB,qBAAqB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC3E,WAAW,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACjE,MAAM,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,MAAM,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,WAAW,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACjE,eAAe,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IACrE,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,uBAAuB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/E,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,qBAAqB,mBAAAA,QAAU;AAAA,IAC/B,qBAAqB,mBAAAA,QAAU;AAAA,IAC/B,WAAW,mBAAAA,QAAU;AAAA,IACrB,MAAM,mBAAAA,QAAU;AAAA,IAChB,MAAM,mBAAAA,QAAU;AAAA,IAChB,WAAW,mBAAAA,QAAU;AAAA,IACrB,eAAe,mBAAAA,QAAU;AAAA,IACzB,UAAU,mBAAAA,QAAU;AAAA,IACpB,uBAAuB,mBAAAA,QAAU;AAAA,IACjC,uBAAuB,mBAAAA,QAAU;AAAA,EACnC,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtJ,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,sBAAsB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,WAAW,mBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,WAAW,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWjB,SAAS,mBAAAA,QAAU,MAAM,CAAC,aAAa,cAAc,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhE,kBAAkB,mBAAAA,QAAU;AAC9B,IAAI;AACJ,IAAO,eAAQ;", "names": ["styles", "TabScrollButton", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_prop_types", "import_jsx_runtime", "_jsx", "PropTypes", "import_jsx_runtime", "useUtilityClasses", "styles", "Tabs", "children", "conditionalElements", "_jsx", "_jsxs", "PropTypes"]}