import React, { useContext } from 'react';
import { Box, Typography, Chip, Button, Paper, Stack } from '@mui/material';
import { useTokenRefresh } from '@/hooks/use-token-refresh';
import { Refresh, CheckCircle, Warning } from '@mui/icons-material';
import { AppConfigContext } from '@/core/contexts';

interface TokenStatusDebugProps {
  show?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const TokenStatusDebug: React.FC<TokenStatusDebugProps> = ({ 
  show = false, 
  position = 'bottom-left' 
}) => {
  const appConfig = useContext(AppConfigContext);
  const tokenRefresh = useTokenRefresh({
    scopes: appConfig.msDetail?.scope || [],
    autoStart: false, // Don't auto-start since this is just for debugging
  });

  if (!show) return null;

  const getStatusColor = () => {
    if (tokenRefresh.isRefreshing) return 'warning';
    if (tokenRefresh.isTokenNearExpiry) return 'error';
    return 'success';
  };

  const getStatusIcon = () => {
    if (tokenRefresh.isRefreshing) return <Refresh sx={{ animation: 'spin 1s linear infinite' }} />;
    if (tokenRefresh.isTokenNearExpiry) return <Warning />;
    return <CheckCircle />;
  };

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      zIndex: 9999,
      maxWidth: 300,
    };

    switch (position) {
      case 'top-left':
        return { ...baseStyles, top: 16, left: 16 };
      case 'top-right':
        return { ...baseStyles, top: 16, right: 16 };
      case 'bottom-left':
        return { ...baseStyles, bottom: 16, left: 16 };
      case 'bottom-right':
        return { ...baseStyles, bottom: 16, right: 16 };
      default:
        return { ...baseStyles, bottom: 16, left: 16 };
    }
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
      <Paper
        elevation={3}
        sx={{
          ...getPositionStyles(),
          p: 2,
          bgcolor: 'background.paper',
          border: 1,
          borderColor: 'divider',
        }}
      >
        <Stack spacing={1}>
          <Typography variant="subtitle2" fontWeight="bold">
            Token Status Debug
          </Typography>
          
          <Stack direction="row" spacing={1} alignItems="center">
            {getStatusIcon()}
            <Chip
              label={
                tokenRefresh.isRefreshing
                  ? 'Refreshing'
                  : tokenRefresh.isTokenNearExpiry
                  ? 'Near Expiry'
                  : 'Valid'
              }
              color={getStatusColor()}
              size="small"
            />
          </Stack>

          <Box>
            <Typography variant="caption" color="text.secondary">
              Time until expiry:
            </Typography>
            <Typography variant="body2" fontWeight="medium">
              {tokenRefresh.formattedTimeUntilExpiry}
            </Typography>
          </Box>

          {tokenRefresh.lastRefreshTime && (
            <Box>
              <Typography variant="caption" color="text.secondary">
                Last refresh:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {tokenRefresh.lastRefreshTime.toLocaleTimeString()}
              </Typography>
            </Box>
          )}

          <Box>
            <Typography variant="caption" color="text.secondary">
              Refresh count:
            </Typography>
            <Typography variant="body2" fontWeight="medium">
              {tokenRefresh.refreshCount}
            </Typography>
          </Box>

          <Button
            size="small"
            variant="outlined"
            onClick={tokenRefresh.refreshToken}
            disabled={tokenRefresh.isRefreshing}
            startIcon={<Refresh />}
          >
            Force Refresh
          </Button>
        </Stack>
      </Paper>
    </>
  );
};

export default TokenStatusDebug;
