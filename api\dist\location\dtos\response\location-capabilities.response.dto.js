"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationCapabilitiesResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const dtos_1 = require("../../../metadata/dtos");
const enums_1 = require("../../../shared/enums");
class CategoryResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CategoryResponseDto.prototype, "title", void 0);
class MasterCapabilityResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MasterCapabilityResponseDto.prototype, "capability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MasterCapabilityResponseDto.prototype, "product", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: CategoryResponseDto }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => CategoryResponseDto),
    __metadata("design:type", CategoryResponseDto)
], MasterCapabilityResponseDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MasterCapabilityResponseDto.prototype, "subCategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MasterCapabilityResponseDto.prototype, "capabilityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MasterCapabilityResponseDto.prototype, "level", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], MasterCapabilityResponseDto.prototype, "verticals", void 0);
class CapabilityDetailResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], CapabilityDetailResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CapabilityDetailResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CapabilityDetailResponseDto.prototype, "internalStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CapabilityDetailResponseDto.prototype, "statusDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CapabilityDetailResponseDto.prototype, "provider", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CapabilityDetailResponseDto.prototype, "providerName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: MasterCapabilityResponseDto }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => MasterCapabilityResponseDto),
    __metadata("design:type", MasterCapabilityResponseDto)
], CapabilityDetailResponseDto.prototype, "capabilityDetail", void 0);
class LocationDetailResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], LocationDetailResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocationDetailResponseDto.prototype, "locationName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], LocationDetailResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocationDetailResponseDto.prototype, "entityTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocationDetailResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocationDetailResponseDto.prototype, "statusDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocationDetailResponseDto.prototype, "leaseOwnershipStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", dtos_1.BasicLocationTypeResponseDto)
], LocationDetailResponseDto.prototype, "locationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", dtos_1.BaseCoreSolutionResponseDto)
], LocationDetailResponseDto.prototype, "coreSolution", void 0);
class LocationCapabilitiesResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: LocationDetailResponseDto }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => LocationDetailResponseDto),
    __metadata("design:type", LocationDetailResponseDto)
], LocationCapabilitiesResponseDto.prototype, "locationDetail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: CapabilityDetailResponseDto, isArray: true }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Type)(() => CapabilityDetailResponseDto),
    __metadata("design:type", Array)
], LocationCapabilitiesResponseDto.prototype, "capabilityList", void 0);
exports.LocationCapabilitiesResponseDto = LocationCapabilitiesResponseDto;
//# sourceMappingURL=location-capabilities.response.dto.js.map