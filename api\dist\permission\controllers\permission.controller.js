"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const enums_1 = require("../../shared/enums");
const dtos_2 = require("../dtos");
const services_1 = require("../services");
let PermissionController = class PermissionController {
    constructor(permissionService) {
        this.permissionService = permissionService;
    }
    getListOfUserPermissions(request) {
        return this.permissionService.getListOfUserPermissions(request.currentContext);
    }
    getAllAccessControlConfigs() {
        return this.permissionService.getAllAccessControlConfigs();
    }
    getLocalLocationBasedPermissions(request, page = 1, limit = 10, orderBy = 'updatedOn', orderDirection = 'DESC', loginid, configGroupId, locationId) {
        return this.permissionService.getLocalLocationBasedPermissions(request.currentContext, page, limit, orderBy, orderDirection, loginid, configGroupId, locationId);
    }
    createUserPermission(request, data) {
        return this.permissionService.createUserPermission(data, request.currentContext);
    }
    deleteUserPermission(request, id) {
        return this.permissionService.deleteUserPermission(id, request.currentContext);
    }
    getPeopleSearch(loginId) {
        return this.permissionService.getPeopleSearch(loginId);
    }
};
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get list of permissions with locations for the user.',
        type: [dtos_2.PermissionResponseDto],
    }),
    (0, common_1.Get)('/app-permission'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getListOfUserPermissions", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all access control configurations.',
        type: [dtos_2.AllAccessControlConfigResponseDto],
    }),
    (0, common_1.Get)('/access-control-config'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getAllAccessControlConfigs", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get paginated list of user permissions.',
        type: dtos_2.PaginatedUserPermissionResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'loginid',
        required: false,
        type: String,
        description: 'Filter by login ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'configGroupId',
        required: false,
        type: Number,
        description: 'Filter by config group ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'locationId',
        required: false,
        type: Number,
        description: 'Filter by location id',
    }),
    (0, common_1.Get)('/local-permission'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('orderBy')),
    __param(4, (0, common_1.Query)('orderDirection')),
    __param(5, (0, common_1.Query)('loginid')),
    __param(6, (0, common_1.Query)('configGroupId')),
    __param(7, (0, common_1.Query)('locationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, String, String, String, Number, Number]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getLocalLocationBasedPermissions", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Create a new user permission.',
        type: dtos_2.UserPermissionResponseDto,
    }),
    (0, common_1.Post)('/local-permission'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.CreateUserPermissionRequestDto]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "createUserPermission", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete a user permission.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)('/local-permission/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "deleteUserPermission", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get people search response.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Get)('/people-search/:loginId'),
    __param(0, (0, common_1.Param)('loginId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PermissionController.prototype, "getPeopleSearch", null);
PermissionController = __decorate([
    (0, swagger_1.ApiTags)('Permission APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('permissions'),
    __metadata("design:paramtypes", [services_1.PermissionService])
], PermissionController);
exports.PermissionController = PermissionController;
//# sourceMappingURL=permission.controller.js.map