"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreSolution = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const common_dropdown_model_1 = require("./common-dropdown.model");
const location_type_model_1 = require("./location-type.model");
const helpers_1 = require("../../shared/helpers");
const enums_1 = require("../../shared/enums");
const models_2 = require("../../location/models");
const models_3 = require("../../capability/models");
let CoreSolution = class CoreSolution extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], CoreSolution.prototype, "title", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'code', type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.CORES_SOLUTION_ENUM)), allowNull: false, unique: true }),
    __metadata("design:type", String)
], CoreSolution.prototype, "code", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'legal_entity_form_sections',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.LEGAL_ENTITY_FORM_SECTION)),
        allowNull: false
    }),
    __metadata("design:type", String)
], CoreSolution.prototype, "legalEntityFormSections", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'pillar',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.PILLAR)),
        allowNull: false
    }),
    __metadata("design:type", String)
], CoreSolution.prototype, "pillar", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => common_dropdown_model_1.CommonDropdown),
    __metadata("design:type", Array)
], CoreSolution.prototype, "commonDropdowns", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => location_type_model_1.LocationType),
    __metadata("design:type", Array)
], CoreSolution.prototype, "locationTypes", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => models_2.Location),
    __metadata("design:type", Array)
], CoreSolution.prototype, "locations", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => models_3.MasterCapability),
    __metadata("design:type", Array)
], CoreSolution.prototype, "masterCapabilities", void 0);
CoreSolution = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'meta_core_solutions' })
], CoreSolution);
exports.CoreSolution = CoreSolution;
//# sourceMappingURL=core-solution.model.js.map