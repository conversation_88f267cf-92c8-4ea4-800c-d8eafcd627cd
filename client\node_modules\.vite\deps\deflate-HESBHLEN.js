import {
  inflate_1
} from "./chunk-ZFYKOUR7.js";
import {
  BaseDecoder
} from "./chunk-C5KGH6RQ.js";
import "./chunk-EWTE5DHJ.js";

// node_modules/geotiff/dist-module/compression/deflate.js
var DeflateDecoder = class extends BaseDecoder {
  decodeBlock(buffer) {
    return inflate_1(new Uint8Array(buffer)).buffer;
  }
};
export {
  DeflateDecoder as default
};
//# sourceMappingURL=deflate-HESBHLEN.js.map
