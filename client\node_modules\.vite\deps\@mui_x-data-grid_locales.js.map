{"version": 3, "sources": ["../../@mui/x-data-grid/utils/getGridLocalization.js", "../../@mui/x-data-grid/locales/arSD.js", "../../@mui/x-data-grid/locales/coreLocales.js", "../../@mui/x-data-grid/locales/beBY.js", "../../@mui/x-data-grid/locales/bgBG.js", "../../@mui/x-data-grid/locales/bnBD.js", "../../@mui/x-data-grid/locales/csCZ.js", "../../@mui/x-data-grid/locales/daDK.js", "../../@mui/x-data-grid/locales/deDE.js", "../../@mui/x-data-grid/locales/elGR.js", "../../@mui/x-data-grid/constants/localeTextConstants.js", "../../@mui/x-data-grid/locales/enUS.js", "../../@mui/x-data-grid/locales/esES.js", "../../@mui/x-data-grid/locales/faIR.js", "../../@mui/x-data-grid/locales/fiFI.js", "../../@mui/x-data-grid/locales/frFR.js", "../../@mui/x-data-grid/locales/heIL.js", "../../@mui/x-data-grid/locales/huHU.js", "../../@mui/x-data-grid/locales/hyAM.js", "../../@mui/x-data-grid/locales/itIT.js", "../../@mui/x-data-grid/locales/jaJP.js", "../../@mui/x-data-grid/locales/koKR.js", "../../@mui/x-data-grid/locales/nbNO.js", "../../@mui/x-data-grid/locales/nlNL.js", "../../@mui/x-data-grid/locales/nnNO.js", "../../@mui/x-data-grid/locales/plPL.js", "../../@mui/x-data-grid/locales/ptBR.js", "../../@mui/x-data-grid/locales/roRO.js", "../../@mui/x-data-grid/locales/ruRU.js", "../../@mui/x-data-grid/locales/skSK.js", "../../@mui/x-data-grid/locales/svSE.js", "../../@mui/x-data-grid/locales/trTR.js", "../../@mui/x-data-grid/locales/ukUA.js", "../../@mui/x-data-grid/locales/urPK.js", "../../@mui/x-data-grid/locales/viVN.js", "../../@mui/x-data-grid/locales/zhCN.js", "../../@mui/x-data-grid/locales/zhTW.js", "../../@mui/x-data-grid/locales/hrHR.js", "../../@mui/x-data-grid/locales/ptPT.js", "../../@mui/x-data-grid/locales/zhHK.js", "../../@mui/x-data-grid/locales/isIS.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getGridLocalization = (gridTranslations, coreTranslations) => ({\n  components: {\n    MuiDataGrid: {\n      defaultProps: {\n        localeText: _extends({}, gridTranslations, {\n          MuiTablePagination: coreTranslations?.components?.MuiTablePagination?.defaultProps || {}\n        })\n      }\n    }\n  }\n});", "import { arSD as arSDCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst arSDGrid = {\n  // Root\n  noRowsLabel: 'لا توجد صفوف',\n  noResultsOverlayLabel: 'لم يتم العثور على نتائج.',\n  // Density selector toolbar button text\n  toolbarDensity: 'الكثافة',\n  toolbarDensityLabel: 'الكثافة',\n  toolbarDensityCompact: 'مضغوط',\n  toolbarDensityStandard: 'قياسي',\n  toolbarDensityComfortable: 'مريح',\n  // Columns selector toolbar button text\n  toolbarColumns: 'الأعمدة',\n  toolbarColumnsLabel: 'حدد أعمدة',\n  // Filters toolbar button text\n  toolbarFilters: 'المُرشِحات',\n  toolbarFiltersLabel: 'إظهار المرشِحات',\n  toolbarFiltersTooltipHide: 'إخفاء المرشِحات',\n  toolbarFiltersTooltipShow: 'اظهر المرشِحات',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} من المرشِحات النشطة` : `مرشِح نشط`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'بحث...',\n  toolbarQuickFilterLabel: 'بحث',\n  toolbarQuickFilterDeleteIconLabel: 'أزال',\n  // Export selector toolbar button text\n  toolbarExport: 'تصدير',\n  toolbarExportLabel: 'تصدير',\n  toolbarExportCSV: 'تنزيل كملف CSV',\n  toolbarExportPrint: 'طباعة',\n  toolbarExportExcel: 'تحميل كملف الإكسل',\n  // Columns management text\n  // columnsManagementSearchTitle: 'Search',\n  // columnsManagementNoColumns: 'No columns',\n  // columnsManagementShowHideAllText: 'Show/Hide All',\n  // columnsManagementReset: 'Reset',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'إضافة مرشِح',\n  filterPanelRemoveAll: 'حذف الكل',\n  filterPanelDeleteIconLabel: 'حذف',\n  filterPanelLogicOperator: 'عامل منطقي',\n  filterPanelOperator: 'عامل',\n  filterPanelOperatorAnd: 'و',\n  filterPanelOperatorOr: 'أو',\n  filterPanelColumns: 'الأعمدة',\n  filterPanelInputLabel: 'القيمة',\n  filterPanelInputPlaceholder: 'ترشِيح قيمة',\n  // Filter operators text\n  filterOperatorContains: 'يحتوي',\n  // filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'يساوي',\n  // filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'يبدأ بـ',\n  filterOperatorEndsWith: 'ينتهي بـ',\n  filterOperatorIs: 'يكون',\n  filterOperatorNot: 'ليس',\n  filterOperatorAfter: 'بعد',\n  filterOperatorOnOrAfter: 'عند أو بعد',\n  filterOperatorBefore: 'قبل',\n  filterOperatorOnOrBefore: 'عند أو قبل',\n  filterOperatorIsEmpty: 'خالي',\n  filterOperatorIsNotEmpty: 'غير خالي',\n  filterOperatorIsAnyOf: 'أي من',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'يحتوي على',\n  // headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'يساوي',\n  // headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'يبدأ ب',\n  headerFilterOperatorEndsWith: 'ينتهي ب',\n  headerFilterOperatorIs: 'هو',\n  headerFilterOperatorNot: 'هو ليس',\n  headerFilterOperatorAfter: 'يقع بعد',\n  headerFilterOperatorOnOrAfter: 'هو على او بعد',\n  headerFilterOperatorBefore: 'يقع قبل',\n  headerFilterOperatorOnOrBefore: 'هو على او بعد',\n  headerFilterOperatorIsEmpty: 'هو فارغ',\n  headerFilterOperatorIsNotEmpty: 'هو ليس فارغ',\n  headerFilterOperatorIsAnyOf: 'هو أي من',\n  'headerFilterOperator=': 'يساوي',\n  'headerFilterOperator!=': 'لا يساوي',\n  'headerFilterOperator>': 'أكبر من',\n  'headerFilterOperator>=': 'أكبر من او يساوي',\n  'headerFilterOperator<': 'اصغر من',\n  'headerFilterOperator<=': 'اصغر من او يساوي',\n  // Filter values text\n  filterValueAny: 'أي',\n  filterValueTrue: 'صائب',\n  filterValueFalse: 'خاطئ',\n  // Column menu text\n  columnMenuLabel: 'القائمة',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'إظهار الأعمدة',\n  columnMenuManageColumns: 'إدارة الأعمدة',\n  columnMenuFilter: 'المرشِح',\n  columnMenuHideColumn: 'إخفاء',\n  columnMenuUnsort: 'الغاء الفرز',\n  columnMenuSortAsc: 'الفرز تصاعدياً',\n  columnMenuSortDesc: 'الفرز تنازلياً',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} من المرشِحات النشطة` : `مرشِح نشط`,\n  columnHeaderFiltersLabel: 'إظهار المرشحات',\n  columnHeaderSortIconLabel: 'فرز',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `تم تحديد ${count.toLocaleString()} من الصفوف` : `تم تحديد صف واحد`,\n  // Total row amount footer text\n  footerTotalRows: 'إجمالي الصفوف:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} من ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'تحديد خانة الاختيار',\n  checkboxSelectionSelectAllRows: 'تحديد كل الصفوف',\n  checkboxSelectionUnselectAllRows: 'الغاء تحديد كل الصفوف',\n  checkboxSelectionSelectRow: 'تحديد صف',\n  checkboxSelectionUnselectRow: 'الغاء تحديد الصف',\n  // Boolean cell text\n  booleanCellTrueLabel: 'نعم',\n  booleanCellFalseLabel: 'لا',\n  // Actions cell more text\n  actionsCellMore: 'المزيد',\n  // Column pinning text\n  pinToLeft: 'التدبيس يميناً',\n  pinToRight: 'التدبيس يساراً',\n  unpin: 'الغاء التدبيس',\n  // Tree Data\n  treeDataGroupingHeaderName: 'تجميع',\n  treeDataExpand: 'رؤية الأبناء',\n  treeDataCollapse: 'إخفاء الأبناء',\n  // Grouping columns\n  groupingColumnHeaderName: 'تجميع',\n  groupColumn: name => `تجميع حسب ${name}`,\n  unGroupColumn: name => `إيقاف التجميع حسب ${name}`,\n  // Master/detail\n  detailPanelToggle: 'اظهار/اخفاء لوحة التفاصيل',\n  expandDetailPanel: 'توسيع',\n  collapseDetailPanel: 'طوي',\n  // Row reordering text\n  rowReorderingHeaderName: 'أعادة ترتيب الصفوف',\n  // Aggregation\n  aggregationMenuItemHeader: 'الدلالات الحسابية',\n  aggregationFunctionLabelSum: 'مجموع',\n  aggregationFunctionLabelAvg: 'معدل',\n  aggregationFunctionLabelMin: 'الحد الادنى',\n  aggregationFunctionLabelMax: 'الحد الاقصى',\n  aggregationFunctionLabelSize: 'الحجم'\n};\nexport const arSD = getGridLocalization(arSDGrid, arSDCore);", "// This file contains copies of the core locales for `MuiTablePagination` released\n// after the `@mui/material` package `v5.4.1` (peer dependency of `@mui/x-data-grid`).\n// This allows not to bump the minimal version of `@mui/material` in peerDependencies which results\n// in broader compatibility between the packages.\n// See https://github.com/mui/mui-x/pull/7646#issuecomment-1404605556 for additional context.\n\nexport const beBYCore = {\n  components: {\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'Перайсці на першую старонку';\n          }\n          if (type === 'last') {\n            return 'Перайсці на апошнюю старонку';\n          }\n          if (type === 'next') {\n            return 'Перайсці на наступную старонку';\n          }\n          // if (type === 'previous') {\n          return 'Перайсці на папярэднюю старонку';\n        },\n        labelRowsPerPage: 'Радкоў на старонцы:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${from}–${to} з ${count !== -1 ? count : `больш чым ${to}`}`\n      }\n    }\n  }\n};\nexport const urPKCore = {\n  components: {\n    MuiTablePagination: {\n      defaultProps: {\n        getItemAriaLabel: type => {\n          if (type === 'first') {\n            return 'پہلے صفحے پر جائیں';\n          }\n          if (type === 'last') {\n            return 'آخری صفحے پر جائیں';\n          }\n          if (type === 'next') {\n            return 'اگلے صفحے پر جائیں';\n          }\n          // if (type === 'previous') {\n          return 'پچھلے صفحے پر جائیں';\n        },\n        labelRowsPerPage: 'ایک صفحے پر قطاریں:',\n        labelDisplayedRows: ({\n          from,\n          to,\n          count\n        }) => `${count !== -1 ? `${count} میں سے` : `${to} سے ذیادہ میں سے`} ${from} سے ${to} قطاریں`\n      }\n    }\n  }\n};", "import { beBYCore } from \"./coreLocales.js\";\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst getPluralForm = (count, options) => {\n  let pluralForm = options.many;\n  const lastDigit = count % 10;\n  if (lastDigit > 1 && lastDigit < 5 && (count < 10 || count > 20)) {\n    pluralForm = options.few;\n  } else if (lastDigit === 1 && count % 100 !== 11) {\n    pluralForm = options.one;\n  }\n  return `${count} ${pluralForm}`;\n};\nconst beBYGrid = {\n  // Root\n  noRowsLabel: 'Няма радкоў',\n  noResultsOverlayLabel: 'Дадзеныя не знойдзены.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Вышыня радка',\n  toolbarDensityLabel: 'Вышыня радка',\n  toolbarDensityCompact: 'Кампактны',\n  toolbarDensityStandard: 'Стандартны',\n  toolbarDensityComfortable: 'Камфортны',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Слупкі',\n  toolbarColumnsLabel: 'Выберыце слупкі',\n  // Filters toolbar button text\n  toolbarFilters: 'Фільтры',\n  toolbarFiltersLabel: 'Паказаць фільтры',\n  toolbarFiltersTooltipHide: 'Схаваць фільтры',\n  toolbarFiltersTooltipShow: 'Паказаць фільтры',\n  toolbarFiltersTooltipActive: count => getPluralForm(count, {\n    one: 'актыўны фільтр',\n    few: 'актыўных фільтра',\n    many: 'актыўных фільтраў'\n  }),\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Пошук…',\n  toolbarQuickFilterLabel: 'Пошук',\n  toolbarQuickFilterDeleteIconLabel: 'Ачысціць',\n  // Export selector toolbar button text\n  toolbarExport: 'Экспарт',\n  toolbarExportLabel: 'Экспарт',\n  toolbarExportCSV: 'Спампаваць у фармаце CSV',\n  toolbarExportPrint: 'Друк',\n  toolbarExportExcel: 'Спампаваць у фармаце Excel',\n  // Columns management text\n  // columnsManagementSearchTitle: 'Search',\n  // columnsManagementNoColumns: 'No columns',\n  // columnsManagementShowHideAllText: 'Show/Hide All',\n  // columnsManagementReset: 'Reset',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Дадаць фільтр',\n  // filterPanelRemoveAll: 'Remove all',\n  filterPanelDeleteIconLabel: 'Выдаліць',\n  filterPanelLogicOperator: 'Лагічныя аператары',\n  filterPanelOperator: 'Аператары',\n  filterPanelOperatorAnd: 'І',\n  filterPanelOperatorOr: 'Або',\n  filterPanelColumns: 'Слупкі',\n  filterPanelInputLabel: 'Значэнне',\n  filterPanelInputPlaceholder: 'Значэнне фільтра',\n  // Filter operators text\n  filterOperatorContains: 'змяшчае',\n  // filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'роўны',\n  // filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'пачынаецца з',\n  filterOperatorEndsWith: 'скончваецца на',\n  filterOperatorIs: 'роўны',\n  filterOperatorNot: 'не роўны',\n  filterOperatorAfter: 'больш чым',\n  filterOperatorOnOrAfter: 'больш ці роўны',\n  filterOperatorBefore: 'меньш чым',\n  filterOperatorOnOrBefore: 'меньш ці роўны',\n  filterOperatorIsEmpty: 'пусты',\n  filterOperatorIsNotEmpty: 'не пусты',\n  filterOperatorIsAnyOf: 'усякі з',\n  // 'filterOperator=': '=',\n  // 'filterOperator!=': '!=',\n  // 'filterOperator>': '>',\n  // 'filterOperator>=': '>=',\n  // 'filterOperator<': '<',\n  // 'filterOperator<=': '<=',\n\n  // Header filter operators text\n  // headerFilterOperatorContains: 'Contains',\n  // headerFilterOperatorDoesNotContain: 'Does not contain',\n  // headerFilterOperatorEquals: 'Equals',\n  // headerFilterOperatorDoesNotEqual: 'Does not equal',\n  // headerFilterOperatorStartsWith: 'Starts with',\n  // headerFilterOperatorEndsWith: 'Ends with',\n  // headerFilterOperatorIs: 'Is',\n  // headerFilterOperatorNot: 'Is not',\n  // headerFilterOperatorAfter: 'Is after',\n  // headerFilterOperatorOnOrAfter: 'Is on or after',\n  // headerFilterOperatorBefore: 'Is before',\n  // headerFilterOperatorOnOrBefore: 'Is on or before',\n  // headerFilterOperatorIsEmpty: 'Is empty',\n  // headerFilterOperatorIsNotEmpty: 'Is not empty',\n  // headerFilterOperatorIsAnyOf: 'Is any of',\n  // 'headerFilterOperator=': 'Equals',\n  // 'headerFilterOperator!=': 'Not equals',\n  // 'headerFilterOperator>': 'Greater than',\n  // 'headerFilterOperator>=': 'Greater than or equal to',\n  // 'headerFilterOperator<': 'Less than',\n  // 'headerFilterOperator<=': 'Less than or equal to',\n\n  // Filter values text\n  filterValueAny: 'усякі',\n  filterValueTrue: 'праўда',\n  filterValueFalse: 'няпраўда',\n  // Column menu text\n  columnMenuLabel: 'Меню',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Паказаць слупкі',\n  columnMenuManageColumns: 'Кіраваць слупкамі',\n  columnMenuFilter: 'Фільтр',\n  columnMenuHideColumn: 'Схаваць',\n  columnMenuUnsort: 'Скасаваць сартыроўку',\n  columnMenuSortAsc: 'Сартыраваць па нарастанню',\n  columnMenuSortDesc: 'Сартыраваць па спаданню',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => getPluralForm(count, {\n    one: 'актыўны фільтр',\n    few: 'актыўных фільтра',\n    many: 'актыўных фільтраў'\n  }),\n  columnHeaderFiltersLabel: 'Паказаць фільтры',\n  columnHeaderSortIconLabel: 'Сартыраваць',\n  // Rows selected footer text\n  footerRowSelected: count => getPluralForm(count, {\n    one: 'абраны радок',\n    few: 'абраных радка',\n    many: 'абраных радкоў'\n  }),\n  // Total row amount footer text\n  footerTotalRows: 'Усяго радкоў:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} з ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Выбар сцяжка',\n  checkboxSelectionSelectAllRows: 'Абраць усе радкі',\n  checkboxSelectionUnselectAllRows: 'Скасаваць выбар усіх радкоў',\n  checkboxSelectionSelectRow: 'Абраць радок',\n  checkboxSelectionUnselectRow: 'Скасаваць выбар радка',\n  // Boolean cell text\n  booleanCellTrueLabel: 'праўда',\n  booleanCellFalseLabel: 'няпраўда',\n  // Actions cell more text\n  actionsCellMore: 'больш',\n  // Column pinning text\n  pinToLeft: 'Замацаваць злева',\n  pinToRight: 'Замацаваць справа',\n  unpin: 'Адмацаваць',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Група',\n  treeDataExpand: 'паказаць даччыныя элементы',\n  treeDataCollapse: 'схаваць даччыныя элементы',\n  // Grouping columns\n  groupingColumnHeaderName: 'Група',\n  groupColumn: name => `Групаваць па ${name}`,\n  unGroupColumn: name => `Разгрупаваць па ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Дэталі',\n  expandDetailPanel: 'Разгарнуць',\n  collapseDetailPanel: 'Згарнуць',\n  // Row reordering text\n  rowReorderingHeaderName: 'Змяненне чарговасці радкоў',\n  // Aggregation\n  aggregationMenuItemHeader: \"Аб'яднанне дадзеных\",\n  aggregationFunctionLabelSum: 'сума',\n  aggregationFunctionLabelAvg: 'сярэдняе',\n  aggregationFunctionLabelMin: 'мінімум',\n  aggregationFunctionLabelMax: 'максімум',\n  aggregationFunctionLabelSize: 'памер'\n};\nexport const beBY = getGridLocalization(beBYGrid, beBYCore);", "import { bgBG as bgBGCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst bgBGGrid = {\n  // Root\n  noRowsLabel: 'Няма редове',\n  noResultsOverlayLabel: 'Няма намерени резултати.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Гъстота',\n  toolbarDensityLabel: 'Гъстота',\n  toolbarDensityCompact: 'Компактна',\n  toolbarDensityStandard: 'Стандартна',\n  toolbarDensityComfortable: 'Комфортна',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Колони',\n  toolbarColumnsLabel: 'Покажи селектора на колони',\n  // Filters toolbar button text\n  toolbarFilters: 'Филтри',\n  toolbarFiltersLabel: 'Покажи Филтрите',\n  toolbarFiltersTooltipHide: 'Скрий Филтрите',\n  toolbarFiltersTooltipShow: 'Покажи Филтрите',\n  toolbarFiltersTooltipActive: count => `${count} активни филтри`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Търси…',\n  toolbarQuickFilterLabel: 'Търсене',\n  toolbarQuickFilterDeleteIconLabel: 'Изчисти',\n  // Export selector toolbar button text\n  toolbarExport: 'Изтегли',\n  toolbarExportLabel: 'Изтегли',\n  toolbarExportCSV: 'Изтегли като CSV',\n  toolbarExportPrint: 'Принтиране',\n  toolbarExportExcel: 'Изтегли като Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Търсене',\n  columnsManagementNoColumns: 'Няма колони',\n  columnsManagementShowHideAllText: 'Покажи/Скрий Всичко',\n  columnsManagementReset: 'Нулирай',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Добави Филтър',\n  filterPanelRemoveAll: 'Премахни всички',\n  filterPanelDeleteIconLabel: 'Изтрий',\n  filterPanelLogicOperator: 'Логически оператор',\n  filterPanelOperator: 'Оператори',\n  filterPanelOperatorAnd: 'И',\n  filterPanelOperatorOr: 'Или',\n  filterPanelColumns: 'Колони',\n  filterPanelInputLabel: 'Стойност',\n  filterPanelInputPlaceholder: 'Стойност на филтъра',\n  // Filter operators text\n  filterOperatorContains: 'съдържа',\n  filterOperatorDoesNotContain: 'не съдържа',\n  filterOperatorEquals: 'равно',\n  filterOperatorDoesNotEqual: 'не е равно',\n  filterOperatorStartsWith: 'започва с',\n  filterOperatorEndsWith: 'завършва с',\n  filterOperatorIs: 'е',\n  filterOperatorNot: 'не е',\n  filterOperatorAfter: 'е след',\n  filterOperatorOnOrAfter: 'е на или след',\n  filterOperatorBefore: 'е преди',\n  filterOperatorOnOrBefore: 'е на или преди',\n  filterOperatorIsEmpty: 'е празен',\n  filterOperatorIsNotEmpty: 'не е празен',\n  filterOperatorIsAnyOf: 'е някой от',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Съдържа',\n  headerFilterOperatorDoesNotContain: 'Не съдържа',\n  headerFilterOperatorEquals: 'Равнo',\n  headerFilterOperatorDoesNotEqual: 'Не е равно',\n  headerFilterOperatorStartsWith: 'Започва с',\n  headerFilterOperatorEndsWith: 'Завършва с',\n  headerFilterOperatorIs: 'Равно е на',\n  headerFilterOperatorNot: 'Не се равнява на',\n  headerFilterOperatorAfter: 'След',\n  headerFilterOperatorOnOrAfter: 'След (включително)',\n  headerFilterOperatorBefore: 'Преди',\n  headerFilterOperatorOnOrBefore: 'Преди (включително)',\n  headerFilterOperatorIsEmpty: 'Празен',\n  headerFilterOperatorIsNotEmpty: 'Не е празен',\n  headerFilterOperatorIsAnyOf: 'Всичко от',\n  'headerFilterOperator=': 'Равно',\n  'headerFilterOperator!=': 'Различно',\n  'headerFilterOperator>': 'По-голямо от',\n  'headerFilterOperator>=': 'По-голямо или равно на',\n  'headerFilterOperator<': 'По-малко от',\n  'headerFilterOperator<=': 'По-малко или равно на',\n  // Filter values text\n  filterValueAny: 'всякакви',\n  filterValueTrue: 'вярно',\n  filterValueFalse: 'невярно',\n  // Column menu text\n  columnMenuLabel: 'Меню',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Покажи колоните',\n  columnMenuManageColumns: 'Управление на колони',\n  columnMenuFilter: 'Филтри',\n  columnMenuHideColumn: 'Скрий',\n  columnMenuUnsort: 'Отмени сортирането',\n  columnMenuSortAsc: 'Сортирай по възходящ ред',\n  columnMenuSortDesc: 'Сортирай по низходящ ред',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `${count} активни филтри`,\n  columnHeaderFiltersLabel: 'Покажи Филтрите',\n  columnHeaderSortIconLabel: 'Сортирай',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} избрани редове` : `${count.toLocaleString()} избран ред`,\n  // Total row amount footer text\n  footerTotalRows: 'Общо Редове:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} от ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Избор на квадратче',\n  checkboxSelectionSelectAllRows: 'Избери всички редове',\n  checkboxSelectionUnselectAllRows: 'Отмени избора на всички редове',\n  checkboxSelectionSelectRow: 'Избери ред',\n  checkboxSelectionUnselectRow: 'Отмени избора на ред',\n  // Boolean cell text\n  booleanCellTrueLabel: 'да',\n  booleanCellFalseLabel: 'не',\n  // Actions cell more text\n  actionsCellMore: 'още',\n  // Column pinning text\n  pinToLeft: 'Закачи в ляво',\n  pinToRight: 'Закачи в дясно',\n  unpin: 'Откачи',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Група',\n  treeDataExpand: 'виж деца',\n  treeDataCollapse: 'скрий децата',\n  // Grouping columns\n  groupingColumnHeaderName: 'Група',\n  groupColumn: name => `Групирай по ${name}`,\n  unGroupColumn: name => `Спри групиране по ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Превключване на панела с детайли',\n  expandDetailPanel: 'Разгъване',\n  collapseDetailPanel: 'Свиване',\n  // Row reordering text\n  rowReorderingHeaderName: 'Подредба на редове',\n  // Aggregation\n  aggregationMenuItemHeader: 'Агрегиране',\n  aggregationFunctionLabelSum: 'сума',\n  aggregationFunctionLabelAvg: 'срст',\n  aggregationFunctionLabelMin: 'мин',\n  aggregationFunctionLabelMax: 'макс',\n  aggregationFunctionLabelSize: 'размер'\n};\nexport const bgBG = getGridLocalization(bgBGGrid, bgBGCore);", "import { bnBD as bnBDCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst bnBDGrid = {\n  // Root\n  noRowsLabel: 'কোনো সারি নেই',\n  noResultsOverlayLabel: 'কোনো ফলাফল পাওয়া যায়নি।',\n  // Density selector toolbar button text\n  toolbarDensity: 'ঘনত্ব',\n  toolbarDensityLabel: 'ঘনত্ব',\n  toolbarDensityCompact: 'সংকুচিত',\n  toolbarDensityStandard: 'মানক',\n  toolbarDensityComfortable: 'স্বাচ্ছন্দ্যদায়ক',\n  // Columns selector toolbar button text\n  toolbarColumns: 'কলাম',\n  toolbarColumnsLabel: 'কলাম নির্বাচন করুন',\n  // Filters toolbar button text\n  toolbarFilters: 'ফিল্টার',\n  toolbarFiltersLabel: 'ফিল্টার দেখান',\n  toolbarFiltersTooltipHide: 'ফিল্টার লুকান',\n  toolbarFiltersTooltipShow: 'ফিল্টার দেখান',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} টি সক্রিয় ফিল্টার` : `${count} টি সক্রিয় ফিল্টার`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'অনুসন্ধান করুন…',\n  toolbarQuickFilterLabel: 'অনুসন্ধান',\n  toolbarQuickFilterDeleteIconLabel: 'পরিষ্কার করুন',\n  // Export selector toolbar button text\n  toolbarExport: 'এক্সপোর্ট',\n  toolbarExportLabel: 'এক্সপোর্ট',\n  toolbarExportCSV: 'CSV হিসাবে ডাউনলোড করুন',\n  toolbarExportPrint: 'প্রিন্ট করুন',\n  toolbarExportExcel: 'Excel হিসাবে ডাউনলোড করুন',\n  // Columns management text\n  columnsManagementSearchTitle: 'অনুসন্ধান',\n  columnsManagementNoColumns: 'কোনো কলাম নেই',\n  columnsManagementShowHideAllText: 'সব দেখান/লুকান',\n  columnsManagementReset: 'রিসেট',\n  columnsManagementDeleteIconLabel: 'পরিষ্কার',\n  // Filter panel text\n  filterPanelAddFilter: 'ফিল্টার যোগ করুন',\n  filterPanelRemoveAll: 'সব সরান',\n  filterPanelDeleteIconLabel: 'মুছুন',\n  filterPanelLogicOperator: 'লজিক অপারেটর',\n  filterPanelOperator: 'অপারেটর',\n  filterPanelOperatorAnd: 'এবং',\n  filterPanelOperatorOr: 'অথবা',\n  filterPanelColumns: 'কলাম',\n  filterPanelInputLabel: 'মান',\n  filterPanelInputPlaceholder: 'ফিল্টার মান',\n  // Filter operators text\n  filterOperatorContains: 'অন্তর্ভুক্ত',\n  filterOperatorDoesNotContain: 'অন্তর্ভুক্ত নয়',\n  filterOperatorEquals: 'সমান',\n  filterOperatorDoesNotEqual: 'সমান নয়',\n  filterOperatorStartsWith: 'দিয়ে শুরু হয়',\n  filterOperatorEndsWith: 'দিয়ে শেষ হয়',\n  filterOperatorIs: 'হচ্ছে',\n  filterOperatorNot: 'হচ্ছে না',\n  filterOperatorAfter: 'পরবর্তী',\n  filterOperatorOnOrAfter: 'এই তারিখ বা পরবর্তী',\n  filterOperatorBefore: 'পূর্ববর্তী',\n  filterOperatorOnOrBefore: 'এই তারিখ বা পূর্ববর্তী',\n  filterOperatorIsEmpty: 'খালি',\n  filterOperatorIsNotEmpty: 'খালি নয়',\n  filterOperatorIsAnyOf: 'এর যেকোনো একটি',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'অন্তর্ভুক্ত',\n  headerFilterOperatorDoesNotContain: 'অন্তর্ভুক্ত নয়',\n  headerFilterOperatorEquals: 'সমান',\n  headerFilterOperatorDoesNotEqual: 'সমান নয়',\n  headerFilterOperatorStartsWith: 'দিয়ে শুরু হয়',\n  headerFilterOperatorEndsWith: 'দিয়ে শেষ হয়',\n  headerFilterOperatorIs: 'হচ্ছে',\n  headerFilterOperatorNot: 'হচ্ছে না',\n  headerFilterOperatorAfter: 'পরবর্তী',\n  headerFilterOperatorOnOrAfter: 'এই তারিখ বা পরবর্তী',\n  headerFilterOperatorBefore: 'পূর্ববর্তী',\n  headerFilterOperatorOnOrBefore: 'এই তারিখ বা পূর্ববর্তী',\n  headerFilterOperatorIsEmpty: 'খালি',\n  headerFilterOperatorIsNotEmpty: 'খালি নয়',\n  headerFilterOperatorIsAnyOf: 'এর যেকোনো একটি',\n  'headerFilterOperator=': 'সমান',\n  'headerFilterOperator!=': 'সমান নয়',\n  'headerFilterOperator>': 'বড়',\n  'headerFilterOperator>=': 'বড় বা সমান',\n  'headerFilterOperator<': 'ছোট',\n  'headerFilterOperator<=': 'ছোট বা সমান',\n  // Filter values text\n  filterValueAny: 'যেকোনো',\n  filterValueTrue: 'সত্য',\n  filterValueFalse: 'মিথ্যা',\n  // Column menu text\n  columnMenuLabel: 'মেনু',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'কলাম দেখান',\n  columnMenuManageColumns: 'কলাম পরিচালনা করুন',\n  columnMenuFilter: 'ফিল্টার',\n  columnMenuHideColumn: 'কলাম লুকান',\n  columnMenuUnsort: 'সাজানো বাতিল করুন',\n  columnMenuSortAsc: 'ASC অনুযায়ী সাজান',\n  columnMenuSortDesc: 'DESC অনুযায়ী সাজান',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} টি সক্রিয় ফিল্টার` : `${count} টি সক্রিয় ফিল্টার`,\n  columnHeaderFiltersLabel: 'ফিল্টার দেখান',\n  columnHeaderSortIconLabel: 'সাজান',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} টি সারি নির্বাচিত` : `${count.toLocaleString()} টি সারি নির্বাচিত`,\n  // Total row amount footer text\n  footerTotalRows: 'মোট সারি:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} of ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'চেকবক্স নির্বাচন',\n  checkboxSelectionSelectAllRows: 'সব সারি নির্বাচন করুন',\n  checkboxSelectionUnselectAllRows: 'সব সারি নির্বাচন বাতিল করুন',\n  checkboxSelectionSelectRow: 'সারি নির্বাচন করুন',\n  checkboxSelectionUnselectRow: 'সারি নির্বাচন বাতিল করুন',\n  // Boolean cell text\n  booleanCellTrueLabel: 'হ্যাঁ',\n  booleanCellFalseLabel: 'না',\n  // Actions cell more text\n  actionsCellMore: 'আরও',\n  // Column pinning text\n  pinToLeft: 'বাঁ দিকে পিন করুন',\n  pinToRight: 'ডান দিকে পিন করুন',\n  unpin: 'আনপিন করুন',\n  // Tree Data\n  treeDataGroupingHeaderName: 'গ্রুপ',\n  // treeDataExpand: 'see children',\n  // treeDataCollapse: 'hide children',\n\n  // Grouping columns\n  groupingColumnHeaderName: 'গ্রুপ',\n  groupColumn: name => `${name} অনুসারে গ্রুপ করুন`,\n  unGroupColumn: name => `${name} অনুসারে গ্রুপ বন্ধ করুন`,\n  // Master/detail\n  detailPanelToggle: 'বিস্তারিত প্যানেল টগল করুন',\n  expandDetailPanel: 'সম্প্রসারিত করুন',\n  collapseDetailPanel: 'সংকুচিত করুন',\n  // Row reordering text\n  rowReorderingHeaderName: 'সারি পুনর্বিন্যাস',\n  // Aggregation\n  aggregationMenuItemHeader: 'সংকলন',\n  aggregationFunctionLabelSum: 'যোগফল',\n  aggregationFunctionLabelAvg: 'গড়',\n  aggregationFunctionLabelMin: 'সর্বনিম্ন',\n  aggregationFunctionLabelMax: 'সর্বোচ্চ',\n  aggregationFunctionLabelSize: 'মাপ'\n};\nexport const bnBD = getGridLocalization(bnBDGrid, bnBDCore);", "import { csCZ as csCZCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst csCZGrid = {\n  // Root\n  noRowsLabel: '<PERSON><PERSON><PERSON><PERSON>',\n  noResultsOverlayLabel: '<PERSON><PERSON><PERSON><PERSON> se ž<PERSON> v<PERSON>.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Zobrazení',\n  toolbarDensityLabel: 'Zobrazení',\n  toolbarDensityCompact: 'Kompaktní',\n  toolbarDensityStandard: 'Standartní',\n  toolbarDensityComfortable: 'Komfortní',\n  // Columns selector toolbar button text\n  toolbarColumns: '<PERSON>loup<PERSON>',\n  toolbarColumnsLabel: 'Vybrat sloupec',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtry',\n  toolbarFiltersLabel: 'Zobrazit filtry',\n  toolbarFiltersTooltipHide: 'Skrýt filtry',\n  toolbarFiltersTooltipShow: 'Zobrazit filtry',\n  toolbarFiltersTooltipActive: count => {\n    let pluralForm = 'aktivních filtrů';\n    if (count > 1 && count < 5) {\n      pluralForm = 'aktivní filtry';\n    } else if (count === 1) {\n      pluralForm = 'aktivní filtr';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Hledat…',\n  toolbarQuickFilterLabel: 'Hledat',\n  toolbarQuickFilterDeleteIconLabel: 'Vymazat',\n  // Export selector toolbar button text\n  toolbarExport: 'Export',\n  toolbarExportLabel: 'Export',\n  toolbarExportCSV: 'Stáhnout jako CSV',\n  toolbarExportPrint: 'Vytisknout',\n  toolbarExportExcel: 'Stáhnout jako Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Hledat sloupce',\n  columnsManagementNoColumns: 'Žádné sloupce',\n  columnsManagementShowHideAllText: 'Zobrazit/skrýt vše',\n  columnsManagementReset: 'Resetovat',\n  columnsManagementDeleteIconLabel: 'Vyčistit',\n  // Filter panel text\n  filterPanelAddFilter: 'Přidat filtr',\n  filterPanelRemoveAll: 'Odstranit vše',\n  filterPanelDeleteIconLabel: 'Odstranit',\n  filterPanelLogicOperator: 'Logický operátor',\n  filterPanelOperator: 'Operátory',\n  filterPanelOperatorAnd: 'A',\n  filterPanelOperatorOr: 'Nebo',\n  filterPanelColumns: 'Sloupce',\n  filterPanelInputLabel: 'Hodnota',\n  filterPanelInputPlaceholder: 'Hodnota filtru',\n  // Filter operators text\n  filterOperatorContains: 'obsahuje',\n  filterOperatorDoesNotContain: 'neobsahuje',\n  filterOperatorEquals: 'rovná se',\n  filterOperatorDoesNotEqual: 'nerovná se',\n  filterOperatorStartsWith: 'začíná na',\n  filterOperatorEndsWith: 'končí na',\n  filterOperatorIs: 'je',\n  filterOperatorNot: 'není',\n  filterOperatorAfter: 'je po',\n  filterOperatorOnOrAfter: 'je po včetně',\n  filterOperatorBefore: 'je před',\n  filterOperatorOnOrBefore: 'je před včetně',\n  filterOperatorIsEmpty: 'je prázdný',\n  filterOperatorIsNotEmpty: 'není prázdný',\n  filterOperatorIsAnyOf: 'je jeden z',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Obsahuje',\n  headerFilterOperatorDoesNotContain: 'Neobsahuje',\n  headerFilterOperatorEquals: 'Rovná se',\n  headerFilterOperatorDoesNotEqual: 'Nerovná se',\n  headerFilterOperatorStartsWith: 'Začíná na',\n  headerFilterOperatorEndsWith: 'Končí na',\n  headerFilterOperatorIs: 'Je',\n  headerFilterOperatorNot: 'Není',\n  headerFilterOperatorAfter: 'Je po',\n  headerFilterOperatorOnOrAfter: 'Je po včetně',\n  headerFilterOperatorBefore: 'Je před',\n  headerFilterOperatorOnOrBefore: 'Je před včetně',\n  headerFilterOperatorIsEmpty: 'Je prázdný',\n  headerFilterOperatorIsNotEmpty: 'Není prázdný',\n  headerFilterOperatorIsAnyOf: 'Je jeden z',\n  'headerFilterOperator=': 'Rovná se',\n  'headerFilterOperator!=': 'Nerovná se',\n  'headerFilterOperator>': 'Větší než',\n  'headerFilterOperator>=': 'Větší než nebo rovno',\n  'headerFilterOperator<': 'Menší než',\n  'headerFilterOperator<=': 'Menší než nebo rovno',\n  // Filter values text\n  filterValueAny: 'jakýkoliv',\n  filterValueTrue: 'ano',\n  filterValueFalse: 'ne',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  columnMenuAriaLabel: columnName => `Možnosti sloupce ${columnName}`,\n  columnMenuShowColumns: 'Zobrazit sloupce',\n  columnMenuManageColumns: 'Spravovat sloupce',\n  columnMenuFilter: 'Filtr',\n  columnMenuHideColumn: 'Skrýt',\n  columnMenuUnsort: 'Zrušit filtry',\n  columnMenuSortAsc: 'Seřadit vzestupně',\n  columnMenuSortDesc: 'Seřadit sestupně',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => {\n    let pluralForm = 'aktivních filtrů';\n    if (count > 1 && count < 5) {\n      pluralForm = 'aktivní filtry';\n    } else if (count === 1) {\n      pluralForm = 'aktivní filtr';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  columnHeaderFiltersLabel: 'Zobrazit filtry',\n  columnHeaderSortIconLabel: 'Filtrovat',\n  // Rows selected footer text\n  footerRowSelected: count => {\n    let pluralForm = 'vybraných záznamů';\n    if (count > 1 && count < 5) {\n      pluralForm = 'vybrané záznamy';\n    } else if (count === 1) {\n      pluralForm = 'vybraný záznam';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  // Total row amount footer text\n  footerTotalRows: 'Celkem řádků:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => {\n    const str = totalCount.toString();\n    const firstDigit = str[0];\n    const op = ['4', '6', '7'].includes(firstDigit) || firstDigit === '1' && str.length % 3 === 0 ? 'ze' : 'z';\n    return `${visibleCount.toLocaleString()} ${op} ${totalCount.toLocaleString()}`;\n  },\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Výběr řádku',\n  checkboxSelectionSelectAllRows: 'Označit všechny řádky',\n  checkboxSelectionUnselectAllRows: 'Odznačit všechny řádky',\n  checkboxSelectionSelectRow: 'Označit řádek',\n  checkboxSelectionUnselectRow: 'Odznačit řádek',\n  // Boolean cell text\n  booleanCellTrueLabel: 'ano',\n  booleanCellFalseLabel: 'ne',\n  // Actions cell more text\n  actionsCellMore: 'více',\n  // Column pinning text\n  pinToLeft: 'Připnout vlevo',\n  pinToRight: 'Připnout vpravo',\n  unpin: 'Odepnout',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Skupina',\n  treeDataExpand: 'zobrazit potomky',\n  treeDataCollapse: 'skrýt potomky',\n  // Grouping columns\n  groupingColumnHeaderName: 'Skupina',\n  groupColumn: name => `Seskupit podle ${name}`,\n  unGroupColumn: name => `Přestat seskupovat podle ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Přepnout detail panelu',\n  expandDetailPanel: 'Rozbalit',\n  collapseDetailPanel: 'Sbalit',\n  // Row reordering text\n  rowReorderingHeaderName: 'Přeuspořádávání řádků',\n  // Aggregation\n  aggregationMenuItemHeader: 'Seskupování',\n  aggregationFunctionLabelSum: 'součet',\n  aggregationFunctionLabelAvg: 'průměr',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'počet'\n};\nexport const csCZ = getGridLocalization(csCZGrid, csCZCore);", "import { daDK as daDKCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst daDKGrid = {\n  // Root\n  noRowsLabel: 'Ingen rækker',\n  noResultsOverlayLabel: 'Ingen resultater',\n  // Density selector toolbar button text\n  toolbarDensity: 'Tæthed',\n  toolbarDensityLabel: 'Tæthed',\n  toolbarDensityCompact: 'Kompakt',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Luftig',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Kolon<PERSON>',\n  toolbarColumnsLabel: 'Vælg kolonner',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtre',\n  toolbarFiltersLabel: 'Vis filtre',\n  toolbarFiltersTooltipHide: 'Skjul filtre',\n  toolbarFiltersTooltipShow: 'Vis filtre',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} aktive filtre` : `${count} aktivt filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Søg…',\n  toolbarQuickFilterLabel: 'Søg',\n  toolbarQuickFilterDeleteIconLabel: 'Ryd',\n  // Export selector toolbar button text\n  toolbarExport: 'Eksport',\n  toolbarExportLabel: 'Eksporter',\n  toolbarExportCSV: 'Download som CSV',\n  toolbarExportPrint: 'Print',\n  toolbarExportExcel: 'Download som Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Søg',\n  columnsManagementNoColumns: 'Ingen søjler',\n  columnsManagementShowHideAllText: 'Vis/Skjul Alle',\n  columnsManagementReset: 'Nulstil',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Tilføj filter',\n  filterPanelRemoveAll: 'Fjern alle',\n  filterPanelDeleteIconLabel: 'Slet',\n  filterPanelLogicOperator: 'Logisk operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'Og',\n  filterPanelOperatorOr: 'Eller',\n  filterPanelColumns: 'Kolonner',\n  filterPanelInputLabel: 'Værdi',\n  filterPanelInputPlaceholder: 'Filterværdi',\n  // Filter operators text\n  filterOperatorContains: 'indeholder',\n  filterOperatorDoesNotContain: 'indeholder ikke',\n  filterOperatorEquals: 'lig med',\n  filterOperatorDoesNotEqual: 'ikke lig med',\n  filterOperatorStartsWith: 'begynder med',\n  filterOperatorEndsWith: 'ender med',\n  filterOperatorIs: 'er lig med',\n  filterOperatorNot: 'er ikke lig med',\n  filterOperatorAfter: 'efter',\n  filterOperatorOnOrAfter: 'på eller efter',\n  filterOperatorBefore: 'før',\n  filterOperatorOnOrBefore: 'på eller før',\n  filterOperatorIsEmpty: 'indeholder ikke data',\n  filterOperatorIsNotEmpty: 'indeholder data',\n  filterOperatorIsAnyOf: 'indeholder en af',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Indeholder',\n  headerFilterOperatorDoesNotContain: 'Indeholder ikke',\n  headerFilterOperatorEquals: 'Lig med',\n  headerFilterOperatorDoesNotEqual: 'Ikke lig med',\n  headerFilterOperatorStartsWith: 'Begynder med',\n  headerFilterOperatorEndsWith: 'Ender med',\n  headerFilterOperatorIs: 'Er lig med',\n  headerFilterOperatorNot: 'Er ikke lig med',\n  headerFilterOperatorAfter: 'Efter',\n  headerFilterOperatorOnOrAfter: 'På eller efter',\n  headerFilterOperatorBefore: 'Før',\n  headerFilterOperatorOnOrBefore: 'På eller før',\n  headerFilterOperatorIsEmpty: 'Indeholder ikke data',\n  headerFilterOperatorIsNotEmpty: 'Indeholder data',\n  headerFilterOperatorIsAnyOf: 'Indeholder en af',\n  'headerFilterOperator=': 'Lig med',\n  'headerFilterOperator!=': 'Ikke lig med',\n  'headerFilterOperator>': 'Større end',\n  'headerFilterOperator>=': 'Større end eller lig med',\n  'headerFilterOperator<': 'Mindre end',\n  'headerFilterOperator<=': 'Mindre end eller lig med',\n  // Filter values text\n  filterValueAny: 'hvilken som helst',\n  filterValueTrue: 'positiv',\n  filterValueFalse: 'negativ',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Vis kolonner',\n  columnMenuManageColumns: 'Administrer kolonner',\n  columnMenuFilter: 'Filtrer',\n  columnMenuHideColumn: 'Skjul kolonne',\n  columnMenuUnsort: 'Fjern sortering',\n  columnMenuSortAsc: 'Sorter stigende',\n  columnMenuSortDesc: 'Sorter faldende',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} aktive filtre` : `Ét aktivt filter`,\n  columnHeaderFiltersLabel: 'Vis filtre',\n  columnHeaderSortIconLabel: 'Sorter',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} rækker valgt` : `Én række valgt`,\n  // Total row amount footer text\n  footerTotalRows: 'Antal rækker i alt:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} af ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Afkrydsningsvalg',\n  checkboxSelectionSelectAllRows: 'Vælg alle rækker',\n  checkboxSelectionUnselectAllRows: 'Fravælg alle rækker',\n  checkboxSelectionSelectRow: 'Vælg række',\n  checkboxSelectionUnselectRow: 'Fravælg række',\n  // Boolean cell text\n  booleanCellTrueLabel: 'ja',\n  booleanCellFalseLabel: 'nej',\n  // Actions cell more text\n  actionsCellMore: 'mere',\n  // Column pinning text\n  pinToLeft: 'Fastgør til venstre',\n  pinToRight: 'Fastgør til højre',\n  unpin: 'Frigiv',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Gruppe',\n  treeDataExpand: 'Vis underelementer',\n  treeDataCollapse: 'Skjul underelementer',\n  // Grouping columns\n  groupingColumnHeaderName: 'Gruppe',\n  groupColumn: name => `Gruppér efter ${name}`,\n  unGroupColumn: name => `Fjern gruppering efter ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Udvid/kollaps detaljepanel',\n  expandDetailPanel: 'Udvid',\n  collapseDetailPanel: 'Kollaps',\n  // Row reordering text\n  rowReorderingHeaderName: 'Omarrangering af rækker',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregering',\n  aggregationFunctionLabelSum: 'sum',\n  aggregationFunctionLabelAvg: 'gns',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'størrelse'\n};\nexport const daDK = getGridLocalization(daDKGrid, daDKCore);", "import { deDE as deDECore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst deDEGrid = {\n  // Root\n  noRowsLabel: 'Keine Einträge',\n  noResultsOverlayLabel: 'Keine Ergebnisse gefunden.',\n  // Density selector toolbar button text\n  toolbarDensity: '<PERSON><PERSON><PERSON>h<PERSON><PERSON>',\n  toolbarDensityLabel: '<PERSON>eil<PERSON>hö<PERSON>',\n  toolbarDensityCompact: 'Kompakt',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Breit',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Spalten',\n  toolbarColumnsLabel: '<PERSON><PERSON>ge Spaltenauswahl',\n  // Filters toolbar button text\n  toolbarFilters: 'Filter',\n  toolbarFiltersLabel: 'Zeige Filter',\n  toolbarFiltersTooltipHide: 'Verberge Filter',\n  toolbarFiltersTooltipShow: 'Zeige Filter',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} aktive Filter` : `${count} aktiver Filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Suchen…',\n  toolbarQuickFilterLabel: 'Suchen',\n  toolbarQuickFilterDeleteIconLabel: 'Löschen',\n  // Export selector toolbar button text\n  toolbarExport: 'Exportieren',\n  toolbarExportLabel: 'Exportieren',\n  toolbarExportCSV: 'Download als CSV',\n  toolbarExportPrint: 'Drucken',\n  toolbarExportExcel: 'Download als Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Suche',\n  columnsManagementNoColumns: 'Keine Spalten',\n  columnsManagementShowHideAllText: 'Alle anzeigen/verbergen',\n  columnsManagementReset: 'Zurücksetzen',\n  columnsManagementDeleteIconLabel: 'Löschen',\n  // Filter panel text\n  filterPanelAddFilter: 'Filter hinzufügen',\n  filterPanelRemoveAll: 'Alle entfernen',\n  filterPanelDeleteIconLabel: 'Löschen',\n  filterPanelLogicOperator: 'Logische Operatoren',\n  filterPanelOperator: 'Operatoren',\n  filterPanelOperatorAnd: 'Und',\n  filterPanelOperatorOr: 'Oder',\n  filterPanelColumns: 'Spalten',\n  filterPanelInputLabel: 'Wert',\n  filterPanelInputPlaceholder: 'Wert filtern',\n  // Filter operators text\n  filterOperatorContains: 'enthält',\n  filterOperatorDoesNotContain: 'enthält nicht',\n  filterOperatorEquals: 'ist gleich',\n  filterOperatorDoesNotEqual: 'ist ungleich',\n  filterOperatorStartsWith: 'beginnt mit',\n  filterOperatorEndsWith: 'endet mit',\n  filterOperatorIs: 'ist',\n  filterOperatorNot: 'ist nicht',\n  filterOperatorAfter: 'ist nach',\n  filterOperatorOnOrAfter: 'ist am oder nach',\n  filterOperatorBefore: 'ist vor',\n  filterOperatorOnOrBefore: 'ist am oder vor',\n  filterOperatorIsEmpty: 'ist leer',\n  filterOperatorIsNotEmpty: 'ist nicht leer',\n  filterOperatorIsAnyOf: 'ist einer der Werte',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Enthält',\n  headerFilterOperatorDoesNotContain: 'Enthält nicht',\n  headerFilterOperatorEquals: 'Gleich',\n  headerFilterOperatorDoesNotEqual: 'Ungleich',\n  headerFilterOperatorStartsWith: 'Beginnt mit',\n  headerFilterOperatorEndsWith: 'Endet mit',\n  headerFilterOperatorIs: 'Ist',\n  headerFilterOperatorNot: 'Ist nicht',\n  headerFilterOperatorAfter: 'Ist nach',\n  headerFilterOperatorOnOrAfter: 'Ist am oder nach',\n  headerFilterOperatorBefore: 'Ist vor',\n  headerFilterOperatorOnOrBefore: 'Ist am oder vor',\n  headerFilterOperatorIsEmpty: 'Ist leer',\n  headerFilterOperatorIsNotEmpty: 'Ist nicht leer',\n  headerFilterOperatorIsAnyOf: 'Ist eines von',\n  'headerFilterOperator=': 'Gleich',\n  'headerFilterOperator!=': 'Ungleich',\n  'headerFilterOperator>': 'Größer als',\n  'headerFilterOperator>=': 'Größer als oder gleich',\n  'headerFilterOperator<': 'Kleiner als',\n  'headerFilterOperator<=': 'Kleiner als oder gleich',\n  // Filter values text\n  filterValueAny: 'Beliebig',\n  filterValueTrue: 'Ja',\n  filterValueFalse: 'Nein',\n  // Column menu text\n  columnMenuLabel: 'Menü',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Zeige alle Spalten',\n  columnMenuManageColumns: 'Spalten verwalten',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Verbergen',\n  columnMenuUnsort: 'Sortierung deaktivieren',\n  columnMenuSortAsc: 'Sortiere aufsteigend',\n  columnMenuSortDesc: 'Sortiere absteigend',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} aktive Filter` : `${count} aktiver Filter`,\n  columnHeaderFiltersLabel: 'Zeige Filter',\n  columnHeaderSortIconLabel: 'Sortieren',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} Einträge ausgewählt` : `${count.toLocaleString()} Eintrag ausgewählt`,\n  // Total row amount footer text\n  footerTotalRows: 'Gesamt:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} von ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Checkbox Auswahl',\n  checkboxSelectionSelectAllRows: 'Alle Zeilen auswählen',\n  checkboxSelectionUnselectAllRows: 'Alle Zeilen abwählen',\n  checkboxSelectionSelectRow: 'Zeile auswählen',\n  checkboxSelectionUnselectRow: 'Zeile abwählen',\n  // Boolean cell text\n  booleanCellTrueLabel: 'Ja',\n  booleanCellFalseLabel: 'Nein',\n  // Actions cell more text\n  actionsCellMore: 'Mehr',\n  // Column pinning text\n  pinToLeft: 'Links anheften',\n  pinToRight: 'Rechts anheften',\n  unpin: 'Loslösen',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Gruppe',\n  treeDataExpand: 'Kinder einblenden',\n  treeDataCollapse: 'Kinder ausblenden',\n  // Grouping columns\n  groupingColumnHeaderName: 'Gruppierung',\n  groupColumn: name => `Gruppieren nach ${name}`,\n  unGroupColumn: name => `Gruppierung nach ${name} aufheben`,\n  // Master/detail\n  detailPanelToggle: 'Detailansicht Kippschalter',\n  expandDetailPanel: 'Aufklappen',\n  collapseDetailPanel: 'Zuklappen',\n  // Row reordering text\n  rowReorderingHeaderName: 'Reihen neu ordnen',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregation',\n  aggregationFunctionLabelSum: 'Summe',\n  aggregationFunctionLabelAvg: 'Mittelwert',\n  aggregationFunctionLabelMin: 'Minimum',\n  aggregationFunctionLabelMax: 'Maximum',\n  aggregationFunctionLabelSize: 'Anzahl'\n};\nexport const deDE = getGridLocalization(deDEGrid, deDECore);", "import { elGR as elGRCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst elGRGrid = {\n  // Root\n  noRowsLabel: 'Δεν υπάρχουν καταχωρήσεις',\n  noResultsOverlayLabel: 'Δεν βρέθηκαν αποτελέσματα.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Ύψος σειράς',\n  toolbarDensityLabel: 'Ύψος σειράς',\n  toolbarDensityCompact: 'Συμπαγής',\n  toolbarDensityStandard: 'Προκαθορισμένο',\n  toolbarDensityComfortable: 'Πλατύ',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Στήλες',\n  toolbarColumnsLabel: 'Επιλέξτε στήλες',\n  // Filters toolbar button text\n  toolbarFilters: 'Φίλτρα',\n  toolbarFiltersLabel: 'Εμφάνιση φίλτρων',\n  toolbarFiltersTooltipHide: 'Απόκρυψη φίλτρων',\n  toolbarFiltersTooltipShow: 'Εμφάνιση φίλτρων',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} ενεργά φίλτρα` : `${count} ενεργό φίλτρο`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Αναζήτηση…',\n  toolbarQuickFilterLabel: 'Αναζήτηση',\n  toolbarQuickFilterDeleteIconLabel: 'Καθαρισμός',\n  // Export selector toolbar button text\n  toolbarExport: 'Εξαγωγή',\n  toolbarExportLabel: 'Εξαγωγή',\n  toolbarExportCSV: 'Λήψη ως CSV',\n  toolbarExportPrint: 'Εκτύπωση',\n  toolbarExportExcel: 'Λήψη ως Excel',\n  // Columns management text\n  // columnsManagementSearchTitle: 'Search',\n  // columnsManagementNoColumns: 'No columns',\n  // columnsManagementShowHideAllText: 'Show/Hide All',\n  // columnsManagementReset: 'Reset',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Προσθήκη φίλτρου',\n  filterPanelRemoveAll: 'Αφαίρεση όλων',\n  filterPanelDeleteIconLabel: 'Διαγραφή',\n  filterPanelLogicOperator: 'Λογικός τελεστής',\n  filterPanelOperator: 'Τελεστές',\n  filterPanelOperatorAnd: 'Καί',\n  filterPanelOperatorOr: 'Ή',\n  filterPanelColumns: 'Στήλες',\n  filterPanelInputLabel: 'Τιμή',\n  filterPanelInputPlaceholder: 'Τιμή φίλτρου',\n  // Filter operators text\n  filterOperatorContains: 'περιέχει',\n  // filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'ισούται',\n  // filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'ξεκινάει με',\n  filterOperatorEndsWith: 'τελειώνει με',\n  filterOperatorIs: 'είναι',\n  filterOperatorNot: 'δεν είναι',\n  filterOperatorAfter: 'είναι μετά',\n  filterOperatorOnOrAfter: 'είναι ίσο ή μετά',\n  filterOperatorBefore: 'είναι πριν',\n  filterOperatorOnOrBefore: 'είναι ίσο ή πριν',\n  filterOperatorIsEmpty: 'είναι κενό',\n  filterOperatorIsNotEmpty: 'δεν είναι κενό',\n  filterOperatorIsAnyOf: 'είναι οποιοδήποτε από',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Περιέχει',\n  // headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'Ισούται',\n  // headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Ξεκινάει με',\n  headerFilterOperatorEndsWith: 'Τελειώνει με',\n  headerFilterOperatorIs: 'Είναι',\n  headerFilterOperatorNot: 'Δεν είναι',\n  headerFilterOperatorAfter: 'Είναι μετά',\n  headerFilterOperatorOnOrAfter: 'Είναι ίσο ή μετά',\n  headerFilterOperatorBefore: 'Είναι πριν',\n  headerFilterOperatorOnOrBefore: 'Είναι ίσο ή πριν',\n  headerFilterOperatorIsEmpty: 'Είναι κενό',\n  headerFilterOperatorIsNotEmpty: 'Δεν είναι κενό',\n  headerFilterOperatorIsAnyOf: 'Είναι οποιοδήποτε από',\n  'headerFilterOperator=': 'Ισούται',\n  'headerFilterOperator!=': 'Δεν ισούται',\n  'headerFilterOperator>': 'Μεγαλύτερο από',\n  'headerFilterOperator>=': 'Μεγαλύτερο ή ίσο με',\n  'headerFilterOperator<': 'Μικρότερο από',\n  'headerFilterOperator<=': 'Μικρότερο ή ίσο με',\n  // Filter values text\n  filterValueAny: 'οποιοδήποτε',\n  filterValueTrue: 'αληθές',\n  filterValueFalse: 'ψευδές',\n  // Column menu text\n  columnMenuLabel: 'Μενού',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Εμφάνιση στηλών',\n  columnMenuManageColumns: 'Διαχείριση στηλών',\n  columnMenuFilter: 'Φίλτρο',\n  columnMenuHideColumn: 'Απόκρυψη',\n  columnMenuUnsort: 'Απενεργοποίηση ταξινόμησης',\n  columnMenuSortAsc: 'Ταξινόμηση σε αύξουσα σειρά',\n  columnMenuSortDesc: 'Ταξινόμηση σε φθίνουσα σειρά',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} ενεργά φίλτρα` : `${count} ενεργό φίλτρο`,\n  columnHeaderFiltersLabel: 'Εμφάνιση φίλτρων',\n  columnHeaderSortIconLabel: 'Ταξινόμηση',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} επιλεγμένες γραμμές` : `${count.toLocaleString()} επιλεγμένη γραμμή`,\n  // Total row amount footer text\n  footerTotalRows: 'Σύνολο Γραμμών:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} από ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Επιλογή πλαισίου ελέγχου',\n  checkboxSelectionSelectAllRows: 'Επιλέξτε όλες τις σειρές',\n  checkboxSelectionUnselectAllRows: 'Καταργήση επιλογής όλων των σειρών',\n  checkboxSelectionSelectRow: 'Επιλογή γραμμής',\n  checkboxSelectionUnselectRow: 'Καταργήση επιλογής γραμμής',\n  // Boolean cell text\n  booleanCellTrueLabel: 'ναί',\n  booleanCellFalseLabel: 'όχι',\n  // Actions cell more text\n  actionsCellMore: 'περισσότερα',\n  // Column pinning text\n  pinToLeft: 'Καρφιτσώμα στα αριστερά',\n  pinToRight: 'Καρφιτσώμα στα δεξιά',\n  unpin: 'Ξεκαρφίτσωμα',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Ομαδοποίηση',\n  treeDataExpand: 'εμφάνιση περιεχομένων',\n  treeDataCollapse: 'απόκρυψη περιεχομένων',\n  // Grouping columns\n  groupingColumnHeaderName: 'Ομαδοποίηση',\n  groupColumn: name => `Ομαδοποίηση κατά ${name}`,\n  unGroupColumn: name => `Διακοπή ομαδοποίησης κατά ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Εναλλαγή πίνακα λεπτομερειών',\n  expandDetailPanel: 'Ανάπτυξη',\n  collapseDetailPanel: 'Σύμπτυξη',\n  // Row reordering text\n  rowReorderingHeaderName: 'Αναδιάταξη γραμμών',\n  // Aggregation\n  aggregationMenuItemHeader: 'Συσσωμάτωση',\n  aggregationFunctionLabelSum: 'άθροισμα',\n  aggregationFunctionLabelAvg: 'μέση τιμή',\n  aggregationFunctionLabelMin: 'ελάχιστο',\n  aggregationFunctionLabelMax: 'μέγιστο',\n  aggregationFunctionLabelSize: 'μέγεθος'\n};\nexport const elGR = getGridLocalization(elGRGrid, elGRCore);", "export const GRID_DEFAULT_LOCALE_TEXT = {\n  // Root\n  noRowsLabel: 'No rows',\n  noResultsOverlayLabel: 'No results found.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Density',\n  toolbarDensityLabel: 'Density',\n  toolbarDensityCompact: 'Compact',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Comfortable',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Columns',\n  toolbarColumnsLabel: 'Select columns',\n  // Filters toolbar button text\n  toolbarFilters: 'Filters',\n  toolbarFiltersLabel: 'Show filters',\n  toolbarFiltersTooltipHide: 'Hide filters',\n  toolbarFiltersTooltipShow: 'Show filters',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} active filters` : `${count} active filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Search…',\n  toolbarQuickFilterLabel: 'Search',\n  toolbarQuickFilterDeleteIconLabel: 'Clear',\n  // Export selector toolbar button text\n  toolbarExport: 'Export',\n  toolbarExportLabel: 'Export',\n  toolbarExportCSV: 'Download as CSV',\n  toolbarExportPrint: 'Print',\n  toolbarExportExcel: 'Download as Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Search',\n  columnsManagementNoColumns: 'No columns',\n  columnsManagementShowHideAllText: 'Show/Hide All',\n  columnsManagementReset: 'Reset',\n  columnsManagementDeleteIconLabel: 'Clear',\n  // Filter panel text\n  filterPanelAddFilter: 'Add filter',\n  filterPanelRemoveAll: 'Remove all',\n  filterPanelDeleteIconLabel: 'Delete',\n  filterPanelLogicOperator: 'Logic operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'And',\n  filterPanelOperatorOr: 'Or',\n  filterPanelColumns: 'Columns',\n  filterPanelInputLabel: 'Value',\n  filterPanelInputPlaceholder: 'Filter value',\n  // Filter operators text\n  filterOperatorContains: 'contains',\n  filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'equals',\n  filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'starts with',\n  filterOperatorEndsWith: 'ends with',\n  filterOperatorIs: 'is',\n  filterOperatorNot: 'is not',\n  filterOperatorAfter: 'is after',\n  filterOperatorOnOrAfter: 'is on or after',\n  filterOperatorBefore: 'is before',\n  filterOperatorOnOrBefore: 'is on or before',\n  filterOperatorIsEmpty: 'is empty',\n  filterOperatorIsNotEmpty: 'is not empty',\n  filterOperatorIsAnyOf: 'is any of',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contains',\n  headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'Equals',\n  headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Starts with',\n  headerFilterOperatorEndsWith: 'Ends with',\n  headerFilterOperatorIs: 'Is',\n  headerFilterOperatorNot: 'Is not',\n  headerFilterOperatorAfter: 'Is after',\n  headerFilterOperatorOnOrAfter: 'Is on or after',\n  headerFilterOperatorBefore: 'Is before',\n  headerFilterOperatorOnOrBefore: 'Is on or before',\n  headerFilterOperatorIsEmpty: 'Is empty',\n  headerFilterOperatorIsNotEmpty: 'Is not empty',\n  headerFilterOperatorIsAnyOf: 'Is any of',\n  'headerFilterOperator=': 'Equals',\n  'headerFilterOperator!=': 'Not equals',\n  'headerFilterOperator>': 'Greater than',\n  'headerFilterOperator>=': 'Greater than or equal to',\n  'headerFilterOperator<': 'Less than',\n  'headerFilterOperator<=': 'Less than or equal to',\n  // Filter values text\n  filterValueAny: 'any',\n  filterValueTrue: 'true',\n  filterValueFalse: 'false',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  columnMenuAriaLabel: columnName => `${columnName} column menu`,\n  columnMenuShowColumns: 'Show columns',\n  columnMenuManageColumns: 'Manage columns',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Hide column',\n  columnMenuUnsort: 'Unsort',\n  columnMenuSortAsc: 'Sort by ASC',\n  columnMenuSortDesc: 'Sort by DESC',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} active filters` : `${count} active filter`,\n  columnHeaderFiltersLabel: 'Show filters',\n  columnHeaderSortIconLabel: 'Sort',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} rows selected` : `${count.toLocaleString()} row selected`,\n  // Total row amount footer text\n  footerTotalRows: 'Total Rows:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} of ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Checkbox selection',\n  checkboxSelectionSelectAllRows: 'Select all rows',\n  checkboxSelectionUnselectAllRows: 'Unselect all rows',\n  checkboxSelectionSelectRow: 'Select row',\n  checkboxSelectionUnselectRow: 'Unselect row',\n  // Boolean cell text\n  booleanCellTrueLabel: 'yes',\n  booleanCellFalseLabel: 'no',\n  // Actions cell more text\n  actionsCellMore: 'more',\n  // Column pinning text\n  pinToLeft: 'Pin to left',\n  pinToRight: 'Pin to right',\n  unpin: 'Unpin',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Group',\n  treeDataExpand: 'see children',\n  treeDataCollapse: 'hide children',\n  // Grouping columns\n  groupingColumnHeaderName: 'Group',\n  groupColumn: name => `Group by ${name}`,\n  unGroupColumn: name => `Stop grouping by ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Detail panel toggle',\n  expandDetailPanel: 'Expand',\n  collapseDetailPanel: 'Collapse',\n  // Used core components translation keys\n  MuiTablePagination: {},\n  // Row reordering text\n  rowReorderingHeaderName: 'Row reordering',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregation',\n  aggregationFunctionLabelSum: 'sum',\n  aggregationFunctionLabelAvg: 'avg',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'size'\n};", "import { enUS as enUSCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nimport { GRID_DEFAULT_LOCALE_TEXT } from \"../constants/localeTextConstants.js\";\nexport const enUS = getGridLocalization(GRID_DEFAULT_LOCALE_TEXT, enUSCore);", "import { esES as esESCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst esESGrid = {\n  // Root\n  noRowsLabel: 'Sin filas',\n  noResultsOverlayLabel: 'Resultados no encontrados',\n  // Density selector toolbar button text\n  toolbarDensity: 'Densidad',\n  toolbarDensityLabel: 'Densidad',\n  toolbarDensityCompact: 'Compacta',\n  toolbarDensityStandard: 'Estándar',\n  toolbarDensityComfortable: 'Cómoda',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Columnas',\n  toolbarColumnsLabel: 'Seleccionar columnas',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtros',\n  toolbarFiltersLabel: 'Mostrar filtros',\n  toolbarFiltersTooltipHide: 'Ocultar filtros',\n  toolbarFiltersTooltipShow: 'Mostrar filtros',\n  toolbarFiltersTooltipActive: count => count > 1 ? `${count} filtros activos` : `${count} filtro activo`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Buscar…',\n  toolbarQuickFilterLabel: 'Buscar',\n  toolbarQuickFilterDeleteIconLabel: 'Limpiar',\n  // Export selector toolbar button text\n  toolbarExport: 'Exportar',\n  toolbarExportLabel: 'Exportar',\n  toolbarExportCSV: 'Descargar como CSV',\n  toolbarExportPrint: 'Imprimir',\n  toolbarExportExcel: 'Descargar como Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Buscar',\n  columnsManagementNoColumns: 'Sin columnas',\n  columnsManagementShowHideAllText: 'Mostrar/Ocultar todas',\n  columnsManagementReset: 'Restablecer',\n  columnsManagementDeleteIconLabel: 'Limpiar',\n  // Filter panel text\n  filterPanelAddFilter: 'Agregar filtro',\n  filterPanelRemoveAll: 'Remover todos',\n  filterPanelDeleteIconLabel: 'Borrar',\n  filterPanelLogicOperator: 'Operador lógico',\n  filterPanelOperator: 'Operadores',\n  filterPanelOperatorAnd: 'Y',\n  filterPanelOperatorOr: 'O',\n  filterPanelColumns: 'Columnas',\n  filterPanelInputLabel: 'Valor',\n  filterPanelInputPlaceholder: 'Valor de filtro',\n  // Filter operators text\n  filterOperatorContains: 'contiene',\n  filterOperatorDoesNotContain: 'no contiene',\n  filterOperatorEquals: 'es igual',\n  filterOperatorDoesNotEqual: 'es diferente a',\n  filterOperatorStartsWith: 'comienza con',\n  filterOperatorEndsWith: 'termina con',\n  filterOperatorIs: 'es',\n  filterOperatorNot: 'no es',\n  filterOperatorAfter: 'es posterior',\n  filterOperatorOnOrAfter: 'es en o posterior',\n  filterOperatorBefore: 'es anterior',\n  filterOperatorOnOrBefore: 'es en o anterior',\n  filterOperatorIsEmpty: 'esta vacío',\n  filterOperatorIsNotEmpty: 'no esta vacío',\n  filterOperatorIsAnyOf: 'es cualquiera de',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contiene',\n  headerFilterOperatorDoesNotContain: 'No contiene',\n  headerFilterOperatorEquals: 'Es igual a',\n  headerFilterOperatorDoesNotEqual: 'Es diferente a',\n  headerFilterOperatorStartsWith: 'Comienza con',\n  headerFilterOperatorEndsWith: 'Termina con',\n  headerFilterOperatorIs: 'Es',\n  headerFilterOperatorNot: 'No es',\n  headerFilterOperatorAfter: 'Esta después de',\n  headerFilterOperatorOnOrAfter: 'Esta en o después de',\n  headerFilterOperatorBefore: 'Esta antes de',\n  headerFilterOperatorOnOrBefore: 'Esta en o antes de',\n  headerFilterOperatorIsEmpty: 'Esta vacío',\n  headerFilterOperatorIsNotEmpty: 'No esta vacío',\n  headerFilterOperatorIsAnyOf: 'Es cualquiera de',\n  'headerFilterOperator=': 'Es igual a',\n  'headerFilterOperator!=': 'Es diferente a',\n  'headerFilterOperator>': 'Es mayor que',\n  'headerFilterOperator>=': 'Es mayor o igual que',\n  'headerFilterOperator<': 'Es menor que',\n  'headerFilterOperator<=': 'Es menor o igual que',\n  // Filter values text\n  filterValueAny: 'cualquiera',\n  filterValueTrue: 'verdadero',\n  filterValueFalse: 'falso',\n  // Column menu text\n  columnMenuLabel: 'Menú',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Mostrar columnas',\n  columnMenuManageColumns: 'Administrar columnas',\n  columnMenuFilter: 'Filtro',\n  columnMenuHideColumn: 'Ocultar',\n  columnMenuUnsort: 'Desordenar',\n  columnMenuSortAsc: 'Ordenar ASC',\n  columnMenuSortDesc: 'Ordenar DESC',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count > 1 ? `${count} filtros activos` : `${count} filtro activo`,\n  columnHeaderFiltersLabel: 'Mostrar filtros',\n  columnHeaderSortIconLabel: 'Ordenar',\n  // Rows selected footer text\n  footerRowSelected: count => count > 1 ? `${count.toLocaleString()} filas seleccionadas` : `${count.toLocaleString()} fila seleccionada`,\n  // Total row amount footer text\n  footerTotalRows: 'Filas Totales:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} de ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Seleccionar casilla',\n  checkboxSelectionSelectAllRows: 'Seleccionar todas las filas',\n  checkboxSelectionUnselectAllRows: 'Deseleccionar todas las filas',\n  checkboxSelectionSelectRow: 'Seleccionar fila',\n  checkboxSelectionUnselectRow: 'Deseleccionar fila',\n  // Boolean cell text\n  booleanCellTrueLabel: 'si',\n  booleanCellFalseLabel: 'no',\n  // Actions cell more text\n  actionsCellMore: 'más',\n  // Column pinning text\n  pinToLeft: 'Anclar a la izquierda',\n  pinToRight: 'Anclar a la derecha',\n  unpin: 'Desanclar',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grupo',\n  treeDataExpand: 'mostrar hijos',\n  treeDataCollapse: 'ocultar hijos',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupo',\n  groupColumn: name => `Agrupar por ${name}`,\n  unGroupColumn: name => `No agrupar por ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Alternar detalle',\n  expandDetailPanel: 'Expandir',\n  collapseDetailPanel: 'Contraer',\n  // Row reordering text\n  rowReorderingHeaderName: 'Reordenar filas',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agregación',\n  aggregationFunctionLabelSum: 'suma',\n  aggregationFunctionLabelAvg: 'promedio',\n  aggregationFunctionLabelMin: 'mínimo',\n  aggregationFunctionLabelMax: 'máximo',\n  aggregationFunctionLabelSize: 'tamaño'\n};\nexport const esES = getGridLocalization(esESGrid, esESCore);", "import { faIR as faIRCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst faIRGrid = {\n  // Root\n  noRowsLabel: 'بدون سطر',\n  noResultsOverlayLabel: 'نتیجه‌ای پیدا نشد.',\n  // Density selector toolbar button text\n  toolbarDensity: 'تراکم',\n  toolbarDensityLabel: 'تراکم',\n  toolbarDensityCompact: 'فشرده',\n  toolbarDensityStandard: 'استاندارد',\n  toolbarDensityComfortable: 'راحت',\n  // Columns selector toolbar button text\n  toolbarColumns: 'ستون‌ها',\n  toolbarColumnsLabel: 'ستون‌ها را انتخاب کنید',\n  // Filters toolbar button text\n  toolbarFilters: 'فیلترها',\n  toolbarFiltersLabel: 'نمایش فیلترها',\n  toolbarFiltersTooltipHide: 'مخفی کردن فیلترها',\n  toolbarFiltersTooltipShow: 'نمایش فیلترها',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} فیلترهای فعال` : `${count} فیلتر فعال`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'جستجو...',\n  toolbarQuickFilterLabel: 'جستجو',\n  toolbarQuickFilterDeleteIconLabel: 'حذف',\n  // Export selector toolbar button text\n  toolbarExport: 'خروجی',\n  toolbarExportLabel: 'خروجی',\n  toolbarExportCSV: 'دانلود به صورت CSV',\n  toolbarExportPrint: 'چاپ',\n  toolbarExportExcel: 'دانلود به صورت اکسل',\n  // Columns management text\n  columnsManagementSearchTitle: 'جستجو',\n  columnsManagementNoColumns: 'بدون سطر',\n  columnsManagementShowHideAllText: 'نمایش/مخفی کردن همه',\n  columnsManagementReset: 'بازنشانی',\n  columnsManagementDeleteIconLabel: 'پاک کردن',\n  // Filter panel text\n  filterPanelAddFilter: 'افزودن فیلتر',\n  filterPanelRemoveAll: 'حذف همه',\n  filterPanelDeleteIconLabel: 'حذف',\n  filterPanelLogicOperator: 'عملگر منطقی',\n  filterPanelOperator: 'عملگرها',\n  filterPanelOperatorAnd: 'و',\n  filterPanelOperatorOr: 'یا',\n  filterPanelColumns: 'ستون‌ها',\n  filterPanelInputLabel: 'مقدار',\n  filterPanelInputPlaceholder: 'فیلتر مقدار',\n  // Filter operators text\n  filterOperatorContains: 'شامل',\n  filterOperatorDoesNotContain: 'شامل نمیشود',\n  filterOperatorEquals: 'مساوی',\n  filterOperatorDoesNotEqual: 'برابر نیست',\n  filterOperatorStartsWith: 'شروع با',\n  filterOperatorEndsWith: 'پایان با',\n  filterOperatorIs: 'هست',\n  filterOperatorNot: 'نیست',\n  filterOperatorAfter: 'بعد از',\n  filterOperatorOnOrAfter: 'معادل یا بعدش',\n  filterOperatorBefore: 'قبلش',\n  filterOperatorOnOrBefore: 'معادل یا قبلش',\n  filterOperatorIsEmpty: 'خالی است',\n  filterOperatorIsNotEmpty: 'خالی نیست',\n  filterOperatorIsAnyOf: 'هر یک از',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'شامل',\n  headerFilterOperatorDoesNotContain: 'شامل نمیشود',\n  headerFilterOperatorEquals: 'مساوی',\n  headerFilterOperatorDoesNotEqual: 'برابر نیست',\n  headerFilterOperatorStartsWith: 'شروع با',\n  headerFilterOperatorEndsWith: 'پایان با',\n  headerFilterOperatorIs: 'هست',\n  headerFilterOperatorNot: 'نیست',\n  headerFilterOperatorAfter: 'بعد از',\n  headerFilterOperatorOnOrAfter: 'معادل یا بعد از',\n  headerFilterOperatorBefore: 'قبل از',\n  headerFilterOperatorOnOrBefore: 'معادل یا قبل از',\n  headerFilterOperatorIsEmpty: 'خالی است',\n  headerFilterOperatorIsNotEmpty: 'خالی نیست',\n  headerFilterOperatorIsAnyOf: 'هر یک از',\n  'headerFilterOperator=': 'مساوی',\n  'headerFilterOperator!=': 'نامساوی',\n  'headerFilterOperator>': 'بزرگتر',\n  'headerFilterOperator>=': 'بزرگتر یا مساوی',\n  'headerFilterOperator<': 'کوچکتر',\n  'headerFilterOperator<=': 'کوچکتر یا مساوی',\n  // Filter values text\n  filterValueAny: 'هرچیزی',\n  filterValueTrue: 'صحیح',\n  filterValueFalse: 'غلط',\n  // Column menu text\n  columnMenuLabel: 'فهرست',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'نمایش ستون‌ها',\n  columnMenuManageColumns: 'مدیریت ستون‌ها',\n  columnMenuFilter: 'فیلتر',\n  columnMenuHideColumn: 'مخفی',\n  columnMenuUnsort: 'نامرتب‌کردن',\n  columnMenuSortAsc: 'مرتب‌سازی صعودی',\n  columnMenuSortDesc: 'مرتب‌سازی نزولی',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} فیلتر‌های فعال` : `${count} فیلتر فعال`,\n  columnHeaderFiltersLabel: 'نمایش فیلترها',\n  columnHeaderSortIconLabel: 'مرتب‌سازی',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} سطرهای انتخاب شده` : `${count.toLocaleString()} سطر انتخاب شده`,\n  // Total row amount footer text\n  footerTotalRows: 'مجموع سطرها:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} از ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'چک‌باکس انتخاب',\n  checkboxSelectionSelectAllRows: 'انتخاب همه‌ی ردیف‌ها',\n  checkboxSelectionUnselectAllRows: 'لغو انتخاب همه‌ی ردیف‌ها',\n  checkboxSelectionSelectRow: 'انتخاب ردیف',\n  checkboxSelectionUnselectRow: 'لغو انتخاب ردیف',\n  // Boolean cell text\n  booleanCellTrueLabel: 'صحیح',\n  booleanCellFalseLabel: 'غلط',\n  // Actions cell more text\n  actionsCellMore: 'بیشتر',\n  // Column pinning text\n  pinToLeft: 'سنجاق کردن به چپ',\n  pinToRight: 'سنجاق کردن به راست',\n  unpin: 'برداشتن سنجاق',\n  // Tree Data\n  treeDataGroupingHeaderName: 'گروه‌بندی',\n  treeDataExpand: 'نمایش فرزندان',\n  treeDataCollapse: 'پنهان‌سازی فرزندان',\n  // Grouping columns\n  groupingColumnHeaderName: 'گروه‌بندی',\n  groupColumn: name => `گروه‌بندی براساس ${name}`,\n  unGroupColumn: name => `لغو گروه‌بندی براساس ${name}`,\n  // Master/detail\n  detailPanelToggle: 'پنل جزئیات',\n  expandDetailPanel: 'بازکردن پنل جزئیات',\n  collapseDetailPanel: 'بستن پنل جزئیات',\n  // Row reordering text\n  rowReorderingHeaderName: 'ترتیب مجدد سطر',\n  // Aggregation\n  aggregationMenuItemHeader: 'تجمیع',\n  aggregationFunctionLabelSum: 'جمع',\n  aggregationFunctionLabelAvg: 'میانگین',\n  aggregationFunctionLabelMin: 'حداقل',\n  aggregationFunctionLabelMax: 'حداکثر',\n  aggregationFunctionLabelSize: 'اندازه'\n};\nexport const faIR = getGridLocalization(faIRGrid, faIRCore);", "import { fiFI as fiFICore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst fiFIGrid = {\n  // Root\n  noRowsLabel: 'Ei rivejä',\n  noResultsOverlayLabel: 'Ei tuloksia.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Tiiveys',\n  toolbarDensityLabel: 'Tiiveys',\n  toolbarDensityCompact: '<PERSON><PERSON><PERSON><PERSON>',\n  toolbarDensityStandard: 'Vakio',\n  toolbarDensityComfortable: 'Mukava',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Sarakkeet',\n  toolbarColumnsLabel: 'Valitse sarakkeet',\n  // Filters toolbar button text\n  toolbarFilters: 'Suodattimet',\n  toolbarFiltersLabel: 'Näytä suodattimet',\n  toolbarFiltersTooltipHide: 'Piilota suodattimet',\n  toolbarFiltersTooltipShow: 'Näytä suodattimet',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} aktiivista suodatinta` : `${count} aktiivinen suodatin`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Hae…',\n  toolbarQuickFilterLabel: 'Hae',\n  toolbarQuickFilterDeleteIconLabel: 'Tyhjennä',\n  // Export selector toolbar button text\n  toolbarExport: 'Vie',\n  toolbarExportLabel: 'Vie',\n  toolbarExportCSV: 'Lataa CSV-muodossa',\n  toolbarExportPrint: 'Tulosta',\n  toolbarExportExcel: 'Lataa Excel-muodossa',\n  // Columns management text\n  columnsManagementSearchTitle: 'Hae',\n  columnsManagementNoColumns: 'Ei sarakkeita näytettäväksi',\n  columnsManagementShowHideAllText: 'Näytä/Piilota kaikki',\n  columnsManagementReset: 'Palauta',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Lisää suodatin',\n  filterPanelRemoveAll: 'Poista kaikki',\n  filterPanelDeleteIconLabel: 'Poista',\n  filterPanelLogicOperator: 'Logiikkaoperaattori',\n  filterPanelOperator: 'Operaattorit',\n  filterPanelOperatorAnd: 'Ja',\n  filterPanelOperatorOr: 'Tai',\n  filterPanelColumns: 'Sarakkeet',\n  filterPanelInputLabel: 'Arvo',\n  filterPanelInputPlaceholder: 'Suodattimen arvo',\n  // Filter operators text\n  filterOperatorContains: 'sisältää',\n  // filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'on yhtä suuri kuin',\n  // filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'alkaa',\n  filterOperatorEndsWith: 'päättyy',\n  filterOperatorIs: 'on',\n  filterOperatorNot: 'ei ole',\n  filterOperatorAfter: 'on jälkeen',\n  filterOperatorOnOrAfter: 'on sama tai jälkeen',\n  filterOperatorBefore: 'on ennen',\n  filterOperatorOnOrBefore: 'on sama tai ennen',\n  filterOperatorIsEmpty: 'on tyhjä',\n  filterOperatorIsNotEmpty: 'ei ole tyhjä',\n  filterOperatorIsAnyOf: 'on mikä tahansa seuraavista',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Sisältää',\n  // headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'On yhtä suuri kuin',\n  // headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Alkaa',\n  headerFilterOperatorEndsWith: 'Päättyy',\n  headerFilterOperatorIs: 'On',\n  headerFilterOperatorNot: 'Ei ole',\n  headerFilterOperatorAfter: 'On jälkeen',\n  headerFilterOperatorOnOrAfter: 'On sama tai jälkeen',\n  headerFilterOperatorBefore: 'On ennen',\n  headerFilterOperatorOnOrBefore: 'On sama tai ennen',\n  headerFilterOperatorIsEmpty: 'On tyhjä',\n  headerFilterOperatorIsNotEmpty: 'Ei ole tyhjä',\n  headerFilterOperatorIsAnyOf: 'On mikä tahansa seuraavista',\n  'headerFilterOperator=': 'On yhtä suuri kuin',\n  'headerFilterOperator!=': 'Ei ole yhtä suuri kuin',\n  'headerFilterOperator>': 'Enemmän kuin',\n  'headerFilterOperator>=': 'Enemmän tai yhtä paljon kuin',\n  'headerFilterOperator<': 'Vähemmän kuin',\n  'headerFilterOperator<=': 'Vähemmän tai yhtä paljon kuin',\n  // Filter values text\n  filterValueAny: 'mikä tahansa',\n  filterValueTrue: 'tosi',\n  filterValueFalse: 'epätosi',\n  // Column menu text\n  columnMenuLabel: 'Valikko',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Näytä sarakkeet',\n  columnMenuManageColumns: 'Hallitse sarakkeita',\n  columnMenuFilter: 'Suodata',\n  columnMenuHideColumn: 'Piilota',\n  columnMenuUnsort: 'Poista järjestys',\n  columnMenuSortAsc: 'Järjestä nousevasti',\n  columnMenuSortDesc: 'Järjestä laskevasti',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} aktiivista suodatinta` : `${count} aktiivinen suodatin`,\n  columnHeaderFiltersLabel: 'Näytä suodattimet',\n  columnHeaderSortIconLabel: 'Järjestä',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} riviä valittu` : `${count.toLocaleString()} rivi valittu`,\n  // Total row amount footer text\n  footerTotalRows: 'Rivejä yhteensä:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Valintaruutu',\n  checkboxSelectionSelectAllRows: 'Valitse kaikki rivit',\n  checkboxSelectionUnselectAllRows: 'Poista kaikkien rivien valinta',\n  checkboxSelectionSelectRow: 'Valitse rivi',\n  checkboxSelectionUnselectRow: 'Poista rivin valinta',\n  // Boolean cell text\n  booleanCellTrueLabel: 'tosi',\n  booleanCellFalseLabel: 'epätosi',\n  // Actions cell more text\n  actionsCellMore: 'lisää',\n  // Column pinning text\n  pinToLeft: 'Kiinnitä vasemmalle',\n  pinToRight: 'Kiinnitä oikealle',\n  unpin: 'Irrota kiinnitys',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Ryhmä',\n  treeDataExpand: 'Laajenna',\n  treeDataCollapse: 'Supista',\n  // Grouping columns\n  groupingColumnHeaderName: 'Ryhmä',\n  groupColumn: name => `Ryhmittelyperuste ${name}`,\n  unGroupColumn: name => `Poista ryhmittelyperuste ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Yksityiskohtapaneelin vaihto',\n  expandDetailPanel: 'Laajenna',\n  collapseDetailPanel: 'Tiivistä',\n  // Row reordering text\n  rowReorderingHeaderName: 'Rivien uudelleenjärjestely',\n  // Aggregation\n  aggregationMenuItemHeader: 'Koostaminen',\n  aggregationFunctionLabelSum: 'summa',\n  aggregationFunctionLabelAvg: 'ka.',\n  aggregationFunctionLabelMin: 'min.',\n  aggregationFunctionLabelMax: 'maks.',\n  aggregationFunctionLabelSize: 'koko'\n};\nexport const fiFI = getGridLocalization(fiFIGrid, fiFICore);", "import { frFR as frFRCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst frFRGrid = {\n  // Root\n  noRowsLabel: 'Pas de résultats',\n  noResultsOverlayLabel: 'Aucun résultat.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Densi<PERSON>',\n  toolbarDensityLabel: 'Densité',\n  toolbarDensityCompact: 'Compacte',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Confortable',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Colonnes',\n  toolbarColumnsLabel: 'Choisir les colonnes',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtres',\n  toolbarFiltersLabel: 'Afficher les filtres',\n  toolbarFiltersTooltipHide: 'Masquer les filtres',\n  toolbarFiltersTooltipShow: 'Afficher les filtres',\n  toolbarFiltersTooltipActive: count => count > 1 ? `${count} filtres actifs` : `${count} filtre actif`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Rechercher…',\n  toolbarQuickFilterLabel: 'Recherche',\n  toolbarQuickFilterDeleteIconLabel: 'Supprimer',\n  // Export selector toolbar button text\n  toolbarExport: 'Exporter',\n  toolbarExportLabel: 'Exporter',\n  toolbarExportCSV: 'Télécharger en CSV',\n  toolbarExportPrint: 'Imprimer',\n  toolbarExportExcel: 'Télécharger pour Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Rechercher',\n  columnsManagementNoColumns: 'Pas de colonnes',\n  columnsManagementShowHideAllText: 'Afficher/masquer toutes',\n  columnsManagementReset: 'Réinitialiser',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Ajouter un filtre',\n  filterPanelRemoveAll: 'Tout supprimer',\n  filterPanelDeleteIconLabel: 'Supprimer',\n  filterPanelLogicOperator: 'Opérateur logique',\n  filterPanelOperator: 'Opérateur',\n  filterPanelOperatorAnd: 'Et',\n  filterPanelOperatorOr: 'Ou',\n  filterPanelColumns: 'Colonne',\n  filterPanelInputLabel: 'Valeur',\n  filterPanelInputPlaceholder: 'Filtrer la valeur',\n  // Filter operators text\n  filterOperatorContains: 'contient',\n  filterOperatorDoesNotContain: 'ne contient pas',\n  filterOperatorEquals: 'est égal à',\n  filterOperatorDoesNotEqual: \"n'est pas égal à\",\n  filterOperatorStartsWith: 'commence par',\n  filterOperatorEndsWith: 'se termine par',\n  filterOperatorIs: 'est',\n  filterOperatorNot: \"n'est pas\",\n  filterOperatorAfter: 'postérieur',\n  filterOperatorOnOrAfter: 'égal ou postérieur',\n  filterOperatorBefore: 'antérieur',\n  filterOperatorOnOrBefore: 'égal ou antérieur',\n  filterOperatorIsEmpty: 'est vide',\n  filterOperatorIsNotEmpty: \"n'est pas vide\",\n  filterOperatorIsAnyOf: 'fait partie de',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contient',\n  headerFilterOperatorDoesNotContain: 'Ne contient pas',\n  headerFilterOperatorEquals: 'Est égal à',\n  headerFilterOperatorDoesNotEqual: \"N'est pas égal à\",\n  headerFilterOperatorStartsWith: 'Commence par',\n  headerFilterOperatorEndsWith: 'Se termine par',\n  headerFilterOperatorIs: 'Est',\n  headerFilterOperatorNot: \"N'est pas\",\n  headerFilterOperatorAfter: 'Postérieur',\n  headerFilterOperatorOnOrAfter: 'Égal ou postérieur',\n  headerFilterOperatorBefore: 'Antérieur',\n  headerFilterOperatorOnOrBefore: 'Égal ou antérieur',\n  headerFilterOperatorIsEmpty: 'Est vide',\n  headerFilterOperatorIsNotEmpty: \"N'est pas vide\",\n  headerFilterOperatorIsAnyOf: 'Fait partie de',\n  'headerFilterOperator=': 'Est égal à',\n  'headerFilterOperator!=': \"N'est pas égal à\",\n  'headerFilterOperator>': 'Est supérieur à',\n  'headerFilterOperator>=': 'Est supérieur ou égal à',\n  'headerFilterOperator<': 'Est inférieur à',\n  'headerFilterOperator<=': 'Est inférieur ou égal à',\n  // Filter values text\n  filterValueAny: 'tous',\n  filterValueTrue: 'vrai',\n  filterValueFalse: 'faux',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Afficher les colonnes',\n  columnMenuManageColumns: 'Gérer les colonnes',\n  columnMenuFilter: 'Filtrer',\n  columnMenuHideColumn: 'Masquer',\n  columnMenuUnsort: 'Annuler le tri',\n  columnMenuSortAsc: 'Tri ascendant',\n  columnMenuSortDesc: 'Tri descendant',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count > 1 ? `${count} filtres actifs` : `${count} filtre actif`,\n  columnHeaderFiltersLabel: 'Afficher les filtres',\n  columnHeaderSortIconLabel: 'Trier',\n  // Rows selected footer text\n  footerRowSelected: count => count > 1 ? `${count.toLocaleString()} lignes sélectionnées` : `${count.toLocaleString()} ligne sélectionnée`,\n  // Total row amount footer text\n  footerTotalRows: 'Total de lignes :',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} sur ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Sélection',\n  checkboxSelectionSelectAllRows: 'Sélectionner toutes les lignes',\n  checkboxSelectionUnselectAllRows: 'Désélectionner toutes les lignes',\n  checkboxSelectionSelectRow: 'Sélectionner la ligne',\n  checkboxSelectionUnselectRow: 'Désélectionner la ligne',\n  // Boolean cell text\n  booleanCellTrueLabel: 'vrai',\n  booleanCellFalseLabel: 'faux',\n  // Actions cell more text\n  actionsCellMore: 'Plus',\n  // Column pinning text\n  pinToLeft: 'Épingler à gauche',\n  pinToRight: 'Épingler à droite',\n  unpin: 'Désépingler',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Groupe',\n  treeDataExpand: 'afficher les enfants',\n  treeDataCollapse: 'masquer les enfants',\n  // Grouping columns\n  groupingColumnHeaderName: 'Groupe',\n  groupColumn: name => `Grouper par ${name}`,\n  unGroupColumn: name => `Arrêter de grouper par ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Afficher/masquer les détails',\n  expandDetailPanel: 'Afficher',\n  collapseDetailPanel: 'Masquer',\n  // Row reordering text\n  rowReorderingHeaderName: 'Positionnement des lignes',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agrégation',\n  aggregationFunctionLabelSum: 'Somme',\n  aggregationFunctionLabelAvg: 'Moyenne',\n  aggregationFunctionLabelMin: 'Minimum',\n  aggregationFunctionLabelMax: 'Maximum',\n  aggregationFunctionLabelSize: \"Nombre d'éléments\"\n};\nexport const frFR = getGridLocalization(frFRGrid, frFRCore);", "import { heIL as heILCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst heILGrid = {\n  // Root\n  noRowsLabel: 'אין שורות',\n  noResultsOverlayLabel: 'לא נמצאו תוצאות.',\n  // Density selector toolbar button text\n  toolbarDensity: 'צפיפות',\n  toolbarDensityLabel: 'צפיפות',\n  toolbarDensityCompact: 'דחוסה',\n  toolbarDensityStandard: 'רגילה',\n  toolbarDensityComfortable: 'אוורירית',\n  // Columns selector toolbar button text\n  toolbarColumns: 'עמודות',\n  toolbarColumnsLabel: 'בחר עמודות',\n  // Filters toolbar button text\n  toolbarFilters: 'סינון',\n  toolbarFiltersLabel: 'הצג מסננים',\n  toolbarFiltersTooltipHide: 'הסתר מסננים',\n  toolbarFiltersTooltipShow: 'הצג מסננים',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} מסננים פעילים` : `מסנן אחד פעיל`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'חיפוש…',\n  toolbarQuickFilterLabel: 'חיפוש',\n  toolbarQuickFilterDeleteIconLabel: 'ניקוי',\n  // Export selector toolbar button text\n  toolbarExport: 'ייצוא',\n  toolbarExportLabel: 'ייצוא',\n  toolbarExportCSV: 'ייצוא ל- CSV',\n  toolbarExportPrint: 'הדפסה',\n  toolbarExportExcel: 'ייצוא ל- Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'חיפוש',\n  columnsManagementNoColumns: 'אין עמודות',\n  columnsManagementShowHideAllText: 'הצג/הסתר הכל',\n  columnsManagementReset: 'אתחול',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'הוסף מסנן',\n  filterPanelRemoveAll: 'מחק הכל',\n  filterPanelDeleteIconLabel: 'מחק',\n  filterPanelLogicOperator: 'אופרטור לוגי',\n  filterPanelOperator: 'אופרטור',\n  filterPanelOperatorAnd: 'וגם',\n  filterPanelOperatorOr: 'או',\n  filterPanelColumns: 'עמודות',\n  filterPanelInputLabel: 'ערך',\n  filterPanelInputPlaceholder: 'ערך מסנן',\n  // Filter operators text\n  filterOperatorContains: 'מכיל',\n  filterOperatorDoesNotContain: 'לא מכיל',\n  filterOperatorEquals: 'שווה',\n  filterOperatorDoesNotEqual: 'שונה',\n  filterOperatorStartsWith: 'מתחיל ב-',\n  filterOperatorEndsWith: 'נגמר ב-',\n  filterOperatorIs: 'הינו',\n  filterOperatorNot: 'אינו',\n  filterOperatorAfter: 'אחרי',\n  filterOperatorOnOrAfter: 'ב- או אחרי',\n  filterOperatorBefore: 'לפני',\n  filterOperatorOnOrBefore: 'ב- או לפני',\n  filterOperatorIsEmpty: 'ריק',\n  filterOperatorIsNotEmpty: 'אינו ריק',\n  filterOperatorIsAnyOf: 'הוא אחד מ-',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'מכיל',\n  headerFilterOperatorDoesNotContain: 'לא מכיל',\n  headerFilterOperatorEquals: 'שווה',\n  headerFilterOperatorDoesNotEqual: 'שונה',\n  headerFilterOperatorStartsWith: 'מתחיל ב-',\n  headerFilterOperatorEndsWith: 'נגמר ב-',\n  headerFilterOperatorIs: 'הינו',\n  headerFilterOperatorNot: 'אינו',\n  headerFilterOperatorAfter: 'אחרי',\n  headerFilterOperatorOnOrAfter: 'ב- או אחרי',\n  headerFilterOperatorBefore: 'לפני',\n  headerFilterOperatorOnOrBefore: 'ב- או לפני',\n  headerFilterOperatorIsEmpty: 'ריק',\n  headerFilterOperatorIsNotEmpty: 'אינו ריק',\n  headerFilterOperatorIsAnyOf: 'הוא אחד מ-',\n  'headerFilterOperator=': 'שווה',\n  'headerFilterOperator!=': 'אינו שווה',\n  'headerFilterOperator>': 'גדול מ-',\n  'headerFilterOperator>=': 'גדול שווה ל-',\n  'headerFilterOperator<': 'קטן מ-',\n  'headerFilterOperator<=': 'קטן שווה ל-',\n  // Filter values text\n  filterValueAny: 'כל ערך',\n  filterValueTrue: 'כן',\n  filterValueFalse: 'לא',\n  // Column menu text\n  columnMenuLabel: 'תפריט',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'הצג עמודות',\n  columnMenuManageColumns: 'נהל עמודות',\n  columnMenuFilter: 'סנן',\n  columnMenuHideColumn: 'הסתר',\n  columnMenuUnsort: 'בטל מיון',\n  columnMenuSortAsc: 'מיין בסדר עולה',\n  columnMenuSortDesc: 'מיין בסדר יורד',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} מסננים פעילים` : `מסנן אחד פעיל`,\n  columnHeaderFiltersLabel: 'הצג מסננים',\n  columnHeaderSortIconLabel: 'מיין',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} שורות נבחרו` : `שורה אחת נבחרה`,\n  // Total row amount footer text\n  footerTotalRows: 'סך הכל:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} מתוך ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'בחירה',\n  checkboxSelectionSelectAllRows: 'בחר הכל',\n  checkboxSelectionUnselectAllRows: 'בטל הכל',\n  checkboxSelectionSelectRow: 'בחר שורה',\n  checkboxSelectionUnselectRow: 'בטל בחירת שורה',\n  // Boolean cell text\n  booleanCellTrueLabel: 'כן',\n  booleanCellFalseLabel: 'לא',\n  // Actions cell more text\n  actionsCellMore: 'עוד',\n  // Column pinning text\n  pinToLeft: 'נעץ משמאל',\n  pinToRight: 'נעץ מימין',\n  unpin: 'הסר נעיצה',\n  // Tree Data\n  treeDataGroupingHeaderName: 'קבוצה',\n  treeDataExpand: 'הרחב',\n  treeDataCollapse: 'כווץ',\n  // Grouping columns\n  groupingColumnHeaderName: 'קבוצה',\n  groupColumn: name => `קבץ לפי ${name}`,\n  unGroupColumn: name => `הפסק לקבץ לפי ${name}`,\n  // Master/detail\n  detailPanelToggle: 'הצג/הסתר פרטים',\n  expandDetailPanel: 'הרחב',\n  collapseDetailPanel: 'כווץ',\n  // Row reordering text\n  rowReorderingHeaderName: 'סידור שורות',\n  // Aggregation\n  aggregationMenuItemHeader: 'צבירה',\n  aggregationFunctionLabelSum: 'סכום',\n  aggregationFunctionLabelAvg: 'ממוצע',\n  aggregationFunctionLabelMin: 'מינימום',\n  aggregationFunctionLabelMax: 'מקסימום',\n  aggregationFunctionLabelSize: 'גודל'\n};\nexport const heIL = getGridLocalization(heILGrid, heILCore);", "import { huHU as huHUCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst huHUGrid = {\n  // Root\n  noRowsLabel: 'Nincsenek sorok',\n  noResultsOverlayLabel: 'Ninc<PERSON> találat.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Sormagasság',\n  toolbarDensityLabel: 'Sormagasság',\n  toolbarDensityCompact: 'Kompakt',\n  toolbarDensityStandard: 'Normál',\n  toolbarDensityComfortable: 'Kényelmes',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Oszlopok',\n  toolbarColumnsLabel: 'Oszlopok kiválasztása',\n  // Filters toolbar button text\n  toolbarFilters: 'Sz<PERSON>rők',\n  toolbarFiltersLabel: 'Szűrők megjelenítése',\n  toolbarFiltersTooltipHide: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elre<PERSON>',\n  toolbarFiltersTooltipShow: 'Szűrők megjeleníté<PERSON>',\n  toolbarFiltersTooltipActive: count => `${count} aktív szűrő`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Keresés…',\n  toolbarQuickFilterLabel: 'Keresés',\n  toolbarQuickFilterDeleteIconLabel: 'Törlés',\n  // Export selector toolbar button text\n  toolbarExport: 'Exportálás',\n  toolbarExportLabel: 'Exportálás',\n  toolbarExportCSV: 'Mentés CSV fájlként',\n  toolbarExportPrint: 'Nyomtatás',\n  toolbarExportExcel: 'Mentés Excel fájlként',\n  // Columns management text\n  columnsManagementSearchTitle: 'Keresés',\n  columnsManagementNoColumns: 'Nincsenek oszlopok',\n  columnsManagementShowHideAllText: 'Összes',\n  columnsManagementReset: 'Visszavon',\n  columnsManagementDeleteIconLabel: 'Törlés',\n  // Filter panel text\n  filterPanelAddFilter: 'Szűrő hozzáadása',\n  filterPanelRemoveAll: 'Összes törlése',\n  filterPanelDeleteIconLabel: 'Törlés',\n  filterPanelLogicOperator: 'Logikai operátor',\n  filterPanelOperator: 'Operátorok',\n  filterPanelOperatorAnd: 'És',\n  filterPanelOperatorOr: 'Vagy',\n  filterPanelColumns: 'Oszlopok',\n  filterPanelInputLabel: 'Érték',\n  filterPanelInputPlaceholder: 'Érték szűrése',\n  // Filter operators text\n  filterOperatorContains: 'tartalmazza:',\n  filterOperatorDoesNotContain: 'nem tartalmazza',\n  filterOperatorEquals: 'egyenlő ezzel:',\n  filterOperatorDoesNotEqual: 'nem egyenlő',\n  filterOperatorStartsWith: 'ezzel kezdődik:',\n  filterOperatorEndsWith: 'ezzel végződik:',\n  filterOperatorIs: 'a következő:',\n  filterOperatorNot: 'nem a következő:',\n  filterOperatorAfter: 'ezutáni:',\n  filterOperatorOnOrAfter: 'ekkori vagy ezutáni:',\n  filterOperatorBefore: 'ezelőtti:',\n  filterOperatorOnOrBefore: 'ekkori vagy ezelőtti:',\n  filterOperatorIsEmpty: 'üres',\n  filterOperatorIsNotEmpty: 'nem üres',\n  filterOperatorIsAnyOf: 'a következők egyike:',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Tartalmazza:',\n  headerFilterOperatorDoesNotContain: 'Nem tartalmazza',\n  headerFilterOperatorEquals: 'Egyenlő ezzel:',\n  headerFilterOperatorDoesNotEqual: 'Nem egyenlő',\n  headerFilterOperatorStartsWith: 'Ezzel kezdődik:',\n  headerFilterOperatorEndsWith: 'Ezzel végződik:',\n  headerFilterOperatorIs: 'Megegyezik',\n  headerFilterOperatorNot: 'Nem egyezik meg',\n  headerFilterOperatorAfter: 'Ezutáni:',\n  headerFilterOperatorOnOrAfter: 'Ekkori vagy ezutáni:',\n  headerFilterOperatorBefore: 'Ezelőtti:',\n  headerFilterOperatorOnOrBefore: 'Ekkori vagy ezelőtti:',\n  headerFilterOperatorIsEmpty: 'Üres',\n  headerFilterOperatorIsNotEmpty: 'Nem üres',\n  headerFilterOperatorIsAnyOf: 'A következők egyike:',\n  'headerFilterOperator=': 'Egyenlő',\n  'headerFilterOperator!=': 'Nem egyenlő',\n  'headerFilterOperator>': 'Nagyobb mint',\n  'headerFilterOperator>=': 'Nagyobb vagy egyenlő',\n  'headerFilterOperator<': 'Kisebb mint',\n  'headerFilterOperator<=': 'Kisebb vagy egyenlő',\n  // Filter values text\n  filterValueAny: 'bármilyen',\n  filterValueTrue: 'igaz',\n  filterValueFalse: 'hamis',\n  // Column menu text\n  columnMenuLabel: 'Menü',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Oszlopok megjelenítése',\n  columnMenuManageColumns: 'Oszlopok kezelése',\n  columnMenuFilter: 'Szűrők',\n  columnMenuHideColumn: 'Elrejtés',\n  columnMenuUnsort: 'Sorrend visszaállítása',\n  columnMenuSortAsc: 'Növekvő sorrendbe',\n  columnMenuSortDesc: 'Csökkenő sorrendbe',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `${count} aktív szűrő`,\n  columnHeaderFiltersLabel: 'Szűrők megjelenítése',\n  columnHeaderSortIconLabel: 'Átrendezés',\n  // Rows selected footer text\n  footerRowSelected: count => `${count.toLocaleString()} sor kiválasztva`,\n  // Total row amount footer text\n  footerTotalRows: 'Összesen:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} (összesen: ${totalCount.toLocaleString()})`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Jelölőnégyzetes kijelölés',\n  checkboxSelectionSelectAllRows: 'Minden sor kijelölése',\n  checkboxSelectionUnselectAllRows: 'Minden sor kijelölésének törlése',\n  checkboxSelectionSelectRow: 'Sor kijelölése',\n  checkboxSelectionUnselectRow: 'Sor kijelölésének törlése',\n  // Boolean cell text\n  booleanCellTrueLabel: 'igen',\n  booleanCellFalseLabel: 'nem',\n  // Actions cell more text\n  actionsCellMore: 'további',\n  // Column pinning text\n  pinToLeft: 'Rögzítés balra',\n  pinToRight: 'Rögzítés jobbra',\n  unpin: 'Rögzítés törlése',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Csoport',\n  treeDataExpand: 'gyermekek megjelenítése',\n  treeDataCollapse: 'gyermekek elrejtése',\n  // Grouping columns\n  groupingColumnHeaderName: 'Csoportosítás',\n  groupColumn: name => `Csoportosítás ${name} szerint`,\n  unGroupColumn: name => `${name} szerinti csoportosítás törlése`,\n  // Master/detail\n  detailPanelToggle: 'Részletek panel váltása',\n  expandDetailPanel: 'Kibontás',\n  collapseDetailPanel: 'Összecsukás',\n  // Row reordering text\n  rowReorderingHeaderName: 'Sorok újrarendezése',\n  // Aggregation\n  aggregationMenuItemHeader: 'Összesítés',\n  aggregationFunctionLabelSum: 'Összeg',\n  aggregationFunctionLabelAvg: 'Átlag',\n  aggregationFunctionLabelMin: 'Minimum',\n  aggregationFunctionLabelMax: 'Maximum',\n  aggregationFunctionLabelSize: 'Darabszám'\n};\nexport const huHU = getGridLocalization(huHUGrid, huHUCore);", "import { hyAM as hyAMCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst hyAMGrid = {\n  // Root\n  noRowsLabel: 'Տվյալներ չկան',\n  noResultsOverlayLabel: 'Արդյունքներ չեն գտնվել։',\n  // Density selector toolbar button text\n  toolbarDensity: 'Խտություն',\n  toolbarDensityLabel: 'Խտություն',\n  toolbarDensityCompact: 'Կոմպակտ',\n  toolbarDensityStandard: 'Ստանդարտ',\n  toolbarDensityComfortable: 'Հարմարավետ',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Սյունակներ',\n  toolbarColumnsLabel: 'Ընտրել սյունակներ',\n  // Filters toolbar button text\n  toolbarFilters: 'Զտիչներ',\n  toolbarFiltersLabel: 'Ցուցադրել զտիչները',\n  toolbarFiltersTooltipHide: 'Թաքցնել զտիչները',\n  toolbarFiltersTooltipShow: 'Ցուցադրել զտիչները',\n  toolbarFiltersTooltipActive: count => {\n    let pluralForm = 'ակտիվ զտիչ';\n    if (count === 1) {\n      pluralForm = 'ակտիվ զտիչ';\n    } else {\n      pluralForm = 'ակտիվ զտիչներ';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Որոնել…',\n  toolbarQuickFilterLabel: 'Որոնել',\n  toolbarQuickFilterDeleteIconLabel: 'Մաքրել',\n  // Export selector toolbar button text\n  toolbarExport: 'Արտահանում',\n  toolbarExportLabel: 'Արտահանում',\n  toolbarExportCSV: 'Ներբեռնել CSV-ով',\n  toolbarExportPrint: 'Տպել',\n  toolbarExportExcel: 'Ներբեռնել Excel-ով',\n  // Columns management text\n  columnsManagementSearchTitle: 'Որոնել',\n  columnsManagementNoColumns: 'Սյունակներ չկան',\n  columnsManagementShowHideAllText: 'Ցուցադրել/Թաքցնել բոլորը',\n  columnsManagementReset: 'Վերակայել',\n  columnsManagementDeleteIconLabel: 'Հեռացնել',\n  // Filter panel text\n  filterPanelAddFilter: 'Ավելացնել զտիչ',\n  filterPanelRemoveAll: 'Հեռացնել բոլորը',\n  filterPanelDeleteIconLabel: 'Հեռացնել',\n  filterPanelLogicOperator: 'Տրամաբանական օպերատոր',\n  filterPanelOperator: 'Օպերատոր',\n  filterPanelOperatorAnd: 'Եվ',\n  filterPanelOperatorOr: 'Կամ',\n  filterPanelColumns: 'Սյունակներ',\n  filterPanelInputLabel: 'Արժեք',\n  filterPanelInputPlaceholder: 'Զտիչի արժեք',\n  // Filter operators text\n  filterOperatorContains: 'պարունակում է',\n  filterOperatorDoesNotContain: 'չի պարունակում',\n  filterOperatorEquals: 'հավասար է',\n  filterOperatorDoesNotEqual: 'հավասար չէ',\n  filterOperatorStartsWith: 'սկսվում է',\n  filterOperatorEndsWith: 'վերջանում է',\n  filterOperatorIs: 'է',\n  filterOperatorNot: 'չէ',\n  filterOperatorAfter: 'հետո է',\n  filterOperatorOnOrAfter: 'այդ օրը կամ հետո է',\n  filterOperatorBefore: 'մինչ է',\n  filterOperatorOnOrBefore: 'այդ օրը կամ առաջ է',\n  filterOperatorIsEmpty: 'դատարկ է',\n  filterOperatorIsNotEmpty: 'դատարկ չէ',\n  filterOperatorIsAnyOf: 'որևէ մեկը',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Պարունակում է',\n  headerFilterOperatorDoesNotContain: 'Չի պարունակում',\n  headerFilterOperatorEquals: 'Հավասար է',\n  headerFilterOperatorDoesNotEqual: 'Հավասար չէ',\n  headerFilterOperatorStartsWith: 'Սկսվում է',\n  headerFilterOperatorEndsWith: 'Վերջանում է',\n  headerFilterOperatorIs: 'Է',\n  headerFilterOperatorNot: 'Չէ',\n  headerFilterOperatorAfter: 'Հետո է',\n  headerFilterOperatorOnOrAfter: 'Այդ օրը կամ հետո է',\n  headerFilterOperatorBefore: 'Մինչ է',\n  headerFilterOperatorOnOrBefore: 'Այդ օրը կամ առաջ է',\n  headerFilterOperatorIsEmpty: 'Դատարկ է',\n  headerFilterOperatorIsNotEmpty: 'Դատարկ չէ',\n  headerFilterOperatorIsAnyOf: 'Որևէ մեկը',\n  'headerFilterOperator=': 'Հավասար է',\n  'headerFilterOperator!=': 'Հավասար չէ',\n  'headerFilterOperator>': 'Ավելի մեծ է',\n  'headerFilterOperator>=': 'Ավելի մեծ կամ հավասար է',\n  'headerFilterOperator<': 'Ավելի փոքր է',\n  'headerFilterOperator<=': 'Ավելի փոքր կամ հավասար է',\n  // Filter values text\n  filterValueAny: 'ցանկացած',\n  filterValueTrue: 'այո',\n  filterValueFalse: 'ոչ',\n  // Column menu text\n  columnMenuLabel: 'Մենյու',\n  columnMenuAriaLabel: columnName => `${columnName} սյունակի մենյու`,\n  columnMenuShowColumns: 'Ցուցադրել սյունակները',\n  columnMenuManageColumns: 'Կառավարել սյունակները',\n  columnMenuFilter: 'Զտիչ',\n  columnMenuHideColumn: 'Թաքցնել',\n  columnMenuUnsort: 'Մաքրել դասավորումը',\n  columnMenuSortAsc: 'Աճման կարգով դասավորել',\n  columnMenuSortDesc: 'Նվազման կարգով դասավորել',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => {\n    let pluralForm = 'ակտիվ զտիչներ';\n    if (count === 1) {\n      pluralForm = 'ակտիվ զտիչ';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  columnHeaderFiltersLabel: 'Ցուցադրել զտիչները',\n  columnHeaderSortIconLabel: 'Դասավորել',\n  // Rows selected footer text\n  footerRowSelected: count => {\n    let pluralForm = 'ընտրված տող';\n    if (count === 1) {\n      pluralForm = 'ընտրված տող';\n    } else {\n      pluralForm = 'ընտրված տողեր';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  // Total row amount footer text\n  footerTotalRows: 'Ընդամենը տողեր:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => {\n    return `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`;\n  },\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Տողի ընտրություն',\n  checkboxSelectionSelectAllRows: 'Ընտրել բոլոր տողերը',\n  checkboxSelectionUnselectAllRows: 'Չընտրել բոլոր տողերը',\n  checkboxSelectionSelectRow: 'Ընտրել տողը',\n  checkboxSelectionUnselectRow: 'Չընտրել տողը',\n  // Boolean cell text\n  booleanCellTrueLabel: 'այո',\n  booleanCellFalseLabel: 'ոչ',\n  // Actions cell more text\n  actionsCellMore: 'ավելին',\n  // Column pinning text\n  pinToLeft: 'Կցել ձախ',\n  pinToRight: 'Կցել աջ',\n  unpin: 'Անջատել',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Խումբ',\n  treeDataExpand: 'Բացել ենթատողերը',\n  treeDataCollapse: 'Փակել ենթատողերը',\n  // Grouping columns\n  groupingColumnHeaderName: 'Խմբավորում',\n  groupColumn: name => `Խմբավորել ըստ ${name}`,\n  unGroupColumn: name => `Չխմբավորել ըստ ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Փոխարկել մանրամասն տեսքը',\n  expandDetailPanel: 'Բացել',\n  collapseDetailPanel: 'Փակել',\n  // Row reordering text\n  rowReorderingHeaderName: 'Տողերի վերադասավորում',\n  // Aggregation\n  aggregationMenuItemHeader: 'Ագրեգացում',\n  aggregationFunctionLabelSum: 'գումար',\n  aggregationFunctionLabelAvg: 'միջին',\n  aggregationFunctionLabelMin: 'մինիմում',\n  aggregationFunctionLabelMax: 'մաքսիմում',\n  aggregationFunctionLabelSize: 'քանակ'\n};\nexport const hyAM = getGridLocalization(hyAMGrid, hyAMCore);", "import { itIT as itITCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst itITGrid = {\n  // Root\n  noRowsLabel: 'Nessun record',\n  noResultsOverlayLabel: 'Nessun record trovato.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Densità',\n  toolbarDensityLabel: 'Densità',\n  toolbarDensityCompact: 'Compatta',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Comoda',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Colonne',\n  toolbarColumnsLabel: 'Seleziona le colonne',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtri',\n  toolbarFiltersLabel: 'Mostra i filtri',\n  toolbarFiltersTooltipHide: 'Nascondi i filtri',\n  toolbarFiltersTooltipShow: 'Mostra i filtri',\n  toolbarFiltersTooltipActive: count => count > 1 ? `${count} filtri attivi` : `${count} filtro attivo`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Cerca…',\n  toolbarQuickFilterLabel: 'Cerca',\n  toolbarQuickFilterDeleteIconLabel: 'Resetta',\n  // Export selector toolbar button text\n  toolbarExport: 'Esporta',\n  toolbarExportLabel: 'Esporta',\n  toolbarExportCSV: 'Esporta in CSV',\n  toolbarExportPrint: 'Stampa',\n  toolbarExportExcel: 'Scarica come Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Cerca',\n  columnsManagementNoColumns: 'Nessuna colonna',\n  columnsManagementShowHideAllText: 'Mostra/Nascondi Tutto',\n  columnsManagementReset: 'Resetta',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Aggiungi un filtro',\n  filterPanelRemoveAll: 'Rimuovi filtri',\n  filterPanelDeleteIconLabel: 'Rimuovi',\n  filterPanelLogicOperator: 'Operatore logico',\n  filterPanelOperator: 'Operatori',\n  filterPanelOperatorAnd: 'E (and)',\n  filterPanelOperatorOr: 'O (or)',\n  filterPanelColumns: 'Colonne',\n  filterPanelInputLabel: 'Valore',\n  filterPanelInputPlaceholder: 'Filtra il valore',\n  // Filter operators text\n  filterOperatorContains: 'contiene',\n  filterOperatorDoesNotContain: 'non contiene',\n  filterOperatorEquals: 'uguale a',\n  filterOperatorDoesNotEqual: 'diverso da',\n  filterOperatorStartsWith: 'comincia per',\n  filterOperatorEndsWith: 'termina per',\n  filterOperatorIs: 'uguale a',\n  filterOperatorNot: 'diverso da',\n  filterOperatorAfter: 'dopo il',\n  filterOperatorOnOrAfter: 'a partire dal',\n  filterOperatorBefore: 'prima del',\n  filterOperatorOnOrBefore: 'fino al',\n  filterOperatorIsEmpty: 'è vuoto',\n  filterOperatorIsNotEmpty: 'non è vuoto',\n  filterOperatorIsAnyOf: 'è uno tra',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contiene',\n  headerFilterOperatorDoesNotContain: 'Non contiene',\n  headerFilterOperatorEquals: 'Uguale a',\n  headerFilterOperatorDoesNotEqual: 'Diverso da',\n  headerFilterOperatorStartsWith: 'Comincia per',\n  headerFilterOperatorEndsWith: 'Termina per',\n  headerFilterOperatorIs: 'Uguale a',\n  headerFilterOperatorNot: 'Diverso da',\n  headerFilterOperatorAfter: 'Dopo il',\n  headerFilterOperatorOnOrAfter: 'A partire dal',\n  headerFilterOperatorBefore: 'Prima del',\n  headerFilterOperatorOnOrBefore: 'Fino al',\n  headerFilterOperatorIsEmpty: 'È vuoto',\n  headerFilterOperatorIsNotEmpty: 'Non è vuoto',\n  headerFilterOperatorIsAnyOf: 'È uno tra',\n  'headerFilterOperator=': 'Uguale a',\n  'headerFilterOperator!=': 'Diverso da',\n  'headerFilterOperator>': 'Maggiore di',\n  'headerFilterOperator>=': 'Maggiore o uguale a',\n  'headerFilterOperator<': 'Minore di',\n  'headerFilterOperator<=': 'Minore o uguale a',\n  // Filter values text\n  filterValueAny: 'qualunque',\n  filterValueTrue: 'vero',\n  filterValueFalse: 'falso',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Mostra le colonne',\n  columnMenuManageColumns: 'Gestisci colonne',\n  columnMenuFilter: 'Filtra',\n  columnMenuHideColumn: 'Nascondi',\n  columnMenuUnsort: \"Annulla l'ordinamento\",\n  columnMenuSortAsc: 'Ordinamento crescente',\n  columnMenuSortDesc: 'Ordinamento decrescente',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count > 1 ? `${count} filtri attivi` : `${count} filtro attivo`,\n  columnHeaderFiltersLabel: 'Mostra i filtri',\n  columnHeaderSortIconLabel: 'Ordina',\n  // Rows selected footer text\n  footerRowSelected: count => count > 1 ? `${count.toLocaleString()} record selezionati` : `${count.toLocaleString()} record selezionato`,\n  // Total row amount footer text\n  footerTotalRows: 'Record totali:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} di ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Seleziona',\n  checkboxSelectionSelectAllRows: 'Seleziona tutte le righe',\n  checkboxSelectionUnselectAllRows: 'Deseleziona tutte le righe',\n  checkboxSelectionSelectRow: 'Seleziona riga',\n  checkboxSelectionUnselectRow: 'Deseleziona riga',\n  // Boolean cell text\n  booleanCellTrueLabel: 'vero',\n  booleanCellFalseLabel: 'falso',\n  // Actions cell more text\n  actionsCellMore: 'più',\n  // Column pinning text\n  pinToLeft: 'Blocca a sinistra',\n  pinToRight: 'Blocca a destra',\n  unpin: 'Sblocca',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Gruppo',\n  treeDataExpand: 'mostra figli',\n  treeDataCollapse: 'nascondi figli',\n  // Grouping columns\n  groupingColumnHeaderName: 'Gruppo',\n  groupColumn: name => `Raggruppa per ${name}`,\n  unGroupColumn: name => `Annulla raggruppamento per ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Abilita pannello dettagli',\n  expandDetailPanel: 'Espandi',\n  collapseDetailPanel: 'Comprimi',\n  // Row reordering text\n  rowReorderingHeaderName: 'Riordinamento righe',\n  // Aggregation\n  aggregationMenuItemHeader: 'aggregazione',\n  aggregationFunctionLabelSum: 'somma',\n  aggregationFunctionLabelAvg: 'media',\n  aggregationFunctionLabelMin: 'minimo',\n  aggregationFunctionLabelMax: 'massimo',\n  aggregationFunctionLabelSize: 'numero di elementi'\n};\nexport const itIT = getGridLocalization(itITGrid, itITCore);", "import { jaJP as jaJPCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst jaJPGrid = {\n  // Root\n  noRowsLabel: '行がありません。',\n  noResultsOverlayLabel: '結果がありません。',\n  // Density selector toolbar button text\n  toolbarDensity: '行間隔',\n  toolbarDensityLabel: '行間隔',\n  toolbarDensityCompact: 'コンパクト',\n  toolbarDensityStandard: '標準',\n  toolbarDensityComfortable: '広め',\n  // Columns selector toolbar button text\n  toolbarColumns: '列一覧',\n  toolbarColumnsLabel: '列選択',\n  // Filters toolbar button text\n  toolbarFilters: 'フィルター',\n  toolbarFiltersLabel: 'フィルター表示',\n  toolbarFiltersTooltipHide: 'フィルター非表示',\n  toolbarFiltersTooltipShow: 'フィルター表示',\n  toolbarFiltersTooltipActive: count => `${count}件のフィルターを適用中`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: '検索…',\n  toolbarQuickFilterLabel: '検索',\n  toolbarQuickFilterDeleteIconLabel: 'クリア',\n  // Export selector toolbar button text\n  toolbarExport: 'エクスポート',\n  toolbarExportLabel: 'エクスポート',\n  toolbarExportCSV: 'CSVダウンロード',\n  toolbarExportPrint: '印刷',\n  toolbarExportExcel: 'Excelダウンロード',\n  // Columns management text\n  columnsManagementSearchTitle: '検索',\n  columnsManagementNoColumns: 'カラムなし',\n  columnsManagementShowHideAllText: 'すべて表示/非表示',\n  columnsManagementReset: 'リセット',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'フィルター追加',\n  filterPanelRemoveAll: 'すべて削除',\n  filterPanelDeleteIconLabel: '削除',\n  filterPanelLogicOperator: '論理演算子',\n  filterPanelOperator: '演算子',\n  filterPanelOperatorAnd: 'And',\n  filterPanelOperatorOr: 'Or',\n  filterPanelColumns: '列',\n  filterPanelInputLabel: '値',\n  filterPanelInputPlaceholder: '値を入力…',\n  // Filter operators text\n  filterOperatorContains: '...を含む',\n  filterOperatorDoesNotContain: '...を含まない',\n  filterOperatorEquals: '...に等しい',\n  filterOperatorDoesNotEqual: '...に等しくない',\n  filterOperatorStartsWith: '...で始まる',\n  filterOperatorEndsWith: '...で終わる',\n  filterOperatorIs: '...である',\n  filterOperatorNot: '...でない',\n  filterOperatorAfter: '...より後ろ',\n  filterOperatorOnOrAfter: '...以降',\n  filterOperatorBefore: '...より前',\n  filterOperatorOnOrBefore: '...以前',\n  filterOperatorIsEmpty: '...空である',\n  filterOperatorIsNotEmpty: '...空でない',\n  filterOperatorIsAnyOf: '...のいずれか',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: '含む',\n  headerFilterOperatorDoesNotContain: '含まない',\n  headerFilterOperatorEquals: '等しい',\n  headerFilterOperatorDoesNotEqual: '等しくない',\n  headerFilterOperatorStartsWith: 'で始まる',\n  headerFilterOperatorEndsWith: 'で終わる',\n  headerFilterOperatorIs: 'である',\n  headerFilterOperatorNot: 'ではない',\n  headerFilterOperatorAfter: '...より後ろ',\n  headerFilterOperatorOnOrAfter: '...以降',\n  headerFilterOperatorBefore: '...より前',\n  headerFilterOperatorOnOrBefore: '...以前',\n  headerFilterOperatorIsEmpty: '空白',\n  headerFilterOperatorIsNotEmpty: '空白ではない',\n  headerFilterOperatorIsAnyOf: 'いずれか',\n  'headerFilterOperator=': '等しい',\n  'headerFilterOperator!=': '等しくない',\n  'headerFilterOperator>': 'より大きい',\n  'headerFilterOperator>=': '以上',\n  'headerFilterOperator<': '未満',\n  'headerFilterOperator<=': '以下',\n  // Filter values text\n  filterValueAny: 'いずれか',\n  filterValueTrue: '真',\n  filterValueFalse: '偽',\n  // Column menu text\n  columnMenuLabel: 'メニュー',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: '列表示',\n  columnMenuManageColumns: '列管理',\n  columnMenuFilter: 'フィルター',\n  columnMenuHideColumn: '列非表示',\n  columnMenuUnsort: 'ソート解除',\n  columnMenuSortAsc: '昇順ソート',\n  columnMenuSortDesc: '降順ソート',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `${count}件のフィルターを適用中`,\n  columnHeaderFiltersLabel: 'フィルター表示',\n  columnHeaderSortIconLabel: 'ソート',\n  // Rows selected footer text\n  footerRowSelected: count => `${count}行を選択中`,\n  // Total row amount footer text\n  footerTotalRows: '総行数:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'チェックボックス',\n  checkboxSelectionSelectAllRows: 'すべての行を選択',\n  checkboxSelectionUnselectAllRows: 'すべての行選択を解除',\n  checkboxSelectionSelectRow: '行を選択',\n  checkboxSelectionUnselectRow: '行選択を解除',\n  // Boolean cell text\n  booleanCellTrueLabel: '真',\n  booleanCellFalseLabel: '偽',\n  // Actions cell more text\n  actionsCellMore: 'もっと見る',\n  // Column pinning text\n  pinToLeft: '左側に固定',\n  pinToRight: '右側に固定',\n  unpin: '固定解除',\n  // Tree Data\n  treeDataGroupingHeaderName: 'グループ',\n  treeDataExpand: '展開',\n  treeDataCollapse: '折りたたみ',\n  // Grouping columns\n  groupingColumnHeaderName: 'グループ',\n  groupColumn: name => `${name}でグループ化`,\n  unGroupColumn: name => `${name}のグループを解除`,\n  // Master/detail\n  detailPanelToggle: '詳細パネルの切り替え',\n  expandDetailPanel: '展開',\n  collapseDetailPanel: '折りたたみ',\n  // Row reordering text\n  rowReorderingHeaderName: '行並び替え',\n  // Aggregation\n  aggregationMenuItemHeader: '合計',\n  aggregationFunctionLabelSum: '和',\n  aggregationFunctionLabelAvg: '平均',\n  aggregationFunctionLabelMin: '最小値',\n  aggregationFunctionLabelMax: '最大値',\n  aggregationFunctionLabelSize: 'サイズ'\n};\nexport const jaJP = getGridLocalization(jaJPGrid, jaJPCore);", "import { koKR as koKRCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst koKRGrid = {\n  // Root\n  noRowsLabel: '행이 없습니다.',\n  noResultsOverlayLabel: '결과값이 없습니다.',\n  // Density selector toolbar button text\n  toolbarDensity: '행 간격',\n  toolbarDensityLabel: '행 간격',\n  toolbarDensityCompact: '좁게',\n  toolbarDensityStandard: '기본',\n  toolbarDensityComfortable: '넓게',\n  // Columns selector toolbar button text\n  toolbarColumns: '열 목록',\n  toolbarColumnsLabel: '열 선택',\n  // Filters toolbar button text\n  toolbarFilters: '필터',\n  toolbarFiltersLabel: '필터 표시',\n  toolbarFiltersTooltipHide: '필터 숨기기',\n  toolbarFiltersTooltipShow: '필터 표시',\n  toolbarFiltersTooltipActive: count => `${count}건의 필터를 적용중`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: '검색…',\n  toolbarQuickFilterLabel: '검색',\n  toolbarQuickFilterDeleteIconLabel: '초기화',\n  // Export selector toolbar button text\n  toolbarExport: '내보내기',\n  toolbarExportLabel: '내보내기',\n  toolbarExportCSV: 'CSV로 내보내기',\n  toolbarExportPrint: '프린트',\n  toolbarExportExcel: 'Excel로 내보내기',\n  // Columns management text\n  columnsManagementSearchTitle: '검색',\n  columnsManagementNoColumns: '열이 없습니다.',\n  columnsManagementShowHideAllText: '모두 보기/숨기기',\n  columnsManagementReset: '초기화',\n  columnsManagementDeleteIconLabel: '제거',\n  // Filter panel text\n  filterPanelAddFilter: '필터 추가',\n  filterPanelRemoveAll: '모두 삭제',\n  filterPanelDeleteIconLabel: '삭제',\n  filterPanelLogicOperator: '논리 연산자',\n  filterPanelOperator: '연산자',\n  filterPanelOperatorAnd: '그리고',\n  filterPanelOperatorOr: '또는',\n  filterPanelColumns: '목록',\n  filterPanelInputLabel: '값',\n  filterPanelInputPlaceholder: '값 입력',\n  // Filter operators text\n  filterOperatorContains: '포함하는',\n  filterOperatorDoesNotContain: '포함하지 않는',\n  filterOperatorEquals: '값이 같은',\n  filterOperatorDoesNotEqual: '값이 다른',\n  filterOperatorStartsWith: '시작하는',\n  filterOperatorEndsWith: '끝나는',\n  filterOperatorIs: '~인',\n  filterOperatorNot: '~아닌',\n  filterOperatorAfter: '더 이후',\n  filterOperatorOnOrAfter: '이후',\n  filterOperatorBefore: '더 이전',\n  filterOperatorOnOrBefore: '이전',\n  filterOperatorIsEmpty: '값이 없는',\n  filterOperatorIsNotEmpty: '값이 있는',\n  filterOperatorIsAnyOf: '값 중 하나인',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: '포함하는',\n  headerFilterOperatorDoesNotContain: '포함하지 않는',\n  headerFilterOperatorEquals: '값이 같은',\n  headerFilterOperatorDoesNotEqual: '값이 다른',\n  headerFilterOperatorStartsWith: '시작하는',\n  headerFilterOperatorEndsWith: '끝나는',\n  headerFilterOperatorIs: '~인',\n  headerFilterOperatorNot: '~아닌',\n  headerFilterOperatorAfter: '더 이후',\n  headerFilterOperatorOnOrAfter: '이후',\n  headerFilterOperatorBefore: '더 이전',\n  headerFilterOperatorOnOrBefore: '이전',\n  headerFilterOperatorIsEmpty: '값이 없는',\n  headerFilterOperatorIsNotEmpty: '값이 있는',\n  headerFilterOperatorIsAnyOf: '값 중 하나인',\n  'headerFilterOperator=': '값이 같은',\n  'headerFilterOperator!=': '값이 다른',\n  'headerFilterOperator>': '더 큰',\n  'headerFilterOperator>=': '같거나 더 큰',\n  'headerFilterOperator<': '더 작은',\n  'headerFilterOperator<=': '같거나 더 작은',\n  // Filter values text\n  filterValueAny: '아무값',\n  filterValueTrue: '참',\n  filterValueFalse: '거짓',\n  // Column menu text\n  columnMenuLabel: '메뉴',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: '열 표시',\n  columnMenuManageColumns: '열 관리',\n  columnMenuFilter: '필터',\n  columnMenuHideColumn: '열 숨기기',\n  columnMenuUnsort: '정렬 해제',\n  columnMenuSortAsc: '오름차순 정렬',\n  columnMenuSortDesc: '내림차순 정렬',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `${count}건의 필터를 적용중`,\n  columnHeaderFiltersLabel: '필터 표시',\n  columnHeaderSortIconLabel: '정렬',\n  // Rows selected footer text\n  footerRowSelected: count => `${count}행 선택중`,\n  // Total row amount footer text\n  footerTotalRows: '총 행수:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: '선택',\n  checkboxSelectionSelectAllRows: '모든 행 선택',\n  checkboxSelectionUnselectAllRows: '모든 행 선택 해제',\n  checkboxSelectionSelectRow: '행 선택',\n  checkboxSelectionUnselectRow: '행 선택 해제',\n  // Boolean cell text\n  booleanCellTrueLabel: '참',\n  booleanCellFalseLabel: '거짓',\n  // Actions cell more text\n  actionsCellMore: '더보기',\n  // Column pinning text\n  pinToLeft: '왼쪽에 고정',\n  pinToRight: '오른쪽에 고정',\n  unpin: '고정 해제',\n  // Tree Data\n  treeDataGroupingHeaderName: '그룹',\n  treeDataExpand: '하위노드 펼치기',\n  treeDataCollapse: '하위노드 접기',\n  // Grouping columns\n  groupingColumnHeaderName: '그룹',\n  groupColumn: name => `${name} 값으로 그룹 생성`,\n  unGroupColumn: name => `${name} 값으로 그룹 해제`,\n  // Master/detail\n  detailPanelToggle: '상세 패널 토글',\n  expandDetailPanel: '열기',\n  collapseDetailPanel: '접기',\n  // Row reordering text\n  rowReorderingHeaderName: '행 재배치',\n  // Aggregation\n  aggregationMenuItemHeader: '집계',\n  aggregationFunctionLabelSum: '합',\n  aggregationFunctionLabelAvg: '평균',\n  aggregationFunctionLabelMin: '최소값',\n  aggregationFunctionLabelMax: '최대값',\n  aggregationFunctionLabelSize: '크기'\n};\nexport const koKR = getGridLocalization(koKRGrid, koKRCore);", "import { nbNO as nbNOCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst nbNOGrid = {\n  // Root\n  noRowsLabel: 'Ingen rader',\n  noResultsOverlayLabel: 'Fant ingen resultat.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Tetthet',\n  toolbarDensityLabel: 'Tetthet',\n  toolbarDensityCompact: 'Kompakt',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Komfortabelt',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Kolonner',\n  toolbarColumnsLabel: 'Velg kolonner',\n  // Filters toolbar button text\n  toolbarFilters: 'Filter',\n  toolbarFiltersLabel: 'Vis filter',\n  toolbarFiltersTooltipHide: 'Skjul filter',\n  toolbarFiltersTooltipShow: 'Vis filter',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Søk…',\n  toolbarQuickFilterLabel: 'Søk',\n  toolbarQuickFilterDeleteIconLabel: 'Slett',\n  // Export selector toolbar button text\n  toolbarExport: 'Eksporter',\n  toolbarExportLabel: 'Eksporter',\n  toolbarExportCSV: 'Last ned som CSV',\n  toolbarExportPrint: 'Skriv ut',\n  toolbarExportExcel: 'Last ned som Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Søk',\n  columnsManagementNoColumns: 'Ingen kolonner',\n  columnsManagementShowHideAllText: 'Vis/skjul alle',\n  columnsManagementReset: 'Nullstill',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Legg til filter',\n  filterPanelRemoveAll: 'Fjern alle',\n  filterPanelDeleteIconLabel: 'Slett',\n  filterPanelLogicOperator: 'Logisk operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'Og',\n  filterPanelOperatorOr: 'Eller',\n  filterPanelColumns: 'Kolonner',\n  filterPanelInputLabel: 'Verdi',\n  filterPanelInputPlaceholder: 'Filter verdi',\n  // Filter operators text\n  filterOperatorContains: 'inneholder',\n  // filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'er lik',\n  // filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'starter med',\n  filterOperatorEndsWith: 'slutter med',\n  filterOperatorIs: 'er',\n  filterOperatorNot: 'er ikke',\n  filterOperatorAfter: 'er etter',\n  filterOperatorOnOrAfter: 'er på eller etter',\n  filterOperatorBefore: 'er før',\n  filterOperatorOnOrBefore: 'er på eller før',\n  filterOperatorIsEmpty: 'er tom',\n  filterOperatorIsNotEmpty: 'er ikke tom',\n  filterOperatorIsAnyOf: 'er en av',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Inneholder',\n  // headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'Lik',\n  // headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Starter på',\n  headerFilterOperatorEndsWith: 'Slutter på',\n  headerFilterOperatorIs: 'Er',\n  headerFilterOperatorNot: 'Er ikke',\n  headerFilterOperatorAfter: 'Er etter',\n  headerFilterOperatorOnOrAfter: 'Er på eller etter',\n  headerFilterOperatorBefore: 'Er før',\n  headerFilterOperatorOnOrBefore: 'Er på eller før',\n  headerFilterOperatorIsEmpty: 'Er tom',\n  headerFilterOperatorIsNotEmpty: 'Er ikke tom',\n  headerFilterOperatorIsAnyOf: 'Er en av',\n  'headerFilterOperator=': 'Lik',\n  'headerFilterOperator!=': 'Ikke lik',\n  'headerFilterOperator>': 'Større enn',\n  'headerFilterOperator>=': 'Større enn eller lik',\n  'headerFilterOperator<': 'Mindre enn',\n  'headerFilterOperator<=': 'Mindre enn eller lik',\n  // Filter values text\n  filterValueAny: 'noen',\n  filterValueTrue: 'sant',\n  filterValueFalse: 'usant',\n  // Column menu text\n  columnMenuLabel: 'Meny',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Vis kolonner',\n  columnMenuManageColumns: 'Administrer kolonner',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Skjul',\n  columnMenuUnsort: 'Usorter',\n  columnMenuSortAsc: 'Sorter ØKENDE',\n  columnMenuSortDesc: 'Sorter SYNKENDE',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,\n  columnHeaderFiltersLabel: 'Vis filter',\n  columnHeaderSortIconLabel: 'Sorter',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} rader valgt` : `${count.toLocaleString()} rad valgt`,\n  // Total row amount footer text\n  footerTotalRows: 'Totalt antall rader:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} av ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Avmerkingsboks',\n  checkboxSelectionSelectAllRows: 'Velg alle rader',\n  checkboxSelectionUnselectAllRows: 'Velg bort alle rader',\n  checkboxSelectionSelectRow: 'Velg rad',\n  checkboxSelectionUnselectRow: 'Velg bort rad',\n  // Boolean cell text\n  booleanCellTrueLabel: 'sant',\n  booleanCellFalseLabel: 'usant',\n  // Actions cell more text\n  actionsCellMore: 'mer',\n  // Column pinning text\n  pinToLeft: 'Fest til venstre',\n  pinToRight: 'Fest til høyre',\n  unpin: 'Løsne',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grupper',\n  treeDataExpand: 'se barn',\n  treeDataCollapse: 'skjul barn',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupper',\n  groupColumn: name => `Grupper på ${name}`,\n  unGroupColumn: name => `Slutt å grupper på ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Utvid/kollaps detalj panel',\n  expandDetailPanel: 'Utvid',\n  collapseDetailPanel: 'Kollaps',\n  // Row reordering text\n  rowReorderingHeaderName: 'Radreorganisering',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregering',\n  aggregationFunctionLabelSum: 'sum',\n  aggregationFunctionLabelAvg: 'snitt',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'maks',\n  aggregationFunctionLabelSize: 'størrelse'\n};\nexport const nbNO = getGridLocalization(nbNOGrid, nbNOCore);", "import { nlNL as nlNLCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst nlNLGrid = {\n  // Root\n  noRowsLabel: 'Geen resultaten.',\n  noResultsOverlayLabel: 'Geen resultaten gevonden.',\n  // Density selector toolbar button text\n  toolbarDensity: '<PERSON>root<PERSON>',\n  toolbarDensityLabel: 'Grootte',\n  toolbarDensityCompact: 'Compact',\n  toolbarDensityStandard: 'Normaal',\n  toolbarDensityComfortable: 'Breed',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Kolommen',\n  toolbarColumnsLabel: 'Kies kolommen',\n  // Filters toolbar button text\n  toolbarFilters: 'Filters',\n  toolbarFiltersLabel: 'Toon filters',\n  toolbarFiltersTooltipHide: 'Verberg filters',\n  toolbarFiltersTooltipShow: 'Toon filters',\n  toolbarFiltersTooltipActive: count => count > 1 ? `${count} actieve filters` : `${count} filter actief`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: '<PERSON><PERSON>…',\n  toolbarQuickFilterLabel: 'Zoeken',\n  toolbarQuickFilterDeleteIconLabel: 'Wissen',\n  // Export selector toolbar button text\n  toolbarExport: 'Exporteren',\n  toolbarExportLabel: 'Exporteren',\n  toolbarExportCSV: 'Exporteer naar CSV',\n  toolbarExportPrint: 'Print',\n  toolbarExportExcel: 'Downloaden als Excel-bestand',\n  // Columns management text\n  columnsManagementSearchTitle: 'Zoeken',\n  columnsManagementNoColumns: 'Geen kolommen',\n  columnsManagementShowHideAllText: 'Toon/Verberg Alle',\n  columnsManagementReset: 'Reset',\n  columnsManagementDeleteIconLabel: 'Verwijderen',\n  // Filter panel text\n  filterPanelAddFilter: 'Filter toevoegen',\n  filterPanelRemoveAll: 'Alles verwijderen',\n  filterPanelDeleteIconLabel: 'Verwijderen',\n  filterPanelLogicOperator: 'Logische operator',\n  filterPanelOperator: 'Operatoren',\n  filterPanelOperatorAnd: 'En',\n  filterPanelOperatorOr: 'Of',\n  filterPanelColumns: 'Kolommen',\n  filterPanelInputLabel: 'Waarde',\n  filterPanelInputPlaceholder: 'Filter waarde',\n  // Filter operators text\n  filterOperatorContains: 'bevat',\n  filterOperatorDoesNotContain: 'bevat niet',\n  filterOperatorEquals: 'gelijk aan',\n  filterOperatorDoesNotEqual: 'niet gelijk aan',\n  filterOperatorStartsWith: 'begint met',\n  filterOperatorEndsWith: 'eindigt met',\n  filterOperatorIs: 'is',\n  filterOperatorNot: 'is niet',\n  filterOperatorAfter: 'is na',\n  filterOperatorOnOrAfter: 'is gelijk of er na',\n  filterOperatorBefore: 'is voor',\n  filterOperatorOnOrBefore: 'is gelijk of er voor',\n  filterOperatorIsEmpty: 'is leeg',\n  filterOperatorIsNotEmpty: 'is niet leeg',\n  filterOperatorIsAnyOf: 'is een van',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Bevat',\n  headerFilterOperatorDoesNotContain: 'Bevat niet',\n  headerFilterOperatorEquals: 'Gelijk aan',\n  headerFilterOperatorDoesNotEqual: 'Niet gelijk aan',\n  headerFilterOperatorStartsWith: 'Begint met',\n  headerFilterOperatorEndsWith: 'Eindigt met',\n  headerFilterOperatorIs: 'Is',\n  headerFilterOperatorNot: 'Is niet',\n  headerFilterOperatorAfter: 'Is na',\n  headerFilterOperatorOnOrAfter: 'Is op of na',\n  headerFilterOperatorBefore: 'Is voor',\n  headerFilterOperatorOnOrBefore: 'Is op of voor',\n  headerFilterOperatorIsEmpty: 'Is leeg',\n  headerFilterOperatorIsNotEmpty: 'Is niet leeg',\n  headerFilterOperatorIsAnyOf: 'Is een van',\n  'headerFilterOperator=': 'Gelijk aan',\n  'headerFilterOperator!=': 'Niet gelijk aan',\n  'headerFilterOperator>': 'Is groter dan',\n  'headerFilterOperator>=': 'Is groter dan of gelijk aan',\n  'headerFilterOperator<': 'Is kleiner dan',\n  'headerFilterOperator<=': 'Is kleiner dan of gelijk aan',\n  // Filter values text\n  filterValueAny: 'alles',\n  filterValueTrue: 'waar',\n  filterValueFalse: 'onwaar',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Toon kolommen',\n  columnMenuManageColumns: 'Kolommen beheren',\n  columnMenuFilter: 'Filteren',\n  columnMenuHideColumn: 'Verbergen',\n  columnMenuUnsort: 'Annuleer sortering',\n  columnMenuSortAsc: 'Oplopend sorteren',\n  columnMenuSortDesc: 'Aflopend sorteren',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count > 1 ? `${count} actieve filters` : `${count} filter actief`,\n  columnHeaderFiltersLabel: 'Toon filters',\n  columnHeaderSortIconLabel: 'Sorteren',\n  // Rows selected footer text\n  footerRowSelected: count => count > 1 ? `${count.toLocaleString()} rijen geselecteerd` : `${count.toLocaleString()} rij geselecteerd`,\n  // Total row amount footer text\n  footerTotalRows: 'Totaal:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} van ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Checkbox selectie',\n  checkboxSelectionSelectAllRows: 'Alle rijen selecteren',\n  checkboxSelectionUnselectAllRows: 'Alle rijen de-selecteren',\n  checkboxSelectionSelectRow: 'Rij selecteren',\n  checkboxSelectionUnselectRow: 'Rij de-selecteren',\n  // Boolean cell text\n  booleanCellTrueLabel: 'waar',\n  booleanCellFalseLabel: 'onwaar',\n  // Actions cell more text\n  actionsCellMore: 'meer',\n  // Column pinning text\n  pinToLeft: 'Links vastzetten',\n  pinToRight: 'Rechts vastzetten',\n  unpin: 'Losmaken',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Groep',\n  treeDataExpand: 'Uitvouwen',\n  treeDataCollapse: 'Inklappen',\n  // Grouping columns\n  groupingColumnHeaderName: 'Groep',\n  groupColumn: name => `Groepeer op ${name}`,\n  unGroupColumn: name => `Stop groeperen op ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Detailmenu in- of uitklappen',\n  expandDetailPanel: 'Uitklappen',\n  collapseDetailPanel: 'Inklappen',\n  // Row reordering text\n  rowReorderingHeaderName: 'Rijen hersorteren',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregatie',\n  aggregationFunctionLabelSum: 'som',\n  aggregationFunctionLabelAvg: 'gem',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'grootte'\n};\nexport const nlNL = getGridLocalization(nlNLGrid, nlNLCore);", "import { nnNO as nnNOCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst nnNOGrid = {\n  // Root\n  noRowsLabel: 'Ingen rader',\n  noResultsOverlayLabel: 'Fann ingen resultat.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Tettheit',\n  toolbarDensityLabel: 'Tettheit',\n  toolbarDensityCompact: 'Kompakt',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Komfortabelt',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Kolonner',\n  toolbarColumnsLabel: 'Vel kolonner',\n  // Filters toolbar button text\n  toolbarFilters: 'Filter',\n  toolbarFiltersLabel: 'Vis filter',\n  toolbarFiltersTooltipHide: 'Skjul filter',\n  toolbarFiltersTooltipShow: 'Vis filter',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Søk…',\n  toolbarQuickFilterLabel: 'Søk',\n  toolbarQuickFilterDeleteIconLabel: 'Slett',\n  // Export selector toolbar button text\n  toolbarExport: 'Eksporter',\n  toolbarExportLabel: 'Eksporter',\n  toolbarExportCSV: 'Last ned som CSV',\n  toolbarExportPrint: 'Skriv ut',\n  toolbarExportExcel: 'Last ned som Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Søk',\n  columnsManagementNoColumns: 'Ingen kolonner',\n  columnsManagementShowHideAllText: 'Vis/skjul alle',\n  columnsManagementReset: 'Nullstill',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Legg til filter',\n  filterPanelRemoveAll: 'Fjern alle',\n  filterPanelDeleteIconLabel: 'Slett',\n  filterPanelLogicOperator: 'Logisk operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'Og',\n  filterPanelOperatorOr: 'Eller',\n  filterPanelColumns: 'Kolonner',\n  filterPanelInputLabel: 'Verdi',\n  filterPanelInputPlaceholder: 'Filter verdi',\n  // Filter operators text\n  filterOperatorContains: 'inneheld',\n  // filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'er lik',\n  // filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'startar med',\n  filterOperatorEndsWith: 'sluttar med',\n  filterOperatorIs: 'er',\n  filterOperatorNot: 'er ikkje',\n  filterOperatorAfter: 'er etter',\n  filterOperatorOnOrAfter: 'er på eller etter',\n  filterOperatorBefore: 'er før',\n  filterOperatorOnOrBefore: 'er på eller før',\n  filterOperatorIsEmpty: 'er tom',\n  filterOperatorIsNotEmpty: 'er ikkje tom',\n  filterOperatorIsAnyOf: 'er ein av',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Inneheld',\n  // headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'Lik',\n  // headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Startar på',\n  headerFilterOperatorEndsWith: 'Sluttar på',\n  headerFilterOperatorIs: 'Er',\n  headerFilterOperatorNot: 'Er ikkje',\n  headerFilterOperatorAfter: 'Er etter',\n  headerFilterOperatorOnOrAfter: 'Er på eller etter',\n  headerFilterOperatorBefore: 'Er før',\n  headerFilterOperatorOnOrBefore: 'Er på eller før',\n  headerFilterOperatorIsEmpty: 'Er tom',\n  headerFilterOperatorIsNotEmpty: 'Er ikkje tom',\n  headerFilterOperatorIsAnyOf: 'Er ein av',\n  'headerFilterOperator=': 'Lik',\n  'headerFilterOperator!=': 'Ikkje lik',\n  'headerFilterOperator>': 'Større enn',\n  'headerFilterOperator>=': 'Større enn eller lik',\n  'headerFilterOperator<': 'Mindre enn',\n  'headerFilterOperator<=': 'Mindre enn eller lik',\n  // Filter values text\n  filterValueAny: 'nokon',\n  filterValueTrue: 'sant',\n  filterValueFalse: 'usant',\n  // Column menu text\n  columnMenuLabel: 'Meny',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Vis kolonner',\n  columnMenuManageColumns: 'Administrer kolonner',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Skjul',\n  columnMenuUnsort: 'Usorter',\n  columnMenuSortAsc: 'Sorter AUKANDE',\n  columnMenuSortDesc: 'Sorter SYNKANDE',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} aktive filter` : `${count} aktivt filter`,\n  columnHeaderFiltersLabel: 'Vis filter',\n  columnHeaderSortIconLabel: 'Sorter',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} rader valt` : `${count.toLocaleString()} rad valt`,\n  // Total row amount footer text\n  footerTotalRows: 'Totalt tal rader:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} av ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Avmerkingsboks',\n  checkboxSelectionSelectAllRows: 'Vel alle rader',\n  checkboxSelectionUnselectAllRows: 'Vel vekk alle rader',\n  checkboxSelectionSelectRow: 'Vel rad',\n  checkboxSelectionUnselectRow: 'Vel vekk rad',\n  // Boolean cell text\n  booleanCellTrueLabel: 'sant',\n  booleanCellFalseLabel: 'usant',\n  // Actions cell more text\n  actionsCellMore: 'meir',\n  // Column pinning text\n  pinToLeft: 'Fest til venstre',\n  pinToRight: 'Fest til høgre',\n  unpin: 'Lausne',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grupper',\n  treeDataExpand: 'vis barn',\n  treeDataCollapse: 'skjul barn',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupper',\n  groupColumn: name => `Grupper på ${name}`,\n  unGroupColumn: name => `Slutt å grupper på ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Utvid/kollaps detalj panel',\n  expandDetailPanel: 'Utvid',\n  collapseDetailPanel: 'Kolaps',\n  // Row reordering text\n  rowReorderingHeaderName: 'Radreorganisering',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregering',\n  aggregationFunctionLabelSum: 'sum',\n  aggregationFunctionLabelAvg: 'snitt',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'maks',\n  aggregationFunctionLabelSize: 'størrelse'\n};\nexport const nnNO = getGridLocalization(nnNOGrid, nnNOCore);", "import { plPL as plPLCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst plPLGrid = {\n  // Root\n  noRowsLabel: '<PERSON>rak danych',\n  noResultsOverlayLabel: 'Nie znaleziono wyników.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Wysokość rzędu',\n  toolbarDensityLabel: 'Wysokość rzędu',\n  toolbarDensityCompact: 'Kompakt',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Komfort',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Kolumny',\n  toolbarColumnsLabel: 'Zaznacz kolumny',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtry',\n  toolbarFiltersLabel: 'Po<PERSON>ż filtry',\n  toolbarFiltersTooltipHide: 'Ukryj filtry',\n  toolbarFiltersTooltipShow: '<PERSON><PERSON><PERSON> filtry',\n  toolbarFiltersTooltipActive: count => `Liczba aktywnych filtrów: ${count}`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Wyszukaj…',\n  toolbarQuickFilterLabel: 'Szukaj',\n  toolbarQuickFilterDeleteIconLabel: 'Wyczyść',\n  // Export selector toolbar button text\n  toolbarExport: 'Eksportuj',\n  toolbarExportLabel: 'Eksportuj',\n  toolbarExportCSV: 'Pobierz jako plik CSV',\n  toolbarExportPrint: 'Drukuj',\n  toolbarExportExcel: 'Pobierz jako plik Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Szukaj',\n  columnsManagementNoColumns: 'Brak kolumn',\n  columnsManagementShowHideAllText: 'Wyświetl/Ukryj wszystkie',\n  columnsManagementReset: 'Resetuj',\n  columnsManagementDeleteIconLabel: 'Wyczyść',\n  // Filter panel text\n  filterPanelAddFilter: 'Dodaj filtr',\n  filterPanelRemoveAll: 'Usuń wszystkie',\n  filterPanelDeleteIconLabel: 'Usuń',\n  filterPanelLogicOperator: 'Operator logiczny',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'I',\n  filterPanelOperatorOr: 'Lub',\n  filterPanelColumns: 'Kolumny',\n  filterPanelInputLabel: 'Wartość',\n  filterPanelInputPlaceholder: 'Filtrowana wartość',\n  // Filter operators text\n  filterOperatorContains: 'zawiera',\n  filterOperatorDoesNotContain: 'nie zawiera',\n  filterOperatorEquals: 'równa się',\n  filterOperatorDoesNotEqual: 'nie równa się',\n  filterOperatorStartsWith: 'zaczyna się od',\n  filterOperatorEndsWith: 'kończy się na',\n  filterOperatorIs: 'równa się',\n  filterOperatorNot: 'różne',\n  filterOperatorAfter: 'większe niż',\n  filterOperatorOnOrAfter: 'większe lub równe',\n  filterOperatorBefore: 'mniejsze niż',\n  filterOperatorOnOrBefore: 'mniejsze lub równe',\n  filterOperatorIsEmpty: 'jest pusty',\n  filterOperatorIsNotEmpty: 'nie jest pusty',\n  filterOperatorIsAnyOf: 'jest jednym z',\n  'filterOperator=': 'równa się',\n  'filterOperator!=': 'nie równa się',\n  'filterOperator>': 'większy niż',\n  'filterOperator>=': 'większy lub równy',\n  'filterOperator<': 'mniejszy niż',\n  'filterOperator<=': 'mniejszy lub równy',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Zawiera',\n  headerFilterOperatorDoesNotContain: 'Nie zawiera',\n  headerFilterOperatorEquals: 'Równa się',\n  headerFilterOperatorDoesNotEqual: 'Nie równa się',\n  headerFilterOperatorStartsWith: 'Zaczyna się od',\n  headerFilterOperatorEndsWith: 'Kończy się na',\n  headerFilterOperatorIs: 'Jest',\n  headerFilterOperatorNot: 'Niepuste',\n  headerFilterOperatorAfter: 'Jest po',\n  headerFilterOperatorOnOrAfter: 'Jest w lub po',\n  headerFilterOperatorBefore: 'Jest przed',\n  headerFilterOperatorOnOrBefore: 'Jest w lub przed',\n  headerFilterOperatorIsEmpty: 'Jest pusty',\n  headerFilterOperatorIsNotEmpty: 'Nie jest pusty',\n  headerFilterOperatorIsAnyOf: 'Jest jednym z',\n  'headerFilterOperator=': 'Równa się',\n  'headerFilterOperator!=': 'Nie równa się',\n  'headerFilterOperator>': 'Większy niż',\n  'headerFilterOperator>=': 'Większy lub równy',\n  'headerFilterOperator<': 'Mniejszy niż',\n  'headerFilterOperator<=': 'Mniejszy lub równy',\n  // Filter values text\n  filterValueAny: 'dowolny',\n  filterValueTrue: 'prawda',\n  filterValueFalse: 'fałsz',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  columnMenuAriaLabel: columnName => `Menu kolumny: ${columnName}`,\n  columnMenuShowColumns: 'Pokaż wszystkie kolumny',\n  columnMenuManageColumns: 'Zarządzaj kolumnami',\n  columnMenuFilter: 'Filtr',\n  columnMenuHideColumn: 'Ukryj',\n  columnMenuUnsort: 'Anuluj sortowanie',\n  columnMenuSortAsc: 'Sortuj rosnąco',\n  columnMenuSortDesc: 'Sortuj malejąco',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `Liczba aktywnych filtrów: ${count}`,\n  columnHeaderFiltersLabel: 'Pokaż filtry',\n  columnHeaderSortIconLabel: 'Sortuj',\n  // Rows selected footer text\n  footerRowSelected: count => `Liczba wybranych wierszy: ${count.toLocaleString()}`,\n  // Total row amount footer text\n  footerTotalRows: 'Łączna liczba wierszy:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} z ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Pole wyboru',\n  checkboxSelectionSelectAllRows: 'Zaznacz wszystkie wiersze',\n  checkboxSelectionUnselectAllRows: 'Odznacz wszystkie wiersze',\n  checkboxSelectionSelectRow: 'Zaznacz wiersz',\n  checkboxSelectionUnselectRow: 'Odznacz wiersz',\n  // Boolean cell text\n  booleanCellTrueLabel: 'tak',\n  booleanCellFalseLabel: 'nie',\n  // Actions cell more text\n  actionsCellMore: 'więcej',\n  // Column pinning text\n  pinToLeft: 'Przypnij do lewej',\n  pinToRight: 'Przypnij do prawej',\n  unpin: 'Odepnij',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grupa',\n  treeDataExpand: 'pokaż elementy potomne',\n  treeDataCollapse: 'ukryj elementy potomne',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupa',\n  groupColumn: name => `Grupuj według ${name}`,\n  unGroupColumn: name => `Rozgrupuj ${name}`,\n  // Master/detail\n  // detailPanelToggle: 'Detail panel toggle',\n  expandDetailPanel: 'Rozwiń',\n  collapseDetailPanel: 'Zwiń',\n  // Row reordering text\n  rowReorderingHeaderName: 'Porządkowanie wierszy',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agregacja',\n  aggregationFunctionLabelSum: 'suma',\n  aggregationFunctionLabelAvg: 'średnia',\n  aggregationFunctionLabelMin: 'minimum',\n  aggregationFunctionLabelMax: 'maximum',\n  aggregationFunctionLabelSize: 'rozmiar'\n};\nexport const plPL = getGridLocalization(plPLGrid, plPLCore);", "import { ptBR as ptBRCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst ptBRGrid = {\n  // Root\n  noRowsLabel: 'Nenhuma linha',\n  noResultsOverlayLabel: 'Nenhum resultado encontrado.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Densidade',\n  toolbarDensityLabel: 'Densidade',\n  toolbarDensityCompact: 'Compacto',\n  toolbarDensityStandard: 'Padrão',\n  toolbarDensityComfortable: 'Confortável',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Colunas',\n  toolbarColumnsLabel: 'Exibir seletor de colunas',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtros',\n  toolbarFiltersLabel: 'Exibir filtros',\n  toolbarFiltersTooltipHide: 'Ocultar filtros',\n  toolbarFiltersTooltipShow: 'Exibir filtros',\n  toolbarFiltersTooltipActive: count => `${count} ${count !== 1 ? 'filtros' : 'filtro'} ${count !== 1 ? 'ativos' : 'ativo'}`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Procurar…',\n  toolbarQuickFilterLabel: 'Procurar',\n  toolbarQuickFilterDeleteIconLabel: 'Limpar',\n  // Export selector toolbar button text\n  toolbarExport: 'Exportar',\n  toolbarExportLabel: 'Exportar',\n  toolbarExportCSV: 'Baixar como CSV',\n  toolbarExportPrint: 'Imprimir',\n  toolbarExportExcel: 'Baixar como Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Buscar',\n  columnsManagementNoColumns: 'Nenhuma coluna',\n  columnsManagementShowHideAllText: 'Mostrar/Ocultar Todas',\n  columnsManagementReset: 'Redefinir',\n  columnsManagementDeleteIconLabel: 'Limpar',\n  // Filter panel text\n  filterPanelAddFilter: 'Adicionar filtro',\n  filterPanelRemoveAll: 'Remover todos',\n  filterPanelDeleteIconLabel: 'Excluir',\n  filterPanelLogicOperator: 'Operador lógico',\n  filterPanelOperator: 'Operador',\n  filterPanelOperatorAnd: 'E',\n  filterPanelOperatorOr: 'Ou',\n  filterPanelColumns: 'Colunas',\n  filterPanelInputLabel: 'Valor',\n  filterPanelInputPlaceholder: 'Filtrar valor',\n  // Filter operators text\n  filterOperatorContains: 'contém',\n  filterOperatorDoesNotContain: 'não contém',\n  filterOperatorEquals: 'é igual a',\n  filterOperatorDoesNotEqual: 'não é igual a',\n  filterOperatorStartsWith: 'começa com',\n  filterOperatorEndsWith: 'termina com',\n  filterOperatorIs: 'é',\n  filterOperatorNot: 'não é',\n  filterOperatorAfter: 'após',\n  filterOperatorOnOrAfter: 'em ou após',\n  filterOperatorBefore: 'antes de',\n  filterOperatorOnOrBefore: 'em ou antes de',\n  filterOperatorIsEmpty: 'está vazio',\n  filterOperatorIsNotEmpty: 'não está vazio',\n  filterOperatorIsAnyOf: 'é qualquer um dos',\n  'filterOperator=': 'igual à',\n  'filterOperator!=': 'diferente de',\n  'filterOperator>': 'maior que',\n  'filterOperator>=': 'maior ou igual que',\n  'filterOperator<': 'menor que',\n  'filterOperator<=': 'menor ou igual que',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contém',\n  headerFilterOperatorDoesNotContain: 'Não contém',\n  headerFilterOperatorEquals: 'Igual',\n  headerFilterOperatorDoesNotEqual: 'Não é igual a',\n  headerFilterOperatorStartsWith: 'Começa com',\n  headerFilterOperatorEndsWith: 'Termina com',\n  headerFilterOperatorIs: 'É',\n  headerFilterOperatorNot: 'Não é',\n  headerFilterOperatorAfter: 'Depois de',\n  headerFilterOperatorOnOrAfter: 'Está entre ou depois de',\n  headerFilterOperatorBefore: 'Antes de',\n  headerFilterOperatorOnOrBefore: 'Está entre ou antes de',\n  headerFilterOperatorIsEmpty: 'É vazio',\n  headerFilterOperatorIsNotEmpty: 'Não é vazio',\n  headerFilterOperatorIsAnyOf: 'É algum',\n  'headerFilterOperator=': 'Igual',\n  'headerFilterOperator!=': 'Não igual',\n  'headerFilterOperator>': 'Maior que',\n  'headerFilterOperator>=': 'Maior que ou igual a',\n  'headerFilterOperator<': 'Menor que',\n  'headerFilterOperator<=': 'Menor que ou igual a',\n  // Filter values text\n  filterValueAny: 'qualquer',\n  filterValueTrue: 'verdadeiro',\n  filterValueFalse: 'falso',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Exibir colunas',\n  columnMenuManageColumns: 'Gerir colunas',\n  columnMenuFilter: 'Filtrar',\n  columnMenuHideColumn: 'Ocultar',\n  columnMenuUnsort: 'Desfazer ordenação',\n  columnMenuSortAsc: 'Ordenar do menor para o maior',\n  columnMenuSortDesc: 'Ordenar do maior para o menor',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `${count} ${count !== 1 ? 'filtros' : 'filtro'} ${count !== 1 ? 'ativos' : 'ativo'}`,\n  columnHeaderFiltersLabel: 'Exibir Filtros',\n  columnHeaderSortIconLabel: 'Ordenar',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} linhas selecionadas` : `${count.toLocaleString()} linha selecionada`,\n  // Total row amount footer text\n  footerTotalRows: 'Total de linhas:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} de ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Seleção',\n  checkboxSelectionSelectAllRows: 'Selecionar todas linhas',\n  checkboxSelectionUnselectAllRows: 'Deselecionar todas linhas',\n  checkboxSelectionSelectRow: 'Selecionar linha',\n  checkboxSelectionUnselectRow: 'Deselecionar linha',\n  // Boolean cell text\n  booleanCellTrueLabel: 'sim',\n  booleanCellFalseLabel: 'não',\n  // Actions cell more text\n  actionsCellMore: 'mais',\n  // Column pinning text\n  pinToLeft: 'Fixar à esquerda',\n  pinToRight: 'Fixar à direita',\n  unpin: 'Desafixar',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grupo',\n  treeDataExpand: 'mostrar filhos',\n  treeDataCollapse: 'esconder filhos',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupo',\n  groupColumn: name => `Agrupar por ${name}`,\n  unGroupColumn: name => `Parar agrupamento por ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Painel de detalhes',\n  expandDetailPanel: 'Expandir',\n  collapseDetailPanel: 'Esconder',\n  // Row reordering text\n  rowReorderingHeaderName: 'Reorganizar linhas',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agrupar',\n  aggregationFunctionLabelSum: 'soma',\n  aggregationFunctionLabelAvg: 'média',\n  aggregationFunctionLabelMin: 'mín',\n  aggregationFunctionLabelMax: 'máx',\n  aggregationFunctionLabelSize: 'tamanho'\n};\nexport const ptBR = getGridLocalization(ptBRGrid, ptBRCore);", "import { roRO as roROCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst roROGrid = {\n  // Root\n  noRowsLabel: 'Lipsă date',\n  noResultsOverlayLabel: 'Nu au fost găsite rezultate.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Înălțime rând',\n  toolbarDensityLabel: 'Înălțime rând',\n  toolbarDensityCompact: 'Compact',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Lat',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Coloane',\n  toolbarColumnsLabel: 'Afișează selecție coloane',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtru',\n  toolbarFiltersLabel: 'Afișează filtru',\n  toolbarFiltersTooltipHide: 'Ascunde filtru',\n  toolbarFiltersTooltipShow: 'Afișează filtru',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} filtru activ` : `${count} filtru activ`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Căutare…',\n  toolbarQuickFilterLabel: 'Căutare',\n  toolbarQuickFilterDeleteIconLabel: 'Ștergere',\n  // Export selector toolbar button text\n  toolbarExport: 'Export',\n  toolbarExportLabel: 'Export',\n  toolbarExportCSV: 'Download în format CSV',\n  toolbarExportPrint: 'Printare',\n  toolbarExportExcel: 'Download în format Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Caută',\n  columnsManagementNoColumns: 'Nicio coloană',\n  columnsManagementShowHideAllText: 'Arată/Ascunde tot',\n  columnsManagementReset: 'Resetează',\n  columnsManagementDeleteIconLabel: 'Șterge',\n  // Filter panel text\n  filterPanelAddFilter: 'Adăugare filtru',\n  filterPanelRemoveAll: 'Șterge tot',\n  filterPanelDeleteIconLabel: 'Ștergere',\n  filterPanelLogicOperator: 'Operatori logici',\n  filterPanelOperator: 'Operatori',\n  filterPanelOperatorAnd: 'Și',\n  filterPanelOperatorOr: 'Sau',\n  filterPanelColumns: 'Coloane',\n  filterPanelInputLabel: 'Valoare',\n  filterPanelInputPlaceholder: 'Filtrare valoare',\n  // Filter operators text\n  filterOperatorContains: 'conține',\n  filterOperatorDoesNotContain: 'nu conține',\n  filterOperatorEquals: 'este egal cu',\n  filterOperatorDoesNotEqual: 'nu este egal cu',\n  filterOperatorStartsWith: 'începe cu',\n  filterOperatorEndsWith: 'se termină cu',\n  filterOperatorIs: 'este',\n  filterOperatorNot: 'nu este',\n  filterOperatorAfter: 'este după',\n  filterOperatorOnOrAfter: 'este la sau după',\n  filterOperatorBefore: 'este înainte de',\n  filterOperatorOnOrBefore: 'este la sau înainte de',\n  filterOperatorIsEmpty: 'este gol',\n  filterOperatorIsNotEmpty: 'nu este gol',\n  filterOperatorIsAnyOf: 'este una din valori',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Conține',\n  headerFilterOperatorDoesNotContain: 'Nu conține',\n  headerFilterOperatorEquals: 'Egal cu',\n  headerFilterOperatorDoesNotEqual: 'Nu este egal cu',\n  headerFilterOperatorStartsWith: 'Începe cu',\n  headerFilterOperatorEndsWith: 'Se termină cu',\n  headerFilterOperatorIs: 'Este',\n  headerFilterOperatorNot: 'Nu este',\n  headerFilterOperatorAfter: 'Este după',\n  headerFilterOperatorOnOrAfter: 'Este la sau după',\n  headerFilterOperatorBefore: 'Este înainte de',\n  headerFilterOperatorOnOrBefore: 'este la sau înainte de',\n  headerFilterOperatorIsEmpty: 'Este gol',\n  headerFilterOperatorIsNotEmpty: 'Nu este gol',\n  headerFilterOperatorIsAnyOf: 'Este una din valori',\n  'headerFilterOperator=': 'Egal cu',\n  'headerFilterOperator!=': 'Nu este egal cu',\n  'headerFilterOperator>': 'Mai mare decât',\n  'headerFilterOperator>=': 'Mai mare sau egal cu',\n  'headerFilterOperator<': 'Mai mic decât',\n  'headerFilterOperator<=': 'Mai mic sau egal cu',\n  // Filter values text\n  filterValueAny: 'Aleatoriu',\n  filterValueTrue: 'Da',\n  filterValueFalse: 'Nu',\n  // Column menu text\n  columnMenuLabel: 'Meniu',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Afișează toate coloanele',\n  columnMenuManageColumns: 'Gestionează coloane',\n  columnMenuFilter: 'Filtru',\n  columnMenuHideColumn: 'Ascunde',\n  columnMenuUnsort: 'Dezactivare sortare',\n  columnMenuSortAsc: 'Sortează crescător',\n  columnMenuSortDesc: 'Sortează descrescător',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} filtru activ` : `${count} filtru activ`,\n  columnHeaderFiltersLabel: 'Afișează filtru',\n  columnHeaderSortIconLabel: 'Sortare',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} Înregistrări selectate` : `${count.toLocaleString()} Înregistrare selectată`,\n  // Total row amount footer text\n  footerTotalRows: 'Total:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} din ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Checkbox Selecție',\n  checkboxSelectionSelectAllRows: 'Selectare toate rândurile',\n  checkboxSelectionUnselectAllRows: 'Deselectare toate rândurile',\n  checkboxSelectionSelectRow: 'Selectare rând',\n  checkboxSelectionUnselectRow: 'Deselectare rând',\n  // Boolean cell text\n  booleanCellTrueLabel: 'Da',\n  booleanCellFalseLabel: 'Nu',\n  // Actions cell more text\n  actionsCellMore: 'Mai multe',\n  // Column pinning text\n  pinToLeft: 'Fixare în stânga',\n  pinToRight: 'Fixare în dreapta',\n  unpin: 'Anulare fixare',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grup',\n  treeDataExpand: 'Afișare copii',\n  treeDataCollapse: 'Ascundere copii',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupare',\n  groupColumn: name => `Grupare după ${name}`,\n  unGroupColumn: name => `Anulare Grupare după ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Comutare panou detalii',\n  expandDetailPanel: 'Extindere',\n  collapseDetailPanel: 'Restrângere',\n  // Row reordering text\n  rowReorderingHeaderName: 'Reordonare rânduri',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agregare',\n  aggregationFunctionLabelSum: 'Sumă',\n  aggregationFunctionLabelAvg: 'Medie',\n  aggregationFunctionLabelMin: 'Minim',\n  aggregationFunctionLabelMax: 'Maxim',\n  aggregationFunctionLabelSize: 'Numărul elementelor'\n};\nexport const roRO = getGridLocalization(roROGrid, roROCore);", "import { ruRU as ruRUCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nfunction getPluralForm(count, options) {\n  const penultimateDigit = Math.floor(count / 10) % 10;\n  const lastDigit = count % 10;\n  let pluralForm = options.many;\n  if (penultimateDigit !== 1 && lastDigit > 1 && lastDigit < 5) {\n    pluralForm = options.few;\n  } else if (penultimateDigit !== 1 && lastDigit === 1) {\n    pluralForm = options.one;\n  }\n  return `${count} ${pluralForm}`;\n}\nconst ruRUGrid = {\n  // Root\n  noRowsLabel: 'Нет строк',\n  noResultsOverlayLabel: 'Данные не найдены.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Высота строки',\n  toolbarDensityLabel: 'Высота строки',\n  toolbarDensityCompact: 'Компактная',\n  toolbarDensityStandard: 'Стандартная',\n  toolbarDensityComfortable: 'Комфортная',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Столбцы',\n  toolbarColumnsLabel: 'Выделите столбцы',\n  // Filters toolbar button text\n  toolbarFilters: 'Фильтры',\n  toolbarFiltersLabel: 'Показать фильтры',\n  toolbarFiltersTooltipHide: 'Скрыть фильтры',\n  toolbarFiltersTooltipShow: 'Показать фильтры',\n  toolbarFiltersTooltipActive: count => getPluralForm(count, {\n    one: 'активный фильтр',\n    few: 'активных фильтра',\n    many: 'активных фильтров'\n  }),\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Поиск…',\n  toolbarQuickFilterLabel: 'Поиск',\n  toolbarQuickFilterDeleteIconLabel: 'Очистить',\n  // Export selector toolbar button text\n  toolbarExport: 'Экспорт',\n  toolbarExportLabel: 'Экспорт',\n  toolbarExportCSV: 'Скачать в формате CSV',\n  toolbarExportPrint: 'Печать',\n  toolbarExportExcel: 'Скачать в формате Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Поиск',\n  columnsManagementNoColumns: 'Нет столбцов',\n  columnsManagementShowHideAllText: 'Показать/Скрыть Всё',\n  columnsManagementReset: 'Сбросить',\n  columnsManagementDeleteIconLabel: 'Очистить',\n  // Filter panel text\n  filterPanelAddFilter: 'Добавить фильтр',\n  filterPanelRemoveAll: 'Очистить фильтр',\n  filterPanelDeleteIconLabel: 'Удалить',\n  filterPanelLogicOperator: 'Логические операторы',\n  filterPanelOperator: 'Операторы',\n  filterPanelOperatorAnd: 'И',\n  filterPanelOperatorOr: 'Или',\n  filterPanelColumns: 'Столбцы',\n  filterPanelInputLabel: 'Значение',\n  filterPanelInputPlaceholder: 'Значение фильтра',\n  // Filter operators text\n  filterOperatorContains: 'содержит',\n  filterOperatorDoesNotContain: 'не содержит',\n  filterOperatorEquals: 'равен',\n  filterOperatorDoesNotEqual: 'не равен',\n  filterOperatorStartsWith: 'начинается с',\n  filterOperatorEndsWith: 'заканчивается на',\n  filterOperatorIs: 'равен',\n  filterOperatorNot: 'не равен',\n  filterOperatorAfter: 'больше чем',\n  filterOperatorOnOrAfter: 'больше или равно',\n  filterOperatorBefore: 'меньше чем',\n  filterOperatorOnOrBefore: 'меньше или равно',\n  filterOperatorIsEmpty: 'пустой',\n  filterOperatorIsNotEmpty: 'не пустой',\n  filterOperatorIsAnyOf: 'любой из',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'содержит',\n  headerFilterOperatorDoesNotContain: 'не содержит',\n  headerFilterOperatorEquals: 'равен',\n  headerFilterOperatorDoesNotEqual: 'не равен',\n  headerFilterOperatorStartsWith: 'начинается с',\n  headerFilterOperatorEndsWith: 'заканчивается на',\n  headerFilterOperatorIs: 'равен',\n  headerFilterOperatorNot: 'не равен',\n  headerFilterOperatorAfter: 'больше чем',\n  headerFilterOperatorOnOrAfter: 'больше или равно',\n  headerFilterOperatorBefore: 'меньше чем',\n  headerFilterOperatorOnOrBefore: 'меньше или равно',\n  headerFilterOperatorIsEmpty: 'пустой',\n  headerFilterOperatorIsNotEmpty: 'не пустой',\n  headerFilterOperatorIsAnyOf: 'любой из',\n  'headerFilterOperator=': 'содержит',\n  'headerFilterOperator!=': 'не содержит',\n  'headerFilterOperator>': 'больше чем',\n  'headerFilterOperator>=': 'больше или равно',\n  'headerFilterOperator<': 'меньше чем',\n  'headerFilterOperator<=': 'меньше или равно',\n  // Filter values text\n  filterValueAny: 'любой',\n  filterValueTrue: 'истина',\n  filterValueFalse: 'ложь',\n  // Column menu text\n  columnMenuLabel: 'Меню',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Показать столбцы',\n  columnMenuManageColumns: 'Управление колонками',\n  columnMenuFilter: 'Фильтр',\n  columnMenuHideColumn: 'Скрыть',\n  columnMenuUnsort: 'Отменить сортировку',\n  columnMenuSortAsc: 'Сортировать по возрастанию',\n  columnMenuSortDesc: 'Сортировать по убыванию',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => getPluralForm(count, {\n    one: 'активный фильтр',\n    few: 'активных фильтра',\n    many: 'активных фильтров'\n  }),\n  columnHeaderFiltersLabel: 'Показать фильтры',\n  columnHeaderSortIconLabel: 'Сортировать',\n  // Rows selected footer text\n  footerRowSelected: count => getPluralForm(count, {\n    one: 'строка выбрана',\n    few: 'строки выбраны',\n    many: 'строк выбрано'\n  }),\n  // Total row amount footer text\n  footerTotalRows: 'Всего строк:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} из ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Выбор флажка',\n  checkboxSelectionSelectAllRows: 'Выбрать все строки',\n  checkboxSelectionUnselectAllRows: 'Отменить выбор всех строк',\n  checkboxSelectionSelectRow: 'Выбрать строку',\n  checkboxSelectionUnselectRow: 'Отменить выбор строки',\n  // Boolean cell text\n  booleanCellTrueLabel: 'истина',\n  booleanCellFalseLabel: 'ложь',\n  // Actions cell more text\n  actionsCellMore: 'ещё',\n  // Column pinning text\n  pinToLeft: 'Закрепить слева',\n  pinToRight: 'Закрепить справа',\n  unpin: 'Открепить',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Группа',\n  treeDataExpand: 'показать дочерние элементы',\n  treeDataCollapse: 'скрыть дочерние элементы',\n  // Grouping columns\n  groupingColumnHeaderName: 'Группа',\n  groupColumn: name => `Сгруппировать по ${name}`,\n  unGroupColumn: name => `Разгруппировать по ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Детали',\n  expandDetailPanel: 'Развернуть',\n  collapseDetailPanel: 'Свернуть',\n  // Row reordering text\n  rowReorderingHeaderName: 'Изменение порядка строк',\n  // Aggregation\n  aggregationMenuItemHeader: 'Объединение данных',\n  aggregationFunctionLabelSum: 'сумм',\n  aggregationFunctionLabelAvg: 'срзнач',\n  aggregationFunctionLabelMin: 'мин',\n  aggregationFunctionLabelMax: 'макс',\n  aggregationFunctionLabelSize: 'счет'\n};\nexport const ruRU = getGridLocalization(ruRUGrid, ruRUCore);", "import { skSK as skSKCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst skSKGrid = {\n  // Root\n  noRowsLabel: 'Žiadne záznamy',\n  noResultsOverlayLabel: 'Nena<PERSON><PERSON> sa žadne výsledky.',\n  // Density selector toolbar button text\n  toolbarDensity: '<PERSON><PERSON><PERSON>',\n  toolbarDensityLabel: '<PERSON><PERSON><PERSON>',\n  toolbarDensityCompact: 'Kompaktná',\n  toolbarDensityStandard: 'Štandartná',\n  toolbarDensityComfortable: 'Komfortná',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Stĺpce',\n  toolbarColumnsLabel: 'Vybrať stĺpce',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtre',\n  toolbarFiltersLabel: 'Zobraziť filtre',\n  toolbarFiltersTooltipHide: 'Skryť filtre ',\n  toolbarFiltersTooltipShow: 'Zobraziť filtre',\n  toolbarFiltersTooltipActive: count => {\n    let pluralForm = 'aktívnych filtrov';\n    if (count > 1 && count < 5) {\n      pluralForm = 'aktívne filtre';\n    } else if (count === 1) {\n      pluralForm = 'aktívny filter';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Vyhľadať…',\n  toolbarQuickFilterLabel: 'Vyhľadať',\n  toolbarQuickFilterDeleteIconLabel: 'Vymazať',\n  // Export selector toolbar button text\n  toolbarExport: 'Export',\n  toolbarExportLabel: 'Export',\n  toolbarExportCSV: 'Stiahnuť ako CSV',\n  toolbarExportPrint: 'Vytlačiť',\n  toolbarExportExcel: 'Stiahnuť ako Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Vyhľadať',\n  columnsManagementNoColumns: 'Žiadne stĺpce',\n  columnsManagementShowHideAllText: 'Zobraziť/Skryť všetko',\n  columnsManagementReset: 'Resetovať',\n  columnsManagementDeleteIconLabel: 'Vymazať',\n  // Filter panel text\n  filterPanelAddFilter: 'Pridať filter',\n  filterPanelRemoveAll: 'Odstrániť všetky',\n  filterPanelDeleteIconLabel: 'Odstrániť',\n  filterPanelLogicOperator: 'Logický operátor',\n  filterPanelOperator: 'Operátory',\n  filterPanelOperatorAnd: 'A',\n  filterPanelOperatorOr: 'Alebo',\n  filterPanelColumns: 'Stĺpce',\n  filterPanelInputLabel: 'Hodnota',\n  filterPanelInputPlaceholder: 'Hodnota filtra',\n  // Filter operators text\n  filterOperatorContains: 'obsahuje',\n  filterOperatorDoesNotContain: 'neobsahuje',\n  filterOperatorEquals: 'rovná sa',\n  filterOperatorDoesNotEqual: 'nerovná sa',\n  filterOperatorStartsWith: 'začína s',\n  filterOperatorEndsWith: 'končí na',\n  filterOperatorIs: 'je',\n  filterOperatorNot: 'nie je',\n  filterOperatorAfter: 'je po',\n  filterOperatorOnOrAfter: 'je na alebo po',\n  filterOperatorBefore: 'je pred',\n  filterOperatorOnOrBefore: 'je na alebo skôr',\n  filterOperatorIsEmpty: 'je prázdny',\n  filterOperatorIsNotEmpty: 'nie je prázdny',\n  filterOperatorIsAnyOf: 'je jeden z',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Obsahuje',\n  headerFilterOperatorDoesNotContain: 'Neobsahuje',\n  headerFilterOperatorEquals: 'Rovná sa',\n  headerFilterOperatorDoesNotEqual: 'Nerovná sa',\n  headerFilterOperatorStartsWith: 'Začína s',\n  headerFilterOperatorEndsWith: 'Končí na',\n  headerFilterOperatorIs: 'Je',\n  headerFilterOperatorNot: 'Nie je',\n  headerFilterOperatorAfter: 'Je po',\n  headerFilterOperatorOnOrAfter: 'Je na alebo po',\n  headerFilterOperatorBefore: 'Je pred',\n  headerFilterOperatorOnOrBefore: 'Je na alebo skôr',\n  headerFilterOperatorIsEmpty: 'Je prázdny',\n  headerFilterOperatorIsNotEmpty: 'Nie je prázdny',\n  headerFilterOperatorIsAnyOf: 'Je jeden z',\n  'headerFilterOperator=': 'Rovná sa',\n  'headerFilterOperator!=': 'Nerovná sa',\n  'headerFilterOperator>': 'Väčší ako',\n  'headerFilterOperator>=': 'Väčší ako alebo rovný',\n  'headerFilterOperator<': 'Menší ako',\n  'headerFilterOperator<=': 'Menší ako alebo rovný',\n  // Filter values text\n  filterValueAny: 'akýkoľvek',\n  filterValueTrue: 'áno',\n  filterValueFalse: 'nie',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  columnMenuAriaLabel: columnName => `Ponuka stĺpca ${columnName}`,\n  columnMenuShowColumns: 'Zobraziť stĺpce',\n  columnMenuManageColumns: 'Spravovať stĺpce',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Skryť',\n  columnMenuUnsort: 'Zrušiť filtre',\n  columnMenuSortAsc: 'Zoradiť vzostupne',\n  columnMenuSortDesc: 'Zoradiť zostupne',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => {\n    let pluralForm = 'aktívnych filtrov';\n    if (count > 1 && count < 5) {\n      pluralForm = 'aktívne filtre';\n    } else if (count === 1) {\n      pluralForm = 'aktívny filter';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  columnHeaderFiltersLabel: 'Zobraziť filtre',\n  columnHeaderSortIconLabel: 'Filtrovať',\n  // Rows selected footer text\n  footerRowSelected: count => {\n    let pluralForm = 'vybraných záznamov';\n    if (count > 1 && count < 5) {\n      pluralForm = 'vybrané záznamy';\n    } else if (count === 1) {\n      pluralForm = 'vybraný záznam';\n    }\n    return `${count} ${pluralForm}`;\n  },\n  // Total row amount footer text\n  footerTotalRows: 'Riadkov spolu:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => {\n    const str = totalCount.toString();\n    const firstDigit = str[0];\n    const op = ['4', '6', '7'].includes(firstDigit) || firstDigit === '1' && str.length % 3 === 0 ? 'zo' : 'z';\n    return `${visibleCount.toLocaleString()} ${op} ${totalCount.toLocaleString()}`;\n  },\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Výber riadku',\n  checkboxSelectionSelectAllRows: 'Vybrať všetky riadky',\n  checkboxSelectionUnselectAllRows: 'Zrušiť výber všetkých riadkov',\n  checkboxSelectionSelectRow: 'Vyber riadok',\n  checkboxSelectionUnselectRow: 'Zruš výber riadku',\n  // Boolean cell text\n  booleanCellTrueLabel: 'áno',\n  booleanCellFalseLabel: 'nie',\n  // Actions cell more text\n  actionsCellMore: 'viac',\n  // Column pinning text\n  pinToLeft: 'Pripnúť na ľavo',\n  pinToRight: 'Pripnúť na pravo',\n  unpin: 'Odopnúť',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Skupina',\n  treeDataExpand: 'zobraziť potomkov',\n  treeDataCollapse: 'skryť potomkov',\n  // Grouping columns\n  groupingColumnHeaderName: 'Skupina',\n  groupColumn: name => `Zoskupiť podľa ${name}`,\n  unGroupColumn: name => `Prestať zoskupovať podľa ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Prepnúť detail panelu',\n  expandDetailPanel: 'Rozbaliť',\n  collapseDetailPanel: 'Zbaliť',\n  // Row reordering text\n  rowReorderingHeaderName: 'Preusporiadávanie riadkov',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agregácia',\n  aggregationFunctionLabelSum: 'suma',\n  aggregationFunctionLabelAvg: 'priemer',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'počet'\n};\nexport const skSK = getGridLocalization(skSKGrid, skSKCore);", "import { svSE as svSECore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst svSEGrid = {\n  // Root\n  noRowsLabel: 'Inga rader',\n  noResultsOverlayLabel: 'Inga resultat funna.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Densitet',\n  toolbarDensityLabel: 'Densitet',\n  toolbarDensityCompact: 'Kompakt',\n  toolbarDensityStandard: 'Standard',\n  toolbarDensityComfortable: 'Luftig',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Kolumner',\n  toolbarColumnsLabel: 'Välj kolumner',\n  // Filters toolbar button text\n  toolbarFilters: 'Filter',\n  toolbarFiltersLabel: 'Visa filter',\n  toolbarFiltersTooltipHide: 'Dölj filter',\n  toolbarFiltersTooltipShow: 'Visa filter',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} aktiva filter` : `${count} aktivt filter`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Sök…',\n  toolbarQuickFilterLabel: 'Sök',\n  toolbarQuickFilterDeleteIconLabel: 'Rensa',\n  // Export selector toolbar button text\n  toolbarExport: 'Exportera',\n  toolbarExportLabel: 'Exportera',\n  toolbarExportCSV: 'Ladda ner som CSV',\n  toolbarExportPrint: 'Skriv ut',\n  toolbarExportExcel: 'Ladda ner som Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Sök',\n  columnsManagementNoColumns: 'Inga kolumner',\n  columnsManagementShowHideAllText: 'Visa/Dölj alla',\n  columnsManagementReset: 'Återställ',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Lägg till filter',\n  filterPanelRemoveAll: 'Ta bort alla',\n  filterPanelDeleteIconLabel: 'Ta bort',\n  filterPanelLogicOperator: 'Logisk operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'Och',\n  filterPanelOperatorOr: 'Eller',\n  filterPanelColumns: 'Kolumner',\n  filterPanelInputLabel: 'Värde',\n  filterPanelInputPlaceholder: 'Filtervärde',\n  // Filter operators text\n  filterOperatorContains: 'innehåller',\n  filterOperatorDoesNotContain: 'innehåller inte',\n  filterOperatorEquals: 'lika med',\n  filterOperatorDoesNotEqual: 'inte lika med',\n  filterOperatorStartsWith: 'börjar med',\n  filterOperatorEndsWith: 'slutar med',\n  filterOperatorIs: 'är',\n  filterOperatorNot: 'är inte',\n  filterOperatorAfter: 'är efter',\n  filterOperatorOnOrAfter: 'är på eller efter',\n  filterOperatorBefore: 'är innan',\n  filterOperatorOnOrBefore: 'är på eller innan',\n  filterOperatorIsEmpty: 'är tom',\n  filterOperatorIsNotEmpty: 'är inte tom',\n  filterOperatorIsAnyOf: 'är någon av',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Innehåller',\n  headerFilterOperatorDoesNotContain: 'Innehåller inte',\n  headerFilterOperatorEquals: 'Lika med',\n  headerFilterOperatorDoesNotEqual: 'Inte lika med',\n  headerFilterOperatorStartsWith: 'Börjar med',\n  headerFilterOperatorEndsWith: 'Slutar med',\n  headerFilterOperatorIs: 'Är',\n  headerFilterOperatorNot: 'Är inte',\n  headerFilterOperatorAfter: 'Är efter',\n  headerFilterOperatorOnOrAfter: 'Är på eller efter',\n  headerFilterOperatorBefore: 'Är innan',\n  headerFilterOperatorOnOrBefore: 'Är på eller innan',\n  headerFilterOperatorIsEmpty: 'Är tom',\n  headerFilterOperatorIsNotEmpty: 'Är inte tom',\n  headerFilterOperatorIsAnyOf: 'Innehåller någon av',\n  'headerFilterOperator=': 'Lika med',\n  'headerFilterOperator!=': 'Inte lika med',\n  'headerFilterOperator>': 'Större än',\n  'headerFilterOperator>=': 'Större eller lika med',\n  'headerFilterOperator<': 'Mindre än',\n  'headerFilterOperator<=': 'Mindre eller lika med',\n  // Filter values text\n  filterValueAny: 'något',\n  filterValueTrue: 'sant',\n  filterValueFalse: 'falskt',\n  // Column menu text\n  columnMenuLabel: 'Meny',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Visa kolumner',\n  columnMenuManageColumns: 'Hantera kolumner',\n  columnMenuFilter: 'Filtrera',\n  columnMenuHideColumn: 'Dölj',\n  columnMenuUnsort: 'Ta bort sortering',\n  columnMenuSortAsc: 'Sortera stigande',\n  columnMenuSortDesc: 'Sortera fallande',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} aktiva filter` : `${count} aktivt filter`,\n  columnHeaderFiltersLabel: 'Visa filter',\n  columnHeaderSortIconLabel: 'Sortera',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} rader markerade` : `${count.toLocaleString()} rad markerad`,\n  // Total row amount footer text\n  footerTotalRows: 'Totalt antal rader:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} av ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Markering med kryssruta',\n  checkboxSelectionSelectAllRows: 'Markera alla rader',\n  checkboxSelectionUnselectAllRows: 'Avmarkera alla rader',\n  checkboxSelectionSelectRow: 'Markera rad',\n  checkboxSelectionUnselectRow: 'Avmarkera rad',\n  // Boolean cell text\n  booleanCellTrueLabel: 'ja',\n  booleanCellFalseLabel: 'nej',\n  // Actions cell more text\n  actionsCellMore: 'mer',\n  // Column pinning text\n  pinToLeft: 'Lås till vänster',\n  pinToRight: 'Lås till höger',\n  unpin: 'Lås upp',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grupp',\n  treeDataExpand: 'visa underordnade',\n  treeDataCollapse: 'dölj underordnade',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupp',\n  groupColumn: name => `Gruppera efter ${name}`,\n  unGroupColumn: name => `Sluta gruppera efter ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Växla detaljpanel',\n  expandDetailPanel: 'Expandera',\n  collapseDetailPanel: 'Kollapsa',\n  // Row reordering text\n  rowReorderingHeaderName: 'Ordna om rader',\n  // Aggregation\n  aggregationMenuItemHeader: 'Aggregering',\n  aggregationFunctionLabelSum: 'summa',\n  aggregationFunctionLabelAvg: 'medel',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'antal'\n};\nexport const svSE = getGridLocalization(svSEGrid, svSECore);", "import { trTR as trTRCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst trTRGrid = {\n  // Root\n  noRowsLabel: 'Satır yok',\n  noResultsOverlayLabel: '<PERSON><PERSON><PERSON> bulunamadı.',\n  // Density selector toolbar button text\n  toolbarDensity: '<PERSON><PERSON><PERSON><PERSON>',\n  toolbarDensityLabel: '<PERSON><PERSON><PERSON><PERSON>',\n  toolbarDensityCompact: 'Sık<PERSON>',\n  toolbarDensityStandard: 'Standart',\n  toolbarDensityComfortable: 'Rahat',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Sütunlar',\n  toolbarColumnsLabel: 'Sütun seç',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtreler',\n  toolbarFiltersLabel: 'Filtreleri göster',\n  toolbarFiltersTooltipHide: 'Filtreleri gizle',\n  toolbarFiltersTooltipShow: 'Filtrel<PERSON> göster',\n  toolbarFiltersTooltipActive: count => `${count} aktif filtre`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Ara…',\n  toolbarQuickFilterLabel: 'Ara',\n  toolbarQuickFilterDeleteIconLabel: 'Temizle',\n  // Export selector toolbar button text\n  toolbarExport: 'Dışa aktar',\n  toolbarExportLabel: 'Dışa aktar',\n  toolbarExportCSV: 'CSV olarak aktar',\n  toolbarExportPrint: 'Yazdır',\n  toolbarExportExcel: 'Excel olarak aktar',\n  // Columns management text\n  columnsManagementSearchTitle: 'Arama',\n  columnsManagementNoColumns: 'Kolon yok',\n  columnsManagementShowHideAllText: 'Hepsini Göster/Gizle',\n  columnsManagementReset: 'Sıfırla',\n  columnsManagementDeleteIconLabel: 'Temizle',\n  // Filter panel text\n  filterPanelAddFilter: 'Filtre Ekle',\n  filterPanelRemoveAll: 'Hepsini kaldır',\n  filterPanelDeleteIconLabel: 'Kaldır',\n  filterPanelLogicOperator: 'Mantıksal operatörler',\n  filterPanelOperator: 'Operatör',\n  filterPanelOperatorAnd: 'Ve',\n  filterPanelOperatorOr: 'Veya',\n  filterPanelColumns: 'Sütunlar',\n  filterPanelInputLabel: 'Değer',\n  filterPanelInputPlaceholder: 'Filtre değeri',\n  // Filter operators text\n  filterOperatorContains: 'içerir',\n  filterOperatorDoesNotContain: 'içermiyor',\n  filterOperatorEquals: 'eşittir',\n  filterOperatorDoesNotEqual: 'eşit değil',\n  filterOperatorStartsWith: 'ile başlar',\n  filterOperatorEndsWith: 'ile biter',\n  filterOperatorIs: 'eşittir',\n  filterOperatorNot: 'eşit değildir',\n  filterOperatorAfter: 'büyük',\n  filterOperatorOnOrAfter: 'büyük eşit',\n  filterOperatorBefore: 'küçük',\n  filterOperatorOnOrBefore: 'küçük eşit',\n  filterOperatorIsEmpty: 'boş',\n  filterOperatorIsNotEmpty: 'dolu',\n  filterOperatorIsAnyOf: 'herhangi biri',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Şunu içerir',\n  headerFilterOperatorDoesNotContain: 'İçermez',\n  headerFilterOperatorEquals: 'Şuna eşittir',\n  headerFilterOperatorDoesNotEqual: 'Eşit değildir',\n  headerFilterOperatorStartsWith: 'Şununla başlar',\n  headerFilterOperatorEndsWith: 'Şununla biter',\n  headerFilterOperatorIs: 'Eşittir',\n  headerFilterOperatorNot: 'Eşit değil',\n  headerFilterOperatorAfter: 'Sonra',\n  headerFilterOperatorOnOrAfter: 'Sonra veya eşit',\n  headerFilterOperatorBefore: 'Önce',\n  headerFilterOperatorOnOrBefore: 'Önce veya eşit',\n  headerFilterOperatorIsEmpty: 'Boş',\n  headerFilterOperatorIsNotEmpty: 'Boş değil',\n  headerFilterOperatorIsAnyOf: 'Herhangi biri',\n  'headerFilterOperator=': 'Eşittir',\n  'headerFilterOperator!=': 'Eşit değil',\n  'headerFilterOperator>': 'Büyüktür',\n  'headerFilterOperator>=': 'Büyük veya eşit',\n  'headerFilterOperator<': 'Küçüktür',\n  'headerFilterOperator<=': 'Küçük veya eşit',\n  // Filter values text\n  filterValueAny: 'herhangi',\n  filterValueTrue: 'doğru',\n  filterValueFalse: 'yanlış',\n  // Column menu text\n  columnMenuLabel: 'Menü',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Sütunları göster',\n  columnMenuManageColumns: 'Sütunları yönet',\n  columnMenuFilter: 'Filtre Ekle',\n  columnMenuHideColumn: 'Gizle',\n  columnMenuUnsort: 'Varsayılan Sıralama',\n  columnMenuSortAsc: 'Sırala - Artan',\n  columnMenuSortDesc: 'Sırala - Azalan',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `${count} filtre aktif`,\n  columnHeaderFiltersLabel: 'Filtreleri göster',\n  columnHeaderSortIconLabel: 'Sırala',\n  // Rows selected footer text\n  footerRowSelected: count => `${count.toLocaleString()} satır seçildi`,\n  // Total row amount footer text\n  footerTotalRows: 'Toplam Satır:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Seçim',\n  checkboxSelectionSelectAllRows: 'Tüm satırları seç',\n  checkboxSelectionUnselectAllRows: 'Tüm satırların seçimini kaldır',\n  checkboxSelectionSelectRow: 'Satırı seç',\n  checkboxSelectionUnselectRow: 'Satır seçimini bırak',\n  // Boolean cell text\n  booleanCellTrueLabel: 'Evet',\n  booleanCellFalseLabel: 'Hayır',\n  // Actions cell more text\n  actionsCellMore: 'daha fazla',\n  // Column pinning text\n  pinToLeft: 'Sola sabitle',\n  pinToRight: 'Sağa sabitle',\n  unpin: 'Sabitlemeyi kaldır',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grup',\n  treeDataExpand: 'göster',\n  treeDataCollapse: 'gizle',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grup',\n  groupColumn: name => `${name} için grupla`,\n  unGroupColumn: name => `${name} için gruplamayı kaldır`,\n  // Master/detail\n  detailPanelToggle: 'Detay görünümüne geçiş',\n  expandDetailPanel: 'Genişlet',\n  collapseDetailPanel: 'Gizle',\n  // Row reordering text\n  rowReorderingHeaderName: 'Satırı yeniden sırala',\n  // Aggregation\n  aggregationMenuItemHeader: 'Toplama',\n  aggregationFunctionLabelSum: 'top',\n  aggregationFunctionLabelAvg: 'ort',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'maks',\n  aggregationFunctionLabelSize: 'boyut'\n};\nexport const trTR = getGridLocalization(trTRGrid, trTRCore);", "import { ukUA as ukUACore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nfunction getPluralForm(count, options) {\n  const penultimateDigit = Math.floor(count / 10) % 10;\n  const lastDigit = count % 10;\n  let pluralForm = options.many;\n  if (penultimateDigit !== 1 && lastDigit > 1 && lastDigit < 5) {\n    pluralForm = options.few;\n  } else if (penultimateDigit !== 1 && lastDigit === 1) {\n    pluralForm = options.one;\n  }\n  return `${count} ${pluralForm}`;\n}\nconst ukUAGrid = {\n  // Root\n  noRowsLabel: 'Немає рядків',\n  noResultsOverlayLabel: 'Дані не знайдено.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Висота рядка',\n  toolbarDensityLabel: 'Висота рядка',\n  toolbarDensityCompact: 'Компактний',\n  toolbarDensityStandard: 'Стандартний',\n  toolbarDensityComfortable: 'Комфортний',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Стовпці',\n  toolbarColumnsLabel: 'Виділіть стовпці',\n  // Filters toolbar button text\n  toolbarFilters: 'Фільтри',\n  toolbarFiltersLabel: 'Показати фільтри',\n  toolbarFiltersTooltipHide: 'Приховати фільтри',\n  toolbarFiltersTooltipShow: 'Показати фільтри',\n  toolbarFiltersTooltipActive: count => getPluralForm(count, {\n    one: 'активний фільтр',\n    few: 'активні фільтри',\n    many: 'активних фільтрів'\n  }),\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Пошук…',\n  toolbarQuickFilterLabel: 'Пошук',\n  toolbarQuickFilterDeleteIconLabel: 'Очистити',\n  // Export selector toolbar button text\n  toolbarExport: 'Експорт',\n  toolbarExportLabel: 'Експорт',\n  toolbarExportCSV: 'Завантажити у форматі CSV',\n  toolbarExportPrint: 'Друк',\n  toolbarExportExcel: 'Завантажити у форматі Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Пошук',\n  columnsManagementNoColumns: 'Немає стовпців',\n  columnsManagementShowHideAllText: 'Показати/Приховати всі',\n  columnsManagementReset: 'Скинути',\n  columnsManagementDeleteIconLabel: 'Очистити',\n  // Filter panel text\n  filterPanelAddFilter: 'Додати фільтр',\n  filterPanelRemoveAll: 'Видалити всі',\n  filterPanelDeleteIconLabel: 'Видалити',\n  filterPanelLogicOperator: 'Логічна функція',\n  filterPanelOperator: 'Оператори',\n  filterPanelOperatorAnd: 'І',\n  filterPanelOperatorOr: 'Або',\n  filterPanelColumns: 'Стовпці',\n  filterPanelInputLabel: 'Значення',\n  filterPanelInputPlaceholder: 'Значення фільтра',\n  // Filter operators text\n  filterOperatorContains: 'містить',\n  filterOperatorDoesNotContain: 'не містить',\n  filterOperatorEquals: 'дорівнює',\n  filterOperatorDoesNotEqual: 'не дорівнює',\n  filterOperatorStartsWith: 'починається з',\n  filterOperatorEndsWith: 'закінчується на',\n  filterOperatorIs: 'дорівнює',\n  filterOperatorNot: 'не дорівнює',\n  filterOperatorAfter: 'більше ніж',\n  filterOperatorOnOrAfter: 'більше або дорівнює',\n  filterOperatorBefore: 'менше ніж',\n  filterOperatorOnOrBefore: 'менше або дорівнює',\n  filterOperatorIsEmpty: 'порожній',\n  filterOperatorIsNotEmpty: 'не порожній',\n  filterOperatorIsAnyOf: 'будь-що із',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Містить',\n  headerFilterOperatorDoesNotContain: 'Не містить',\n  headerFilterOperatorEquals: 'Дорівнює',\n  headerFilterOperatorDoesNotEqual: 'Не дорівнює',\n  headerFilterOperatorStartsWith: 'Починається з',\n  headerFilterOperatorEndsWith: 'Закінчується на',\n  headerFilterOperatorIs: 'Дорівнює',\n  headerFilterOperatorNot: 'Не дорівнює',\n  headerFilterOperatorAfter: 'Після',\n  headerFilterOperatorOnOrAfter: 'Після (включаючи)',\n  headerFilterOperatorBefore: 'Раніше',\n  headerFilterOperatorOnOrBefore: 'Раніше (включаючи)',\n  headerFilterOperatorIsEmpty: 'Порожнє',\n  headerFilterOperatorIsNotEmpty: 'Не порожнє',\n  headerFilterOperatorIsAnyOf: 'Будь-що із',\n  'headerFilterOperator=': 'Дорівнює',\n  'headerFilterOperator!=': 'Не дорівнює',\n  'headerFilterOperator>': 'Більше ніж',\n  'headerFilterOperator>=': 'Більше або дорівнює',\n  'headerFilterOperator<': 'Менше ніж',\n  'headerFilterOperator<=': 'Менше або дорівнює',\n  // Filter values text\n  filterValueAny: 'будь-який',\n  filterValueTrue: 'так',\n  filterValueFalse: 'ні',\n  // Column menu text\n  columnMenuLabel: 'Меню',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Показати стовпці',\n  columnMenuManageColumns: 'Керування стовпцями',\n  columnMenuFilter: 'Фільтр',\n  columnMenuHideColumn: 'Приховати',\n  columnMenuUnsort: 'Скасувати сортування',\n  columnMenuSortAsc: 'Сортувати за зростанням',\n  columnMenuSortDesc: 'Сортувати за спаданням',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => getPluralForm(count, {\n    one: 'активний фільтр',\n    few: 'активні фільтри',\n    many: 'активних фільтрів'\n  }),\n  columnHeaderFiltersLabel: 'Показати фільтри',\n  columnHeaderSortIconLabel: 'Сортувати',\n  // Rows selected footer text\n  footerRowSelected: count => getPluralForm(count, {\n    one: 'вибраний рядок',\n    few: 'вибрані рядки',\n    many: 'вибраних рядків'\n  }),\n  // Total row amount footer text\n  footerTotalRows: 'Усього рядків:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} з ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Вибір прапорця',\n  checkboxSelectionSelectAllRows: 'Вибрати всі рядки',\n  checkboxSelectionUnselectAllRows: 'Скасувати вибір всіх рядків',\n  checkboxSelectionSelectRow: 'Вибрати рядок',\n  checkboxSelectionUnselectRow: 'Скасувати вибір рядка',\n  // Boolean cell text\n  booleanCellTrueLabel: 'так',\n  booleanCellFalseLabel: 'ні',\n  // Actions cell more text\n  actionsCellMore: 'більше',\n  // Column pinning text\n  pinToLeft: 'Закріпити ліворуч',\n  pinToRight: 'Закріпити праворуч',\n  unpin: 'Відкріпити',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Група',\n  treeDataExpand: 'показати дочірні елементи',\n  treeDataCollapse: 'приховати дочірні елементи',\n  // Grouping columns\n  groupingColumnHeaderName: 'Група',\n  groupColumn: name => `Групувати за ${name}`,\n  unGroupColumn: name => `Відмінити групування за ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Перемикач панелі деталей',\n  expandDetailPanel: 'Показати',\n  collapseDetailPanel: 'Приховати',\n  // Row reordering text\n  rowReorderingHeaderName: 'Порядок рядків',\n  // Aggregation\n  aggregationMenuItemHeader: 'Агрегація',\n  aggregationFunctionLabelSum: 'сума',\n  aggregationFunctionLabelAvg: 'сер',\n  aggregationFunctionLabelMin: 'мін',\n  aggregationFunctionLabelMax: 'макс',\n  aggregationFunctionLabelSize: 'кількість'\n};\nexport const ukUA = getGridLocalization(ukUAGrid, ukUACore);", "import { urPKCore } from \"./coreLocales.js\";\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst urPKGrid = {\n  // Root\n  noRowsLabel: 'کوئی قطاریں نہیں',\n  noResultsOverlayLabel: 'کوئی نتائج نہیں',\n  // Density selector toolbar button text\n  toolbarDensity: 'کثافت',\n  toolbarDensityLabel: 'کثافت',\n  toolbarDensityCompact: 'تنگ',\n  toolbarDensityStandard: 'درمیانہ',\n  toolbarDensityComfortable: 'مناسب',\n  // Columns selector toolbar button text\n  toolbarColumns: 'کالمز',\n  toolbarColumnsLabel: 'کالمز کو منتخب کریں',\n  // Filters toolbar button text\n  toolbarFilters: 'فلٹرز',\n  toolbarFiltersLabel: 'فلٹرز دکھائیں',\n  toolbarFiltersTooltipHide: 'فلٹرز چھپائیں',\n  toolbarFiltersTooltipShow: 'فلٹرز دکھائیں',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} فعال فلٹرز` : `${count} فلٹرز فعال`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'تلاش کریں۔۔۔',\n  toolbarQuickFilterLabel: 'تلاش کریں',\n  toolbarQuickFilterDeleteIconLabel: 'کلئیر کریں',\n  // Export selector toolbar button text\n  toolbarExport: 'ایکسپورٹ',\n  toolbarExportLabel: 'ایکسپورٹ',\n  toolbarExportCSV: 'CSV کے طور پر ڈاوٴنلوڈ کریں',\n  toolbarExportPrint: 'پرنٹ کریں',\n  toolbarExportExcel: 'ایکسل کے طور پر ڈاوٴنلوڈ کریں',\n  // Columns management text\n  columnsManagementSearchTitle: 'تلاش',\n  columnsManagementNoColumns: 'کوئی کالم نہیں',\n  columnsManagementShowHideAllText: 'تمام دکھائیں/چھپائیں',\n  columnsManagementReset: 'ریسیٹ',\n  columnsManagementDeleteIconLabel: 'کلئیر',\n  // Filter panel text\n  filterPanelAddFilter: 'نیا فلٹر',\n  filterPanelRemoveAll: 'سارے ختم کریں',\n  filterPanelDeleteIconLabel: 'ختم کریں',\n  filterPanelLogicOperator: 'لاجک آپریٹر',\n  filterPanelOperator: 'آپریٹر',\n  filterPanelOperatorAnd: 'اور',\n  filterPanelOperatorOr: 'یا',\n  filterPanelColumns: 'کالمز',\n  filterPanelInputLabel: 'ویلیو',\n  filterPanelInputPlaceholder: 'ویلیو کو فلٹر کریں',\n  // Filter operators text\n  filterOperatorContains: 'شامل ہے',\n  filterOperatorDoesNotContain: 'موجود نہیں ہے',\n  filterOperatorEquals: 'برابر ہے',\n  filterOperatorDoesNotEqual: 'برابر نہیں ہے',\n  filterOperatorStartsWith: 'شروع ہوتا ہے',\n  filterOperatorEndsWith: 'ختم ہوتا ہے',\n  filterOperatorIs: 'ہے',\n  filterOperatorNot: 'نہیں',\n  filterOperatorAfter: 'بعد میں ہے',\n  filterOperatorOnOrAfter: 'پر یا بعد میں ہے',\n  filterOperatorBefore: 'پہلے ہے',\n  filterOperatorOnOrBefore: 'پر یا پہلے ہے',\n  filterOperatorIsEmpty: 'خالی ہے',\n  filterOperatorIsNotEmpty: 'خالی نہیں ہے',\n  filterOperatorIsAnyOf: 'ان میں سے کوئی ہے',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'شامل ہے',\n  headerFilterOperatorDoesNotContain: 'موجود نہیں ہے',\n  headerFilterOperatorEquals: 'برابر ہے',\n  headerFilterOperatorDoesNotEqual: 'برابر نہیں ہے',\n  headerFilterOperatorStartsWith: 'شروع ہوتا ہے',\n  headerFilterOperatorEndsWith: 'ختم ہوتا ہے',\n  headerFilterOperatorIs: 'ہے',\n  headerFilterOperatorNot: 'نہیں ہے',\n  headerFilterOperatorAfter: 'بعد میں ہے',\n  headerFilterOperatorOnOrAfter: 'پر یا بعد میں ہے',\n  headerFilterOperatorBefore: 'پہلے ہے',\n  headerFilterOperatorOnOrBefore: 'پر یا پہلے ہے',\n  headerFilterOperatorIsEmpty: 'خالی ہے',\n  headerFilterOperatorIsNotEmpty: 'خالی نہیں ہے',\n  headerFilterOperatorIsAnyOf: 'ان میں سے کوئی ہے',\n  'headerFilterOperator=': 'برابر ہے',\n  'headerFilterOperator!=': 'برابر نہیں ہے',\n  'headerFilterOperator>': 'ذیادہ ہے',\n  'headerFilterOperator>=': 'ذیادہ یا برابر ہے',\n  'headerFilterOperator<': 'کم ہے',\n  'headerFilterOperator<=': 'کم یا برابر ہے',\n  // Filter values text\n  filterValueAny: 'کوئی بھی',\n  filterValueTrue: 'صحیح',\n  filterValueFalse: 'غلط',\n  // Column menu text\n  columnMenuLabel: 'مینیو',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'کالم دکھائیں',\n  columnMenuManageColumns: 'کالم مینج کریں',\n  columnMenuFilter: 'فلٹر',\n  columnMenuHideColumn: 'چھپائیں',\n  columnMenuUnsort: 'sort ختم کریں',\n  columnMenuSortAsc: 'ترتیب صعودی',\n  columnMenuSortDesc: 'ترتیب نزولی',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} فعال فلٹرز` : `${count} فلٹرز فعال`,\n  columnHeaderFiltersLabel: 'فلٹرز دکھائیں',\n  columnHeaderSortIconLabel: 'Sort',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} منتخب قطاریں` : `${count.toLocaleString()} منتخب قطار`,\n  // Total row amount footer text\n  footerTotalRows: 'کل قطاریں:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${totalCount.toLocaleString()} میں سے ${visibleCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'چیک باکس منتخب کریں',\n  checkboxSelectionSelectAllRows: 'تمام قطاریں منتخب کریں',\n  checkboxSelectionUnselectAllRows: 'تمام قطاریں نامنتخب کریں ',\n  checkboxSelectionSelectRow: 'قطار منتخب کریں',\n  checkboxSelectionUnselectRow: 'قطار نامنتخب کریں',\n  // Boolean cell text\n  booleanCellTrueLabel: 'ہاں',\n  booleanCellFalseLabel: 'نہیں',\n  // Actions cell more text\n  actionsCellMore: 'ذیادہ',\n  // Column pinning text\n  pinToLeft: 'بائیں جانب pin کریں',\n  pinToRight: 'دائیں جانب pin کریں',\n  unpin: 'pin ختم کریں',\n  // Tree Data\n  treeDataGroupingHeaderName: 'گروپ',\n  treeDataExpand: 'شاخیں دیکھیں',\n  treeDataCollapse: 'شاخیں چھپائیں',\n  // Grouping columns\n  groupingColumnHeaderName: 'گروپ',\n  groupColumn: name => `${name} سے گروپ کریں`,\n  unGroupColumn: name => `${name} سے گروپ ختم کریں`,\n  // Master/detail\n  detailPanelToggle: 'ڈیٹیل پینل کھولیں / بند کریں',\n  expandDetailPanel: 'پھیلائیں',\n  collapseDetailPanel: 'تنگ کریں',\n  // Row reordering text\n  rowReorderingHeaderName: 'قطاروں کی ترتیب تبدیل کریں',\n  // Aggregation\n  aggregationMenuItemHeader: 'ایگریگیشن',\n  aggregationFunctionLabelSum: 'کل',\n  aggregationFunctionLabelAvg: 'اوسط',\n  aggregationFunctionLabelMin: 'کم از کم',\n  aggregationFunctionLabelMax: 'زیادہ سے زیادہ',\n  aggregationFunctionLabelSize: 'سائز'\n};\nexport const urPK = getGridLocalization(urPKGrid, urPKCore);", "import { viVN as viVNCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst viVNGrid = {\n  // Root\n  noRowsLabel: 'Không có dữ liệu',\n  noResultsOverlayLabel: '<PERSON>hông tìm thấy kết quả.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Độ giãn',\n  toolbarDensityLabel: 'Độ giãn',\n  toolbarDensityCompact: 'Trung bình',\n  toolbarDensityStandard: 'Tiêu chuẩn',\n  toolbarDensityComfortable: 'Rộng',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Cột',\n  toolbarColumnsLabel: 'Chọn cột',\n  // Filters toolbar button text\n  toolbarFilters: 'Bộ lọc',\n  toolbarFiltersLabel: '<PERSON><PERSON>n thị bộ lọc',\n  toolbarFiltersTooltipHide: 'Ẩn',\n  toolbarFiltersTooltipShow: 'Hiện',\n  toolbarFiltersTooltipActive: count => count > 1 ? `${count} bộ lọc hoạt động` : `${count} bộ lọc hoạt động`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Tìm kiếm…',\n  toolbarQuickFilterLabel: 'Tìm kiếm',\n  toolbarQuickFilterDeleteIconLabel: 'Xóa tìm kiếm',\n  // Export selector toolbar button text\n  toolbarExport: 'Xuất',\n  toolbarExportLabel: 'Xuất',\n  toolbarExportCSV: 'Xuất CSV',\n  toolbarExportPrint: 'In',\n  toolbarExportExcel: 'Xuất Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Tìm kiếm',\n  columnsManagementNoColumns: 'Không có cột',\n  columnsManagementShowHideAllText: 'Hiện/Ẩn Tất cả',\n  columnsManagementReset: 'Đặt lại',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Thêm bộ lọc',\n  filterPanelRemoveAll: 'Xóa tất cả',\n  filterPanelDeleteIconLabel: 'Xóa',\n  filterPanelLogicOperator: 'Toán tử logic',\n  filterPanelOperator: 'Toán tử',\n  filterPanelOperatorAnd: 'Và',\n  filterPanelOperatorOr: 'Hoặc',\n  filterPanelColumns: 'Cột',\n  filterPanelInputLabel: 'Giá trị',\n  filterPanelInputPlaceholder: 'Lọc giá trị',\n  // Filter operators text\n  filterOperatorContains: 'chứa',\n  filterOperatorDoesNotContain: 'không chứa',\n  filterOperatorEquals: 'bằng',\n  filterOperatorDoesNotEqual: 'không bằng',\n  filterOperatorStartsWith: 'bắt đầu với',\n  filterOperatorEndsWith: 'kết thúc với',\n  filterOperatorIs: 'là',\n  filterOperatorNot: 'không phải là',\n  filterOperatorAfter: 'sau',\n  filterOperatorOnOrAfter: 'bằng hoặc sau',\n  filterOperatorBefore: 'trước',\n  filterOperatorOnOrBefore: 'bằng hoặc trước',\n  filterOperatorIsEmpty: 'rỗng',\n  filterOperatorIsNotEmpty: 'khác rỗng',\n  filterOperatorIsAnyOf: 'là một trong',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Chứa',\n  headerFilterOperatorDoesNotContain: 'Không chứa',\n  headerFilterOperatorEquals: 'Bằng',\n  headerFilterOperatorDoesNotEqual: 'Không bằng',\n  headerFilterOperatorStartsWith: 'Bắt đầu với',\n  headerFilterOperatorEndsWith: 'Kết thúc với',\n  headerFilterOperatorIs: 'Là',\n  headerFilterOperatorNot: 'Không phải là',\n  headerFilterOperatorAfter: 'Sau',\n  headerFilterOperatorOnOrAfter: 'Bằng hoặc sau',\n  headerFilterOperatorBefore: 'Trước',\n  headerFilterOperatorOnOrBefore: 'Bằng hoặc trước',\n  headerFilterOperatorIsEmpty: 'Rỗng',\n  headerFilterOperatorIsNotEmpty: 'Khác rỗng',\n  headerFilterOperatorIsAnyOf: 'Là một trong',\n  'headerFilterOperator=': 'Bằng',\n  'headerFilterOperator!=': 'Khác',\n  'headerFilterOperator>': 'Lớn hơn',\n  'headerFilterOperator>=': 'Lớn hơn hoặc bằng',\n  'headerFilterOperator<': 'Nhỏ hơn',\n  'headerFilterOperator<=': 'Nhỏ hơn hoặc bằng',\n  // Filter values text\n  filterValueAny: 'bất kỳ giá trị nào',\n  filterValueTrue: 'Có',\n  filterValueFalse: 'Không',\n  // Column menu text\n  columnMenuLabel: 'Danh mục',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Danh sách cột',\n  columnMenuManageColumns: 'Quản lý cột',\n  columnMenuFilter: 'Bộ lọc',\n  columnMenuHideColumn: 'Ẩn cột',\n  columnMenuUnsort: 'Bỏ sắp xếp',\n  columnMenuSortAsc: 'Sắp xếp tăng dần',\n  columnMenuSortDesc: 'Sắp xếp giảm dần',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count > 1 ? `${count} bộ lọc hoạt động` : `${count} bộ lọc hoạt động`,\n  columnHeaderFiltersLabel: 'Bộ lọc',\n  columnHeaderSortIconLabel: 'Sắp xếp',\n  // Rows selected footer text\n  footerRowSelected: count => count > 1 ? `${count.toLocaleString()} hàng đã chọn` : `${count.toLocaleString()} hàng đã chọn`,\n  // Total row amount footer text\n  footerTotalRows: 'Tổng:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Tích vào ô trống',\n  checkboxSelectionSelectAllRows: 'Chọn tất cả hàng',\n  checkboxSelectionUnselectAllRows: 'Bỏ chọn tất cả hàng',\n  checkboxSelectionSelectRow: 'Chọn hàng',\n  checkboxSelectionUnselectRow: 'Bỏ chọn hàng',\n  // Boolean cell text\n  booleanCellTrueLabel: 'Có',\n  booleanCellFalseLabel: 'Không',\n  // Actions cell more text\n  actionsCellMore: 'Thêm',\n  // Column pinning text\n  pinToLeft: 'Ghim cột bên trái',\n  pinToRight: 'Ghim cột bên phải',\n  unpin: 'Bỏ ghim',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Nhóm',\n  treeDataExpand: 'mở rộng',\n  treeDataCollapse: 'ẩn đi',\n  // Grouping columns\n  groupingColumnHeaderName: 'Nhóm',\n  groupColumn: name => `Nhóm theo ${name}`,\n  unGroupColumn: name => `Hủy nhóm theo ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Ẩn/hiện chi tiết',\n  expandDetailPanel: 'Mở rộng',\n  collapseDetailPanel: 'Thu nhỏ',\n  // Row reordering text\n  rowReorderingHeaderName: 'Sắp xếp hàng',\n  // Aggregation\n  aggregationMenuItemHeader: 'Tổng hợp',\n  aggregationFunctionLabelSum: 'Tổng',\n  aggregationFunctionLabelAvg: 'Trung bình',\n  aggregationFunctionLabelMin: 'Tối thiểu',\n  aggregationFunctionLabelMax: 'Tối đa',\n  aggregationFunctionLabelSize: 'Kích cỡ'\n};\nexport const viVN = getGridLocalization(viVNGrid, viVNCore);", "import { zhCN as zhCNCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst zhCNGrid = {\n  // Root\n  noRowsLabel: '没有数据。',\n  noResultsOverlayLabel: '未找到数据。',\n  // Density selector toolbar button text\n  toolbarDensity: '表格密度',\n  toolbarDensityLabel: '表格密度',\n  toolbarDensityCompact: '紧密',\n  toolbarDensityStandard: '标准',\n  toolbarDensityComfortable: '稀疏',\n  // Columns selector toolbar button text\n  toolbarColumns: '列',\n  toolbarColumnsLabel: '选择列',\n  // Filters toolbar button text\n  toolbarFilters: '筛选器',\n  toolbarFiltersLabel: '显示筛选器',\n  toolbarFiltersTooltipHide: '隐藏筛选器',\n  toolbarFiltersTooltipShow: '显示筛选器',\n  toolbarFiltersTooltipActive: count => `${count} 个筛选器`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: '搜索…',\n  toolbarQuickFilterLabel: '搜索',\n  toolbarQuickFilterDeleteIconLabel: '清除',\n  // Export selector toolbar button text\n  toolbarExport: '导出',\n  toolbarExportLabel: '导出',\n  toolbarExportCSV: '导出至CSV',\n  toolbarExportPrint: '打印',\n  toolbarExportExcel: '导出至Excel',\n  // Columns management text\n  columnsManagementSearchTitle: '搜索',\n  columnsManagementNoColumns: '没有列',\n  columnsManagementShowHideAllText: '显示/隐藏所有',\n  columnsManagementReset: '重置',\n  columnsManagementDeleteIconLabel: '清除',\n  // Filter panel text\n  filterPanelAddFilter: '添加筛选器',\n  filterPanelRemoveAll: '清除全部',\n  filterPanelDeleteIconLabel: '删除',\n  filterPanelLogicOperator: '逻辑操作器',\n  filterPanelOperator: '操作器',\n  filterPanelOperatorAnd: '与',\n  filterPanelOperatorOr: '或',\n  filterPanelColumns: '列',\n  filterPanelInputLabel: '值',\n  filterPanelInputPlaceholder: '筛选值',\n  // Filter operators text\n  filterOperatorContains: '包含',\n  filterOperatorDoesNotContain: '不包含',\n  filterOperatorEquals: '等于',\n  filterOperatorDoesNotEqual: '不等于',\n  filterOperatorStartsWith: '开始于',\n  filterOperatorEndsWith: '结束于',\n  filterOperatorIs: '是',\n  filterOperatorNot: '不是',\n  filterOperatorAfter: '在后面',\n  filterOperatorOnOrAfter: '正在后面',\n  filterOperatorBefore: '在前面',\n  filterOperatorOnOrBefore: '正在前面',\n  filterOperatorIsEmpty: '为空',\n  filterOperatorIsNotEmpty: '不为空',\n  filterOperatorIsAnyOf: '属于',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: '包含',\n  headerFilterOperatorDoesNotContain: '不包含',\n  headerFilterOperatorEquals: '等于',\n  headerFilterOperatorDoesNotEqual: '不等于',\n  headerFilterOperatorStartsWith: '开始于',\n  headerFilterOperatorEndsWith: '结束于',\n  headerFilterOperatorIs: '是',\n  headerFilterOperatorNot: '不是',\n  headerFilterOperatorAfter: '在后面',\n  headerFilterOperatorOnOrAfter: '在当前或后面',\n  headerFilterOperatorBefore: '在前面',\n  headerFilterOperatorOnOrBefore: '在当前或前面',\n  headerFilterOperatorIsEmpty: '为空',\n  headerFilterOperatorIsNotEmpty: '不为空',\n  headerFilterOperatorIsAnyOf: '属于',\n  'headerFilterOperator=': '等于',\n  'headerFilterOperator!=': '不等于',\n  'headerFilterOperator>': '大于',\n  'headerFilterOperator>=': '大于或等于',\n  'headerFilterOperator<': '小于',\n  'headerFilterOperator<=': '小于或等于',\n  // Filter values text\n  filterValueAny: '任何',\n  filterValueTrue: '真',\n  filterValueFalse: '假',\n  // Column menu text\n  columnMenuLabel: '菜单',\n  columnMenuAriaLabel: columnName => `${columnName} 列菜单`,\n  columnMenuShowColumns: '显示',\n  columnMenuManageColumns: '管理列',\n  columnMenuFilter: '筛选器',\n  columnMenuHideColumn: '隐藏',\n  columnMenuUnsort: '恢复默认',\n  columnMenuSortAsc: '升序',\n  columnMenuSortDesc: '降序',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} 个筛选器` : `${count} 个筛选器`,\n  columnHeaderFiltersLabel: '显示筛选器',\n  columnHeaderSortIconLabel: '排序',\n  // Rows selected footer text\n  footerRowSelected: count => `共选中了${count.toLocaleString()}行`,\n  // Total row amount footer text\n  footerTotalRows: '所有行:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: '多选框',\n  checkboxSelectionSelectAllRows: '全选行',\n  checkboxSelectionUnselectAllRows: '反选所有行',\n  checkboxSelectionSelectRow: '选择行',\n  checkboxSelectionUnselectRow: '反选行',\n  // Boolean cell text\n  booleanCellTrueLabel: '真',\n  booleanCellFalseLabel: '假',\n  // Actions cell more text\n  actionsCellMore: '更多',\n  // Column pinning text\n  pinToLeft: '固定到左侧',\n  pinToRight: '固定到右侧',\n  unpin: '取消固定',\n  // Tree Data\n  treeDataGroupingHeaderName: '组',\n  treeDataExpand: '查看子项目',\n  treeDataCollapse: '隐藏子项目',\n  // Grouping columns\n  groupingColumnHeaderName: '组',\n  groupColumn: name => `用${name}分组`,\n  unGroupColumn: name => `不再用${name}分组`,\n  // Master/detail\n  detailPanelToggle: '详细信息',\n  expandDetailPanel: '显示',\n  collapseDetailPanel: '折叠',\n  // Row reordering text\n  rowReorderingHeaderName: '重新排列行',\n  // Aggregation\n  aggregationMenuItemHeader: '集合',\n  aggregationFunctionLabelSum: '总数',\n  aggregationFunctionLabelAvg: '平均',\n  aggregationFunctionLabelMin: '最小',\n  aggregationFunctionLabelMax: '最大',\n  aggregationFunctionLabelSize: '大小'\n};\nexport const zhCN = getGridLocalization(zhCNGrid, zhCNCore);", "import { zhTW as zhTWCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst zhTWGrid = {\n  // Root\n  noRowsLabel: '沒有資料',\n  noResultsOverlayLabel: '沒有結果',\n  // Density selector toolbar button text\n  toolbarDensity: '表格密度',\n  toolbarDensityLabel: '表格密度',\n  toolbarDensityCompact: '緊湊',\n  toolbarDensityStandard: '標準',\n  toolbarDensityComfortable: '舒適',\n  // Columns selector toolbar button text\n  toolbarColumns: '欄位',\n  toolbarColumnsLabel: '選擇欄位',\n  // Filters toolbar button text\n  toolbarFilters: '篩選器',\n  toolbarFiltersLabel: '顯示篩選器',\n  toolbarFiltersTooltipHide: '隱藏篩選器',\n  toolbarFiltersTooltipShow: '顯示篩選器',\n  toolbarFiltersTooltipActive: count => `${count} 個篩選器`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: '搜尋…',\n  toolbarQuickFilterLabel: '搜尋',\n  toolbarQuickFilterDeleteIconLabel: '清除',\n  // Export selector toolbar button text\n  toolbarExport: '匯出',\n  toolbarExportLabel: '匯出',\n  toolbarExportCSV: '匯出 CSV',\n  toolbarExportPrint: '列印',\n  toolbarExportExcel: '匯出 Excel',\n  // Columns management text\n  columnsManagementSearchTitle: '搜尋',\n  columnsManagementNoColumns: '沒有欄位',\n  columnsManagementShowHideAllText: '顯示/隱藏所有',\n  columnsManagementReset: '重置',\n  columnsManagementDeleteIconLabel: '清除',\n  // Filter panel text\n  filterPanelAddFilter: '增加篩選器',\n  filterPanelRemoveAll: '清除所有',\n  filterPanelDeleteIconLabel: '刪除',\n  filterPanelLogicOperator: '邏輯運算子',\n  filterPanelOperator: '運算子',\n  filterPanelOperatorAnd: '且',\n  filterPanelOperatorOr: '或',\n  filterPanelColumns: '欄位',\n  filterPanelInputLabel: '值',\n  filterPanelInputPlaceholder: '篩選值',\n  // Filter operators text\n  filterOperatorContains: '包含',\n  filterOperatorDoesNotContain: '不包含',\n  filterOperatorEquals: '等於',\n  filterOperatorDoesNotEqual: '不等於',\n  filterOperatorStartsWith: '以...開頭',\n  filterOperatorEndsWith: '以...結束',\n  filterOperatorIs: '為',\n  filterOperatorNot: '不為',\n  filterOperatorAfter: '...之後',\n  filterOperatorOnOrAfter: '...(含)之後',\n  filterOperatorBefore: '...之前',\n  filterOperatorOnOrBefore: '...(含)之前',\n  filterOperatorIsEmpty: '為空',\n  filterOperatorIsNotEmpty: '不為空',\n  filterOperatorIsAnyOf: '是其中之一',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: '包含',\n  headerFilterOperatorDoesNotContain: '不包含',\n  headerFilterOperatorEquals: '等於',\n  headerFilterOperatorDoesNotEqual: '不等於',\n  headerFilterOperatorStartsWith: '以...開頭',\n  headerFilterOperatorEndsWith: '以...結束',\n  headerFilterOperatorIs: '為',\n  headerFilterOperatorNot: '不為',\n  headerFilterOperatorAfter: '...之後',\n  headerFilterOperatorOnOrAfter: '...(含)之後',\n  headerFilterOperatorBefore: '...之前',\n  headerFilterOperatorOnOrBefore: '...(含)之前',\n  headerFilterOperatorIsEmpty: '為空',\n  headerFilterOperatorIsNotEmpty: '不為空',\n  headerFilterOperatorIsAnyOf: '是其中之一',\n  'headerFilterOperator=': '等於',\n  'headerFilterOperator!=': '不等於',\n  'headerFilterOperator>': '大於',\n  'headerFilterOperator>=': '大於或等於',\n  'headerFilterOperator<': '小於',\n  'headerFilterOperator<=': '小於或等於',\n  // Filter values text\n  filterValueAny: '任何值',\n  filterValueTrue: '真',\n  filterValueFalse: '假',\n  // Column menu text\n  columnMenuLabel: '選單',\n  columnMenuAriaLabel: columnName => `${columnName} 欄位選單`,\n  columnMenuShowColumns: '顯示欄位',\n  columnMenuManageColumns: '管理欄位',\n  columnMenuFilter: '篩選器',\n  columnMenuHideColumn: '隱藏',\n  columnMenuUnsort: '預設排序',\n  columnMenuSortAsc: '升序',\n  columnMenuSortDesc: '降序',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => `${count} 個篩選器`,\n  columnHeaderFiltersLabel: '顯示篩選器',\n  columnHeaderSortIconLabel: '排序',\n  // Rows selected footer text\n  footerRowSelected: count => `已選取 ${count.toLocaleString()} 個`,\n  // Total row amount footer text\n  footerTotalRows: '總數:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} / ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: '核取方塊',\n  checkboxSelectionSelectAllRows: '全選',\n  checkboxSelectionUnselectAllRows: '取消全選',\n  checkboxSelectionSelectRow: '選取',\n  checkboxSelectionUnselectRow: '取消選取',\n  // Boolean cell text\n  booleanCellTrueLabel: '真',\n  booleanCellFalseLabel: '假',\n  // Actions cell more text\n  actionsCellMore: '查看更多',\n  // Column pinning text\n  pinToLeft: '釘選在左側',\n  pinToRight: '釘選在右側',\n  unpin: '取消釘選',\n  // Tree Data\n  treeDataGroupingHeaderName: '群組',\n  treeDataExpand: '查看子項目',\n  treeDataCollapse: '隱藏子項目',\n  // Grouping columns\n  groupingColumnHeaderName: '群組',\n  groupColumn: name => `以 ${name} 分組`,\n  unGroupColumn: name => `取消以 ${name} 分組`,\n  // Master/detail\n  detailPanelToggle: '切換顯示詳細資訊',\n  expandDetailPanel: '展開',\n  collapseDetailPanel: '摺疊',\n  // Row reordering text\n  rowReorderingHeaderName: '排序',\n  // Aggregation\n  aggregationMenuItemHeader: '集合',\n  aggregationFunctionLabelSum: '總數',\n  aggregationFunctionLabelAvg: '平均數',\n  aggregationFunctionLabelMin: '最小',\n  aggregationFunctionLabelMax: '最大',\n  aggregationFunctionLabelSize: '尺寸'\n};\nexport const zhTW = getGridLocalization(zhTWGrid, zhTWCore);", "import { hrHR as hrHRCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst hrHRGrid = {\n  // Root\n  noRowsLabel: 'Nema redova',\n  noResultsOverlayLabel: 'Nema rezultata.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Gusto<PERSON><PERSON>',\n  toolbarDensityLabel: 'Gustoća',\n  toolbarDensityCompact: 'Kompaktno',\n  toolbarDensityStandard: 'Standardno',\n  toolbarDensityComfortable: 'Udobno',\n  // Columns selector toolbar button text\n  toolbarColumns: '<PERSON>up<PERSON>',\n  toolbarColumnsLabel: 'Odaberite stupce',\n  // Filters toolbar button text\n  toolbarFilters: 'Filteri',\n  toolbarFiltersLabel: 'Prikaži filtere',\n  toolbarFiltersTooltipHide: 'Sakrij filtere',\n  toolbarFiltersTooltipShow: 'Prikaži filtere',\n  toolbarFiltersTooltipActive: count => {\n    if (count === 1) {\n      return `${count} aktivan filter`;\n    }\n    if (count < 5) {\n      return `${count} aktivna filtera`;\n    }\n    return `${count} aktivnih filtera`;\n  },\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Traži…',\n  toolbarQuickFilterLabel: 'traži',\n  toolbarQuickFilterDeleteIconLabel: 'Obriši',\n  // Export selector toolbar button text\n  toolbarExport: 'Izvoz',\n  toolbarExportLabel: 'Izvoz',\n  toolbarExportCSV: 'Preuzmi kao CSV',\n  toolbarExportPrint: 'Štampaj',\n  toolbarExportExcel: 'Preuzmi kao Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Traži',\n  columnsManagementNoColumns: 'Nema stupaca',\n  columnsManagementShowHideAllText: 'Prikaži/Sakrij sve',\n  columnsManagementReset: 'Ponovno namjesti',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Dodaj filter',\n  filterPanelRemoveAll: 'Ukloni sve',\n  filterPanelDeleteIconLabel: 'Obriši',\n  filterPanelLogicOperator: 'Logički operator',\n  filterPanelOperator: 'Operator',\n  filterPanelOperatorAnd: 'I',\n  filterPanelOperatorOr: 'Ili',\n  filterPanelColumns: 'Stupac',\n  filterPanelInputLabel: 'Vrijednost',\n  filterPanelInputPlaceholder: 'Vrijednost filtera',\n  // Filter operators text\n  filterOperatorContains: 'sadrži',\n  filterOperatorDoesNotContain: 'ne sadrži',\n  filterOperatorEquals: 'je jednak',\n  filterOperatorDoesNotEqual: 'nije jednak',\n  filterOperatorStartsWith: 'počinje sa',\n  filterOperatorEndsWith: 'završava sa',\n  filterOperatorIs: 'je',\n  filterOperatorNot: 'nije',\n  filterOperatorAfter: 'je poslije',\n  filterOperatorOnOrAfter: 'je na ili poslije',\n  filterOperatorBefore: 'je prije',\n  filterOperatorOnOrBefore: 'je na ili prije',\n  filterOperatorIsEmpty: 'je prazno',\n  filterOperatorIsNotEmpty: 'nije prazno',\n  filterOperatorIsAnyOf: 'je bilo koji od',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Sadrži',\n  headerFilterOperatorDoesNotContain: 'Ne sadrži',\n  headerFilterOperatorEquals: 'Jednako',\n  headerFilterOperatorDoesNotEqual: 'Nije jednako',\n  headerFilterOperatorStartsWith: 'Počinje sa',\n  headerFilterOperatorEndsWith: 'Završava sa',\n  headerFilterOperatorIs: 'Je',\n  headerFilterOperatorNot: 'Nije',\n  headerFilterOperatorAfter: 'Je poslije',\n  headerFilterOperatorOnOrAfter: 'Je uključeno ili poslije',\n  headerFilterOperatorBefore: 'Je prije',\n  headerFilterOperatorOnOrBefore: 'Je uključeno ili prije',\n  headerFilterOperatorIsEmpty: 'Je prazno',\n  headerFilterOperatorIsNotEmpty: 'Nije prazno',\n  headerFilterOperatorIsAnyOf: 'Je bilo koji od',\n  'headerFilterOperator=': 'Jednako',\n  'headerFilterOperator!=': 'Nije jednako',\n  'headerFilterOperator>': 'Veći od',\n  'headerFilterOperator>=': 'Veće ili jednako',\n  'headerFilterOperator<': 'Manje od',\n  'headerFilterOperator<=': 'Manje od ili jednako',\n  // Filter values text\n  filterValueAny: 'bilo koji',\n  filterValueTrue: 'tačno',\n  filterValueFalse: 'netačno',\n  // Column menu text\n  columnMenuLabel: 'Izbornik',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Prikaži stupce',\n  columnMenuManageColumns: 'Upravljanje stupcima',\n  columnMenuFilter: 'Filter',\n  columnMenuHideColumn: 'Sakrij stupac',\n  columnMenuUnsort: 'Poništi sortiranje',\n  columnMenuSortAsc: 'Poredaj uzlazno',\n  columnMenuSortDesc: 'Poredaj silazno',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => {\n    if (count === 1) {\n      return `${count} aktivan filter`;\n    }\n    if (count < 5) {\n      return `${count} aktivna filtera`;\n    }\n    return `${count} aktivnih filtera`;\n  },\n  columnHeaderFiltersLabel: 'Prikaži filtere',\n  columnHeaderSortIconLabel: 'Poredaj',\n  // Rows selected footer text\n  footerRowSelected: count => {\n    if (count === 1) {\n      return `Odabran je ${count.toLocaleString()} redak`;\n    }\n    if (count < 5) {\n      return `Odabrana su ${count.toLocaleString()} retka`;\n    }\n    return `Odabrano je ${count.toLocaleString()} redaka`;\n  },\n  // Total row amount footer text\n  footerTotalRows: 'Ukupno redaka:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} od ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Odabir redaka',\n  checkboxSelectionSelectAllRows: 'Odaberite sve retke',\n  checkboxSelectionUnselectAllRows: 'Poništi odabir svih redaka',\n  checkboxSelectionSelectRow: 'Odaberite redak',\n  checkboxSelectionUnselectRow: 'Poništi odabir retka',\n  // Boolean cell text\n  booleanCellTrueLabel: 'Da',\n  booleanCellFalseLabel: 'Ne',\n  // Actions cell more text\n  actionsCellMore: 'više',\n  // Column pinning text\n  pinToLeft: 'Prikvači lijevo',\n  pinToRight: 'Prikvači desno',\n  unpin: 'Otkvači',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Skupina',\n  treeDataExpand: 'vidjeti djecu',\n  treeDataCollapse: 'sakriti djecu',\n  // Grouping columns\n  groupingColumnHeaderName: 'Skupina',\n  groupColumn: name => `Grupiraj prema ${name}`,\n  unGroupColumn: name => `Zaustavi grupiranje prema ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Prebacivanje ploče s detaljima',\n  expandDetailPanel: 'Proširiti',\n  collapseDetailPanel: 'Skupiti',\n  // Row reordering text\n  rowReorderingHeaderName: 'Promjena redoslijeda',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agregacija',\n  aggregationFunctionLabelSum: 'iznos',\n  aggregationFunctionLabelAvg: 'prosj',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'veličina'\n};\nexport const hrHR = getGridLocalization(hrHRGrid, hrHRCore);", "import { ptPT as ptPTCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst ptPTGrid = {\n  // Root\n  noRowsLabel: 'Nenhuma linha',\n  noResultsOverlayLabel: 'Nenhum resultado encontrado.',\n  // Density selector toolbar button text\n  toolbarDensity: 'Densidade',\n  toolbarDensityLabel: 'Densidade',\n  toolbarDensityCompact: 'Compactar',\n  toolbarDensityStandard: 'Padrão',\n  toolbarDensityComfortable: 'Confortável',\n  // Columns selector toolbar button text\n  toolbarColumns: 'Colunas',\n  toolbarColumnsLabel: 'Selecione colunas',\n  // Filters toolbar button text\n  toolbarFilters: 'Filtros',\n  toolbarFiltersLabel: 'Mostrar filtros',\n  toolbarFiltersTooltipHide: 'Ocultar filtros',\n  toolbarFiltersTooltipShow: 'Mostrar filtros',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} filtros ativos` : `${count} filtro ativo`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Procurar…',\n  toolbarQuickFilterLabel: 'Procurar',\n  toolbarQuickFilterDeleteIconLabel: 'Claro',\n  // Export selector toolbar button text\n  toolbarExport: 'Exportar',\n  toolbarExportLabel: 'Exportar',\n  toolbarExportCSV: 'Descarregar como CSV',\n  toolbarExportPrint: 'Imprimir',\n  toolbarExportExcel: 'Descarregar como Excel',\n  // Columns management text\n  columnsManagementSearchTitle: 'Procurar',\n  columnsManagementNoColumns: 'Sem colunas',\n  columnsManagementShowHideAllText: 'Mostrar/Ocultar Todas',\n  columnsManagementReset: 'Repor',\n  columnsManagementDeleteIconLabel: 'Limpar',\n  // Filter panel text\n  filterPanelAddFilter: 'Adicionar filtro',\n  filterPanelRemoveAll: 'Excluir todos',\n  filterPanelDeleteIconLabel: 'Excluir',\n  filterPanelLogicOperator: 'Operador lógico',\n  filterPanelOperator: 'Operador',\n  filterPanelOperatorAnd: 'E',\n  filterPanelOperatorOr: 'Ou',\n  filterPanelColumns: 'Colunas',\n  filterPanelInputLabel: 'Valor',\n  filterPanelInputPlaceholder: 'Valor do filtro',\n  // Filter operators text\n  filterOperatorContains: 'contém',\n  filterOperatorDoesNotContain: 'não contém',\n  filterOperatorEquals: 'é igual a',\n  filterOperatorDoesNotEqual: 'não é igual a',\n  filterOperatorStartsWith: 'começa com',\n  filterOperatorEndsWith: 'termina com',\n  filterOperatorIs: 'é',\n  filterOperatorNot: 'não é',\n  filterOperatorAfter: 'está depois',\n  filterOperatorOnOrAfter: 'está ligado ou depois',\n  filterOperatorBefore: 'é antes',\n  filterOperatorOnOrBefore: 'está ligado ou antes',\n  filterOperatorIsEmpty: 'está vazia',\n  filterOperatorIsNotEmpty: 'não está vazio',\n  filterOperatorIsAnyOf: 'é qualquer um',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Contém',\n  headerFilterOperatorDoesNotContain: 'Não contém',\n  headerFilterOperatorEquals: 'É igual a',\n  headerFilterOperatorDoesNotEqual: 'Não é igual',\n  headerFilterOperatorStartsWith: 'Começa com',\n  headerFilterOperatorEndsWith: 'Termina com',\n  headerFilterOperatorIs: 'É',\n  headerFilterOperatorNot: 'Não é',\n  headerFilterOperatorAfter: 'Está depois',\n  headerFilterOperatorOnOrAfter: 'Está ligado ou depois',\n  headerFilterOperatorBefore: 'É antes',\n  headerFilterOperatorOnOrBefore: 'Está ligado ou antes',\n  headerFilterOperatorIsEmpty: 'Está vazia',\n  headerFilterOperatorIsNotEmpty: 'Não está vazio',\n  headerFilterOperatorIsAnyOf: 'Algum',\n  'headerFilterOperator=': 'É igual a',\n  'headerFilterOperator!=': 'Não é igual',\n  'headerFilterOperator>': 'Maior que',\n  'headerFilterOperator>=': 'Melhor que ou igual a',\n  'headerFilterOperator<': 'Menor que',\n  'headerFilterOperator<=': 'Menos que ou igual a',\n  // Filter values text\n  filterValueAny: 'qualquer',\n  filterValueTrue: 'verdadeiro',\n  filterValueFalse: 'falso',\n  // Column menu text\n  columnMenuLabel: 'Menu',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Mostrar colunas',\n  columnMenuManageColumns: 'Gerir colunas',\n  columnMenuFilter: 'Filtro',\n  columnMenuHideColumn: 'Ocultar coluna',\n  columnMenuUnsort: 'Desclassificar',\n  columnMenuSortAsc: 'Classificar por ordem crescente',\n  columnMenuSortDesc: 'Classificar por ordem decrescente',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} filtros ativos` : `${count} filtro ativo`,\n  columnHeaderFiltersLabel: 'Mostrar filtros',\n  columnHeaderSortIconLabel: 'Organizar',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} linhas selecionadas` : `${count.toLocaleString()} linha selecionada`,\n  // Total row amount footer text\n  footerTotalRows: 'Total de linhas:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} de ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Seleção de caixa de seleção',\n  checkboxSelectionSelectAllRows: 'Selecione todas as linhas',\n  checkboxSelectionUnselectAllRows: 'Desmarque todas as linhas',\n  checkboxSelectionSelectRow: 'Selecione a linha',\n  checkboxSelectionUnselectRow: 'Desmarcar linha',\n  // Boolean cell text\n  booleanCellTrueLabel: 'sim',\n  booleanCellFalseLabel: 'não',\n  // Actions cell more text\n  actionsCellMore: 'mais',\n  // Column pinning text\n  pinToLeft: 'Fixar à esquerda',\n  pinToRight: 'Fixar à direita',\n  unpin: 'Desafixar',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Grupo',\n  treeDataExpand: 'ver crianças',\n  treeDataCollapse: 'esconder crianças',\n  // Grouping columns\n  groupingColumnHeaderName: 'Grupo',\n  groupColumn: name => `Agrupar por ${name}`,\n  unGroupColumn: name => `Pare de agrupar por ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Alternar painel de detalhes',\n  expandDetailPanel: 'Expandir',\n  collapseDetailPanel: 'Colapsar',\n  // Row reordering text\n  rowReorderingHeaderName: 'Reordenação de linhas',\n  // Aggregation\n  aggregationMenuItemHeader: 'Agregação',\n  aggregationFunctionLabelSum: 'soma',\n  aggregationFunctionLabelAvg: 'média',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'máx.',\n  aggregationFunctionLabelSize: 'tamanho'\n};\nexport const ptPT = getGridLocalization(ptPTGrid, ptPTCore);", "import { zhHK as zhHKCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst zhHKGrid = {\n  // Root\n  noRowsLabel: '沒有行',\n  noResultsOverlayLabel: '未找到結果。',\n  // Density selector toolbar button text\n  toolbarDensity: '密度',\n  toolbarDensityLabel: '密度',\n  toolbarDensityCompact: '袖珍的',\n  toolbarDensityStandard: '標準',\n  toolbarDensityComfortable: '舒服的',\n  // Columns selector toolbar button text\n  toolbarColumns: '列',\n  toolbarColumnsLabel: '選擇列',\n  // Filters toolbar button text\n  toolbarFilters: '過濾器',\n  toolbarFiltersLabel: '顯示過濾器',\n  toolbarFiltersTooltipHide: '隱藏過濾器',\n  toolbarFiltersTooltipShow: '顯示過濾器',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} 個有效過濾器` : `${count} 個活動過濾器`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: '搜尋…',\n  toolbarQuickFilterLabel: '搜尋',\n  toolbarQuickFilterDeleteIconLabel: '清除',\n  // Export selector toolbar button text\n  toolbarExport: '出口',\n  toolbarExportLabel: '出口',\n  toolbarExportCSV: '下載為 CSV',\n  toolbarExportPrint: '列印',\n  toolbarExportExcel: '下載為 Excel',\n  // Columns management text\n  columnsManagementSearchTitle: '搜尋',\n  columnsManagementNoColumns: '沒有列',\n  columnsManagementShowHideAllText: '顯示/隱藏所有',\n  columnsManagementReset: '重置',\n  columnsManagementDeleteIconLabel: '清除',\n  // Filter panel text\n  filterPanelAddFilter: '新增過濾器',\n  filterPanelRemoveAll: '移除所有',\n  filterPanelDeleteIconLabel: '刪除',\n  filterPanelLogicOperator: '邏輯運算符',\n  filterPanelOperator: '操作員',\n  filterPanelOperatorAnd: '和',\n  filterPanelOperatorOr: '或者',\n  filterPanelColumns: '列',\n  filterPanelInputLabel: '價值',\n  filterPanelInputPlaceholder: '過濾值',\n  // Filter operators text\n  filterOperatorContains: '包含',\n  filterOperatorDoesNotContain: '不包含',\n  filterOperatorEquals: '等於',\n  filterOperatorDoesNotEqual: '不等於',\n  filterOperatorStartsWith: '以。。開始',\n  filterOperatorEndsWith: '以。。結束',\n  filterOperatorIs: '是',\n  filterOperatorNot: '不是',\n  filterOperatorAfter: '是在之後',\n  filterOperatorOnOrAfter: '是在或之後',\n  filterOperatorBefore: '是在之前',\n  filterOperatorOnOrBefore: '是在或之前',\n  filterOperatorIsEmpty: '是空的',\n  filterOperatorIsNotEmpty: '不為空',\n  filterOperatorIsAnyOf: '是以下任一個',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: '包含',\n  headerFilterOperatorDoesNotContain: '不包含',\n  headerFilterOperatorEquals: '等於',\n  headerFilterOperatorDoesNotEqual: '不等於',\n  headerFilterOperatorStartsWith: '以。。開始',\n  headerFilterOperatorEndsWith: '以。。結束',\n  headerFilterOperatorIs: '是',\n  headerFilterOperatorNot: '不是',\n  headerFilterOperatorAfter: '是在之後',\n  headerFilterOperatorOnOrAfter: '是在或之後',\n  headerFilterOperatorBefore: '是之前',\n  headerFilterOperatorOnOrBefore: '是在或之前',\n  headerFilterOperatorIsEmpty: '是空的',\n  headerFilterOperatorIsNotEmpty: '不為空',\n  headerFilterOperatorIsAnyOf: '是以下任一個',\n  'headerFilterOperator=': '等於',\n  'headerFilterOperator!=': '不等於',\n  'headerFilterOperator>': '大於',\n  'headerFilterOperator>=': '大於或等於',\n  'headerFilterOperator<': '少於',\n  'headerFilterOperator<=': '小於或等於',\n  // Filter values text\n  filterValueAny: '任何',\n  filterValueTrue: '真的',\n  filterValueFalse: '錯誤的',\n  // Column menu text\n  columnMenuLabel: '選單',\n  columnMenuAriaLabel: columnName => `${columnName} 欄目選單`,\n  columnMenuShowColumns: '顯示欄目',\n  columnMenuManageColumns: '管理欄目',\n  columnMenuFilter: '篩選',\n  columnMenuHideColumn: '隱藏列',\n  columnMenuUnsort: '取消排序',\n  columnMenuSortAsc: '按升序排序',\n  columnMenuSortDesc: '按降序排序',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} 個有效過濾器` : `${count} 個活動過濾器`,\n  columnHeaderFiltersLabel: '顯示過濾器',\n  columnHeaderSortIconLabel: '種類',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `已選擇 ${count.toLocaleString()} 行` : `已選擇 ${count.toLocaleString()} 行`,\n  // Total row amount footer text\n  footerTotalRows: '總行數：',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${totalCount.toLocaleString()} 的 ${visibleCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: '複選框選擇',\n  checkboxSelectionSelectAllRows: '選擇所有行',\n  checkboxSelectionUnselectAllRows: '取消選擇所有行',\n  checkboxSelectionSelectRow: '選擇行',\n  checkboxSelectionUnselectRow: '取消選擇行',\n  // Boolean cell text\n  booleanCellTrueLabel: '是的',\n  booleanCellFalseLabel: '不',\n  // Actions cell more text\n  actionsCellMore: '更多的',\n  // Column pinning text\n  pinToLeft: '固定到左側',\n  pinToRight: '固定到右側',\n  unpin: '取消固定',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Group',\n  treeDataExpand: '看看孩子們',\n  treeDataCollapse: '隱藏孩子',\n  // Grouping columns\n  groupingColumnHeaderName: '團體',\n  groupColumn: name => `按 ${name} 分組`,\n  unGroupColumn: name => `停止以 ${name} 分組`,\n  // Master/detail\n  detailPanelToggle: '詳細資訊面板切換',\n  expandDetailPanel: '擴張',\n  collapseDetailPanel: '坍塌',\n  // Row reordering text\n  rowReorderingHeaderName: '行重新排序',\n  // Aggregation\n  aggregationMenuItemHeader: '聚合',\n  aggregationFunctionLabelSum: '和',\n  aggregationFunctionLabelAvg: '平均',\n  aggregationFunctionLabelMin: '分分鐘',\n  aggregationFunctionLabelMax: '最大限度',\n  aggregationFunctionLabelSize: '尺寸'\n};\nexport const zhHK = getGridLocalization(zhHKGrid, zhHKCore);", "import { isIS as isISCore } from '@mui/material/locale';\nimport { getGridLocalization } from \"../utils/getGridLocalization.js\";\nconst isISGrid = {\n  // Root\n  noRowsLabel: 'Engar raðir',\n  noResultsOverlayLabel: 'Engar niðurstöður',\n  // Density selector toolbar button text\n  toolbarDensity: 'Þéttle<PERSON>',\n  toolbarDensityLabel: 'Þéttleiki',\n  toolbarDensityCompact: 'Þétt',\n  toolbarDensityStandard: 'Staðlað',\n  toolbarDensityComfortable: 'Rúmlegt',\n  // Columns selector toolbar button text\n  toolbarColumns: '<PERSON><PERSON><PERSON><PERSON>',\n  toolbarColumnsLabel: 'Veldu dálka',\n  // Filters toolbar button text\n  toolbarFilters: 'Sía',\n  toolbarFiltersLabel: 'Sjá síur',\n  toolbarFiltersTooltipHide: 'Fela síur',\n  toolbarFiltersTooltipShow: 'Sj<PERSON> síur',\n  toolbarFiltersTooltipActive: count => count !== 1 ? `${count} virk sía` : `${count} virkar síur`,\n  // Quick filter toolbar field\n  toolbarQuickFilterPlaceholder: 'Leita…',\n  toolbarQuickFilterLabel: 'Leita',\n  toolbarQuickFilterDeleteIconLabel: 'Eyða',\n  // Export selector toolbar button text\n  toolbarExport: 'Flytja út',\n  toolbarExportLabel: 'Flytja út',\n  toolbarExportCSV: 'Hlaða niður sem CSV',\n  toolbarExportPrint: 'Prenta',\n  toolbarExportExcel: 'Hlaða niður sem Excel',\n  // Columns management text\n  // columnsManagementSearchTitle: 'Search',\n  // columnsManagementNoColumns: 'No columns',\n  // columnsManagementShowHideAllText: 'Show/Hide All',\n  // columnsManagementReset: 'Reset',\n  // columnsManagementDeleteIconLabel: 'Clear',\n\n  // Filter panel text\n  filterPanelAddFilter: 'Bæta síu',\n  filterPanelRemoveAll: 'Fjarlægja alla',\n  filterPanelDeleteIconLabel: 'Eyða',\n  filterPanelLogicOperator: 'Rökvirkir',\n  filterPanelOperator: 'Virkir',\n  filterPanelOperatorAnd: 'Og',\n  filterPanelOperatorOr: 'Eða',\n  filterPanelColumns: 'Dálkar',\n  filterPanelInputLabel: 'Gildi',\n  filterPanelInputPlaceholder: 'Síu gildi',\n  // Filter operators text\n  filterOperatorContains: 'inniheldur',\n  // filterOperatorDoesNotContain: 'does not contain',\n  filterOperatorEquals: 'jafnt og',\n  // filterOperatorDoesNotEqual: 'does not equal',\n  filterOperatorStartsWith: 'byrjar með',\n  filterOperatorEndsWith: 'endar með',\n  filterOperatorIs: 'er líka með',\n  filterOperatorNot: 'er ekki líka með',\n  filterOperatorAfter: 'eftir',\n  filterOperatorOnOrAfter: 'á eða eftir',\n  filterOperatorBefore: 'fyrir',\n  filterOperatorOnOrBefore: 'á eða fyrir',\n  filterOperatorIsEmpty: 'inniheldur ekki gögn',\n  filterOperatorIsNotEmpty: 'inniheldur gögn',\n  filterOperatorIsAnyOf: 'inniheldur einn af',\n  'filterOperator=': '=',\n  'filterOperator!=': '!=',\n  'filterOperator>': '>',\n  'filterOperator>=': '>=',\n  'filterOperator<': '<',\n  'filterOperator<=': '<=',\n  // Header filter operators text\n  headerFilterOperatorContains: 'Inniheldur',\n  // headerFilterOperatorDoesNotContain: 'Does not contain',\n  headerFilterOperatorEquals: 'Jafnt og',\n  // headerFilterOperatorDoesNotEqual: 'Does not equal',\n  headerFilterOperatorStartsWith: 'Byrjar með',\n  headerFilterOperatorEndsWith: 'Endar með',\n  headerFilterOperatorIs: 'Er jafnt og',\n  headerFilterOperatorNot: 'Er ekki jafnt og',\n  headerFilterOperatorAfter: 'Eftir',\n  headerFilterOperatorOnOrAfter: 'Á eða eftir',\n  headerFilterOperatorBefore: 'Fyrir',\n  headerFilterOperatorOnOrBefore: 'Á eða fyrir',\n  headerFilterOperatorIsEmpty: 'Inniheldur ekki gögn',\n  headerFilterOperatorIsNotEmpty: 'Inniheldur gögn',\n  headerFilterOperatorIsAnyOf: 'Inniheldur einn af',\n  'headerFilterOperator=': 'Jafnt og',\n  'headerFilterOperator!=': 'Ekki jafnt og',\n  'headerFilterOperator>': 'Stærra en',\n  'headerFilterOperator>=': 'Stærra en eða jafnt og',\n  'headerFilterOperator<': 'Minna en',\n  'headerFilterOperator<=': 'Minna en eða jafnt og',\n  // Filter values text\n  filterValueAny: 'hvað sem er',\n  filterValueTrue: 'satt',\n  filterValueFalse: 'falskt',\n  // Column menu text\n  columnMenuLabel: 'Valmynd',\n  // columnMenuAriaLabel: (columnName: string) => `${columnName} column menu`,\n  columnMenuShowColumns: 'Sýna dálka',\n  columnMenuManageColumns: 'Stjórna dálkum',\n  columnMenuFilter: 'Síur',\n  columnMenuHideColumn: 'Fela dálka',\n  columnMenuUnsort: 'Fjarlægja röðun',\n  columnMenuSortAsc: 'Raða hækkandi',\n  columnMenuSortDesc: 'Raða lækkandi',\n  // Column header text\n  columnHeaderFiltersTooltipActive: count => count !== 1 ? `${count} virkar síur` : `Ein virk sía`,\n  columnHeaderFiltersLabel: 'Sýna síur',\n  columnHeaderSortIconLabel: 'Raða',\n  // Rows selected footer text\n  footerRowSelected: count => count !== 1 ? `${count.toLocaleString()} raðir valdar` : `Ein röð valin`,\n  // Total row amount footer text\n  footerTotalRows: 'Heildarfjöldi lína:',\n  // Total visible row amount footer text\n  footerTotalVisibleRows: (visibleCount, totalCount) => `${visibleCount.toLocaleString()} af ${totalCount.toLocaleString()}`,\n  // Checkbox selection text\n  checkboxSelectionHeaderName: 'Val á gátreit',\n  checkboxSelectionSelectAllRows: 'Velja allar raðir',\n  checkboxSelectionUnselectAllRows: 'Afvelja allar raðir',\n  checkboxSelectionSelectRow: 'Velja röð',\n  checkboxSelectionUnselectRow: 'Afvelja röð',\n  // Boolean cell text\n  booleanCellTrueLabel: 'já',\n  booleanCellFalseLabel: 'nei',\n  // Actions cell more text\n  actionsCellMore: 'meira',\n  // Column pinning text\n  pinToLeft: 'Festa til vinstri',\n  pinToRight: 'Festa til hægri',\n  unpin: 'Losa um',\n  // Tree Data\n  treeDataGroupingHeaderName: 'Hópur',\n  treeDataExpand: 'Sýna undirliði',\n  treeDataCollapse: 'Fela undirliði',\n  // Grouping columns\n  groupingColumnHeaderName: 'Hópur',\n  groupColumn: name => `Hópa eftir ${name}`,\n  unGroupColumn: name => `Fjarlægja hópun eftir ${name}`,\n  // Master/detail\n  detailPanelToggle: 'Stækka/minnka smáatriðaspjald',\n  expandDetailPanel: 'Stækka',\n  collapseDetailPanel: 'Minnka',\n  // Row reordering text\n  rowReorderingHeaderName: 'Endurröðun raða',\n  // Aggregation\n  aggregationMenuItemHeader: 'Samsafn',\n  aggregationFunctionLabelSum: 'sum',\n  aggregationFunctionLabelAvg: 'avg',\n  aggregationFunctionLabelMin: 'min',\n  aggregationFunctionLabelMax: 'max',\n  aggregationFunctionLabelSize: 'stærð'\n};\nexport const isIS = getGridLocalization(isISGrid, isISCore);"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACO,IAAM,sBAAsB,CAAC,kBAAkB,qBAAkB;AADxE;AAC4E;AAAA,IAC1E,YAAY;AAAA,MACV,aAAa;AAAA,QACX,cAAc;AAAA,UACZ,YAAY,SAAS,CAAC,GAAG,kBAAkB;AAAA,YACzC,sBAAoB,gEAAkB,eAAlB,mBAA8B,uBAA9B,mBAAkD,iBAAgB,CAAC;AAAA,UACzF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;;;ACTA,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,yBAAyB;AAAA;AAAA,EAErF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA;AAAA,EAExB,sBAAsB;AAAA;AAAA,EAEtB,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA;AAAA,EAE9B,4BAA4B;AAAA;AAAA,EAE5B,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,yBAAyB;AAAA,EAC1F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,YAAY,MAAM,eAAe,CAAC,eAAe;AAAA;AAAA,EAE3F,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,aAAa,IAAI;AAAA,EACtC,eAAe,UAAQ,qBAAqB,IAAI;AAAA;AAAA,EAEhD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMA,QAAO,oBAAoB,UAAU,IAAQ;;;ACpJnD,IAAM,WAAW;AAAA,EACtB,YAAY;AAAA,IACV,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,IAAI,IAAI,EAAE,MAAM,UAAU,KAAK,QAAQ,aAAa,EAAE,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAM,WAAW;AAAA,EACtB,YAAY;AAAA,IACV,oBAAoB;AAAA,MAClB,cAAc;AAAA,QACZ,kBAAkB,UAAQ;AACxB,cAAI,SAAS,SAAS;AACpB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AACA,cAAI,SAAS,QAAQ;AACnB,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,oBAAoB,CAAC;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM,GAAG,UAAU,KAAK,GAAG,KAAK,YAAY,GAAG,EAAE,kBAAkB,IAAI,IAAI,OAAO,EAAE;AAAA,MACtF;AAAA,IACF;AAAA,EACF;AACF;;;ACzDA,IAAM,gBAAgB,CAAC,OAAO,YAAY;AACxC,MAAI,aAAa,QAAQ;AACzB,QAAM,YAAY,QAAQ;AAC1B,MAAI,YAAY,KAAK,YAAY,MAAM,QAAQ,MAAM,QAAQ,KAAK;AAChE,iBAAa,QAAQ;AAAA,EACvB,WAAW,cAAc,KAAK,QAAQ,QAAQ,IAAI;AAChD,iBAAa,QAAQ;AAAA,EACvB;AACA,SAAO,GAAG,KAAK,IAAI,UAAU;AAC/B;AACA,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,cAAc,OAAO;AAAA,IACzD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpB,sBAAsB;AAAA;AAAA,EAEtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA;AAAA,EAExB,sBAAsB;AAAA;AAAA,EAEtB,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCvB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,cAAc,OAAO;AAAA,IAC9D,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA,EACD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,cAAc,OAAO;AAAA,IAC/C,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,gBAAgB,IAAI;AAAA,EACzC,eAAe,UAAQ,mBAAmB,IAAI;AAAA;AAAA,EAE9C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAM,OAAO,oBAAoB,UAAU,QAAQ;;;AChL1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK;AAAA;AAAA,EAE9C,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,GAAG,KAAK;AAAA,EACnD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,oBAAoB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAEhH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,eAAe,IAAI;AAAA,EACxC,eAAe,UAAQ,qBAAqB,IAAI;AAAA;AAAA,EAEhD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,wBAAwB,GAAG,KAAK;AAAA;AAAA,EAE5F,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,wBAAwB,GAAG,KAAK;AAAA,EACjG,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,uBAAuB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAEnH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA;AAAA;AAAA;AAAA,EAK5B,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,GAAG,IAAI;AAAA,EAC5B,eAAe,UAAQ,GAAG,IAAI;AAAA;AAAA,EAE9B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS;AACpC,QAAI,aAAa;AACjB,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,mBAAa;AAAA,IACf,WAAW,UAAU,GAAG;AACtB,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA;AAAA,EAEA,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,oBAAoB,UAAU;AAAA,EACjE,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS;AACzC,QAAI,aAAa;AACjB,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,mBAAa;AAAA,IACf,WAAW,UAAU,GAAG;AACtB,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA,EACA,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS;AAC1B,QAAI,aAAa;AACjB,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,mBAAa;AAAA,IACf,WAAW,UAAU,GAAG;AACtB,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe;AACpD,UAAM,MAAM,WAAW,SAAS;AAChC,UAAM,aAAa,IAAI,CAAC;AACxB,UAAM,KAAK,CAAC,KAAK,KAAK,GAAG,EAAE,SAAS,UAAU,KAAK,eAAe,OAAO,IAAI,SAAS,MAAM,IAAI,OAAO;AACvG,WAAO,GAAG,aAAa,eAAe,CAAC,IAAI,EAAE,IAAI,WAAW,eAAe,CAAC;AAAA,EAC9E;AAAA;AAAA,EAEA,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,kBAAkB,IAAI;AAAA,EAC3C,eAAe,UAAQ,4BAA4B,IAAI;AAAA;AAAA,EAEvD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACpL1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB;AAAA,EACpF,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,kBAAkB;AAAA;AAAA,EAErF,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,iBAAiB,IAAI;AAAA,EAC1C,eAAe,UAAQ,0BAA0B,IAAI;AAAA;AAAA,EAErD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA,EAC5F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,yBAAyB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAErH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,QAAQ,WAAW,eAAe,CAAC;AAAA;AAAA,EAEzH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,mBAAmB,IAAI;AAAA,EAC5C,eAAe,UAAQ,oBAAoB,IAAI;AAAA;AAAA,EAE/C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA;AAAA,EAExB,sBAAsB;AAAA;AAAA,EAEtB,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA;AAAA,EAE9B,4BAA4B;AAAA;AAAA,EAE5B,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA,EAC5F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,yBAAyB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAErH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,QAAQ,WAAW,eAAe,CAAC;AAAA;AAAA,EAEzH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,oBAAoB,IAAI;AAAA,EAC7C,eAAe,UAAQ,6BAA6B,IAAI;AAAA;AAAA,EAExD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;AC1JnD,IAAM,2BAA2B;AAAA;AAAA,EAEtC,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA;AAAA,EAExF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,GAAG,UAAU;AAAA,EAChD,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA,EAC7F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,mBAAmB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAE/G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,YAAY,IAAI;AAAA,EACrC,eAAe,UAAQ,oBAAoB,IAAI;AAAA;AAAA,EAE/C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,oBAAoB,CAAC;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;;;ACrJO,IAAMC,QAAO,oBAAoB,0BAA0B,IAAQ;;;ACD1E,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,QAAQ,IAAI,GAAG,KAAK,qBAAqB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,QAAQ,IAAI,GAAG,KAAK,qBAAqB,GAAG,KAAK;AAAA,EAC5F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,QAAQ,IAAI,GAAG,MAAM,eAAe,CAAC,yBAAyB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAEnH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,eAAe,IAAI;AAAA,EACxC,eAAe,UAAQ,kBAAkB,IAAI;AAAA;AAAA,EAE7C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA,EAC7F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,uBAAuB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAEnH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,oBAAoB,IAAI;AAAA,EAC7C,eAAe,UAAQ,wBAAwB,IAAI;AAAA;AAAA,EAEnD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,2BAA2B,GAAG,KAAK;AAAA;AAAA,EAE/F,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA;AAAA,EAExB,sBAAsB;AAAA;AAAA,EAEtB,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA;AAAA,EAE9B,4BAA4B;AAAA;AAAA,EAE5B,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,2BAA2B,GAAG,KAAK;AAAA,EACpG,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,mBAAmB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAE/G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,qBAAqB,IAAI;AAAA,EAC9C,eAAe,UAAQ,4BAA4B,IAAI;AAAA;AAAA,EAEvD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,QAAQ,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA;AAAA,EAEtF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,QAAQ,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA,EAC3F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,QAAQ,IAAI,GAAG,MAAM,eAAe,CAAC,0BAA0B,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAEpH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,QAAQ,WAAW,eAAe,CAAC;AAAA;AAAA,EAEzH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,eAAe,IAAI;AAAA,EACxC,eAAe,UAAQ,0BAA0B,IAAI;AAAA;AAAA,EAErD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB;AAAA;AAAA,EAE/E,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB;AAAA,EACpF,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,iBAAiB;AAAA;AAAA,EAEpF,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,SAAS,WAAW,eAAe,CAAC;AAAA;AAAA,EAE1H,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,WAAW,IAAI;AAAA,EACpC,eAAe,UAAQ,iBAAiB,IAAI;AAAA;AAAA,EAE5C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK;AAAA;AAAA,EAE9C,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,GAAG,KAAK;AAAA,EACnD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAErD,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,eAAe,WAAW,eAAe,CAAC;AAAA;AAAA,EAEhI,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,iBAAiB,IAAI;AAAA,EAC1C,eAAe,UAAQ,GAAG,IAAI;AAAA;AAAA,EAE9B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS;AACpC,QAAI,aAAa;AACjB,QAAI,UAAU,GAAG;AACf,mBAAa;AAAA,IACf,OAAO;AACL,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA;AAAA,EAEA,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,GAAG,UAAU;AAAA,EAChD,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS;AACzC,QAAI,aAAa;AACjB,QAAI,UAAU,GAAG;AACf,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA,EACA,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS;AAC1B,QAAI,aAAa;AACjB,QAAI,UAAU,GAAG;AACf,mBAAa;AAAA,IACf,OAAO;AACL,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe;AACpD,WAAO,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA,EAC1E;AAAA;AAAA,EAEA,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,iBAAiB,IAAI;AAAA,EAC1C,eAAe,UAAQ,kBAAkB,IAAI;AAAA;AAAA,EAE7C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;AC/K1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,QAAQ,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAErF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,QAAQ,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA,EAC1F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,QAAQ,IAAI,GAAG,MAAM,eAAe,CAAC,wBAAwB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAElH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,iBAAiB,IAAI;AAAA,EAC1C,eAAe,UAAQ,8BAA8B,IAAI;AAAA;AAAA,EAEzD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK;AAAA;AAAA,EAE9C,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,GAAG,KAAK;AAAA,EACnD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,GAAG,KAAK;AAAA;AAAA,EAEpC,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,GAAG,IAAI;AAAA,EAC5B,eAAe,UAAQ,GAAG,IAAI;AAAA;AAAA,EAE9B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK;AAAA;AAAA,EAE9C,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,GAAG,KAAK;AAAA,EACnD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,GAAG,KAAK;AAAA;AAAA,EAEpC,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,GAAG,IAAI;AAAA,EAC5B,eAAe,UAAQ,GAAG,IAAI;AAAA;AAAA,EAE9B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA;AAAA,EAExB,sBAAsB;AAAA;AAAA,EAEtB,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA;AAAA,EAE9B,4BAA4B;AAAA;AAAA,EAE5B,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA,EAC5F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,iBAAiB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAE7G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,cAAc,IAAI;AAAA,EACvC,eAAe,UAAQ,sBAAsB,IAAI;AAAA;AAAA,EAEjD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,QAAQ,IAAI,GAAG,KAAK,qBAAqB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,QAAQ,IAAI,GAAG,KAAK,qBAAqB,GAAG,KAAK;AAAA,EAC5F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,QAAQ,IAAI,GAAG,MAAM,eAAe,CAAC,wBAAwB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAElH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,QAAQ,WAAW,eAAe,CAAC;AAAA;AAAA,EAEzH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,eAAe,IAAI;AAAA,EACxC,eAAe,UAAQ,qBAAqB,IAAI;AAAA;AAAA,EAEhD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA;AAAA,EAExB,sBAAsB;AAAA;AAAA,EAEtB,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA;AAAA,EAE9B,4BAA4B;AAAA;AAAA,EAE5B,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA,EAC5F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,gBAAgB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAE5G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,cAAc,IAAI;AAAA,EACvC,eAAe,UAAQ,sBAAsB,IAAI;AAAA;AAAA,EAEjD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,6BAA6B,KAAK;AAAA;AAAA,EAExE,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,iBAAiB,UAAU;AAAA,EAC9D,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,6BAA6B,KAAK;AAAA,EAC7E,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,6BAA6B,MAAM,eAAe,CAAC;AAAA;AAAA,EAE/E,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,iBAAiB,IAAI;AAAA,EAC1C,eAAe,UAAQ,aAAa,IAAI;AAAA;AAAA;AAAA,EAGxC,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,YAAY,QAAQ,IAAI,UAAU,IAAI,WAAW,OAAO;AAAA;AAAA,EAExH,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,GAAG,KAAK,IAAI,UAAU,IAAI,YAAY,QAAQ,IAAI,UAAU,IAAI,WAAW,OAAO;AAAA,EAC7H,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,yBAAyB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAErH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,eAAe,IAAI;AAAA,EACxC,eAAe,UAAQ,yBAAyB,IAAI;AAAA;AAAA,EAEpD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,kBAAkB,GAAG,KAAK;AAAA;AAAA,EAEtF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,kBAAkB,GAAG,KAAK;AAAA,EAC3F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,4BAA4B,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAExH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,QAAQ,WAAW,eAAe,CAAC;AAAA;AAAA,EAEzH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,gBAAgB,IAAI;AAAA,EACzC,eAAe,UAAQ,wBAAwB,IAAI;AAAA;AAAA,EAEnD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,SAASC,eAAc,OAAO,SAAS;AACrC,QAAM,mBAAmB,KAAK,MAAM,QAAQ,EAAE,IAAI;AAClD,QAAM,YAAY,QAAQ;AAC1B,MAAI,aAAa,QAAQ;AACzB,MAAI,qBAAqB,KAAK,YAAY,KAAK,YAAY,GAAG;AAC5D,iBAAa,QAAQ;AAAA,EACvB,WAAW,qBAAqB,KAAK,cAAc,GAAG;AACpD,iBAAa,QAAQ;AAAA,EACvB;AACA,SAAO,GAAG,KAAK,IAAI,UAAU;AAC/B;AACA,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAASA,eAAc,OAAO;AAAA,IACzD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAASA,eAAc,OAAO;AAAA,IAC9D,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA,EACD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAASA,eAAc,OAAO;AAAA,IAC/C,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,oBAAoB,IAAI;AAAA,EAC7C,eAAe,UAAQ,sBAAsB,IAAI;AAAA;AAAA,EAEjD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;AC9K1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS;AACpC,QAAI,aAAa;AACjB,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,mBAAa;AAAA,IACf,WAAW,UAAU,GAAG;AACtB,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA;AAAA,EAEA,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,iBAAiB,UAAU;AAAA,EAC9D,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS;AACzC,QAAI,aAAa;AACjB,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,mBAAa;AAAA,IACf,WAAW,UAAU,GAAG;AACtB,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA,EACA,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS;AAC1B,QAAI,aAAa;AACjB,QAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,mBAAa;AAAA,IACf,WAAW,UAAU,GAAG;AACtB,mBAAa;AAAA,IACf;AACA,WAAO,GAAG,KAAK,IAAI,UAAU;AAAA,EAC/B;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe;AACpD,UAAM,MAAM,WAAW,SAAS;AAChC,UAAM,aAAa,IAAI,CAAC;AACxB,UAAM,KAAK,CAAC,KAAK,KAAK,GAAG,EAAE,SAAS,UAAU,KAAK,eAAe,OAAO,IAAI,SAAS,MAAM,IAAI,OAAO;AACvG,WAAO,GAAG,aAAa,eAAe,CAAC,IAAI,EAAE,IAAI,WAAW,eAAe,CAAC;AAAA,EAC9E;AAAA;AAAA,EAEA,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,kBAAkB,IAAI;AAAA,EAC3C,eAAe,UAAQ,4BAA4B,IAAI;AAAA;AAAA,EAEvD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACpL1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA;AAAA,EAEvF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,mBAAmB,GAAG,KAAK;AAAA,EAC5F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,qBAAqB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAEjH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,kBAAkB,IAAI;AAAA,EAC3C,eAAe,UAAQ,wBAAwB,IAAI;AAAA;AAAA,EAEnD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK;AAAA;AAAA,EAE9C,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,GAAG,KAAK;AAAA,EACnD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAErD,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,GAAG,IAAI;AAAA,EAC5B,eAAe,UAAQ,GAAG,IAAI;AAAA;AAAA,EAE9B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,SAASC,eAAc,OAAO,SAAS;AACrC,QAAM,mBAAmB,KAAK,MAAM,QAAQ,EAAE,IAAI;AAClD,QAAM,YAAY,QAAQ;AAC1B,MAAI,aAAa,QAAQ;AACzB,MAAI,qBAAqB,KAAK,YAAY,KAAK,YAAY,GAAG;AAC5D,iBAAa,QAAQ;AAAA,EACvB,WAAW,qBAAqB,KAAK,cAAc,GAAG;AACpD,iBAAa,QAAQ;AAAA,EACvB;AACA,SAAO,GAAG,KAAK,IAAI,UAAU;AAC/B;AACA,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAASA,eAAc,OAAO;AAAA,IACzD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAASA,eAAc,OAAO;AAAA,IAC9D,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA,EACD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAASA,eAAc,OAAO;AAAA,IAC/C,KAAK;AAAA,IACL,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AAAA;AAAA,EAED,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,gBAAgB,IAAI;AAAA,EACzC,eAAe,UAAQ,2BAA2B,IAAI;AAAA;AAAA,EAEtD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;AC9K1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,gBAAgB,GAAG,KAAK;AAAA;AAAA,EAEpF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,gBAAgB,GAAG,KAAK;AAAA,EACzF,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,kBAAkB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAE9G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,WAAW,eAAe,CAAC,WAAW,aAAa,eAAe,CAAC;AAAA;AAAA,EAE5H,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,GAAG,IAAI;AAAA,EAC5B,eAAe,UAAQ,GAAG,IAAI;AAAA;AAAA,EAE9B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAM,OAAO,oBAAoB,UAAU,QAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,QAAQ,IAAI,GAAG,KAAK,sBAAsB,GAAG,KAAK;AAAA;AAAA,EAExF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,QAAQ,IAAI,GAAG,KAAK,sBAAsB,GAAG,KAAK;AAAA,EAC7F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,QAAQ,IAAI,GAAG,MAAM,eAAe,CAAC,kBAAkB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAE5G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,aAAa,IAAI;AAAA,EACtC,eAAe,UAAQ,iBAAiB,IAAI;AAAA;AAAA,EAE5C,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACxJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK;AAAA;AAAA,EAE9C,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,GAAG,UAAU;AAAA,EAChD,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,UAAU,GAAG,KAAK;AAAA,EACnF,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,OAAO,MAAM,eAAe,CAAC;AAAA;AAAA,EAEzD,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,IAAI,IAAI;AAAA,EAC7B,eAAe,UAAQ,MAAM,IAAI;AAAA;AAAA,EAEjC,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,GAAG,KAAK;AAAA;AAAA,EAE9C,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,GAAG,UAAU;AAAA,EAChD,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,GAAG,KAAK;AAAA,EACnD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,OAAO,MAAM,eAAe,CAAC;AAAA;AAAA,EAEzD,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,MAAM,WAAW,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,KAAK,IAAI;AAAA,EAC9B,eAAe,UAAQ,OAAO,IAAI;AAAA;AAAA,EAElC,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS;AACpC,QAAI,UAAU,GAAG;AACf,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,QAAI,QAAQ,GAAG;AACb,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,WAAO,GAAG,KAAK;AAAA,EACjB;AAAA;AAAA,EAEA,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA;AAAA;AAAA,EAIxB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS;AACzC,QAAI,UAAU,GAAG;AACf,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,QAAI,QAAQ,GAAG;AACb,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,WAAO,GAAG,KAAK;AAAA,EACjB;AAAA,EACA,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS;AAC1B,QAAI,UAAU,GAAG;AACf,aAAO,cAAc,MAAM,eAAe,CAAC;AAAA,IAC7C;AACA,QAAI,QAAQ,GAAG;AACb,aAAO,eAAe,MAAM,eAAe,CAAC;AAAA,IAC9C;AACA,WAAO,eAAe,MAAM,eAAe,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,kBAAkB,IAAI;AAAA,EAC3C,eAAe,UAAQ,6BAA6B,IAAI;AAAA;AAAA,EAExD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;AChL1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA;AAAA,EAExF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,oBAAoB,GAAG,KAAK;AAAA,EAC7F,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,yBAAyB,GAAG,MAAM,eAAe,CAAC;AAAA;AAAA,EAErH,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,eAAe,IAAI;AAAA,EACxC,eAAe,UAAQ,uBAAuB,IAAI;AAAA;AAAA,EAElD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,YAAY,GAAG,KAAK;AAAA;AAAA,EAEhF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,wBAAwB;AAAA,EACxB,kCAAkC;AAAA;AAAA,EAElC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA,EACjB,qBAAqB,gBAAc,GAAG,UAAU;AAAA,EAChD,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,YAAY,GAAG,KAAK;AAAA,EACrF,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,OAAO,MAAM,eAAe,CAAC,OAAO,OAAO,MAAM,eAAe,CAAC;AAAA;AAAA,EAE3G,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,WAAW,eAAe,CAAC,MAAM,aAAa,eAAe,CAAC;AAAA;AAAA,EAEvH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,KAAK,IAAI;AAAA,EAC9B,eAAe,UAAQ,OAAO,IAAI;AAAA;AAAA,EAElC,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;;;ACvJ1D,IAAM,WAAW;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,uBAAuB;AAAA;AAAA,EAEvB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,2BAA2B;AAAA;AAAA,EAE3B,gBAAgB;AAAA,EAChB,qBAAqB;AAAA;AAAA,EAErB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,2BAA2B;AAAA,EAC3B,6BAA6B,WAAS,UAAU,IAAI,GAAG,KAAK,cAAc,GAAG,KAAK;AAAA;AAAA,EAElF,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,mCAAmC;AAAA;AAAA,EAEnC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA;AAAA,EAE7B,wBAAwB;AAAA;AAAA,EAExB,sBAAsB;AAAA;AAAA,EAEtB,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,8BAA8B;AAAA;AAAA,EAE9B,4BAA4B;AAAA;AAAA,EAE5B,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,4BAA4B;AAAA,EAC5B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA;AAAA,EAE1B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA;AAAA,EAElB,iBAAiB;AAAA;AAAA,EAEjB,uBAAuB;AAAA,EACvB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,oBAAoB;AAAA;AAAA,EAEpB,kCAAkC,WAAS,UAAU,IAAI,GAAG,KAAK,iBAAiB;AAAA,EAClF,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA;AAAA,EAE3B,mBAAmB,WAAS,UAAU,IAAI,GAAG,MAAM,eAAe,CAAC,kBAAkB;AAAA;AAAA,EAErF,iBAAiB;AAAA;AAAA,EAEjB,wBAAwB,CAAC,cAAc,eAAe,GAAG,aAAa,eAAe,CAAC,OAAO,WAAW,eAAe,CAAC;AAAA;AAAA,EAExH,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,kCAAkC;AAAA,EAClC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA;AAAA,EAE9B,sBAAsB;AAAA,EACtB,uBAAuB;AAAA;AAAA,EAEvB,iBAAiB;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,OAAO;AAAA;AAAA,EAEP,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA,EAC1B,aAAa,UAAQ,cAAc,IAAI;AAAA,EACvC,eAAe,UAAQ,yBAAyB,IAAI;AAAA;AAAA,EAEpD,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA;AAAA,EAErB,yBAAyB;AAAA;AAAA,EAEzB,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8BAA8B;AAChC;AACO,IAAMC,QAAO,oBAAoB,UAAU,IAAQ;", "names": ["arSD", "bgBG", "bnBD", "csCZ", "daDK", "deDE", "elGR", "enUS", "esES", "faIR", "fiFI", "frFR", "heIL", "huHU", "hyAM", "itIT", "jaJP", "koKR", "nbNO", "nlNL", "nnNO", "plPL", "ptBR", "roRO", "getPluralForm", "ruRU", "skSK", "svSE", "trTR", "getPluralForm", "ukUA", "viVN", "zhCN", "zhTW", "hrHR", "ptPT", "zhHK", "isIS"]}