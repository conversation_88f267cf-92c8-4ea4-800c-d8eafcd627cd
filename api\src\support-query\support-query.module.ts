import { Module } from '@nestjs/common';
import { UserPermissionRepository } from 'src/permission/repositories';
import { AdminApiClient, MSGraphApiClient, NotificationApiClient } from 'src/shared/clients';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedNotificationService, SharedPermissionService } from 'src/shared/services';
import { SupportQueryController } from './controllers';
import { SupportQueryRepository } from './repositories';
import { SupportQueryService } from './services';

const repositories = [SupportQueryRepository, UserPermissionRepository];

@Module({
	controllers: [SupportQueryController],
	providers: [
		...repositories,
		AdminApiClient,
		SharedPermissionService,
		SharedNotificationService,
		NotificationApiClient,
		SupportQueryService,
		DatabaseHelper,
		MSGraphApiClient,
	],
})
export class SupportQueryModule {}
