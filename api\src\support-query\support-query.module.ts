import { Module } from '@nestjs/common';
import { UserPermissionRepository } from 'src/permission/repositories';
import { AdminApiClient } from 'src/shared/clients';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedPermissionService } from 'src/shared/services';
import { SupportQueryController } from './controllers';
import { SupportQueryRepository } from './repositories';
import { SupportQueryService } from './services';

const repositories = [SupportQueryRepository, UserPermissionRepository];

@Module({
	controllers: [SupportQueryController],
	providers: [
		...repositories,
		AdminApiClient,
		SharedPermissionService,
		SupportQueryService,
		DatabaseHelper,
	],
})
export class SupportQueryModule {}
