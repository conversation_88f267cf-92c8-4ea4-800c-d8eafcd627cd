{"version": 3, "sources": ["../../@mui/material/RadioGroup/useRadioGroup.js", "../../@mui/material/RadioGroup/RadioGroupContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport RadioGroupContext from \"./RadioGroupContext.js\";\nexport default function useRadioGroup() {\n  return React.useContext(RadioGroupContext);\n}", "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst RadioGroupContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  RadioGroupContext.displayName = 'RadioGroupContext';\n}\nexport default RadioGroupContext;"], "mappings": ";;;;;;;;AAEA,IAAAA,SAAuB;;;ACAvB,YAAuB;AAIvB,IAAM,oBAAuC,oBAAc,MAAS;AACpE,IAAI,MAAuC;AACzC,oBAAkB,cAAc;AAClC;AACA,IAAO,4BAAQ;;;ADNA,SAAR,gBAAiC;AACtC,SAAa,kBAAW,yBAAiB;AAC3C;", "names": ["React"]}