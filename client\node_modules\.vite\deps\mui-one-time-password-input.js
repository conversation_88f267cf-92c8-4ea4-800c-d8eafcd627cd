import {
  TextField_default
} from "./chunk-PN7XH4TQ.js";
import "./chunk-RNRCLZ6Q.js";
import "./chunk-Z62RDO4U.js";
import "./chunk-Q7PFM2MR.js";
import "./chunk-SJVJRA5O.js";
import "./chunk-U3R3OX25.js";
import "./chunk-PIJVA3N4.js";
import "./chunk-UHF4SIML.js";
import "./chunk-A2T44KXR.js";
import "./chunk-TRN77PDL.js";
import "./chunk-GLCVTW7W.js";
import "./chunk-LRJUQAIR.js";
import {
  Box_default
} from "./chunk-ON7RU6XR.js";
import "./chunk-Y4PULBJI.js";
import "./chunk-FZKRYO5W.js";
import "./chunk-ZOKU5DI5.js";
import "./chunk-AGCLCLMH.js";
import "./chunk-TGJPMUYK.js";
import "./chunk-OJAHRWBG.js";
import "./chunk-3FBLO4ZC.js";
import "./chunk-3HPN5KNE.js";
import "./chunk-PR6ZCO7G.js";
import "./chunk-AKKANAVW.js";
import "./chunk-QDFPR22U.js";
import "./chunk-HKUARNEK.js";
import "./chunk-AACZXOME.js";
import "./chunk-RU4OS4O3.js";
import "./chunk-GEWEBP33.js";
import "./chunk-FAMVC6XK.js";
import "./chunk-HZOIS4LS.js";
import "./chunk-A3Q7B7W4.js";
import "./chunk-4FTWOKSW.js";
import "./chunk-BFL632LT.js";
import "./chunk-ZKV3TKMI.js";
import "./chunk-OLGFUZVB.js";
import "./chunk-MCDEKIA4.js";
import "./chunk-UGHI33QN.js";
import "./chunk-XCKL3XTV.js";
import "./chunk-LCP2UDSO.js";
import "./chunk-SLNW3ETP.js";
import "./chunk-IHGJNEEY.js";
import "./chunk-BTIOHRX6.js";
import "./chunk-HN6H4SXL.js";
import "./chunk-P4UD7LEE.js";
import "./chunk-U27VJAZE.js";
import "./chunk-YXVLDLGC.js";
import "./chunk-5DQNP7OD.js";
import "./chunk-7SK3IGWN.js";
import "./chunk-WEUG6UQ7.js";
import "./chunk-E5UMWPXU.js";
import {
  styled_default
} from "./chunk-HGZMMVE4.js";
import "./chunk-OLICLHZN.js";
import "./chunk-UUHLHOPM.js";
import "./chunk-KU2YNSU4.js";
import "./chunk-UE7CETWW.js";
import "./chunk-NUO2DALJ.js";
import "./chunk-J4LPPHPF.js";
import "./chunk-OPLPMYTC.js";
import "./chunk-X53PWDJZ.js";
import "./chunk-EQCCHGRT.js";
import "./chunk-GS3CDLZ6.js";
import "./chunk-4JLRNKH6.js";
import "./chunk-SRNDHWC2.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/mui-one-time-password-input/dist/mui-one-time-password-input.es.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
var G = styled_default(TextField_default)`
  input {
    text-align: center;
  }
`;
var J = {
  TextFieldStyled: G
};
var Q = (n) => (0, import_jsx_runtime.jsx)(J.TextFieldStyled, { ...n });
var D = {
  left: "ArrowLeft",
  right: "ArrowRight",
  backspace: "Backspace",
  home: "Home",
  end: "End"
};
function U(n, s) {
  return n <= 0 ? [] : Array.from({ length: n }, s);
}
function X(n, s, l) {
  return n.map((i, F) => s === F ? l : i);
}
function P(n) {
  return n.join("");
}
function M(n, s) {
  return [...n, s];
}
function Z(n, s, l) {
  return n.reduce(
    (i, F, C) => {
      const { characters: y, restArrayMerged: d } = i;
      if (C < l)
        return {
          restArrayMerged: d,
          characters: M(y, F)
        };
      const [V, ...E] = d;
      return {
        restArrayMerged: E,
        characters: M(y, V || "")
      };
    },
    {
      restArrayMerged: s,
      characters: []
    }
  ).characters;
}
function v(n) {
  return (s) => {
    n.forEach((l) => {
      typeof l == "function" ? l(s) : l != null && (l.current = s);
    });
  };
}
function ee(n) {
  return n.split("");
}
function N(n) {
  const s = import_react.default.useRef(() => {
    throw new Error("Cannot call an event handler while rendering.");
  });
  return import_react.default.useInsertionEffect(() => {
    s.current = n;
  }), import_react.default.useCallback((...l) => {
    var _a;
    return (_a = s.current) == null ? void 0 : _a.call(s, ...l);
  }, []);
}
var te = () => true;
var le = import_react.default.forwardRef(
  (n, s) => {
    const {
      value: l = "",
      length: i = 4,
      autoFocus: F = false,
      onChange: C,
      TextFieldsProps: y,
      onComplete: d,
      validateChar: V = te,
      className: E,
      onBlur: b,
      ...K
    } = n, j = import_react.default.useRef(l), w = N(d), I = N((e) => {
      const t = e.slice(0, i);
      return {
        isCompleted: t.length === i,
        finalValue: t
      };
    });
    import_react.default.useEffect(() => {
      const { isCompleted: e, finalValue: t } = I(
        j.current
      );
      e && w(t);
    }, [i, w, I]);
    const p = U(
      i,
      (e, t) => ({
        character: l[t] || "",
        inputRef: import_react.default.createRef()
      })
    ), T = (e) => p.findIndex(({ inputRef: t }) => t.current === e), k = () => p.map(({ character: e }) => e), A = (e, t) => {
      const r = X(
        k(),
        e,
        t
      );
      return P(r);
    }, $ = (e) => {
      var _a, _b;
      (_b = (_a = p[e]) == null ? void 0 : _a.inputRef.current) == null ? void 0 : _b.focus();
    }, c = (e) => {
      var _a, _b;
      (_b = (_a = p[e]) == null ? void 0 : _a.inputRef.current) == null ? void 0 : _b.select();
    }, O = (e) => {
      e + 1 !== i && (p[e + 1].character ? c(e + 1) : $(e + 1));
    }, S = (e, t) => typeof V != "function" ? true : V(e, t), Y = (e) => {
      const t = T(e.target);
      if (t === 0 && e.target.value.length > 1) {
        const { finalValue: m, isCompleted: B } = I(
          e.target.value
        );
        C == null ? void 0 : C(m), B && (d == null ? void 0 : d(m)), c(m.length - 1);
        return;
      }
      const r = e.target.value[0] || "";
      let u = r;
      u && !S(u, t) && (u = "");
      const a = A(t, u);
      C == null ? void 0 : C(a);
      const { isCompleted: h, finalValue: f } = I(a);
      h && (d == null ? void 0 : d(f)), u !== "" ? a.length - 1 < t ? c(a.length) : O(t) : r === "" && a.length <= t && c(t - 1);
    }, _ = (e) => {
      const t = e.target, r = t.selectionStart, u = t.selectionEnd, a = T(t), h = r === 0 && u === 0;
      if (t.value === e.key)
        e.preventDefault(), O(a);
      else if (D.backspace === e.key) {
        if (!t.value)
          e.preventDefault(), c(a - 1);
        else if (h) {
          e.preventDefault();
          const f = A(a, "");
          C == null ? void 0 : C(f), f.length <= a && c(a - 1);
        }
      } else D.left === e.key ? (e.preventDefault(), c(a - 1)) : D.right === e.key ? (e.preventDefault(), c(a + 1)) : D.home === e.key ? (e.preventDefault(), c(0)) : D.end === e.key && (e.preventDefault(), c(p.length - 1));
    }, H = (e) => {
      const t = e.clipboardData.getData("text/plain"), r = e.target, u = p.findIndex(
        ({ character: x, inputRef: o }) => x === "" || o.current === r
      ), a = k(), h = Z(
        a,
        ee(t),
        u
      ).map((x, o) => S(x, o) ? x : ""), f = P(h);
      C == null ? void 0 : C(f);
      const { isCompleted: m, finalValue: B } = I(f);
      m ? (d == null ? void 0 : d(B), c(i - 1)) : c(f.length);
    }, L = (e) => {
      if (!p.some(({ inputRef: r }) => r.current === e.relatedTarget)) {
        const { isCompleted: r, finalValue: u } = I(l);
        b == null ? void 0 : b(u, r);
      }
    };
    return (0, import_jsx_runtime.jsx)(
      Box_default,
      {
        display: "flex",
        gap: "20px",
        alignItems: "center",
        ref: s,
        className: `MuiOtpInput-Box ${E || ""}`,
        ...K,
        children: p.map(({ character: e, inputRef: t }, r) => {
          const {
            onPaste: u,
            onFocus: a,
            onKeyDown: h,
            className: f,
            onBlur: m,
            inputRef: B,
            ...x
          } = typeof y == "function" ? y(r) || {} : y || {};
          return (0, import_jsx_runtime.jsx)(
            Q,
            {
              autoFocus: F ? r === 0 : false,
              autoComplete: "one-time-code",
              value: e,
              inputRef: v([t, B]),
              className: `MuiOtpInput-TextField MuiOtpInput-TextField-${r + 1} ${f || ""}`,
              onPaste: (o) => {
                o.preventDefault(), H(o), u == null ? void 0 : u(o);
              },
              onFocus: (o) => {
                o.preventDefault(), o.target.select(), a == null ? void 0 : a(o);
              },
              onChange: Y,
              onKeyDown: (o) => {
                _(o), h == null ? void 0 : h(o);
              },
              onBlur: (o) => {
                m == null ? void 0 : m(o), L(o);
              },
              ...x
            },
            r
          );
        })
      }
    );
  }
);
export {
  le as MuiOtpInput
};
//# sourceMappingURL=mui-one-time-password-input.js.map
