{"version": 3, "sources": ["../../@mui/material/Table/TableContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst TableContext = /*#__PURE__*/React.createContext();\nif (process.env.NODE_ENV !== 'production') {\n  TableContext.displayName = 'TableContext';\n}\nexport default TableContext;"], "mappings": ";;;;;;;;AAEA,YAAuB;AAKvB,IAAM,eAAkC,oBAAc;AACtD,IAAI,MAAuC;AACzC,eAAa,cAAc;AAC7B;AACA,IAAO,uBAAQ;", "names": []}