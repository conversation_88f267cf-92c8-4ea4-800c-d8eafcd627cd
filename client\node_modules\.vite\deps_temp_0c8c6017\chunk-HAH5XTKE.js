import {
  inputBaseClasses_default
} from "./chunk-J565RMYM.js";
import {
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-UE7CETWW.js";

// node_modules/@mui/material/FilledInput/filledInputClasses.js
function getFilledInputUtilityClass(slot) {
  return generateUtilityClass("MuiFilledInput", slot);
}
var filledInputClasses = {
  ...inputBaseClasses_default,
  ...generateUtilityClasses("MuiFilledInput", ["root", "underline", "input", "adornedStart", "adornedEnd", "sizeSmall", "multiline", "hiddenLabel"])
};
var filledInputClasses_default = filledInputClasses;

export {
  getFilledInputUtilityClass,
  filledInputClasses_default
};
//# sourceMappingURL=chunk-HAH5XTKE.js.map
