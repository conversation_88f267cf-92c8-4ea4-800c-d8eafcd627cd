import { LocationDetailContext, UserPermissionsContext } from '@/core/contexts';
import { Box } from '@mui/system';
import React, { useContext } from 'react';
import { Navigate, useNavigate } from 'react-router';
import { paths, ReplaceUrlVariable } from '@/routes/paths';
import LocationSetup from '../components/location-setup/location-setup';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { checkAnyPermission } from '@/shared/utils';
import { toNumber } from 'lodash';
import { HIERARCHY_ENTITY_TYPE } from '@/shared/enum';

export default function LocationSetupView() {
  const currentlySelected = React.useContext(LocationDetailContext);
  console.log('🚀 ~ LocationSetupView ~ currentlySelected:', currentlySelected);
  const navigate = useNavigate();
  const userPermissions = useContext(UserPermissionsContext);
  const hasPermission = checkAnyPermission(
    [PERMISSIONS.GLOBAL_MANAGE, PERMISSIONS.GLOBAL_LEGAL],
    userPermissions,
    toNumber(currentlySelected.id),
  );
  if (
    !hasPermission &&
    (currentlySelected.entityType == HIERARCHY_ENTITY_TYPE.AREA ||
      currentlySelected.entityType == HIERARCHY_ENTITY_TYPE.COUNTRY)
  ) {
    return <Navigate to={paths.root} />;
  }
  return (
    <Box>
      <LocationSetup
        onSuccess={(id?: string) => {
          navigate(
            ReplaceUrlVariable(paths.locationSetup.root + paths.locationSetup.locationDetails, {
              id: id,
            }),
          );
        }}
        toggleCountryEdit={() => {
          navigate(
            ReplaceUrlVariable(paths.locationSetup.root + paths.locationSetup.countryEdit, {
              id: currentlySelected.parentId,
            }),
          );
        }}
      />
    </Box>
  );
}
