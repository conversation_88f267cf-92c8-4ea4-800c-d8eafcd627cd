{"version": 3, "file": "attachment.dto.js", "sourceRoot": "", "sources": ["../../../src/shared/dtos/attachment.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAA4E;AAE5E,MAAa,aAAa;CAqGzB;AApGC;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC/B,IAAA,0BAAQ,GAAE;;kDACS;AAEpB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC/B,IAAA,0BAAQ,GAAE;;sDACa;AAExB;IAAC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;2CACC;AAEd;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAChD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACe;AAE1B;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;;2CACF;AAEX;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC/B,IAAA,0BAAQ,GAAE;;8DACqB;AAEhC;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACM;AAEjB;IAAC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC/B,IAAA,2BAAS,GAAE;IACX,IAAA,4BAAU,GAAE;;gDACO;AAEpB;IAAC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC/B,IAAA,2BAAS,GAAE;IACX,IAAA,4BAAU,GAAE;;gDACO;AAEpB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yCACC;AAEZ;IAAC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC/B,IAAA,2BAAS,GAAE;IACX,IAAA,4BAAU,GAAE;;4CACG;AAEhB;IAAC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC/B,IAAA,2BAAS,GAAE;IACX,IAAA,4BAAU,GAAE;;+CACM;AAEnB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0CACE;AAEb;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2CACG;AAEd;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACU;AAErB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACe;AAE1B;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACc;AAEzB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACM;AAEjB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACS;AAEpB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACS;AAEpB;IAAC,IAAA,qBAAW,EAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAChC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACO;AApGpB,sCAqGC"}