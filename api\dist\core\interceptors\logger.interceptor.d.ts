import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { LoggerService } from '../services';
export declare class LoggingInterceptor implements NestInterceptor {
    private readonly logger;
    constructor(logger: LoggerService);
    private readonly HEALTH_CHECK_URL;
    private now;
    intercept(context: ExecutionContext, call: CallHandler): Observable<unknown>;
    private logNext;
    private logError;
    private getLogObject;
    private getIpAddress;
    private getPathFromUrl;
}
