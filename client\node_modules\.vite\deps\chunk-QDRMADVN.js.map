{"version": 3, "sources": ["../../@mui/x-tree-view/internals/TreeViewProvider/TreeViewProvider.js", "../../@mui/x-tree-view/internals/TreeViewProvider/TreeViewContext.js", "../../@mui/x-tree-view/internals/TreeViewProvider/useTreeViewContext.js", "../../@mui/x-tree-view/internals/utils/plugins.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewLabel/useTreeViewLabel.js", "../../@mui/x-tree-view/internals/plugins/useTreeViewLabel/useTreeViewLabel.itemPlugin.js"], "sourcesContent": ["import * as React from 'react';\nimport { TreeViewContext } from \"./TreeViewContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Sets up the contexts for the underlying TreeItem components.\n *\n * @ignore - do not document.\n */\nexport function TreeViewProvider(props) {\n  const {\n    value,\n    children\n  } = props;\n  return /*#__PURE__*/_jsx(TreeViewContext.Provider, {\n    value: value,\n    children: value.wrapRoot({\n      children,\n      instance: value.instance\n    })\n  });\n}", "import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nexport const TreeViewContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  TreeViewContext.displayName = 'TreeViewContext';\n}", "import * as React from 'react';\nimport { TreeViewContext } from \"./TreeViewContext.js\";\nexport const useTreeViewContext = () => {\n  const context = React.useContext(TreeViewContext);\n  if (context == null) {\n    throw new Error(['MUI X: Could not find the Tree View context.', 'It looks like you rendered your component outside of a SimpleTreeView or RichTreeView parent component.', 'This can also happen if you are bundling multiple versions of the Tree View.'].join('\\n'));\n  }\n  return context;\n};", "export const hasPlugin = (instance, plugin) => {\n  const plugins = instance.getAvailablePlugins();\n  return plugins.has(plugin);\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useTreeViewLabelItemPlugin } from \"./useTreeViewLabel.itemPlugin.js\";\nexport const useTreeViewLabel = ({\n  instance,\n  state,\n  setState,\n  params\n}) => {\n  const editedItemRef = React.useRef(state.editedItemId);\n  const isItemBeingEditedRef = itemId => editedItemRef.current === itemId;\n  const setEditedItemId = editedItemId => {\n    setState(prevState => _extends({}, prevState, {\n      editedItemId\n    }));\n    editedItemRef.current = editedItemId;\n  };\n  const isItemBeingEdited = itemId => itemId === state.editedItemId;\n  const isTreeViewEditable = Boolean(params.isItemEditable);\n  const isItemEditable = itemId => {\n    if (itemId == null || !isTreeViewEditable) {\n      return false;\n    }\n    const item = instance.getItem(itemId);\n    if (!item) {\n      return false;\n    }\n    return typeof params.isItemEditable === 'function' ? params.isItemEditable(item) : Boolean(params.isItemEditable);\n  };\n  const updateItemLabel = (itemId, label) => {\n    if (!label) {\n      throw new Error(['MUI X: The Tree View component requires all items to have a `label` property.', 'The label of an item cannot be empty.', itemId].join('\\n'));\n    }\n    setState(prevState => {\n      const item = prevState.items.itemMetaMap[itemId];\n      if (item.label !== label) {\n        return _extends({}, prevState, {\n          items: _extends({}, prevState.items, {\n            itemMetaMap: _extends({}, prevState.items.itemMetaMap, {\n              [itemId]: _extends({}, item, {\n                label\n              })\n            })\n          })\n        });\n      }\n      return prevState;\n    });\n    if (params.onItemLabelChange) {\n      params.onItemLabelChange(itemId, label);\n    }\n  };\n  return {\n    instance: {\n      setEditedItemId,\n      isItemBeingEdited,\n      updateItemLabel,\n      isItemEditable,\n      isTreeViewEditable,\n      isItemBeingEditedRef\n    },\n    publicAPI: {\n      updateItemLabel\n    }\n  };\n};\nuseTreeViewLabel.itemPlugin = useTreeViewLabelItemPlugin;\nuseTreeViewLabel.getDefaultizedParams = ({\n  params,\n  experimentalFeatures\n}) => {\n  const canUseFeature = experimentalFeatures?.labelEditing;\n  if (process.env.NODE_ENV !== 'production') {\n    if (params.isItemEditable && !canUseFeature) {\n      warnOnce(['MUI X: The label editing feature requires the `labelEditing` experimental feature to be enabled.', 'You can do it by passing `experimentalFeatures={{ labelEditing: true}}` to the Rich Tree View Pro component.', 'Check the documentation for more details: https://mui.com/x/react-tree-view/rich-tree-view/editing/']);\n    }\n  }\n  return _extends({}, params, {\n    isItemEditable: canUseFeature ? params.isItemEditable ?? false : false\n  });\n};\nuseTreeViewLabel.getInitialState = () => ({\n  editedItemId: null\n});\nuseTreeViewLabel.params = {\n  onItemLabelChange: true,\n  isItemEditable: true\n};", "import * as React from 'react';\nimport { useTreeViewContext } from \"../../TreeViewProvider/index.js\";\nexport const useTreeViewLabelItemPlugin = ({\n  props\n}) => {\n  const {\n    instance\n  } = useTreeViewContext();\n  const {\n    label,\n    itemId\n  } = props;\n  const [labelInputValue, setLabelInputValue] = React.useState(label);\n  const isItemBeingEdited = instance.isItemBeingEdited(itemId);\n  React.useEffect(() => {\n    if (!isItemBeingEdited) {\n      setLabelInputValue(label);\n    }\n  }, [isItemBeingEdited, label]);\n  return {\n    propsEnhancers: {\n      labelInput: ({\n        externalEventHandlers,\n        interactions\n      }) => {\n        const editable = instance.isItemEditable(itemId);\n        if (!editable) {\n          return {};\n        }\n        const handleKeydown = event => {\n          externalEventHandlers.onKeyDown?.(event);\n          if (event.defaultMuiPrevented) {\n            return;\n          }\n          const target = event.target;\n          if (event.key === 'Enter' && target.value) {\n            interactions.handleSaveItemLabel(event, target.value);\n          } else if (event.key === 'Escape') {\n            interactions.handleCancelItemLabelEditing(event);\n          }\n        };\n        const handleBlur = event => {\n          externalEventHandlers.onBlur?.(event);\n          if (event.defaultMuiPrevented) {\n            return;\n          }\n          if (event.target.value) {\n            interactions.handleSaveItemLabel(event, event.target.value);\n          }\n        };\n        const handleInputChange = event => {\n          externalEventHandlers.onChange?.(event);\n          setLabelInputValue(event.target.value);\n        };\n        return {\n          value: labelInputValue ?? '',\n          'data-element': 'labelInput',\n          onChange: handleInputChange,\n          onKeyDown: handleKeydown,\n          onBlur: handleBlur,\n          autoFocus: true,\n          type: 'text'\n        };\n      }\n    }\n  };\n};"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACAvB,YAAuB;AAIhB,IAAM,kBAAqC,oBAAc,IAAI;AACpE,IAAI,MAAuC;AACzC,kBAAgB,cAAc;AAChC;;;ADLA,yBAA4B;AAMrB,SAAS,iBAAiB,OAAO;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,aAAoB,mBAAAC,KAAK,gBAAgB,UAAU;AAAA,IACjD;AAAA,IACA,UAAU,MAAM,SAAS;AAAA,MACvB;AAAA,MACA,UAAU,MAAM;AAAA,IAClB,CAAC;AAAA,EACH,CAAC;AACH;;;AEpBA,IAAAC,SAAuB;AAEhB,IAAM,qBAAqB,MAAM;AACtC,QAAM,UAAgB,kBAAW,eAAe;AAChD,MAAI,WAAW,MAAM;AACnB,UAAM,IAAI,MAAM,CAAC,gDAAgD,2GAA2G,8EAA8E,EAAE,KAAK,IAAI,CAAC;AAAA,EACxQ;AACA,SAAO;AACT;;;ACRO,IAAM,YAAY,CAAC,UAAU,WAAW;AAC7C,QAAM,UAAU,SAAS,oBAAoB;AAC7C,SAAO,QAAQ,IAAI,MAAM;AAC3B;;;ACFA,IAAAC,SAAuB;;;ACDvB,IAAAC,SAAuB;AAEhB,IAAM,6BAA6B,CAAC;AAAA,EACzC;AACF,MAAM;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,gBAAS,KAAK;AAClE,QAAM,oBAAoB,SAAS,kBAAkB,MAAM;AAC3D,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,mBAAmB;AACtB,yBAAmB,KAAK;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,mBAAmB,KAAK,CAAC;AAC7B,SAAO;AAAA,IACL,gBAAgB;AAAA,MACd,YAAY,CAAC;AAAA,QACX;AAAA,QACA;AAAA,MACF,MAAM;AACJ,cAAM,WAAW,SAAS,eAAe,MAAM;AAC/C,YAAI,CAAC,UAAU;AACb,iBAAO,CAAC;AAAA,QACV;AACA,cAAM,gBAAgB,WAAS;AA7BvC;AA8BU,sCAAsB,cAAtB,+CAAkC;AAClC,cAAI,MAAM,qBAAqB;AAC7B;AAAA,UACF;AACA,gBAAM,SAAS,MAAM;AACrB,cAAI,MAAM,QAAQ,WAAW,OAAO,OAAO;AACzC,yBAAa,oBAAoB,OAAO,OAAO,KAAK;AAAA,UACtD,WAAW,MAAM,QAAQ,UAAU;AACjC,yBAAa,6BAA6B,KAAK;AAAA,UACjD;AAAA,QACF;AACA,cAAM,aAAa,WAAS;AAzCpC;AA0CU,sCAAsB,WAAtB,+CAA+B;AAC/B,cAAI,MAAM,qBAAqB;AAC7B;AAAA,UACF;AACA,cAAI,MAAM,OAAO,OAAO;AACtB,yBAAa,oBAAoB,OAAO,MAAM,OAAO,KAAK;AAAA,UAC5D;AAAA,QACF;AACA,cAAM,oBAAoB,WAAS;AAlD3C;AAmDU,sCAAsB,aAAtB,+CAAiC;AACjC,6BAAmB,MAAM,OAAO,KAAK;AAAA,QACvC;AACA,eAAO;AAAA,UACL,OAAO,mBAAmB;AAAA,UAC1B,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AD9DO,IAAM,mBAAmB,CAAC;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,gBAAsB,cAAO,MAAM,YAAY;AACrD,QAAM,uBAAuB,YAAU,cAAc,YAAY;AACjE,QAAM,kBAAkB,kBAAgB;AACtC,aAAS,eAAa,SAAS,CAAC,GAAG,WAAW;AAAA,MAC5C;AAAA,IACF,CAAC,CAAC;AACF,kBAAc,UAAU;AAAA,EAC1B;AACA,QAAM,oBAAoB,YAAU,WAAW,MAAM;AACrD,QAAM,qBAAqB,QAAQ,OAAO,cAAc;AACxD,QAAM,iBAAiB,YAAU;AAC/B,QAAI,UAAU,QAAQ,CAAC,oBAAoB;AACzC,aAAO;AAAA,IACT;AACA,UAAM,OAAO,SAAS,QAAQ,MAAM;AACpC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,WAAO,OAAO,OAAO,mBAAmB,aAAa,OAAO,eAAe,IAAI,IAAI,QAAQ,OAAO,cAAc;AAAA,EAClH;AACA,QAAM,kBAAkB,CAAC,QAAQ,UAAU;AACzC,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,CAAC,iFAAiF,yCAAyC,MAAM,EAAE,KAAK,IAAI,CAAC;AAAA,IAC/J;AACA,aAAS,eAAa;AACpB,YAAM,OAAO,UAAU,MAAM,YAAY,MAAM;AAC/C,UAAI,KAAK,UAAU,OAAO;AACxB,eAAO,SAAS,CAAC,GAAG,WAAW;AAAA,UAC7B,OAAO,SAAS,CAAC,GAAG,UAAU,OAAO;AAAA,YACnC,aAAa,SAAS,CAAC,GAAG,UAAU,MAAM,aAAa;AAAA,cACrD,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,MAAM;AAAA,gBAC3B;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,OAAO,mBAAmB;AAC5B,aAAO,kBAAkB,QAAQ,KAAK;AAAA,IACxC;AAAA,EACF;AACA,SAAO;AAAA,IACL,UAAU;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACA,iBAAiB,aAAa;AAC9B,iBAAiB,uBAAuB,CAAC;AAAA,EACvC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,gBAAgB,6DAAsB;AAC5C,MAAI,MAAuC;AACzC,QAAI,OAAO,kBAAkB,CAAC,eAAe;AAC3C,eAAS,CAAC,oGAAoG,gHAAgH,qGAAqG,CAAC;AAAA,IACtU;AAAA,EACF;AACA,SAAO,SAAS,CAAC,GAAG,QAAQ;AAAA,IAC1B,gBAAgB,gBAAgB,OAAO,kBAAkB,QAAQ;AAAA,EACnE,CAAC;AACH;AACA,iBAAiB,kBAAkB,OAAO;AAAA,EACxC,cAAc;AAChB;AACA,iBAAiB,SAAS;AAAA,EACxB,mBAAmB;AAAA,EACnB,gBAAgB;AAClB;", "names": ["React", "_jsx", "React", "React", "React"]}