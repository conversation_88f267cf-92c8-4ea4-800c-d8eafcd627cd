import { useTranslate } from '@/locales/use-locales';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { METADATA_TYPE } from '@/shared/models';
import { getBusinessUnitHierarchyByPermission, getCapabilitySearchDrodown } from '@/shared/services';
import { getCoreSolutions, getMetadata } from '@/shared/services/metadata.service';
import { enqueueSnackbar } from 'notistack';
import { useRef } from 'react';
import { useQuery } from 'react-query';

export const useCapabilityData = (enabled = false, onlyFetchMeta = false) => {
  const hasFetchedBusinessUnits = useRef(false);
  const hasFetchedCoreSolutions = useRef(false);
  const hasFetchVerticals = useRef(false);
  const hasFetchedCapabilities = useRef(false);

  const { t } = useTranslate();

  // Only fetch business units if enabled AND we haven't fetched them before
  const shouldFetchBusinessUnits = enabled && !onlyFetchMeta && !hasFetchedBusinessUnits.current;
  const businessUnitQuery = useQuery({
    enabled: shouldFetchBusinessUnits,
    queryKey: ['businessunit', PERMISSIONS.APPLICATION_ADMIN],
    queryFn: () => getBusinessUnitHierarchyByPermission(PERMISSIONS.APPLICATION_ADMIN),
    onSuccess: () => {
      hasFetchedBusinessUnits.current = true;
    },
    onError: () => {
      //   enqueueSnackbar(t(LocationMessages.BUSINESS_UNIT_ERROR), { variant: 'error' });
    },
  });

  // Only fetch core solutions if enabled AND we haven't fetched them before
  const shouldFetchCoreSolutions = enabled && !onlyFetchMeta && !hasFetchedCoreSolutions.current;
  const coreSolutionsQuery = useQuery({
    enabled: shouldFetchCoreSolutions,
    queryKey: ['coresolutions'],
    queryFn: getCoreSolutions,
    onSuccess: () => {
      hasFetchedCoreSolutions.current = true;
    },
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
  });

  // Metadata query (industry verticals) - this one is always enabled
  const shouldFetchVerticals = enabled && !hasFetchVerticals.current;

  const { data: metadata, isFetching: isFetchingVerticals } = useQuery({
    enabled: shouldFetchVerticals,
    queryKey: ['metadata', METADATA_TYPE.INDUSTRY_VERTICLE],
    queryFn: () =>
      getMetadata({
        types: [METADATA_TYPE.INDUSTRY_VERTICLE],
      }),
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
  });

  // Metadata query (industry verticals) - this one is always enabled

  const { data: capabilityLIst, isFetching: isFetchingCapabilities } = useQuery({
    queryKey: ['capabilities'],
    queryFn: () => getCapabilitySearchDrodown(),
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
    },
  });

  return {
    businessUnitData: businessUnitQuery.data,
    isFilterFetchingLocationData: businessUnitQuery.isFetching,
    coresolutions: coreSolutionsQuery.data,
    isFetchingCoreSolutions: coreSolutionsQuery.isFetching,
    industryVerticals: metadata?.INDUSTRY_VERTICALS ?? [],
    isFetchingVerticals: isFetchingVerticals,
    isFetchingCapabilities,
    capabilityList: capabilityLIst ?? [],
  };
};
