"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetadataModule = void 0;
const common_1 = require("@nestjs/common");
const controllers_1 = require("./controllers");
const repositories_1 = require("./repositories");
const services_1 = require("./services");
const repositories_2 = require("../location/repositories");
const repositories_3 = require("../capability/repositories");
const repositories = [
    repositories_1.CoreSolutionRepository,
    repositories_1.LocationTypeRepository,
    repositories_1.CommonDropdownRepository,
    repositories_1.LocationLifecycleManagementsTypeRepository,
    repositories_2.LocationRepository,
    repositories_3.LocationWiseCapabilityRepository,
];
let MetadataModule = class MetadataModule {
};
MetadataModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.MetadataController],
        providers: [
            ...repositories,
            services_1.MetadataService,
        ],
    })
], MetadataModule);
exports.MetadataModule = MetadataModule;
//# sourceMappingURL=metadata.modules.js.map