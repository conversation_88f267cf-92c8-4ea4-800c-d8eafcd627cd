{"version": 3, "file": "logger.interceptor.js", "sourceRoot": "", "sources": ["../../../src/core/interceptors/logger.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAOwB;AAExB,+BAAyC;AACzC,8CAAqC;AACrC,mDAAqC;AACrC,0CAA4C;AAMrC,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC9B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;QACjC,qBAAgB,GAAG,aAAa,CAAC;IADG,CAAC;IAS/C,SAAS,CAAC,OAAyB,EAAE,IAAiB;QAC5D,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtB,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC;QACpD,IAAI,GAAG,KAAK,IAAI,CAAC,gBAAgB,EAAE;YAClC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACxB,IAAA,eAAG,EAAC;gBACH,IAAI,EAAE,CAAC,CAAU,EAAQ,EAAE;oBAC1B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACvB,CAAC;gBACD,KAAK,EAAE,CAAC,GAAU,EAAQ,EAAE;oBAC3B,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBAC7B,CAAC;aACD,CAAC,CACF,CAAC;SACF;QACD,OAAO,YAAK,CAAC;IACd,CAAC;IAOO,OAAO,CAAC,OAAyB;QACxC,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,GAAG,GAAa,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3D,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;QAC5B,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;QAC3B,MAAM,OAAO,GAAW,cAAc,UAAU,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;QAExE,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,GAAG,iBAAG,OAAO,IAAK,SAAS,EAAG,CAAC;IAC5C,CAAC;IAOO,QAAQ,CAAC,KAAY,EAAE,OAAyB;QACvD,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QAChD,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;QAE5B,IAAI,KAAK,YAAY,sBAAa,EAAE;YACnC,MAAM,UAAU,GAAW,KAAK,CAAC,SAAS,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAW,cAAc,UAAU,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;YACxE,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YAErD,IAAI,UAAU,IAAI,mBAAU,CAAC,qBAAqB,EAAE;gBACnD,IAAI,CAAC,MAAM,CAAC,KAAK,+BAAG,OAAO,IAAK,SAAS,KAAE,KAAK,KAAI,KAAK,CAAC,KAAK,CAAC,CAAC;aACjE;iBAAM;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,+BAAG,OAAO,IAAK,SAAS,KAAE,KAAK,IAAG,CAAC;aACnD;SACD;aAAM;YACN,MAAM,UAAU,GAAW,GAAG,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,KAAK,iCACX,SAAS,KAAE,OAAO,EAAE,cAAc,UAAU,MAAM,MAAM,MAAM,GAAG,EAAE,KACxE,KAAK,CAAC,KAAK,CACX,CAAC;SACF;IACF,CAAC;IAQO,YAAY,CAAC,OAAO,EAAE,UAAU;QACvC,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAChF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;QAC/D,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,WAAW,EAAE,GAAG,gBAAgB,CAAC;QAE5D,MAAM,iBAAiB,GAAG;YACzB,MAAM,EAAE;gBACP,MAAM,EAAE,WAAW;aACnB;YACD,EAAE,oBAAO,EAAE,CAAE;YACb,OAAO,EAAE;gBACR,MAAM,EAAE,IAAI;gBACZ,OAAO;aACP;SACD,CAAC;QAEF,MAAM,OAAO,GAAG;YACf,MAAM,EAAE;gBACP,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC;aAC1C;SACD,CAAC;QAEF,MAAM,IAAI,GAAG;YACZ,MAAM;YACN,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC/C,WAAW,EAAE,UAAU;YACvB,iBAAiB;SACjB,CAAC;QAEF,MAAM,IAAI,GACT,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;QACrF,OAAO,OAAO,CAAC,aAAa,CAAC;QAE7B,OAAO;YACN,IAAI;YACJ,KAAK;YACL,MAAM;YACN,IAAI;YACJ,OAAO;YACP,YAAY,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;YAC1C,IAAI;YACJ,OAAO;SACP,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,OAAO,EAAE,UAAU;QACvC,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,UAAU,CAAC,aAAa,CAAC;IAC/D,CAAC;IAEO,cAAc,CAAC,GAAW;QACjC,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;CACD,CAAA;AAnIY,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAEyB,wBAAa;GADtC,kBAAkB,CAmI9B;AAnIY,gDAAkB"}