import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { BingoCardConfigRepository } from '../repositories';
import {
	BingoCardConfigResponseDto,
	BingoCardConfigWithCapabilitiesResponseDto,
	BingoLocationCapabilitiesResponse,
	FilterRequestDto,
} from '../dtos';
import { multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import { CapabilityLegRepository, CapabilityRepository } from 'src/capability/repositories';
import { LocationRepository } from 'src/location/repositories';
import { CurrentContext } from 'src/shared/types';
import { ReportResponseDto } from '../dtos/response/list-reports.response.dto';
import { MessageResponseDto } from 'src/shared/dtos';
import { UpsertReportDTO } from '../dtos/request/upsert-report-request.dto';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE, PERMISSIONS } from 'src/shared/enums';

@Injectable()
export class BingoCardService {
	constructor(
		private readonly bingoCardConfigRepository: BingoCardConfigRepository,
		private readonly capabilityRepository: CapabilityRepository,
		private readonly capabilityLegRepository: CapabilityLegRepository,
		private readonly locationRepository: LocationRepository,
		private readonly adminApiClient: AdminApiClient,
		private readonly historyService: HistoryApiClient,
	) {}

	public async getAllBingoCardConfigs(): Promise<BingoCardConfigResponseDto[]> {
		const configs = await this.bingoCardConfigRepository.getAllBingoCardConfigs();

		return multiObjectToInstance(BingoCardConfigResponseDto, configs);
	}
	async listReports(currentContext: CurrentContext): Promise<ReportResponseDto[]> {
		const reports = await this.bingoCardConfigRepository.getAllBingoCardConfigs(
			currentContext.user.username,
		);
		return multiObjectToInstance(ReportResponseDto, reports);
	}
	async createReport(
		createReportDTO: UpsertReportDTO,
		currentContext: CurrentContext,
	): Promise<ReportResponseDto> {
		const { id, ...restOfData } = createReportDTO;
		const existingReport = await this.bingoCardConfigRepository.getExistingReportByTitle(
			restOfData.title,
			id,
			currentContext.user.username,
		);
		if (existingReport.length) {
			throw new HttpException(
				'Report with same title exists for your account',
				HttpStatus.CONFLICT,
			);
		}
		if (!restOfData.isPersonalReport) {
			const hasPermission = await this.adminApiClient.hasPermissionToUser(
				currentContext.user.username,
				PERMISSIONS.APPLICATION_ADMIN,
			);

			if (!hasPermission) {
				throw new HttpException(
					`Unauthorized to ${id != undefined ? 'edit' : 'add'} global report`,
					HttpStatus.UNAUTHORIZED,
				);
			}
		}
		let upsertedReport = null;
		if (id) {
			upsertedReport = singleObjectToInstance(
				ReportResponseDto,
				await this.bingoCardConfigRepository.updateReport(id, restOfData, currentContext),
			);
		} else {
			upsertedReport = singleObjectToInstance(
				ReportResponseDto,
				await this.bingoCardConfigRepository.createReport(restOfData, currentContext),
			);
		}
		await this.historyService.addRequestHistory({
			created_by: currentContext.user.username,
			entity_id: upsertedReport.id,
			entity_type: HISTORY_ENTITY_TYPE.REPORT,
			action_performed: id ? HISTORY_ACTION_TYPE.UPDATE : HISTORY_ACTION_TYPE.CREATE,
			action_date: new Date(),
			comments: id ? `Report updated` : `Report created`,
			additional_info: createReportDTO,
		});
		return upsertedReport;
	}
	async deleteReport(currentContext: CurrentContext, id: number): Promise<MessageResponseDto> {
		const config = await this.bingoCardConfigRepository.getBingoCardConfigById(id);

		if (!config) {
			throw new HttpException('Config not found', HttpStatus.NOT_FOUND);
		}
		if (
			config.isPersonalReport &&
			config.createdBy.toLowerCase() != currentContext.user.username.toLowerCase()
		) {
			throw new HttpException('Unauthorized to delete report', HttpStatus.UNAUTHORIZED);
		}
		if (!config.isPersonalReport) {
			const hasPermission = await this.adminApiClient.hasPermissionToUser(
				currentContext.user.username,
				PERMISSIONS.APPLICATION_ADMIN,
			);

			if (!hasPermission) {
				throw new HttpException('Unauthorized to delete report', HttpStatus.UNAUTHORIZED);
			}
		}

		await this.bingoCardConfigRepository.deleteById(config.id, currentContext);
		await this.historyService.addRequestHistory({
			created_by: currentContext.user.username,
			entity_id: config.id,
			entity_type: HISTORY_ENTITY_TYPE.REPORT,
			action_performed: HISTORY_ACTION_TYPE.DELETE,
			action_date: new Date(),
			comments: 'Report deleted',
		});
		return { message: 'Report deleted succesfully' };
	}
	public async getBingoCardConfigById(
		id: number,
		currentContext: CurrentContext,
	): Promise<BingoCardConfigWithCapabilitiesResponseDto> {
		const config = await this.bingoCardConfigRepository.getBingoCardConfigById(id);

		if (!config) {
			throw new HttpException('Config not found', HttpStatus.NOT_FOUND);
		}
		if (
			config.isPersonalReport &&
			config.createdBy.toLowerCase() != currentContext.user.username.toLowerCase()
		) {
			throw new HttpException(
				'Configuration not available for your account',
				HttpStatus.UNAUTHORIZED,
			);
		}

		const capabilities = await this.capabilityRepository.getMasterCapabilityDetailByCapabilityIds(
			config.capabilityIds,
		);

		if (!capabilities.length) {
			throw new HttpException('No entries found for this config', HttpStatus.NOT_FOUND);
		}

		const allLegs = await this.capabilityLegRepository.getAllLegs();

		const capabilitiesWithLegs = capabilities.map(capability => {
			const legs = allLegs.filter(leg => capability.legs.includes(leg.id));
			return { ...capability, legs };
		});

		return singleObjectToInstance(BingoCardConfigWithCapabilitiesResponseDto, {
			...config,
			capabilities: capabilitiesWithLegs,
		});
	}

	public async getConfigLocationCapabilities(
		id: number,
		filters?: FilterRequestDto,
	): Promise<BingoLocationCapabilitiesResponse[]> {
		const config = await this.bingoCardConfigRepository.getBingoCardConfigById(id);

		if (!config) {
			throw new HttpException('Config not found', HttpStatus.NOT_FOUND);
		}

		const { capabilityIds } = config;

		if (!capabilityIds.length) {
			throw new HttpException('No entries found for this config', HttpStatus.NOT_FOUND);
		}

		const locations = await this.locationRepository.getAllLocationsWithCapabilities(
			capabilityIds,
			filters,
		);

		return multiObjectToInstance(BingoLocationCapabilitiesResponse, locations, {
			excludeExtraneousValues: true,
			enableImplicitConversion: true,
		});
	}
}
