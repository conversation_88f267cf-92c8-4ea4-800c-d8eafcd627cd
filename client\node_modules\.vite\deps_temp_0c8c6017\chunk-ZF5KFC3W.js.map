{"version": 3, "sources": ["../../@mui/material/MenuItem/MenuItem.js", "../../@mui/material/MenuItem/menuItemClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport ButtonBase from \"../ButtonBase/index.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport { dividerClasses } from \"../Divider/index.js\";\nimport { listItemIconClasses } from \"../ListItemIcon/index.js\";\nimport { listItemTextClasses } from \"../ListItemText/index.js\";\nimport menuItemClasses, { getMenuItemUtilityClass } from \"./menuItemClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    disabled,\n    dense,\n    divider,\n    disableGutters,\n    selected,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', disabled && 'disabled', !disableGutters && 'gutters', divider && 'divider', selected && 'selected']\n  };\n  const composedClasses = composeClasses(slots, getMenuItemUtilityClass, classes);\n  return {\n    ...classes,\n    ...composedClasses\n  };\n};\nconst MenuItemRoot = styled(ButtonBase, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiMenuItem',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  ...theme.typography.body1,\n  display: 'flex',\n  justifyContent: 'flex-start',\n  alignItems: 'center',\n  position: 'relative',\n  textDecoration: 'none',\n  minHeight: 48,\n  paddingTop: 6,\n  paddingBottom: 6,\n  boxSizing: 'border-box',\n  whiteSpace: 'nowrap',\n  '&:hover': {\n    textDecoration: 'none',\n    backgroundColor: (theme.vars || theme).palette.action.hover,\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  [`&.${menuItemClasses.selected}`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n    [`&.${menuItemClasses.focusVisible}`]: {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n    }\n  },\n  [`&.${menuItemClasses.selected}:hover`]: {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity)\n    }\n  },\n  [`&.${menuItemClasses.focusVisible}`]: {\n    backgroundColor: (theme.vars || theme).palette.action.focus\n  },\n  [`&.${menuItemClasses.disabled}`]: {\n    opacity: (theme.vars || theme).palette.action.disabledOpacity\n  },\n  [`& + .${dividerClasses.root}`]: {\n    marginTop: theme.spacing(1),\n    marginBottom: theme.spacing(1)\n  },\n  [`& + .${dividerClasses.inset}`]: {\n    marginLeft: 52\n  },\n  [`& .${listItemTextClasses.root}`]: {\n    marginTop: 0,\n    marginBottom: 0\n  },\n  [`& .${listItemTextClasses.inset}`]: {\n    paddingLeft: 36\n  },\n  [`& .${listItemIconClasses.root}`]: {\n    minWidth: 36\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disableGutters,\n    style: {\n      paddingLeft: 16,\n      paddingRight: 16\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.divider,\n    style: {\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n      backgroundClip: 'padding-box'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.dense,\n    style: {\n      [theme.breakpoints.up('sm')]: {\n        minHeight: 'auto'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.dense,\n    style: {\n      minHeight: 32,\n      // https://m2.material.io/components/menus#specs > Dense\n      paddingTop: 4,\n      paddingBottom: 4,\n      ...theme.typography.body2,\n      [`& .${listItemIconClasses.root} svg`]: {\n        fontSize: '1.25rem'\n      }\n    }\n  }]\n})));\nconst MenuItem = /*#__PURE__*/React.forwardRef(function MenuItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiMenuItem'\n  });\n  const {\n    autoFocus = false,\n    component = 'li',\n    dense = false,\n    divider = false,\n    disableGutters = false,\n    focusVisibleClassName,\n    role = 'menuitem',\n    tabIndex: tabIndexProp,\n    className,\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    disableGutters\n  }), [context.dense, dense, disableGutters]);\n  const menuItemRef = React.useRef(null);\n  useEnhancedEffect(() => {\n    if (autoFocus) {\n      if (menuItemRef.current) {\n        menuItemRef.current.focus();\n      } else if (process.env.NODE_ENV !== 'production') {\n        console.error('MUI: Unable to set focus to a MenuItem whose component has not been rendered.');\n      }\n    }\n  }, [autoFocus]);\n  const ownerState = {\n    ...props,\n    dense: childContext.dense,\n    divider,\n    disableGutters\n  };\n  const classes = useUtilityClasses(props);\n  const handleRef = useForkRef(menuItemRef, ref);\n  let tabIndex;\n  if (!props.disabled) {\n    tabIndex = tabIndexProp !== undefined ? tabIndexProp : -1;\n  }\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(MenuItemRoot, {\n      ref: handleRef,\n      role: role,\n      tabIndex: tabIndex,\n      component: component,\n      focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n      className: clsx(classes.root, className),\n      ...other,\n      ownerState: ownerState,\n      classes: classes\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the list item is focused during the first mount.\n   * Focus will also be triggered if the value changes from false to true.\n   * @default false\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent Menu component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the menu item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * This prop can help identify which element has keyboard focus.\n   * The class name will be applied when the element gains the focus through keyboard interaction.\n   * It's a polyfill for the [CSS :focus-visible selector](https://drafts.csswg.org/selectors-4/#the-focus-visible-pseudo).\n   * The rationale for using this feature [is explained here](https://github.com/WICG/focus-visible/blob/HEAD/explainer.md).\n   * A [polyfill can be used](https://github.com/WICG/focus-visible) to apply a `focus-visible` class to other components\n   * if needed.\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * If `true`, the component is selected.\n   * @default false\n   */\n  selected: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @default 0\n   */\n  tabIndex: PropTypes.number\n} : void 0;\nexport default MenuItem;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItem', slot);\n}\nconst menuItemClasses = generateUtilityClasses('MuiMenuItem', ['root', 'focusVisible', 'dense', 'disabled', 'divider', 'gutters', 'selected']);\nexport default menuItemClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,wBAAwB,MAAM;AAC5C,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACA,IAAM,kBAAkB,uBAAuB,eAAe,CAAC,QAAQ,gBAAgB,SAAS,YAAY,WAAW,WAAW,UAAU,CAAC;AAC7I,IAAO,0BAAQ;;;ADaf,yBAA4B;AACrB,IAAM,oBAAoB,CAAC,OAAO,WAAW;AAClD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,CAAC,OAAO,MAAM,WAAW,SAAS,OAAO,OAAO,WAAW,WAAW,OAAO,SAAS,CAAC,WAAW,kBAAkB,OAAO,OAAO;AAC3I;AACA,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,SAAS,YAAY,YAAY,CAAC,kBAAkB,WAAW,WAAW,WAAW,YAAY,UAAU;AAAA,EACrI;AACA,QAAM,kBAAkB,eAAe,OAAO,yBAAyB,OAAO;AAC9E,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AACA,IAAM,eAAe,eAAO,oBAAY;AAAA,EACtC,mBAAmB,UAAQ,8BAAsB,IAAI,KAAK,SAAS;AAAA,EACnE,MAAM;AAAA,EACN,MAAM;AAAA,EACN;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,GAAG,MAAM,WAAW;AAAA,EACpB,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,IACT,gBAAgB;AAAA,IAChB,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA;AAAA,IAEtD,wBAAwB;AAAA,MACtB,iBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EACA,CAAC,KAAK,wBAAgB,QAAQ,EAAE,GAAG;AAAA,IACjC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,eAAe;AAAA,IACvM,CAAC,KAAK,wBAAgB,YAAY,EAAE,GAAG;AAAA,MACrC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,WAAW,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,OAAO,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,kBAAkB,MAAM,QAAQ,OAAO,YAAY;AAAA,IAC/R;AAAA,EACF;AAAA,EACA,CAAC,KAAK,wBAAgB,QAAQ,QAAQ,GAAG;AAAA,IACvC,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,WAAW,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,OAAO,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,kBAAkB,MAAM,QAAQ,OAAO,YAAY;AAAA;AAAA,IAE7R,wBAAwB;AAAA,MACtB,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,eAAe,MAAM,MAAM,MAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ,OAAO,eAAe;AAAA,IACzM;AAAA,EACF;AAAA,EACA,CAAC,KAAK,wBAAgB,YAAY,EAAE,GAAG;AAAA,IACrC,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EACxD;AAAA,EACA,CAAC,KAAK,wBAAgB,QAAQ,EAAE,GAAG;AAAA,IACjC,UAAU,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EAChD;AAAA,EACA,CAAC,QAAQ,uBAAe,IAAI,EAAE,GAAG;AAAA,IAC/B,WAAW,MAAM,QAAQ,CAAC;AAAA,IAC1B,cAAc,MAAM,QAAQ,CAAC;AAAA,EAC/B;AAAA,EACA,CAAC,QAAQ,uBAAe,KAAK,EAAE,GAAG;AAAA,IAChC,YAAY;AAAA,EACd;AAAA,EACA,CAAC,MAAM,4BAAoB,IAAI,EAAE,GAAG;AAAA,IAClC,WAAW;AAAA,IACX,cAAc;AAAA,EAChB;AAAA,EACA,CAAC,MAAM,4BAAoB,KAAK,EAAE,GAAG;AAAA,IACnC,aAAa;AAAA,EACf;AAAA,EACA,CAAC,MAAM,4BAAoB,IAAI,EAAE,GAAG;AAAA,IAClC,UAAU;AAAA,EACZ;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,CAAC,WAAW;AAAA,IAClB,OAAO;AAAA,MACL,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,cAAc,cAAc,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,MAChE,gBAAgB;AAAA,IAClB;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,CAAC,WAAW;AAAA,IAClB,OAAO;AAAA,MACL,CAAC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG;AAAA,QAC5B,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,WAAW;AAAA;AAAA,MAEX,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,GAAG,MAAM,WAAW;AAAA,MACpB,CAAC,MAAM,4BAAoB,IAAI,MAAM,GAAG;AAAA,QACtC,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,WAA8B,iBAAW,SAASA,UAAS,SAAS,KAAK;AAC7E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB;AAAA,IACA,OAAO;AAAA,IACP,UAAU;AAAA,IACV;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAgB,iBAAW,mBAAW;AAC5C,QAAM,eAAqB,cAAQ,OAAO;AAAA,IACxC,OAAO,SAAS,QAAQ,SAAS;AAAA,IACjC;AAAA,EACF,IAAI,CAAC,QAAQ,OAAO,OAAO,cAAc,CAAC;AAC1C,QAAM,cAAoB,aAAO,IAAI;AACrC,4BAAkB,MAAM;AACtB,QAAI,WAAW;AACb,UAAI,YAAY,SAAS;AACvB,oBAAY,QAAQ,MAAM;AAAA,MAC5B,WAAW,MAAuC;AAChD,gBAAQ,MAAM,+EAA+E;AAAA,MAC/F;AAAA,IACF;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AACd,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH,OAAO,aAAa;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,KAAK;AACvC,QAAM,YAAY,mBAAW,aAAa,GAAG;AAC7C,MAAI;AACJ,MAAI,CAAC,MAAM,UAAU;AACnB,eAAW,iBAAiB,SAAY,eAAe;AAAA,EACzD;AACA,aAAoB,mBAAAC,KAAK,oBAAY,UAAU;AAAA,IAC7C,OAAO;AAAA,IACP,cAAuB,mBAAAA,KAAK,cAAc;AAAA,MACxC,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,uBAAuB,aAAK,QAAQ,cAAc,qBAAqB;AAAA,MACvE,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,SAAS,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlF,WAAW,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnB,uBAAuB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjC,MAAM,kBAAAA,QAAgD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,UAAU,kBAAAA,QAAU;AACtB,IAAI;AACJ,IAAO,mBAAQ;", "names": ["MenuItem", "_jsx", "PropTypes"]}