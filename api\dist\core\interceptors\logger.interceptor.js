"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggingInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const platform = __importStar(require("platform"));
const services_1 = require("../services");
let LoggingInterceptor = class LoggingInterceptor {
    constructor(logger) {
        this.logger = logger;
        this.HEALTH_CHECK_URL = '/api/health';
    }
    intercept(context, call) {
        this.now = Date.now();
        const url = context.switchToHttp().getRequest().url;
        if (url !== this.HEALTH_CHECK_URL) {
            return call.handle().pipe((0, operators_1.tap)({
                next: (_) => {
                    this.logNext(context);
                },
                error: (err) => {
                    this.logError(err, context);
                },
            }));
        }
        return rxjs_1.EMPTY;
    }
    logNext(context) {
        const req = context.switchToHttp().getRequest();
        const res = context.switchToHttp().getResponse();
        const { method, url } = req;
        const { statusCode } = res;
        const message = `Response - ${statusCode} - ${method} - ${url}`;
        const logObject = this.getLogObject(req, statusCode);
        this.logger.log(Object.assign({ message }, logObject));
    }
    logError(error, context) {
        const req = context.switchToHttp().getRequest();
        const { method, url } = req;
        if (error instanceof common_1.HttpException) {
            const statusCode = error.getStatus();
            const message = `Response - ${statusCode} - ${method} - ${url}`;
            const logObject = this.getLogObject(req, statusCode);
            if (statusCode >= common_1.HttpStatus.INTERNAL_SERVER_ERROR) {
                this.logger.error(Object.assign(Object.assign({ message }, logObject), { error }), error.stack);
            }
            else {
                this.logger.warn(Object.assign(Object.assign({ message }, logObject), { error }));
            }
        }
        else {
            const statusCode = 500;
            const logObject = this.getLogObject(req, statusCode);
            this.logger.error(Object.assign(Object.assign({}, logObject), { message: `Response - ${statusCode} - ${method} - ${url}` }), error.stack);
        }
    }
    getLogObject(request, statusCode) {
        const { method, url, body, headers, connection, user, query, params } = request;
        const clientDeviceInfo = platform.parse(headers['user-agent']);
        const { name, version, os, description } = clientDeviceInfo;
        const useragent_details = {
            device: {
                family: description,
            },
            os: Object.assign({}, os),
            browser: {
                family: name,
                version,
            },
        };
        const network = {
            client: {
                ip: this.getIpAddress(headers, connection),
            },
        };
        const http = {
            method,
            url_details: { path: this.getPathFromUrl(url) },
            status_code: statusCode,
            useragent_details,
        };
        const auth = user && user.id ? { protectedUrl: true, userId: user.id } : { protectedUrl: false };
        delete headers.authorization;
        return {
            body,
            query,
            params,
            http,
            network,
            responseTime: `${Date.now() - this.now}ms`,
            auth,
            headers,
        };
    }
    getIpAddress(headers, connection) {
        return headers['x-forwarded-for'] || connection.remoteAddress;
    }
    getPathFromUrl(url) {
        return url.toLowerCase().split(/[?#]/)[0];
    }
};
LoggingInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [services_1.LoggerService])
], LoggingInterceptor);
exports.LoggingInterceptor = LoggingInterceptor;
//# sourceMappingURL=logger.interceptor.js.map