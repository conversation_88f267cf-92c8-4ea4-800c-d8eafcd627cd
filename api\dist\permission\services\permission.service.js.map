{"version": 3, "file": "permission.service.js", "sourceRoot": "", "sources": ["../../../src/permission/services/permission.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAAuE;AACvE,sDAAiD;AACjD,8DAA+D;AAC/D,kDAAwF;AAExF,8CAAyF;AACzF,kDAA2E;AAC3E,oDAA8D;AAE9D,kCAMiB;AACjB,kDAA0F;AAGnF,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC7B,YACkB,cAA8B,EAC9B,6BAA4D,EAC5D,wBAAkD,EAClD,kBAAsC,EACtC,gBAAkC,EAClC,uBAAgD,EAChD,cAAgC,EAChC,cAA8B;QAP9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,kCAA6B,GAA7B,6BAA6B,CAA+B;QAC5D,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,4BAAuB,GAAvB,uBAAuB,CAAyB;QAChD,mBAAc,GAAd,cAAc,CAAkB;QAChC,mBAAc,GAAd,cAAc,CAAgB;IAC5C,CAAC;IAOQ,wBAAwB,CACpC,cAA8B;;YAE9B,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC,IAAI,CAAC;YACzC,MAAM,CAAC,WAAW,EAAE,gBAAgB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACzD,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACpE,IAAI,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;aAChF,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;YACzC,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;YAE3C,KAAK,MAAM,EAAE,cAAc,EAAE,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,gBAAgB,IAAI,EAAE,EAAE;gBAC9F,IAAI,EAAE;oBAAE,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACvC,IAAI,EAAE;oBAAE,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;aACzC;YAED,IAAI,cAAc,CAAC,IAAI,EAAE;gBACxB,WAAW,CAAC,IAAI,CAAC;oBAChB,cAAc,EAAE,mBAAW,CAAC,qBAAqB;oBACjD,aAAa,EAAE,IAAI;oBACnB,SAAS,EAAE,CAAC,GAAG,cAAc,CAAC;iBAC9B,CAAC,CAAC;aACH;YAED,IAAI,gBAAgB,CAAC,IAAI,EAAE;gBAC1B,WAAW,CAAC,IAAI,CAAC;oBAChB,cAAc,EAAE,mBAAW,CAAC,uBAAuB;oBACnD,aAAa,EAAE,IAAI;oBACnB,SAAS,EAAE,CAAC,GAAG,gBAAgB,CAAC;iBAChC,CAAC,CAAC;aACH;YAED,OAAO,IAAA,+BAAqB,EAAC,4BAAqB,EAAE,WAAW,CAAC,CAAC;QAClE,CAAC;KAAA;IAMY,0BAA0B;;YACtC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,0BAA0B,EAAE,CAAC;YACtF,OAAO,IAAA,+BAAqB,EAAC,wCAAiC,EAAE,OAAO,CAAC,CAAC;QAC1E,CAAC;KAAA;IAUY,gCAAgC,CAC5C,cAA8B,EAC9B,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,OAAe,EACf,cAAsB,EACtB,OAAgB,EAChB,aAA6B,EAC7B,UAA0B;;YAE1B,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACpE,MAAM,kBAAkB,GACvB,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI;gBACpD,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;oBACpC,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;gBACxB,CAAC,CAAC,IAAI,CAAC;YAET,MAAM,eAAe,GACpB,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI;gBAC9C,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBACjC,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;gBACrB,CAAC,CAAC,IAAI,CAAC;YAET,IAAI,UAAU,IAAI,CAAC,eAAe,EAAE;gBACnC,MAAM,IAAI,sBAAa,CAAC,2BAA2B,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aAC7E;YAED,IAAI,aAAa,IAAI,CAAC,kBAAkB,EAAE;gBACzC,MAAM,IAAI,sBAAa,CAAC,+BAA+B,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aACjF;YAED,IAAI,qBAAqB,GAAG,EAAE,CAAC;YAE/B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,kCAAkC,CACzF,cAAc,CAAC,IAAI,CAAC,WAAW,EAC/B,mBAAW,CAAC,aAAa,CACzB,CAAC;YAEF,IAAI,CAAC,UAAU,IAAI,CAAC,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,CAAA,EAAE;gBACzC,MAAM,IAAI,sBAAa,CACtB,wDAAwD,EACxD,mBAAU,CAAC,SAAS,CACpB,CAAC;aACF;YAED,IAAI,SAAS,GAAG,EAAE,CAAC;YACnB,IAAI,YAAY,CAAC,MAAM,EAAE;gBACxB,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;aACnF;YAED,qBAAqB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAExD,IAAI,UAAU,EAAE;gBACf,IAAI,qBAAqB,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;oBAC/C,qBAAqB,GAAG,CAAC,UAAU,CAAC,CAAC;iBACrC;qBAAM;oBACN,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAC1E,CAAC,mBAAW,CAAC,qBAAqB,CAAC,EACnC,cAAc,CAAC,IAAI,EACnB,IAAI,EACJ,UAAU,CACV,CAAC;oBAEF,IAAI,CAAC,aAAa,EAAE;wBACnB,MAAM,IAAI,sBAAa,CACtB,yDAAyD,EACzD,mBAAU,CAAC,SAAS,CACpB,CAAC;qBACF;oBAED,qBAAqB,GAAG,CAAC,UAAU,CAAC,CAAC;iBACrC;aACD;YAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,CAC7E,SAAS,EACT,UAAU,EACV,OAAO,EACP,cAAc,EACd,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,EACtB,kBAAkB,EAClB,qBAAqB,CACrB,CAAC;YAEF,MAAM,OAAO,GAAG,IAAA,+BAAqB,EAAC,gCAAyB,EAAE,IAAI,CAAC,CAAC;YACvE,OAAO,IAAI,uBAAU,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC;KAAA;IAQY,oBAAoB,CAChC,IAAoC,EACpC,cAA8B;;;YAE9B,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;YAClD,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAEhC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,0BAA0B,CACtF,aAAa,CACb,CAAC;YAEF,IAAI,CAAC,WAAW,EAAE;gBACjB,MAAM,IAAI,sBAAa,CAAC,wCAAwC,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aAC1F;YAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAEnF,IAAI,CAAC,CAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,0CAAE,MAAM,CAAA,EAAE;gBAC/B,MAAM,IAAI,sBAAa,CAAC,cAAc,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;aAChE;YAED,IAAI,UAAU,EAAE;gBACf,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;gBAEvF,IAAI,CAAC,cAAc,EAAE;oBACpB,MAAM,IAAI,sBAAa,CAAC,2BAA2B,EAAE,mBAAU,CAAC,WAAW,CAAC,CAAC;iBAC7E;gBAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAC1E,CAAC,mBAAW,CAAC,aAAa,EAAE,mBAAW,CAAC,qBAAqB,CAAC,EAC9D,cAAc,CAAC,IAAI,EACnB,cAAc,CAAC,QAAQ,EACvB,UAAU,CACV,CAAC;gBAEF,IAAI,CAAC,aAAa,EAAE;oBACnB,MAAM,IAAI,sBAAa,CACtB,yDAAyD,EACzD,mBAAU,CAAC,SAAS,CACpB,CAAC;iBACF;aACD;YAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,CAC7E,aAAa,EACb,OAAO,EACP,UAAU,CACV,CAAC;YAEF,IAAI,YAAY,EAAE;gBACjB,MAAM,IAAI,sBAAa,CAAC,+BAA+B,EAAE,mBAAU,CAAC,QAAQ,CAAC,CAAC;aAC9E;YAED,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAS,EAAE;gBAC5D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,CAC1E;oBACC,UAAU;oBACV,OAAO,EAAE,OAAO;oBAChB,aAAa,EAAE,aAAa;oBAC5B,cAAc,EAAE,WAAW,CAAC,cAAc;oBAC1C,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;iBAC9C,EACD,cAAc,CACd,CAAC;gBAEF,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;oBAC3C,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,QAAQ;oBACxC,SAAS,EAAE,UAAU;oBACrB,WAAW,EAAE,2BAAmB,CAAC,QAAQ;oBACzC,gBAAgB,EAAE,2BAAmB,CAAC,gBAAgB;oBACtD,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,QAAQ,EAAE,GAAG,OAAO,aAAa,WAAW,CAAC,SAAS,GAAG;oBACzD,eAAe,EAAE;wBAChB,cAAc,EAAE,IAAI;qBACpB;iBACD,CAAC,CAAC;gBAEH,OAAO,EAAE,OAAO,EAAE,sCAAsC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;YAC9E,CAAC,CAAA,CAAC,CAAC;;KACH;IAQY,oBAAoB,CAChC,EAAU,EACV,cAA8B;;YAE9B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;YAEjF,IAAI,CAAC,UAAU,EAAE;gBAChB,MAAM,IAAI,sBAAa,CAAC,2BAA2B,EAAE,mBAAU,CAAC,SAAS,CAAC,CAAC;aAC3E;YAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CACzE,UAAU,CAAC,UAAU,CACrB,CAAC;YAEF,IAAI,cAAc,EAAE;gBACnB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAC1E,CAAC,mBAAW,CAAC,aAAa,EAAE,mBAAW,CAAC,qBAAqB,CAAC,EAC9D,cAAc,CAAC,IAAI,EACnB,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,EAAE,CACjB,CAAC;gBAEF,IAAI,CAAC,aAAa,EAAE;oBACnB,MAAM,IAAI,sBAAa,CACtB,6DAA6D,EAC7D,mBAAU,CAAC,SAAS,CACpB,CAAC;iBACF;aACD;YAED,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAS,EAAE;gBACtD,MAAM,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;gBAE7E,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;oBAC3C,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,QAAQ;oBACxC,SAAS,EAAE,UAAU,CAAC,UAAU;oBAChC,WAAW,EAAE,2BAAmB,CAAC,QAAQ;oBACzC,gBAAgB,EAAE,2BAAmB,CAAC,kBAAkB;oBACxD,WAAW,EAAE,IAAI,IAAI,EAAE;oBACvB,QAAQ,EAAE,GAAG,UAAU,CAAC,OAAO,iBAAiB,UAAU,CAAC,mBAAmB,CAAC,SAAS,GAAG;oBAC3F,eAAe,EAAE;wBAChB,cAAc,EAAE,UAAU;qBAC1B;iBACD,CAAC,CAAC;gBAEH,OAAO,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;YAC5D,CAAC,CAAA,CAAC,CAAC;QACJ,CAAC;KAAA;IAOY,eAAe,CAAC,OAAe;;YAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAS,EAAE;gBACtD,MAAM,SAAS,GAAG,CAAC,MAAM,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,OAAO,CAAC,CAE5E,CAAC;gBACJ,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC1D,OAAO;oBACN,OAAO,EAAE,8BAA8B;oBACvC,IAAI;iBACJ,CAAC;YACH,CAAC,CAAA,CAAC,CAAC;QACJ,CAAC;KAAA;CACD,CAAA;AAhUY,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAGsB,wBAAc;QACC,4CAA6B;QAClC,uCAAwB;QAC9B,iCAAkB;QACpB,0BAAgB;QACT,kCAAuB;QAChC,0BAAgB;QAChB,wBAAc;GATpC,iBAAiB,CAgU7B;AAhUY,8CAAiB"}