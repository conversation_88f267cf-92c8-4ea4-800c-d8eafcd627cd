"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupportQueryController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const enums_1 = require("../../shared/enums");
const dtos_1 = require("../dtos");
const services_1 = require("../services");
let SupportQueryController = class SupportQueryController {
    constructor(supportQueryService) {
        this.supportQueryService = supportQueryService;
    }
    createUserSupportQuery(request, data) {
        return this.supportQueryService.createUserSupportQuery(data, request.currentContext);
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Create a new user support query',
        type: dtos_1.SupportQueryResponseDto,
    }),
    (0, common_1.Post)('/help'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.SupportQueryRequestDto]),
    __metadata("design:returntype", Promise)
], SupportQueryController.prototype, "createUserSupportQuery", null);
SupportQueryController = __decorate([
    (0, swagger_1.ApiTags)('Support APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('support-query'),
    __metadata("design:paramtypes", [services_1.SupportQueryService])
], SupportQueryController);
exports.SupportQueryController = SupportQueryController;
//# sourceMappingURL=support-query.controller.js.map