export const API_PATHS = {
  APPLICATION_CONFIGURATION: {
    WEB_CLIENT: 'config/web-client',
    APPLICATION_DETAIL: 'config/application',
    USER_PERMISSIONS: 'permissions/app-permission',
  },

  BUSINESS_ENTITY: {
    BUSINESS_HIERARCHY: 'business-entities',
  },
  COUNTRY_MANGEMENT: {
    UPSERT_COUNTRY: 'country/upsert',
    GET_COUNTRY: 'country/detail/:entityId',
  },
  LEGALENTITY_MANAGEMENT: {
    GET_LEGAL_ENTITIY: 'country/legal-entity',
    UPSERT_LEGAL_ENTITY: 'country/legal-entity/upsert',
    DELETE_LEGAL_ENTITY: 'country/legal-entity/:legalEntityId',
  },
  ACTIVE_DIRECTORY: {
    SEARCH_GRAPH_USER: 'graph-users/search-users',
    GET_USER_DETAILS: 'graph-users',
  },
  CONTACT: {
    LIST_CONTACTS: 'contact-detail/user-list',
    CREATE_CONTACT: 'contact-detail/hierarchy-contact',
    DELETET_CONTACT: 'contact-detail/user-detail',
    PATCH_CONTACT: 'contact-detail/user-detail',
  },
  ATTACHMENT: {
    GETFILE: `attachment/content/:fileId`,
  },
  METADATA: {
    ALL_LOCATION_LIFECYCLE: 'metadata/all-location-lifecycle-management',
    GET_CORE_SOUTIONS: 'metadata/core-solution',
    GET_LOCATIONTYPE_BY_CORE_SOLUTION: 'metadata/location-type/:coreSolutionId',
    GET_ALL_LOCATIONTYPES: 'metadata/all-location-type',
    GET_METADATA_BY_TYPE: 'metadata/common-metadata-type-wise',
  },
  ROLE_MANAGEMENT: {
    USER_SUMMARY: 'role-management/get-user-summary',
    GET_ACCESSIBLE_ROLE: 'role-management/get-accessible-roles',
    GET_ROLE_DETAIL: 'role-management/get-role-detail/:roleId',
    GET_ROLE_USER_LIST: 'role-management/get-role-user-list',
    ADD_ROLE_USERS: 'role-management/add-role-user',
    UPDATE_ROLE_USERS: 'role-management/update-role-user',
    DELETE_ROLE_USERS: 'role-management/delete-role-user',
  },
  PARTNER_BRANCH: {
    CREATE: 'partner-branch',
    ACTION: 'partner-branch/action',

    GET: 'partner-branch/:id',
  },

  LOCATION: {
    UPSERT: 'location',
    UPDATE_STATUS: 'location/:id',

    DELETE: 'location/:id',
    REQUEST_BRANCH: 'location',
    BASIC_DETAIL: 'location/:id/basic-detail',
    COMPLETE_DETAIL: 'location/:id/complete-detail',
    LOCATION_LIST: 'location/list',
    AVAILABLE_CAPABILITY_LIST: 'location/:locationId/capabilities',
    EXPORT_LOCATION_LIST: 'location/download',
    EXPORT_AVAILABLE_CAPABILITY_LIST: 'location/:locationId/capabilities/download',
  },
  USER_ACCESS: {
    GET_ALL_PERMISSIONS: 'permissions/local-permission',
    GET_USER_GROUP_DETAILS: 'permissions/access-control-config',
    GET_LOCATION_DETAILS: 'location/dropdown',
    DELETE_USER_PERMISSION: 'permissions/local-permission/:id',
    ADD_USER_PERMISSION: 'permissions/local-permission',
  },
  CAPABILITY: {
    GET_CAPABILITY_SEARCH_DROPDOWN: '/capability/master/dropdown-location-wise',
    GET_CAPABILITY_MASTER_DETAIL: '/capability/master/detail',
    CAPABILITY_UPSERT: 'capability/upsert',
    CAPABILITY_DETAIL: 'capability/capability-detail/:id',
    CAPABILITY_LIST: 'capability/list',
    CAPABILITY_DELETE: 'capability/:id',
    EXPORT_CAPABILITY_LIST: 'capability/download',
    UPDATE_STATUS: 'capability/:id',
  },
  REPORTS: {
    GET_CUSTOM_REPORTS: 'bingo-card/reports',
    CUSTOM_REPORT_UPSERT: 'bingo-card/report/upsert',
    DELETE_REPORT: 'bingo-card/report/:id',
  },
  BINGO_CARD: {
    GET_ALL_CONFIG: 'bingo-card/configs',
    GET_CAPABILITIES_BY_ID: 'bingo-card/configs/{id}',
    GET_LOCATION_CAPABILITIES: 'bingo-card/configs/{id}/location-capabilities',
  },
  HISTORY: {
    GET_HISTORY_LIST: 'history/:type/:id',
  },
  MASTER_CAPABILITY: {
    dropdownData: 'master-capability/dropdowns',
    addEvidence: 'master-capability/evidence/setup',
    addMasterEntry: 'master-capability/setup',
    MASTER_CAPABILITY_LIST: 'master-capability/list',
    MASTER_EVIDENCES_LIST: 'master-capability/evidences',
    ADD_NEW_MASTER_METADATA: 'master-capability/metadata/setup',
  },
  PEOPLE_PROFILE: {
    GET_PEOPLE_PROFILE_LIST: 'permissions/people-search/:loginId',
  },
  SUPPORT_QUERY: {
    UPSERT_QUERY: 'support-query/help',
  },
};
