import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { LocationService } from '../services';
import { CreateLocationRequestDto, LocationBasicDetailResponseDto, LocationCompleteDetailResponseDto, LocationDropdownResponseDto, PaginatedLocationListResponseDto, UpdateLocationStatusRequestDto } from '../dtos';
import { LocationFilterRequestDto } from '../dtos/request/location-filter-request.dto';
import { LocationCapabilitiesResponseDto } from '../dtos/response/location-capabilities.response.dto';
import { Response } from 'express';
import { ExportLocationWiseCapabilityFilterRequestDto } from 'src/capability/dtos';
export declare class LocationController {
    private readonly locationService;
    constructor(locationService: LocationService);
    getAllLocations(searchTerm?: string): Promise<LocationDropdownResponseDto[]>;
    upsertLocation(request: RequestContext, createLocationDto: CreateLocationRequestDto): Promise<MessageResponseDto>;
    getBasicLocationDetailById(id: number): Promise<LocationBasicDetailResponseDto>;
    getCompleteLocationDetailById(id: number): Promise<LocationCompleteDetailResponseDto>;
    getLocationsByFilter(page?: number, limit?: number, orderBy?: string, orderDirection?: string, filterDto?: LocationFilterRequestDto): Promise<PaginatedLocationListResponseDto>;
    exportLocations(res: Response, request: RequestContext, filterDto?: LocationFilterRequestDto): Promise<Response<any, Record<string, any>>>;
    deleteLocation(request: RequestContext, id: number): Promise<MessageResponseDto>;
    updateLocationStatus(request: RequestContext, id: number, updateStatusRequestDto: UpdateLocationStatusRequestDto): Promise<MessageResponseDto>;
    getLocationWiseCapabilities(locationId: number): Promise<LocationCapabilitiesResponseDto>;
    exportLocationWiseCapabilities(locationId: number, res: Response, request: RequestContext, filterDto?: ExportLocationWiseCapabilityFilterRequestDto): Promise<Response<any, Record<string, any>>>;
}
