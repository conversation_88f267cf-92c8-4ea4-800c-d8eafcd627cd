"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetadataController = void 0;
const common_1 = require("@nestjs/common");
const services_1 = require("../services");
const swagger_1 = require("@nestjs/swagger");
const passport_1 = require("@nestjs/passport");
const dtos_1 = require("../dtos");
let MetadataController = class MetadataController {
    constructor(metadataService) {
        this.metadataService = metadataService;
    }
    getByCoreSolutionId() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.metadataService.getAllCoreSolutions();
        });
    }
    getLocationTypesByCoreSolutionId(coreSolutionId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.metadataService.getLocationTypesByCoreSolutionId(coreSolutionId);
        });
    }
    getAllLocationTypes() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.metadataService.getAllLocationTypes();
        });
    }
    getAllLocationLifeCycleManagementTypes() {
        return __awaiter(this, void 0, void 0, function* () {
            return this.metadataService.getAllLocationLifeCycleTypes();
        });
    }
    getMetadataByType(requestParms) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.metadataService.getMetadataByType(requestParms);
        });
    }
    getUniqueTags(type, searchTerm) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.metadataService.getUniqueTags(type, searchTerm);
        });
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all Capabilities (core solutions) list.',
        type: [dtos_1.CoreSolutionCompleteResponseDto],
    }),
    (0, common_1.Get)('core-solution'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MetadataController.prototype, "getByCoreSolutionId", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all location type by Capabilities (core solutions) id.',
        type: [dtos_1.LocationTypeResponseDto],
    }),
    (0, common_1.Get)('location-type/:coreSolutionId'),
    __param(0, (0, common_1.Param)('coreSolutionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MetadataController.prototype, "getLocationTypesByCoreSolutionId", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all location type.',
        type: [dtos_1.LocationTypeResponseDto],
    }),
    (0, common_1.Get)('all-location-type'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MetadataController.prototype, "getAllLocationTypes", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all location lifecycle management type.',
    }),
    (0, common_1.Get)('all-location-lifecycle-management'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MetadataController.prototype, "getAllLocationLifeCycleManagementTypes", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: `Get metadata or dropdown list by data type: \n\n
        * Possible types - ["TIMEZONE", "CURRENCY", "LANGUAGE", "INDUSTRY_VERTICALS", "SUSTAINABILITY", "STRATEGIC_CLASSIFICATION", "CONTACT_TYPE", "FF_LOCATION_ID_TYPES", "BRANDS", "BRANCH_ARCHETYPE", "ALL_STRATEGIC_CLASSIFICATION"] \n\n
        * Core solution (Capability) id is required for [FF_LOCATION_ID_TYPES] \n\n
        * Core solution (Capability) id & location type id are required for [STRATEGIC_CLASSIFICATION] \n\n
		* Core solution (Capability) id & location type id are required for [CONTACT_TYPE].`,
    }),
    (0, common_1.Post)('common-metadata-type-wise'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.GetMetadataByTypeRequestDto]),
    __metadata("design:returntype", Promise)
], MetadataController.prototype, "getMetadataByType", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all unique tags.',
    }),
    (0, common_1.Get)('unique-tags/:type'),
    __param(0, (0, common_1.Param)('type')),
    __param(1, (0, common_1.Query)('searchTerm')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], MetadataController.prototype, "getUniqueTags", null);
MetadataController = __decorate([
    (0, swagger_1.ApiTags)('MetaData or Dropdown APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('metadata'),
    __metadata("design:paramtypes", [services_1.MetadataService])
], MetadataController);
exports.MetadataController = MetadataController;
//# sourceMappingURL=metadata.controller.js.map