{"version": 3, "sources": ["../../@mui/material/FilledInput/filledInputClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport { inputBaseClasses } from \"../InputBase/index.js\";\nexport function getFilledInputUtilityClass(slot) {\n  return generateUtilityClass('MuiFilledInput', slot);\n}\nconst filledInputClasses = {\n  ...inputBaseClasses,\n  ...generateUtilityClasses('MuiFilledInput', ['root', 'underline', 'input', 'adornedStart', 'adornedEnd', 'sizeSmall', 'multiline', 'hiddenLabel'])\n};\nexport default filledInputClasses;"], "mappings": ";;;;;;;;;AAGO,SAAS,2BAA2B,MAAM;AAC/C,SAAO,qBAAqB,kBAAkB,IAAI;AACpD;AACA,IAAM,qBAAqB;AAAA,EACzB,GAAG;AAAA,EACH,GAAG,uBAAuB,kBAAkB,CAAC,QAAQ,aAAa,SAAS,gBAAgB,cAAc,aAAa,aAAa,aAAa,CAAC;AACnJ;AACA,IAAO,6BAAQ;", "names": []}