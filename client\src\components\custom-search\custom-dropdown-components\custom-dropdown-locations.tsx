import { LOCATION_STATUS_COLOR_ENUM, LOCATION_STATUS_DISPLAY_ENUM, LOCATION_STATUS_ENUM } from '@/shared/enum';
import { LocationRecord } from '@/shared/models';
import { getIcon } from '@/shared/utils/get-icon';
import { Box, Chip, ListItem, ListItemText, Typography } from '@mui/material';
import React from 'react';

interface CustomDropdownLocationsProps {
  option: LocationRecord;
  props: any;
  showDescription?: boolean;
  showChips?: boolean;
  getIconPath: (code: string) => string;
  iconSize?: number;
  listItemStyle?: object;
  chipLabelStyle?: object;
  statusColors: { [key: string]: string };
}

const CustomDropdownLocations: React.FC<CustomDropdownLocationsProps> = ({
  option,
  props,
  showDescription = true,
  showChips = true,
  getIconPath,
  iconSize = 24,
  listItemStyle = {},
  chipLabelStyle = {},
  statusColors,
}) => {
  const { key, ...restOfProps } = props;
  return (
    <ListItem {...restOfProps} sx={{ padding: 1, ...listItemStyle }}>
      <ListItemText
        primary={<Typography sx={{ fontWeight: 'bold' }}>{option.locationName}</Typography>}
        secondary={
          showDescription && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {option?.coreSolution?.code && (
                <img
                  src={getIcon(option?.coreSolution.code)}
                  alt="icon"
                  style={{ width: iconSize, height: iconSize, marginRight: 8 }}
                />
              )}
              <Box>
                <Typography variant="body2">{`${option.entityTitle} ${option?.countryDetail?.entityTitle && option.entityTitle !== option?.countryDetail?.entityTitle ? ', ' + option?.countryDetail?.entityTitle : ''}`}</Typography>
                <Typography variant="body2">{`${option?.coreSolution?.title} - ${option?.locationType?.title}`}</Typography>
              </Box>
            </Box>
          )
        }
      />
      {showChips && (
        <Chip
          size="small"
          label={LOCATION_STATUS_DISPLAY_ENUM[option.status as keyof typeof LOCATION_STATUS_DISPLAY_ENUM]}
          sx={{
            '&:hover': {
              bgcolor: LOCATION_STATUS_COLOR_ENUM[option?.status as LOCATION_STATUS_ENUM],
            },
            backgroundColor: statusColors && option.status ? statusColors[option?.status] : 'gray',
            color: [LOCATION_STATUS_ENUM['IN_ACTIVE'], LOCATION_STATUS_ENUM['OTHER']].includes(option.status)
              ? '#ffffff'
              : '#000000',
            ...chipLabelStyle,
          }}
        />
      )}
    </ListItem>
  );
};

export default CustomDropdownLocations;
