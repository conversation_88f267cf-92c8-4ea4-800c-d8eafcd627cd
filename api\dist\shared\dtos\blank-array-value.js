"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsNotEmptyStringArray = exports.IsNotEmptyStringArrayConstraint = void 0;
const class_validator_1 = require("class-validator");
let IsNotEmptyStringArrayConstraint = class IsNotEmptyStringArrayConstraint {
    validate(users) {
        return Array.isArray(users) && users.every(user => typeof user === 'string' && user.trim().length > 0);
    }
    defaultMessage() {
        return 'Each user must be a non-empty string';
    }
};
IsNotEmptyStringArrayConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)({ async: false })
], IsNotEmptyStringArrayConstraint);
exports.IsNotEmptyStringArrayConstraint = IsNotEmptyStringArrayConstraint;
function IsNotEmptyStringArray(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsNotEmptyStringArrayConstraint,
        });
    };
}
exports.IsNotEmptyStringArray = IsNotEmptyStringArray;
//# sourceMappingURL=blank-array-value.js.map