import { useState } from 'react';
import { FILTER_CONFIG, locationFilterHeaders } from '../../config';

import { useTranslate } from '@/locales/use-locales';

import MobileViewLocationCard from './mobile-view-location-card';

import CustomLocationTableFilterDrawer from './custom-location-filter-drawer';

import { ConfirmDialog } from '@/components/custom-dialog';
import CustomTableFilterChip from '@/components/custom-tables/custom-table-filter-chip';
import MobileViewLocationCardSkeleton from '@/components/skeleton/location-card-skeleton';
import { TableSkeleton } from '@/components/table';
import { useBoolean } from '@/hooks/use-boolean';
import { useResponsive } from '@/hooks/use-responsive';
import { GeneralLocationResponse, KeyValue, LocationListResponse, LocationRecord } from '@/shared/models';
import { queryClient } from '@/shared/services';
import { deleteLocation } from '@/shared/services/location.service';
import { LoadingButton } from '@mui/lab';
import { enqueueSnackbar } from 'notistack';
import { useMutation } from 'react-query';
import LocationListTable from '../new-listing/location-list-table';

interface Props {
  showFilterDrawer: boolean;
  setShowFilterDrawer: React.Dispatch<React.SetStateAction<boolean>>;
  filters: Record<string, KeyValue[]>;
  setFilters: React.Dispatch<React.SetStateAction<Record<string, KeyValue[]>>>;
  locationListData: LocationListResponse | undefined;
  page: number;
  sortBy: string;
  sortDirection: 'asc' | 'desc';
  setSortBy: any;
  setSortDirection: any;
  setPage: React.Dispatch<React.SetStateAction<number>>;
  rowsPerPage: number;
  setRowsPerPage: React.Dispatch<React.SetStateAction<number>>;
  _isLocationListFetching: boolean;
  tempFilters: Record<string, KeyValue[]>;
  setTempFilters: React.Dispatch<React.SetStateAction<Record<string, KeyValue[]>>>;
  showFilterChip: boolean;
  setShowFilterChip: React.Dispatch<React.SetStateAction<boolean>>;
}
const LocationListing = ({
  showFilterDrawer,
  setShowFilterDrawer,
  filters,
  setFilters,
  locationListData,
  page,
  setPage,
  rowsPerPage,
  sortBy,
  sortDirection,
  setSortBy,
  setSortDirection,
  setRowsPerPage,
  _isLocationListFetching,
  tempFilters,
  setTempFilters,
  showFilterChip,
  setShowFilterChip,
}: Props) => {
  const [selectedLocationRecord, setSelectedLocationRecord] = useState<LocationRecord>();
  const isMobile = useResponsive('down', 'sm');
  const confirm = useBoolean();

  const { t } = useTranslate();

  const TABLE_HEADERS = [
    { id: 'locationName', label: t('label.location_name'), enableSort: true },
    { id: 'coreSolution', label: t('label.core_solution'), width: 200, enableSort: true },
    { id: 'locationType', label: t('label.type'), enableSort: true },
    { id: 'locationStatus', label: t('label.status'), enableSort: true },
    // { id: 'statusDate', label: t('label.status_date') },
    { id: 'leaseOwnershipStatus', label: t('label.lease_ownership'), width: 100 },
    { id: 'strategicClassification', label: t('label.strategic_classification'), width: 100 },
    { id: '', label: t('label.action'), width: 88 },
  ];

  const { mutateAsync, isLoading } = useMutation({
    mutationFn: () => {
      return deleteLocation(selectedLocationRecord?.id.toString());
    },
    onSuccess: (response: GeneralLocationResponse) => {
      confirm.onFalse();
      enqueueSnackbar(response.message, {
        variant: 'success',
      });
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
    },
  });

  const paginatedData = locationListData?.records || [];
  const total = locationListData?.total || 0;
  const pageTotal = locationListData?.pageTotal || 0;

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleMobilePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage - 1);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const handleSort = (id: string) => {
    setSortBy(id);
    setSortDirection(sortDirection == 'asc' ? 'desc' : 'asc');
    handleChangePage(null, 0);
  };

  const handleFilterChange = (key: string, value: KeyValue[]) => {
    setTempFilters((prev) => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    const appliedFilters = Object.fromEntries(Object.entries(tempFilters).filter(([_, values]) => values.length > 0));
    setFilters(appliedFilters);
    setPage(0);
    setShowFilterChip(Object.keys(appliedFilters).length > 0);
    setShowFilterDrawer(false);
  };

  const resetFilters = () => {
    setTempFilters({});
    setFilters({});
    setPage(0);
    setShowFilterChip(false);
    setShowFilterDrawer(false);
  };

  const handleClearFilter = (key: string, value?: string | number | null) => {
    setFilters((prev) => {
      const updated = { ...prev };
      if (value !== undefined) {
        updated[key] = updated[key]?.filter((v) => v.label !== value);
      }
      if (!updated[key]?.length) delete updated[key];
      return updated;
    });

    setTempFilters((prev) => {
      const updated = { ...prev };
      if (value !== undefined) {
        updated[key] = updated[key]?.filter((v) => v.label !== value);
      }
      if (!updated[key]?.length) delete updated[key];
      return updated;
    });

    setPage(0);
    const hasFilters = Object.keys(filters).some((key) => filters[key].length > 0);
    setShowFilterChip(hasFilters);
  };

  const handleClearAllFilters = () => {
    setFilters({});
    setTempFilters({});
    setPage(0);
    setShowFilterChip(false);
  };

  const handleDeleteLocation = (id: any) => {
    const record = paginatedData.find((r) => String(r.id) === id);
    if (record) {
      setSelectedLocationRecord(record);
      confirm.onTrue();
    }
  };

  return (
    <>
      {showFilterChip && (
        <CustomTableFilterChip
          filters={filters}
          onClearFilter={handleClearFilter}
          onClearAll={handleClearAllFilters}
          filterConfig={FILTER_CONFIG}
        />
      )}

      {_isLocationListFetching && isMobile ? (
        <MobileViewLocationCardSkeleton />
      ) : (
        isMobile && (
          <MobileViewLocationCard
            data={paginatedData || []}
            count={total}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={handleMobilePageChange}
            onDelete={handleDeleteLocation}
          />
        )
      )}

      {_isLocationListFetching && !isMobile ? (
        <TableSkeleton rowCount={10} colCount={8} colSpacing={1} />
      ) : (
        !isMobile && (
          <LocationListTable
            tableHeaders={TABLE_HEADERS}
            isPaginated={total >= pageTotal || total < rowsPerPage}
            tableData={paginatedData || []}
            rowsPerPage={rowsPerPage}
            totalRows={total}
            page={page}
            sortBy={sortBy}
            sortDirection={sortDirection}
            onSort={handleSort}
            handleChangePage={handleChangePage}
            handleChangeRowsPerPage={handleChangeRowsPerPage}
            onDelete={handleDeleteLocation}
          />
        )
      )}

      <CustomLocationTableFilterDrawer
        open={showFilterDrawer}
        onClose={() => setShowFilterDrawer(false)}
        filters={tempFilters}
        onFilterChange={handleFilterChange}
        onSearch={applyFilters}
        onCancel={resetFilters}
        tableHeaders={locationFilterHeaders}
        tableData={paginatedData || []}
      />

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title={t('label.delete')}
        content={t('messages.are_you_sure_want_to_delete')}
        action={
          <LoadingButton
            loading={isLoading}
            variant="contained"
            onClick={async () => {
              try {
                await mutateAsync();
                await queryClient.invalidateQueries(['locationList']);
                confirm.onFalse();
              } catch (err) {
                console.debug(err);
              }
            }}
          >
            {t('label.delete')}
          </LoadingButton>
        }
      />
    </>
  );
};

export default LocationListing;
