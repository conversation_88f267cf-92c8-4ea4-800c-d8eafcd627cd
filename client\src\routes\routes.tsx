import { lazy, Suspense } from 'react';
import { Outlet, useRoutes } from 'react-router-dom';

import { LoadingScreen } from '../components/loading-screen';
import DashboardLayout from '../layouts/dashboard';

// Paths
import HomePage from '@/modules/home/<USER>/home';
import ReportRoutes from '@/modules/reports/routes/report-routes';
import { paths } from './paths';

// Pages
const CapabilityRoutes = lazy(() => import('@/modules/capabilities/routes/capability-routes'));
const UserAccessRoutes = lazy(() => import('@/modules/user-access/routes/user-access-routes'));
const LocationSetupRoutes = lazy(() => import('@/modules/location-setup/routes/location-setup-routes'));
const LocationRoutes = lazy(() => import('@/modules/locations/routes/location-routes'));
const BingoCardRoutes = lazy(() => import('@/modules/bingo-card/routes/bingo-card-routes'));
const MasterCapabilitiesRoutes = lazy(() => import('@/modules/master-capabilities/routes/master-capability-routes'));
const PeopleProfileRoutes = lazy(() => import('@/modules/people-search/routes/people-profile-routes'));
const PageNotFound = lazy(() => import('@/components/error/not-found-view'));

export default function Router() {
  return useRoutes([
    {
      path: paths.home.root,
      element: (
        <DashboardLayout>
          <Suspense fallback={<LoadingScreen />}>
            <Outlet />
          </Suspense>
        </DashboardLayout>
      ),
      children: [
        { path: paths.home.root, index: true, element: <HomePage /> },
        { path: paths.locationSetup.root + '/*', element: <LocationSetupRoutes /> },
        { path: paths.capabilities.root + '/*', element: <CapabilityRoutes /> },
        { path: paths.masterCapabilities.root + '/*', element: <MasterCapabilitiesRoutes /> },
        { path: paths.locations.root + '/*', element: <LocationRoutes /> },
        { path: paths.userAccess.root + '/*', element: <UserAccessRoutes /> },
        { path: paths.bingoCard.root + '/*', element: <BingoCardRoutes /> },
        { path: paths.reports.root + '/*', element: <ReportRoutes /> },

        { path: paths.peopleProfile.root + '/*', element: <PeopleProfileRoutes /> },
        { path: '*', element: <PageNotFound /> },
      ],
    },
    { path: '/*', element: <PageNotFound /> },
  ]);
}
