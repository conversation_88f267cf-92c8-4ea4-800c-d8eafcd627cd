"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationWiseCapabilityRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("sequelize");
const models_1 = require("../../metadata/models");
const repositories_1 = require("../../shared/repositories");
const models_2 = require("../models");
const models_3 = require("../../location/models");
const master_capability_category_model_1 = require("../models/master-capability-category.model");
let LocationWiseCapabilityRepository = class LocationWiseCapabilityRepository extends repositories_1.BaseRepository {
    constructor() {
        super(models_2.LocationWiseCapabilityDetail);
    }
    deleteByLocationId(locationId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.deleteByCondition({ locationId }, currentContext);
        });
    }
    getLocationWiseCapabilityDetail(locationId, capabilityId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: { locationId, capabilityId },
            });
        });
    }
    isCapabilityDetailExist(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.isRecordExist({ where: { id } });
        });
    }
    getExistingCapabilityDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                attributes: ['id', 'locationId', 'capabilityId', 'status', 'statusDate'],
                where: { id },
                include: [
                    {
                        model: models_3.Location,
                        as: 'locationDetail',
                        required: true,
                        attributes: ['id', 'locationName', 'entityId'],
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                ],
            });
        });
    }
    getLocationWiseCapabilityDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: { id },
                include: [
                    {
                        model: models_3.Location,
                        as: 'locationDetail',
                        required: true,
                        attributes: [
                            'id',
                            'locationName',
                            'entityId',
                            'entityTitle',
                            'status',
                            'leaseOwnershipStatus',
                            'statusDate',
                        ],
                        where: {
                            active: true,
                            deleted: false,
                        },
                        include: [
                            {
                                model: models_1.CoreSolution,
                                as: 'coreSolution',
                                attributes: ['id', 'title', 'code', 'pillar'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                            {
                                model: models_1.LocationType,
                                as: 'locationType',
                                attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                    },
                    {
                        model: models_2.MasterCapability,
                        as: 'capabilityDetail',
                        required: true,
                        attributes: [
                            'id',
                            'capability',
                            'subCategory',
                            'product',
                            'capabilityType',
                            'level',
                            'verticals',
                            'isEvidenceMandatory',
                            'otherDetails',
                            'legs',
                        ],
                        include: [
                            {
                                model: models_2.MetaEvidence,
                                as: 'evidence',
                                attributes: ['id', 'name', 'description', 'code', 'type'],
                            },
                            {
                                model: master_capability_category_model_1.CapabilityCategory,
                                as: 'category',
                                attributes: ['title'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                ],
            });
        });
    }
    addNewCapability(payload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const capability = new models_2.LocationWiseCapabilityDetail(payload);
            return this.save(capability, currentContext);
        });
    }
    updateCapabilityDetailById(capabilityId, data, currentContext) {
        return this.update(data, currentContext, {
            where: {
                id: capabilityId,
            },
        });
    }
    deleteLocationCapability(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.deleteByCondition({ id }, currentContext);
        });
    }
    getCapabilitiesByFilter(filterDto, page, limit) {
        return __awaiter(this, void 0, void 0, function* () {
            const whereClause = this.buildWhereClause(filterDto);
            return this.findAndCountAll(Object.assign(Object.assign(Object.assign({ where: whereClause, include: [
                    {
                        model: models_3.Location,
                        as: 'locationDetail',
                        required: true,
                        attributes: ['id', 'locationName', 'entityId', 'entityTitle'],
                        where: {
                            active: true,
                            deleted: false,
                        },
                        include: [
                            {
                                model: models_1.CoreSolution,
                                as: 'coreSolution',
                                attributes: ['id', 'title', 'code', 'pillar'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                    },
                    {
                        model: models_2.MasterCapability,
                        as: 'capabilityDetail',
                        required: true,
                        attributes: [
                            'id',
                            'capability',
                            'subCategory',
                            'product',
                            'capabilityType',
                            'level',
                            'verticals',
                            'otherDetails',
                        ],
                        include: [
                            {
                                model: models_1.CoreSolution,
                                as: 'coreSolution',
                                attributes: ['id', 'title', 'code', 'pillar'],
                                required: false,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                            {
                                model: master_capability_category_model_1.CapabilityCategory,
                                as: 'category',
                                attributes: ['title'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                ] }, (page && { offset: (page - 1) * limit })), (limit && { limit })), { order: [['updatedOn', 'DESC']], distinct: true }));
        });
    }
    buildWhereClause(filterDto) {
        const { capabilityLevel, capabilityTypes, coreSolutionIds, entityIds, statuses, providers, verticalCodes, ownerLoginId, searchTerm, entryIds } = filterDto;
        const whereClause = {};
        const andCondition = [];
        if (searchTerm && searchTerm.length) {
            andCondition.push({
                [sequelize_1.Op.or]: [
                    (0, sequelize_1.literal)(`"capabilityDetail"."capability" ILIKE '%${searchTerm}%'`),
                    (0, sequelize_1.literal)(`"capabilityDetail"."subcategory" ILIKE '%${searchTerm}%'`),
                    (0, sequelize_1.literal)(`"capabilityDetail"."product" ILIKE '%${searchTerm}%'`),
                ],
            });
        }
        if (statuses && statuses.length) {
            whereClause.status = { [sequelize_1.Op.in]: statuses };
        }
        if (entryIds && entryIds.length) {
            whereClause.capabilityId = { [sequelize_1.Op.in]: entryIds };
        }
        if (providers && providers.length) {
            whereClause.provider = { [sequelize_1.Op.in]: providers };
        }
        if (ownerLoginId && ownerLoginId.length) {
            andCondition.push((0, sequelize_1.where)((0, sequelize_1.fn)('LOWER', (0, sequelize_1.fn)('jsonb_extract_path_text', (0, sequelize_1.col)('"LocationWiseCapabilityDetail"."other_details"'), 'owners', 'loginId')), { [sequelize_1.Op.iLike]: `%${ownerLoginId.toLowerCase()}%` }));
        }
        if (capabilityLevel && capabilityLevel.length) {
            andCondition.push({
                '$capabilityDetail.level$': {
                    [sequelize_1.Op.in]: capabilityLevel,
                },
            });
        }
        if (capabilityTypes && capabilityTypes.length) {
            andCondition.push({
                '$capabilityDetail.capability_type$': {
                    [sequelize_1.Op.in]: capabilityTypes,
                },
            });
        }
        if (coreSolutionIds && coreSolutionIds.length) {
            andCondition.push({
                [sequelize_1.Op.or]: [
                    { '$capabilityDetail.core_solution_id$': { [sequelize_1.Op.in]: coreSolutionIds } },
                    { '$capabilityDetail.core_solution_id$': null }
                ]
            });
        }
        if (verticalCodes && verticalCodes.length) {
            andCondition.push({
                [sequelize_1.Op.and]: [
                    {
                        [sequelize_1.Op.or]: verticalCodes.map(code => (0, sequelize_1.where)((0, sequelize_1.col)('capabilityDetail.verticals'), sequelize_1.Op.contains, JSON.stringify([code]))),
                    },
                    {
                        '$capabilityDetail.verticals$': {
                            [sequelize_1.Op.not]: null,
                        },
                    },
                ],
            });
        }
        if (entityIds && (entityIds === null || entityIds === void 0 ? void 0 : entityIds.length)) {
            andCondition.push({
                '$locationDetail.entity_id$': {
                    [sequelize_1.Op.in]: entityIds,
                },
            });
        }
        if (entityIds && (entityIds === null || entityIds === void 0 ? void 0 : entityIds.length)) {
            andCondition.push({
                '$locationDetail.entity_id$': {
                    [sequelize_1.Op.in]: entityIds,
                },
            });
        }
        if (andCondition.length > 0) {
            whereClause[sequelize_1.Op.and] = andCondition;
        }
        return whereClause;
    }
    getCapabilitiesToExport(filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const whereClause = this.buildWhereClause(filterDto);
            return this.findAll({
                where: whereClause,
                include: [
                    {
                        model: models_3.Location,
                        as: 'locationDetail',
                        required: true,
                        attributes: ['id', 'locationName', 'entityId', 'entityTitle'],
                        include: [
                            {
                                model: models_1.CoreSolution,
                                as: 'coreSolution',
                                attributes: ['id', 'title', 'code', 'pillar'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                        where: {
                            active: true,
                            deleted: false,
                        },
                    },
                    {
                        model: models_2.MasterCapability,
                        as: 'capabilityDetail',
                        required: true,
                        attributes: [
                            'id',
                            'capability',
                            'subCategory',
                            'product',
                            'capabilityType',
                            'level',
                            'verticals',
                            'otherDetails',
                        ],
                        where: {
                            active: true,
                            deleted: false,
                        },
                        include: [
                            {
                                model: master_capability_category_model_1.CapabilityCategory,
                                as: 'category',
                                attributes: ['title'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                    },
                ],
                distinct: true,
            });
        });
    }
    getCapabilitiesByLocationId(locationId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findAll({
                where: {
                    locationId,
                },
                include: [
                    {
                        model: models_2.MasterCapability,
                        as: 'capabilityDetail',
                        required: true,
                        attributes: [
                            'id',
                            'capability',
                            'subCategory',
                            'product',
                            'capabilityType',
                            'level',
                            'verticals',
                            'otherDetails',
                        ],
                        where: {
                            active: true,
                            deleted: false,
                        },
                        include: [
                            {
                                model: master_capability_category_model_1.CapabilityCategory,
                                as: 'category',
                                attributes: ['title'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                    },
                ],
                order: [['updatedOn', 'DESC']],
                distinct: true,
            });
        });
    }
    exportCapabilitiesByLocationId(locationId, filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            let whereClause = this.buildWhereClause(filterDto);
            if (whereClause) {
                whereClause.locationId = locationId;
            }
            else {
                whereClause = { locationId };
            }
            return this.findAll({
                where: whereClause,
                include: [
                    {
                        model: models_2.MasterCapability,
                        as: 'capabilityDetail',
                        required: true,
                        attributes: [
                            'id',
                            'capability',
                            'subCategory',
                            'product',
                            'capabilityType',
                            'level',
                            'verticals',
                            'otherDetails',
                        ],
                        where: {
                            active: true,
                            deleted: false,
                        },
                        include: [
                            {
                                model: master_capability_category_model_1.CapabilityCategory,
                                as: 'category',
                                attributes: ['title'],
                                required: true,
                                where: {
                                    active: true,
                                    deleted: false,
                                },
                            },
                        ],
                    },
                ],
                order: [['updatedOn', 'DESC']],
                distinct: true,
            });
        });
    }
};
LocationWiseCapabilityRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], LocationWiseCapabilityRepository);
exports.LocationWiseCapabilityRepository = LocationWiseCapabilityRepository;
//# sourceMappingURL=location-wise-capability.repository.js.map