import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/shared/repositories';
import { CommonDropdown } from '../models';
import { GetMetadataByTypeRequestDto } from '../dtos';
import { Op } from 'sequelize';
import { COMMON_DROPDOWN_TYPE } from 'src/shared/enums';
import { CurrentContext } from 'src/shared/types';

@Injectable()
export class CommonDropdownRepository extends BaseRepository<CommonDropdown> {
	constructor() {
		super(CommonDropdown);
	}

	public async setupNewMetadataOfCommonTypes(payload: any, currentContext: CurrentContext) {
		const newMetadata = new CommonDropdown(payload);

		return this.save(newMetadata, currentContext);
	}

	public getDropdownList(filter: GetMetadataByTypeRequestDto): Promise<CommonDropdown[] | null> {
		// Filter for type which need type with core solution check.
		const typeWithCoreSolution = filter.type.includes(COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES)
			? [COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES]
			: [];

		//Filter for type which need type with core solution and location type check.
		// const typeWithCoreSolutionNLocationType = filter.type.filter((type) =>
		// 	[COMMON_DROPDOWN_TYPE.CONTACT_TYPE].includes(type),
		// );

		//Filter for type which need type check only.
		let listWithOnlyType = filter.type.filter(
			type =>
				![
					// COMMON_DROPDOWN_TYPE.CONTACT_TYPE,
					COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION,
					COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES,
				].includes(type),
		);

		// Build query conditions dynamically
		const orConditions: any[] = [];

		// Add condition for type and core solution id required
		if (typeWithCoreSolution.length > 0 && filter.coreSolutionId) {
			orConditions.push({
				type: { [Op.in]: typeWithCoreSolution },
				coreSolutionId: filter.coreSolutionId,
			});
		}

		// Add condition for type, core solution id and location type id required.
		// if (typeWithCoreSolutionNLocationType.length > 0 && filter.coreSolutionId && filter.locationTypeId) {
		// 	orConditions.push({
		// 		type: { [Op.in]: typeWithCoreSolutionNLocationType },
		// 		coreSolutionId: filter.coreSolutionId,
		// 		locationTypeId: filter.locationTypeId,
		// 	});
		// }

		// Add condition for metadata where only types required.
		if (listWithOnlyType.length > 0) {
			if (listWithOnlyType.includes(COMMON_DROPDOWN_TYPE.ALL_STRATEGIC_CLASSIFICATION)) {
				listWithOnlyType = listWithOnlyType.filter(
					type => type !== COMMON_DROPDOWN_TYPE.ALL_STRATEGIC_CLASSIFICATION,
				);
				listWithOnlyType.push(COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION);
			}

			orConditions.push({
				type: { [Op.in]: listWithOnlyType },
			});
		}

		// Execute the query with dynamically built conditions
		return this.findAll({
			where: {
				[Op.or]: orConditions,
			},
			order: [['title', 'asc']],
		});
	}

	public async getStrategicClassificationType(
		coreSolutionId: number,
		locationTypeId: number,
	): Promise<CommonDropdown[] | null> {
		// const locationOverridenData = await this.findAll({
		// 	where: {
		// 		type: COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION,
		// 		coreSolutionId: coreSolutionId,
		// 		locationTypeId: locationTypeId,
		// 	},
		// 	order: [['title', 'asc']],
		// });

		// if (locationOverridenData.length > 0) {
		// 	return locationOverridenData;
		// }

		// const coreSolutionBasedData = await this.findAll({
		// 	where: {
		// 		type: COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION,
		// 		coreSolutionId: coreSolutionId,
		// 		locationTypeId: null,
		// 	},
		// 	order: [['title', 'asc']],
		// });

		// return coreSolutionBasedData;

		const strategicClassificationData = await this.findAll({
			where: {
				type: COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION,
				coreSolutionId: { [Op.or]: [coreSolutionId, null] },
				locationTypeId: { [Op.or]: [locationTypeId, null] },
			},
			order: [['title', 'asc']],
		});


		// Prioritize location-specific data if available
		const locationOverridenData = strategicClassificationData.filter(
			item => item.locationTypeId === locationTypeId,
		);

		const commonList = strategicClassificationData.filter(item => item.coreSolutionId === null);

		if (locationOverridenData.length > 0) {
			return [...locationOverridenData, ...commonList];
		}

		// Otherwise return core solution based data
		return strategicClassificationData.filter(item => item.locationTypeId === null);
	}

	public isDropdownRecordExists(condition: any) {
		return this.isRecordExist({ where: condition });
	}
}
