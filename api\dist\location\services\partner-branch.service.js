"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerBranchService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const dtos_1 = require("../dtos");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const clients_1 = require("../../shared/clients");
let PartnerBranchService = class PartnerBranchService {
    constructor(permissionService, partnerBranchRepository, locationRepository, historyService, databaseHelper) {
        this.permissionService = permissionService;
        this.partnerBranchRepository = partnerBranchRepository;
        this.locationRepository = locationRepository;
        this.historyService = historyService;
        this.databaseHelper = databaseHelper;
    }
    newPartnerBranch(createPartnerBranchDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { locationId, partnerLocationId, relationship, note } = createPartnerBranchDto;
            if (locationId === partnerLocationId) {
                throw new common_1.HttpException('You cannot connect to yourself. ', common_1.HttpStatus.BAD_REQUEST);
            }
            const locationDetail = yield this.locationRepository.getLocationDetailById(locationId);
            if (!locationDetail) {
                throw new common_1.HttpException('Location doesn\'t exist.', common_1.HttpStatus.NOT_FOUND);
            }
            const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, locationDetail.entityId, locationDetail.id);
            if (!hasPermission) {
                throw new common_1.HttpException('You don\'t have permission to create partner branch for this location.', common_1.HttpStatus.FORBIDDEN);
            }
            const partnerLocationDetail = yield this.locationRepository.getLocationDetailById(partnerLocationId);
            if (!partnerLocationDetail) {
                throw new common_1.HttpException('Partner location doesn\'t exist.', common_1.HttpStatus.NOT_FOUND);
            }
            const existingConnection = yield this.partnerBranchRepository.isConnectionExist(locationId, partnerLocationId);
            if (existingConnection) {
                throw new common_1.HttpException('Connection already exist.', common_1.HttpStatus.CONFLICT);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const newConnection = yield this.partnerBranchRepository.createPartnerBranch({
                    locationId,
                    partnerLocationId,
                    relationship,
                    note: note || null,
                }, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: locationId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.PARTNER_REQUEST,
                    action_date: new Date(),
                    comments: `New partner branch connection request sent to ${partnerLocationDetail.locationName}.`,
                    additional_info: {
                        connectionData: createPartnerBranchDto
                    }
                });
                return { message: 'New connection request sent successfully.', data: newConnection };
            }));
        });
    }
    getPartnerBranches(locationId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const currentLocationDetail = yield this.locationRepository.getLocationDetailById(locationId);
            const hasUserPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, currentLocationDetail.entityId, currentLocationDetail.id);
            const partnerBranches = yield this.partnerBranchRepository.getPartnerBranches([locationId], hasUserPermission ? [enums_1.PARTNER_BRANCH_STATUS_TYPE.REJECTED] : [enums_1.PARTNER_BRANCH_STATUS_TYPE.REJECTED, enums_1.PARTNER_BRANCH_STATUS_TYPE.PENDING]);
            const directPartnerLocationIds = [];
            let directPartners = partnerBranches.map((item) => {
                const isSource = item.locationId === locationId;
                const partnerLocation = isSource ? item.partnerLocation : item.location;
                if (partnerLocation === null || partnerLocation === void 0 ? void 0 : partnerLocation.id) {
                    directPartnerLocationIds.push(partnerLocation === null || partnerLocation === void 0 ? void 0 : partnerLocation.id);
                }
                return Object.assign(Object.assign({ id: item.id, relationship: item.relationship, note: item.note, status: item.status }, this.mapLocationDetail(partnerLocation)), { allowedAction: !isSource, partners: [] });
            });
            let secondConnections = [];
            if (directPartnerLocationIds.length) {
                secondConnections = yield this.partnerBranchRepository.getPartnerBranches(directPartnerLocationIds, [enums_1.PARTNER_BRANCH_STATUS_TYPE.REJECTED, enums_1.PARTNER_BRANCH_STATUS_TYPE.PENDING], [locationId]);
            }
            const secondConnectionMap = new Map();
            for (const connection of secondConnections) {
                const ids = [connection.locationId, connection.partnerLocationId];
                for (const id of ids) {
                    if (!secondConnectionMap.has(id)) {
                        secondConnectionMap.set(id, []);
                    }
                    secondConnectionMap.get(id).push(connection);
                }
            }
            for (const partnerDetail of directPartners) {
                const relatedConnections = secondConnectionMap.get(partnerDetail.locationId) || [];
                partnerDetail.partners = relatedConnections.map((partner) => {
                    const isSource = partner.locationId === partnerDetail.locationId;
                    const partnerLocation = isSource ? partner.partnerLocation : partner.location;
                    return Object.assign(Object.assign({ id: partner.id, relationship: partner.relationship, note: partner.note, status: partner.status, allowedAction: false }, this.mapLocationDetail(partnerLocation)), { partners: [] });
                });
            }
            return (0, helpers_1.multiObjectToInstance)(dtos_1.PartnerBranchResponseDto, directPartners);
        });
    }
    mapLocationDetail(locationDetail) {
        var _a, _b;
        return {
            locationId: locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.id,
            locationName: locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.locationName,
            entityTitle: locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.entityTitle,
            locationStatus: locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.status,
            locationStatusDate: locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.statusDate,
            locationType: (_a = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.locationType) === null || _a === void 0 ? void 0 : _a.title,
            coreSolution: (_b = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.coreSolution) === null || _b === void 0 ? void 0 : _b.title,
        };
    }
    actionPartnerConnection(actionDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, action } = actionDto;
            const connectionDetail = yield this.partnerBranchRepository.getPartnerBranchById(id);
            if (!connectionDetail) {
                throw new common_1.HttpException('Connection not found.', common_1.HttpStatus.NOT_FOUND);
            }
            if (connectionDetail.status !== enums_1.PARTNER_BRANCH_STATUS_TYPE.PENDING) {
                throw new common_1.HttpException('Connection is not pending.', common_1.HttpStatus.BAD_REQUEST);
            }
            const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, connectionDetail.location.entityId, connectionDetail.locationId);
            if (!hasPermission) {
                throw new common_1.HttpException(`You don't have permission to ${action.toLowerCase()} this connection.`, common_1.HttpStatus.FORBIDDEN);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.partnerBranchRepository.updatePartnerBranchById(id, {
                    status: action,
                }, currentContext);
                let history = [{
                        created_by: currentContext.user.username,
                        entity_id: connectionDetail.locationId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE[action],
                        action_date: new Date(),
                        comments: `Connection request ${action.toLowerCase()} for ${connectionDetail.partnerLocation.locationName}.`,
                    }, {
                        created_by: currentContext.user.username,
                        entity_id: connectionDetail.partnerLocationId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                        action_performed: enums_1.HISTORY_ACTION_TYPE[action],
                        action_date: new Date(),
                        comments: `Connection request ${action.toLowerCase()} for ${connectionDetail.location.locationName}.`,
                    }];
                yield this.historyService.addBulkRequestHistory(history);
                return { message: `Connection ${action.toLowerCase()} successfully.` };
            }));
        });
    }
};
PartnerBranchService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [services_1.SharedPermissionService,
        repositories_1.PartnerBranchRepository,
        repositories_1.LocationRepository,
        clients_1.HistoryApiClient,
        helpers_1.DatabaseHelper])
], PartnerBranchService);
exports.PartnerBranchService = PartnerBranchService;
//# sourceMappingURL=partner-branch.service.js.map