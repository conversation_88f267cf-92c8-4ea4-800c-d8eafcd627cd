/* eslint-disable @typescript-eslint/no-explicit-any */
import { CustomMessageView, NotFoundView, View403, View500 } from '@/components/error';
import { enqueueSnackbar } from 'notistack';
import React, { Component, ErrorInfo } from 'react';
import { BrowserAuthError, InteractionRequiredAuthError } from '@azure/msal-browser';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  statusCode: null | number;
  isAuthError: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, statusCode: null, isAuthError: false };
  }

  componentDidCatch(error: any, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error: ', error, errorInfo);

    // Check if it's an MSAL authentication error
    const isAuthError = error instanceof BrowserAuthError ||
                       error instanceof InteractionRequiredAuthError ||
                       error?.message?.includes('monitor_window_timeout') ||
                       error?.message?.includes('Token acquisition') ||
                       error?.errorCode === 'monitor_window_timeout';

    if (isAuthError) {
      console.warn('Authentication error detected, attempting to refresh page');
      // For auth errors, try to refresh the page to trigger re-authentication
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    }

    this.setState({
      hasError: true,
      statusCode: error.statusCode,
      isAuthError
    });
  }

  render() {
    if (this.state.isAuthError) {
      return (
        <CustomMessageView
          showImage={true}
          showButton={false}
          heading="Authentication Session Expired"
          message="Your session has expired. The page will refresh automatically to re-authenticate you."
        />
      );
    } else if (this.state.statusCode === 500) {
      return <View500 />;
    } else if (this.state.statusCode === 404) {
      return <NotFoundView />;
    } else if (this.state.statusCode === 403 || this.state.statusCode === 401) {
      return <View403 />;
    } else if (this.state.statusCode === 503) {
      enqueueSnackbar('Server is currently unable!', { variant: 'error' });
      return (
        <CustomMessageView
          showImage={true}
          showButton={true}
          heading="Server is currently unable!"
          message="Please try after sometime."
        />
      );
    } else if (this.state.hasError) {
      enqueueSnackbar('Something went wrong!', { variant: 'error' });
      return (
        <CustomMessageView
          showImage={true}
          showButton={true}
          heading="Opps, that's our bad!"
          message="We're not exactly sure what happended, but something went wrong."
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
