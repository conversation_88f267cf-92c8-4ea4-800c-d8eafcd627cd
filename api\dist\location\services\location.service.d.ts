import { LocationRepository, LocationIndustryVerticalRepository, PartnerBranchRepository } from '../repositories';
import { CurrentContext, RequestContext } from 'src/shared/types';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { CreateLocationRequestDto, LocationBasicDetailResponseDto, LocationCompleteDetailResponseDto, LocationDropdownResponseDto, PaginatedLocationListResponseDto, UpdateLocationStatusRequestDto } from '../dtos';
import { SetupNewLocationRequest } from '../validators';
import { ContactDetailRepository } from 'src/contact-details/repositories';
import { LocationLifecycleManagementsTypeRepository, LocationTypeRepository } from 'src/metadata/repositories';
import { LocationFilterRequestDto } from '../dtos/request/location-filter-request.dto';
import { ExcelSheetService, SharedAttachmentService, SharedPermissionService } from 'src/shared/services';
import { AdminApiClient, AttachmentApiClient, HistoryApiClient } from 'src/shared/clients';
import { LocationWiseCapabilityRepository } from 'src/capability/repositories/location-wise-capability.repository';
import { LocationCapabilitiesResponseDto } from '../dtos/response/location-capabilities.response.dto';
import { BusinessEntityService } from 'src/business-entity/services';
import { ExportLocationWiseCapabilityFilterRequestDto } from 'src/capability/dtos';
import { UserPermissionRepository } from 'src/permission/repositories';
export declare class LocationService {
    private readonly locationRepository;
    private readonly locationIndustryVerticalRepository;
    private readonly setupNewLocationRequest;
    private readonly databaseHelper;
    private readonly contactDetailRepository;
    private readonly locationLifecycleManagementsTypeRepository;
    private readonly sharedAttachmentService;
    private readonly attachmentApiClient;
    private readonly locationWiseCapabilityRepository;
    private readonly businessEntityService;
    private readonly partnerBranchRepository;
    private readonly excelSheetService;
    private readonly adminApiClient;
    private readonly permissionService;
    private readonly userPermissionRepository;
    private readonly historyService;
    private readonly locationTypeRepository;
    constructor(locationRepository: LocationRepository, locationIndustryVerticalRepository: LocationIndustryVerticalRepository, setupNewLocationRequest: SetupNewLocationRequest, databaseHelper: DatabaseHelper, contactDetailRepository: ContactDetailRepository, locationLifecycleManagementsTypeRepository: LocationLifecycleManagementsTypeRepository, sharedAttachmentService: SharedAttachmentService, attachmentApiClient: AttachmentApiClient, locationWiseCapabilityRepository: LocationWiseCapabilityRepository, businessEntityService: BusinessEntityService, partnerBranchRepository: PartnerBranchRepository, excelSheetService: ExcelSheetService, adminApiClient: AdminApiClient, permissionService: SharedPermissionService, userPermissionRepository: UserPermissionRepository, historyService: HistoryApiClient, locationTypeRepository: LocationTypeRepository);
    getAllLocations(searchTerm?: string): Promise<LocationDropdownResponseDto[]>;
    upsertLocation(createLocationDto: CreateLocationRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    private addNewLocation;
    private updateLocation;
    private updateIndustryVerticals;
    private updateContacts;
    private transformLocationData;
    private addNewIndutryVertical;
    private addNewContactManagement;
    private uploadNewLocationDocuments;
    getBasicLocationDetailById(id: number): Promise<LocationBasicDetailResponseDto>;
    getCompleteLocationDetailById(id: number): Promise<LocationCompleteDetailResponseDto>;
    getLocationListByFilter(page: number, limit: number, orderBy: string, orderDirection: string, filterDto?: LocationFilterRequestDto): Promise<PaginatedLocationListResponseDto>;
    deleteLocation(id: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateLocationStatus(id: number, updateStatusRequestDto: UpdateLocationStatusRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getLocationWiseCapabilities(locationId: number): Promise<LocationCapabilitiesResponseDto>;
    exportLocations(requestContext: RequestContext, filterDto?: LocationFilterRequestDto): Promise<{
        report: any;
        filename: string;
    }>;
    exportLocationWiseCapabilities(locationId: number, requestContext: RequestContext, filterDto?: ExportLocationWiseCapabilityFilterRequestDto): Promise<{
        report: any;
        filename: string;
    }>;
}
