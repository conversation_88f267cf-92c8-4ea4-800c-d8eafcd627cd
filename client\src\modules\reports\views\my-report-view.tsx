import LoadingScreen from '@/components/loading-screen/loading-screen';
import { getCustomReports } from '@/shared/services';
import { useQuery } from 'react-query';
import ReportCard from '../components/report-card';
import { Box, Grid, Stack } from '@mui/system';
import { useNavigate } from 'react-router';
import { useTranslate } from '@/locales/use-locales';
import CustomBreadcrumbs from '@/components/custom-breadcrumbs';
import { paths } from '@/routes/paths';
import { getIcon } from '@/shared/utils/get-icon';
import { Button, Card, Typography } from '@mui/material';
import { useBoolean } from '@/hooks/use-boolean';
import ModifyReport from '../components/modify-report';
import { useContext, useState } from 'react';
import { ReportResponse } from '@/shared/models/report.model';
import { UserPermissionsContext } from '@/core/contexts';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { useMsal } from '@azure/msal-react';
import EmptyContent from '@/components/empty-content';
import { CARD_STYLE } from '@/modules/location-setup/components/location-details/sections';

export default function MyReportView() {
  const { data: reports, isLoading } = useQuery(['reports'], () => getCustomReports());
  const navigate = useNavigate();

  const [selectedReport, setSelectedReport] = useState<null | ReportResponse>(null);
  const userPermissions = useContext(UserPermissionsContext);
  const canDelete = userPermissions.some((permission) => permission.permissionName == PERMISSIONS.APPLICATION_ADMIN);
  const { t } = useTranslate();
  const confirm = useBoolean();
  const deleteConfirm = useBoolean();

  const CSSMasonryLayout = ({ children }: { children: React.ReactNode[] }) => {
    return (
      <Box
        sx={{
          columnCount: { xs: 1, md: 2 },
          columnGap: 2,
          columnFill: 'balance',
          '& > *': {
            breakInside: 'avoid',
            marginBottom: 2,
            display: 'inline-block',
            width: '100%',
          },
        }}
      >
        {children}
      </Box>
    );
  };

  const { accounts } = useMsal();
  const handleClose = () => {
    // setAnchorEl(null);
  };
  const renderHeader = () => {
    return (
      <Grid size={12}>
        <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pr: 1 }}>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent={'space-between'} alignItems={'center'}>
            <CustomBreadcrumbs
              heading={t('label.reports')}
              links={[
                { name: t('label.home'), href: paths.root },
                { name: t('label.reports'), href: paths.reports.root },
              ]}
            />
            <Stack direction={'row'} spacing={2}>
              <Box
                component="span"
                display="flex"
                alignItems="center"
                sx={{ cursor: 'pointer', gap: 1, mr: 1 }}
                alignSelf={'center'}
                onClick={() => {
                  navigate(-1);
                }}
              >
                <Box component="img" src={getIcon('backBtn')} sx={{ width: 24, height: 16 }} />
                <Typography variant="value">{t('btn_name.back')}</Typography>
              </Box>
              <Button
                variant="contained"
                onClick={async () => {
                  setSelectedReport(null);
                  confirm.onTrue();
                }}
                sx={{ alignSelf: 'flex-end' }}
              >
                {t('btn_name.new_report')}
              </Button>
            </Stack>
          </Stack>
        </Card>
      </Grid>
    );
  };
  const renderContent = () => {
    if (isLoading)
      return (
        <Card
          sx={{ border: 'solid 1px #E8D6D6', borderRadius: '7px', paddingTop: 2, minHeight: '46vh', width: '100%' }}
        >
          <LoadingScreen></LoadingScreen>
        </Card>
      );
    if (reports?.length == 0) {
      return (
        <Grid size={12}>
          <Card sx={{ ...CARD_STYLE, minHeight: '46vh', alignContent: 'center' }}>
            <EmptyContent title={t('messages.no_reports_available')} />
          </Card>
        </Grid>
      );
    }
    const list = reports?.map((item) => (
      <ReportCard
        accounts={accounts}
        report={item}
        confirm={confirm}
        deleteConfirm={deleteConfirm}
        handleClose={handleClose}
        key={item.id}
        canDelete={canDelete}
        canEdit={item.createdBy == accounts[0].username || canDelete}
      />
    ));
    return (
      <Box overflow="hidden" flex={1}>
        <CSSMasonryLayout>{list ?? []}</CSSMasonryLayout>
      </Box>
    );
  };
  return (
    <Box px={2}>
      <Grid container size={12} spacing={2}>
        {renderHeader()}
        {renderContent()}
      </Grid>
      <ModifyReport
        initialData={selectedReport ?? undefined}
        open={confirm.value}
        onClose={() => {
          confirm.onFalse();
          setSelectedReport(null);
        }}
      />

      {/* Delete Dialog */}
    </Box>
  );
}
