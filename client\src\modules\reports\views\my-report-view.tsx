import LoadingScreen from '@/components/loading-screen/loading-screen';
import { deleteReport, getCustomReports, queryClient } from '@/shared/services';
import { useMutation, useQuery } from 'react-query';
import ReportCard from '../components/report-card';
import { Box, Grid, Stack } from '@mui/system';
import { useNavigate } from 'react-router';
import { useTranslate } from '@/locales/use-locales';
import CustomBreadcrumbs from '@/components/custom-breadcrumbs';
import { paths } from '@/routes/paths';
import { getIcon } from '@/shared/utils/get-icon';
import { Button, Card, MenuItem, Popover, Typography } from '@mui/material';
import { useBoolean } from '@/hooks/use-boolean';
import ModifyReport from '../components/modify-report';
import { useContext, useState } from 'react';
import { GeneralReportResponse, ReportResponse } from '@/shared/models/report.model';
import { ConfirmDialog } from '@/components/custom-dialog';
import { LoadingButton } from '@mui/lab';
import { useLoading } from '@/hooks/use-loading';
import { enqueueSnackbar } from 'notistack';
import { UserPermissionsContext } from '@/core/contexts';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { useMsal } from '@azure/msal-react';

export default function MyReportView() {
  const { data: reports, isLoading } = useQuery(['reports'], () => getCustomReports());
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { setLoading, setMessage } = useLoading();
  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('submitting'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };
  const [selectedReport, setSelectedReport] = useState<null | ReportResponse>(null);
  const userPermissions = useContext(UserPermissionsContext);
  const canDelete = userPermissions.some((permission) => permission.permissionName == PERMISSIONS.APPLICATION_ADMIN);
  const { t } = useTranslate();
  const confirm = useBoolean();
  const deleteConfirm = useBoolean();
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };
  const handleDelete = async (id: number): Promise<GeneralReportResponse> => {
    return deleteReport(id); //TODO Update this
  };
  const { mutateAsync } = useMutation({
    mutationFn: handleDelete,
    onSuccess: (response: GeneralReportResponse) => {
      queryClient.invalidateQueries(['reports']);
      enqueueSnackbar(response.message, {
        variant: 'success',
      });
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });
  const { accounts } = useMsal();
  const handleClose = () => {
    setAnchorEl(null);
  };
  const renderHeader = () => {
    return (
      <Grid size={12}>
        <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pr: 1 }}>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent={'space-between'} alignItems={'center'}>
            <CustomBreadcrumbs
              heading={t('label.reports')}
              links={[
                { name: t('label.home'), href: paths.root },
                { name: t('label.reports'), href: paths.reports.root },
              ]}
            />
            <Stack direction={'row'} spacing={2}>
              <Box
                component="span"
                display="flex"
                alignItems="center"
                sx={{ cursor: 'pointer', gap: 1, mr: 1 }}
                alignSelf={'center'}
                onClick={() => {
                  navigate(-1);
                }}
              >
                <Box component="img" src={getIcon('backBtn')} sx={{ width: 24, height: 16 }} />
                <Typography variant="value">{t('btn_name.back')}</Typography>
              </Box>
              <Button
                variant="contained"
                onClick={async () => {
                  setSelectedReport(null);
                  confirm.onTrue();
                }}
                sx={{ alignSelf: 'flex-end' }}
              >
                {t('btn_name.new_report')}
              </Button>
            </Stack>
          </Stack>
        </Card>
      </Grid>
    );
  };
  const renderContent = () => {
    if (isLoading)
      return (
        <Card
          sx={{ border: 'solid 1px #E8D6D6', borderRadius: '7px', paddingTop: 2, minHeight: '46vh', width: '100%' }}
        >
          <LoadingScreen></LoadingScreen>
        </Card>
      );
    return (
      <Grid size={12}>
        <Card sx={{ border: 'solid 1px #E8D6D6', borderRadius: '7px', paddingTop: 2 }}>
          <Box
            gap={2.75}
            margin={2}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(1, 1fr)',
              md: 'repeat(1, 1fr)',
              lg: 'repeat(2, 1fr)',
            }}
          >
            {reports?.map((item) => (
              <ReportCard
                report={item}
                key={item.id}
                canDelete={canDelete}
                canEdit={item.createdBy == accounts[0].username || canDelete}
                onClick={(event: any) => {
                  handleClick(event);
                  setSelectedReport(item);
                }}
              />
            ))}
          </Box>
        </Card>
      </Grid>
    );
  };
  return (
    <Box px={2}>
      <Grid container size={12} spacing={2}>
        {renderHeader()}
        {renderContent()}
      </Grid>
      <ModifyReport
        initialData={selectedReport ?? undefined}
        open={confirm.value}
        onClose={() => {
          confirm.onFalse();
          setSelectedReport(null);
        }}
      />
      {(canDelete || selectedReport?.createdBy == accounts[0].username) && (
        <Popover
          open={Boolean(anchorEl)}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          sx={{
            '& .MuiPaper-root': {
              minWidth: 150,
              borderRadius: 1,
            },
          }}
          disableRestoreFocus
          disableScrollLock
          onClick={(e) => e.stopPropagation()}
        >
          {(selectedReport?.createdBy == accounts[0].username || canDelete) && (
            <MenuItem
              onClick={() => {
                confirm.onTrue();
                handleClose();
              }}
            >
              {t('label.edit')}
            </MenuItem>
          )}
          {
            <MenuItem
              onClick={() => {
                deleteConfirm.onTrue();
              }}
            >
              {t('label.delete')}
            </MenuItem>
          }
        </Popover>
      )}

      {/* Delete Dialog */}
      <ConfirmDialog
        open={deleteConfirm.value}
        onClose={() => {
          setSelectedReport(null);
          deleteConfirm.onFalse();
        }}
        title={t('label.delete')}
        content={t('messages.are_you_sure_want_to_delete')}
        action={
          <LoadingButton
            variant="contained"
            onClick={async () => {
              try {
                startLoadingState();
                if (selectedReport) {
                  await mutateAsync(selectedReport.id);
                }
                setSelectedReport(null);
                deleteConfirm.onFalse();
              } catch (err) {
              } finally {
                stopLoadingState();
              }
            }}
            loading={isLoading}
          >
            {t('label.delete')}
          </LoadingButton>
        }
      />
    </Box>
  );
}
