{"version": 3, "sources": ["../../geotiff/dist-module/compression/lzw.js"], "sourcesContent": ["import BaseDecoder from './basedecoder.js';\n\nconst MIN_BITS = 9;\nconst CLEAR_CODE = 256; // clear code\nconst EOI_CODE = 257; // end of information\nconst MAX_BYTELENGTH = 12;\n\nfunction getByte(array, position, length) {\n  const d = position % 8;\n  const a = Math.floor(position / 8);\n  const de = 8 - d;\n  const ef = (position + length) - ((a + 1) * 8);\n  let fg = (8 * (a + 2)) - (position + length);\n  const dg = ((a + 2) * 8) - position;\n  fg = Math.max(0, fg);\n  if (a >= array.length) {\n    console.warn('ran off the end of the buffer before finding EOI_CODE (end on input code)');\n    return EOI_CODE;\n  }\n  let chunk1 = array[a] & ((2 ** (8 - d)) - 1);\n  chunk1 <<= (length - de);\n  let chunks = chunk1;\n  if (a + 1 < array.length) {\n    let chunk2 = array[a + 1] >>> fg;\n    chunk2 <<= Math.max(0, (length - dg));\n    chunks += chunk2;\n  }\n  if (ef > 8 && a + 2 < array.length) {\n    const hi = ((a + 3) * 8) - (position + length);\n    const chunk3 = array[a + 2] >>> hi;\n    chunks += chunk3;\n  }\n  return chunks;\n}\n\nfunction appendReversed(dest, source) {\n  for (let i = source.length - 1; i >= 0; i--) {\n    dest.push(source[i]);\n  }\n  return dest;\n}\n\nfunction decompress(input) {\n  const dictionaryIndex = new Uint16Array(4093);\n  const dictionaryChar = new Uint8Array(4093);\n  for (let i = 0; i <= 257; i++) {\n    dictionaryIndex[i] = 4096;\n    dictionaryChar[i] = i;\n  }\n  let dictionaryLength = 258;\n  let byteLength = MIN_BITS;\n  let position = 0;\n\n  function initDictionary() {\n    dictionaryLength = 258;\n    byteLength = MIN_BITS;\n  }\n  function getNext(array) {\n    const byte = getByte(array, position, byteLength);\n    position += byteLength;\n    return byte;\n  }\n  function addToDictionary(i, c) {\n    dictionaryChar[dictionaryLength] = c;\n    dictionaryIndex[dictionaryLength] = i;\n    dictionaryLength++;\n    return dictionaryLength - 1;\n  }\n  function getDictionaryReversed(n) {\n    const rev = [];\n    for (let i = n; i !== 4096; i = dictionaryIndex[i]) {\n      rev.push(dictionaryChar[i]);\n    }\n    return rev;\n  }\n\n  const result = [];\n  initDictionary();\n  const array = new Uint8Array(input);\n  let code = getNext(array);\n  let oldCode;\n  while (code !== EOI_CODE) {\n    if (code === CLEAR_CODE) {\n      initDictionary();\n      code = getNext(array);\n      while (code === CLEAR_CODE) {\n        code = getNext(array);\n      }\n\n      if (code === EOI_CODE) {\n        break;\n      } else if (code > CLEAR_CODE) {\n        throw new Error(`corrupted code at scanline ${code}`);\n      } else {\n        const val = getDictionaryReversed(code);\n        appendReversed(result, val);\n        oldCode = code;\n      }\n    } else if (code < dictionaryLength) {\n      const val = getDictionaryReversed(code);\n      appendReversed(result, val);\n      addToDictionary(oldCode, val[val.length - 1]);\n      oldCode = code;\n    } else {\n      const oldVal = getDictionaryReversed(oldCode);\n      if (!oldVal) {\n        throw new Error(`Bogus entry. Not in dictionary, ${oldCode} / ${dictionaryLength}, position: ${position}`);\n      }\n      appendReversed(result, oldVal);\n      result.push(oldVal[oldVal.length - 1]);\n      addToDictionary(oldCode, oldVal[oldVal.length - 1]);\n      oldCode = code;\n    }\n\n    if (dictionaryLength + 1 >= (2 ** byteLength)) {\n      if (byteLength === MAX_BYTELENGTH) {\n        oldCode = undefined;\n      } else {\n        byteLength++;\n      }\n    }\n    code = getNext(array);\n  }\n  return new Uint8Array(result);\n}\n\nexport default class LZWDecoder extends BaseDecoder {\n  decodeBlock(buffer) {\n    return decompress(buffer, false).buffer;\n  }\n}\n"], "mappings": ";;;;;;AAEA,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,iBAAiB;AAEvB,SAAS,QAAQ,OAAO,UAAU,QAAQ;AACxC,QAAM,IAAI,WAAW;AACrB,QAAM,IAAI,KAAK,MAAM,WAAW,CAAC;AACjC,QAAM,KAAK,IAAI;AACf,QAAM,KAAM,WAAW,UAAY,IAAI,KAAK;AAC5C,MAAI,KAAM,KAAK,IAAI,MAAO,WAAW;AACrC,QAAM,MAAO,IAAI,KAAK,IAAK;AAC3B,OAAK,KAAK,IAAI,GAAG,EAAE;AACnB,MAAI,KAAK,MAAM,QAAQ;AACrB,YAAQ,KAAK,2EAA2E;AACxF,WAAO;AAAA,EACT;AACA,MAAI,SAAS,MAAM,CAAC,IAAM,MAAM,IAAI,KAAM;AAC1C,aAAY,SAAS;AACrB,MAAI,SAAS;AACb,MAAI,IAAI,IAAI,MAAM,QAAQ;AACxB,QAAI,SAAS,MAAM,IAAI,CAAC,MAAM;AAC9B,eAAW,KAAK,IAAI,GAAI,SAAS,EAAG;AACpC,cAAU;AAAA,EACZ;AACA,MAAI,KAAK,KAAK,IAAI,IAAI,MAAM,QAAQ;AAClC,UAAM,MAAO,IAAI,KAAK,KAAM,WAAW;AACvC,UAAM,SAAS,MAAM,IAAI,CAAC,MAAM;AAChC,cAAU;AAAA,EACZ;AACA,SAAO;AACT;AAEA,SAAS,eAAe,MAAM,QAAQ;AACpC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,SAAK,KAAK,OAAO,CAAC,CAAC;AAAA,EACrB;AACA,SAAO;AACT;AAEA,SAAS,WAAW,OAAO;AACzB,QAAM,kBAAkB,IAAI,YAAY,IAAI;AAC5C,QAAM,iBAAiB,IAAI,WAAW,IAAI;AAC1C,WAAS,IAAI,GAAG,KAAK,KAAK,KAAK;AAC7B,oBAAgB,CAAC,IAAI;AACrB,mBAAe,CAAC,IAAI;AAAA,EACtB;AACA,MAAI,mBAAmB;AACvB,MAAI,aAAa;AACjB,MAAI,WAAW;AAEf,WAAS,iBAAiB;AACxB,uBAAmB;AACnB,iBAAa;AAAA,EACf;AACA,WAAS,QAAQA,QAAO;AACtB,UAAM,OAAO,QAAQA,QAAO,UAAU,UAAU;AAChD,gBAAY;AACZ,WAAO;AAAA,EACT;AACA,WAAS,gBAAgB,GAAG,GAAG;AAC7B,mBAAe,gBAAgB,IAAI;AACnC,oBAAgB,gBAAgB,IAAI;AACpC;AACA,WAAO,mBAAmB;AAAA,EAC5B;AACA,WAAS,sBAAsB,GAAG;AAChC,UAAM,MAAM,CAAC;AACb,aAAS,IAAI,GAAG,MAAM,MAAM,IAAI,gBAAgB,CAAC,GAAG;AAClD,UAAI,KAAK,eAAe,CAAC,CAAC;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,CAAC;AAChB,iBAAe;AACf,QAAM,QAAQ,IAAI,WAAW,KAAK;AAClC,MAAI,OAAO,QAAQ,KAAK;AACxB,MAAI;AACJ,SAAO,SAAS,UAAU;AACxB,QAAI,SAAS,YAAY;AACvB,qBAAe;AACf,aAAO,QAAQ,KAAK;AACpB,aAAO,SAAS,YAAY;AAC1B,eAAO,QAAQ,KAAK;AAAA,MACtB;AAEA,UAAI,SAAS,UAAU;AACrB;AAAA,MACF,WAAW,OAAO,YAAY;AAC5B,cAAM,IAAI,MAAM,8BAA8B,IAAI,EAAE;AAAA,MACtD,OAAO;AACL,cAAM,MAAM,sBAAsB,IAAI;AACtC,uBAAe,QAAQ,GAAG;AAC1B,kBAAU;AAAA,MACZ;AAAA,IACF,WAAW,OAAO,kBAAkB;AAClC,YAAM,MAAM,sBAAsB,IAAI;AACtC,qBAAe,QAAQ,GAAG;AAC1B,sBAAgB,SAAS,IAAI,IAAI,SAAS,CAAC,CAAC;AAC5C,gBAAU;AAAA,IACZ,OAAO;AACL,YAAM,SAAS,sBAAsB,OAAO;AAC5C,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,mCAAmC,OAAO,MAAM,gBAAgB,eAAe,QAAQ,EAAE;AAAA,MAC3G;AACA,qBAAe,QAAQ,MAAM;AAC7B,aAAO,KAAK,OAAO,OAAO,SAAS,CAAC,CAAC;AACrC,sBAAgB,SAAS,OAAO,OAAO,SAAS,CAAC,CAAC;AAClD,gBAAU;AAAA,IACZ;AAEA,QAAI,mBAAmB,KAAM,KAAK,YAAa;AAC7C,UAAI,eAAe,gBAAgB;AACjC,kBAAU;AAAA,MACZ,OAAO;AACL;AAAA,MACF;AAAA,IACF;AACA,WAAO,QAAQ,KAAK;AAAA,EACtB;AACA,SAAO,IAAI,WAAW,MAAM;AAC9B;AAEA,IAAqB,aAArB,cAAwC,YAAY;AAAA,EAClD,YAAY,QAAQ;AAClB,WAAO,WAAW,QAAQ,KAAK,EAAE;AAAA,EACnC;AACF;", "names": ["array"]}