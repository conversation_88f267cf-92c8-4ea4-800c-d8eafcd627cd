import {
  CAPABILITY_LEVEL_ENUM,
  CAPABILITY_STATUS_ENUM,
  CAPABILITY_TYPE_ENUM,
  PROVIDER_ENUM,
  SYSTEM_AND_TECHNOLOGY_ENUM,
} from '../enum/capability.enum';
import { Capabilities, LocationCapabilityList } from './bingo-card.model';
import {
  ActionOption,
  CapabilityRecord,
  LocationBasicDetailResponse,
  LocationRecordForCapabilityList,
} from './location.model';
import { CoreSolution } from './metadata-response.model';

export interface KeyValue {
  id: string;
  label: string;
}
export interface CapabilityItem {
  capability_name: string;
  description?: string;
  region?: string;
  cluster?: string;
  country?: string;
  status?: string;
  product: string | string[];
}

export interface CustomCapabilityTableFilterDrawerProps {
  open: boolean;
  onClose: () => void;
  filters: Record<string, KeyValue[]>;
  onFilterChange: (key: string, value: KeyValue[]) => void;
  onSearch: () => void;
  onCancel: () => void;
  tableHeaders: string[];
  tableData: any[];
}

export interface SelectedLocation {
  id: string;
  label: string;
  entityType: string | undefined;
  fullName: string;
}

export interface MobileViewCapListingCardProps {
  data: CapabilityListRecord[];
  count: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (event: React.ChangeEvent<unknown>, newPage: number) => void;
  actionOptions?: Array<{ title: string; icon?: React.ReactNode; onClick: (row: any) => void }>;
  onOpenEditStatusPopup: (id: string) => void;
  onDelete: (id: string) => void;
}

export interface MobileViewMasterCapListingCardProps {
  data: MasterCapabilityRecord[];
  count: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (event: React.ChangeEvent<unknown>, newPage: number) => void;
}

export interface MobileMasterCapabilityCardProps {
  item: MasterCapabilityRecord;
}

export interface MobileCapabilityCardProps {
  item: CapabilityListRecord;
  actionOptions?: ActionOption[];
  onOpenEditStatusPopup: (id: string) => void;
  onDeleteCapability: (id: string) => void;
}
export interface MobileAvailableCapabilityCardProps {
  item: CapabilityRecord;
  locationDetail: LocationRecordForCapabilityList;
  onDeleteCapability: (id: any) => void;
  onOpenEditStatusPopup: (id: string) => void;
}

export interface ICapabilitiesDetailsValueType {
  indicator: string;
  description: string;
}

export interface ICapabilitiesDetails {
  title: string;
  value: ICapabilitiesDetailsValueType[];
}

export interface ITabs {
  label: string;
  icon?: string;
}

export interface AddCapabilitiesFormProps {
  methods: any;
  onSubmit: (data: any) => void;
  handleSubmit: (data: any) => any;
  files: (File | string)[];
  handleDrop: (files: File[]) => void;
  handleRemoveFile: (file: File | string) => void;
  handleRemoveAllFiles: () => void;
  handleRemoveFilesAll: (file: File[] | string[]) => void;
}

export interface CapabilitiesLocationProps {
  tabs: ITabs[];
  data: ICapabilitiesDetails[];
  tabIndex: number;
  setTabIndex: any;
  isLoading: boolean;
}

export type FormFields = any;

export interface IAddCapabilities {
  methods: any;
  files?: (File | string)[]; // Allows both File and string types in the array
  handleDrop?: (fieldName: FormFields, acceptedFiles?: File[]) => void;
  handleRemoveFile?: (fieldName: FormFields, file?: File | string) => void;
  handleRemoveAllFiles?: (fieldName: FormFields) => void;
  onSubmit?: () => void;
  capabilityDetail?: CapabilityMasterDetailResponse | any;
}

export interface CapabilityMasterDetailResponse {
  id: number;
  product: string;
  category: {
    title: string;
  };
  subCategory: string;
  capability?: string;
  capabilityType?: string;
  level: CAPABILITY_LEVEL_ENUM;
  verticals?: [string];
  addionalFields?: [string];
  isEvidenceMandatory?: boolean;
  coreSolution?: CoreSolution;
  evidence?: EvidenceDetail;
  locationDetail?: LocationBasicDetailResponse;
}

///////////////////////////

export interface Owner {
  firstName: string;
  lastName: string;
  email: string;
  loginId: string;
  phone: string;
  jobTitle: string;
  role: string;
  isEscalation: boolean;
  escalationLevel: string;
}

interface Document {
  file_base64: string;
  attachment_name: string;
  type: string;
  fileimage: string;
  file: object;
  attachment_content_type: string;
  file_id: string;
  isdeleted: boolean;
  isPrimary: boolean;
  id: number;
  isNew: boolean;
  isEdited: boolean;
  url: string;
  size: string;
  description: string;
  prev_description: string;
  additional_info: string;
  section: string;
  created_by: string;
  created_on: string;
  progress: number;
}

interface EvidenceDetail {
  id: number | undefined;
  name: string | undefined;
  description: string;
  code: string | undefined;
  type: string | undefined;
  evidenceDocuments: Document[];
  evidenceValue: string;
}

interface CapabilityTypeDetail {
  issuingAuthority: string;
  expirationDate: Date | undefined;
  softwareProviderName: string | undefined;
  systemTechnologyName: string | undefined;
  systemTechnologyVersion: string | undefined;
  systemTechnologyOwner: SYSTEM_AND_TECHNOLOGY_ENUM | undefined;
  contractAgreementLicenseSubscription: string | undefined;
}

interface OperationalDetail {
  instructions: string | undefined;
  mandatoryDocumentsList: string | undefined;
  handlingAndProcedures: string | undefined;
  rulesAndRegulations: string | undefined;
  sop: Document[];
  documentList: Document[];
}

interface DynamicField {
  key: string;
  label: string;
  value: string;
}

interface AdditionalDetail {
  comments: string;
  tags: string[];
  additionalContactDetails: string;
  additionalDocuments: Document[];
  dynamicFields?: DynamicField[];
}

export interface CapabilityAddFormRequest {
  locationId: number;
  capabilityId: number;
  status: CAPABILITY_STATUS_ENUM;
  statusDate: string;
  owners: Owner;
  coOwners: Owner[];
  description: string;
  provider: PROVIDER_ENUM;
  providerName: string;
  evidenceDetail: EvidenceDetail;
  capabilityTypeDetail?: CapabilityTypeDetail;
  operationalDetail: OperationalDetail;
  additionalDetail: AdditionalDetail;
  deletedFileIds?: string[];
  id?: number;
}

export interface CapabilityFormValues {
  status: CAPABILITY_STATUS_ENUM;
  statusDate: string;
  ownerEmailId: string;
  owner: Owner;
  coOwnerEmailIds: Owner[];
  description: string;
  provider: PROVIDER_ENUM;
  providerName: string | undefined;
  evidenceDescription?: string;
  evidenceDocuments: Document[];
  evidenceValue?: string;
  issuingAuthority?: string;
  expirationDate: string;
  softwareProviderName: string;
  systemTechnologyName?: string;
  systemTechnologyVersion?: string;
  systemTechnologyOwner: SYSTEM_AND_TECHNOLOGY_ENUM;
  contractAgreementLicenseSubscription: string;
  instructions?: string;
  sopDocuments: Document[];
  documents: Document[];
  mandatoryDocumentsList?: string;
  handlingAndProcedures?: string;
  rulesAndRegulations?: string;
  tags: string[];
  additionalDocuments: Document[];
  commentsAndNotes?: string;
  additionalContactDetails?: string;
}

export interface CapabilityDetail {
  id: number;
  capability: string;
  capabilityType: CAPABILITY_TYPE_ENUM;
  product: string;
  category: {
    title: string;
  };
  subCategory: string;
  level: CAPABILITY_LEVEL_ENUM;
  verticals: string[];
  evidence?: EvidenceDetail;
  isEvidenceMandatory: Boolean;
}

export interface capabilityLegs {
  id: number;
  shortName: string;
  fullName: string;
  code: string;
}

export interface CapabilityDetailResponse extends CapabilityAddFormRequest {
  id: number;
  capabilityDetail: CapabilityDetail;
  locationDetail: LocationBasicDetailResponse;
  capabilityLegs: capabilityLegs[];
}

export interface CapabilityListFiltersModel {
  entityIds?: number[];
  entryIds?: number[];

  capabilityTypes?: CAPABILITY_TYPE_ENUM[];
  capabilityLevel?: CAPABILITY_LEVEL_ENUM[];
  coreSolutionIds?: number[];
  verticalCodes?: string[];
  statuses?: CAPABILITY_STATUS_ENUM[];
  providers?: PROVIDER_ENUM[];
  searchTerm?: string;
  ownerLoginId?: string;
  hierarchyEntityId?: number;
}
export interface CapabilityListResponse {
  pageTotal: number;
  total: number;
  records: CapabilityListRecord[];
}

export interface MasterCapabilityListResponse {
  pageTotal: number;
  total: number;
  records: MasterCapabilityRecord[];
}

export interface CapabilityListRecord {
  id: number;
  status: CAPABILITY_STATUS_ENUM;
  statusDate: string;
  provider: PROVIDER_ENUM;
  providerName: string;
  locationDetail: LocationDetail;
  capabilityDetail: CapabilityDetailWithCoreSolution;
}

export interface MasterCapabilityRecord {
  id: number;
  product: string;
  subCategory: string;
  category: {
    title: string;
  };
  capability: string;
  capabilityType: CAPABILITY_TYPE_ENUM;
  level: CAPABILITY_LEVEL_ENUM;
  verticals: string[];
  coreSolution: CoreSolution;
  legs: string[];
  legsDetail: capabilityLegs[];
  evidence: EvidenceDetail;
  isEvidenceMandatory: boolean;
}

// Interface for location details
export interface LocationDetail {
  id: number;
  locationName: string;
  entityTitle: string;
  entityId: number;
  coreSolution: CoreSolution;
}

// Interface for capability details
export interface CapabilityDetailWithCoreSolution extends CapabilityDetail {
  coreSolution: CoreSolution;
}
export interface ICapabilityTableFilters {
  searchTerm?: string;
  capabilityTypes?: string;
  providers?: KeyValue[];
  verticalCodes?: KeyValue[];
  capabilityLevel?: KeyValue[];
  statuses?: KeyValue[];
  chartStatus?: KeyValue[];
}

export type ICapabilityTableFilterValue = string | KeyValue[] | null;

export interface BingoCardFilterDrawerProps {
  open: boolean;
  locationCapabilityList?: Capabilities[];
  onClose: () => void;
  filters: Record<string, KeyValue[]>;
  onFilterChange: (key: string, value: KeyValue[]) => void;
  onSearch: () => void;
  onCancel: () => void;
  tableHeaders: string[];
  tableData: any[];
}

export enum MASTER_CAPABILITY_DROPDOWN {
  LEGS = 'LEGS',
  PRODUCT = 'PRODUCT',
  SUB_CATEGORY = 'SUB-CATEGORY',
  CATEGORY = 'CATEGORY',
}
