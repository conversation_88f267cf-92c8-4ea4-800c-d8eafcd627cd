import { Injectable } from '@nestjs/common';
import { col, fn, literal, Op, where } from 'sequelize';
import { CoreSolution, LocationType } from 'src/metadata/models';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { LocationWiseCapabilityDetail, MasterCapability, MetaEvidence } from '../models';

import { Location } from 'src/location/models';
import { CapabilityFilterRequestDto, ExportLocationWiseCapabilityFilterRequestDto } from '../dtos';
import { CapabilityCategory } from '../models/master-capability-category.model';

@Injectable()
export class LocationWiseCapabilityRepository extends BaseRepository<LocationWiseCapabilityDetail> {
	constructor() {
		super(LocationWiseCapabilityDetail);
	}

	public async deleteByLocationId(locationId: number, currentContext: CurrentContext) {
		return this.deleteByCondition({ locationId }, currentContext);
	}

	public async getLocationWiseCapabilityDetail(
		locationId: number,
		capabilityId: number,
	): Promise<LocationWiseCapabilityDetail | null> {
		return this.findOne({
			where: { locationId, capabilityId },
		});
	}

	public async isCapabilityDetailExist(id: number): Promise<boolean> {
		return this.isRecordExist({ where: { id } });
	}

	public async getExistingCapabilityDetailById(
		id: number,
	): Promise<LocationWiseCapabilityDetail | null> {
		return this.findOne({
			attributes: ['id', 'locationId', 'capabilityId', 'status', 'statusDate'],
			where: { id },
			include: [
				{
					model: Location,
					as: 'locationDetail',
					required: true,
					attributes: ['id', 'locationName', 'entityId'],
					where: {
						active: true,
						deleted: false,
					},
				},
			],
		});
	}

	public async getLocationWiseCapabilityDetailById(
		id: number,
	): Promise<LocationWiseCapabilityDetail | null> {
		return this.findOne({
			where: { id },
			include: [
				{
					model: Location,
					as: 'locationDetail',
					required: true,
					attributes: [
						'id',
						'locationName',
						'entityId',
						'entityTitle',
						'status',
						'leaseOwnershipStatus',
						'statusDate',
					],
					where: {
						active: true,
						deleted: false,
					},
					include: [
						{
							model: CoreSolution,
							as: 'coreSolution',
							attributes: ['id', 'title', 'code', 'pillar'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
						{
							model: LocationType,
							as: 'locationType',
							attributes: ['id', 'title', 'code', 'canAcquireCapability'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
				},
				{
					model: MasterCapability,
					as: 'capabilityDetail',
					required: true,
					attributes: [
						'id',
						'capability',
						'subCategory',
						'product',
						'capabilityType',
						'level',
						'verticals',
						'isEvidenceMandatory',
						'otherDetails',
						'legs',
					],
					include: [
						{
							model: MetaEvidence,
							as: 'evidence',
							attributes: ['id', 'name', 'description', 'code', 'type'],
						},
						{
							model: CapabilityCategory,
							as: 'category',
							attributes: ['title'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
					where: {
						active: true,
						deleted: false,
					},
				},
			],
		});
	}

	public async addNewCapability(
		payload: any,
		currentContext: CurrentContext,
	): Promise<LocationWiseCapabilityDetail> {
		const capability = new LocationWiseCapabilityDetail(payload);
		return this.save(capability, currentContext);
	}

	public updateCapabilityDetailById(
		capabilityId: any,
		data,
		currentContext: CurrentContext,
	): Promise<number | null> {
		return this.update(data, currentContext, {
			where: {
				id: capabilityId,
			},
		});
	}

	public async deleteLocationCapability(id: number, currentContext: CurrentContext) {
		return this.deleteByCondition({ id }, currentContext);
	}

	public async getCapabilitiesByFilter(
		filterDto: CapabilityFilterRequestDto,
		page: number,
		limit: number,
	): Promise<{ rows: any[]; count: number }> {
		const whereClause = this.buildWhereClause(filterDto);

		return this.findAndCountAll({
			where: whereClause,
			include: [
				{
					model: Location,
					as: 'locationDetail',
					required: true,
					attributes: ['id', 'locationName', 'entityId', 'entityTitle'],
					where: {
						active: true,
						deleted: false,
					},
					include: [
						{
							model: CoreSolution,
							as: 'coreSolution',
							attributes: ['id', 'title', 'code', 'pillar'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
				},
				{
					model: MasterCapability,
					as: 'capabilityDetail',
					required: true,
					attributes: [
						'id',
						'capability',
						'subCategory',
						'product',
						'capabilityType',
						'level',
						'verticals',
						'otherDetails',
					],
					include: [
						{
							model: CoreSolution,
							as: 'coreSolution',
							attributes: ['id', 'title', 'code', 'pillar'],
							required: false,
							where: {
								active: true,
								deleted: false,
							},
						},
						{
							model: CapabilityCategory,
							as: 'category',
							attributes: ['title'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
					where: {
						active: true,
						deleted: false,
					},
				},
			],
			...(page && { offset: (page - 1) * limit }),
			...(limit && { limit }),
			order: [['updatedOn', 'DESC']],
			// subQuery: false,
			distinct: true,
		});
	}

	private buildWhereClause(filterDto: CapabilityFilterRequestDto) {
		const {
			capabilityLevel,
			capabilityTypes,
			coreSolutionIds,
			entityIds,
			statuses,
			providers,
			verticalCodes,
			ownerLoginId,
			searchTerm,
			entryIds
		} = filterDto;

		const whereClause: any = {};
		const andCondition = [];

		if (searchTerm && searchTerm.length) {
			andCondition.push({
				[Op.or]: [
					literal(`"capabilityDetail"."capability" ILIKE '%${searchTerm}%'`),
					literal(`"capabilityDetail"."subcategory" ILIKE '%${searchTerm}%'`),
					literal(`"capabilityDetail"."product" ILIKE '%${searchTerm}%'`),
				],
			});
		}

		if (statuses && statuses.length) {
			whereClause.status = { [Op.in]: statuses };
		}

		if (entryIds && entryIds.length) {
			whereClause.capabilityId = { [Op.in]: entryIds };
		}

		if (providers && providers.length) {
			whereClause.provider = { [Op.in]: providers };
		}

		if (ownerLoginId && ownerLoginId.length) {
			andCondition.push(
				where(
					fn(
						'LOWER',
						fn(
							'jsonb_extract_path_text',
							col('"LocationWiseCapabilityDetail"."other_details"'),
							'owners',
							'loginId',
						),
					),
					{ [Op.iLike]: `%${ownerLoginId.toLowerCase()}%` },
				),
			);
		}

		if (capabilityLevel && capabilityLevel.length) {
			andCondition.push({
				'$capabilityDetail.level$': {
					[Op.in]: capabilityLevel,
				},
			});
		}

		if (capabilityTypes && capabilityTypes.length) {
			andCondition.push({
				'$capabilityDetail.capability_type$': {
					[Op.in]: capabilityTypes,
				},
			});
		}

		// if (coreSolutionIds && coreSolutionIds.length) {
		// 	andCondition.push({
		// 		'$capabilityDetail.core_solution_id$': {
		// 			[Op.in]: coreSolutionIds,
		// 		},
		// 	});
		// }

		if (coreSolutionIds && coreSolutionIds.length) {
			andCondition.push({
				[Op.or]: [
					{ '$capabilityDetail.core_solution_id$': { [Op.in]: coreSolutionIds } },
					{ '$capabilityDetail.core_solution_id$': null }
				]
			});
		}

		if (verticalCodes && verticalCodes.length) {
			andCondition.push({
				[Op.and]: [
					{
						[Op.or]: verticalCodes.map(code =>
							where(col('capabilityDetail.verticals'), Op.contains, JSON.stringify([code])),
						),
					},
					{
						'$capabilityDetail.verticals$': {
							[Op.not]: null,
						},
					},
				],
			});
		}

		if (entityIds && entityIds?.length) {
			andCondition.push({
				'$locationDetail.entity_id$': {
					[Op.in]: entityIds,
				},
			});
		}

		if (entityIds && entityIds?.length) {
			andCondition.push({
				'$locationDetail.entity_id$': {
					[Op.in]: entityIds,
				},
			});
		}

		if (andCondition.length > 0) {
			whereClause[Op.and] = andCondition;
		}

		return whereClause;
	}

	public async getCapabilitiesToExport(filterDto: CapabilityFilterRequestDto) {
		const whereClause = this.buildWhereClause(filterDto);

		return this.findAll({
			where: whereClause,
			include: [
				{
					model: Location,
					as: 'locationDetail',
					required: true,
					attributes: ['id', 'locationName', 'entityId', 'entityTitle'],
					include: [
						{
							model: CoreSolution,
							as: 'coreSolution',
							attributes: ['id', 'title', 'code', 'pillar'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
					where: {
						active: true,
						deleted: false,
					},
				},
				{
					model: MasterCapability,
					as: 'capabilityDetail',
					required: true,
					attributes: [
						'id',
						'capability',
						'subCategory',
						'product',
						'capabilityType',
						'level',
						'verticals',
						'otherDetails',
					],
					where: {
						active: true,
						deleted: false,
					},
					include: [
						{
							model: CapabilityCategory,
							as: 'category',
							attributes: ['title'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
				},
			],
			// subQuery: false,
			distinct: true,
		});
	}

	public async getCapabilitiesByLocationId(
		locationId: number,
	): Promise<LocationWiseCapabilityDetail[]> {
		return this.findAll({
			where: {
				locationId,
			},
			include: [
				{
					model: MasterCapability,
					as: 'capabilityDetail',
					required: true,
					attributes: [
						'id',
						'capability',
						'subCategory',
						'product',
						'capabilityType',
						'level',
						'verticals',
						'otherDetails',
					],
					where: {
						active: true,
						deleted: false,
					},
					include: [
						{
							model: CapabilityCategory,
							as: 'category',
							attributes: ['title'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
				},
			],
			order: [['updatedOn', 'DESC']],
			distinct: true,
		});
	}

	public async exportCapabilitiesByLocationId(
		locationId: number,
		filterDto?: ExportLocationWiseCapabilityFilterRequestDto,
	): Promise<LocationWiseCapabilityDetail[]> {
		let whereClause = this.buildWhereClause(filterDto);

		if (whereClause) {
			whereClause.locationId = locationId;
		} else {
			whereClause = { locationId };
		}

		return this.findAll({
			where: whereClause,
			include: [
				{
					model: MasterCapability,
					as: 'capabilityDetail',
					required: true,
					attributes: [
						'id',
						'capability',
						'subCategory',
						'product',
						'capabilityType',
						'level',
						'verticals',
						'otherDetails',
					],
					where: {
						active: true,
						deleted: false,
					},
					include: [
						{
							model: CapabilityCategory,
							as: 'category',
							attributes: ['title'],
							required: true,
							where: {
								active: true,
								deleted: false,
							},
						},
					],
				},
			],
			order: [['updatedOn', 'DESC']],
			distinct: true,
		});
	}
}
