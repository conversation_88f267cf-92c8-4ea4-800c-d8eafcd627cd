"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MSGraphApiClient = void 0;
const msal = __importStar(require("@azure/msal-node"));
const common_1 = require("@nestjs/common");
const config_service_1 = require("../../config/config.service");
const constants_1 = require("../constants");
const services_1 = require("../services");
let MSGraphApiClient = class MSGraphApiClient {
    constructor(msGraphApiProvider, msGraphApiHttpService, configService) {
        this.msGraphApiProvider = msGraphApiProvider;
        this.msGraphApiHttpService = msGraphApiHttpService;
        this.configService = configService;
        this.azureADConfig = this.configService.getAppConfig().azureAD;
        const { graphApiUrl } = this.azureADConfig;
        this.tokenRequest = {
            scopes: [`${graphApiUrl}/.default`],
        };
    }
    getToken() {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.msGraphApiProvider.acquireTokenByClientCredential(this.tokenRequest);
        });
    }
    getHeaders() {
        return __awaiter(this, void 0, void 0, function* () {
            const { accessToken } = yield this.getToken();
            return { Authorization: `Bearer ${accessToken}`, ConsistencyLevel: 'eventual' };
        });
    }
    getUserDetails(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const headers = yield this.getHeaders();
            const { data } = yield this.msGraphApiHttpService
                .withHeaders(headers)
                .get(`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${userId}' OR userPrincipalName eq '${userId}'`);
            return (data === null || data === void 0 ? void 0 : data.value[data.value.length - 1]) || null;
        });
    }
    getUserDetailsByEmail(email) {
        return __awaiter(this, void 0, void 0, function* () {
            const headers = yield this.getHeaders();
            const { data } = yield this.msGraphApiHttpService
                .withHeaders(headers)
                .get(`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${email}'`);
            return (data === null || data === void 0 ? void 0 : data.value[data.value.length - 1]) || null;
        });
    }
    getUsersDetails(userIds) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const headers = yield this.getHeaders();
            const ids = userIds.map(id => `'${id}'`).join(',');
            const results = [];
            let nextLink = `/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`;
            while (nextLink) {
                try {
                    const response = yield this.msGraphApiHttpService.withHeaders(headers).get(nextLink);
                    const { data } = response;
                    results.push(...data.value);
                    nextLink =
                        ((_a = data['@odata.nextLink']) === null || _a === void 0 ? void 0 : _a.replace(`${this.azureADConfig.graphApiUrl}/${this.azureADConfig.graphApiVersion}`, '')) || null;
                }
                catch (error) {
                    console.error(`Failed to fetch users: ${error === null || error === void 0 ? void 0 : error.message}`, error);
                    break;
                }
            }
            return results;
        });
    }
    searchUsers(searchText, orderBy, count) {
        return __awaiter(this, void 0, void 0, function* () {
            const headers = yield this.getHeaders();
            const { data } = yield this.msGraphApiHttpService
                .withHeaders(headers)
                .get(`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$search="displayName:${encodeURIComponent(searchText)}" OR "mail:${encodeURIComponent(searchText)}" OR "userPrincipalName:${encodeURIComponent(searchText)}"&$orderby=${orderBy}&$count=${count}&$filter=onPremisesExtensionAttributes%2FextensionAttribute9%20eq%20'DP%20World%20Guest'%20OR%20userType%20eq%20'Member'`);
            data.value = data.value.filter(d => { var _a, _b; return !((_b = (_a = d === null || d === void 0 ? void 0 : d.userPrincipalName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === null || _b === void 0 ? void 0 : _b.endsWith('au.dpworld.com')); });
            return data;
        });
    }
    getUserDetailsInMsResponse(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const headers = yield this.getHeaders();
            const { data } = yield this.msGraphApiHttpService
                .withHeaders(headers)
                .get(`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${userId}' OR userPrincipalName eq '${userId}'`);
            return data;
        });
    }
    getUsersDetailsFromAdInMsResponse(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            const headers = yield this.getHeaders();
            const { data } = yield this.msGraphApiHttpService
                .withHeaders(headers)
                .get(`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`);
            return data;
        });
    }
};
MSGraphApiClient = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(constants_1.MS_GRAPH_API.MS_GRAPH_API_PROVIDER)),
    __param(1, (0, common_1.Inject)(constants_1.MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER)),
    __param(2, (0, common_1.Inject)(config_service_1.ConfigService)),
    __metadata("design:paramtypes", [msal.ConfidentialClientApplication, services_1.HttpService,
        config_service_1.ConfigService])
], MSGraphApiClient);
exports.MSGraphApiClient = MSGraphApiClient;
//# sourceMappingURL=ms-graph-api.client.js.map