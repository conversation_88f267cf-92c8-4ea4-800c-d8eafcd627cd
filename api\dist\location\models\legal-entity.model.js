"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegalEntity = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const country_model_1 = require("./country.model");
const location_model_1 = require("./location.model");
let LegalEntity = class LegalEntity extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => country_model_1.Country),
    (0, sequelize_typescript_1.Column)({ field: 'country_id', type: sequelize_typescript_1.DataType.NUMBER, allowNull: false }),
    __metadata("design:type", Number)
], LegalEntity.prototype, "countryId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => country_model_1.Country),
    __metadata("design:type", country_model_1.Country)
], LegalEntity.prototype, "country", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'name', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], LegalEntity.prototype, "name", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'other_details', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], LegalEntity.prototype, "otherDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => location_model_1.Location),
    __metadata("design:type", Array)
], LegalEntity.prototype, "locations", void 0);
LegalEntity = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'data_legal_entities' })
], LegalEntity);
exports.LegalEntity = LegalEntity;
//# sourceMappingURL=legal-entity.model.js.map