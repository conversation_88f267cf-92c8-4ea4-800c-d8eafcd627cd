"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Location = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const models_2 = require("../../metadata/models");
const legal_entity_model_1 = require("./legal-entity.model");
const helpers_1 = require("../../shared/helpers");
const enums_1 = require("../../shared/enums");
const models_3 = require("../../permission/models");
const location_industry_vertical_model_1 = require("./location_industry_vertical.model");
const models_4 = require("../../contact-details/models");
const partner_branch_model_1 = require("./partner-branch.model");
const models_5 = require("../../capability/models");
let Location = class Location extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_id', type: sequelize_typescript_1.DataType.NUMBER, allowNull: false }),
    __metadata("design:type", Number)
], Location.prototype, "entityId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_code', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Location.prototype, "entityCode", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Location.prototype, "entityTitle", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_type', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Location.prototype, "entityType", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => models_2.CoreSolution),
    (0, sequelize_typescript_1.Column)({ field: 'core_solution_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], Location.prototype, "coreSolutionId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => models_2.CoreSolution),
    __metadata("design:type", models_2.CoreSolution)
], Location.prototype, "coreSolution", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => models_2.LocationType),
    (0, sequelize_typescript_1.Column)({ field: 'location_type_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], Location.prototype, "locationTypeId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => models_2.LocationType),
    __metadata("design:type", models_2.LocationType)
], Location.prototype, "locationType", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => models_2.CommonDropdown),
    (0, sequelize_typescript_1.Column)({ field: 'branch_archetype_code', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], Location.prototype, "branchArchetypeCode", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => models_2.CommonDropdown, { foreignKey: 'branch_archetype_code', targetKey: 'code' }),
    __metadata("design:type", models_2.CommonDropdown)
], Location.prototype, "branchArchetype", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'location_name', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Location.prototype, "locationName", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => legal_entity_model_1.LegalEntity),
    (0, sequelize_typescript_1.Column)({ field: 'legal_entity_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], Location.prototype, "legalEntityId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => legal_entity_model_1.LegalEntity),
    __metadata("design:type", legal_entity_model_1.LegalEntity)
], Location.prototype, "legalEntity", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'lease_ownership_status',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.LEASE_OWNERSHIP_STATUS_TYPE)),
        allowNull: false,
    }),
    __metadata("design:type", String)
], Location.prototype, "leaseOwnershipStatus", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'status',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.LOCATION_STATUS_TYPE)),
        allowNull: false,
    }),
    __metadata("design:type", String)
], Location.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'status_comment', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], Location.prototype, "statusComment", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'status_date', type: sequelize_typescript_1.DataType.DATE, allowNull: true }),
    __metadata("design:type", Date)
], Location.prototype, "statusDate", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => models_2.CommonDropdown),
    (0, sequelize_typescript_1.Column)({ field: 'strategic_classification_code', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Location.prototype, "strategicClassificationCode", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => models_2.CommonDropdown, { foreignKey: 'strategicClassificationCode', targetKey: 'code' }),
    __metadata("design:type", models_2.CommonDropdown)
], Location.prototype, "strategicClassification", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'ff_location_ids', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Location.prototype, "ffLocationIds", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'brands', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Location.prototype, "brands", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'latitude', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Location.prototype, "latitude", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'longitude', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Location.prototype, "longitude", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'google_listing', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], Location.prototype, "googleListing", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'tags', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Location.prototype, "tags", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'other_details', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], Location.prototype, "otherDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'country_detail', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], Location.prototype, "countryDetail", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => partner_branch_model_1.PartnerBranch),
    __metadata("design:type", Array)
], Location.prototype, "partnerBranches", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => models_3.UserPermission),
    __metadata("design:type", Array)
], Location.prototype, "userPermission", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => location_industry_vertical_model_1.LocationIndustryVertical),
    __metadata("design:type", Array)
], Location.prototype, "locationIndustryVerticals", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => models_4.ContactDetail),
    __metadata("design:type", Array)
], Location.prototype, "contacts", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => models_5.LocationWiseCapabilityDetail),
    __metadata("design:type", Array)
], Location.prototype, "capabilities", void 0);
Location = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'data_locations' })
], Location);
exports.Location = Location;
//# sourceMappingURL=location.model.js.map