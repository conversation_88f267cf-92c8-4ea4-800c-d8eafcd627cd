export const tableHeaders = ['Entry Name', 'Description', 'Region', 'Cluster', 'Country', 'Product', 'Status'];

export const CAPABILITY_FILTER_CONFIG = {
  displayNames: {
    entry_name: 'Entry Name',
    region: 'Region',
    cluster: 'Cluster',
    country: 'Country',
    status: 'Status',
    core_solution: 'Capability',
    capability: 'Capability',
    verticals: 'Verticals',
    provider: 'Provider',
    entry_type: 'Entry Type',
    entry_level: 'Entry Level',
    product: 'Product',
    category: 'Category',
    product_family: 'Product Family',
    owner: 'Owner',
    area: 'Area',
    group: 'Group',
    view: 'View',
  },
};
export const LOCATION_CAPABILITY_FILTER_CONFIG = {
  displayNames: {
    searchTerm: 'Entry Name',
    statuses: 'Status',
    verticalCodes: 'Verticals',
    providers: 'Provider',
    capabilityTypes: 'Entry Type',
    capabilityLevel: 'Entry Level',
  },
};

// Adding Region, Cluster and Country in the filter headers so filteration works //
export const filterheaders = [
  'Entries',
  'Region',
  'Cluster',
  'Country',
  'Entry',
  'Entry Type',
  'Entry Level',
  'Status',
  'Provider',
  // 'Category',
  'Verticals',
  // 'Product Family',
  // 'Product',
  'Owner',
];

export const filterheaderBingoCard = [
  'Region',
  'Core Solution',
  'Entry Type',
  'Entry Level',
  'Entry',
  'Status',
  'Verticals',
];

export const capabilityListFilters = ['Entry Level', 'Status', 'Provider', 'Verticals'];
export const FIELD_KEY_MAPPER = {
  'Entry Level': 'capabilityLevel',
  Status: 'statuses',
  Provider: 'providers',
  Verticals: 'verticalCodes',
};
