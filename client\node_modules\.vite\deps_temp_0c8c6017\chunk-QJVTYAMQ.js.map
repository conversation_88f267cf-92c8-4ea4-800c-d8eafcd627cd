{"version": 3, "sources": ["../../ol/Feature.js"], "sourcesContent": ["/**\n * @module ol/Feature\n */\nimport BaseObject from './Object.js';\nimport {assert} from './asserts.js';\nimport EventType from './events/EventType.js';\nimport {listen, unlistenByKey} from './events.js';\n\n/**\n * @typedef {typeof Feature|typeof import(\"./render/Feature.js\").default} FeatureClass\n */\n\n/**\n * @typedef {Feature|import(\"./render/Feature.js\").default} FeatureLike\n */\n\n/***\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *   import(\"./Observable\").OnSignature<import(\"./ObjectEventType\").Types|'change:geometry', import(\"./Object\").ObjectEvent, Return> &\n *   import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|import(\"./ObjectEventType\").Types\n *     |'change:geometry', Return>} FeatureOnSignature\n */\n\n/***\n * @template {import(\"./geom/Geometry.js\").default} [Geometry=import(\"./geom/Geometry.js\").default]\n * @typedef {Object<string, *> & { geometry?: Geometry }} ObjectWithGeometry\n */\n\n/**\n * @classdesc\n * A vector object for geographic features with a geometry and other\n * attribute properties, similar to the features in vector file formats like\n * GeoJSON.\n *\n * Features can be styled individually with `setStyle`; otherwise they use the\n * style of their vector layer.\n *\n * Note that attribute properties are set as {@link module:ol/Object~BaseObject} properties on\n * the feature object, so they are observable, and have get/set accessors.\n *\n * Typically, a feature has a single geometry property. You can set the\n * geometry using the `setGeometry` method and get it with `getGeometry`.\n * It is possible to store more than one geometry on a feature using attribute\n * properties. By default, the geometry used for rendering is identified by\n * the property name `geometry`. If you want to use another geometry property\n * for rendering, use the `setGeometryName` method to change the attribute\n * property associated with the geometry for the feature.  For example:\n *\n * ```js\n *\n * import Feature from 'ol/Feature.js';\n * import Polygon from 'ol/geom/Polygon.js';\n * import Point from 'ol/geom/Point.js';\n *\n * const feature = new Feature({\n *   geometry: new Polygon(polyCoords),\n *   labelPoint: new Point(labelCoords),\n *   name: 'My Polygon',\n * });\n *\n * // get the polygon geometry\n * const poly = feature.getGeometry();\n *\n * // Render the feature as a point using the coordinates from labelPoint\n * feature.setGeometryName('labelPoint');\n *\n * // get the point geometry\n * const point = feature.getGeometry();\n * ```\n *\n * @api\n * @template {import(\"./geom/Geometry.js\").default} [Geometry=import(\"./geom/Geometry.js\").default]\n */\nclass Feature extends BaseObject {\n  /**\n   * @param {Geometry|ObjectWithGeometry<Geometry>} [geometryOrProperties]\n   *     You may pass a Geometry object directly, or an object literal containing\n   *     properties. If you pass an object literal, you may include a Geometry\n   *     associated with a `geometry` key.\n   */\n  constructor(geometryOrProperties) {\n    super();\n\n    /***\n     * @type {FeatureOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {FeatureOnSignature<import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {FeatureOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {number|string|undefined}\n     */\n    this.id_ = undefined;\n\n    /**\n     * @type {string}\n     * @private\n     */\n    this.geometryName_ = 'geometry';\n\n    /**\n     * User provided style.\n     * @private\n     * @type {import(\"./style/Style.js\").StyleLike}\n     */\n    this.style_ = null;\n\n    /**\n     * @private\n     * @type {import(\"./style/Style.js\").StyleFunction|undefined}\n     */\n    this.styleFunction_ = undefined;\n\n    /**\n     * @private\n     * @type {?import(\"./events.js\").EventsKey}\n     */\n    this.geometryChangeKey_ = null;\n\n    this.addChangeListener(this.geometryName_, this.handleGeometryChanged_);\n\n    if (geometryOrProperties) {\n      if (\n        typeof (\n          /** @type {?} */ (geometryOrProperties).getSimplifiedGeometry\n        ) === 'function'\n      ) {\n        const geometry = /** @type {Geometry} */ (geometryOrProperties);\n        this.setGeometry(geometry);\n      } else {\n        /** @type {Object<string, *>} */\n        const properties = geometryOrProperties;\n        this.setProperties(properties);\n      }\n    }\n  }\n\n  /**\n   * Clone this feature. If the original feature has a geometry it\n   * is also cloned. The feature id is not set in the clone.\n   * @return {Feature<Geometry>} The clone.\n   * @api\n   */\n  clone() {\n    const clone = /** @type {Feature<Geometry>} */ (\n      new Feature(this.hasProperties() ? this.getProperties() : null)\n    );\n    clone.setGeometryName(this.getGeometryName());\n    const geometry = this.getGeometry();\n    if (geometry) {\n      clone.setGeometry(/** @type {Geometry} */ (geometry.clone()));\n    }\n    const style = this.getStyle();\n    if (style) {\n      clone.setStyle(style);\n    }\n    return clone;\n  }\n\n  /**\n   * Get the feature's default geometry.  A feature may have any number of named\n   * geometries.  The \"default\" geometry (the one that is rendered by default) is\n   * set when calling {@link module:ol/Feature~Feature#setGeometry}.\n   * @return {Geometry|undefined} The default geometry for the feature.\n   * @api\n   * @observable\n   */\n  getGeometry() {\n    return /** @type {Geometry|undefined} */ (this.get(this.geometryName_));\n  }\n\n  /**\n   * Get the feature identifier.  This is a stable identifier for the feature and\n   * is either set when reading data from a remote source or set explicitly by\n   * calling {@link module:ol/Feature~Feature#setId}.\n   * @return {number|string|undefined} Id.\n   * @api\n   */\n  getId() {\n    return this.id_;\n  }\n\n  /**\n   * Get the name of the feature's default geometry.  By default, the default\n   * geometry is named `geometry`.\n   * @return {string} Get the property name associated with the default geometry\n   *     for this feature.\n   * @api\n   */\n  getGeometryName() {\n    return this.geometryName_;\n  }\n\n  /**\n   * Get the feature's style. Will return what was provided to the\n   * {@link module:ol/Feature~Feature#setStyle} method.\n   * @return {import(\"./style/Style.js\").StyleLike|undefined} The feature style.\n   * @api\n   */\n  getStyle() {\n    return this.style_;\n  }\n\n  /**\n   * Get the feature's style function.\n   * @return {import(\"./style/Style.js\").StyleFunction|undefined} Return a function\n   * representing the current style of this feature.\n   * @api\n   */\n  getStyleFunction() {\n    return this.styleFunction_;\n  }\n\n  /**\n   * @private\n   */\n  handleGeometryChange_() {\n    this.changed();\n  }\n\n  /**\n   * @private\n   */\n  handleGeometryChanged_() {\n    if (this.geometryChangeKey_) {\n      unlistenByKey(this.geometryChangeKey_);\n      this.geometryChangeKey_ = null;\n    }\n    const geometry = this.getGeometry();\n    if (geometry) {\n      this.geometryChangeKey_ = listen(\n        geometry,\n        EventType.CHANGE,\n        this.handleGeometryChange_,\n        this,\n      );\n    }\n    this.changed();\n  }\n\n  /**\n   * Set the default geometry for the feature.  This will update the property\n   * with the name returned by {@link module:ol/Feature~Feature#getGeometryName}.\n   * @param {Geometry|undefined} geometry The new geometry.\n   * @api\n   * @observable\n   */\n  setGeometry(geometry) {\n    this.set(this.geometryName_, geometry);\n  }\n\n  /**\n   * Set the style for the feature to override the layer style.  This can be a\n   * single style object, an array of styles, or a function that takes a\n   * resolution and returns an array of styles. To unset the feature style, call\n   * `setStyle()` without arguments or a falsey value.\n   * @param {import(\"./style/Style.js\").StyleLike} [style] Style for this feature.\n   * @api\n   * @fires module:ol/events/Event~BaseEvent#event:change\n   */\n  setStyle(style) {\n    this.style_ = style;\n    this.styleFunction_ = !style ? undefined : createStyleFunction(style);\n    this.changed();\n  }\n\n  /**\n   * Set the feature id.  The feature id is considered stable and may be used when\n   * requesting features or comparing identifiers returned from a remote source.\n   * The feature id can be used with the\n   * {@link module:ol/source/Vector~VectorSource#getFeatureById} method.\n   * @param {number|string|undefined} id The feature id.\n   * @api\n   * @fires module:ol/events/Event~BaseEvent#event:change\n   */\n  setId(id) {\n    this.id_ = id;\n    this.changed();\n  }\n\n  /**\n   * Set the property name to be used when getting the feature's default geometry.\n   * When calling {@link module:ol/Feature~Feature#getGeometry}, the value of the property with\n   * this name will be returned.\n   * @param {string} name The property name of the default geometry.\n   * @api\n   */\n  setGeometryName(name) {\n    this.removeChangeListener(this.geometryName_, this.handleGeometryChanged_);\n    this.geometryName_ = name;\n    this.addChangeListener(this.geometryName_, this.handleGeometryChanged_);\n    this.handleGeometryChanged_();\n  }\n}\n\n/**\n * Convert the provided object into a feature style function.  Functions passed\n * through unchanged.  Arrays of Style or single style objects wrapped\n * in a new feature style function.\n * @param {!import(\"./style/Style.js\").StyleFunction|!Array<import(\"./style/Style.js\").default>|!import(\"./style/Style.js\").default} obj\n *     A feature style function, a single style, or an array of styles.\n * @return {import(\"./style/Style.js\").StyleFunction} A style function.\n */\nexport function createStyleFunction(obj) {\n  if (typeof obj === 'function') {\n    return obj;\n  }\n  /**\n   * @type {Array<import(\"./style/Style.js\").default>}\n   */\n  let styles;\n  if (Array.isArray(obj)) {\n    styles = obj;\n  } else {\n    assert(\n      typeof (/** @type {?} */ (obj).getZIndex) === 'function',\n      'Expected an `ol/style/Style` or an array of `ol/style/Style.js`',\n    );\n    const style = /** @type {import(\"./style/Style.js\").default} */ (obj);\n    styles = [style];\n  }\n  return function () {\n    return styles;\n  };\n}\nexport default Feature;\n"], "mappings": ";;;;;;;;;AA0EA,IAAM,UAAN,MAAM,iBAAgB,eAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO/B,YAAY,sBAAsB;AAChC,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,MAAM;AAMX,SAAK,gBAAgB;AAOrB,SAAK,SAAS;AAMd,SAAK,iBAAiB;AAMtB,SAAK,qBAAqB;AAE1B,SAAK,kBAAkB,KAAK,eAAe,KAAK,sBAAsB;AAEtE,QAAI,sBAAsB;AACxB,UACE;AAAA,MACoB,qBAAsB,0BACpC,YACN;AACA,cAAM;AAAA;AAAA,UAAoC;AAAA;AAC1C,aAAK,YAAY,QAAQ;AAAA,MAC3B,OAAO;AAEL,cAAM,aAAa;AACnB,aAAK,cAAc,UAAU;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM;AAAA;AAAA,MACJ,IAAI,SAAQ,KAAK,cAAc,IAAI,KAAK,cAAc,IAAI,IAAI;AAAA;AAEhE,UAAM,gBAAgB,KAAK,gBAAgB,CAAC;AAC5C,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,UAAU;AACZ,YAAM;AAAA;AAAA,QAAqC,SAAS,MAAM;AAAA,MAAE;AAAA,IAC9D;AACA,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,OAAO;AACT,YAAM,SAAS,KAAK;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc;AACZ;AAAA;AAAA,MAA0C,KAAK,IAAI,KAAK,aAAa;AAAA;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,QAAI,KAAK,oBAAoB;AAC3B,oBAAc,KAAK,kBAAkB;AACrC,WAAK,qBAAqB;AAAA,IAC5B;AACA,UAAM,WAAW,KAAK,YAAY;AAClC,QAAI,UAAU;AACZ,WAAK,qBAAqB;AAAA,QACxB;AAAA,QACA,kBAAU;AAAA,QACV,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,UAAU;AACpB,SAAK,IAAI,KAAK,eAAe,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,OAAO;AACd,SAAK,SAAS;AACd,SAAK,iBAAiB,CAAC,QAAQ,SAAY,oBAAoB,KAAK;AACpE,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,IAAI;AACR,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM;AACpB,SAAK,qBAAqB,KAAK,eAAe,KAAK,sBAAsB;AACzE,SAAK,gBAAgB;AACrB,SAAK,kBAAkB,KAAK,eAAe,KAAK,sBAAsB;AACtE,SAAK,uBAAuB;AAAA,EAC9B;AACF;AAUO,SAAS,oBAAoB,KAAK;AACvC,MAAI,OAAO,QAAQ,YAAY;AAC7B,WAAO;AAAA,EACT;AAIA,MAAI;AACJ,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAS;AAAA,EACX,OAAO;AACL;AAAA,MACE;AAAA,MAA0B,IAAK,cAAe;AAAA,MAC9C;AAAA,IACF;AACA,UAAM;AAAA;AAAA,MAA2D;AAAA;AACjE,aAAS,CAAC,KAAK;AAAA,EACjB;AACA,SAAO,WAAY;AACjB,WAAO;AAAA,EACT;AACF;AACA,IAAO,kBAAQ;", "names": []}