import { CAPABILITY_TYPE_ENUM } from '@/shared/enum/capability.enum';
import { LocationRecord } from '@/shared/models/location.model';
import { CoreSolutionWithoutLegalEntity } from '@/shared/models/country-response.model';

export type Option = {
  name: string;
  code: string;
  description: string;
  location: string;
  status: string;
};

export type CapabilitySearchOption = {
  [x: string]: any;
  id: number;
  capability: string;
  capabilityType: CAPABILITY_TYPE_ENUM;
  category: {
    title: string;
  };
  product: string;
  subCategory: string;
  coreSolution: CoreSolutionWithoutLegalEntity;
  status?: string;
};

export type CustomAutocompleteCapabilitySearchDropdownProps = {
  searchResults: object[];
  handleSearch: (value: string) => void;
  handleSelect: (value: CapabilitySearchOption | null) => void;
  searching: boolean;
  inputValue: string;
  openDropdown: boolean;
  setOpenDropdown: (open: boolean) => void;
  placeholder?: string;
  statusColors?: { [key: string]: string };
  maxHeight?: string;
  noResultsText?: string;
  loadingText?: string;
  iconSize?: number;
  chipLabelStyle?: object;
  textFieldStyle?: object;
  paperStyle?: object;
  listItemStyle?: object;
  showChips?: boolean;
  showDescription?: boolean;
  noResultsTextStyle?: object;
  getLabelKey?: string;
  loadingContainer?: object;
  // CustomDropdownComponent: any;
};

export type CustomAutocompleteLocationSearchDropdownProps = {
  searchResults: LocationRecord[];
  handleSearch: (value: string) => void;
  handleSelect: (value: LocationRecord | null) => void;
  searching: boolean;
  inputValue: string;
  openDropdown: boolean;
  setOpenDropdown: (open: boolean) => void;
  placeholder?: string;
  statusColors: { [key: string]: string };
  maxHeight?: string;
  noResultsText?: string;
  loadingText?: string;
  iconSize?: number;
  chipLabelStyle?: object;
  textFieldStyle?: object;
  paperStyle?: object;
  listItemStyle?: object;
  showChips?: boolean;
  showDescription?: boolean;
  noResultsTextStyle?: object;
  getLabelKey?: string;
  loadingContainer?: object;
  // CustomDropdownComponent: any;
};

export type CustomAutocompleteSearchProps = {
  searchResults: object[];
  handleSearch: (value: string) => void;
  handleSelect: (value: Option | null) => void;
  searching: boolean;
  inputValue: string;
  openDropdown: boolean;
  setOpenDropdown: (open: boolean) => void;
  placeholder?: string;
  statusColors: { [key: string]: string };
  maxHeight?: string;
  noResultsText?: string;
  loadingText?: string;
  iconSize?: number;
  chipLabelStyle?: object;
  textFieldStyle?: object;
  paperStyle?: object;
  listItemStyle?: object;
  showChips?: boolean;
  showDescription?: boolean;
  noResultsTextStyle?: object;
  getLabelKey: string;
  loadingContainer?: object;
  CustomDropdownComponent: any;
};
