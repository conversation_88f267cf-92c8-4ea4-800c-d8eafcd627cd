{"version": 3, "sources": ["../../@mui/x-tree-view/hooks/useTreeViewApiRef.js", "../../@mui/x-tree-view/hooks/useTreeItem2Utils/useTreeItem2Utils.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * Hook that instantiates a [[TreeViewApiRef]].\n */\nexport const useTreeViewApiRef = () => React.useRef(undefined);", "import { useTreeViewContext } from \"../../internals/TreeViewProvider/index.js\";\nimport { useTreeViewLabel } from \"../../internals/plugins/useTreeViewLabel/index.js\";\nimport { hasPlugin } from \"../../internals/utils/plugins.js\";\n\n/**\n * Plugins that need to be present in the Tree View in order for `useTreeItem2Utils` to work correctly.\n */\n\n/**\n * Plugins that `useTreeItem2Utils` can use if they are present, but are not required.\n */\n\nconst isItemExpandable = reactChildren => {\n  if (Array.isArray(reactChildren)) {\n    return reactChildren.length > 0 && reactChildren.some(isItemExpandable);\n  }\n  return Boolean(reactChildren);\n};\nexport const useTreeItem2Utils = ({\n  itemId,\n  children\n}) => {\n  const {\n    instance,\n    selection: {\n      multiSelect\n    },\n    publicAPI\n  } = useTreeViewContext();\n  const status = {\n    expandable: isItemExpandable(children),\n    expanded: instance.isItemExpanded(itemId),\n    focused: instance.isItemFocused(itemId),\n    selected: instance.isItemSelected(itemId),\n    disabled: instance.isItemDisabled(itemId),\n    editing: instance?.isItemBeingEdited ? instance?.isItemBeingEdited(itemId) : false,\n    editable: instance.isItemEditable ? instance.isItemEditable(itemId) : false\n  };\n  const handleExpansion = event => {\n    if (status.disabled) {\n      return;\n    }\n    if (!status.focused) {\n      instance.focusItem(event, itemId);\n    }\n    const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);\n\n    // If already expanded and trying to toggle selection don't close\n    if (status.expandable && !(multiple && instance.isItemExpanded(itemId))) {\n      instance.toggleItemExpansion(event, itemId);\n    }\n  };\n  const handleSelection = event => {\n    if (status.disabled) {\n      return;\n    }\n    if (!status.focused && !status.editing) {\n      instance.focusItem(event, itemId);\n    }\n    const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);\n    if (multiple) {\n      if (event.shiftKey) {\n        instance.expandSelectionRange(event, itemId);\n      } else {\n        instance.selectItem({\n          event,\n          itemId,\n          keepExistingSelection: true\n        });\n      }\n    } else {\n      instance.selectItem({\n        event,\n        itemId,\n        shouldBeSelected: true\n      });\n    }\n  };\n  const handleCheckboxSelection = event => {\n    const hasShift = event.nativeEvent.shiftKey;\n    if (multiSelect && hasShift) {\n      instance.expandSelectionRange(event, itemId);\n    } else {\n      instance.selectItem({\n        event,\n        itemId,\n        keepExistingSelection: multiSelect,\n        shouldBeSelected: event.target.checked\n      });\n    }\n  };\n  const toggleItemEditing = () => {\n    if (!hasPlugin(instance, useTreeViewLabel)) {\n      return;\n    }\n    if (instance.isItemEditable(itemId)) {\n      if (instance.isItemBeingEdited(itemId)) {\n        instance.setEditedItemId(null);\n      } else {\n        instance.setEditedItemId(itemId);\n      }\n    }\n  };\n  const handleSaveItemLabel = (event, label) => {\n    if (!hasPlugin(instance, useTreeViewLabel)) {\n      return;\n    }\n\n    // As a side effect of `instance.focusItem` called here and in `handleCancelItemLabelEditing` the `labelInput` is blurred\n    // The `onBlur` event is triggered, which calls `handleSaveItemLabel` again.\n    // To avoid creating an unwanted behavior we need to check if the item is being edited before calling `updateItemLabel`\n    // using `instance.isItemBeingEditedRef` instead of `instance.isItemBeingEdited` since the state is not yet updated in this point\n    if (instance.isItemBeingEditedRef(itemId)) {\n      instance.updateItemLabel(itemId, label);\n      toggleItemEditing();\n      instance.focusItem(event, itemId);\n    }\n  };\n  const handleCancelItemLabelEditing = event => {\n    if (!hasPlugin(instance, useTreeViewLabel)) {\n      return;\n    }\n    if (instance.isItemBeingEditedRef(itemId)) {\n      toggleItemEditing();\n      instance.focusItem(event, itemId);\n    }\n  };\n  const interactions = {\n    handleExpansion,\n    handleSelection,\n    handleCheckboxSelection,\n    toggleItemEditing,\n    handleSaveItemLabel,\n    handleCancelItemLabelEditing\n  };\n  return {\n    interactions,\n    status,\n    publicAPI\n  };\n};"], "mappings": ";;;;;;;;;;;;;;;;AAEA,YAAuB;AAIhB,IAAM,oBAAoB,MAAY,aAAO,MAAS;;;ACM7D,IAAM,mBAAmB,mBAAiB;AACxC,MAAI,MAAM,QAAQ,aAAa,GAAG;AAChC,WAAO,cAAc,SAAS,KAAK,cAAc,KAAK,gBAAgB;AAAA,EACxE;AACA,SAAO,QAAQ,aAAa;AAC9B;AACO,IAAM,oBAAoB,CAAC;AAAA,EAChC;AAAA,EACA;AACF,MAAM;AACJ,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AACvB,QAAM,SAAS;AAAA,IACb,YAAY,iBAAiB,QAAQ;AAAA,IACrC,UAAU,SAAS,eAAe,MAAM;AAAA,IACxC,SAAS,SAAS,cAAc,MAAM;AAAA,IACtC,UAAU,SAAS,eAAe,MAAM;AAAA,IACxC,UAAU,SAAS,eAAe,MAAM;AAAA,IACxC,UAAS,qCAAU,qBAAoB,qCAAU,kBAAkB,UAAU;AAAA,IAC7E,UAAU,SAAS,iBAAiB,SAAS,eAAe,MAAM,IAAI;AAAA,EACxE;AACA,QAAM,kBAAkB,WAAS;AAC/B,QAAI,OAAO,UAAU;AACnB;AAAA,IACF;AACA,QAAI,CAAC,OAAO,SAAS;AACnB,eAAS,UAAU,OAAO,MAAM;AAAA,IAClC;AACA,UAAM,WAAW,gBAAgB,MAAM,YAAY,MAAM,WAAW,MAAM;AAG1E,QAAI,OAAO,cAAc,EAAE,YAAY,SAAS,eAAe,MAAM,IAAI;AACvE,eAAS,oBAAoB,OAAO,MAAM;AAAA,IAC5C;AAAA,EACF;AACA,QAAM,kBAAkB,WAAS;AAC/B,QAAI,OAAO,UAAU;AACnB;AAAA,IACF;AACA,QAAI,CAAC,OAAO,WAAW,CAAC,OAAO,SAAS;AACtC,eAAS,UAAU,OAAO,MAAM;AAAA,IAClC;AACA,UAAM,WAAW,gBAAgB,MAAM,YAAY,MAAM,WAAW,MAAM;AAC1E,QAAI,UAAU;AACZ,UAAI,MAAM,UAAU;AAClB,iBAAS,qBAAqB,OAAO,MAAM;AAAA,MAC7C,OAAO;AACL,iBAAS,WAAW;AAAA,UAClB;AAAA,UACA;AAAA,UACA,uBAAuB;AAAA,QACzB,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,eAAS,WAAW;AAAA,QAClB;AAAA,QACA;AAAA,QACA,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,0BAA0B,WAAS;AACvC,UAAM,WAAW,MAAM,YAAY;AACnC,QAAI,eAAe,UAAU;AAC3B,eAAS,qBAAqB,OAAO,MAAM;AAAA,IAC7C,OAAO;AACL,eAAS,WAAW;AAAA,QAClB;AAAA,QACA;AAAA,QACA,uBAAuB;AAAA,QACvB,kBAAkB,MAAM,OAAO;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,oBAAoB,MAAM;AAC9B,QAAI,CAAC,UAAU,UAAU,gBAAgB,GAAG;AAC1C;AAAA,IACF;AACA,QAAI,SAAS,eAAe,MAAM,GAAG;AACnC,UAAI,SAAS,kBAAkB,MAAM,GAAG;AACtC,iBAAS,gBAAgB,IAAI;AAAA,MAC/B,OAAO;AACL,iBAAS,gBAAgB,MAAM;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,QAAM,sBAAsB,CAAC,OAAO,UAAU;AAC5C,QAAI,CAAC,UAAU,UAAU,gBAAgB,GAAG;AAC1C;AAAA,IACF;AAMA,QAAI,SAAS,qBAAqB,MAAM,GAAG;AACzC,eAAS,gBAAgB,QAAQ,KAAK;AACtC,wBAAkB;AAClB,eAAS,UAAU,OAAO,MAAM;AAAA,IAClC;AAAA,EACF;AACA,QAAM,+BAA+B,WAAS;AAC5C,QAAI,CAAC,UAAU,UAAU,gBAAgB,GAAG;AAC1C;AAAA,IACF;AACA,QAAI,SAAS,qBAAqB,MAAM,GAAG;AACzC,wBAAkB;AAClB,eAAS,UAAU,OAAO,MAAM;AAAA,IAClC;AAAA,EACF;AACA,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}