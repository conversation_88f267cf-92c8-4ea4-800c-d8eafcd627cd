{"version": 3, "sources": ["../../@mui/material/FormLabel/FormLabel.js", "../../@mui/material/FormLabel/formLabelClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from \"../FormControl/formControlState.js\";\nimport useFormControl from \"../FormControl/useFormControl.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport formLabelClasses, { getFormLabelUtilityClasses } from \"./formLabelClasses.js\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    focused,\n    disabled,\n    error,\n    filled,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, disabled && 'disabled', error && 'error', filled && 'filled', focused && 'focused', required && 'required'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormLabelUtilityClasses, classes);\n};\nexport const FormLabelRoot = styled('label', {\n  name: 'MuiFormLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.color === 'secondary' && styles.colorSecondary, ownerState.filled && styles.filled];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary,\n  ...theme.typography.body1,\n  lineHeight: '1.4375em',\n  padding: 0,\n  position: 'relative',\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()).map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${formLabelClasses.focused}`]: {\n        color: (theme.vars || theme).palette[color].main\n      }\n    }\n  })), {\n    props: {},\n    style: {\n      [`&.${formLabelClasses.disabled}`]: {\n        color: (theme.vars || theme).palette.text.disabled\n      },\n      [`&.${formLabelClasses.error}`]: {\n        color: (theme.vars || theme).palette.error.main\n      }\n    }\n  }]\n})));\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})(memoTheme(({\n  theme\n}) => ({\n  [`&.${formLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n})));\nconst FormLabel = /*#__PURE__*/React.forwardRef(function FormLabel(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormLabel'\n  });\n  const {\n    children,\n    className,\n    color,\n    component = 'label',\n    disabled,\n    error,\n    filled,\n    focused,\n    required,\n    ...other\n  } = props;\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['color', 'required', 'focused', 'disabled', 'error', 'filled']\n  });\n  const ownerState = {\n    ...props,\n    color: fcs.color || 'primary',\n    component,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(FormLabelRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    children: [children, fcs.required && /*#__PURE__*/_jsxs(AsteriskComponent, {\n      ownerState: ownerState,\n      \"aria-hidden\": true,\n      className: classes.asterisk,\n      children: [\"\\u2009\", '*']\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the label should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the label should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the input of this label is focused (used by `FormGroup` components).\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormLabel;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormLabelUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormLabel', slot);\n}\nconst formLabelClasses = generateUtilityClasses('MuiFormLabel', ['root', 'colorSecondary', 'focused', 'disabled', 'error', 'filled', 'required', 'asterisk']);\nexport default formLabelClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,2BAA2B,MAAM;AAC/C,SAAO,qBAAqB,gBAAgB,IAAI;AAClD;AACA,IAAM,mBAAmB,uBAAuB,gBAAgB,CAAC,QAAQ,kBAAkB,WAAW,YAAY,SAAS,UAAU,YAAY,UAAU,CAAC;AAC5J,IAAO,2BAAQ;;;ADQf,yBAA8B;AAC9B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,QAAQ,mBAAW,KAAK,CAAC,IAAI,YAAY,YAAY,SAAS,SAAS,UAAU,UAAU,WAAW,WAAW,YAAY,UAAU;AAAA,IACtJ,UAAU,CAAC,YAAY,SAAS,OAAO;AAAA,EACzC;AACA,SAAO,eAAe,OAAO,4BAA4B,OAAO;AAClE;AACO,IAAM,gBAAgB,eAAO,SAAS;AAAA,EAC3C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,UAAU,eAAe,OAAO,gBAAgB,WAAW,UAAU,OAAO,MAAM;AAAA,EACpH;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC1C,GAAG,MAAM,WAAW;AAAA,EACpB,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU,CAAC,GAAG,OAAO,QAAQ,MAAM,OAAO,EAAE,OAAO,+BAA+B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,OAAO;AAAA,IACrG,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,CAAC,KAAK,yBAAiB,OAAO,EAAE,GAAG;AAAA,QACjC,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,MAC9C;AAAA,IACF;AAAA,EACF,EAAE,GAAG;AAAA,IACH,OAAO,CAAC;AAAA,IACR,OAAO;AAAA,MACL,CAAC,KAAK,yBAAiB,QAAQ,EAAE,GAAG;AAAA,QAClC,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,MAC5C;AAAA,MACA,CAAC,KAAK,yBAAiB,KAAK,EAAE,GAAG;AAAA,QAC/B,QAAQ,MAAM,QAAQ,OAAO,QAAQ,MAAM;AAAA,MAC7C;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,oBAAoB,eAAO,QAAQ;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,CAAC,KAAK,yBAAiB,KAAK,EAAE,GAAG;AAAA,IAC/B,QAAQ,MAAM,QAAQ,OAAO,QAAQ,MAAM;AAAA,EAC7C;AACF,EAAE,CAAC;AACH,IAAM,YAA+B,iBAAW,SAASA,WAAU,SAAS,KAAK;AAC/E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,iBAAiB,eAAe;AACtC,QAAM,MAAM,iBAAiB;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,QAAQ,CAAC,SAAS,YAAY,WAAW,YAAY,SAAS,QAAQ;AAAA,EACxE,CAAC;AACD,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH,OAAO,IAAI,SAAS;AAAA,IACpB;AAAA,IACA,UAAU,IAAI;AAAA,IACd,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ,SAAS,IAAI;AAAA,IACb,UAAU,IAAI;AAAA,EAChB;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,MAAM,eAAe;AAAA,IACvC,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,GAAG;AAAA,IACH,UAAU,CAAC,UAAU,IAAI,gBAAyB,mBAAAA,MAAM,mBAAmB;AAAA,MACzE;AAAA,MACA,eAAe;AAAA,MACf,WAAW,QAAQ;AAAA,MACnB,UAAU,CAAC,KAAU,GAAG;AAAA,IAC1B,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,UAAU,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,SAAS,QAAQ,WAAW,aAAa,WAAW,SAAS,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrK,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,oBAAQ;", "names": ["FormLabel", "_jsxs", "PropTypes"]}