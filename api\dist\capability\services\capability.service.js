"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CapabilityService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const dtos_1 = require("../dtos");
const repositories_2 = require("../../location/repositories");
const exceptions_1 = require("../../shared/exceptions");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const dtos_2 = require("../../metadata/dtos");
const location_wise_capability_repository_1 = require("../repositories/location-wise-capability.repository");
const services_1 = require("../../shared/services");
const constants_1 = require("../../shared/constants");
const clients_1 = require("../../shared/clients");
const pagination_1 = require("../../core/pagination");
const services_2 = require("../../business-entity/services");
const moment_1 = __importDefault(require("moment"));
const repositories_3 = require("../../bingo-card/repositories");
let CapabilityService = class CapabilityService {
    constructor(capabilityRepository, locationRepository, locationWiseCapabilityRepository, databaseHelper, sharedAttachmentService, attachmentApiClient, businessEntityService, excelSheetService, capabilityLegRepository, permissionService, historyService, bingoCardConfigRepository) {
        this.capabilityRepository = capabilityRepository;
        this.locationRepository = locationRepository;
        this.locationWiseCapabilityRepository = locationWiseCapabilityRepository;
        this.databaseHelper = databaseHelper;
        this.sharedAttachmentService = sharedAttachmentService;
        this.attachmentApiClient = attachmentApiClient;
        this.businessEntityService = businessEntityService;
        this.excelSheetService = excelSheetService;
        this.capabilityLegRepository = capabilityLegRepository;
        this.permissionService = permissionService;
        this.historyService = historyService;
        this.bingoCardConfigRepository = bingoCardConfigRepository;
    }
    searchMasterCapability(searchTerm = null, locationId = null) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let locationDetail = null;
            if (locationId) {
                locationDetail = yield this.locationRepository.getLocationById(locationId);
                if (!locationDetail) {
                    throw new exceptions_1.HttpException('Invalid location.', enums_1.HttpStatus.NOT_FOUND);
                }
                if (!((_a = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.locationType) === null || _a === void 0 ? void 0 : _a.canAcquireCapability)) {
                    throw new exceptions_1.HttpException('Location is not allowed to acquire entries.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
            }
            const result = yield this.capabilityRepository.searchMasterCapabilityByCoreSolution(searchTerm || null, (locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.coreSolutionId) || null);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.MasterCapabilityDropdownResponseDto, result);
        });
    }
    masterCapabilityDetailById(locationId, capabilityId) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const locationDetail = yield this.locationRepository.getLocationById(locationId);
            if (!locationDetail || !locationDetail.coreSolutionId) {
                throw new exceptions_1.HttpException("Location doesn't exist.", enums_1.HttpStatus.NOT_FOUND);
            }
            if (!((_a = locationDetail === null || locationDetail === void 0 ? void 0 : locationDetail.locationType) === null || _a === void 0 ? void 0 : _a.canAcquireCapability)) {
                throw new exceptions_1.HttpException('Location is not allowed to acquire entries.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            const capabilityDetail = yield this.capabilityRepository.getMasterCapabilityDetailById(capabilityId);
            if (!capabilityDetail) {
                throw new exceptions_1.HttpException("Entry doesn't exist.", enums_1.HttpStatus.NOT_FOUND);
            }
            if (((_b = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.coreSolution) === null || _b === void 0 ? void 0 : _b.id) &&
                locationDetail.coreSolutionId !== capabilityDetail.coreSolution.id) {
                throw new exceptions_1.HttpException("Entry doen't belong to this location.", enums_1.HttpStatus.NOT_FOUND);
            }
            const allLegs = yield this.capabilityLegRepository.getAllLegs();
            const legDetails = allLegs.filter(leg => capabilityDetail.legs.includes(leg.id));
            capabilityDetail.masterLegs = legDetails;
            return (0, helpers_1.singleObjectToInstance)(dtos_1.MasterCapabilityWithLocation, {
                capabilityDetail: (0, helpers_1.singleObjectToInstance)(dtos_1.MasterCapabilityDetailResponseDto, capabilityDetail),
                locationDetail: (0, helpers_1.singleObjectToInstance)(dtos_2.MasterCapabilityLocationResponseDto, locationDetail),
            });
        });
    }
    upsertCapability(capabilitySetupRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, deletedFileIds, locationId, capabilityId } = capabilitySetupRequestDto, otherDetails = __rest(capabilitySetupRequestDto, ["id", "deletedFileIds", "locationId", "capabilityId"]);
            const { status, statusDate, owners, coOwners, description, provider, providerName, evidenceDetail, capabilityTypeDetail, operationalDetail, additionalDetail, capabilityLegs, } = otherDetails;
            const locationDetail = yield this.locationRepository.getLocationById(locationId);
            const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE_CAPABILITY, enums_1.PERMISSIONS.LOCAL_MANAGE_CAPABILITY], currentContext.user, locationDetail.entityId, locationId);
            if (!hasPermission) {
                throw new exceptions_1.HttpException("You don't have permission to manage entry for this location.", enums_1.HttpStatus.FORBIDDEN);
            }
            const masterCapabilityDetail = yield this.capabilityRepository.getMasterCapabilityById(capabilityId);
            if (!locationDetail) {
                throw new exceptions_1.HttpException("Location doesn't exist.", enums_1.HttpStatus.NOT_FOUND);
            }
            if (!masterCapabilityDetail) {
                throw new exceptions_1.HttpException("Entry doesn't exist.", enums_1.HttpStatus.NOT_FOUND);
            }
            if ((masterCapabilityDetail === null || masterCapabilityDetail === void 0 ? void 0 : masterCapabilityDetail.coreSolutionId) &&
                locationDetail.coreSolutionId !== masterCapabilityDetail.coreSolutionId) {
                throw new exceptions_1.HttpException("Entry doen't belong to this location.", enums_1.HttpStatus.NOT_FOUND);
            }
            const existingCapabity = yield this.locationWiseCapabilityRepository.getLocationWiseCapabilityDetail(locationId, capabilityId);
            if (!id && existingCapabity) {
                throw new exceptions_1.HttpException('Entry already added for this location.', enums_1.HttpStatus.CONFLICT);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                var _a;
                const _b = operationalDetail || {}, { documentList } = _b, restOperationalDetail = __rest(_b, ["documentList"]);
                const _c = additionalDetail || {}, { tags, additionalDocuments } = _c, restAdditionalDetail = __rest(_c, ["tags", "additionalDocuments"]);
                let _d = evidenceDetail || {}, { evidenceDocuments } = _d, restEvidenceDetail = __rest(_d, ["evidenceDocuments"]);
                const dataset = Object.assign(Object.assign(Object.assign(Object.assign({}, (!id && {
                    locationId,
                    capabilityId,
                })), { status,
                    statusDate, provider: provider || null, providerName: providerName || null, tags: (tags === null || tags === void 0 ? void 0 : tags.length) ? tags : null, legs: (capabilityLegs === null || capabilityLegs === void 0 ? void 0 : capabilityLegs.length) ? capabilityLegs : [] }), (restEvidenceDetail &&
                    Object.keys(restEvidenceDetail).length > 0 && { evidenceDetail: restEvidenceDetail })), { otherDetails: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (owners && Object.keys(owners).length > 0 && { owners })), (coOwners && coOwners.length > 0 && { coOwners })), { description: description || '' }), (capabilityTypeDetail &&
                        Object.keys(capabilityTypeDetail).length > 0 && { capabilityTypeDetail })), (restOperationalDetail &&
                        Object.keys(restOperationalDetail).length > 0 && {
                        operationalDetail: restOperationalDetail,
                    })), (restAdditionalDetail &&
                        Object.keys(restAdditionalDetail).length > 0 && {
                        additionalDetail: restAdditionalDetail,
                    })) });
                let newCapabilityId = null;
                if (id) {
                    newCapabilityId = id;
                    yield this.locationWiseCapabilityRepository.updateCapabilityDetailById(id, dataset, currentContext);
                }
                else {
                    const newCapability = yield this.locationWiseCapabilityRepository.addNewCapability(dataset, currentContext);
                    newCapabilityId = newCapability.id;
                }
                if (((_a = masterCapabilityDetail === null || masterCapabilityDetail === void 0 ? void 0 : masterCapabilityDetail.evidence) === null || _a === void 0 ? void 0 : _a.type) &&
                    masterCapabilityDetail.evidence.type === enums_1.EVIDENCE_TYPE.UPLOAD &&
                    (evidenceDocuments === null || evidenceDocuments === void 0 ? void 0 : evidenceDocuments.length)) {
                    const newEvidenceDocuments = evidenceDocuments.filter(attachment => (attachment === null || attachment === void 0 ? void 0 : attachment.isNew) || !(attachment === null || attachment === void 0 ? void 0 : attachment.file_id));
                    if (newEvidenceDocuments === null || newEvidenceDocuments === void 0 ? void 0 : newEvidenceDocuments.length) {
                        yield this.sharedAttachmentService.supportingDocumentsActivity(newEvidenceDocuments, newCapabilityId, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_EVIDENCE, constants_1.ATTACHMENT_REL_PATH.CAPABILITY_EVIDENCE, currentContext.user.username);
                    }
                }
                if (documentList === null || documentList === void 0 ? void 0 : documentList.length) {
                    const newDocumentList = documentList.filter(attachment => (attachment === null || attachment === void 0 ? void 0 : attachment.isNew) || !(attachment === null || attachment === void 0 ? void 0 : attachment.file_id));
                    if (newDocumentList === null || newDocumentList === void 0 ? void 0 : newDocumentList.length) {
                        yield this.sharedAttachmentService.supportingDocumentsActivity(newDocumentList, newCapabilityId, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_DOCUMENT_LIST, constants_1.ATTACHMENT_REL_PATH.CAPABILITY_DOCUMENT_LIST, currentContext.user.username);
                    }
                }
                if (additionalDocuments === null || additionalDocuments === void 0 ? void 0 : additionalDocuments.length) {
                    const newAdditionalDocuments = additionalDocuments.filter(attachment => (attachment === null || attachment === void 0 ? void 0 : attachment.isNew) || !(attachment === null || attachment === void 0 ? void 0 : attachment.file_id));
                    if (newAdditionalDocuments === null || newAdditionalDocuments === void 0 ? void 0 : newAdditionalDocuments.length) {
                        yield this.sharedAttachmentService.supportingDocumentsActivity(newAdditionalDocuments, newCapabilityId, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_ADDITIONAL_DOCUMENT, constants_1.ATTACHMENT_REL_PATH.CAPABILITY_ADDITIONAL_DOCUMENT, currentContext.user.username);
                    }
                }
                if (deletedFileIds === null || deletedFileIds === void 0 ? void 0 : deletedFileIds.length) {
                    yield this.sharedAttachmentService.deleteBulkAttachmentByFileIds(deletedFileIds);
                }
                yield this.upsertHistoryCreation({
                    id,
                    newCapabilityId,
                    locationId,
                    locationDetail,
                    masterCapabilityDetail,
                    capabilitySetupRequestDto,
                    existingCapabity,
                    currentContext,
                });
                return {
                    message: id
                        ? 'Entry details updated successfully.'
                        : 'New entry successfully added for location.',
                    data: {
                        id: newCapabilityId,
                    },
                };
            }));
        });
    }
    upsertHistoryCreation(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, newCapabilityId, locationId, locationDetail, masterCapabilityDetail, capabilitySetupRequestDto, existingCapabity, currentContext, } = payload;
            let history = [];
            if (!id) {
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: newCapabilityId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.CAPABILITY,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: 'New entry added.',
                    additional_info: {
                        locationDetail,
                        masterCapabilityDetail,
                        capabilityInputData: capabilitySetupRequestDto,
                    },
                });
            }
            else {
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: newCapabilityId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.CAPABILITY,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                    action_date: new Date(),
                    comments: 'Entry details updated.',
                    additional_info: {
                        locationDetail,
                        masterCapabilityDetail,
                        capabilityInputData: capabilitySetupRequestDto,
                    },
                });
                if (capabilitySetupRequestDto.status !== existingCapabity.status) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: newCapabilityId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.CAPABILITY,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.STATUS_CHANGE,
                        action_date: new Date(),
                        comments: `Entry status changed from ${existingCapabity.status} to ${capabilitySetupRequestDto.status}.`,
                        additional_info: {
                            old: {
                                status: existingCapabity.status,
                                statusDate: existingCapabity.statusDate,
                            },
                            new: {
                                status: capabilitySetupRequestDto.status,
                                statusDate: capabilitySetupRequestDto.statusDate,
                            },
                        },
                    });
                }
                if (!(0, moment_1.default)(capabilitySetupRequestDto.statusDate).isSame((0, moment_1.default)(existingCapabity.statusDate), 'day')) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: newCapabilityId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.CAPABILITY,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        action_date: new Date(),
                        comments: `Entry status date changed from ${existingCapabity.statusDate} to ${capabilitySetupRequestDto.statusDate}.`,
                        additional_info: {
                            old: {
                                statusDate: existingCapabity.statusDate,
                            },
                            new: {
                                statusDate: capabilitySetupRequestDto.statusDate,
                            },
                        },
                    });
                }
            }
            if (history.length) {
                yield this.historyService.addBulkRequestHistory(history);
            }
        });
    }
    updateCapabilityStatus(id, updateStatusRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { status, statusDate } = updateStatusRequestDto;
            const capabilityDetail = yield this.locationWiseCapabilityRepository.getExistingCapabilityDetailById(id);
            if (!capabilityDetail) {
                throw new exceptions_1.HttpException("Entry doesn't exist.", enums_1.HttpStatus.NOT_FOUND);
            }
            const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE_CAPABILITY, enums_1.PERMISSIONS.LOCAL_MANAGE_CAPABILITY], currentContext.user, capabilityDetail.locationDetail.entityId, capabilityDetail.locationId);
            if (!hasPermission) {
                throw new exceptions_1.HttpException("You don't have permission to update status of this entry.", enums_1.HttpStatus.FORBIDDEN);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.locationWiseCapabilityRepository.updateCapabilityDetailById(id, {
                    status,
                    statusDate,
                }, currentContext);
                let history = [];
                if (updateStatusRequestDto.status !== capabilityDetail.status) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.CAPABILITY,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.STATUS_CHANGE,
                        action_date: new Date(),
                        comments: `Entry status changed from ${capabilityDetail.status} to ${updateStatusRequestDto.status}.`,
                        additional_info: {
                            old: {
                                status: capabilityDetail.status,
                                statusDate: capabilityDetail.statusDate,
                            },
                            new: {
                                status: updateStatusRequestDto.status,
                                statusDate: updateStatusRequestDto.statusDate,
                            },
                        },
                    });
                }
                if (!(0, moment_1.default)(updateStatusRequestDto.statusDate).isSame((0, moment_1.default)(capabilityDetail.statusDate), 'day')) {
                    history.push({
                        created_by: currentContext.user.username,
                        entity_id: id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.CAPABILITY,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        action_date: new Date(),
                        comments: `Entry status date changed from ${capabilityDetail.statusDate} to ${updateStatusRequestDto.statusDate}.`,
                        additional_info: {
                            old: {
                                statusDate: capabilityDetail.statusDate,
                            },
                            new: {
                                statusDate: updateStatusRequestDto.statusDate,
                            },
                        },
                    });
                }
                if (history.length) {
                    yield this.historyService.addBulkRequestHistory(history);
                }
                return {
                    message: 'Status updated successfully.',
                };
            }));
        });
    }
    getLocationCapabilityDetail(id) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        return __awaiter(this, void 0, void 0, function* () {
            const capabilityDetail = yield this.locationWiseCapabilityRepository.getLocationWiseCapabilityDetailById(id);
            if (!capabilityDetail) {
                throw new exceptions_1.HttpException("Entry doesn't exist.", enums_1.HttpStatus.NOT_FOUND);
            }
            const allLegs = yield this.capabilityLegRepository.getAllLegs();
            const masterLegs = allLegs.filter(leg => capabilityDetail.capabilityDetail.legs.includes(leg.id));
            const actualLegs = allLegs.filter(leg => capabilityDetail.legs.includes(leg.id));
            const returnData = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({ id: capabilityDetail.id, status: capabilityDetail.status, statusDate: (capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.statusDate) || null }, (((_a = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.otherDetails) === null || _a === void 0 ? void 0 : _a.owners) && {
                owners: (_b = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.otherDetails) === null || _b === void 0 ? void 0 : _b.owners,
            })), (((_d = (_c = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.otherDetails) === null || _c === void 0 ? void 0 : _c.coOwners) === null || _d === void 0 ? void 0 : _d.length) && {
                coOwners: capabilityDetail.otherDetails.coOwners,
            })), (((_e = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.otherDetails) === null || _e === void 0 ? void 0 : _e.description) && {
                description: capabilityDetail.otherDetails.description,
            })), ((capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.provider) && { provider: capabilityDetail.provider })), ((capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.providerName) && { providerName: capabilityDetail.providerName })), { capabilityLegs: actualLegs }), ((capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.evidenceDetail) && { evidenceDetail: capabilityDetail.evidenceDetail })), (((_f = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.otherDetails) === null || _f === void 0 ? void 0 : _f.capabilityTypeDetail) && {
                capabilityTypeDetail: capabilityDetail.otherDetails.capabilityTypeDetail,
            })), (((_g = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.otherDetails) === null || _g === void 0 ? void 0 : _g.operationalDetail) && {
                operationalDetail: capabilityDetail.otherDetails.operationalDetail,
            })), (((_h = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.otherDetails) === null || _h === void 0 ? void 0 : _h.additionalDetail) && {
                additionalDetail: capabilityDetail.otherDetails.additionalDetail,
            })), { locationDetail: capabilityDetail.locationDetail, capabilityDetail: Object.assign(Object.assign({}, capabilityDetail.capabilityDetail), { masterLegs }) });
            const [additionalDocuments, documentLists, evidenceDocuments] = yield Promise.all([
                this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_ADDITIONAL_DOCUMENT),
                this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_DOCUMENT_LIST),
                ((_k = (_j = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.capabilityDetail) === null || _j === void 0 ? void 0 : _j.evidence) === null || _k === void 0 ? void 0 : _k.type) === enums_1.EVIDENCE_TYPE.UPLOAD
                    ? this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_EVIDENCE)
                    : [],
            ]);
            if (additionalDocuments.length) {
                returnData.additionalDetail = Object.assign(Object.assign({}, (returnData.additionalDetail || {})), { additionalDocuments });
            }
            if ((_l = capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.tags) === null || _l === void 0 ? void 0 : _l.length) {
                returnData.additionalDetail = Object.assign(Object.assign({}, (returnData.additionalDetail || {})), { tags: capabilityDetail === null || capabilityDetail === void 0 ? void 0 : capabilityDetail.tags });
            }
            if (documentLists.length) {
                returnData.operationalDetail = Object.assign(Object.assign({}, (returnData.operationalDetail || {})), { documentList: documentLists });
            }
            if (evidenceDocuments.length) {
                returnData.evidenceDetail = Object.assign(Object.assign({}, (returnData.evidenceDetail || {})), { evidenceDocuments });
            }
            return (0, helpers_1.singleObjectToInstance)(dtos_1.LocationWiseCapabilityDetailResponseDto, returnData);
        });
    }
    deleteLocationCapability(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const capabilityDetail = yield this.locationWiseCapabilityRepository.getExistingCapabilityDetailById(id);
            if (!capabilityDetail) {
                throw new exceptions_1.HttpException("Entry doesn't exist.", enums_1.HttpStatus.NOT_FOUND);
            }
            const hasPermission = yield this.permissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE_CAPABILITY, enums_1.PERMISSIONS.LOCAL_MANAGE_CAPABILITY], currentContext.user, capabilityDetail.locationDetail.entityId, capabilityDetail.locationId);
            if (!hasPermission) {
                throw new exceptions_1.HttpException("You don't have permission to delete this entry.", enums_1.HttpStatus.FORBIDDEN);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.locationWiseCapabilityRepository.deleteLocationCapability(id, currentContext);
                const [additionalDocuments, documentLists, evidenceDocuments] = yield Promise.all([
                    this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_ADDITIONAL_DOCUMENT),
                    this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_DOCUMENT_LIST),
                    this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.CAPABILITY_EVIDENCE),
                ]);
                let fileIds = [];
                if (additionalDocuments === null || additionalDocuments === void 0 ? void 0 : additionalDocuments.length) {
                    fileIds = [...fileIds, ...additionalDocuments.map(attachment => attachment.file_id)];
                }
                if (documentLists === null || documentLists === void 0 ? void 0 : documentLists.length) {
                    fileIds = [...fileIds, ...documentLists.map(attachment => attachment.file_id)];
                }
                if (evidenceDocuments === null || evidenceDocuments === void 0 ? void 0 : evidenceDocuments.length) {
                    fileIds = [...fileIds, ...evidenceDocuments.map(attachment => attachment.file_id)];
                }
                if (fileIds === null || fileIds === void 0 ? void 0 : fileIds.length) {
                    yield this.sharedAttachmentService.deleteBulkAttachmentByFileIds(fileIds);
                }
                let history = [];
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.CAPABILITY,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                    action_date: new Date(),
                    comments: 'Entry has been deleted.',
                });
                if (history.length) {
                    yield this.historyService.addBulkRequestHistory(history);
                }
                return { message: 'Entry deleted successfully' };
            }));
        });
    }
    getCapabilitiesByFilter(page, limit, filterDto) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            if (((_a = filterDto === null || filterDto === void 0 ? void 0 : filterDto.entityIds) === null || _a === void 0 ? void 0 : _a.length) || (filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId)) {
                filterDto.entityIds = yield this.businessEntityService.getBusinessEntitiesChildIds((filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId) ? [filterDto.hierarchyEntityId] : filterDto.entityIds);
            }
            const { rows, count } = yield this.locationWiseCapabilityRepository.getCapabilitiesByFilter(filterDto, page, limit);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.CapabilityListResponseDto, rows, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    exportCapabilities(requestContext, filterDto) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const { currentContext, headers } = requestContext;
            const { username } = currentContext.user;
            if (((_a = filterDto === null || filterDto === void 0 ? void 0 : filterDto.entityIds) === null || _a === void 0 ? void 0 : _a.length) || (filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId)) {
                filterDto.entityIds = yield this.businessEntityService.getBusinessEntitiesChildIds((filterDto === null || filterDto === void 0 ? void 0 : filterDto.hierarchyEntityId) ? [filterDto.hierarchyEntityId] : filterDto.entityIds);
            }
            const capabilityList = yield this.locationWiseCapabilityRepository.getCapabilitiesToExport(filterDto);
            if (!(capabilityList === null || capabilityList === void 0 ? void 0 : capabilityList.length)) {
                throw new exceptions_1.HttpException('No capabilties available to export', enums_1.HttpStatus.NOT_FOUND);
            }
            const SHEET_NAME = 'CapMApp entry List - ' + new Date().getTime();
            const report = yield this.excelSheetService.createCapabilitySheet(capabilityList, SHEET_NAME);
            yield this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: -1,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.EXPORT,
                action_performed: enums_1.HISTORY_ACTION_TYPE.EXPORT,
                action_date: new Date(),
                comments: `Entry list exported.`,
                additional_info: {
                    filters: filterDto,
                    fileName: SHEET_NAME,
                    totalExportCount: capabilityList.length,
                    host: headers === null || headers === void 0 ? void 0 : headers.host,
                    origin: headers === null || headers === void 0 ? void 0 : headers.origin,
                },
            });
            return { report, filename: SHEET_NAME };
        });
    }
};
CapabilityService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.CapabilityRepository,
        repositories_2.LocationRepository,
        location_wise_capability_repository_1.LocationWiseCapabilityRepository,
        helpers_1.DatabaseHelper,
        services_1.SharedAttachmentService,
        clients_1.AttachmentApiClient,
        services_2.BusinessEntityService,
        services_1.ExcelSheetService,
        repositories_1.CapabilityLegRepository,
        services_1.SharedPermissionService,
        clients_1.HistoryApiClient,
        repositories_3.BingoCardConfigRepository])
], CapabilityService);
exports.CapabilityService = CapabilityService;
//# sourceMappingURL=capability.service.js.map