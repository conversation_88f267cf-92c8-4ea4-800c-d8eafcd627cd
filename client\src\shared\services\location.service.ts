import { ReplaceUrlVariable } from '@/routes/paths';
import * as http from '@/shared/services';
import { API_PATHS } from '../constants';
import {
  AvailableCapabilitesFiltersModel,
  GeneralLocationResponse,
  LocationBasicDetailResponse,
  LocationCapabilityResponse,
  LocationCompleteDetailResponse,
  LocationListFiltersModel,
  LocationListResponse,
  LocationStatusUpdateRequestModel,
  LocationUpsertRequestModel,
  PartnerBranches,
  RequestPartnerBranchSetup,
  UpdatePartnerShipStatus,
} from '../models';

export const createLocationRelationship = (data: RequestPartnerBranchSetup): Promise<GeneralLocationResponse> => {
  return http.post<GeneralLocationResponse>(API_PATHS.PARTNER_BRANCH.CREATE, data);
};
export const updateRelationShipStatus = (data: UpdatePartnerShipStatus): Promise<GeneralLocationResponse> => {
  return http.post<GeneralLocationResponse>(API_PATHS.PARTNER_BRANCH.ACTION, data);
};
export const getPartnerBranches = (id: number): Promise<PartnerBranches> => {
  return http.get<PartnerBranches>(ReplaceUrlVariable(API_PATHS.PARTNER_BRANCH.GET, { id }));
};
export const upsertLocation = (data: LocationUpsertRequestModel): Promise<GeneralLocationResponse> => {
  return http.post<GeneralLocationResponse>(API_PATHS.LOCATION.UPSERT, data);
};
export const updateLocationStatus = (data: LocationStatusUpdateRequestModel): Promise<GeneralLocationResponse> => {
  const { id, ...restOfData } = data;
  return http.patch<GeneralLocationResponse>(
    ReplaceUrlVariable(API_PATHS.LOCATION.UPDATE_STATUS, { id: id }),
    restOfData,
  );
};
export const deleteLocation = (id: string | undefined): Promise<GeneralLocationResponse> => {
  return http.remove<GeneralLocationResponse>(API_PATHS.LOCATION.DELETE.replace(':id', String(id)));
};

export const basicLocationDetail = (id: string | undefined): Promise<LocationBasicDetailResponse> => {
  const url = API_PATHS.LOCATION.BASIC_DETAIL.replace(':id', String(id)); // ✅ Replace :id with actual ID
  return http.get<LocationBasicDetailResponse>(url);
};

export const completeLocationDetail = (id: string): Promise<LocationCompleteDetailResponse> => {
  const url = API_PATHS.LOCATION.COMPLETE_DETAIL.replace(':id', id); // ✅ Replace :id with actual ID
  return http.get<LocationCompleteDetailResponse>(url);
};

export const getLocationList = (
  params: { page: number; limit: number; orderBy?: string; orderDirection?: string },
  filters?: LocationListFiltersModel,
): Promise<LocationListResponse> => {
  return http.post<LocationListResponse>(API_PATHS.LOCATION.LOCATION_LIST, filters, { params });
};

export const getAvailableCapabilityList = (locationId: string | undefined): Promise<LocationCapabilityResponse> => {
  return http.get<LocationCapabilityResponse>(
    ReplaceUrlVariable(API_PATHS.LOCATION.AVAILABLE_CAPABILITY_LIST, { locationId }),
  );
};
export const exportLocationCapabilityList = (
  locationId: string | undefined,
  filters: AvailableCapabilitesFiltersModel,
): Promise<LocationCapabilityResponse> => {
  return http.post<LocationCapabilityResponse>(
    ReplaceUrlVariable(API_PATHS.LOCATION.EXPORT_AVAILABLE_CAPABILITY_LIST, { locationId }),
    filters,
    {
      responseType: 'blob',
    },
  );
};

export const exportLocationList = (filters?: LocationListFiltersModel): Promise<LocationListResponse> => {
  return http.post<LocationListResponse>(API_PATHS.LOCATION.EXPORT_LOCATION_LIST, filters, {
    responseType: 'blob',
  });
};

export const getAllLocations = (searchTerm: string): Promise<LocationBasicDetailResponse[] | null> => {
  return http.get<LocationBasicDetailResponse[]>(API_PATHS.USER_ACCESS.GET_LOCATION_DETAILS, {
    params: { searchTerm },
  });
};
