{"version": 3, "sources": ["../../ol/CollectionEventType.js", "../../ol/Collection.js", "../../ol/featureloader.js", "../../ol/loadingstrategy.js", "../../ol/geom/flat/center.js", "../../ol/geom/flat/contains.js", "../../ol/geom/flat/interiorpoint.js", "../../ol/geom/flat/interpolate.js", "../../ol/geom/flat/reverse.js", "../../ol/geom/flat/orient.js", "../../ol/geom/flat/simplify.js", "../../ol/geom/Circle.js", "../../ol/geom/GeometryCollection.js", "../../ol/geom/flat/area.js", "../../ol/geom/flat/closest.js", "../../ol/geom/flat/inflate.js", "../../ol/geom/LinearRing.js", "../../ol/geom/flat/segments.js", "../../ol/geom/flat/intersectsextent.js", "../../ol/geom/flat/length.js", "../../ol/geom/LineString.js", "../../ol/geom/MultiLineString.js", "../../ol/geom/MultiPoint.js", "../../ol/geom/Polygon.js", "../../ol/geom/MultiPolygon.js", "../../ol/render/Feature.js", "../../quickselect/index.js", "../../rbush/index.js", "../../ol/structs/RBush.js", "../../ol/source/Source.js", "../../ol/source/VectorEventType.js", "../../ol/source/Vector.js"], "sourcesContent": ["/**\n * @module ol/CollectionEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered when an item is added to the collection.\n   * @event module:ol/Collection.CollectionEvent#add\n   * @api\n   */\n  ADD: 'add',\n  /**\n   * Triggered when an item is removed from the collection.\n   * @event module:ol/Collection.CollectionEvent#remove\n   * @api\n   */\n  REMOVE: 'remove',\n};\n", "/**\n * @module ol/Collection\n */\nimport CollectionEventType from './CollectionEventType.js';\nimport BaseObject from './Object.js';\nimport Event from './events/Event.js';\n\n/**\n * @enum {string}\n * @private\n */\nconst Property = {\n  LENGTH: 'length',\n};\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/Collection~Collection} instances are instances of this\n * type.\n * @template T\n */\nexport class CollectionEvent extends Event {\n  /**\n   * @param {import(\"./CollectionEventType.js\").default} type Type.\n   * @param {T} element Element.\n   * @param {number} index The index of the added or removed element.\n   */\n  constructor(type, element, index) {\n    super(type);\n\n    /**\n     * The element that is added to or removed from the collection.\n     * @type {T}\n     * @api\n     */\n    this.element = element;\n\n    /**\n     * The index of the added or removed element.\n     * @type {number}\n     * @api\n     */\n    this.index = index;\n  }\n}\n\n/***\n * @template T\n * @template Return\n * @typedef {import(\"./Observable\").OnSignature<import(\"./Observable\").EventTypes, import(\"./events/Event.js\").default, Return> &\n *   import(\"./Observable\").OnSignature<import(\"./ObjectEventType\").Types|'change:length', import(\"./Object\").ObjectEvent, Return> &\n *   import(\"./Observable\").OnSignature<'add'|'remove', CollectionEvent<T>, Return> &\n *   import(\"./Observable\").CombinedOnSignature<import(\"./Observable\").EventTypes|import(\"./ObjectEventType\").Types|\n *     'change:length'|'add'|'remove',Return>} CollectionOnSignature\n */\n\n/**\n * @typedef {Object} Options\n * @property {boolean} [unique=false] Disallow the same item from being added to\n * the collection twice.\n */\n\n/**\n * @classdesc\n * An expanded version of standard JS Array, adding convenience methods for\n * manipulation. Add and remove changes to the Collection trigger a Collection\n * event. Note that this does not cover changes to the objects _within_ the\n * Collection; they trigger events on the appropriate object, not on the\n * Collection as a whole.\n *\n * @fires CollectionEvent\n *\n * @template T\n * @api\n */\nclass Collection extends BaseObject {\n  /**\n   * @param {Array<T>} [array] Array.\n   * @param {Options} [options] Collection options.\n   */\n  constructor(array, options) {\n    super();\n\n    /***\n     * @type {CollectionOnSignature<T, import(\"./events\").EventsKey>}\n     */\n    this.on;\n\n    /***\n     * @type {CollectionOnSignature<T, import(\"./events\").EventsKey>}\n     */\n    this.once;\n\n    /***\n     * @type {CollectionOnSignature<T, void>}\n     */\n    this.un;\n\n    options = options || {};\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.unique_ = !!options.unique;\n\n    /**\n     * @private\n     * @type {!Array<T>}\n     */\n    this.array_ = array ? array : [];\n\n    if (this.unique_) {\n      for (let i = 0, ii = this.array_.length; i < ii; ++i) {\n        this.assertUnique_(this.array_[i], i);\n      }\n    }\n\n    this.updateLength_();\n  }\n\n  /**\n   * Remove all elements from the collection.\n   * @api\n   */\n  clear() {\n    while (this.getLength() > 0) {\n      this.pop();\n    }\n  }\n\n  /**\n   * Add elements to the collection.  This pushes each item in the provided array\n   * to the end of the collection.\n   * @param {!Array<T>} arr Array.\n   * @return {Collection<T>} This collection.\n   * @api\n   */\n  extend(arr) {\n    for (let i = 0, ii = arr.length; i < ii; ++i) {\n      this.push(arr[i]);\n    }\n    return this;\n  }\n\n  /**\n   * Iterate over each element, calling the provided callback.\n   * @param {function(T, number, Array<T>): *} f The function to call\n   *     for every element. This function takes 3 arguments (the element, the\n   *     index and the array). The return value is ignored.\n   * @api\n   */\n  forEach(f) {\n    const array = this.array_;\n    for (let i = 0, ii = array.length; i < ii; ++i) {\n      f(array[i], i, array);\n    }\n  }\n\n  /**\n   * Get a reference to the underlying Array object. Warning: if the array\n   * is mutated, no events will be dispatched by the collection, and the\n   * collection's \"length\" property won't be in sync with the actual length\n   * of the array.\n   * @return {!Array<T>} Array.\n   * @api\n   */\n  getArray() {\n    return this.array_;\n  }\n\n  /**\n   * Get the element at the provided index.\n   * @param {number} index Index.\n   * @return {T} Element.\n   * @api\n   */\n  item(index) {\n    return this.array_[index];\n  }\n\n  /**\n   * Get the length of this collection.\n   * @return {number} The length of the array.\n   * @observable\n   * @api\n   */\n  getLength() {\n    return this.get(Property.LENGTH);\n  }\n\n  /**\n   * Insert an element at the provided index.\n   * @param {number} index Index.\n   * @param {T} elem Element.\n   * @api\n   */\n  insertAt(index, elem) {\n    if (index < 0 || index > this.getLength()) {\n      throw new Error('Index out of bounds: ' + index);\n    }\n    if (this.unique_) {\n      this.assertUnique_(elem);\n    }\n    this.array_.splice(index, 0, elem);\n    this.updateLength_();\n    this.dispatchEvent(\n      new CollectionEvent(CollectionEventType.ADD, elem, index),\n    );\n  }\n\n  /**\n   * Remove the last element of the collection and return it.\n   * Return `undefined` if the collection is empty.\n   * @return {T|undefined} Element.\n   * @api\n   */\n  pop() {\n    return this.removeAt(this.getLength() - 1);\n  }\n\n  /**\n   * Insert the provided element at the end of the collection.\n   * @param {T} elem Element.\n   * @return {number} New length of the collection.\n   * @api\n   */\n  push(elem) {\n    if (this.unique_) {\n      this.assertUnique_(elem);\n    }\n    const n = this.getLength();\n    this.insertAt(n, elem);\n    return this.getLength();\n  }\n\n  /**\n   * Remove the first occurrence of an element from the collection.\n   * @param {T} elem Element.\n   * @return {T|undefined} The removed element or undefined if none found.\n   * @api\n   */\n  remove(elem) {\n    const arr = this.array_;\n    for (let i = 0, ii = arr.length; i < ii; ++i) {\n      if (arr[i] === elem) {\n        return this.removeAt(i);\n      }\n    }\n    return undefined;\n  }\n\n  /**\n   * Remove the element at the provided index and return it.\n   * Return `undefined` if the collection does not contain this index.\n   * @param {number} index Index.\n   * @return {T|undefined} Value.\n   * @api\n   */\n  removeAt(index) {\n    if (index < 0 || index >= this.getLength()) {\n      return undefined;\n    }\n    const prev = this.array_[index];\n    this.array_.splice(index, 1);\n    this.updateLength_();\n    this.dispatchEvent(\n      /** @type {CollectionEvent<T>} */ (\n        new CollectionEvent(CollectionEventType.REMOVE, prev, index)\n      ),\n    );\n    return prev;\n  }\n\n  /**\n   * Set the element at the provided index.\n   * @param {number} index Index.\n   * @param {T} elem Element.\n   * @api\n   */\n  setAt(index, elem) {\n    const n = this.getLength();\n    if (index >= n) {\n      this.insertAt(index, elem);\n      return;\n    }\n    if (index < 0) {\n      throw new Error('Index out of bounds: ' + index);\n    }\n    if (this.unique_) {\n      this.assertUnique_(elem, index);\n    }\n    const prev = this.array_[index];\n    this.array_[index] = elem;\n    this.dispatchEvent(\n      /** @type {CollectionEvent<T>} */ (\n        new CollectionEvent(CollectionEventType.REMOVE, prev, index)\n      ),\n    );\n    this.dispatchEvent(\n      /** @type {CollectionEvent<T>} */ (\n        new CollectionEvent(CollectionEventType.ADD, elem, index)\n      ),\n    );\n  }\n\n  /**\n   * @private\n   */\n  updateLength_() {\n    this.set(Property.LENGTH, this.array_.length);\n  }\n\n  /**\n   * @private\n   * @param {T} elem Element.\n   * @param {number} [except] Optional index to ignore.\n   */\n  assertUnique_(elem, except) {\n    for (let i = 0, ii = this.array_.length; i < ii; ++i) {\n      if (this.array_[i] === elem && i !== except) {\n        throw new Error('Duplicate item added to a unique collection');\n      }\n    }\n  }\n}\n\nexport default Collection;\n", "/**\n * @module ol/featureloader\n */\n\n/**\n *\n * @type {boolean}\n * @private\n */\nlet withCredentials = false;\n\n/**\n * {@link module:ol/source/Vector~VectorSource} sources use a function of this type to\n * load features.\n *\n * This function takes up to 5 arguments. These are an {@link module:ol/extent~Extent} representing\n * the area to be loaded, a `{number}` representing the resolution (map units per pixel), a\n * {@link module:ol/proj/Projection~Projection} for the projection, an optional success callback that should get\n * the loaded features passed as an argument and an optional failure callback with no arguments. If\n * the callbacks are not used, the corresponding vector source will not fire `'featuresloadend'` and\n * `'featuresloaderror'` events. `this` within the function is bound to the\n * {@link module:ol/source/Vector~VectorSource} it's called from.\n *\n * The function is responsible for loading the features and adding them to the\n * source.\n *\n * @template {import(\"./Feature.js\").FeatureLike} [FeatureType=import(\"./Feature.js\").FeatureLike]\n * @typedef {(\n *           extent: import(\"./extent.js\").Extent,\n *           resolution: number,\n *           projection: import(\"./proj/Projection.js\").default,\n *           success?: (features: Array<FeatureType>) => void,\n *           failure?: () => void) => void} FeatureLoader\n * @api\n */\n\n/**\n * {@link module:ol/source/Vector~VectorSource} sources use a function of this type to\n * get the url to load features from.\n *\n * This function takes an {@link module:ol/extent~Extent} representing the area\n * to be loaded, a `{number}` representing the resolution (map units per pixel)\n * and an {@link module:ol/proj/Projection~Projection} for the projection  as\n * arguments and returns a `{string}` representing the URL.\n * @typedef {function(import(\"./extent.js\").Extent, number, import(\"./proj/Projection.js\").default): string} FeatureUrlFunction\n * @api\n */\n\n/**\n * @template {import(\"./Feature.js\").FeatureLike} [FeatureType=import(\"./Feature.js\").default]\n * @param {string|FeatureUrlFunction} url Feature URL service.\n * @param {import(\"./format/Feature.js\").default<FeatureType>} format Feature format.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @param {function(Array<FeatureType>, import(\"./proj/Projection.js\").default): void} success Success\n *      Function called with the loaded features and optionally with the data projection.\n * @param {function(): void} failure Failure\n *      Function called when loading failed.\n */\nexport function loadFeaturesXhr(\n  url,\n  format,\n  extent,\n  resolution,\n  projection,\n  success,\n  failure,\n) {\n  const xhr = new XMLHttpRequest();\n  xhr.open(\n    'GET',\n    typeof url === 'function' ? url(extent, resolution, projection) : url,\n    true,\n  );\n  if (format.getType() == 'arraybuffer') {\n    xhr.responseType = 'arraybuffer';\n  }\n  xhr.withCredentials = withCredentials;\n  /**\n   * @param {Event} event Event.\n   * @private\n   */\n  xhr.onload = function (event) {\n    // status will be 0 for file:// urls\n    if (!xhr.status || (xhr.status >= 200 && xhr.status < 300)) {\n      const type = format.getType();\n      try {\n        /** @type {Document|Node|Object|string|undefined} */\n        let source;\n        if (type == 'text' || type == 'json') {\n          source = xhr.responseText;\n        } else if (type == 'xml') {\n          source = xhr.responseXML || xhr.responseText;\n        } else if (type == 'arraybuffer') {\n          source = /** @type {ArrayBuffer} */ (xhr.response);\n        }\n        if (source) {\n          success(\n            /** @type {Array<FeatureType>} */\n            (\n              format.readFeatures(source, {\n                extent: extent,\n                featureProjection: projection,\n              })\n            ),\n            format.readProjection(source),\n          );\n        } else {\n          failure();\n        }\n      } catch {\n        failure();\n      }\n    } else {\n      failure();\n    }\n  };\n  /**\n   * @private\n   */\n  xhr.onerror = failure;\n  xhr.send();\n}\n\n/**\n * Create an XHR feature loader for a `url` and `format`. The feature loader\n * loads features (with XHR), parses the features, and adds them to the\n * vector source.\n *\n * @template {import(\"./Feature.js\").FeatureLike} [FeatureType=import(\"./Feature.js\").default]\n * @param {string|FeatureUrlFunction} url Feature URL service.\n * @param {import(\"./format/Feature.js\").default<FeatureType>} format Feature format.\n * @return {FeatureLoader<FeatureType>} The feature loader.\n * @api\n */\nexport function xhr(url, format) {\n  /**\n   * @param {import(\"./extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {import(\"./proj/Projection.js\").default} projection Projection.\n   * @param {function(Array<FeatureType>): void} [success] Success\n   *      Function called when loading succeeded.\n   * @param {function(): void} [failure] Failure\n   *      Function called when loading failed.\n   * @this {import(\"./source/Vector.js\").default<FeatureType>}\n   */\n  return function (extent, resolution, projection, success, failure) {\n    loadFeaturesXhr(\n      url,\n      format,\n      extent,\n      resolution,\n      projection,\n      /**\n       * @param {Array<FeatureType>} features The loaded features.\n       * @param {import(\"./proj/Projection.js\").default} dataProjection Data\n       * projection.\n       */\n      (features, dataProjection) => {\n        this.addFeatures(features);\n        if (success !== undefined) {\n          success(features);\n        }\n      },\n      () => {\n        this.changed();\n        if (failure !== undefined) {\n          failure();\n        }\n      },\n    );\n  };\n}\n\n/**\n * Setter for the withCredentials configuration for the XHR.\n *\n * @param {boolean} xhrWithCredentials The value of withCredentials to set.\n * Compare https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/\n * @api\n */\nexport function setWithCredentials(xhrWithCredentials) {\n  withCredentials = xhrWithCredentials;\n}\n", "/**\n * @module ol/loadingstrategy\n */\n\nimport {fromUserExtent, fromUserResolution, toUserExtent} from './proj.js';\n\n/**\n * Strategy function for loading all features with a single request.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @return {Array<import(\"./extent.js\").Extent>} Extents.\n * @api\n */\nexport function all(extent, resolution) {\n  return [[-Infinity, -Infinity, Infinity, Infinity]];\n}\n\n/**\n * Strategy function for loading features based on the view's extent and\n * resolution.\n * @param {import(\"./extent.js\").Extent} extent Extent.\n * @param {number} resolution Resolution.\n * @return {Array<import(\"./extent.js\").Extent>} Extents.\n * @api\n */\nexport function bbox(extent, resolution) {\n  return [extent];\n}\n\n/**\n * Creates a strategy function for loading features based on a tile grid.\n * @param {import(\"./tilegrid/TileGrid.js\").default} tileGrid Tile grid.\n * @return {function(import(\"./extent.js\").Extent, number, import(\"./proj.js\").Projection): Array<import(\"./extent.js\").Extent>} Loading strategy.\n * @api\n */\nexport function tile(tileGrid) {\n  return (\n    /**\n     * @param {import(\"./extent.js\").Extent} extent Extent.\n     * @param {number} resolution Resolution.\n     * @param {import(\"./proj.js\").Projection} projection Projection.\n     * @return {Array<import(\"./extent.js\").Extent>} Extents.\n     */\n    function (extent, resolution, projection) {\n      const z = tileGrid.getZForResolution(\n        fromUserResolution(resolution, projection),\n      );\n      const tileRange = tileGrid.getTileRangeForExtentAndZ(\n        fromUserExtent(extent, projection),\n        z,\n      );\n      /** @type {Array<import(\"./extent.js\").Extent>} */\n      const extents = [];\n      /** @type {import(\"./tilecoord.js\").TileCoord} */\n      const tileCoord = [z, 0, 0];\n      for (\n        tileCoord[1] = tileRange.minX;\n        tileCoord[1] <= tileRange.maxX;\n        ++tileCoord[1]\n      ) {\n        for (\n          tileCoord[2] = tileRange.minY;\n          tileCoord[2] <= tileRange.maxY;\n          ++tileCoord[2]\n        ) {\n          extents.push(\n            toUserExtent(tileGrid.getTileCoordExtent(tileCoord), projection),\n          );\n        }\n      }\n      return extents;\n    }\n  );\n}\n", "/**\n * @module ol/geom/flat/center\n */\nimport {createEmpty, createOrUpdateFromFlatCoordinates} from '../../extent.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @return {Array<number>} Flat centers.\n */\nexport function linearRingss(flatCoordinates, offset, endss, stride) {\n  const flatCenters = [];\n  let extent = createEmpty();\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    extent = createOrUpdateFromFlatCoordinates(\n      flatCoordinates,\n      offset,\n      ends[0],\n      stride,\n    );\n    flatCenters.push((extent[0] + extent[2]) / 2, (extent[1] + extent[3]) / 2);\n    offset = ends[ends.length - 1];\n  }\n  return flatCenters;\n}\n", "/**\n * @module ol/geom/flat/contains\n */\nimport {forEach<PERSON>orner} from '../../extent.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} Contains extent.\n */\nexport function linearRingContainsExtent(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  extent,\n) {\n  const outside = forEachCorner(\n    extent,\n    /**\n     * @param {import(\"../../coordinate.js\").Coordinate} coordinate Coordinate.\n     * @return {boolean} Contains (x, y).\n     */\n    function (coordinate) {\n      return !linearRingContainsXY(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        coordinate[0],\n        coordinate[1],\n      );\n    },\n  );\n  return !outside;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {boolean} Contains (x, y).\n */\nexport function linearRingContainsXY(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  x,\n  y,\n) {\n  // https://geomalgorithms.com/a03-_inclusion.html\n  // Copyright 2000 softSurfer, 2012 Dan Sunday\n  // This code may be freely used and modified for any purpose\n  // providing that this copyright notice is included with it.\n  // SoftSurfer makes no warranty for this code, and cannot be held\n  // liable for any real or imagined damage resulting from its use.\n  // Users of this code must verify correctness for their application.\n  let wn = 0;\n  let x1 = flatCoordinates[end - stride];\n  let y1 = flatCoordinates[end - stride + 1];\n  for (; offset < end; offset += stride) {\n    const x2 = flatCoordinates[offset];\n    const y2 = flatCoordinates[offset + 1];\n    if (y1 <= y) {\n      if (y2 > y && (x2 - x1) * (y - y1) - (x - x1) * (y2 - y1) > 0) {\n        wn++;\n      }\n    } else if (y2 <= y && (x2 - x1) * (y - y1) - (x - x1) * (y2 - y1) < 0) {\n      wn--;\n    }\n    x1 = x2;\n    y1 = y2;\n  }\n  return wn !== 0;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {boolean} Contains (x, y).\n */\nexport function linearRingsContainsXY(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  x,\n  y,\n) {\n  if (ends.length === 0) {\n    return false;\n  }\n  if (!linearRingContainsXY(flatCoordinates, offset, ends[0], stride, x, y)) {\n    return false;\n  }\n  for (let i = 1, ii = ends.length; i < ii; ++i) {\n    if (\n      linearRingContainsXY(flatCoordinates, ends[i - 1], ends[i], stride, x, y)\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {boolean} Contains (x, y).\n */\nexport function linearRingssContainsXY(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  x,\n  y,\n) {\n  if (endss.length === 0) {\n    return false;\n  }\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    if (linearRingsContainsXY(flatCoordinates, offset, ends, stride, x, y)) {\n      return true;\n    }\n    offset = ends[ends.length - 1];\n  }\n  return false;\n}\n", "/**\n * @module ol/geom/flat/interiorpoint\n */\nimport {ascending} from '../../array.js';\nimport {linearRingsContainsXY} from './contains.js';\n\n/**\n * Calculates a point that is likely to lie in the interior of the linear rings.\n * Inspired by JTS's com.vividsolutions.jts.geom.Geometry#getInteriorPoint.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {Array<number>} flatCenters Flat centers.\n * @param {number} flatCentersOffset Flat center offset.\n * @param {Array<number>} [dest] Destination.\n * @return {Array<number>} Destination point as XYM coordinate, where M is the\n * length of the horizontal intersection that the point belongs to.\n */\nexport function getInteriorPointOfArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  flatCenters,\n  flatCentersOffset,\n  dest,\n) {\n  let i, ii, x, x1, x2, y1, y2;\n  const y = flatCenters[flatCentersOffset + 1];\n  /** @type {Array<number>} */\n  const intersections = [];\n  // Calculate intersections with the horizontal line\n  for (let r = 0, rr = ends.length; r < rr; ++r) {\n    const end = ends[r];\n    x1 = flatCoordinates[end - stride];\n    y1 = flatCoordinates[end - stride + 1];\n    for (i = offset; i < end; i += stride) {\n      x2 = flatCoordinates[i];\n      y2 = flatCoordinates[i + 1];\n      if ((y <= y1 && y2 <= y) || (y1 <= y && y <= y2)) {\n        x = ((y - y1) / (y2 - y1)) * (x2 - x1) + x1;\n        intersections.push(x);\n      }\n      x1 = x2;\n      y1 = y2;\n    }\n  }\n  // Find the longest segment of the horizontal line that has its center point\n  // inside the linear ring.\n  let pointX = NaN;\n  let maxSegmentLength = -Infinity;\n  intersections.sort(ascending);\n  x1 = intersections[0];\n  for (i = 1, ii = intersections.length; i < ii; ++i) {\n    x2 = intersections[i];\n    const segmentLength = Math.abs(x2 - x1);\n    if (segmentLength > maxSegmentLength) {\n      x = (x1 + x2) / 2;\n      if (linearRingsContainsXY(flatCoordinates, offset, ends, stride, x, y)) {\n        pointX = x;\n        maxSegmentLength = segmentLength;\n      }\n    }\n    x1 = x2;\n  }\n  if (isNaN(pointX)) {\n    // There is no horizontal line that has its center point inside the linear\n    // ring.  Use the center of the the linear ring's extent.\n    pointX = flatCenters[flatCentersOffset];\n  }\n  if (dest) {\n    dest.push(pointX, y, maxSegmentLength);\n    return dest;\n  }\n  return [pointX, y, maxSegmentLength];\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {Array<number>} flatCenters Flat centers.\n * @return {Array<number>} Interior points as XYM coordinates, where M is the\n * length of the horizontal intersection that the point belongs to.\n */\nexport function getInteriorPointsOfMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  flatCenters,\n) {\n  /** @type {Array<number>} */\n  let interiorPoints = [];\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    interiorPoints = getInteriorPointOfArray(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      flatCenters,\n      2 * i,\n      interiorPoints,\n    );\n    offset = ends[ends.length - 1];\n  }\n  return interiorPoints;\n}\n", "/**\n * @module ol/geom/flat/interpolate\n */\nimport {binarySearch} from '../../array.js';\nimport {lerp} from '../../math.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} fraction Fraction.\n * @param {Array<number>} [dest] Destination.\n * @param {number} [dimension] Destination dimension (default is `2`)\n * @return {Array<number>} Destination.\n */\nexport function interpolatePoint(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  fraction,\n  dest,\n  dimension,\n) {\n  let o, t;\n  const n = (end - offset) / stride;\n  if (n === 1) {\n    o = offset;\n  } else if (n === 2) {\n    o = offset;\n    t = fraction;\n  } else if (n !== 0) {\n    let x1 = flatCoordinates[offset];\n    let y1 = flatCoordinates[offset + 1];\n    let length = 0;\n    const cumulativeLengths = [0];\n    for (let i = offset + stride; i < end; i += stride) {\n      const x2 = flatCoordinates[i];\n      const y2 = flatCoordinates[i + 1];\n      length += Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n      cumulativeLengths.push(length);\n      x1 = x2;\n      y1 = y2;\n    }\n    const target = fraction * length;\n    const index = binarySearch(cumulativeLengths, target);\n    if (index < 0) {\n      t =\n        (target - cumulativeLengths[-index - 2]) /\n        (cumulativeLengths[-index - 1] - cumulativeLengths[-index - 2]);\n      o = offset + (-index - 2) * stride;\n    } else {\n      o = offset + index * stride;\n    }\n  }\n  dimension = dimension > 1 ? dimension : 2;\n  dest = dest ? dest : new Array(dimension);\n  for (let i = 0; i < dimension; ++i) {\n    dest[i] =\n      o === undefined\n        ? NaN\n        : t === undefined\n          ? flatCoordinates[o + i]\n          : lerp(flatCoordinates[o + i], flatCoordinates[o + stride + i], t);\n  }\n  return dest;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} m M.\n * @param {boolean} extrapolate Extrapolate.\n * @return {import(\"../../coordinate.js\").Coordinate|null} Coordinate.\n */\nexport function lineStringCoordinateAtM(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  m,\n  extrapolate,\n) {\n  if (end == offset) {\n    return null;\n  }\n  let coordinate;\n  if (m < flatCoordinates[offset + stride - 1]) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(offset, offset + stride);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  if (flatCoordinates[end - 1] < m) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(end - stride, end);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  // FIXME use O(1) search\n  if (m == flatCoordinates[offset + stride - 1]) {\n    return flatCoordinates.slice(offset, offset + stride);\n  }\n  let lo = offset / stride;\n  let hi = end / stride;\n  while (lo < hi) {\n    const mid = (lo + hi) >> 1;\n    if (m < flatCoordinates[(mid + 1) * stride - 1]) {\n      hi = mid;\n    } else {\n      lo = mid + 1;\n    }\n  }\n  const m0 = flatCoordinates[lo * stride - 1];\n  if (m == m0) {\n    return flatCoordinates.slice((lo - 1) * stride, (lo - 1) * stride + stride);\n  }\n  const m1 = flatCoordinates[(lo + 1) * stride - 1];\n  const t = (m - m0) / (m1 - m0);\n  coordinate = [];\n  for (let i = 0; i < stride - 1; ++i) {\n    coordinate.push(\n      lerp(\n        flatCoordinates[(lo - 1) * stride + i],\n        flatCoordinates[lo * stride + i],\n        t,\n      ),\n    );\n  }\n  coordinate.push(m);\n  return coordinate;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} m M.\n * @param {boolean} extrapolate Extrapolate.\n * @param {boolean} interpolate Interpolate.\n * @return {import(\"../../coordinate.js\").Coordinate|null} Coordinate.\n */\nexport function lineStringsCoordinateAtM(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  m,\n  extrapolate,\n  interpolate,\n) {\n  if (interpolate) {\n    return lineStringCoordinateAtM(\n      flatCoordinates,\n      offset,\n      ends[ends.length - 1],\n      stride,\n      m,\n      extrapolate,\n    );\n  }\n  let coordinate;\n  if (m < flatCoordinates[stride - 1]) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(0, stride);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  if (flatCoordinates[flatCoordinates.length - 1] < m) {\n    if (extrapolate) {\n      coordinate = flatCoordinates.slice(flatCoordinates.length - stride);\n      coordinate[stride - 1] = m;\n      return coordinate;\n    }\n    return null;\n  }\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    if (offset == end) {\n      continue;\n    }\n    if (m < flatCoordinates[offset + stride - 1]) {\n      return null;\n    }\n    if (m <= flatCoordinates[end - 1]) {\n      return lineStringCoordinateAtM(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        m,\n        false,\n      );\n    }\n    offset = end;\n  }\n  return null;\n}\n", "/**\n * @module ol/geom/flat/reverse\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n */\nexport function coordinates(flatCoordinates, offset, end, stride) {\n  while (offset < end - stride) {\n    for (let i = 0; i < stride; ++i) {\n      const tmp = flatCoordinates[offset + i];\n      flatCoordinates[offset + i] = flatCoordinates[end - stride + i];\n      flatCoordinates[end - stride + i] = tmp;\n    }\n    offset += stride;\n    end -= stride;\n  }\n}\n", "/**\n * @module ol/geom/flat/orient\n */\nimport {coordinates as reverseCoordinates} from './reverse.js';\n\n/**\n * Is the linear ring oriented clockwise in a coordinate system with a bottom-left\n * coordinate origin? For a coordinate system with a top-left coordinate origin,\n * the ring's orientation is clockwise when this function returns false.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {boolean|undefined} Is clockwise.\n */\nexport function linearRingIsClockwise(flatCoordinates, offset, end, stride) {\n  // https://stackoverflow.com/q/1165647/clockwise-method#1165943\n  // https://github.com/OSGeo/gdal/blob/master/gdal/ogr/ogrlinearring.cpp\n  let edge = 0;\n  let x1 = flatCoordinates[end - stride];\n  let y1 = flatCoordinates[end - stride + 1];\n  for (; offset < end; offset += stride) {\n    const x2 = flatCoordinates[offset];\n    const y2 = flatCoordinates[offset + 1];\n    edge += (x2 - x1) * (y2 + y1);\n    x1 = x2;\n    y1 = y2;\n  }\n  return edge === 0 ? undefined : edge > 0;\n}\n\n/**\n * Determines if linear rings are oriented.  By default, left-hand orientation\n * is tested (first ring must be clockwise, remaining rings counter-clockwise).\n * To test for right-hand orientation, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Array of end indexes.\n * @param {number} stride Stride.\n * @param {boolean} [right] Test for right-hand orientation\n *     (counter-clockwise exterior ring and clockwise interior rings).\n * @return {boolean} Rings are correctly oriented.\n */\nexport function linearRingsAreOriented(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  right,\n) {\n  right = right !== undefined ? right : false;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    const isClockwise = linearRingIsClockwise(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n    );\n    if (i === 0) {\n      if ((right && isClockwise) || (!right && !isClockwise)) {\n        return false;\n      }\n    } else {\n      if ((right && !isClockwise) || (!right && isClockwise)) {\n        return false;\n      }\n    }\n    offset = end;\n  }\n  return true;\n}\n\n/**\n * Determines if linear rings are oriented.  By default, left-hand orientation\n * is tested (first ring must be clockwise, remaining rings counter-clockwise).\n * To test for right-hand orientation, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Array of array of end indexes.\n * @param {number} stride Stride.\n * @param {boolean} [right] Test for right-hand orientation\n *     (counter-clockwise exterior ring and clockwise interior rings).\n * @return {boolean} Rings are correctly oriented.\n */\nexport function linearRingssAreOriented(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  right,\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    if (!linearRingsAreOriented(flatCoordinates, offset, ends, stride, right)) {\n      return false;\n    }\n    if (ends.length) {\n      offset = ends[ends.length - 1];\n    }\n  }\n  return true;\n}\n\n/**\n * Orient coordinates in a flat array of linear rings.  By default, rings\n * are oriented following the left-hand rule (clockwise for exterior and\n * counter-clockwise for interior rings).  To orient according to the\n * right-hand rule, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {boolean} [right] Follow the right-hand rule for orientation.\n * @return {number} End.\n */\nexport function orientLinearRings(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  right,\n) {\n  right = right !== undefined ? right : false;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    const isClockwise = linearRingIsClockwise(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n    );\n    const reverse =\n      i === 0\n        ? (right && isClockwise) || (!right && !isClockwise)\n        : (right && !isClockwise) || (!right && isClockwise);\n    if (reverse) {\n      reverseCoordinates(flatCoordinates, offset, end, stride);\n    }\n    offset = end;\n  }\n  return offset;\n}\n\n/**\n * Orient coordinates in a flat array of linear rings.  By default, rings\n * are oriented following the left-hand rule (clockwise for exterior and\n * counter-clockwise for interior rings).  To orient according to the\n * right-hand rule, use the `right` argument.\n *\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Array of array of end indexes.\n * @param {number} stride Stride.\n * @param {boolean} [right] Follow the right-hand rule for orientation.\n * @return {number} End.\n */\nexport function orientLinearRingsArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  right,\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    offset = orientLinearRings(\n      flatCoordinates,\n      offset,\n      endss[i],\n      stride,\n      right,\n    );\n  }\n  return offset;\n}\n\n/**\n * Return a two-dimensional endss\n * @param {Array<number>} flatCoordinates Flat coordinates\n * @param {Array<number>} ends Linear ring end indexes\n * @return {Array<Array<number>>} Two dimensional endss array that can\n * be used to construct a MultiPolygon\n */\nexport function inflateEnds(flatCoordinates, ends) {\n  const endss = [];\n  let offset = 0;\n  let prevEndIndex = 0;\n  let startOrientation;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    // classifies an array of rings into polygons with outer rings and holes\n    const orientation = linearRingIsClockwise(flatCoordinates, offset, end, 2);\n    if (startOrientation === undefined) {\n      startOrientation = orientation;\n    }\n    if (orientation === startOrientation) {\n      endss.push(ends.slice(prevEndIndex, i + 1));\n    } else {\n      if (endss.length === 0) {\n        continue;\n      }\n      endss[endss.length - 1].push(ends[prevEndIndex]);\n    }\n    prevEndIndex = i + 1;\n    offset = end;\n  }\n  return endss;\n}\n", "/**\n * @module ol/geom/flat/simplify\n */\n// Based on simplify-js https://github.com/mourner/simplify-js\n// Copyright (c) 2012, <PERSON>in\n// All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are met:\n//\n//    1. Redistributions of source code must retain the above copyright notice,\n//       this list of conditions and the following disclaimer.\n//\n//    2. Redistributions in binary form must reproduce the above copyright\n//       notice, this list of conditions and the following disclaimer in the\n//       documentation and/or other materials provided with the distribution.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n// POSSIBILITY OF SUCH DAMAGE.\n\nimport {squaredDistance, squaredSegmentDistance} from '../../math.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {boolean} highQuality Highest quality.\n * @param {Array<number>} [simplifiedFlatCoordinates] Simplified flat\n *     coordinates.\n * @return {Array<number>} Simplified line string.\n */\nexport function simplifyLineString(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  squaredTolerance,\n  highQuality,\n  simplifiedFlatCoordinates,\n) {\n  simplifiedFlatCoordinates =\n    simplifiedFlatCoordinates !== undefined ? simplifiedFlatCoordinates : [];\n  if (!highQuality) {\n    end = radialDistance(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0,\n    );\n    flatCoordinates = simplifiedFlatCoordinates;\n    offset = 0;\n    stride = 2;\n  }\n  simplifiedFlatCoordinates.length = douglasPeucker(\n    flatCoordinates,\n    offset,\n    end,\n    stride,\n    squaredTolerance,\n    simplifiedFlatCoordinates,\n    0,\n  );\n  return simplifiedFlatCoordinates;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @return {number} Simplified offset.\n */\nexport function douglasPeucker(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n) {\n  const n = (end - offset) / stride;\n  if (n < 3) {\n    for (; offset < end; offset += stride) {\n      simplifiedFlatCoordinates[simplifiedOffset++] = flatCoordinates[offset];\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + 1];\n    }\n    return simplifiedOffset;\n  }\n  /** @type {Array<number>} */\n  const markers = new Array(n);\n  markers[0] = 1;\n  markers[n - 1] = 1;\n  /** @type {Array<number>} */\n  const stack = [offset, end - stride];\n  let index = 0;\n  while (stack.length > 0) {\n    const last = stack.pop();\n    const first = stack.pop();\n    let maxSquaredDistance = 0;\n    const x1 = flatCoordinates[first];\n    const y1 = flatCoordinates[first + 1];\n    const x2 = flatCoordinates[last];\n    const y2 = flatCoordinates[last + 1];\n    for (let i = first + stride; i < last; i += stride) {\n      const x = flatCoordinates[i];\n      const y = flatCoordinates[i + 1];\n      const squaredDistance = squaredSegmentDistance(x, y, x1, y1, x2, y2);\n      if (squaredDistance > maxSquaredDistance) {\n        index = i;\n        maxSquaredDistance = squaredDistance;\n      }\n    }\n    if (maxSquaredDistance > squaredTolerance) {\n      markers[(index - offset) / stride] = 1;\n      if (first + stride < index) {\n        stack.push(first, index);\n      }\n      if (index + stride < last) {\n        stack.push(index, last);\n      }\n    }\n  }\n  for (let i = 0; i < n; ++i) {\n    if (markers[i]) {\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + i * stride];\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + i * stride + 1];\n    }\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<number>} simplifiedEnds Simplified ends.\n * @return {number} Simplified offset.\n */\nexport function douglasPeuckerArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEnds,\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    simplifiedOffset = douglasPeucker(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset,\n    );\n    simplifiedEnds.push(simplifiedOffset);\n    offset = end;\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<Array<number>>} simplifiedEndss Simplified endss.\n * @return {number} Simplified offset.\n */\nexport function douglasPeuckerMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEndss,\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    /** @type {Array<number>} */\n    const simplifiedEnds = [];\n    simplifiedOffset = douglasPeuckerArray(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset,\n      simplifiedEnds,\n    );\n    simplifiedEndss.push(simplifiedEnds);\n    offset = ends[ends.length - 1];\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} squaredTolerance Squared tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @return {number} Simplified offset.\n */\nexport function radialDistance(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  squaredTolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n) {\n  if (end <= offset + stride) {\n    // zero or one point, no simplification possible, so copy and return\n    for (; offset < end; offset += stride) {\n      simplifiedFlatCoordinates[simplifiedOffset++] = flatCoordinates[offset];\n      simplifiedFlatCoordinates[simplifiedOffset++] =\n        flatCoordinates[offset + 1];\n    }\n    return simplifiedOffset;\n  }\n  let x1 = flatCoordinates[offset];\n  let y1 = flatCoordinates[offset + 1];\n  // copy first point\n  simplifiedFlatCoordinates[simplifiedOffset++] = x1;\n  simplifiedFlatCoordinates[simplifiedOffset++] = y1;\n  let x2 = x1;\n  let y2 = y1;\n  for (offset += stride; offset < end; offset += stride) {\n    x2 = flatCoordinates[offset];\n    y2 = flatCoordinates[offset + 1];\n    if (squaredDistance(x1, y1, x2, y2) > squaredTolerance) {\n      // copy point at offset\n      simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n      simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n      x1 = x2;\n      y1 = y2;\n    }\n  }\n  if (x2 != x1 || y2 != y1) {\n    // copy last point\n    simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n    simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {number} value Value.\n * @param {number} tolerance Tolerance.\n * @return {number} Rounded value.\n */\nexport function snap(value, tolerance) {\n  return tolerance * Math.round(value / tolerance);\n}\n\n/**\n * Simplifies a line string using an algorithm designed by Tim Schaub.\n * Coordinates are snapped to the nearest value in a virtual grid and\n * consecutive duplicate coordinates are discarded.  This effectively preserves\n * topology as the simplification of any subsection of a line string is\n * independent of the rest of the line string.  This means that, for examples,\n * the common edge between two polygons will be simplified to the same line\n * string independently in both polygons.  This implementation uses a single\n * pass over the coordinates and eliminates intermediate collinear points.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} tolerance Tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @return {number} Simplified offset.\n */\nexport function quantize(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  tolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n) {\n  // do nothing if the line is empty\n  if (offset == end) {\n    return simplifiedOffset;\n  }\n  // snap the first coordinate (P1)\n  let x1 = snap(flatCoordinates[offset], tolerance);\n  let y1 = snap(flatCoordinates[offset + 1], tolerance);\n  offset += stride;\n  // add the first coordinate to the output\n  simplifiedFlatCoordinates[simplifiedOffset++] = x1;\n  simplifiedFlatCoordinates[simplifiedOffset++] = y1;\n  // find the next coordinate that does not snap to the same value as the first\n  // coordinate (P2)\n  let x2, y2;\n  do {\n    x2 = snap(flatCoordinates[offset], tolerance);\n    y2 = snap(flatCoordinates[offset + 1], tolerance);\n    offset += stride;\n    if (offset == end) {\n      // all coordinates snap to the same value, the line collapses to a point\n      // push the last snapped value anyway to ensure that the output contains\n      // at least two points\n      // FIXME should we really return at least two points anyway?\n      simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n      simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n      return simplifiedOffset;\n    }\n  } while (x2 == x1 && y2 == y1);\n  while (offset < end) {\n    // snap the next coordinate (P3)\n    const x3 = snap(flatCoordinates[offset], tolerance);\n    const y3 = snap(flatCoordinates[offset + 1], tolerance);\n    offset += stride;\n    // skip P3 if it is equal to P2\n    if (x3 == x2 && y3 == y2) {\n      continue;\n    }\n    // calculate the delta between P1 and P2\n    const dx1 = x2 - x1;\n    const dy1 = y2 - y1;\n    // calculate the delta between P3 and P1\n    const dx2 = x3 - x1;\n    const dy2 = y3 - y1;\n    // if P1, P2, and P3 are colinear and P3 is further from P1 than P2 is from\n    // P1 in the same direction then P2 is on the straight line between P1 and\n    // P3\n    if (\n      dx1 * dy2 == dy1 * dx2 &&\n      ((dx1 < 0 && dx2 < dx1) || dx1 == dx2 || (dx1 > 0 && dx2 > dx1)) &&\n      ((dy1 < 0 && dy2 < dy1) || dy1 == dy2 || (dy1 > 0 && dy2 > dy1))\n    ) {\n      // discard P2 and set P2 = P3\n      x2 = x3;\n      y2 = y3;\n      continue;\n    }\n    // either P1, P2, and P3 are not colinear, or they are colinear but P3 is\n    // between P3 and P1 or on the opposite half of the line to P2.  add P2,\n    // and continue with P1 = P2 and P2 = P3\n    simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n    simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n    x1 = x2;\n    y1 = y2;\n    x2 = x3;\n    y2 = y3;\n  }\n  // add the last point (P2)\n  simplifiedFlatCoordinates[simplifiedOffset++] = x2;\n  simplifiedFlatCoordinates[simplifiedOffset++] = y2;\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} tolerance Tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<number>} simplifiedEnds Simplified ends.\n * @return {number} Simplified offset.\n */\nexport function quantizeArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  tolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEnds,\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    simplifiedOffset = quantize(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      tolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset,\n    );\n    simplifiedEnds.push(simplifiedOffset);\n    offset = end;\n  }\n  return simplifiedOffset;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} tolerance Tolerance.\n * @param {Array<number>} simplifiedFlatCoordinates Simplified flat\n *     coordinates.\n * @param {number} simplifiedOffset Simplified offset.\n * @param {Array<Array<number>>} simplifiedEndss Simplified endss.\n * @return {number} Simplified offset.\n */\nexport function quantizeMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  tolerance,\n  simplifiedFlatCoordinates,\n  simplifiedOffset,\n  simplifiedEndss,\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    /** @type {Array<number>} */\n    const simplifiedEnds = [];\n    simplifiedOffset = quantizeArray(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      tolerance,\n      simplifiedFlatCoordinates,\n      simplifiedOffset,\n      simplifiedEnds,\n    );\n    simplifiedEndss.push(simplifiedEnds);\n    offset = ends[ends.length - 1];\n  }\n  return simplifiedOffset;\n}\n", "/**\n * @module ol/geom/Circle\n */\nimport {createOrUpdate, for<PERSON><PERSON><PERSON><PERSON><PERSON>, intersects} from '../extent.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {deflateCoordinate} from './flat/deflate.js';\nimport {rotate} from './flat/transform.js';\n\n/**\n * @classdesc\n * Circle geometry.\n *\n * @api\n */\nclass Circle extends SimpleGeometry {\n  /**\n   * @param {!import(\"../coordinate.js\").Coordinate} center Center.\n   *     For internal use, flat coordinates in combination with `layout` and no\n   *     `radius` are also accepted.\n   * @param {number} [radius] Radius in units of the projection.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(center, radius, layout) {\n    super();\n    if (layout !== undefined && radius === undefined) {\n      this.setFlatCoordinates(layout, center);\n    } else {\n      radius = radius ? radius : 0;\n      this.setCenterAndRadius(center, radius, layout);\n    }\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!Circle} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const circle = new Circle(\n      this.flatCoordinates.slice(),\n      undefined,\n      this.layout,\n    );\n    circle.applyProperties(this);\n    return circle;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    const flatCoordinates = this.flatCoordinates;\n    const dx = x - flatCoordinates[0];\n    const dy = y - flatCoordinates[1];\n    const squaredDistance = dx * dx + dy * dy;\n    if (squaredDistance < minSquaredDistance) {\n      if (squaredDistance === 0) {\n        for (let i = 0; i < this.stride; ++i) {\n          closestPoint[i] = flatCoordinates[i];\n        }\n      } else {\n        const delta = this.getRadius() / Math.sqrt(squaredDistance);\n        closestPoint[0] = flatCoordinates[0] + delta * dx;\n        closestPoint[1] = flatCoordinates[1] + delta * dy;\n        for (let i = 2; i < this.stride; ++i) {\n          closestPoint[i] = flatCoordinates[i];\n        }\n      }\n      closestPoint.length = this.stride;\n      return squaredDistance;\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   * @override\n   */\n  containsXY(x, y) {\n    const flatCoordinates = this.flatCoordinates;\n    const dx = x - flatCoordinates[0];\n    const dy = y - flatCoordinates[1];\n    return dx * dx + dy * dy <= this.getRadiusSquared_();\n  }\n\n  /**\n   * Return the center of the circle as {@link module:ol/coordinate~Coordinate coordinate}.\n   * @return {import(\"../coordinate.js\").Coordinate} Center.\n   * @api\n   */\n  getCenter() {\n    return this.flatCoordinates.slice(0, this.stride);\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   * @override\n   */\n  computeExtent(extent) {\n    const flatCoordinates = this.flatCoordinates;\n    const radius = flatCoordinates[this.stride] - flatCoordinates[0];\n    return createOrUpdate(\n      flatCoordinates[0] - radius,\n      flatCoordinates[1] - radius,\n      flatCoordinates[0] + radius,\n      flatCoordinates[1] + radius,\n      extent,\n    );\n  }\n\n  /**\n   * Return the radius of the circle.\n   * @return {number} Radius.\n   * @api\n   */\n  getRadius() {\n    return Math.sqrt(this.getRadiusSquared_());\n  }\n\n  /**\n   * @private\n   * @return {number} Radius squared.\n   */\n  getRadiusSquared_() {\n    const dx = this.flatCoordinates[this.stride] - this.flatCoordinates[0];\n    const dy = this.flatCoordinates[this.stride + 1] - this.flatCoordinates[1];\n    return dx * dx + dy * dy;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'Circle';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    const circleExtent = this.getExtent();\n    if (intersects(extent, circleExtent)) {\n      const center = this.getCenter();\n\n      if (extent[0] <= center[0] && extent[2] >= center[0]) {\n        return true;\n      }\n      if (extent[1] <= center[1] && extent[3] >= center[1]) {\n        return true;\n      }\n\n      return forEachCorner(extent, this.intersectsCoordinate.bind(this));\n    }\n    return false;\n  }\n\n  /**\n   * Set the center of the circle as {@link module:ol/coordinate~Coordinate coordinate}.\n   * @param {import(\"../coordinate.js\").Coordinate} center Center.\n   * @api\n   */\n  setCenter(center) {\n    const stride = this.stride;\n    const radius = this.flatCoordinates[stride] - this.flatCoordinates[0];\n    const flatCoordinates = center.slice();\n    flatCoordinates[stride] = flatCoordinates[0] + radius;\n    for (let i = 1; i < stride; ++i) {\n      flatCoordinates[stride + i] = center[i];\n    }\n    this.setFlatCoordinates(this.layout, flatCoordinates);\n    this.changed();\n  }\n\n  /**\n   * Set the center (as {@link module:ol/coordinate~Coordinate coordinate}) and the radius (as\n   * number) of the circle.\n   * @param {!import(\"../coordinate.js\").Coordinate} center Center.\n   * @param {number} radius Radius.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   */\n  setCenterAndRadius(center, radius, layout) {\n    this.setLayout(layout, center, 0);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    /** @type {Array<number>} */\n    const flatCoordinates = this.flatCoordinates;\n    let offset = deflateCoordinate(flatCoordinates, 0, center, this.stride);\n    flatCoordinates[offset++] = flatCoordinates[0] + radius;\n    for (let i = 1, ii = this.stride; i < ii; ++i) {\n      flatCoordinates[offset++] = flatCoordinates[i];\n    }\n    flatCoordinates.length = offset;\n    this.changed();\n  }\n\n  /**\n   * @override\n   */\n  getCoordinates() {\n    return null;\n  }\n\n  /**\n   * @override\n   */\n  setCoordinates(coordinates, layout) {}\n\n  /**\n   * Set the radius of the circle. The radius is in the units of the projection.\n   * @param {number} radius Radius.\n   * @api\n   */\n  setRadius(radius) {\n    this.flatCoordinates[this.stride] = this.flatCoordinates[0] + radius;\n    this.changed();\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @param {number} angle Rotation angle in counter-clockwise radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   * @override\n   */\n  rotate(angle, anchor) {\n    const center = this.getCenter();\n    const stride = this.getStride();\n    this.setCenter(\n      rotate(center, 0, center.length, stride, angle, anchor, center),\n    );\n    this.changed();\n  }\n}\n\n/**\n * Transform each coordinate of the circle from one coordinate reference system\n * to another. The geometry is modified in place.\n * If you do not want the geometry modified in place, first clone() it and\n * then use this function on the clone.\n *\n * Internally a circle is currently represented by two points: the center of\n * the circle `[cx, cy]`, and the point to the right of the circle\n * `[cx + r, cy]`. This `transform` function just transforms these two points.\n * So the resulting geometry is also a circle, and that circle does not\n * correspond to the shape that would be obtained by transforming every point\n * of the original circle.\n *\n * @param {import(\"../proj.js\").ProjectionLike} source The current projection.  Can be a\n *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n * @param {import(\"../proj.js\").ProjectionLike} destination The desired projection.  Can be a\n *     string identifier or a {@link module:ol/proj/Projection~Projection} object.\n * @return {Circle} This geometry.  Note that original geometry is\n *     modified in place.\n * @function\n * @api\n */\nCircle.prototype.transform;\nexport default Circle;\n", "/**\n * @module ol/geom/GeometryCollection\n */\nimport EventType from '../events/EventType.js';\nimport {listen, unlistenByKey} from '../events.js';\nimport {\n  closestSquaredDistanceXY,\n  createOrUpdateEmpty,\n  extend,\n  getCenter,\n} from '../extent.js';\nimport Geometry from './Geometry.js';\n\n/**\n * @classdesc\n * An array of {@link module:ol/geom/Geometry~Geometry} objects.\n *\n * @api\n */\nclass GeometryCollection extends Geometry {\n  /**\n   * @param {Array<Geometry>} geometries Geometries.\n   */\n  constructor(geometries) {\n    super();\n\n    /**\n     * @private\n     * @type {Array<Geometry>}\n     */\n    this.geometries_ = geometries;\n\n    /**\n     * @private\n     * @type {Array<import(\"../events.js\").EventsKey>}\n     */\n    this.changeEventsKeys_ = [];\n\n    this.listenGeometriesChange_();\n  }\n\n  /**\n   * @private\n   */\n  unlistenGeometriesChange_() {\n    this.changeEventsKeys_.forEach(unlistenByKey);\n    this.changeEventsKeys_.length = 0;\n  }\n\n  /**\n   * @private\n   */\n  listenGeometriesChange_() {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      this.changeEventsKeys_.push(\n        listen(geometries[i], EventType.CHANGE, this.changed, this),\n      );\n    }\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!GeometryCollection} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const geometryCollection = new GeometryCollection(\n      cloneGeometries(this.geometries_),\n    );\n    geometryCollection.applyProperties(this);\n    return geometryCollection;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      minSquaredDistance = geometries[i].closestPointXY(\n        x,\n        y,\n        closestPoint,\n        minSquaredDistance,\n      );\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   * @override\n   */\n  containsXY(x, y) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      if (geometries[i].containsXY(x, y)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @protected\n   * @return {import(\"../extent.js\").Extent} extent Extent.\n   * @override\n   */\n  computeExtent(extent) {\n    createOrUpdateEmpty(extent);\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      extend(extent, geometries[i].getExtent());\n    }\n    return extent;\n  }\n\n  /**\n   * Return the geometries that make up this geometry collection.\n   * @return {Array<Geometry>} Geometries.\n   * @api\n   */\n  getGeometries() {\n    return cloneGeometries(this.geometries_);\n  }\n\n  /**\n   * @return {Array<Geometry>} Geometries.\n   */\n  getGeometriesArray() {\n    return this.geometries_;\n  }\n\n  /**\n   * @return {Array<Geometry>} Geometries.\n   */\n  getGeometriesArrayRecursive() {\n    /** @type {Array<Geometry>} */\n    let geometriesArray = [];\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      if (geometries[i].getType() === this.getType()) {\n        geometriesArray = geometriesArray.concat(\n          /** @type {GeometryCollection} */ (\n            geometries[i]\n          ).getGeometriesArrayRecursive(),\n        );\n      } else {\n        geometriesArray.push(geometries[i]);\n      }\n    }\n    return geometriesArray;\n  }\n\n  /**\n   * Create a simplified version of this geometry using the Douglas Peucker algorithm.\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {GeometryCollection} Simplified GeometryCollection.\n   * @override\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    if (this.simplifiedGeometryRevision !== this.getRevision()) {\n      this.simplifiedGeometryMaxMinSquaredTolerance = 0;\n      this.simplifiedGeometryRevision = this.getRevision();\n    }\n    if (\n      squaredTolerance < 0 ||\n      (this.simplifiedGeometryMaxMinSquaredTolerance !== 0 &&\n        squaredTolerance < this.simplifiedGeometryMaxMinSquaredTolerance)\n    ) {\n      return this;\n    }\n\n    const simplifiedGeometries = [];\n    const geometries = this.geometries_;\n    let simplified = false;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      const geometry = geometries[i];\n      const simplifiedGeometry =\n        geometry.getSimplifiedGeometry(squaredTolerance);\n      simplifiedGeometries.push(simplifiedGeometry);\n      if (simplifiedGeometry !== geometry) {\n        simplified = true;\n      }\n    }\n    if (simplified) {\n      const simplifiedGeometryCollection = new GeometryCollection(\n        simplifiedGeometries,\n      );\n      return simplifiedGeometryCollection;\n    }\n    this.simplifiedGeometryMaxMinSquaredTolerance = squaredTolerance;\n    return this;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'GeometryCollection';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      if (geometries[i].intersectsExtent(extent)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    return this.geometries_.length === 0;\n  }\n\n  /**\n   * Rotate the geometry around a given coordinate. This modifies the geometry\n   * coordinates in place.\n   * @param {number} angle Rotation angle in radians.\n   * @param {import(\"../coordinate.js\").Coordinate} anchor The rotation center.\n   * @api\n   * @override\n   */\n  rotate(angle, anchor) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].rotate(angle, anchor);\n    }\n    this.changed();\n  }\n\n  /**\n   * Scale the geometry (with an optional origin).  This modifies the geometry\n   * coordinates in place.\n   * @abstract\n   * @param {number} sx The scaling factor in the x-direction.\n   * @param {number} [sy] The scaling factor in the y-direction (defaults to sx).\n   * @param {import(\"../coordinate.js\").Coordinate} [anchor] The scale origin (defaults to the center\n   *     of the geometry extent).\n   * @api\n   * @override\n   */\n  scale(sx, sy, anchor) {\n    if (!anchor) {\n      anchor = getCenter(this.getExtent());\n    }\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].scale(sx, sy, anchor);\n    }\n    this.changed();\n  }\n\n  /**\n   * Set the geometries that make up this geometry collection.\n   * @param {Array<Geometry>} geometries Geometries.\n   * @api\n   */\n  setGeometries(geometries) {\n    this.setGeometriesArray(cloneGeometries(geometries));\n  }\n\n  /**\n   * @param {Array<Geometry>} geometries Geometries.\n   */\n  setGeometriesArray(geometries) {\n    this.unlistenGeometriesChange_();\n    this.geometries_ = geometries;\n    this.listenGeometriesChange_();\n    this.changed();\n  }\n\n  /**\n   * Apply a transform function to the coordinates of the geometry.\n   * The geometry is modified in place.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   * @param {import(\"../proj.js\").TransformFunction} transformFn Transform function.\n   * Called with a flat array of geometry coordinates.\n   * @api\n   * @override\n   */\n  applyTransform(transformFn) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].applyTransform(transformFn);\n    }\n    this.changed();\n  }\n\n  /**\n   * Translate the geometry.  This modifies the geometry coordinates in place.  If\n   * instead you want a new geometry, first `clone()` this geometry.\n   * @param {number} deltaX Delta X.\n   * @param {number} deltaY Delta Y.\n   * @api\n   * @override\n   */\n  translate(deltaX, deltaY) {\n    const geometries = this.geometries_;\n    for (let i = 0, ii = geometries.length; i < ii; ++i) {\n      geometries[i].translate(deltaX, deltaY);\n    }\n    this.changed();\n  }\n\n  /**\n   * Clean up.\n   * @override\n   */\n  disposeInternal() {\n    this.unlistenGeometriesChange_();\n    super.disposeInternal();\n  }\n}\n\n/**\n * @param {Array<Geometry>} geometries Geometries.\n * @return {Array<Geometry>} Cloned geometries.\n */\nfunction cloneGeometries(geometries) {\n  return geometries.map((geometry) => geometry.clone());\n}\n\nexport default GeometryCollection;\n", "/**\n * @module ol/geom/flat/area\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {number} Area.\n */\nexport function linearRing(flatCoordinates, offset, end, stride) {\n  let twiceArea = 0;\n  const x0 = flatCoordinates[end - stride];\n  const y0 = flatCoordinates[end - stride + 1];\n  let dx1 = 0;\n  let dy1 = 0;\n  for (; offset < end; offset += stride) {\n    const dx2 = flatCoordinates[offset] - x0;\n    const dy2 = flatCoordinates[offset + 1] - y0;\n    twiceArea += dy1 * dx2 - dx1 * dy2;\n    dx1 = dx2;\n    dy1 = dy2;\n  }\n  return twiceArea / 2;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @return {number} Area.\n */\nexport function linearRings(flatCoordinates, offset, ends, stride) {\n  let area = 0;\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    area += linearRing(flatCoordinates, offset, end, stride);\n    offset = end;\n  }\n  return area;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @return {number} Area.\n */\nexport function linearRingss(flatCoordinates, offset, endss, stride) {\n  let area = 0;\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    area += linearRings(flatCoordinates, offset, ends, stride);\n    offset = ends[ends.length - 1];\n  }\n  return area;\n}\n", "/**\n * @module ol/geom/flat/closest\n */\nimport {lerp, squaredDistance as squaredDx} from '../../math.js';\n\n/**\n * Returns the point on the 2D line segment flatCoordinates[offset1] to\n * flatCoordinates[offset2] that is closest to the point (x, y).  Extra\n * dimensions are linearly interpolated.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset1 Offset 1.\n * @param {number} offset2 Offset 2.\n * @param {number} stride Stride.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n */\nfunction assignClosest(\n  flatCoordinates,\n  offset1,\n  offset2,\n  stride,\n  x,\n  y,\n  closestPoint,\n) {\n  const x1 = flatCoordinates[offset1];\n  const y1 = flatCoordinates[offset1 + 1];\n  const dx = flatCoordinates[offset2] - x1;\n  const dy = flatCoordinates[offset2 + 1] - y1;\n  let offset;\n  if (dx === 0 && dy === 0) {\n    offset = offset1;\n  } else {\n    const t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n    if (t > 1) {\n      offset = offset2;\n    } else if (t > 0) {\n      for (let i = 0; i < stride; ++i) {\n        closestPoint[i] = lerp(\n          flatCoordinates[offset1 + i],\n          flatCoordinates[offset2 + i],\n          t,\n        );\n      }\n      closestPoint.length = stride;\n      return;\n    } else {\n      offset = offset1;\n    }\n  }\n  for (let i = 0; i < stride; ++i) {\n    closestPoint[i] = flatCoordinates[offset + i];\n  }\n  closestPoint.length = stride;\n}\n\n/**\n * Return the squared of the largest distance between any pair of consecutive\n * coordinates.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} max Max squared delta.\n * @return {number} Max squared delta.\n */\nexport function maxSquaredDelta(flatCoordinates, offset, end, stride, max) {\n  let x1 = flatCoordinates[offset];\n  let y1 = flatCoordinates[offset + 1];\n  for (offset += stride; offset < end; offset += stride) {\n    const x2 = flatCoordinates[offset];\n    const y2 = flatCoordinates[offset + 1];\n    const squaredDelta = squaredDx(x1, y1, x2, y2);\n    if (squaredDelta > max) {\n      max = squaredDelta;\n    }\n    x1 = x2;\n    y1 = y2;\n  }\n  return max;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} max Max squared delta.\n * @return {number} Max squared delta.\n */\nexport function arrayMaxSquaredDelta(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  max,\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    max = maxSquaredDelta(flatCoordinates, offset, end, stride, max);\n    offset = end;\n  }\n  return max;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} max Max squared delta.\n * @return {number} Max squared delta.\n */\nexport function multiArrayMaxSquaredDelta(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  max,\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    max = arrayMaxSquaredDelta(flatCoordinates, offset, ends, stride, max);\n    offset = ends[ends.length - 1];\n  }\n  return max;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {number} maxDelta Max delta.\n * @param {boolean} isRing Is ring.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n * @param {number} minSquaredDistance Minimum squared distance.\n * @param {Array<number>} [tmpPoint] Temporary point object.\n * @return {number} Minimum squared distance.\n */\nexport function assignClosestPoint(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  maxDelta,\n  isRing,\n  x,\n  y,\n  closestPoint,\n  minSquaredDistance,\n  tmpPoint,\n) {\n  if (offset == end) {\n    return minSquaredDistance;\n  }\n  let i, squaredDistance;\n  if (maxDelta === 0) {\n    // All points are identical, so just test the first point.\n    squaredDistance = squaredDx(\n      x,\n      y,\n      flatCoordinates[offset],\n      flatCoordinates[offset + 1],\n    );\n    if (squaredDistance < minSquaredDistance) {\n      for (i = 0; i < stride; ++i) {\n        closestPoint[i] = flatCoordinates[offset + i];\n      }\n      closestPoint.length = stride;\n      return squaredDistance;\n    }\n    return minSquaredDistance;\n  }\n  tmpPoint = tmpPoint ? tmpPoint : [NaN, NaN];\n  let index = offset + stride;\n  while (index < end) {\n    assignClosest(\n      flatCoordinates,\n      index - stride,\n      index,\n      stride,\n      x,\n      y,\n      tmpPoint,\n    );\n    squaredDistance = squaredDx(x, y, tmpPoint[0], tmpPoint[1]);\n    if (squaredDistance < minSquaredDistance) {\n      minSquaredDistance = squaredDistance;\n      for (i = 0; i < stride; ++i) {\n        closestPoint[i] = tmpPoint[i];\n      }\n      closestPoint.length = stride;\n      index += stride;\n    } else {\n      // Skip ahead multiple points, because we know that all the skipped\n      // points cannot be any closer than the closest point we have found so\n      // far.  We know this because we know how close the current point is, how\n      // close the closest point we have found so far is, and the maximum\n      // distance between consecutive points.  For example, if we're currently\n      // at distance 10, the best we've found so far is 3, and that the maximum\n      // distance between consecutive points is 2, then we'll need to skip at\n      // least (10 - 3) / 2 == 3 (rounded down) points to have any chance of\n      // finding a closer point.  We use Math.max(..., 1) to ensure that we\n      // always advance at least one point, to avoid an infinite loop.\n      index +=\n        stride *\n        Math.max(\n          ((Math.sqrt(squaredDistance) - Math.sqrt(minSquaredDistance)) /\n            maxDelta) |\n            0,\n          1,\n        );\n    }\n  }\n  if (isRing) {\n    // Check the closing segment.\n    assignClosest(\n      flatCoordinates,\n      end - stride,\n      offset,\n      stride,\n      x,\n      y,\n      tmpPoint,\n    );\n    squaredDistance = squaredDx(x, y, tmpPoint[0], tmpPoint[1]);\n    if (squaredDistance < minSquaredDistance) {\n      minSquaredDistance = squaredDistance;\n      for (i = 0; i < stride; ++i) {\n        closestPoint[i] = tmpPoint[i];\n      }\n      closestPoint.length = stride;\n    }\n  }\n  return minSquaredDistance;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {number} maxDelta Max delta.\n * @param {boolean} isRing Is ring.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n * @param {number} minSquaredDistance Minimum squared distance.\n * @param {Array<number>} [tmpPoint] Temporary point object.\n * @return {number} Minimum squared distance.\n */\nexport function assignClosestArrayPoint(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  maxDelta,\n  isRing,\n  x,\n  y,\n  closestPoint,\n  minSquaredDistance,\n  tmpPoint,\n) {\n  tmpPoint = tmpPoint ? tmpPoint : [NaN, NaN];\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    const end = ends[i];\n    minSquaredDistance = assignClosestPoint(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      maxDelta,\n      isRing,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n      tmpPoint,\n    );\n    offset = end;\n  }\n  return minSquaredDistance;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {number} maxDelta Max delta.\n * @param {boolean} isRing Is ring.\n * @param {number} x X.\n * @param {number} y Y.\n * @param {Array<number>} closestPoint Closest point.\n * @param {number} minSquaredDistance Minimum squared distance.\n * @param {Array<number>} [tmpPoint] Temporary point object.\n * @return {number} Minimum squared distance.\n */\nexport function assignClosestMultiArrayPoint(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  maxDelta,\n  isRing,\n  x,\n  y,\n  closestPoint,\n  minSquaredDistance,\n  tmpPoint,\n) {\n  tmpPoint = tmpPoint ? tmpPoint : [NaN, NaN];\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    minSquaredDistance = assignClosestArrayPoint(\n      flatCoordinates,\n      offset,\n      ends,\n      stride,\n      maxDelta,\n      isRing,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n      tmpPoint,\n    );\n    offset = ends[ends.length - 1];\n  }\n  return minSquaredDistance;\n}\n", "/**\n * @module ol/geom/flat/inflate\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {Array<import(\"../../coordinate.js\").Coordinate>} [coordinates] Coordinates.\n * @return {Array<import(\"../../coordinate.js\").Coordinate>} Coordinates.\n */\nexport function inflateCoordinates(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  coordinates,\n) {\n  coordinates = coordinates !== undefined ? coordinates : [];\n  let i = 0;\n  for (let j = offset; j < end; j += stride) {\n    coordinates[i++] = flatCoordinates.slice(j, j + stride);\n  }\n  coordinates.length = i;\n  return coordinates;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {Array<Array<import(\"../../coordinate.js\").Coordinate>>} [coordinatess] Coordinatess.\n * @return {Array<Array<import(\"../../coordinate.js\").Coordinate>>} Coordinatess.\n */\nexport function inflateCoordinatesArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  coordinatess,\n) {\n  coordinatess = coordinatess !== undefined ? coordinatess : [];\n  let i = 0;\n  for (let j = 0, jj = ends.length; j < jj; ++j) {\n    const end = ends[j];\n    coordinatess[i++] = inflateCoordinates(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      coordinatess[i],\n    );\n    offset = end;\n  }\n  coordinatess.length = i;\n  return coordinatess;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {Array<Array<Array<import(\"../../coordinate.js\").Coordinate>>>} [coordinatesss]\n *     Coordinatesss.\n * @return {Array<Array<Array<import(\"../../coordinate.js\").Coordinate>>>} Coordinatesss.\n */\nexport function inflateMultiCoordinatesArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  coordinatesss,\n) {\n  coordinatesss = coordinatesss !== undefined ? coordinatesss : [];\n  let i = 0;\n  for (let j = 0, jj = endss.length; j < jj; ++j) {\n    const ends = endss[j];\n    coordinatesss[i++] =\n      ends.length === 1 && ends[0] === offset\n        ? []\n        : inflateCoordinatesArray(\n            flatCoordinates,\n            offset,\n            ends,\n            stride,\n            coordinatesss[i],\n          );\n    offset = ends[ends.length - 1];\n  }\n  coordinatesss.length = i;\n  return coordinatesss;\n}\n", "/**\n * @module ol/geom/LinearRing\n */\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {linearRing as linearRingArea} from './flat/area.js';\nimport {assignClosestPoint, maxSquaredDelta} from './flat/closest.js';\nimport {deflateCoordinates} from './flat/deflate.js';\nimport {inflateCoordinates} from './flat/inflate.js';\nimport {douglasPeucker} from './flat/simplify.js';\n\n/**\n * @classdesc\n * Linear ring geometry. Only used as part of polygon; cannot be rendered\n * on its own.\n *\n * @api\n */\nclass LinearRing extends SimpleGeometry {\n  /**\n   * @param {Array<import(\"../coordinate.js\").Coordinate>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    if (layout !== undefined && !Array.isArray(coordinates[0])) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates),\n      );\n    } else {\n      this.setCoordinates(\n        /** @type {Array<import(\"../coordinate.js\").Coordinate>} */ (\n          coordinates\n        ),\n        layout,\n      );\n    }\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!LinearRing} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    return new LinearRing(this.flatCoordinates.slice(), this.layout);\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        maxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.flatCoordinates.length,\n          this.stride,\n          0,\n        ),\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestPoint(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      this.maxDelta_,\n      true,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n    );\n  }\n\n  /**\n   * Return the area of the linear ring on projected plane.\n   * @return {number} Area (on projected plane).\n   * @api\n   */\n  getArea() {\n    return linearRingArea(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n    );\n  }\n\n  /**\n   * Return the coordinates of the linear ring.\n   * @return {Array<import(\"../coordinate.js\").Coordinate>} Coordinates.\n   * @api\n   * @override\n   */\n  getCoordinates() {\n    return inflateCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n    );\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {LinearRing} Simplified LinearRing.\n   * @protected\n   * @override\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    /** @type {Array<number>} */\n    const simplifiedFlatCoordinates = [];\n    simplifiedFlatCoordinates.length = douglasPeucker(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0,\n    );\n    return new LinearRing(simplifiedFlatCoordinates, 'XY');\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'LinearRing';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    return false;\n  }\n\n  /**\n   * Set the coordinates of the linear ring.\n   * @param {!Array<import(\"../coordinate.js\").Coordinate>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   * @override\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 1);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinates(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n    );\n    this.changed();\n  }\n}\n\nexport default LinearRing;\n", "/**\n * @module ol/geom/flat/segments\n */\n\n/**\n * This function calls `callback` for each segment of the flat coordinates\n * array. If the callback returns a truthy value the function returns that\n * value immediately. Otherwise the function returns `false`.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {function(import(\"../../coordinate.js\").Coordinate, import(\"../../coordinate.js\").Coordinate): T} callback Function\n *     called for each segment.\n * @return {T|boolean} Value.\n * @template T\n */\nexport function forEach(flatCoordinates, offset, end, stride, callback) {\n  let ret;\n  offset += stride;\n  for (; offset < end; offset += stride) {\n    ret = callback(\n      flatCoordinates.slice(offset - stride, offset),\n      flatCoordinates.slice(offset, offset + stride),\n    );\n    if (ret) {\n      return ret;\n    }\n  }\n  return false;\n}\n\n/**\n * Calculate the intersection point of two line segments.\n * Reference: https://stackoverflow.com/a/72474223/2389327\n * @param {Array<import(\"../../coordinate.js\").Coordinate>} segment1 The first line segment as an array of two points.\n * @param {Array<import(\"../../coordinate.js\").Coordinate>} segment2 The second line segment as an array of two points.\n * @return {import(\"../../coordinate.js\").Coordinate|undefined} The intersection point or `undefined` if no intersection.\n */\nexport function getIntersectionPoint(segment1, segment2) {\n  const [a, b] = segment1;\n  const [c, d] = segment2;\n  const t =\n    ((a[0] - c[0]) * (c[1] - d[1]) - (a[1] - c[1]) * (c[0] - d[0])) /\n    ((a[0] - b[0]) * (c[1] - d[1]) - (a[1] - b[1]) * (c[0] - d[0]));\n  const u =\n    ((a[0] - c[0]) * (a[1] - b[1]) - (a[1] - c[1]) * (a[0] - b[0])) /\n    ((a[0] - b[0]) * (c[1] - d[1]) - (a[1] - b[1]) * (c[0] - d[0]));\n\n  // Check if lines actually intersect\n  if (0 <= t && t <= 1 && 0 <= u && u <= 1) {\n    return [a[0] + t * (b[0] - a[0]), a[1] + t * (b[1] - a[1])];\n  }\n  return undefined;\n}\n", "/**\n * @module ol/geom/flat/intersectsextent\n */\nimport {\n  createEmpty,\n  extendFlatCoordinates,\n  intersects,\n  intersectsSegment,\n} from '../../extent.js';\nimport {linearRingContainsExtent, linearRingContainsXY} from './contains.js';\nimport {forEach as forEachSegment} from './segments.js';\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @param {import('../../extent.js').Extent} [coordinatesExtent] Coordinates extent\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLineString(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  extent,\n  coordinatesExtent,\n) {\n  coordinatesExtent =\n    coordinatesExtent ??\n    extendFlatCoordinates(createEmpty(), flatCoordinates, offset, end, stride);\n  if (!intersects(extent, coordinatesExtent)) {\n    return false;\n  }\n  if (\n    (coordinatesExtent[0] >= extent[0] && coordinatesExtent[2] <= extent[2]) ||\n    (coordinatesExtent[1] >= extent[1] && coordinatesExtent[3] <= extent[3])\n  ) {\n    return true;\n  }\n  return forEachSegment(\n    flatCoordinates,\n    offset,\n    end,\n    stride,\n    /**\n     * @param {import(\"../../coordinate.js\").Coordinate} point1 Start point.\n     * @param {import(\"../../coordinate.js\").Coordinate} point2 End point.\n     * @return {boolean} `true` if the segment and the extent intersect,\n     *     `false` otherwise.\n     */\n    function (point1, point2) {\n      return intersectsSegment(extent, point1, point2);\n    },\n  );\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLineStringArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  extent,\n) {\n  for (let i = 0, ii = ends.length; i < ii; ++i) {\n    if (\n      intersectsLineString(flatCoordinates, offset, ends[i], stride, extent)\n    ) {\n      return true;\n    }\n    offset = ends[i];\n  }\n  return false;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLinearRing(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  extent,\n) {\n  if (intersectsLineString(flatCoordinates, offset, end, stride, extent)) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[0],\n      extent[1],\n    )\n  ) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[0],\n      extent[3],\n    )\n  ) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[2],\n      extent[1],\n    )\n  ) {\n    return true;\n  }\n  if (\n    linearRingContainsXY(\n      flatCoordinates,\n      offset,\n      end,\n      stride,\n      extent[2],\n      extent[3],\n    )\n  ) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<number>} ends Ends.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLinearRingArray(\n  flatCoordinates,\n  offset,\n  ends,\n  stride,\n  extent,\n) {\n  if (!intersectsLinearRing(flatCoordinates, offset, ends[0], stride, extent)) {\n    return false;\n  }\n  if (ends.length === 1) {\n    return true;\n  }\n  for (let i = 1, ii = ends.length; i < ii; ++i) {\n    if (\n      linearRingContainsExtent(\n        flatCoordinates,\n        ends[i - 1],\n        ends[i],\n        stride,\n        extent,\n      )\n    ) {\n      if (\n        !intersectsLineString(\n          flatCoordinates,\n          ends[i - 1],\n          ends[i],\n          stride,\n          extent,\n        )\n      ) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {Array<Array<number>>} endss Endss.\n * @param {number} stride Stride.\n * @param {import(\"../../extent.js\").Extent} extent Extent.\n * @return {boolean} True if the geometry and the extent intersect.\n */\nexport function intersectsLinearRingMultiArray(\n  flatCoordinates,\n  offset,\n  endss,\n  stride,\n  extent,\n) {\n  for (let i = 0, ii = endss.length; i < ii; ++i) {\n    const ends = endss[i];\n    if (\n      intersectsLinearRingArray(flatCoordinates, offset, ends, stride, extent)\n    ) {\n      return true;\n    }\n    offset = ends[ends.length - 1];\n  }\n  return false;\n}\n", "/**\n * @module ol/geom/flat/length\n */\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {number} Length.\n */\nexport function lineStringLength(flatCoordinates, offset, end, stride) {\n  let x1 = flatCoordinates[offset];\n  let y1 = flatCoordinates[offset + 1];\n  let length = 0;\n  for (let i = offset + stride; i < end; i += stride) {\n    const x2 = flatCoordinates[i];\n    const y2 = flatCoordinates[i + 1];\n    length += Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n    x1 = x2;\n    y1 = y2;\n  }\n  return length;\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {number} Perimeter.\n */\nexport function linearRingLength(flatCoordinates, offset, end, stride) {\n  let perimeter = lineStringLength(flatCoordinates, offset, end, stride);\n  const dx = flatCoordinates[end - stride] - flatCoordinates[offset];\n  const dy = flatCoordinates[end - stride + 1] - flatCoordinates[offset + 1];\n  perimeter += Math.sqrt(dx * dx + dy * dy);\n  return perimeter;\n}\n", "/**\n * @module ol/geom/LineString\n */\nimport {extend} from '../array.js';\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {assignClosestPoint, maxSquaredDelta} from './flat/closest.js';\nimport {deflateCoordinates} from './flat/deflate.js';\nimport {inflateCoordinates} from './flat/inflate.js';\nimport {interpolatePoint, lineStringCoordinateAtM} from './flat/interpolate.js';\nimport {intersectsLineString} from './flat/intersectsextent.js';\nimport {lineStringLength} from './flat/length.js';\nimport {forEach as forEachSegment} from './flat/segments.js';\nimport {douglasPeucker} from './flat/simplify.js';\n\n/**\n * @classdesc\n * Linestring geometry.\n *\n * @api\n */\nclass LineString extends SimpleGeometry {\n  /**\n   * @param {Array<import(\"../coordinate.js\").Coordinate>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n\n    /**\n     * @private\n     * @type {import(\"../coordinate.js\").Coordinate|null}\n     */\n    this.flatMidpoint_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.flatMidpointRevision_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    if (layout !== undefined && !Array.isArray(coordinates[0])) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates),\n      );\n    } else {\n      this.setCoordinates(\n        /** @type {Array<import(\"../coordinate.js\").Coordinate>} */ (\n          coordinates\n        ),\n        layout,\n      );\n    }\n  }\n\n  /**\n   * Append the passed coordinate to the coordinates of the linestring.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @api\n   */\n  appendCoordinate(coordinate) {\n    extend(this.flatCoordinates, coordinate);\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!LineString} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const lineString = new LineString(\n      this.flatCoordinates.slice(),\n      this.layout,\n    );\n    lineString.applyProperties(this);\n    return lineString;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        maxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.flatCoordinates.length,\n          this.stride,\n          0,\n        ),\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestPoint(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      this.maxDelta_,\n      false,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n    );\n  }\n\n  /**\n   * Iterate over each segment, calling the provided callback.\n   * If the callback returns a truthy value the function returns that\n   * value immediately. Otherwise the function returns `false`.\n   *\n   * @param {function(this: S, import(\"../coordinate.js\").Coordinate, import(\"../coordinate.js\").Coordinate): T} callback Function\n   *     called for each segment. The function will receive two arguments, the start and end coordinates of the segment.\n   * @return {T|boolean} Value.\n   * @template T,S\n   * @api\n   */\n  forEachSegment(callback) {\n    return forEachSegment(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      callback,\n    );\n  }\n\n  /**\n   * Returns the coordinate at `m` using linear interpolation, or `null` if no\n   * such coordinate exists.\n   *\n   * `extrapolate` controls extrapolation beyond the range of Ms in the\n   * MultiLineString. If `extrapolate` is `true` then Ms less than the first\n   * M will return the first coordinate and Ms greater than the last M will\n   * return the last coordinate.\n   *\n   * @param {number} m M.\n   * @param {boolean} [extrapolate] Extrapolate. Default is `false`.\n   * @return {import(\"../coordinate.js\").Coordinate|null} Coordinate.\n   * @api\n   */\n  getCoordinateAtM(m, extrapolate) {\n    if (this.layout != 'XYM' && this.layout != 'XYZM') {\n      return null;\n    }\n    extrapolate = extrapolate !== undefined ? extrapolate : false;\n    return lineStringCoordinateAtM(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      m,\n      extrapolate,\n    );\n  }\n\n  /**\n   * Return the coordinates of the linestring.\n   * @return {Array<import(\"../coordinate.js\").Coordinate>} Coordinates.\n   * @api\n   * @override\n   */\n  getCoordinates() {\n    return inflateCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n    );\n  }\n\n  /**\n   * Return the coordinate at the provided fraction along the linestring.\n   * The `fraction` is a number between 0 and 1, where 0 is the start of the\n   * linestring and 1 is the end.\n   * @param {number} fraction Fraction.\n   * @param {import(\"../coordinate.js\").Coordinate} [dest] Optional coordinate whose values will\n   *     be modified. If not provided, a new coordinate will be returned.\n   * @return {import(\"../coordinate.js\").Coordinate} Coordinate of the interpolated point.\n   * @api\n   */\n  getCoordinateAt(fraction, dest) {\n    return interpolatePoint(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      fraction,\n      dest,\n      this.stride,\n    );\n  }\n\n  /**\n   * Return the length of the linestring on projected plane.\n   * @return {number} Length (on projected plane).\n   * @api\n   */\n  getLength() {\n    return lineStringLength(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n    );\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoint.\n   */\n  getFlatMidpoint() {\n    if (this.flatMidpointRevision_ != this.getRevision()) {\n      this.flatMidpoint_ = this.getCoordinateAt(\n        0.5,\n        this.flatMidpoint_ ?? undefined,\n      );\n      this.flatMidpointRevision_ = this.getRevision();\n    }\n    return /** @type {Array<number>} */ (this.flatMidpoint_);\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {LineString} Simplified LineString.\n   * @protected\n   * @override\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    /** @type {Array<number>} */\n    const simplifiedFlatCoordinates = [];\n    simplifiedFlatCoordinates.length = douglasPeucker(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0,\n    );\n    return new LineString(simplifiedFlatCoordinates, 'XY');\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'LineString';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    return intersectsLineString(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n      extent,\n      this.getExtent(),\n    );\n  }\n\n  /**\n   * Set the coordinates of the linestring.\n   * @param {!Array<import(\"../coordinate.js\").Coordinate>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   * @override\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 1);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinates(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n    );\n    this.changed();\n  }\n}\n\nexport default LineString;\n", "/**\n * @module ol/geom/MultiLineString\n */\nimport {extend} from '../array.js';\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport LineString from './LineString.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {arrayMaxSquaredDelta, assignClosestArrayPoint} from './flat/closest.js';\nimport {deflateCoordinatesArray} from './flat/deflate.js';\nimport {inflateCoordinatesArray} from './flat/inflate.js';\nimport {\n  interpolatePoint,\n  lineStringsCoordinateAtM,\n} from './flat/interpolate.js';\nimport {intersectsLineStringArray} from './flat/intersectsextent.js';\nimport {lineStringLength} from './flat/length.js';\nimport {douglasPeuckerArray} from './flat/simplify.js';\n\n/**\n * @classdesc\n * Multi-linestring geometry.\n *\n * @api\n */\nclass MultiLineString extends SimpleGeometry {\n  /**\n   * @param {Array<Array<import(\"../coordinate.js\").Coordinate>|LineString>|Array<number>} coordinates\n   *     Coordinates or LineString geometries. (For internal use, flat coordinates in\n   *     combination with `layout` and `ends` are also accepted.)\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @param {Array<number>} [ends] Flat coordinate ends for internal use.\n   */\n  constructor(coordinates, layout, ends) {\n    super();\n\n    /**\n     * @type {Array<number>}\n     * @private\n     */\n    this.ends_ = [];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    if (Array.isArray(coordinates[0])) {\n      this.setCoordinates(\n        /** @type {Array<Array<import(\"../coordinate.js\").Coordinate>>} */ (\n          coordinates\n        ),\n        layout,\n      );\n    } else if (layout !== undefined && ends) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates),\n      );\n      this.ends_ = ends;\n    } else {\n      const lineStrings = /** @type {Array<LineString>} */ (coordinates);\n      /** @type {Array<number>} */\n      const flatCoordinates = [];\n      const ends = [];\n      for (let i = 0, ii = lineStrings.length; i < ii; ++i) {\n        const lineString = lineStrings[i];\n        extend(flatCoordinates, lineString.getFlatCoordinates());\n        ends.push(flatCoordinates.length);\n      }\n      const layout =\n        lineStrings.length === 0\n          ? this.getLayout()\n          : lineStrings[0].getLayout();\n      this.setFlatCoordinates(layout, flatCoordinates);\n      this.ends_ = ends;\n    }\n  }\n\n  /**\n   * Append the passed linestring to the multilinestring.\n   * @param {LineString} lineString LineString.\n   * @api\n   */\n  appendLineString(lineString) {\n    extend(this.flatCoordinates, lineString.getFlatCoordinates().slice());\n    this.ends_.push(this.flatCoordinates.length);\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!MultiLineString} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const multiLineString = new MultiLineString(\n      this.flatCoordinates.slice(),\n      this.layout,\n      this.ends_.slice(),\n    );\n    multiLineString.applyProperties(this);\n    return multiLineString;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        arrayMaxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.ends_,\n          this.stride,\n          0,\n        ),\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestArrayPoint(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      this.maxDelta_,\n      false,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n    );\n  }\n\n  /**\n   * Returns the coordinate at `m` using linear interpolation, or `null` if no\n   * such coordinate exists.\n   *\n   * `extrapolate` controls extrapolation beyond the range of Ms in the\n   * MultiLineString. If `extrapolate` is `true` then Ms less than the first\n   * M will return the first coordinate and Ms greater than the last M will\n   * return the last coordinate.\n   *\n   * `interpolate` controls interpolation between consecutive LineStrings\n   * within the MultiLineString. If `interpolate` is `true` the coordinates\n   * will be linearly interpolated between the last coordinate of one LineString\n   * and the first coordinate of the next LineString.  If `interpolate` is\n   * `false` then the function will return `null` for Ms falling between\n   * LineStrings.\n   *\n   * @param {number} m M.\n   * @param {boolean} [extrapolate] Extrapolate. Default is `false`.\n   * @param {boolean} [interpolate] Interpolate. Default is `false`.\n   * @return {import(\"../coordinate.js\").Coordinate|null} Coordinate.\n   * @api\n   */\n  getCoordinateAtM(m, extrapolate, interpolate) {\n    if (\n      (this.layout != 'XYM' && this.layout != 'XYZM') ||\n      this.flatCoordinates.length === 0\n    ) {\n      return null;\n    }\n    extrapolate = extrapolate !== undefined ? extrapolate : false;\n    interpolate = interpolate !== undefined ? interpolate : false;\n    return lineStringsCoordinateAtM(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      m,\n      extrapolate,\n      interpolate,\n    );\n  }\n\n  /**\n   * Return the coordinates of the multilinestring.\n   * @return {Array<Array<import(\"../coordinate.js\").Coordinate>>} Coordinates.\n   * @api\n   * @override\n   */\n  getCoordinates() {\n    return inflateCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n    );\n  }\n\n  /**\n   * @return {Array<number>} Ends.\n   */\n  getEnds() {\n    return this.ends_;\n  }\n\n  /**\n   * Return the linestring at the specified index.\n   * @param {number} index Index.\n   * @return {LineString} LineString.\n   * @api\n   */\n  getLineString(index) {\n    if (index < 0 || this.ends_.length <= index) {\n      return null;\n    }\n    return new LineString(\n      this.flatCoordinates.slice(\n        index === 0 ? 0 : this.ends_[index - 1],\n        this.ends_[index],\n      ),\n      this.layout,\n    );\n  }\n\n  /**\n   * Return the linestrings of this multilinestring.\n   * @return {Array<LineString>} LineStrings.\n   * @api\n   */\n  getLineStrings() {\n    const flatCoordinates = this.flatCoordinates;\n    const ends = this.ends_;\n    const layout = this.layout;\n    /** @type {Array<LineString>} */\n    const lineStrings = [];\n    let offset = 0;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      const end = ends[i];\n      const lineString = new LineString(\n        flatCoordinates.slice(offset, end),\n        layout,\n      );\n      lineStrings.push(lineString);\n      offset = end;\n    }\n    return lineStrings;\n  }\n\n  /**\n   * Return the sum of all line string lengths\n   * @return {number} Length (on projected plane).\n   * @api\n   */\n  getLength() {\n    const ends = this.ends_;\n    let start = 0;\n    let length = 0;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      length += lineStringLength(\n        this.flatCoordinates,\n        start,\n        ends[i],\n        this.stride,\n      );\n      start = ends[i];\n    }\n    return length;\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoints.\n   */\n  getFlatMidpoints() {\n    /** @type {Array<number>} */\n    const midpoints = [];\n    const flatCoordinates = this.flatCoordinates;\n    let offset = 0;\n    const ends = this.ends_;\n    const stride = this.stride;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      const end = ends[i];\n      const midpoint = interpolatePoint(\n        flatCoordinates,\n        offset,\n        end,\n        stride,\n        0.5,\n      );\n      extend(midpoints, midpoint);\n      offset = end;\n    }\n    return midpoints;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {MultiLineString} Simplified MultiLineString.\n   * @protected\n   * @override\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    /** @type {Array<number>} */\n    const simplifiedFlatCoordinates = [];\n    /** @type {Array<number>} */\n    const simplifiedEnds = [];\n    simplifiedFlatCoordinates.length = douglasPeuckerArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      squaredTolerance,\n      simplifiedFlatCoordinates,\n      0,\n      simplifiedEnds,\n    );\n    return new MultiLineString(simplifiedFlatCoordinates, 'XY', simplifiedEnds);\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'MultiLineString';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    return intersectsLineStringArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      extent,\n    );\n  }\n\n  /**\n   * Set the coordinates of the multilinestring.\n   * @param {!Array<Array<import(\"../coordinate.js\").Coordinate>>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   * @override\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 2);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    const ends = deflateCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n      this.ends_,\n    );\n    this.flatCoordinates.length = ends.length === 0 ? 0 : ends[ends.length - 1];\n    this.changed();\n  }\n}\n\nexport default MultiLineString;\n", "/**\n * @module ol/geom/MultiPoint\n */\nimport {extend} from '../array.js';\nimport {closestSquaredDistanceXY, containsXY} from '../extent.js';\nimport {squaredDistance as squaredDx} from '../math.js';\nimport Point from './Point.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {deflateCoordinates} from './flat/deflate.js';\nimport {inflateCoordinates} from './flat/inflate.js';\n\n/**\n * @classdesc\n * Multi-point geometry.\n *\n * @api\n */\nclass MultiPoint extends SimpleGeometry {\n  /**\n   * @param {Array<import(\"../coordinate.js\").Coordinate>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   */\n  constructor(coordinates, layout) {\n    super();\n    if (layout && !Array.isArray(coordinates[0])) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates),\n      );\n    } else {\n      this.setCoordinates(\n        /** @type {Array<import(\"../coordinate.js\").Coordinate>} */ (\n          coordinates\n        ),\n        layout,\n      );\n    }\n  }\n\n  /**\n   * Append the passed point to this multipoint.\n   * @param {Point} point Point.\n   * @api\n   */\n  appendPoint(point) {\n    extend(this.flatCoordinates, point.getFlatCoordinates());\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!MultiPoint} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const multiPoint = new MultiPoint(\n      this.flatCoordinates.slice(),\n      this.layout,\n    );\n    multiPoint.applyProperties(this);\n    return multiPoint;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    const flatCoordinates = this.flatCoordinates;\n    const stride = this.stride;\n    for (let i = 0, ii = flatCoordinates.length; i < ii; i += stride) {\n      const squaredDistance = squaredDx(\n        x,\n        y,\n        flatCoordinates[i],\n        flatCoordinates[i + 1],\n      );\n      if (squaredDistance < minSquaredDistance) {\n        minSquaredDistance = squaredDistance;\n        for (let j = 0; j < stride; ++j) {\n          closestPoint[j] = flatCoordinates[i + j];\n        }\n        closestPoint.length = stride;\n      }\n    }\n    return minSquaredDistance;\n  }\n\n  /**\n   * Return the coordinates of the multipoint.\n   * @return {Array<import(\"../coordinate.js\").Coordinate>} Coordinates.\n   * @api\n   * @override\n   */\n  getCoordinates() {\n    return inflateCoordinates(\n      this.flatCoordinates,\n      0,\n      this.flatCoordinates.length,\n      this.stride,\n    );\n  }\n\n  /**\n   * Return the point at the specified index.\n   * @param {number} index Index.\n   * @return {Point} Point.\n   * @api\n   */\n  getPoint(index) {\n    const n = this.flatCoordinates.length / this.stride;\n    if (index < 0 || n <= index) {\n      return null;\n    }\n    return new Point(\n      this.flatCoordinates.slice(\n        index * this.stride,\n        (index + 1) * this.stride,\n      ),\n      this.layout,\n    );\n  }\n\n  /**\n   * Return the points of this multipoint.\n   * @return {Array<Point>} Points.\n   * @api\n   */\n  getPoints() {\n    const flatCoordinates = this.flatCoordinates;\n    const layout = this.layout;\n    const stride = this.stride;\n    /** @type {Array<Point>} */\n    const points = [];\n    for (let i = 0, ii = flatCoordinates.length; i < ii; i += stride) {\n      const point = new Point(flatCoordinates.slice(i, i + stride), layout);\n      points.push(point);\n    }\n    return points;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'MultiPoint';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    const flatCoordinates = this.flatCoordinates;\n    const stride = this.stride;\n    for (let i = 0, ii = flatCoordinates.length; i < ii; i += stride) {\n      const x = flatCoordinates[i];\n      const y = flatCoordinates[i + 1];\n      if (containsXY(extent, x, y)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Set the coordinates of the multipoint.\n   * @param {!Array<import(\"../coordinate.js\").Coordinate>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   * @override\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 1);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    this.flatCoordinates.length = deflateCoordinates(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n    );\n    this.changed();\n  }\n}\n\nexport default MultiPoint;\n", "/**\n * @module ol/geom/Polygon\n */\nimport {extend} from '../array.js';\nimport {closestSquaredDistanceXY, getCenter, isEmpty} from '../extent.js';\nimport {modulo} from '../math.js';\nimport {offset as sphereOffset} from '../sphere.js';\nimport LinearRing from './LinearRing.js';\nimport Point from './Point.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {linearRings as linearRingsArea} from './flat/area.js';\nimport {arrayMaxSquaredDelta, assignClosestArrayPoint} from './flat/closest.js';\nimport {linearRingsContainsXY} from './flat/contains.js';\nimport {deflateCoordinatesArray} from './flat/deflate.js';\nimport {inflateCoordinatesArray} from './flat/inflate.js';\nimport {getInteriorPointOfArray} from './flat/interiorpoint.js';\nimport {intersectsLinearRingArray} from './flat/intersectsextent.js';\nimport {linearRingsAreOriented, orientLinearRings} from './flat/orient.js';\nimport {quantizeArray} from './flat/simplify.js';\n\n/**\n * @classdesc\n * Polygon geometry.\n *\n * @api\n */\nclass Polygon extends SimpleGeometry {\n  /**\n   * @param {!Array<Array<import(\"../coordinate.js\").Coordinate>>|!Array<number>} coordinates\n   *     Array of linear rings that define the polygon. The first linear ring of the\n   *     array defines the outer-boundary or surface of the polygon. Each subsequent\n   *     linear ring defines a hole in the surface of the polygon. A linear ring is\n   *     an array of vertices' coordinates where the first coordinate and the last are\n   *     equivalent. (For internal use, flat coordinates in combination with\n   *     `layout` and `ends` are also accepted.)\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @param {Array<number>} [ends] Ends (for internal use with flat coordinates).\n   */\n  constructor(coordinates, layout, ends) {\n    super();\n\n    /**\n     * @type {Array<number>}\n     * @private\n     */\n    this.ends_ = [];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.flatInteriorPointRevision_ = -1;\n\n    /**\n     * @private\n     * @type {import(\"../coordinate.js\").Coordinate|null}\n     */\n    this.flatInteriorPoint_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.orientedRevision_ = -1;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.orientedFlatCoordinates_ = null;\n\n    if (layout !== undefined && ends) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates),\n      );\n      this.ends_ = ends;\n    } else {\n      this.setCoordinates(\n        /** @type {Array<Array<import(\"../coordinate.js\").Coordinate>>} */ (\n          coordinates\n        ),\n        layout,\n      );\n    }\n  }\n\n  /**\n   * Append the passed linear ring to this polygon.\n   * @param {LinearRing} linearRing Linear ring.\n   * @api\n   */\n  appendLinearRing(linearRing) {\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = linearRing.getFlatCoordinates().slice();\n    } else {\n      extend(this.flatCoordinates, linearRing.getFlatCoordinates());\n    }\n    this.ends_.push(this.flatCoordinates.length);\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!Polygon} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const polygon = new Polygon(\n      this.flatCoordinates.slice(),\n      this.layout,\n      this.ends_.slice(),\n    );\n    polygon.applyProperties(this);\n    return polygon;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        arrayMaxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.ends_,\n          this.stride,\n          0,\n        ),\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestArrayPoint(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      this.maxDelta_,\n      true,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n    );\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   * @override\n   */\n  containsXY(x, y) {\n    return linearRingsContainsXY(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.ends_,\n      this.stride,\n      x,\n      y,\n    );\n  }\n\n  /**\n   * Return the area of the polygon on projected plane.\n   * @return {number} Area (on projected plane).\n   * @api\n   */\n  getArea() {\n    return linearRingsArea(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.ends_,\n      this.stride,\n    );\n  }\n\n  /**\n   * Get the coordinate array for this geometry.  This array has the structure\n   * of a GeoJSON coordinate array for polygons.\n   *\n   * @param {boolean} [right] Orient coordinates according to the right-hand\n   *     rule (counter-clockwise for exterior and clockwise for interior rings).\n   *     If `false`, coordinates will be oriented according to the left-hand rule\n   *     (clockwise for exterior and counter-clockwise for interior rings).\n   *     By default, coordinate orientation will depend on how the geometry was\n   *     constructed.\n   * @return {Array<Array<import(\"../coordinate.js\").Coordinate>>} Coordinates.\n   * @api\n   * @override\n   */\n  getCoordinates(right) {\n    let flatCoordinates;\n    if (right !== undefined) {\n      flatCoordinates = this.getOrientedFlatCoordinates().slice();\n      orientLinearRings(flatCoordinates, 0, this.ends_, this.stride, right);\n    } else {\n      flatCoordinates = this.flatCoordinates;\n    }\n\n    return inflateCoordinatesArray(flatCoordinates, 0, this.ends_, this.stride);\n  }\n\n  /**\n   * @return {Array<number>} Ends.\n   */\n  getEnds() {\n    return this.ends_;\n  }\n\n  /**\n   * @return {Array<number>} Interior point.\n   */\n  getFlatInteriorPoint() {\n    if (this.flatInteriorPointRevision_ != this.getRevision()) {\n      const flatCenter = getCenter(this.getExtent());\n      this.flatInteriorPoint_ = getInteriorPointOfArray(\n        this.getOrientedFlatCoordinates(),\n        0,\n        this.ends_,\n        this.stride,\n        flatCenter,\n        0,\n      );\n      this.flatInteriorPointRevision_ = this.getRevision();\n    }\n    return /** @type {import(\"../coordinate.js\").Coordinate} */ (\n      this.flatInteriorPoint_\n    );\n  }\n\n  /**\n   * Return an interior point of the polygon.\n   * @return {Point} Interior point as XYM coordinate, where M is the\n   * length of the horizontal intersection that the point belongs to.\n   * @api\n   */\n  getInteriorPoint() {\n    return new Point(this.getFlatInteriorPoint(), 'XYM');\n  }\n\n  /**\n   * Return the number of rings of the polygon,  this includes the exterior\n   * ring and any interior rings.\n   *\n   * @return {number} Number of rings.\n   * @api\n   */\n  getLinearRingCount() {\n    return this.ends_.length;\n  }\n\n  /**\n   * Return the Nth linear ring of the polygon geometry. Return `null` if the\n   * given index is out of range.\n   * The exterior linear ring is available at index `0` and the interior rings\n   * at index `1` and beyond.\n   *\n   * @param {number} index Index.\n   * @return {LinearRing|null} Linear ring.\n   * @api\n   */\n  getLinearRing(index) {\n    if (index < 0 || this.ends_.length <= index) {\n      return null;\n    }\n    return new LinearRing(\n      this.flatCoordinates.slice(\n        index === 0 ? 0 : this.ends_[index - 1],\n        this.ends_[index],\n      ),\n      this.layout,\n    );\n  }\n\n  /**\n   * Return the linear rings of the polygon.\n   * @return {Array<LinearRing>} Linear rings.\n   * @api\n   */\n  getLinearRings() {\n    const layout = this.layout;\n    const flatCoordinates = this.flatCoordinates;\n    const ends = this.ends_;\n    const linearRings = [];\n    let offset = 0;\n    for (let i = 0, ii = ends.length; i < ii; ++i) {\n      const end = ends[i];\n      const linearRing = new LinearRing(\n        flatCoordinates.slice(offset, end),\n        layout,\n      );\n      linearRings.push(linearRing);\n      offset = end;\n    }\n    return linearRings;\n  }\n\n  /**\n   * @return {Array<number>} Oriented flat coordinates.\n   */\n  getOrientedFlatCoordinates() {\n    if (this.orientedRevision_ != this.getRevision()) {\n      const flatCoordinates = this.flatCoordinates;\n      if (linearRingsAreOriented(flatCoordinates, 0, this.ends_, this.stride)) {\n        this.orientedFlatCoordinates_ = flatCoordinates;\n      } else {\n        this.orientedFlatCoordinates_ = flatCoordinates.slice();\n        this.orientedFlatCoordinates_.length = orientLinearRings(\n          this.orientedFlatCoordinates_,\n          0,\n          this.ends_,\n          this.stride,\n        );\n      }\n      this.orientedRevision_ = this.getRevision();\n    }\n    return /** @type {Array<number>} */ (this.orientedFlatCoordinates_);\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {Polygon} Simplified Polygon.\n   * @protected\n   * @override\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    /** @type {Array<number>} */\n    const simplifiedFlatCoordinates = [];\n    /** @type {Array<number>} */\n    const simplifiedEnds = [];\n    simplifiedFlatCoordinates.length = quantizeArray(\n      this.flatCoordinates,\n      0,\n      this.ends_,\n      this.stride,\n      Math.sqrt(squaredTolerance),\n      simplifiedFlatCoordinates,\n      0,\n      simplifiedEnds,\n    );\n    return new Polygon(simplifiedFlatCoordinates, 'XY', simplifiedEnds);\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'Polygon';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    return intersectsLinearRingArray(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.ends_,\n      this.stride,\n      extent,\n    );\n  }\n\n  /**\n   * Set the coordinates of the polygon.\n   * @param {!Array<Array<import(\"../coordinate.js\").Coordinate>>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   * @override\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 2);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    const ends = deflateCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n      this.ends_,\n    );\n    this.flatCoordinates.length = ends.length === 0 ? 0 : ends[ends.length - 1];\n    this.changed();\n  }\n}\n\nexport default Polygon;\n\n/**\n * Create an approximation of a circle on the surface of a sphere.\n * @param {import(\"../coordinate.js\").Coordinate} center Center (`[lon, lat]` in degrees).\n * @param {number} radius The great-circle distance from the center to\n *     the polygon vertices in meters.\n * @param {number} [n] Optional number of vertices for the resulting\n *     polygon. Default is `32`.\n * @param {number} [sphereRadius] Optional radius for the sphere (defaults to\n *     the Earth's mean radius using the WGS84 ellipsoid).\n * @return {Polygon} The \"circular\" polygon.\n * @api\n */\nexport function circular(center, radius, n, sphereRadius) {\n  n = n ? n : 32;\n  /** @type {Array<number>} */\n  const flatCoordinates = [];\n  for (let i = 0; i < n; ++i) {\n    extend(\n      flatCoordinates,\n      sphereOffset(center, radius, (2 * Math.PI * i) / n, sphereRadius),\n    );\n  }\n  flatCoordinates.push(flatCoordinates[0], flatCoordinates[1]);\n  return new Polygon(flatCoordinates, 'XY', [flatCoordinates.length]);\n}\n\n/**\n * Create a polygon from an extent. The layout used is `XY`.\n * @param {import(\"../extent.js\").Extent} extent The extent.\n * @return {Polygon} The polygon.\n * @api\n */\nexport function fromExtent(extent) {\n  if (isEmpty(extent)) {\n    throw new Error('Cannot create polygon from empty extent');\n  }\n  const minX = extent[0];\n  const minY = extent[1];\n  const maxX = extent[2];\n  const maxY = extent[3];\n  const flatCoordinates = [\n    minX,\n    minY,\n    minX,\n    maxY,\n    maxX,\n    maxY,\n    maxX,\n    minY,\n    minX,\n    minY,\n  ];\n  return new Polygon(flatCoordinates, 'XY', [flatCoordinates.length]);\n}\n\n/**\n * Create a regular polygon from a circle.\n * @param {import(\"./Circle.js\").default} circle Circle geometry.\n * @param {number} [sides] Number of sides of the polygon. Default is 32.\n * @param {number} [angle] Start angle for the first vertex of the polygon in\n *     counter-clockwise radians. 0 means East. Default is 0.\n * @return {Polygon} Polygon geometry.\n * @api\n */\nexport function fromCircle(circle, sides, angle) {\n  sides = sides ? sides : 32;\n  const stride = circle.getStride();\n  const layout = circle.getLayout();\n  const center = circle.getCenter();\n  const arrayLength = stride * (sides + 1);\n  const flatCoordinates = new Array(arrayLength);\n  for (let i = 0; i < arrayLength; i += stride) {\n    flatCoordinates[i] = 0;\n    flatCoordinates[i + 1] = 0;\n    for (let j = 2; j < stride; j++) {\n      flatCoordinates[i + j] = center[j];\n    }\n  }\n  const ends = [flatCoordinates.length];\n  const polygon = new Polygon(flatCoordinates, layout, ends);\n  makeRegular(polygon, center, circle.getRadius(), angle);\n  return polygon;\n}\n\n/**\n * Modify the coordinates of a polygon to make it a regular polygon.\n * @param {Polygon} polygon Polygon geometry.\n * @param {import(\"../coordinate.js\").Coordinate} center Center of the regular polygon.\n * @param {number} radius Radius of the regular polygon.\n * @param {number} [angle] Start angle for the first vertex of the polygon in\n *     counter-clockwise radians. 0 means East. Default is 0.\n */\nexport function makeRegular(polygon, center, radius, angle) {\n  const flatCoordinates = polygon.getFlatCoordinates();\n  const stride = polygon.getStride();\n  const sides = flatCoordinates.length / stride - 1;\n  const startAngle = angle ? angle : 0;\n  for (let i = 0; i <= sides; ++i) {\n    const offset = i * stride;\n    const angle = startAngle + (modulo(i, sides) * 2 * Math.PI) / sides;\n    flatCoordinates[offset] = center[0] + radius * Math.cos(angle);\n    flatCoordinates[offset + 1] = center[1] + radius * Math.sin(angle);\n  }\n  polygon.changed();\n}\n", "/**\n * @module ol/geom/MultiPolygon\n */\nimport {extend} from '../array.js';\nimport {closestSquaredDistanceXY} from '../extent.js';\nimport MultiPoint from './MultiPoint.js';\nimport Polygon from './Polygon.js';\nimport SimpleGeometry from './SimpleGeometry.js';\nimport {linearRingss as linearRingssArea} from './flat/area.js';\nimport {linearRingss as linearRingssCenter} from './flat/center.js';\nimport {\n  assignClosestMultiArrayPoint,\n  multiArrayMaxSquaredDelta,\n} from './flat/closest.js';\nimport {linearRingssContainsXY} from './flat/contains.js';\nimport {deflateMultiCoordinatesArray} from './flat/deflate.js';\nimport {inflateMultiCoordinatesArray} from './flat/inflate.js';\nimport {getInteriorPointsOfMultiArray} from './flat/interiorpoint.js';\nimport {intersectsLinearRingMultiArray} from './flat/intersectsextent.js';\nimport {\n  linearRingssAreOriented,\n  orientLinearRingsArray,\n} from './flat/orient.js';\nimport {quantizeMultiArray} from './flat/simplify.js';\n\n/**\n * @classdesc\n * Multi-polygon geometry.\n *\n * @api\n */\nclass MultiPolygon extends SimpleGeometry {\n  /**\n   * @param {Array<Array<Array<import(\"../coordinate.js\").Coordinate>>|Polygon>|Array<number>} coordinates Coordinates.\n   *     For internal use, flat coordinates in combination with `layout` and `endss` are also accepted.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @param {Array<Array<number>>} [endss] Array of ends for internal use with flat coordinates.\n   */\n  constructor(coordinates, layout, endss) {\n    super();\n\n    /**\n     * @type {Array<Array<number>>}\n     * @private\n     */\n    this.endss_ = [];\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.flatInteriorPointsRevision_ = -1;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.flatInteriorPoints_ = null;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDelta_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.maxDeltaRevision_ = -1;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.orientedRevision_ = -1;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.orientedFlatCoordinates_ = null;\n\n    if (!endss && !Array.isArray(coordinates[0])) {\n      const polygons = /** @type {Array<Polygon>} */ (coordinates);\n      /** @type {Array<number>} */\n      const flatCoordinates = [];\n      const thisEndss = [];\n      for (let i = 0, ii = polygons.length; i < ii; ++i) {\n        const polygon = polygons[i];\n        const offset = flatCoordinates.length;\n        const ends = polygon.getEnds();\n        for (let j = 0, jj = ends.length; j < jj; ++j) {\n          ends[j] += offset;\n        }\n        extend(flatCoordinates, polygon.getFlatCoordinates());\n        thisEndss.push(ends);\n      }\n      layout =\n        polygons.length === 0 ? this.getLayout() : polygons[0].getLayout();\n      coordinates = flatCoordinates;\n      endss = thisEndss;\n    }\n    if (layout !== undefined && endss) {\n      this.setFlatCoordinates(\n        layout,\n        /** @type {Array<number>} */ (coordinates),\n      );\n      this.endss_ = endss;\n    } else {\n      this.setCoordinates(\n        /** @type {Array<Array<Array<import(\"../coordinate.js\").Coordinate>>>} */ (\n          coordinates\n        ),\n        layout,\n      );\n    }\n  }\n\n  /**\n   * Append the passed polygon to this multipolygon.\n   * @param {Polygon} polygon Polygon.\n   * @api\n   */\n  appendPolygon(polygon) {\n    /** @type {Array<number>} */\n    let ends;\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = polygon.getFlatCoordinates().slice();\n      ends = polygon.getEnds().slice();\n      this.endss_.push();\n    } else {\n      const offset = this.flatCoordinates.length;\n      extend(this.flatCoordinates, polygon.getFlatCoordinates());\n      ends = polygon.getEnds().slice();\n      for (let i = 0, ii = ends.length; i < ii; ++i) {\n        ends[i] += offset;\n      }\n    }\n    this.endss_.push(ends);\n    this.changed();\n  }\n\n  /**\n   * Make a complete copy of the geometry.\n   * @return {!MultiPolygon} Clone.\n   * @api\n   * @override\n   */\n  clone() {\n    const len = this.endss_.length;\n    const newEndss = new Array(len);\n    for (let i = 0; i < len; ++i) {\n      newEndss[i] = this.endss_[i].slice();\n    }\n\n    const multiPolygon = new MultiPolygon(\n      this.flatCoordinates.slice(),\n      this.layout,\n      newEndss,\n    );\n    multiPolygon.applyProperties(this);\n\n    return multiPolygon;\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @param {import(\"../coordinate.js\").Coordinate} closestPoint Closest point.\n   * @param {number} minSquaredDistance Minimum squared distance.\n   * @return {number} Minimum squared distance.\n   * @override\n   */\n  closestPointXY(x, y, closestPoint, minSquaredDistance) {\n    if (minSquaredDistance < closestSquaredDistanceXY(this.getExtent(), x, y)) {\n      return minSquaredDistance;\n    }\n    if (this.maxDeltaRevision_ != this.getRevision()) {\n      this.maxDelta_ = Math.sqrt(\n        multiArrayMaxSquaredDelta(\n          this.flatCoordinates,\n          0,\n          this.endss_,\n          this.stride,\n          0,\n        ),\n      );\n      this.maxDeltaRevision_ = this.getRevision();\n    }\n    return assignClosestMultiArrayPoint(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride,\n      this.maxDelta_,\n      true,\n      x,\n      y,\n      closestPoint,\n      minSquaredDistance,\n    );\n  }\n\n  /**\n   * @param {number} x X.\n   * @param {number} y Y.\n   * @return {boolean} Contains (x, y).\n   * @override\n   */\n  containsXY(x, y) {\n    return linearRingssContainsXY(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride,\n      x,\n      y,\n    );\n  }\n\n  /**\n   * Return the area of the multipolygon on projected plane.\n   * @return {number} Area (on projected plane).\n   * @api\n   */\n  getArea() {\n    return linearRingssArea(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride,\n    );\n  }\n\n  /**\n   * Get the coordinate array for this geometry.  This array has the structure\n   * of a GeoJSON coordinate array for multi-polygons.\n   *\n   * @param {boolean} [right] Orient coordinates according to the right-hand\n   *     rule (counter-clockwise for exterior and clockwise for interior rings).\n   *     If `false`, coordinates will be oriented according to the left-hand rule\n   *     (clockwise for exterior and counter-clockwise for interior rings).\n   *     By default, coordinate orientation will depend on how the geometry was\n   *     constructed.\n   * @return {Array<Array<Array<import(\"../coordinate.js\").Coordinate>>>} Coordinates.\n   * @api\n   * @override\n   */\n  getCoordinates(right) {\n    let flatCoordinates;\n    if (right !== undefined) {\n      flatCoordinates = this.getOrientedFlatCoordinates().slice();\n      orientLinearRingsArray(\n        flatCoordinates,\n        0,\n        this.endss_,\n        this.stride,\n        right,\n      );\n    } else {\n      flatCoordinates = this.flatCoordinates;\n    }\n\n    return inflateMultiCoordinatesArray(\n      flatCoordinates,\n      0,\n      this.endss_,\n      this.stride,\n    );\n  }\n\n  /**\n   * @return {Array<Array<number>>} Endss.\n   */\n  getEndss() {\n    return this.endss_;\n  }\n\n  /**\n   * @return {Array<number>} Flat interior points.\n   */\n  getFlatInteriorPoints() {\n    if (this.flatInteriorPointsRevision_ != this.getRevision()) {\n      const flatCenters = linearRingssCenter(\n        this.flatCoordinates,\n        0,\n        this.endss_,\n        this.stride,\n      );\n      this.flatInteriorPoints_ = getInteriorPointsOfMultiArray(\n        this.getOrientedFlatCoordinates(),\n        0,\n        this.endss_,\n        this.stride,\n        flatCenters,\n      );\n      this.flatInteriorPointsRevision_ = this.getRevision();\n    }\n    return /** @type {Array<number>} */ (this.flatInteriorPoints_);\n  }\n\n  /**\n   * Return the interior points as {@link module:ol/geom/MultiPoint~MultiPoint multipoint}.\n   * @return {MultiPoint} Interior points as XYM coordinates, where M is\n   * the length of the horizontal intersection that the point belongs to.\n   * @api\n   */\n  getInteriorPoints() {\n    return new MultiPoint(this.getFlatInteriorPoints().slice(), 'XYM');\n  }\n\n  /**\n   * @return {Array<number>} Oriented flat coordinates.\n   */\n  getOrientedFlatCoordinates() {\n    if (this.orientedRevision_ != this.getRevision()) {\n      const flatCoordinates = this.flatCoordinates;\n      if (\n        linearRingssAreOriented(flatCoordinates, 0, this.endss_, this.stride)\n      ) {\n        this.orientedFlatCoordinates_ = flatCoordinates;\n      } else {\n        this.orientedFlatCoordinates_ = flatCoordinates.slice();\n        this.orientedFlatCoordinates_.length = orientLinearRingsArray(\n          this.orientedFlatCoordinates_,\n          0,\n          this.endss_,\n          this.stride,\n        );\n      }\n      this.orientedRevision_ = this.getRevision();\n    }\n    return /** @type {Array<number>} */ (this.orientedFlatCoordinates_);\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {MultiPolygon} Simplified MultiPolygon.\n   * @protected\n   * @override\n   */\n  getSimplifiedGeometryInternal(squaredTolerance) {\n    /** @type {Array<number>} */\n    const simplifiedFlatCoordinates = [];\n    /** @type {Array<Array<number>>} */\n    const simplifiedEndss = [];\n    simplifiedFlatCoordinates.length = quantizeMultiArray(\n      this.flatCoordinates,\n      0,\n      this.endss_,\n      this.stride,\n      Math.sqrt(squaredTolerance),\n      simplifiedFlatCoordinates,\n      0,\n      simplifiedEndss,\n    );\n    return new MultiPolygon(simplifiedFlatCoordinates, 'XY', simplifiedEndss);\n  }\n\n  /**\n   * Return the polygon at the specified index.\n   * @param {number} index Index.\n   * @return {Polygon} Polygon.\n   * @api\n   */\n  getPolygon(index) {\n    if (index < 0 || this.endss_.length <= index) {\n      return null;\n    }\n    let offset;\n    if (index === 0) {\n      offset = 0;\n    } else {\n      const prevEnds = this.endss_[index - 1];\n      offset = prevEnds[prevEnds.length - 1];\n    }\n    const ends = this.endss_[index].slice();\n    const end = ends[ends.length - 1];\n    if (offset !== 0) {\n      for (let i = 0, ii = ends.length; i < ii; ++i) {\n        ends[i] -= offset;\n      }\n    }\n    return new Polygon(\n      this.flatCoordinates.slice(offset, end),\n      this.layout,\n      ends,\n    );\n  }\n\n  /**\n   * Return the polygons of this multipolygon.\n   * @return {Array<Polygon>} Polygons.\n   * @api\n   */\n  getPolygons() {\n    const layout = this.layout;\n    const flatCoordinates = this.flatCoordinates;\n    const endss = this.endss_;\n    const polygons = [];\n    let offset = 0;\n    for (let i = 0, ii = endss.length; i < ii; ++i) {\n      const ends = endss[i].slice();\n      const end = ends[ends.length - 1];\n      if (offset !== 0) {\n        for (let j = 0, jj = ends.length; j < jj; ++j) {\n          ends[j] -= offset;\n        }\n      }\n      const polygon = new Polygon(\n        flatCoordinates.slice(offset, end),\n        layout,\n        ends,\n      );\n      polygons.push(polygon);\n      offset = end;\n    }\n    return polygons;\n  }\n\n  /**\n   * Get the type of this geometry.\n   * @return {import(\"./Geometry.js\").Type} Geometry type.\n   * @api\n   * @override\n   */\n  getType() {\n    return 'MultiPolygon';\n  }\n\n  /**\n   * Test if the geometry and the passed extent intersect.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {boolean} `true` if the geometry and the extent intersect.\n   * @api\n   * @override\n   */\n  intersectsExtent(extent) {\n    return intersectsLinearRingMultiArray(\n      this.getOrientedFlatCoordinates(),\n      0,\n      this.endss_,\n      this.stride,\n      extent,\n    );\n  }\n\n  /**\n   * Set the coordinates of the multipolygon.\n   * @param {!Array<Array<Array<import(\"../coordinate.js\").Coordinate>>>} coordinates Coordinates.\n   * @param {import(\"./Geometry.js\").GeometryLayout} [layout] Layout.\n   * @api\n   * @override\n   */\n  setCoordinates(coordinates, layout) {\n    this.setLayout(layout, coordinates, 3);\n    if (!this.flatCoordinates) {\n      this.flatCoordinates = [];\n    }\n    const endss = deflateMultiCoordinatesArray(\n      this.flatCoordinates,\n      0,\n      coordinates,\n      this.stride,\n      this.endss_,\n    );\n    if (endss.length === 0) {\n      this.flatCoordinates.length = 0;\n    } else {\n      const lastEnds = endss[endss.length - 1];\n      this.flatCoordinates.length =\n        lastEnds.length === 0 ? 0 : lastEnds[lastEnds.length - 1];\n    }\n    this.changed();\n  }\n}\n\nexport default MultiPolygon;\n", "/**\n * @module ol/render/Feature\n */\nimport Feature from '../Feature.js';\nimport {extend} from '../array.js';\nimport {\n  createOrUpdateFromCoordinate,\n  createOrUpdateFromFlatCoordinates,\n  getCenter,\n  getHeight,\n} from '../extent.js';\nimport {memoizeOne} from '../functions.js';\nimport {linearRingss as linearRingssCenter} from '../geom/flat/center.js';\nimport {\n  getInteriorPointOfArray,\n  getInteriorPointsOfMultiArray,\n} from '../geom/flat/interiorpoint.js';\nimport {interpolatePoint} from '../geom/flat/interpolate.js';\nimport {inflateEnds} from '../geom/flat/orient.js';\nimport {\n  douglasPeucker,\n  douglasPeuckerArray,\n  quantizeArray,\n} from '../geom/flat/simplify.js';\nimport {transform2D} from '../geom/flat/transform.js';\nimport {\n  LineString,\n  MultiLineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from '../geom.js';\nimport {get as getProjection} from '../proj.js';\nimport {\n  compose as composeTransform,\n  create as createTransform,\n} from '../transform.js';\n\n/**\n * @typedef {'Point' | 'LineString' | 'LinearRing' | 'Polygon' | 'MultiPoint' | 'MultiLineString'} Type\n * The geometry type.  One of `'Point'`, `'LineString'`, `'LinearRing'`,\n * `'Polygon'`, `'MultiPoint'` or 'MultiLineString'`.\n */\n\n/**\n * @type {import(\"../transform.js\").Transform}\n */\nconst tmpTransform = createTransform();\n\n/**\n * Lightweight, read-only, {@link module:ol/Feature~Feature} and {@link module:ol/geom/Geometry~Geometry} like\n * structure, optimized for vector tile rendering and styling. Geometry access\n * through the API is limited to getting the type and extent of the geometry.\n */\nclass RenderFeature {\n  /**\n   * @param {Type} type Geometry type.\n   * @param {Array<number>} flatCoordinates Flat coordinates. These always need\n   *     to be right-handed for polygons.\n   * @param {Array<number>} ends Ends.\n   * @param {number} stride Stride.\n   * @param {Object<string, *>} properties Properties.\n   * @param {number|string|undefined} id Feature id.\n   */\n  constructor(type, flatCoordinates, ends, stride, properties, id) {\n    /**\n     * @type {import(\"../style/Style.js\").StyleFunction|undefined}\n     */\n    this.styleFunction;\n\n    /**\n     * @private\n     * @type {import(\"../extent.js\").Extent|undefined}\n     */\n    this.extent_;\n\n    /**\n     * @private\n     * @type {number|string|undefined}\n     */\n    this.id_ = id;\n\n    /**\n     * @private\n     * @type {Type}\n     */\n    this.type_ = type;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.flatCoordinates_ = flatCoordinates;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.flatInteriorPoints_ = null;\n\n    /**\n     * @private\n     * @type {Array<number>}\n     */\n    this.flatMidpoints_ = null;\n\n    /**\n     * @private\n     * @type {Array<number>|null}\n     */\n    this.ends_ = ends || null;\n\n    /**\n     * @private\n     * @type {Object<string, *>}\n     */\n    this.properties_ = properties;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.squaredTolerance_;\n\n    /**\n     * @private\n     * @type {number}\n     */\n    this.stride_ = stride;\n\n    /**\n     * @private\n     * @type {RenderFeature}\n     */\n    this.simplifiedGeometry_;\n  }\n\n  /**\n   * Get a feature property by its key.\n   * @param {string} key Key\n   * @return {*} Value for the requested key.\n   * @api\n   */\n  get(key) {\n    return this.properties_[key];\n  }\n\n  /**\n   * Get the extent of this feature's geometry.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getExtent() {\n    if (!this.extent_) {\n      this.extent_ =\n        this.type_ === 'Point'\n          ? createOrUpdateFromCoordinate(this.flatCoordinates_)\n          : createOrUpdateFromFlatCoordinates(\n              this.flatCoordinates_,\n              0,\n              this.flatCoordinates_.length,\n              2,\n            );\n    }\n    return this.extent_;\n  }\n\n  /**\n   * @return {Array<number>} Flat interior points.\n   */\n  getFlatInteriorPoint() {\n    if (!this.flatInteriorPoints_) {\n      const flatCenter = getCenter(this.getExtent());\n      this.flatInteriorPoints_ = getInteriorPointOfArray(\n        this.flatCoordinates_,\n        0,\n        this.ends_,\n        2,\n        flatCenter,\n        0,\n      );\n    }\n    return this.flatInteriorPoints_;\n  }\n\n  /**\n   * @return {Array<number>} Flat interior points.\n   */\n  getFlatInteriorPoints() {\n    if (!this.flatInteriorPoints_) {\n      const ends = inflateEnds(this.flatCoordinates_, this.ends_);\n      const flatCenters = linearRingssCenter(this.flatCoordinates_, 0, ends, 2);\n      this.flatInteriorPoints_ = getInteriorPointsOfMultiArray(\n        this.flatCoordinates_,\n        0,\n        ends,\n        2,\n        flatCenters,\n      );\n    }\n    return this.flatInteriorPoints_;\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoint.\n   */\n  getFlatMidpoint() {\n    if (!this.flatMidpoints_) {\n      this.flatMidpoints_ = interpolatePoint(\n        this.flatCoordinates_,\n        0,\n        this.flatCoordinates_.length,\n        2,\n        0.5,\n      );\n    }\n    return this.flatMidpoints_;\n  }\n\n  /**\n   * @return {Array<number>} Flat midpoints.\n   */\n  getFlatMidpoints() {\n    if (!this.flatMidpoints_) {\n      this.flatMidpoints_ = [];\n      const flatCoordinates = this.flatCoordinates_;\n      let offset = 0;\n      const ends = /** @type {Array<number>} */ (this.ends_);\n      for (let i = 0, ii = ends.length; i < ii; ++i) {\n        const end = ends[i];\n        const midpoint = interpolatePoint(flatCoordinates, offset, end, 2, 0.5);\n        extend(this.flatMidpoints_, midpoint);\n        offset = end;\n      }\n    }\n    return this.flatMidpoints_;\n  }\n\n  /**\n   * Get the feature identifier.  This is a stable identifier for the feature and\n   * is set when reading data from a remote source.\n   * @return {number|string|undefined} Id.\n   * @api\n   */\n  getId() {\n    return this.id_;\n  }\n\n  /**\n   * @return {Array<number>} Flat coordinates.\n   */\n  getOrientedFlatCoordinates() {\n    return this.flatCoordinates_;\n  }\n\n  /**\n   * For API compatibility with {@link module:ol/Feature~Feature}, this method is useful when\n   * determining the geometry type in style function (see {@link #getType}).\n   * @return {RenderFeature} Feature.\n   * @api\n   */\n  getGeometry() {\n    return this;\n  }\n\n  /**\n   * @param {number} squaredTolerance Squared tolerance.\n   * @return {RenderFeature} Simplified geometry.\n   */\n  getSimplifiedGeometry(squaredTolerance) {\n    return this;\n  }\n\n  /**\n   * Get a transformed and simplified version of the geometry.\n   * @param {number} squaredTolerance Squared tolerance.\n   * @param {import(\"../proj.js\").TransformFunction} [transform] Optional transform function.\n   * @return {RenderFeature} Simplified geometry.\n   */\n  simplifyTransformed(squaredTolerance, transform) {\n    return this;\n  }\n\n  /**\n   * Get the feature properties.\n   * @return {Object<string, *>} Feature properties.\n   * @api\n   */\n  getProperties() {\n    return this.properties_;\n  }\n\n  /**\n   * Get an object of all property names and values.  This has the same behavior as getProperties,\n   * but is here to conform with the {@link module:ol/Feature~Feature} interface.\n   * @return {Object<string, *>?} Object.\n   */\n  getPropertiesInternal() {\n    return this.properties_;\n  }\n\n  /**\n   * @return {number} Stride.\n   */\n  getStride() {\n    return this.stride_;\n  }\n\n  /**\n   * @return {import('../style/Style.js').StyleFunction|undefined} Style\n   */\n  getStyleFunction() {\n    return this.styleFunction;\n  }\n\n  /**\n   * Get the type of this feature's geometry.\n   * @return {Type} Geometry type.\n   * @api\n   */\n  getType() {\n    return this.type_;\n  }\n\n  /**\n   * Transform geometry coordinates from tile pixel space to projected.\n   *\n   * @param {import(\"../proj.js\").ProjectionLike} projection The data projection\n   */\n  transform(projection) {\n    projection = getProjection(projection);\n    const pixelExtent = projection.getExtent();\n    const projectedExtent = projection.getWorldExtent();\n    if (pixelExtent && projectedExtent) {\n      const scale = getHeight(projectedExtent) / getHeight(pixelExtent);\n      composeTransform(\n        tmpTransform,\n        projectedExtent[0],\n        projectedExtent[3],\n        scale,\n        -scale,\n        0,\n        0,\n        0,\n      );\n      transform2D(\n        this.flatCoordinates_,\n        0,\n        this.flatCoordinates_.length,\n        2,\n        tmpTransform,\n        this.flatCoordinates_,\n      );\n    }\n  }\n\n  /**\n   * Apply a transform function to the coordinates of the geometry.\n   * The geometry is modified in place.\n   * If you do not want the geometry modified in place, first `clone()` it and\n   * then use this function on the clone.\n   * @param {import(\"../proj.js\").TransformFunction} transformFn Transform function.\n   */\n  applyTransform(transformFn) {\n    transformFn(this.flatCoordinates_, this.flatCoordinates_, this.stride_);\n  }\n\n  /**\n   * @return {RenderFeature} A cloned render feature.\n   */\n  clone() {\n    return new RenderFeature(\n      this.type_,\n      this.flatCoordinates_.slice(),\n      this.ends_?.slice(),\n      this.stride_,\n      Object.assign({}, this.properties_),\n      this.id_,\n    );\n  }\n\n  /**\n   * @return {Array<number>|null} Ends.\n   */\n  getEnds() {\n    return this.ends_;\n  }\n\n  /**\n   * Add transform and resolution based geometry simplification to this instance.\n   * @return {RenderFeature} This render feature.\n   */\n  enableSimplifyTransformed() {\n    this.simplifyTransformed = memoizeOne((squaredTolerance, transform) => {\n      if (squaredTolerance === this.squaredTolerance_) {\n        return this.simplifiedGeometry_;\n      }\n      this.simplifiedGeometry_ = this.clone();\n      if (transform) {\n        this.simplifiedGeometry_.applyTransform(transform);\n      }\n      const simplifiedFlatCoordinates =\n        this.simplifiedGeometry_.getFlatCoordinates();\n      let simplifiedEnds;\n      switch (this.type_) {\n        case 'LineString':\n          simplifiedFlatCoordinates.length = douglasPeucker(\n            simplifiedFlatCoordinates,\n            0,\n            this.simplifiedGeometry_.flatCoordinates_.length,\n            this.simplifiedGeometry_.stride_,\n            squaredTolerance,\n            simplifiedFlatCoordinates,\n            0,\n          );\n          simplifiedEnds = [simplifiedFlatCoordinates.length];\n          break;\n        case 'MultiLineString':\n          simplifiedEnds = [];\n          simplifiedFlatCoordinates.length = douglasPeuckerArray(\n            simplifiedFlatCoordinates,\n            0,\n            this.simplifiedGeometry_.ends_,\n            this.simplifiedGeometry_.stride_,\n            squaredTolerance,\n            simplifiedFlatCoordinates,\n            0,\n            simplifiedEnds,\n          );\n          break;\n        case 'Polygon':\n          simplifiedEnds = [];\n          simplifiedFlatCoordinates.length = quantizeArray(\n            simplifiedFlatCoordinates,\n            0,\n            this.simplifiedGeometry_.ends_,\n            this.simplifiedGeometry_.stride_,\n            Math.sqrt(squaredTolerance),\n            simplifiedFlatCoordinates,\n            0,\n            simplifiedEnds,\n          );\n          break;\n        default:\n      }\n      if (simplifiedEnds) {\n        this.simplifiedGeometry_ = new RenderFeature(\n          this.type_,\n          simplifiedFlatCoordinates,\n          simplifiedEnds,\n          2,\n          this.properties_,\n          this.id_,\n        );\n      }\n      this.squaredTolerance_ = squaredTolerance;\n      return this.simplifiedGeometry_;\n    });\n    return this;\n  }\n}\n\n/**\n * @return {Array<number>} Flat coordinates.\n */\nRenderFeature.prototype.getFlatCoordinates =\n  RenderFeature.prototype.getOrientedFlatCoordinates;\n\n/**\n * Create a geometry from an `ol/render/Feature`\n * @param {RenderFeature} renderFeature\n * Render Feature\n * @return {Point|MultiPoint|LineString|MultiLineString|Polygon|MultiPolygon}\n * New geometry instance.\n * @api\n */\nexport function toGeometry(renderFeature) {\n  const geometryType = renderFeature.getType();\n  switch (geometryType) {\n    case 'Point':\n      return new Point(renderFeature.getFlatCoordinates());\n    case 'MultiPoint':\n      return new MultiPoint(renderFeature.getFlatCoordinates(), 'XY');\n    case 'LineString':\n      return new LineString(renderFeature.getFlatCoordinates(), 'XY');\n    case 'MultiLineString':\n      return new MultiLineString(\n        renderFeature.getFlatCoordinates(),\n        'XY',\n        /** @type {Array<number>} */ (renderFeature.getEnds()),\n      );\n    case 'Polygon':\n      const flatCoordinates = renderFeature.getFlatCoordinates();\n      const ends = renderFeature.getEnds();\n      const endss = inflateEnds(flatCoordinates, ends);\n      return endss.length > 1\n        ? new MultiPolygon(flatCoordinates, 'XY', endss)\n        : new Polygon(flatCoordinates, 'XY', ends);\n    default:\n      throw new Error('Invalid geometry type:' + geometryType);\n  }\n}\n\n/**\n * Create an `ol/Feature` from an `ol/render/Feature`\n * @param {RenderFeature} renderFeature RenderFeature\n * @param {string} [geometryName] Geometry name to use\n * when creating the Feature.\n * @return {Feature} Newly constructed `ol/Feature` with properties,\n * geometry, and id copied over.\n * @api\n */\nexport function toFeature(renderFeature, geometryName) {\n  const id = renderFeature.getId();\n  const geometry = toGeometry(renderFeature);\n  const properties = renderFeature.getProperties();\n  const feature = new Feature();\n  if (geometryName !== undefined) {\n    feature.setGeometryName(geometryName);\n  }\n  feature.setGeometry(geometry);\n  if (id !== undefined) {\n    feature.setId(id);\n  }\n  feature.setProperties(properties, true);\n  return feature;\n}\n\nexport default RenderFeature;\n", "\n/**\n * Rearranges items so that all items in the [left, k] are the smallest.\n * The k-th element will have the (k - left + 1)-th smallest value in [left, right].\n *\n * @template T\n * @param {T[]} arr the array to partially sort (in place)\n * @param {number} k middle index for partial sorting (as defined above)\n * @param {number} [left=0] left index of the range to sort\n * @param {number} [right=arr.length-1] right index\n * @param {(a: T, b: T) => number} [compare = (a, b) => a - b] compare function\n */\nexport default function quickselect(arr, k, left = 0, right = arr.length - 1, compare = defaultCompare) {\n\n    while (right > left) {\n        if (right - left > 600) {\n            const n = right - left + 1;\n            const m = k - left + 1;\n            const z = Math.log(n);\n            const s = 0.5 * Math.exp(2 * z / 3);\n            const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n            const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n            const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n            quickselect(arr, k, newLeft, newRight, compare);\n        }\n\n        const t = arr[k];\n        let i = left;\n        /** @type {number} */\n        let j = right;\n\n        swap(arr, left, k);\n        if (compare(arr[right], t) > 0) swap(arr, left, right);\n\n        while (i < j) {\n            swap(arr, i, j);\n            i++;\n            j--;\n            while (compare(arr[i], t) < 0) i++;\n            while (compare(arr[j], t) > 0) j--;\n        }\n\n        if (compare(arr[left], t) === 0) swap(arr, left, j);\n        else {\n            j++;\n            swap(arr, j, right);\n        }\n\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n}\n\n/**\n * @template T\n * @param {T[]} arr\n * @param {number} i\n * @param {number} j\n */\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\n/**\n * @template T\n * @param {T} a\n * @param {T} b\n * @returns {number}\n */\nfunction defaultCompare(a, b) {\n    return a < b ? -1 : a > b ? 1 : 0;\n}\n", "import quickselect from 'quickselect';\n\nexport default class RBush {\n    constructor(maxEntries = 9) {\n        // max entries in a node is 9 by default; min node fill is 40% for best performance\n        this._maxEntries = Math.max(4, maxEntries);\n        this._minEntries = Math.max(2, Math.ceil(this._maxEntries * 0.4));\n        this.clear();\n    }\n\n    all() {\n        return this._all(this.data, []);\n    }\n\n    search(bbox) {\n        let node = this.data;\n        const result = [];\n\n        if (!intersects(bbox, node)) return result;\n\n        const toBBox = this.toBBox;\n        const nodesToSearch = [];\n\n        while (node) {\n            for (let i = 0; i < node.children.length; i++) {\n                const child = node.children[i];\n                const childBBox = node.leaf ? toBBox(child) : child;\n\n                if (intersects(bbox, childBBox)) {\n                    if (node.leaf) result.push(child);\n                    else if (contains(bbox, childBBox)) this._all(child, result);\n                    else nodesToSearch.push(child);\n                }\n            }\n            node = nodesToSearch.pop();\n        }\n\n        return result;\n    }\n\n    collides(bbox) {\n        let node = this.data;\n\n        if (!intersects(bbox, node)) return false;\n\n        const nodesToSearch = [];\n        while (node) {\n            for (let i = 0; i < node.children.length; i++) {\n                const child = node.children[i];\n                const childBBox = node.leaf ? this.toBBox(child) : child;\n\n                if (intersects(bbox, childBBox)) {\n                    if (node.leaf || contains(bbox, childBBox)) return true;\n                    nodesToSearch.push(child);\n                }\n            }\n            node = nodesToSearch.pop();\n        }\n\n        return false;\n    }\n\n    load(data) {\n        if (!(data && data.length)) return this;\n\n        if (data.length < this._minEntries) {\n            for (let i = 0; i < data.length; i++) {\n                this.insert(data[i]);\n            }\n            return this;\n        }\n\n        // recursively build the tree with the given data from scratch using OMT algorithm\n        let node = this._build(data.slice(), 0, data.length - 1, 0);\n\n        if (!this.data.children.length) {\n            // save as is if tree is empty\n            this.data = node;\n\n        } else if (this.data.height === node.height) {\n            // split root if trees have the same height\n            this._splitRoot(this.data, node);\n\n        } else {\n            if (this.data.height < node.height) {\n                // swap trees if inserted one is bigger\n                const tmpNode = this.data;\n                this.data = node;\n                node = tmpNode;\n            }\n\n            // insert the small tree into the large tree at appropriate level\n            this._insert(node, this.data.height - node.height - 1, true);\n        }\n\n        return this;\n    }\n\n    insert(item) {\n        if (item) this._insert(item, this.data.height - 1);\n        return this;\n    }\n\n    clear() {\n        this.data = createNode([]);\n        return this;\n    }\n\n    remove(item, equalsFn) {\n        if (!item) return this;\n\n        let node = this.data;\n        const bbox = this.toBBox(item);\n        const path = [];\n        const indexes = [];\n        let i, parent, goingUp;\n\n        // depth-first iterative tree traversal\n        while (node || path.length) {\n\n            if (!node) { // go up\n                node = path.pop();\n                parent = path[path.length - 1];\n                i = indexes.pop();\n                goingUp = true;\n            }\n\n            if (node.leaf) { // check current node\n                const index = findItem(item, node.children, equalsFn);\n\n                if (index !== -1) {\n                    // item found, remove the item and condense tree upwards\n                    node.children.splice(index, 1);\n                    path.push(node);\n                    this._condense(path);\n                    return this;\n                }\n            }\n\n            if (!goingUp && !node.leaf && contains(node, bbox)) { // go down\n                path.push(node);\n                indexes.push(i);\n                i = 0;\n                parent = node;\n                node = node.children[0];\n\n            } else if (parent) { // go right\n                i++;\n                node = parent.children[i];\n                goingUp = false;\n\n            } else node = null; // nothing found\n        }\n\n        return this;\n    }\n\n    toBBox(item) { return item; }\n\n    compareMinX(a, b) { return a.minX - b.minX; }\n    compareMinY(a, b) { return a.minY - b.minY; }\n\n    toJSON() { return this.data; }\n\n    fromJSON(data) {\n        this.data = data;\n        return this;\n    }\n\n    _all(node, result) {\n        const nodesToSearch = [];\n        while (node) {\n            if (node.leaf) result.push(...node.children);\n            else nodesToSearch.push(...node.children);\n\n            node = nodesToSearch.pop();\n        }\n        return result;\n    }\n\n    _build(items, left, right, height) {\n\n        const N = right - left + 1;\n        let M = this._maxEntries;\n        let node;\n\n        if (N <= M) {\n            // reached leaf level; return leaf\n            node = createNode(items.slice(left, right + 1));\n            calcBBox(node, this.toBBox);\n            return node;\n        }\n\n        if (!height) {\n            // target height of the bulk-loaded tree\n            height = Math.ceil(Math.log(N) / Math.log(M));\n\n            // target number of root entries to maximize storage utilization\n            M = Math.ceil(N / Math.pow(M, height - 1));\n        }\n\n        node = createNode([]);\n        node.leaf = false;\n        node.height = height;\n\n        // split the items into M mostly square tiles\n\n        const N2 = Math.ceil(N / M);\n        const N1 = N2 * Math.ceil(Math.sqrt(M));\n\n        multiSelect(items, left, right, N1, this.compareMinX);\n\n        for (let i = left; i <= right; i += N1) {\n\n            const right2 = Math.min(i + N1 - 1, right);\n\n            multiSelect(items, i, right2, N2, this.compareMinY);\n\n            for (let j = i; j <= right2; j += N2) {\n\n                const right3 = Math.min(j + N2 - 1, right2);\n\n                // pack each entry recursively\n                node.children.push(this._build(items, j, right3, height - 1));\n            }\n        }\n\n        calcBBox(node, this.toBBox);\n\n        return node;\n    }\n\n    _chooseSubtree(bbox, node, level, path) {\n        while (true) {\n            path.push(node);\n\n            if (node.leaf || path.length - 1 === level) break;\n\n            let minArea = Infinity;\n            let minEnlargement = Infinity;\n            let targetNode;\n\n            for (let i = 0; i < node.children.length; i++) {\n                const child = node.children[i];\n                const area = bboxArea(child);\n                const enlargement = enlargedArea(bbox, child) - area;\n\n                // choose entry with the least area enlargement\n                if (enlargement < minEnlargement) {\n                    minEnlargement = enlargement;\n                    minArea = area < minArea ? area : minArea;\n                    targetNode = child;\n\n                } else if (enlargement === minEnlargement) {\n                    // otherwise choose one with the smallest area\n                    if (area < minArea) {\n                        minArea = area;\n                        targetNode = child;\n                    }\n                }\n            }\n\n            node = targetNode || node.children[0];\n        }\n\n        return node;\n    }\n\n    _insert(item, level, isNode) {\n        const bbox = isNode ? item : this.toBBox(item);\n        const insertPath = [];\n\n        // find the best node for accommodating the item, saving all nodes along the path too\n        const node = this._chooseSubtree(bbox, this.data, level, insertPath);\n\n        // put the item into the node\n        node.children.push(item);\n        extend(node, bbox);\n\n        // split on node overflow; propagate upwards if necessary\n        while (level >= 0) {\n            if (insertPath[level].children.length > this._maxEntries) {\n                this._split(insertPath, level);\n                level--;\n            } else break;\n        }\n\n        // adjust bboxes along the insertion path\n        this._adjustParentBBoxes(bbox, insertPath, level);\n    }\n\n    // split overflowed node into two\n    _split(insertPath, level) {\n        const node = insertPath[level];\n        const M = node.children.length;\n        const m = this._minEntries;\n\n        this._chooseSplitAxis(node, m, M);\n\n        const splitIndex = this._chooseSplitIndex(node, m, M);\n\n        const newNode = createNode(node.children.splice(splitIndex, node.children.length - splitIndex));\n        newNode.height = node.height;\n        newNode.leaf = node.leaf;\n\n        calcBBox(node, this.toBBox);\n        calcBBox(newNode, this.toBBox);\n\n        if (level) insertPath[level - 1].children.push(newNode);\n        else this._splitRoot(node, newNode);\n    }\n\n    _splitRoot(node, newNode) {\n        // split root node\n        this.data = createNode([node, newNode]);\n        this.data.height = node.height + 1;\n        this.data.leaf = false;\n        calcBBox(this.data, this.toBBox);\n    }\n\n    _chooseSplitIndex(node, m, M) {\n        let index;\n        let minOverlap = Infinity;\n        let minArea = Infinity;\n\n        for (let i = m; i <= M - m; i++) {\n            const bbox1 = distBBox(node, 0, i, this.toBBox);\n            const bbox2 = distBBox(node, i, M, this.toBBox);\n\n            const overlap = intersectionArea(bbox1, bbox2);\n            const area = bboxArea(bbox1) + bboxArea(bbox2);\n\n            // choose distribution with minimum overlap\n            if (overlap < minOverlap) {\n                minOverlap = overlap;\n                index = i;\n\n                minArea = area < minArea ? area : minArea;\n\n            } else if (overlap === minOverlap) {\n                // otherwise choose distribution with minimum area\n                if (area < minArea) {\n                    minArea = area;\n                    index = i;\n                }\n            }\n        }\n\n        return index || M - m;\n    }\n\n    // sorts node children by the best axis for split\n    _chooseSplitAxis(node, m, M) {\n        const compareMinX = node.leaf ? this.compareMinX : compareNodeMinX;\n        const compareMinY = node.leaf ? this.compareMinY : compareNodeMinY;\n        const xMargin = this._allDistMargin(node, m, M, compareMinX);\n        const yMargin = this._allDistMargin(node, m, M, compareMinY);\n\n        // if total distributions margin value is minimal for x, sort by minX,\n        // otherwise it's already sorted by minY\n        if (xMargin < yMargin) node.children.sort(compareMinX);\n    }\n\n    // total margin of all possible split distributions where each node is at least m full\n    _allDistMargin(node, m, M, compare) {\n        node.children.sort(compare);\n\n        const toBBox = this.toBBox;\n        const leftBBox = distBBox(node, 0, m, toBBox);\n        const rightBBox = distBBox(node, M - m, M, toBBox);\n        let margin = bboxMargin(leftBBox) + bboxMargin(rightBBox);\n\n        for (let i = m; i < M - m; i++) {\n            const child = node.children[i];\n            extend(leftBBox, node.leaf ? toBBox(child) : child);\n            margin += bboxMargin(leftBBox);\n        }\n\n        for (let i = M - m - 1; i >= m; i--) {\n            const child = node.children[i];\n            extend(rightBBox, node.leaf ? toBBox(child) : child);\n            margin += bboxMargin(rightBBox);\n        }\n\n        return margin;\n    }\n\n    _adjustParentBBoxes(bbox, path, level) {\n        // adjust bboxes along the given tree path\n        for (let i = level; i >= 0; i--) {\n            extend(path[i], bbox);\n        }\n    }\n\n    _condense(path) {\n        // go through the path, removing empty nodes and updating bboxes\n        for (let i = path.length - 1, siblings; i >= 0; i--) {\n            if (path[i].children.length === 0) {\n                if (i > 0) {\n                    siblings = path[i - 1].children;\n                    siblings.splice(siblings.indexOf(path[i]), 1);\n\n                } else this.clear();\n\n            } else calcBBox(path[i], this.toBBox);\n        }\n    }\n}\n\nfunction findItem(item, items, equalsFn) {\n    if (!equalsFn) return items.indexOf(item);\n\n    for (let i = 0; i < items.length; i++) {\n        if (equalsFn(item, items[i])) return i;\n    }\n    return -1;\n}\n\n// calculate node's bbox from bboxes of its children\nfunction calcBBox(node, toBBox) {\n    distBBox(node, 0, node.children.length, toBBox, node);\n}\n\n// min bounding rectangle of node children from k to p-1\nfunction distBBox(node, k, p, toBBox, destNode) {\n    if (!destNode) destNode = createNode(null);\n    destNode.minX = Infinity;\n    destNode.minY = Infinity;\n    destNode.maxX = -Infinity;\n    destNode.maxY = -Infinity;\n\n    for (let i = k; i < p; i++) {\n        const child = node.children[i];\n        extend(destNode, node.leaf ? toBBox(child) : child);\n    }\n\n    return destNode;\n}\n\nfunction extend(a, b) {\n    a.minX = Math.min(a.minX, b.minX);\n    a.minY = Math.min(a.minY, b.minY);\n    a.maxX = Math.max(a.maxX, b.maxX);\n    a.maxY = Math.max(a.maxY, b.maxY);\n    return a;\n}\n\nfunction compareNodeMinX(a, b) { return a.minX - b.minX; }\nfunction compareNodeMinY(a, b) { return a.minY - b.minY; }\n\nfunction bboxArea(a)   { return (a.maxX - a.minX) * (a.maxY - a.minY); }\nfunction bboxMargin(a) { return (a.maxX - a.minX) + (a.maxY - a.minY); }\n\nfunction enlargedArea(a, b) {\n    return (Math.max(b.maxX, a.maxX) - Math.min(b.minX, a.minX)) *\n           (Math.max(b.maxY, a.maxY) - Math.min(b.minY, a.minY));\n}\n\nfunction intersectionArea(a, b) {\n    const minX = Math.max(a.minX, b.minX);\n    const minY = Math.max(a.minY, b.minY);\n    const maxX = Math.min(a.maxX, b.maxX);\n    const maxY = Math.min(a.maxY, b.maxY);\n\n    return Math.max(0, maxX - minX) *\n           Math.max(0, maxY - minY);\n}\n\nfunction contains(a, b) {\n    return a.minX <= b.minX &&\n           a.minY <= b.minY &&\n           b.maxX <= a.maxX &&\n           b.maxY <= a.maxY;\n}\n\nfunction intersects(a, b) {\n    return b.minX <= a.maxX &&\n           b.minY <= a.maxY &&\n           b.maxX >= a.minX &&\n           b.maxY >= a.minY;\n}\n\nfunction createNode(children) {\n    return {\n        children,\n        height: 1,\n        leaf: true,\n        minX: Infinity,\n        minY: Infinity,\n        maxX: -Infinity,\n        maxY: -Infinity\n    };\n}\n\n// sort an array so that items come in groups of n unsorted items, with groups sorted between each other;\n// combines selection algorithm with binary divide & conquer approach\n\nfunction multiSelect(arr, left, right, n, compare) {\n    const stack = [left, right];\n\n    while (stack.length) {\n        right = stack.pop();\n        left = stack.pop();\n\n        if (right - left <= n) continue;\n\n        const mid = left + Math.ceil((right - left) / n / 2) * n;\n        quickselect(arr, mid, left, right, compare);\n\n        stack.push(left, mid, mid, right);\n    }\n}\n", "/**\n * @module ol/structs/RBush\n */\nimport RBush_ from 'rbush';\nimport {createOrUpdate, equals} from '../extent.js';\nimport {isEmpty} from '../obj.js';\nimport {getUid} from '../util.js';\n\n/**\n * @typedef {import(\"rbush\").BBox & {value: T}} Entry\n * @template T\n */\n\n/**\n * @classdesc\n * Wrapper around the RBush by Vladimir Agafonkin.\n * See https://github.com/mourner/rbush.\n *\n * @template {Object} T\n */\nclass RBush {\n  /**\n   * @param {number} [maxEntries] Max entries.\n   */\n  constructor(maxEntries) {\n    /**\n     * @private\n     * @type {RBush_<Entry<T>>}\n     */\n    this.rbush_ = new RBush_(maxEntries);\n\n    /**\n     * A mapping between the objects added to this rbush wrapper\n     * and the objects that are actually added to the internal rbush.\n     * @private\n     * @type {Object<string, Entry<T>>}\n     */\n    this.items_ = {};\n  }\n\n  /**\n   * Insert a value into the RBush.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {T} value Value.\n   */\n  insert(extent, value) {\n    /** @type {Entry<T>} */\n    const item = {\n      minX: extent[0],\n      minY: extent[1],\n      maxX: extent[2],\n      maxY: extent[3],\n      value: value,\n    };\n\n    this.rbush_.insert(item);\n    this.items_[getUid(value)] = item;\n  }\n\n  /**\n   * Bulk-insert values into the RBush.\n   * @param {Array<import(\"../extent.js\").Extent>} extents Extents.\n   * @param {Array<T>} values Values.\n   */\n  load(extents, values) {\n    const items = new Array(values.length);\n    for (let i = 0, l = values.length; i < l; i++) {\n      const extent = extents[i];\n      const value = values[i];\n\n      /** @type {Entry<T>} */\n      const item = {\n        minX: extent[0],\n        minY: extent[1],\n        maxX: extent[2],\n        maxY: extent[3],\n        value: value,\n      };\n      items[i] = item;\n      this.items_[getUid(value)] = item;\n    }\n    this.rbush_.load(items);\n  }\n\n  /**\n   * Remove a value from the RBush.\n   * @param {T} value Value.\n   * @return {boolean} Removed.\n   */\n  remove(value) {\n    const uid = getUid(value);\n\n    // get the object in which the value was wrapped when adding to the\n    // internal rbush. then use that object to do the removal.\n    const item = this.items_[uid];\n    delete this.items_[uid];\n    return this.rbush_.remove(item) !== null;\n  }\n\n  /**\n   * Update the extent of a value in the RBush.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {T} value Value.\n   */\n  update(extent, value) {\n    const item = this.items_[getUid(value)];\n    const bbox = [item.minX, item.minY, item.maxX, item.maxY];\n    if (!equals(bbox, extent)) {\n      this.remove(value);\n      this.insert(extent, value);\n    }\n  }\n\n  /**\n   * Return all values in the RBush.\n   * @return {Array<T>} All.\n   */\n  getAll() {\n    const items = this.rbush_.all();\n    return items.map(function (item) {\n      return item.value;\n    });\n  }\n\n  /**\n   * Return all values in the given extent.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @return {Array<T>} All in extent.\n   */\n  getInExtent(extent) {\n    /** @type {import(\"rbush\").BBox} */\n    const bbox = {\n      minX: extent[0],\n      minY: extent[1],\n      maxX: extent[2],\n      maxY: extent[3],\n    };\n    const items = this.rbush_.search(bbox);\n    return items.map(function (item) {\n      return item.value;\n    });\n  }\n\n  /**\n   * Calls a callback function with each value in the tree.\n   * If the callback returns a truthy value, this value is returned without\n   * checking the rest of the tree.\n   * @param {function(T): R} callback Callback.\n   * @return {R|undefined} Callback return value.\n   * @template R\n   */\n  forEach(callback) {\n    return this.forEach_(this.getAll(), callback);\n  }\n\n  /**\n   * Calls a callback function with each value in the provided extent.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {function(T): R} callback Callback.\n   * @return {R|undefined} Callback return value.\n   * @template R\n   */\n  forEachInExtent(extent, callback) {\n    return this.forEach_(this.getInExtent(extent), callback);\n  }\n\n  /**\n   * @param {Array<T>} values Values.\n   * @param {function(T): R} callback Callback.\n   * @return {R|undefined} Callback return value.\n   * @template R\n   * @private\n   */\n  forEach_(values, callback) {\n    let result;\n    for (let i = 0, l = values.length; i < l; i++) {\n      result = callback(values[i]);\n      if (result) {\n        return result;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    return isEmpty(this.items_);\n  }\n\n  /**\n   * Remove all values from the RBush.\n   */\n  clear() {\n    this.rbush_.clear();\n    this.items_ = {};\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} [extent] Extent.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   */\n  getExtent(extent) {\n    const data = this.rbush_.toJSON();\n    return createOrUpdate(data.minX, data.minY, data.maxX, data.maxY, extent);\n  }\n\n  /**\n   * @param {RBush<T>} rbush R-Tree.\n   */\n  concat(rbush) {\n    this.rbush_.load(rbush.rbush_.all());\n    for (const i in rbush.items_) {\n      this.items_[i] = rbush.items_[i];\n    }\n  }\n}\n\nexport default RBush;\n", "/**\n * @module ol/source/Source\n */\nimport BaseObject from '../Object.js';\nimport {get as getProjection} from '../proj.js';\n\n/**\n * @typedef {'undefined' | 'loading' | 'ready' | 'error'} State\n * State of the source, one of 'undefined', 'loading', 'ready' or 'error'.\n */\n\n/**\n * A function that takes a {@link import(\"../View.js\").ViewStateLayerStateExtent} and returns a string or\n * an array of strings representing source attributions.\n *\n * @typedef {function(import(\"../View.js\").ViewStateLayerStateExtent): (string|Array<string>)} Attribution\n */\n\n/**\n * A type that can be used to provide attribution information for data sources.\n *\n * It represents either\n * a simple string (e.g. `'© Acme Inc.'`)\n * an array of simple strings (e.g. `['© Acme Inc.', '© Bacme Inc.']`)\n * a function that returns a string or array of strings ({@link module:ol/source/Source~Attribution})\n *\n * @typedef {string|Array<string>|Attribution} AttributionLike\n */\n\n/**\n * @typedef {Object} Options\n * @property {AttributionLike} [attributions] Attributions.\n * @property {boolean} [attributionsCollapsible=true] Attributions are collapsible.\n * @property {import(\"../proj.js\").ProjectionLike} [projection] Projection. Default is the view projection.\n * @property {import(\"./Source.js\").State} [state='ready'] State.\n * @property {boolean} [wrapX=false] WrapX.\n * @property {boolean} [interpolate=false] Use interpolated values when resampling.  By default,\n * the nearest neighbor is used when resampling.\n */\n\n/**\n * @classdesc\n * Abstract base class; normally only used for creating subclasses and not\n * instantiated in apps.\n * Base class for {@link module:ol/layer/Layer~Layer} sources.\n *\n * A generic `change` event is triggered when the state of the source changes.\n * @abstract\n * @api\n */\nclass Source extends BaseObject {\n  /**\n   * @param {Options} options Source options.\n   */\n  constructor(options) {\n    super();\n\n    /**\n     * @protected\n     * @type {import(\"../proj/Projection.js\").default|null}\n     */\n    this.projection = getProjection(options.projection);\n\n    /**\n     * @private\n     * @type {?Attribution}\n     */\n    this.attributions_ = adaptAttributions(options.attributions);\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.attributionsCollapsible_ = options.attributionsCollapsible ?? true;\n\n    /**\n     * This source is currently loading data. Sources that defer loading to the\n     * map's tile queue never set this to `true`.\n     * @type {boolean}\n     */\n    this.loading = false;\n\n    /**\n     * @private\n     * @type {import(\"./Source.js\").State}\n     */\n    this.state_ = options.state !== undefined ? options.state : 'ready';\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.wrapX_ = options.wrapX !== undefined ? options.wrapX : false;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.interpolate_ = !!options.interpolate;\n\n    /**\n     * @protected\n     * @type {function(import(\"../View.js\").ViewOptions):void}\n     */\n    this.viewResolver = null;\n\n    /**\n     * @protected\n     * @type {function(Error):void}\n     */\n    this.viewRejector = null;\n\n    const self = this;\n    /**\n     * @private\n     * @type {Promise<import(\"../View.js\").ViewOptions>}\n     */\n    this.viewPromise_ = new Promise(function (resolve, reject) {\n      self.viewResolver = resolve;\n      self.viewRejector = reject;\n    });\n  }\n\n  /**\n   * Get the attribution function for the source.\n   * @return {?Attribution} Attribution function.\n   * @api\n   */\n  getAttributions() {\n    return this.attributions_;\n  }\n\n  /**\n   * @return {boolean} Attributions are collapsible.\n   * @api\n   */\n  getAttributionsCollapsible() {\n    return this.attributionsCollapsible_;\n  }\n\n  /**\n   * Get the projection of the source.\n   * @return {import(\"../proj/Projection.js\").default|null} Projection.\n   * @api\n   */\n  getProjection() {\n    return this.projection;\n  }\n\n  /**\n   * @param {import(\"../proj/Projection\").default} [projection] Projection.\n   * @return {Array<number>|null} Resolutions.\n   */\n  getResolutions(projection) {\n    return null;\n  }\n\n  /**\n   * @return {Promise<import(\"../View.js\").ViewOptions>} A promise for view-related properties.\n   */\n  getView() {\n    return this.viewPromise_;\n  }\n\n  /**\n   * Get the state of the source, see {@link import(\"./Source.js\").State} for possible states.\n   * @return {import(\"./Source.js\").State} State.\n   * @api\n   */\n  getState() {\n    return this.state_;\n  }\n\n  /**\n   * @return {boolean|undefined} Wrap X.\n   */\n  getWrapX() {\n    return this.wrapX_;\n  }\n\n  /**\n   * @return {boolean} Use linear interpolation when resampling.\n   */\n  getInterpolate() {\n    return this.interpolate_;\n  }\n\n  /**\n   * Refreshes the source. The source will be cleared, and data from the server will be reloaded.\n   * @api\n   */\n  refresh() {\n    this.changed();\n  }\n\n  /**\n   * Set the attributions of the source.\n   * @param {AttributionLike|undefined} attributions Attributions.\n   *     Can be passed as `string`, `Array<string>`, {@link module:ol/source/Source~Attribution},\n   *     or `undefined`.\n   * @api\n   */\n  setAttributions(attributions) {\n    this.attributions_ = adaptAttributions(attributions);\n    this.changed();\n  }\n\n  /**\n   * Set the state of the source.\n   * @param {import(\"./Source.js\").State} state State.\n   */\n  setState(state) {\n    this.state_ = state;\n    this.changed();\n  }\n}\n\n/**\n * Turns the attributions option into an attributions function.\n * @param {AttributionLike|undefined} attributionLike The attribution option.\n * @return {Attribution|null} An attribution function (or null).\n */\nfunction adaptAttributions(attributionLike) {\n  if (!attributionLike) {\n    return null;\n  }\n  if (typeof attributionLike === 'function') {\n    return attributionLike;\n  }\n  if (!Array.isArray(attributionLike)) {\n    attributionLike = [attributionLike];\n  }\n  return (frameState) => attributionLike;\n}\n\nexport default Source;\n", "/**\n * @module ol/source/VectorEventType\n */\n\n/**\n * @enum {string}\n */\nexport default {\n  /**\n   * Triggered when a feature is added to the source.\n   * @event module:ol/source/Vector.VectorSourceEvent#addfeature\n   * @api\n   */\n  ADDFEATURE: 'addfeature',\n\n  /**\n   * Triggered when a feature is updated.\n   * @event module:ol/source/Vector.VectorSourceEvent#changefeature\n   * @api\n   */\n  CHANGEFEATURE: 'changefeature',\n\n  /**\n   * Triggered when the clear method is called on the source.\n   * @event module:ol/source/Vector.VectorSourceEvent#clear\n   * @api\n   */\n  CLEAR: 'clear',\n\n  /**\n   * Triggered when a feature is removed from the source.\n   * See {@link module:ol/source/Vector~VectorSource#clear source.clear()} for exceptions.\n   * @event module:ol/source/Vector.VectorSourceEvent#removefeature\n   * @api\n   */\n  REMOVEFEATURE: 'removefeature',\n\n  /**\n   * Triggered when features starts loading.\n   * @event module:ol/source/Vector.VectorSourceEvent#featuresloadstart\n   * @api\n   */\n  FEATURESLOADSTART: 'featuresloadstart',\n\n  /**\n   * Triggered when features finishes loading.\n   * @event module:ol/source/Vector.VectorSourceEvent#featuresloadend\n   * @api\n   */\n  FEATURESLOADEND: 'featuresloadend',\n\n  /**\n   * Triggered if feature loading results in an error.\n   * @event module:ol/source/Vector.VectorSourceEvent#featuresloaderror\n   * @api\n   */\n  FEATURESLOADERROR: 'featuresloaderror',\n};\n\n/**\n * @typedef {'addfeature'|'changefeature'|'clear'|'removefeature'|'featuresloadstart'|'featuresloadend'|'featuresloaderror'} VectorSourceEventTypes\n */\n", "/**\n * @module ol/source/Vector\n */\n\nimport Collection from '../Collection.js';\nimport CollectionEventType from '../CollectionEventType.js';\nimport ObjectEventType from '../ObjectEventType.js';\nimport {extend} from '../array.js';\nimport {assert} from '../asserts.js';\nimport Event from '../events/Event.js';\nimport EventType from '../events/EventType.js';\nimport {listen, unlistenByKey} from '../events.js';\nimport {containsExtent, equals, wrapAndSliceX} from '../extent.js';\nimport {xhr} from '../featureloader.js';\nimport {TRUE, VOID} from '../functions.js';\nimport {all as allStrategy} from '../loadingstrategy.js';\nimport {isEmpty} from '../obj.js';\nimport RenderFeature from '../render/Feature.js';\nimport RBush from '../structs/RBush.js';\nimport {getUid} from '../util.js';\nimport Source from './Source.js';\nimport VectorEventType from './VectorEventType.js';\n\n/**\n * A function that takes an {@link module:ol/extent~Extent} and a resolution as arguments, and\n * returns an array of {@link module:ol/extent~Extent} with the extents to load. Usually this\n * is one of the standard {@link module:ol/loadingstrategy} strategies.\n *\n * @typedef {function(import(\"../extent.js\").Extent, number, import(\"../proj/Projection.js\").default): Array<import(\"../extent.js\").Extent>} LoadingStrategy\n * @api\n */\n\n/**\n * @classdesc\n * Events emitted by {@link module:ol/source/Vector~VectorSource} instances are instances of this\n * type.\n * @template {import(\"../Feature.js\").FeatureLike} [FeatureType=import(\"../Feature.js\").default]\n */\nexport class VectorSourceEvent extends Event {\n  /**\n   * @param {string} type Type.\n   * @param {FeatureType} [feature] Feature.\n   * @param {Array<FeatureType>} [features] Features.\n   */\n  constructor(type, feature, features) {\n    super(type);\n\n    /**\n     * The added or removed feature for the `ADDFEATURE` and `REMOVEFEATURE` events, `undefined` otherwise.\n     * @type {FeatureType|undefined}\n     * @api\n     */\n    this.feature = feature;\n\n    /**\n     * The loaded features for the `FEATURESLOADED` event, `undefined` otherwise.\n     * @type {Array<FeatureType>|undefined}\n     * @api\n     */\n    this.features = features;\n  }\n}\n\n/***\n * @template {import(\"../Feature.js\").FeatureLike} [T=import(\"../Feature.js\").default]\n * @typedef {T extends RenderFeature ? T|Array<T> : T} FeatureClassOrArrayOfRenderFeatures\n */\n\n/***\n * @template Return\n * @template {import(\"../Feature.js\").FeatureLike} [FeatureType=import(\"../Feature.js\").default]\n * @typedef {import(\"../Observable\").OnSignature<import(\"../Observable\").EventTypes, import(\"../events/Event.js\").default, Return> &\n *   import(\"../Observable\").OnSignature<import(\"../ObjectEventType\").Types, import(\"../Object\").ObjectEvent, Return> &\n *   import(\"../Observable\").OnSignature<import(\"./VectorEventType\").VectorSourceEventTypes, VectorSourceEvent<FeatureType>, Return> &\n *   import(\"../Observable\").CombinedOnSignature<import(\"../Observable\").EventTypes|import(\"../ObjectEventType\").Types|\n *     import(\"./VectorEventType\").VectorSourceEventTypes, Return>} VectorSourceOnSignature\n */\n\n/**\n * @template {import(\"../Feature.js\").FeatureLike} [FeatureType=import(\"../Feature.js\").default]\n * @typedef {Object} Options\n * @property {import(\"./Source.js\").AttributionLike} [attributions] Attributions.\n * @property {Array<FeatureType>|Collection<FeatureType>} [features]\n * Features. If provided as {@link module:ol/Collection~Collection}, the features in the source\n * and the collection will stay in sync.\n * @property {import(\"../format/Feature.js\").default<FeatureType>} [format] The feature format used by the XHR\n * feature loader when `url` is set. Required if `url` is set, otherwise ignored.\n * @property {import(\"../featureloader.js\").FeatureLoader<FeatureType>} [loader]\n * The loader function used to load features, from a remote source for example.\n * If this is not set and `url` is set, the source will create and use an XHR\n * feature loader. The `'featuresloadend'` and `'featuresloaderror'` events\n * will only fire if the `success` and `failure` callbacks are used.\n *\n * Example:\n *\n * ```js\n * import Vector from 'ol/source/Vector.js';\n * import GeoJSON from 'ol/format/GeoJSON.js';\n * import {bbox} from 'ol/loadingstrategy.js';\n *\n * const vectorSource = new Vector({\n *   format: new GeoJSON(),\n *   loader: function(extent, resolution, projection, success, failure) {\n *      const proj = projection.getCode();\n *      const url = 'https://ahocevar.com/geoserver/wfs?service=WFS&' +\n *          'version=1.1.0&request=GetFeature&typename=osm:water_areas&' +\n *          'outputFormat=application/json&srsname=' + proj + '&' +\n *          'bbox=' + extent.join(',') + ',' + proj;\n *      const xhr = new XMLHttpRequest();\n *      xhr.open('GET', url);\n *      const onError = function() {\n *        vectorSource.removeLoadedExtent(extent);\n *        failure();\n *      }\n *      xhr.onerror = onError;\n *      xhr.onload = function() {\n *        if (xhr.status == 200) {\n *          const features = vectorSource.getFormat().readFeatures(xhr.responseText);\n *          vectorSource.addFeatures(features);\n *          success(features);\n *        } else {\n *          onError();\n *        }\n *      }\n *      xhr.send();\n *    },\n *    strategy: bbox,\n *  });\n * ```\n * @property {boolean} [overlaps=true] This source may have overlapping geometries.\n * Setting this to `false` (e.g. for sources with polygons that represent administrative\n * boundaries or TopoJSON sources) allows the renderer to optimise fill and\n * stroke operations.\n * @property {LoadingStrategy} [strategy] The loading strategy to use.\n * By default an {@link module:ol/loadingstrategy.all}\n * strategy is used, a one-off strategy which loads all features at once.\n * @property {string|import(\"../featureloader.js\").FeatureUrlFunction} [url]\n * Setting this option instructs the source to load features using an XHR loader\n * (see {@link module:ol/featureloader.xhr}). Use a `string` and an\n * {@link module:ol/loadingstrategy.all} for a one-off download of all features from\n * the given URL. Use a {@link module:ol/featureloader~FeatureUrlFunction} to generate the url with\n * other loading strategies.\n * Requires `format` to be set as well.\n * When default XHR feature loader is provided, the features will\n * be transformed from the data projection to the view projection\n * during parsing. If your remote data source does not advertise its projection\n * properly, this transformation will be incorrect. For some formats, the\n * default projection (usually EPSG:4326) can be overridden by setting the\n * dataProjection constructor option on the format.\n * Note that if a source contains non-feature data, such as a GeoJSON geometry\n * or a KML NetworkLink, these will be ignored. Use a custom loader to load these.\n * @property {boolean} [useSpatialIndex=true]\n * By default, an RTree is used as spatial index. When features are removed and\n * added frequently, and the total number of features is low, setting this to\n * `false` may improve performance.\n *\n * Note that\n * {@link module:ol/source/Vector~VectorSource#getFeaturesInExtent},\n * {@link module:ol/source/Vector~VectorSource#getClosestFeatureToCoordinate} and\n * {@link module:ol/source/Vector~VectorSource#getExtent} cannot be used when `useSpatialIndex` is\n * set to `false`, and {@link module:ol/source/Vector~VectorSource#forEachFeatureInExtent} will loop\n * through all features.\n *\n * When set to `false`, the features will be maintained in an\n * {@link module:ol/Collection~Collection}, which can be retrieved through\n * {@link module:ol/source/Vector~VectorSource#getFeaturesCollection}.\n * @property {boolean} [wrapX=true] Wrap the world horizontally. For vector editing across the\n * -180° and 180° meridians to work properly, this should be set to `false`. The\n * resulting geometry coordinates will then exceed the world bounds.\n */\n\n/**\n * @classdesc\n * Provides a source of features for vector layers. Vector features provided\n * by this source are suitable for editing. See {@link module:ol/source/VectorTile~VectorTile} for\n * vector data that is optimized for rendering.\n *\n * @fires VectorSourceEvent\n * @api\n * @template {import(\"../Feature.js\").FeatureLike} [FeatureType=import(\"../Feature.js\").default]\n */\nclass VectorSource extends Source {\n  /**\n   * @param {Options<FeatureType>} [options] Vector source options.\n   */\n  constructor(options) {\n    options = options || {};\n\n    super({\n      attributions: options.attributions,\n      interpolate: true,\n      projection: undefined,\n      state: 'ready',\n      wrapX: options.wrapX !== undefined ? options.wrapX : true,\n    });\n\n    /***\n     * @type {VectorSourceOnSignature<import(\"../events\").EventsKey, FeatureType>}\n     */\n    this.on;\n\n    /***\n     * @type {VectorSourceOnSignature<import(\"../events\").EventsKey, FeatureType>}\n     */\n    this.once;\n\n    /***\n     * @type {VectorSourceOnSignature<void>}\n     */\n    this.un;\n\n    /**\n     * @private\n     * @type {import(\"../featureloader.js\").FeatureLoader<import(\"../Feature.js\").FeatureLike>}\n     */\n    this.loader_ = VOID;\n\n    /**\n     * @private\n     * @type {import(\"../format/Feature.js\").default<FeatureType>|null}\n     */\n    this.format_ = options.format || null;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.overlaps_ = options.overlaps === undefined ? true : options.overlaps;\n\n    /**\n     * @private\n     * @type {string|import(\"../featureloader.js\").FeatureUrlFunction|undefined}\n     */\n    this.url_ = options.url;\n\n    if (options.loader !== undefined) {\n      this.loader_ = options.loader;\n    } else if (this.url_ !== undefined) {\n      assert(this.format_, '`format` must be set when `url` is set');\n      // create a XHR feature loader for \"url\" and \"format\"\n      this.loader_ = xhr(this.url_, this.format_);\n    }\n\n    /**\n     * @private\n     * @type {LoadingStrategy}\n     */\n    this.strategy_ =\n      options.strategy !== undefined ? options.strategy : allStrategy;\n\n    const useSpatialIndex =\n      options.useSpatialIndex !== undefined ? options.useSpatialIndex : true;\n\n    /**\n     * @private\n     * @type {RBush<FeatureType>}\n     */\n    this.featuresRtree_ = useSpatialIndex ? new RBush() : null;\n\n    /**\n     * @private\n     * @type {RBush<{extent: import(\"../extent.js\").Extent}>}\n     */\n    this.loadedExtentsRtree_ = new RBush();\n\n    /**\n     * @type {number}\n     * @private\n     */\n    this.loadingExtentsCount_ = 0;\n\n    /**\n     * @private\n     * @type {!Object<string, FeatureType>}\n     */\n    this.nullGeometryFeatures_ = {};\n\n    /**\n     * A lookup of features by id (the return from feature.getId()).\n     * @private\n     * @type {!Object<string, import('../Feature.js').FeatureLike|Array<import('../Feature.js').FeatureLike>>}\n     */\n    this.idIndex_ = {};\n\n    /**\n     * A lookup of features by uid (using getUid(feature)).\n     * @private\n     * @type {!Object<string, FeatureType>}\n     */\n    this.uidIndex_ = {};\n\n    /**\n     * @private\n     * @type {Object<string, Array<import(\"../events.js\").EventsKey>>}\n     */\n    this.featureChangeKeys_ = {};\n\n    /**\n     * @private\n     * @type {Collection<FeatureType>|null}\n     */\n    this.featuresCollection_ = null;\n\n    /** @type {Collection<FeatureType>} */\n    let collection;\n    /** @type {Array<FeatureType>} */\n    let features;\n    if (Array.isArray(options.features)) {\n      features = options.features;\n    } else if (options.features) {\n      collection = options.features;\n      features = collection.getArray();\n    }\n    if (!useSpatialIndex && collection === undefined) {\n      collection = new Collection(features);\n    }\n    if (features !== undefined) {\n      this.addFeaturesInternal(features);\n    }\n    if (collection !== undefined) {\n      this.bindFeaturesCollection_(collection);\n    }\n  }\n\n  /**\n   * Add a single feature to the source.  If you want to add a batch of features\n   * at once, call {@link module:ol/source/Vector~VectorSource#addFeatures #addFeatures()}\n   * instead. A feature will not be added to the source if feature with\n   * the same id is already there. The reason for this behavior is to avoid\n   * feature duplication when using bbox or tile loading strategies.\n   * Note: this also applies if a {@link module:ol/Collection~Collection} is used for features,\n   * meaning that if a feature with a duplicate id is added in the collection, it will\n   * be removed from it right away.\n   * @param {FeatureType} feature Feature to add.\n   * @api\n   */\n  addFeature(feature) {\n    this.addFeatureInternal(feature);\n    this.changed();\n  }\n\n  /**\n   * Add a feature without firing a `change` event.\n   * @param {FeatureType} feature Feature.\n   * @protected\n   */\n  addFeatureInternal(feature) {\n    const featureKey = getUid(feature);\n\n    if (!this.addToIndex_(featureKey, feature)) {\n      if (this.featuresCollection_) {\n        this.featuresCollection_.remove(feature);\n      }\n      return;\n    }\n\n    this.setupChangeEvents_(featureKey, feature);\n\n    const geometry = feature.getGeometry();\n    if (geometry) {\n      const extent = geometry.getExtent();\n      if (this.featuresRtree_) {\n        this.featuresRtree_.insert(extent, feature);\n      }\n    } else {\n      this.nullGeometryFeatures_[featureKey] = feature;\n    }\n\n    this.dispatchEvent(\n      new VectorSourceEvent(VectorEventType.ADDFEATURE, feature),\n    );\n  }\n\n  /**\n   * @param {string} featureKey Unique identifier for the feature.\n   * @param {FeatureType} feature The feature.\n   * @private\n   */\n  setupChangeEvents_(featureKey, feature) {\n    if (feature instanceof RenderFeature) {\n      return;\n    }\n    this.featureChangeKeys_[featureKey] = [\n      listen(feature, EventType.CHANGE, this.handleFeatureChange_, this),\n      listen(\n        feature,\n        ObjectEventType.PROPERTYCHANGE,\n        this.handleFeatureChange_,\n        this,\n      ),\n    ];\n  }\n\n  /**\n   * @param {string} featureKey Unique identifier for the feature.\n   * @param {FeatureType} feature The feature.\n   * @return {boolean} The feature is \"valid\", in the sense that it is also a\n   *     candidate for insertion into the Rtree.\n   * @private\n   */\n  addToIndex_(featureKey, feature) {\n    let valid = true;\n    if (feature.getId() !== undefined) {\n      const id = String(feature.getId());\n      if (!(id in this.idIndex_)) {\n        this.idIndex_[id] = feature;\n      } else if (feature instanceof RenderFeature) {\n        const indexedFeature = this.idIndex_[id];\n        if (!(indexedFeature instanceof RenderFeature)) {\n          valid = false;\n        } else if (!Array.isArray(indexedFeature)) {\n          this.idIndex_[id] = [indexedFeature, feature];\n        } else {\n          indexedFeature.push(feature);\n        }\n      } else {\n        valid = false;\n      }\n    }\n    if (valid) {\n      assert(\n        !(featureKey in this.uidIndex_),\n        'The passed `feature` was already added to the source',\n      );\n      this.uidIndex_[featureKey] = feature;\n    }\n    return valid;\n  }\n\n  /**\n   * Add a batch of features to the source.\n   * @param {Array<FeatureType>} features Features to add.\n   * @api\n   */\n  addFeatures(features) {\n    this.addFeaturesInternal(features);\n    this.changed();\n  }\n\n  /**\n   * Add features without firing a `change` event.\n   * @param {Array<FeatureType>} features Features.\n   * @protected\n   */\n  addFeaturesInternal(features) {\n    const extents = [];\n    /** @type {Array<FeatureType>} */\n    const newFeatures = [];\n    /** @type {Array<FeatureType>} */\n    const geometryFeatures = [];\n\n    for (let i = 0, length = features.length; i < length; i++) {\n      const feature = features[i];\n      const featureKey = getUid(feature);\n      if (this.addToIndex_(featureKey, feature)) {\n        newFeatures.push(feature);\n      }\n    }\n\n    for (let i = 0, length = newFeatures.length; i < length; i++) {\n      const feature = newFeatures[i];\n      const featureKey = getUid(feature);\n      this.setupChangeEvents_(featureKey, feature);\n\n      const geometry = feature.getGeometry();\n      if (geometry) {\n        const extent = geometry.getExtent();\n        extents.push(extent);\n        geometryFeatures.push(feature);\n      } else {\n        this.nullGeometryFeatures_[featureKey] = feature;\n      }\n    }\n    if (this.featuresRtree_) {\n      this.featuresRtree_.load(extents, geometryFeatures);\n    }\n\n    if (this.hasListener(VectorEventType.ADDFEATURE)) {\n      for (let i = 0, length = newFeatures.length; i < length; i++) {\n        this.dispatchEvent(\n          new VectorSourceEvent(VectorEventType.ADDFEATURE, newFeatures[i]),\n        );\n      }\n    }\n  }\n\n  /**\n   * @param {!Collection<FeatureType>} collection Collection.\n   * @private\n   */\n  bindFeaturesCollection_(collection) {\n    let modifyingCollection = false;\n    this.addEventListener(\n      VectorEventType.ADDFEATURE,\n      /**\n       * @param {VectorSourceEvent<FeatureType>} evt The vector source event\n       */\n      function (evt) {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          collection.push(evt.feature);\n          modifyingCollection = false;\n        }\n      },\n    );\n    this.addEventListener(\n      VectorEventType.REMOVEFEATURE,\n      /**\n       * @param {VectorSourceEvent<FeatureType>} evt The vector source event\n       */\n      function (evt) {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          collection.remove(evt.feature);\n          modifyingCollection = false;\n        }\n      },\n    );\n    collection.addEventListener(\n      CollectionEventType.ADD,\n      /**\n       * @param {import(\"../Collection.js\").CollectionEvent<FeatureType>} evt The collection event\n       */\n      (evt) => {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          this.addFeature(evt.element);\n          modifyingCollection = false;\n        }\n      },\n    );\n    collection.addEventListener(\n      CollectionEventType.REMOVE,\n      /**\n       * @param {import(\"../Collection.js\").CollectionEvent<FeatureType>} evt The collection event\n       */\n      (evt) => {\n        if (!modifyingCollection) {\n          modifyingCollection = true;\n          this.removeFeature(evt.element);\n          modifyingCollection = false;\n        }\n      },\n    );\n    this.featuresCollection_ = collection;\n  }\n\n  /**\n   * Remove all features from the source.\n   * @param {boolean} [fast] Skip dispatching of {@link module:ol/source/Vector.VectorSourceEvent#event:removefeature} events.\n   * @api\n   */\n  clear(fast) {\n    if (fast) {\n      for (const featureId in this.featureChangeKeys_) {\n        const keys = this.featureChangeKeys_[featureId];\n        keys.forEach(unlistenByKey);\n      }\n      if (!this.featuresCollection_) {\n        this.featureChangeKeys_ = {};\n        this.idIndex_ = {};\n        this.uidIndex_ = {};\n      }\n    } else {\n      if (this.featuresRtree_) {\n        this.featuresRtree_.forEach((feature) => {\n          this.removeFeatureInternal(feature);\n        });\n        for (const id in this.nullGeometryFeatures_) {\n          this.removeFeatureInternal(this.nullGeometryFeatures_[id]);\n        }\n      }\n    }\n    if (this.featuresCollection_) {\n      this.featuresCollection_.clear();\n    }\n\n    if (this.featuresRtree_) {\n      this.featuresRtree_.clear();\n    }\n    this.nullGeometryFeatures_ = {};\n\n    const clearEvent = new VectorSourceEvent(VectorEventType.CLEAR);\n    this.dispatchEvent(clearEvent);\n    this.changed();\n  }\n\n  /**\n   * Iterate through all features on the source, calling the provided callback\n   * with each one.  If the callback returns any \"truthy\" value, iteration will\n   * stop and the function will return the same value.\n   * Note: this function only iterate through the feature that have a defined geometry.\n   *\n   * @param {function(FeatureType): T} callback Called with each feature\n   *     on the source.  Return a truthy value to stop iteration.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   * @api\n   */\n  forEachFeature(callback) {\n    if (this.featuresRtree_) {\n      return this.featuresRtree_.forEach(callback);\n    }\n    if (this.featuresCollection_) {\n      this.featuresCollection_.forEach(callback);\n    }\n  }\n\n  /**\n   * Iterate through all features whose geometries contain the provided\n   * coordinate, calling the callback with each feature.  If the callback returns\n   * a \"truthy\" value, iteration will stop and the function will return the same\n   * value.\n   *\n   * For {@link module:ol/render/Feature~RenderFeature} features, the callback will be\n   * called for all features.\n   *\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {function(FeatureType): T} callback Called with each feature\n   *     whose goemetry contains the provided coordinate.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   */\n  forEachFeatureAtCoordinateDirect(coordinate, callback) {\n    const extent = [coordinate[0], coordinate[1], coordinate[0], coordinate[1]];\n    return this.forEachFeatureInExtent(extent, function (feature) {\n      const geometry = feature.getGeometry();\n      if (\n        geometry instanceof RenderFeature ||\n        geometry.intersectsCoordinate(coordinate)\n      ) {\n        return callback(feature);\n      }\n      return undefined;\n    });\n  }\n\n  /**\n   * Iterate through all features whose bounding box intersects the provided\n   * extent (note that the feature's geometry may not intersect the extent),\n   * calling the callback with each feature.  If the callback returns a \"truthy\"\n   * value, iteration will stop and the function will return the same value.\n   *\n   * If you are interested in features whose geometry intersects an extent, call\n   * the {@link module:ol/source/Vector~VectorSource#forEachFeatureIntersectingExtent #forEachFeatureIntersectingExtent()} method instead.\n   *\n   * When `useSpatialIndex` is set to false, this method will loop through all\n   * features, equivalent to {@link module:ol/source/Vector~VectorSource#forEachFeature #forEachFeature()}.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {function(FeatureType): T} callback Called with each feature\n   *     whose bounding box intersects the provided extent.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   * @api\n   */\n  forEachFeatureInExtent(extent, callback) {\n    if (this.featuresRtree_) {\n      return this.featuresRtree_.forEachInExtent(extent, callback);\n    }\n    if (this.featuresCollection_) {\n      this.featuresCollection_.forEach(callback);\n    }\n  }\n\n  /**\n   * Iterate through all features whose geometry intersects the provided extent,\n   * calling the callback with each feature.  If the callback returns a \"truthy\"\n   * value, iteration will stop and the function will return the same value.\n   *\n   * If you only want to test for bounding box intersection, call the\n   * {@link module:ol/source/Vector~VectorSource#forEachFeatureInExtent #forEachFeatureInExtent()} method instead.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {function(FeatureType): T} callback Called with each feature\n   *     whose geometry intersects the provided extent.\n   * @return {T|undefined} The return value from the last call to the callback.\n   * @template T\n   * @api\n   */\n  forEachFeatureIntersectingExtent(extent, callback) {\n    return this.forEachFeatureInExtent(\n      extent,\n      /**\n       * @param {FeatureType} feature Feature.\n       * @return {T|undefined} The return value from the last call to the callback.\n       */\n      function (feature) {\n        const geometry = feature.getGeometry();\n        if (\n          geometry instanceof RenderFeature ||\n          geometry.intersectsExtent(extent)\n        ) {\n          const result = callback(feature);\n          if (result) {\n            return result;\n          }\n        }\n      },\n    );\n  }\n\n  /**\n   * Get the features collection associated with this source. Will be `null`\n   * unless the source was configured with `useSpatialIndex` set to `false`, or\n   * with a {@link module:ol/Collection~Collection} as `features`.\n   * @return {Collection<FeatureType>|null} The collection of features.\n   * @api\n   */\n  getFeaturesCollection() {\n    return this.featuresCollection_;\n  }\n\n  /**\n   * Get a snapshot of the features currently on the source in random order. The returned array\n   * is a copy, the features are references to the features in the source.\n   * @return {Array<FeatureType>} Features.\n   * @api\n   */\n  getFeatures() {\n    let features;\n    if (this.featuresCollection_) {\n      features = this.featuresCollection_.getArray().slice(0);\n    } else if (this.featuresRtree_) {\n      features = this.featuresRtree_.getAll();\n      if (!isEmpty(this.nullGeometryFeatures_)) {\n        extend(features, Object.values(this.nullGeometryFeatures_));\n      }\n    }\n    return features;\n  }\n\n  /**\n   * Get all features whose geometry intersects the provided coordinate.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @return {Array<FeatureType>} Features.\n   * @api\n   */\n  getFeaturesAtCoordinate(coordinate) {\n    /** @type {Array<FeatureType>} */\n    const features = [];\n    this.forEachFeatureAtCoordinateDirect(coordinate, function (feature) {\n      features.push(feature);\n    });\n    return features;\n  }\n\n  /**\n   * Get all features whose bounding box intersects the provided extent.  Note that this returns an array of\n   * all features intersecting the given extent in random order (so it may include\n   * features whose geometries do not intersect the extent).\n   *\n   * When `useSpatialIndex` is set to false, this method will return all\n   * features.\n   *\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {import(\"../proj/Projection.js\").default} [projection] Include features\n   * where `extent` exceeds the x-axis bounds of `projection` and wraps around the world.\n   * @return {Array<FeatureType>} Features.\n   * @api\n   */\n  getFeaturesInExtent(extent, projection) {\n    if (this.featuresRtree_) {\n      const multiWorld = projection && projection.canWrapX() && this.getWrapX();\n\n      if (!multiWorld) {\n        return this.featuresRtree_.getInExtent(extent);\n      }\n\n      const extents = wrapAndSliceX(extent, projection);\n\n      return [].concat(\n        ...extents.map((anExtent) => this.featuresRtree_.getInExtent(anExtent)),\n      );\n    }\n    if (this.featuresCollection_) {\n      return this.featuresCollection_.getArray().slice(0);\n    }\n    return [];\n  }\n\n  /**\n   * Get the closest feature to the provided coordinate.\n   *\n   * This method is not available when the source is configured with\n   * `useSpatialIndex` set to `false` and the features in this source are of type\n   * {@link module:ol/Feature~Feature}.\n   * @param {import(\"../coordinate.js\").Coordinate} coordinate Coordinate.\n   * @param {function(FeatureType):boolean} [filter] Feature filter function.\n   *     The filter function will receive one argument, the {@link module:ol/Feature~Feature feature}\n   *     and it should return a boolean value. By default, no filtering is made.\n   * @return {FeatureType} Closest feature.\n   * @api\n   */\n  getClosestFeatureToCoordinate(coordinate, filter) {\n    // Find the closest feature using branch and bound.  We start searching an\n    // infinite extent, and find the distance from the first feature found.  This\n    // becomes the closest feature.  We then compute a smaller extent which any\n    // closer feature must intersect.  We continue searching with this smaller\n    // extent, trying to find a closer feature.  Every time we find a closer\n    // feature, we update the extent being searched so that any even closer\n    // feature must intersect it.  We continue until we run out of features.\n    const x = coordinate[0];\n    const y = coordinate[1];\n    let closestFeature = null;\n    const closestPoint = [NaN, NaN];\n    let minSquaredDistance = Infinity;\n    const extent = [-Infinity, -Infinity, Infinity, Infinity];\n    filter = filter ? filter : TRUE;\n    this.featuresRtree_.forEachInExtent(\n      extent,\n      /**\n       * @param {FeatureType} feature Feature.\n       */\n      function (feature) {\n        if (filter(feature)) {\n          const geometry = feature.getGeometry();\n          const previousMinSquaredDistance = minSquaredDistance;\n          minSquaredDistance =\n            geometry instanceof RenderFeature\n              ? 0\n              : geometry.closestPointXY(x, y, closestPoint, minSquaredDistance);\n          if (minSquaredDistance < previousMinSquaredDistance) {\n            closestFeature = feature;\n            // This is sneaky.  Reduce the extent that it is currently being\n            // searched while the R-Tree traversal using this same extent object\n            // is still in progress.  This is safe because the new extent is\n            // strictly contained by the old extent.\n            const minDistance = Math.sqrt(minSquaredDistance);\n            extent[0] = x - minDistance;\n            extent[1] = y - minDistance;\n            extent[2] = x + minDistance;\n            extent[3] = y + minDistance;\n          }\n        }\n      },\n    );\n    return closestFeature;\n  }\n\n  /**\n   * Get the extent of the features currently in the source.\n   *\n   * This method is not available when the source is configured with\n   * `useSpatialIndex` set to `false`.\n   * @param {import(\"../extent.js\").Extent} [extent] Destination extent. If provided, no new extent\n   *     will be created. Instead, that extent's coordinates will be overwritten.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getExtent(extent) {\n    return this.featuresRtree_.getExtent(extent);\n  }\n\n  /**\n   * Get a feature by its identifier (the value returned by feature.getId()). When `RenderFeature`s\n   * are used, `getFeatureById()` can return an array of `RenderFeature`s. This allows for handling\n   * of `GeometryCollection` geometries, where format readers create one `RenderFeature` per\n   * `GeometryCollection` member.\n   * Note that the index treats string and numeric identifiers as the same.  So\n   * `source.getFeatureById(2)` will return a feature with id `'2'` or `2`.\n   *\n   * @param {string|number} id Feature identifier.\n   * @return {FeatureClassOrArrayOfRenderFeatures<FeatureType>|null} The feature (or `null` if not found).\n   * @api\n   */\n  getFeatureById(id) {\n    const feature = this.idIndex_[id.toString()];\n    return feature !== undefined\n      ? /** @type {FeatureClassOrArrayOfRenderFeatures<FeatureType>} */ (\n          feature\n        )\n      : null;\n  }\n\n  /**\n   * Get a feature by its internal unique identifier (using `getUid`).\n   *\n   * @param {string} uid Feature identifier.\n   * @return {FeatureType|null} The feature (or `null` if not found).\n   */\n  getFeatureByUid(uid) {\n    const feature = this.uidIndex_[uid];\n    return feature !== undefined ? feature : null;\n  }\n\n  /**\n   * Get the format associated with this source.\n   *\n   * @return {import(\"../format/Feature.js\").default<FeatureType>|null}} The feature format.\n   * @api\n   */\n  getFormat() {\n    return this.format_;\n  }\n\n  /**\n   * @return {boolean} The source can have overlapping geometries.\n   */\n  getOverlaps() {\n    return this.overlaps_;\n  }\n\n  /**\n   * Get the url associated with this source.\n   *\n   * @return {string|import(\"../featureloader.js\").FeatureUrlFunction|undefined} The url.\n   * @api\n   */\n  getUrl() {\n    return this.url_;\n  }\n\n  /**\n   * @param {Event} event Event.\n   * @private\n   */\n  handleFeatureChange_(event) {\n    const feature = /** @type {FeatureType} */ (event.target);\n    const featureKey = getUid(feature);\n    const geometry = feature.getGeometry();\n    if (!geometry) {\n      if (!(featureKey in this.nullGeometryFeatures_)) {\n        if (this.featuresRtree_) {\n          this.featuresRtree_.remove(feature);\n        }\n        this.nullGeometryFeatures_[featureKey] = feature;\n      }\n    } else {\n      const extent = geometry.getExtent();\n      if (featureKey in this.nullGeometryFeatures_) {\n        delete this.nullGeometryFeatures_[featureKey];\n        if (this.featuresRtree_) {\n          this.featuresRtree_.insert(extent, feature);\n        }\n      } else {\n        if (this.featuresRtree_) {\n          this.featuresRtree_.update(extent, feature);\n        }\n      }\n    }\n    const id = feature.getId();\n    if (id !== undefined) {\n      const sid = id.toString();\n      if (this.idIndex_[sid] !== feature) {\n        this.removeFromIdIndex_(feature);\n        this.idIndex_[sid] = feature;\n      }\n    } else {\n      this.removeFromIdIndex_(feature);\n      this.uidIndex_[featureKey] = feature;\n    }\n    this.changed();\n    this.dispatchEvent(\n      new VectorSourceEvent(VectorEventType.CHANGEFEATURE, feature),\n    );\n  }\n\n  /**\n   * Returns true if the feature is contained within the source.\n   * @param {FeatureType} feature Feature.\n   * @return {boolean} Has feature.\n   * @api\n   */\n  hasFeature(feature) {\n    const id = feature.getId();\n    if (id !== undefined) {\n      return id in this.idIndex_;\n    }\n    return getUid(feature) in this.uidIndex_;\n  }\n\n  /**\n   * @return {boolean} Is empty.\n   */\n  isEmpty() {\n    if (this.featuresRtree_) {\n      return (\n        this.featuresRtree_.isEmpty() && isEmpty(this.nullGeometryFeatures_)\n      );\n    }\n    if (this.featuresCollection_) {\n      return this.featuresCollection_.getLength() === 0;\n    }\n    return true;\n  }\n\n  /**\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @param {number} resolution Resolution.\n   * @param {import(\"../proj/Projection.js\").default} projection Projection.\n   */\n  loadFeatures(extent, resolution, projection) {\n    const loadedExtentsRtree = this.loadedExtentsRtree_;\n    const extentsToLoad = this.strategy_(extent, resolution, projection);\n    for (let i = 0, ii = extentsToLoad.length; i < ii; ++i) {\n      const extentToLoad = extentsToLoad[i];\n      const alreadyLoaded = loadedExtentsRtree.forEachInExtent(\n        extentToLoad,\n        /**\n         * @param {{extent: import(\"../extent.js\").Extent}} object Object.\n         * @return {boolean} Contains.\n         */\n        function (object) {\n          return containsExtent(object.extent, extentToLoad);\n        },\n      );\n      if (!alreadyLoaded) {\n        ++this.loadingExtentsCount_;\n        this.dispatchEvent(\n          new VectorSourceEvent(VectorEventType.FEATURESLOADSTART),\n        );\n        this.loader_.call(\n          this,\n          extentToLoad,\n          resolution,\n          projection,\n          /**\n           * @param {Array<FeatureType>} features Loaded features\n           */\n          (features) => {\n            --this.loadingExtentsCount_;\n            this.dispatchEvent(\n              new VectorSourceEvent(\n                VectorEventType.FEATURESLOADEND,\n                undefined,\n                features,\n              ),\n            );\n          },\n          () => {\n            --this.loadingExtentsCount_;\n            this.dispatchEvent(\n              new VectorSourceEvent(VectorEventType.FEATURESLOADERROR),\n            );\n          },\n        );\n        loadedExtentsRtree.insert(extentToLoad, {extent: extentToLoad.slice()});\n      }\n    }\n    this.loading =\n      this.loader_.length < 4 ? false : this.loadingExtentsCount_ > 0;\n  }\n\n  /**\n   * @override\n   */\n  refresh() {\n    this.clear(true);\n    this.loadedExtentsRtree_.clear();\n    super.refresh();\n  }\n\n  /**\n   * Remove an extent from the list of loaded extents.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @api\n   */\n  removeLoadedExtent(extent) {\n    const loadedExtentsRtree = this.loadedExtentsRtree_;\n    const obj = loadedExtentsRtree.forEachInExtent(extent, function (object) {\n      if (equals(object.extent, extent)) {\n        return object;\n      }\n    });\n    if (obj) {\n      loadedExtentsRtree.remove(obj);\n    }\n  }\n\n  /**\n   * Batch remove features from the source.  If you want to remove all features\n   * at once, use the {@link module:ol/source/Vector~VectorSource#clear #clear()} method\n   * instead.\n   * @param {Array<FeatureType>} features Features to remove.\n   * @api\n   */\n  removeFeatures(features) {\n    let removed = false;\n    for (let i = 0, ii = features.length; i < ii; ++i) {\n      removed = this.removeFeatureInternal(features[i]) || removed;\n    }\n    if (removed) {\n      this.changed();\n    }\n  }\n\n  /**\n   * Remove a single feature from the source. If you want to batch remove\n   * features, use the {@link module:ol/source/Vector~VectorSource#removeFeatures #removeFeatures()} method\n   * instead.\n   * @param {FeatureType} feature Feature to remove.\n   * @api\n   */\n  removeFeature(feature) {\n    if (!feature) {\n      return;\n    }\n    const removed = this.removeFeatureInternal(feature);\n    if (removed) {\n      this.changed();\n    }\n  }\n\n  /**\n   * Remove feature without firing a `change` event.\n   * @param {FeatureType} feature Feature.\n   * @return {boolean} True if the feature was removed, false if it was not found.\n   * @protected\n   */\n  removeFeatureInternal(feature) {\n    const featureKey = getUid(feature);\n    if (!(featureKey in this.uidIndex_)) {\n      return false;\n    }\n\n    if (featureKey in this.nullGeometryFeatures_) {\n      delete this.nullGeometryFeatures_[featureKey];\n    } else {\n      if (this.featuresRtree_) {\n        this.featuresRtree_.remove(feature);\n      }\n    }\n\n    const featureChangeKeys = this.featureChangeKeys_[featureKey];\n    featureChangeKeys?.forEach(unlistenByKey);\n    delete this.featureChangeKeys_[featureKey];\n\n    const id = feature.getId();\n    if (id !== undefined) {\n      const idString = id.toString();\n      const indexedFeature = this.idIndex_[idString];\n      if (indexedFeature === feature) {\n        delete this.idIndex_[idString];\n      } else if (Array.isArray(indexedFeature)) {\n        indexedFeature.splice(indexedFeature.indexOf(feature), 1);\n        if (indexedFeature.length === 1) {\n          this.idIndex_[idString] = indexedFeature[0];\n        }\n      }\n    }\n    delete this.uidIndex_[featureKey];\n    if (this.hasListener(VectorEventType.REMOVEFEATURE)) {\n      this.dispatchEvent(\n        new VectorSourceEvent(VectorEventType.REMOVEFEATURE, feature),\n      );\n    }\n    return true;\n  }\n\n  /**\n   * Remove a feature from the id index.  Called internally when the feature id\n   * may have changed.\n   * @param {FeatureType} feature The feature.\n   * @private\n   */\n  removeFromIdIndex_(feature) {\n    for (const id in this.idIndex_) {\n      if (this.idIndex_[id] === feature) {\n        delete this.idIndex_[id];\n        break;\n      }\n    }\n  }\n\n  /**\n   * Set the new loader of the source. The next render cycle will use the\n   * new loader.\n   * @param {import(\"../featureloader.js\").FeatureLoader} loader The loader to set.\n   * @api\n   */\n  setLoader(loader) {\n    this.loader_ = loader;\n  }\n\n  /**\n   * Points the source to a new url. The next render cycle will use the new url.\n   * @param {string|import(\"../featureloader.js\").FeatureUrlFunction} url Url.\n   * @api\n   */\n  setUrl(url) {\n    assert(this.format_, '`format` must be set when `url` is set');\n    this.url_ = url;\n    this.setLoader(xhr(url, this.format_));\n  }\n\n  /**\n   * @param {boolean} overlaps The source can have overlapping geometries.\n   */\n  setOverlaps(overlaps) {\n    this.overlaps_ = overlaps;\n    this.changed();\n  }\n}\n\nexport default VectorSource;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAO,8BAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAML,QAAQ;AACV;;;ACTA,IAAM,WAAW;AAAA,EACf,QAAQ;AACV;AAQO,IAAM,kBAAN,cAA8B,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzC,YAAY,MAAM,SAAS,OAAO;AAChC,UAAM,IAAI;AAOV,SAAK,UAAU;AAOf,SAAK,QAAQ;AAAA,EACf;AACF;AA+BA,IAAM,aAAN,cAAyB,eAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,YAAY,OAAO,SAAS;AAC1B,UAAM;AAKN,SAAK;AAKL,SAAK;AAKL,SAAK;AAEL,cAAU,WAAW,CAAC;AAMtB,SAAK,UAAU,CAAC,CAAC,QAAQ;AAMzB,SAAK,SAAS,QAAQ,QAAQ,CAAC;AAE/B,QAAI,KAAK,SAAS;AAChB,eAAS,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,aAAK,cAAc,KAAK,OAAO,CAAC,GAAG,CAAC;AAAA,MACtC;AAAA,IACF;AAEA,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,WAAO,KAAK,UAAU,IAAI,GAAG;AAC3B,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,KAAK;AACV,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5C,WAAK,KAAK,IAAI,CAAC,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,GAAG;AACT,UAAM,QAAQ,KAAK;AACnB,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,QAAE,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,OAAO;AACV,WAAO,KAAK,OAAO,KAAK;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK,IAAI,SAAS,MAAM;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,MAAM;AACpB,QAAI,QAAQ,KAAK,QAAQ,KAAK,UAAU,GAAG;AACzC,YAAM,IAAI,MAAM,0BAA0B,KAAK;AAAA,IACjD;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,SAAK,OAAO,OAAO,OAAO,GAAG,IAAI;AACjC,SAAK,cAAc;AACnB,SAAK;AAAA,MACH,IAAI,gBAAgB,4BAAoB,KAAK,MAAM,KAAK;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM;AACJ,WAAO,KAAK,SAAS,KAAK,UAAU,IAAI,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,MAAM;AACT,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,UAAM,IAAI,KAAK,UAAU;AACzB,SAAK,SAAS,GAAG,IAAI;AACrB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,MAAM;AACX,UAAM,MAAM,KAAK;AACjB,aAAS,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5C,UAAI,IAAI,CAAC,MAAM,MAAM;AACnB,eAAO,KAAK,SAAS,CAAC;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,OAAO;AACd,QAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,SAAK,OAAO,OAAO,OAAO,CAAC;AAC3B,SAAK,cAAc;AACnB,SAAK;AAAA;AAAA,MAED,IAAI,gBAAgB,4BAAoB,QAAQ,MAAM,KAAK;AAAA,IAE/D;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,OAAO,MAAM;AACjB,UAAM,IAAI,KAAK,UAAU;AACzB,QAAI,SAAS,GAAG;AACd,WAAK,SAAS,OAAO,IAAI;AACzB;AAAA,IACF;AACA,QAAI,QAAQ,GAAG;AACb,YAAM,IAAI,MAAM,0BAA0B,KAAK;AAAA,IACjD;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,cAAc,MAAM,KAAK;AAAA,IAChC;AACA,UAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK;AAAA;AAAA,MAED,IAAI,gBAAgB,4BAAoB,QAAQ,MAAM,KAAK;AAAA,IAE/D;AACA,SAAK;AAAA;AAAA,MAED,IAAI,gBAAgB,4BAAoB,KAAK,MAAM,KAAK;AAAA,IAE5D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,IAAI,SAAS,QAAQ,KAAK,OAAO,MAAM;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,MAAM,QAAQ;AAC1B,aAAS,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,UAAI,KAAK,OAAO,CAAC,MAAM,QAAQ,MAAM,QAAQ;AAC3C,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,qBAAQ;;;AC9Tf,IAAI,kBAAkB;AAmDf,SAAS,gBACd,KACA,QACA,QACA,YACA,YACA,SACA,SACA;AACA,QAAMA,OAAM,IAAI,eAAe;AAC/B,EAAAA,KAAI;AAAA,IACF;AAAA,IACA,OAAO,QAAQ,aAAa,IAAI,QAAQ,YAAY,UAAU,IAAI;AAAA,IAClE;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,KAAK,eAAe;AACrC,IAAAA,KAAI,eAAe;AAAA,EACrB;AACA,EAAAA,KAAI,kBAAkB;AAKtB,EAAAA,KAAI,SAAS,SAAU,OAAO;AAE5B,QAAI,CAACA,KAAI,UAAWA,KAAI,UAAU,OAAOA,KAAI,SAAS,KAAM;AAC1D,YAAM,OAAO,OAAO,QAAQ;AAC5B,UAAI;AAEF,YAAI;AACJ,YAAI,QAAQ,UAAU,QAAQ,QAAQ;AACpC,mBAASA,KAAI;AAAA,QACf,WAAW,QAAQ,OAAO;AACxB,mBAASA,KAAI,eAAeA,KAAI;AAAA,QAClC,WAAW,QAAQ,eAAe;AAChC;AAAA,UAAqCA,KAAI;AAAA,QAC3C;AACA,YAAI,QAAQ;AACV;AAAA;AAAA,YAGI,OAAO,aAAa,QAAQ;AAAA,cAC1B;AAAA,cACA,mBAAmB;AAAA,YACrB,CAAC;AAAA,YAEH,OAAO,eAAe,MAAM;AAAA,UAC9B;AAAA,QACF,OAAO;AACL,kBAAQ;AAAA,QACV;AAAA,MACF,QAAQ;AACN,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AAIA,EAAAA,KAAI,UAAU;AACd,EAAAA,KAAI,KAAK;AACX;AAaO,SAAS,IAAI,KAAK,QAAQ;AAW/B,SAAO,SAAU,QAAQ,YAAY,YAAY,SAAS,SAAS;AACjE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,CAAC,UAAU,mBAAmB;AAC5B,aAAK,YAAY,QAAQ;AACzB,YAAI,YAAY,QAAW;AACzB,kBAAQ,QAAQ;AAAA,QAClB;AAAA,MACF;AAAA,MACA,MAAM;AACJ,aAAK,QAAQ;AACb,YAAI,YAAY,QAAW;AACzB,kBAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AChKO,SAAS,IAAI,QAAQ,YAAY;AACtC,SAAO,CAAC,CAAC,WAAW,WAAW,UAAU,QAAQ,CAAC;AACpD;;;ACHO,SAAS,aAAa,iBAAiBC,SAAQ,OAAO,QAAQ;AACnE,QAAM,cAAc,CAAC;AACrB,MAAI,SAAS,YAAY;AACzB,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,aAAS;AAAA,MACP;AAAA,MACAA;AAAA,MACA,KAAK,CAAC;AAAA,MACN;AAAA,IACF;AACA,gBAAY,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;AACzE,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;ACdO,SAAS,yBACd,iBACAC,SACA,KACA,QACA,QACA;AACA,QAAM,UAAU;AAAA,IACd;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,SAAU,YAAY;AACpB,aAAO,CAAC;AAAA,QACN;AAAA,QACAA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,CAAC;AAAA,QACZ,WAAW,CAAC;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC;AACV;AAWO,SAAS,qBACd,iBACAA,SACA,KACA,QACA,GACA,GACA;AAQA,MAAI,KAAK;AACT,MAAI,KAAK,gBAAgB,MAAM,MAAM;AACrC,MAAI,KAAK,gBAAgB,MAAM,SAAS,CAAC;AACzC,SAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,UAAM,KAAK,gBAAgBA,OAAM;AACjC,UAAM,KAAK,gBAAgBA,UAAS,CAAC;AACrC,QAAI,MAAM,GAAG;AACX,UAAI,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,MAAM,GAAG;AAC7D;AAAA,MACF;AAAA,IACF,WAAW,MAAM,MAAM,KAAK,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,MAAM,GAAG;AACrE;AAAA,IACF;AACA,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO,OAAO;AAChB;AAWO,SAAS,sBACd,iBACAA,SACA,MACA,QACA,GACA,GACA;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,qBAAqB,iBAAiBA,SAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG;AACzE,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,QACE,qBAAqB,iBAAiB,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,GAAG,CAAC,GACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAWO,SAAS,uBACd,iBACAA,SACA,OACA,QACA,GACA,GACA;AACA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,sBAAsB,iBAAiBA,SAAQ,MAAM,QAAQ,GAAG,CAAC,GAAG;AACtE,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;AC7HO,SAAS,wBACd,iBACAC,SACA,MACA,QACA,aACA,mBACA,MACA;AACA,MAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI;AAC1B,QAAM,IAAI,YAAY,oBAAoB,CAAC;AAE3C,QAAM,gBAAgB,CAAC;AAEvB,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,SAAK,gBAAgB,MAAM,MAAM;AACjC,SAAK,gBAAgB,MAAM,SAAS,CAAC;AACrC,SAAK,IAAIA,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACrC,WAAK,gBAAgB,CAAC;AACtB,WAAK,gBAAgB,IAAI,CAAC;AAC1B,UAAK,KAAK,MAAM,MAAM,KAAO,MAAM,KAAK,KAAK,IAAK;AAChD,aAAM,IAAI,OAAO,KAAK,OAAQ,KAAK,MAAM;AACzC,sBAAc,KAAK,CAAC;AAAA,MACtB;AACA,WAAK;AACL,WAAK;AAAA,IACP;AAAA,EACF;AAGA,MAAI,SAAS;AACb,MAAI,mBAAmB;AACvB,gBAAc,KAAK,SAAS;AAC5B,OAAK,cAAc,CAAC;AACpB,OAAK,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AAClD,SAAK,cAAc,CAAC;AACpB,UAAM,gBAAgB,KAAK,IAAI,KAAK,EAAE;AACtC,QAAI,gBAAgB,kBAAkB;AACpC,WAAK,KAAK,MAAM;AAChB,UAAI,sBAAsB,iBAAiBA,SAAQ,MAAM,QAAQ,GAAG,CAAC,GAAG;AACtE,iBAAS;AACT,2BAAmB;AAAA,MACrB;AAAA,IACF;AACA,SAAK;AAAA,EACP;AACA,MAAI,MAAM,MAAM,GAAG;AAGjB,aAAS,YAAY,iBAAiB;AAAA,EACxC;AACA,MAAI,MAAM;AACR,SAAK,KAAK,QAAQ,GAAG,gBAAgB;AACrC,WAAO;AAAA,EACT;AACA,SAAO,CAAC,QAAQ,GAAG,gBAAgB;AACrC;AAWO,SAAS,8BACd,iBACAA,SACA,OACA,QACA,aACA;AAEA,MAAI,iBAAiB,CAAC;AACtB,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,qBAAiB;AAAA,MACf;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ;AAAA,IACF;AACA,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;AC9FO,SAAS,iBACd,iBACAC,SACA,KACA,QACA,UACA,MACA,WACA;AACA,MAAI,GAAG;AACP,QAAM,KAAK,MAAMA,WAAU;AAC3B,MAAI,MAAM,GAAG;AACX,QAAIA;AAAA,EACN,WAAW,MAAM,GAAG;AAClB,QAAIA;AACJ,QAAI;AAAA,EACN,WAAW,MAAM,GAAG;AAClB,QAAI,KAAK,gBAAgBA,OAAM;AAC/B,QAAI,KAAK,gBAAgBA,UAAS,CAAC;AACnC,QAAI,SAAS;AACb,UAAM,oBAAoB,CAAC,CAAC;AAC5B,aAAS,IAAIA,UAAS,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAClD,YAAM,KAAK,gBAAgB,CAAC;AAC5B,YAAM,KAAK,gBAAgB,IAAI,CAAC;AAChC,gBAAU,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,GAAG;AACjE,wBAAkB,KAAK,MAAM;AAC7B,WAAK;AACL,WAAK;AAAA,IACP;AACA,UAAM,SAAS,WAAW;AAC1B,UAAM,QAAQ,aAAa,mBAAmB,MAAM;AACpD,QAAI,QAAQ,GAAG;AACb,WACG,SAAS,kBAAkB,CAAC,QAAQ,CAAC,MACrC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,kBAAkB,CAAC,QAAQ,CAAC;AAC/D,UAAIA,WAAU,CAAC,QAAQ,KAAK;AAAA,IAC9B,OAAO;AACL,UAAIA,UAAS,QAAQ;AAAA,IACvB;AAAA,EACF;AACA,cAAY,YAAY,IAAI,YAAY;AACxC,SAAO,OAAO,OAAO,IAAI,MAAM,SAAS;AACxC,WAAS,IAAI,GAAG,IAAI,WAAW,EAAE,GAAG;AAClC,SAAK,CAAC,IACJ,MAAM,SACF,MACA,MAAM,SACJ,gBAAgB,IAAI,CAAC,IACrB,KAAK,gBAAgB,IAAI,CAAC,GAAG,gBAAgB,IAAI,SAAS,CAAC,GAAG,CAAC;AAAA,EACzE;AACA,SAAO;AACT;AAWO,SAAS,wBACd,iBACAA,SACA,KACA,QACA,GACA,aACA;AACA,MAAI,OAAOA,SAAQ;AACjB,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,IAAI,gBAAgBA,UAAS,SAAS,CAAC,GAAG;AAC5C,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAMA,SAAQA,UAAS,MAAM;AAC1D,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,MAAM,CAAC,IAAI,GAAG;AAChC,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAM,MAAM,QAAQ,GAAG;AACpD,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,gBAAgBA,UAAS,SAAS,CAAC,GAAG;AAC7C,WAAO,gBAAgB,MAAMA,SAAQA,UAAS,MAAM;AAAA,EACtD;AACA,MAAI,KAAKA,UAAS;AAClB,MAAI,KAAK,MAAM;AACf,SAAO,KAAK,IAAI;AACd,UAAM,MAAO,KAAK,MAAO;AACzB,QAAI,IAAI,iBAAiB,MAAM,KAAK,SAAS,CAAC,GAAG;AAC/C,WAAK;AAAA,IACP,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACA,QAAM,KAAK,gBAAgB,KAAK,SAAS,CAAC;AAC1C,MAAI,KAAK,IAAI;AACX,WAAO,gBAAgB,OAAO,KAAK,KAAK,SAAS,KAAK,KAAK,SAAS,MAAM;AAAA,EAC5E;AACA,QAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,CAAC;AAChD,QAAM,KAAK,IAAI,OAAO,KAAK;AAC3B,eAAa,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,SAAS,GAAG,EAAE,GAAG;AACnC,eAAW;AAAA,MACT;AAAA,QACE,iBAAiB,KAAK,KAAK,SAAS,CAAC;AAAA,QACrC,gBAAgB,KAAK,SAAS,CAAC;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAW,KAAK,CAAC;AACjB,SAAO;AACT;AAYO,SAAS,yBACd,iBACAA,SACA,MACA,QACA,GACA,aACA,aACA;AACA,MAAI,aAAa;AACf,WAAO;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK,KAAK,SAAS,CAAC;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACJ,MAAI,IAAI,gBAAgB,SAAS,CAAC,GAAG;AACnC,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAM,GAAG,MAAM;AAC5C,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,gBAAgB,SAAS,CAAC,IAAI,GAAG;AACnD,QAAI,aAAa;AACf,mBAAa,gBAAgB,MAAM,gBAAgB,SAAS,MAAM;AAClE,iBAAW,SAAS,CAAC,IAAI;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,QAAIA,WAAU,KAAK;AACjB;AAAA,IACF;AACA,QAAI,IAAI,gBAAgBA,UAAS,SAAS,CAAC,GAAG;AAC5C,aAAO;AAAA,IACT;AACA,QAAI,KAAK,gBAAgB,MAAM,CAAC,GAAG;AACjC,aAAO;AAAA,QACL;AAAA,QACAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;;;ACrMO,SAAS,YAAY,iBAAiBC,SAAQ,KAAK,QAAQ;AAChE,SAAOA,UAAS,MAAM,QAAQ;AAC5B,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,YAAM,MAAM,gBAAgBA,UAAS,CAAC;AACtC,sBAAgBA,UAAS,CAAC,IAAI,gBAAgB,MAAM,SAAS,CAAC;AAC9D,sBAAgB,MAAM,SAAS,CAAC,IAAI;AAAA,IACtC;AACA,IAAAA,WAAU;AACV,WAAO;AAAA,EACT;AACF;;;ACLO,SAAS,sBAAsB,iBAAiBC,SAAQ,KAAK,QAAQ;AAG1E,MAAI,OAAO;AACX,MAAI,KAAK,gBAAgB,MAAM,MAAM;AACrC,MAAI,KAAK,gBAAgB,MAAM,SAAS,CAAC;AACzC,SAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,UAAM,KAAK,gBAAgBA,OAAM;AACjC,UAAM,KAAK,gBAAgBA,UAAS,CAAC;AACrC,aAAS,KAAK,OAAO,KAAK;AAC1B,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO,SAAS,IAAI,SAAY,OAAO;AACzC;AAeO,SAAS,uBACd,iBACAA,SACA,MACA,QACA,OACA;AACA,UAAQ,UAAU,SAAY,QAAQ;AACtC,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,cAAc;AAAA,MAClB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,MAAM,GAAG;AACX,UAAK,SAAS,eAAiB,CAAC,SAAS,CAAC,aAAc;AACtD,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,UAAK,SAAS,CAAC,eAAiB,CAAC,SAAS,aAAc;AACtD,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAeO,SAAS,wBACd,iBACAA,SACA,OACA,QACA,OACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,CAAC,uBAAuB,iBAAiBA,SAAQ,MAAM,QAAQ,KAAK,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,QAAQ;AACf,MAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAeO,SAAS,kBACd,iBACAA,SACA,MACA,QACA,OACA;AACA,UAAQ,UAAU,SAAY,QAAQ;AACtC,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,cAAc;AAAA,MAClB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,UACJ,MAAM,IACD,SAAS,eAAiB,CAAC,SAAS,CAAC,cACrC,SAAS,CAAC,eAAiB,CAAC,SAAS;AAC5C,QAAI,SAAS;AACX,kBAAmB,iBAAiBA,SAAQ,KAAK,MAAM;AAAA,IACzD;AACA,IAAAA,UAAS;AAAA,EACX;AACA,SAAOA;AACT;AAeO,SAAS,uBACd,iBACAA,SACA,OACA,QACA,OACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,IAAAA,UAAS;AAAA,MACP;AAAA,MACAA;AAAA,MACA,MAAM,CAAC;AAAA,MACP;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;AASO,SAAS,YAAY,iBAAiB,MAAM;AACjD,QAAM,QAAQ,CAAC;AACf,MAAIA,UAAS;AACb,MAAI,eAAe;AACnB,MAAI;AACJ,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAElB,UAAM,cAAc,sBAAsB,iBAAiBA,SAAQ,KAAK,CAAC;AACzE,QAAI,qBAAqB,QAAW;AAClC,yBAAmB;AAAA,IACrB;AACA,QAAI,gBAAgB,kBAAkB;AACpC,YAAM,KAAK,KAAK,MAAM,cAAc,IAAI,CAAC,CAAC;AAAA,IAC5C,OAAO;AACL,UAAI,MAAM,WAAW,GAAG;AACtB;AAAA,MACF;AACA,YAAM,MAAM,SAAS,CAAC,EAAE,KAAK,KAAK,YAAY,CAAC;AAAA,IACjD;AACA,mBAAe,IAAI;AACnB,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;;;ACxHO,SAAS,eACd,iBACAC,SACA,KACA,QACA,kBACA,2BACA,kBACA;AACA,QAAM,KAAK,MAAMA,WAAU;AAC3B,MAAI,IAAI,GAAG;AACT,WAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,gCAA0B,kBAAkB,IAAI,gBAAgBA,OAAM;AACtE,gCAA0B,kBAAkB,IAC1C,gBAAgBA,UAAS,CAAC;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,UAAQ,CAAC,IAAI;AACb,UAAQ,IAAI,CAAC,IAAI;AAEjB,QAAM,QAAQ,CAACA,SAAQ,MAAM,MAAM;AACnC,MAAI,QAAQ;AACZ,SAAO,MAAM,SAAS,GAAG;AACvB,UAAM,OAAO,MAAM,IAAI;AACvB,UAAM,QAAQ,MAAM,IAAI;AACxB,QAAI,qBAAqB;AACzB,UAAM,KAAK,gBAAgB,KAAK;AAChC,UAAM,KAAK,gBAAgB,QAAQ,CAAC;AACpC,UAAM,KAAK,gBAAgB,IAAI;AAC/B,UAAM,KAAK,gBAAgB,OAAO,CAAC;AACnC,aAAS,IAAI,QAAQ,QAAQ,IAAI,MAAM,KAAK,QAAQ;AAClD,YAAM,IAAI,gBAAgB,CAAC;AAC3B,YAAM,IAAI,gBAAgB,IAAI,CAAC;AAC/B,YAAMC,mBAAkB,uBAAuB,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AACnE,UAAIA,mBAAkB,oBAAoB;AACxC,gBAAQ;AACR,6BAAqBA;AAAA,MACvB;AAAA,IACF;AACA,QAAI,qBAAqB,kBAAkB;AACzC,eAAS,QAAQD,WAAU,MAAM,IAAI;AACrC,UAAI,QAAQ,SAAS,OAAO;AAC1B,cAAM,KAAK,OAAO,KAAK;AAAA,MACzB;AACA,UAAI,QAAQ,SAAS,MAAM;AACzB,cAAM,KAAK,OAAO,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,QAAI,QAAQ,CAAC,GAAG;AACd,gCAA0B,kBAAkB,IAC1C,gBAAgBA,UAAS,IAAI,MAAM;AACrC,gCAA0B,kBAAkB,IAC1C,gBAAgBA,UAAS,IAAI,SAAS,CAAC;AAAA,IAC3C;AAAA,EACF;AACA,SAAO;AACT;AAcO,SAAS,oBACd,iBACAA,SACA,MACA,QACA,kBACA,2BACA,kBACA,gBACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,uBAAmB;AAAA,MACjB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,mBAAe,KAAK,gBAAgB;AACpC,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAwGO,SAAS,KAAK,OAAO,WAAW;AACrC,SAAO,YAAY,KAAK,MAAM,QAAQ,SAAS;AACjD;AAqBO,SAAS,SACd,iBACAE,SACA,KACA,QACA,WACA,2BACA,kBACA;AAEA,MAAIA,WAAU,KAAK;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,KAAK,gBAAgBA,OAAM,GAAG,SAAS;AAChD,MAAI,KAAK,KAAK,gBAAgBA,UAAS,CAAC,GAAG,SAAS;AACpD,EAAAA,WAAU;AAEV,4BAA0B,kBAAkB,IAAI;AAChD,4BAA0B,kBAAkB,IAAI;AAGhD,MAAI,IAAI;AACR,KAAG;AACD,SAAK,KAAK,gBAAgBA,OAAM,GAAG,SAAS;AAC5C,SAAK,KAAK,gBAAgBA,UAAS,CAAC,GAAG,SAAS;AAChD,IAAAA,WAAU;AACV,QAAIA,WAAU,KAAK;AAKjB,gCAA0B,kBAAkB,IAAI;AAChD,gCAA0B,kBAAkB,IAAI;AAChD,aAAO;AAAA,IACT;AAAA,EACF,SAAS,MAAM,MAAM,MAAM;AAC3B,SAAOA,UAAS,KAAK;AAEnB,UAAM,KAAK,KAAK,gBAAgBA,OAAM,GAAG,SAAS;AAClD,UAAM,KAAK,KAAK,gBAAgBA,UAAS,CAAC,GAAG,SAAS;AACtD,IAAAA,WAAU;AAEV,QAAI,MAAM,MAAM,MAAM,IAAI;AACxB;AAAA,IACF;AAEA,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,KAAK;AAEjB,UAAM,MAAM,KAAK;AACjB,UAAM,MAAM,KAAK;AAIjB,QACE,MAAM,OAAO,MAAM,QACjB,MAAM,KAAK,MAAM,OAAQ,OAAO,OAAQ,MAAM,KAAK,MAAM,SACzD,MAAM,KAAK,MAAM,OAAQ,OAAO,OAAQ,MAAM,KAAK,MAAM,MAC3D;AAEA,WAAK;AACL,WAAK;AACL;AAAA,IACF;AAIA,8BAA0B,kBAAkB,IAAI;AAChD,8BAA0B,kBAAkB,IAAI;AAChD,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACP;AAEA,4BAA0B,kBAAkB,IAAI;AAChD,4BAA0B,kBAAkB,IAAI;AAChD,SAAO;AACT;AAcO,SAAS,cACd,iBACAA,SACA,MACA,QACA,WACA,2BACA,kBACA,gBACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,uBAAmB;AAAA,MACjB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,mBAAe,KAAK,gBAAgB;AACpC,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAcO,SAAS,mBACd,iBACAA,SACA,OACA,QACA,WACA,2BACA,kBACA,iBACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AAEpB,UAAM,iBAAiB,CAAC;AACxB,uBAAmB;AAAA,MACjB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,oBAAgB,KAAK,cAAc;AACnC,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;AC/cA,IAAM,SAAN,MAAM,gBAAe,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlC,YAAY,QAAQ,QAAQ,QAAQ;AAClC,UAAM;AACN,QAAI,WAAW,UAAa,WAAW,QAAW;AAChD,WAAK,mBAAmB,QAAQ,MAAM;AAAA,IACxC,OAAO;AACL,eAAS,SAAS,SAAS;AAC3B,WAAK,mBAAmB,QAAQ,QAAQ,MAAM;AAAA,IAChD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,SAAS,IAAI;AAAA,MACjB,KAAK,gBAAgB,MAAM;AAAA,MAC3B;AAAA,MACA,KAAK;AAAA,IACP;AACA,WAAO,gBAAgB,IAAI;AAC3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,UAAM,kBAAkB,KAAK;AAC7B,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,UAAMC,mBAAkB,KAAK,KAAK,KAAK;AACvC,QAAIA,mBAAkB,oBAAoB;AACxC,UAAIA,qBAAoB,GAAG;AACzB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,uBAAa,CAAC,IAAI,gBAAgB,CAAC;AAAA,QACrC;AAAA,MACF,OAAO;AACL,cAAM,QAAQ,KAAK,UAAU,IAAI,KAAK,KAAKA,gBAAe;AAC1D,qBAAa,CAAC,IAAI,gBAAgB,CAAC,IAAI,QAAQ;AAC/C,qBAAa,CAAC,IAAI,gBAAgB,CAAC,IAAI,QAAQ;AAC/C,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,uBAAa,CAAC,IAAI,gBAAgB,CAAC;AAAA,QACrC;AAAA,MACF;AACA,mBAAa,SAAS,KAAK;AAC3B,aAAOA;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,GAAG,GAAG;AACf,UAAM,kBAAkB,KAAK;AAC7B,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,UAAM,KAAK,IAAI,gBAAgB,CAAC;AAChC,WAAO,KAAK,KAAK,KAAK,MAAM,KAAK,kBAAkB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,QAAQ;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,gBAAgB,KAAK,MAAM,IAAI,gBAAgB,CAAC;AAC/D,WAAO;AAAA,MACL,gBAAgB,CAAC,IAAI;AAAA,MACrB,gBAAgB,CAAC,IAAI;AAAA,MACrB,gBAAgB,CAAC,IAAI;AAAA,MACrB,gBAAgB,CAAC,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK,KAAK,KAAK,kBAAkB,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,UAAM,KAAK,KAAK,gBAAgB,KAAK,MAAM,IAAI,KAAK,gBAAgB,CAAC;AACrE,UAAM,KAAK,KAAK,gBAAgB,KAAK,SAAS,CAAC,IAAI,KAAK,gBAAgB,CAAC;AACzE,WAAO,KAAK,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,UAAM,eAAe,KAAK,UAAU;AACpC,QAAI,WAAW,QAAQ,YAAY,GAAG;AACpC,YAAM,SAAS,KAAK,UAAU;AAE9B,UAAI,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACpD,eAAO;AAAA,MACT;AACA,UAAI,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACpD,eAAO;AAAA,MACT;AAEA,aAAO,cAAc,QAAQ,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAAA,IACnE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK,gBAAgB,MAAM,IAAI,KAAK,gBAAgB,CAAC;AACpE,UAAM,kBAAkB,OAAO,MAAM;AACrC,oBAAgB,MAAM,IAAI,gBAAgB,CAAC,IAAI;AAC/C,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,sBAAgB,SAAS,CAAC,IAAI,OAAO,CAAC;AAAA,IACxC;AACA,SAAK,mBAAmB,KAAK,QAAQ,eAAe;AACpD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,QAAQ,QAAQ,QAAQ;AACzC,SAAK,UAAU,QAAQ,QAAQ,CAAC;AAChC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AAEA,UAAM,kBAAkB,KAAK;AAC7B,QAAIC,UAAS,kBAAkB,iBAAiB,GAAG,QAAQ,KAAK,MAAM;AACtE,oBAAgBA,SAAQ,IAAI,gBAAgB,CAAC,IAAI;AACjD,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,sBAAgBA,SAAQ,IAAI,gBAAgB,CAAC;AAAA,IAC/C;AACA,oBAAgB,SAASA;AACzB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAeC,cAAa,QAAQ;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrC,UAAU,QAAQ;AAChB,SAAK,gBAAgB,KAAK,MAAM,IAAI,KAAK,gBAAgB,CAAC,IAAI;AAC9D,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAO,QAAQ;AACpB,UAAM,SAAS,KAAK,UAAU;AAC9B,UAAM,SAAS,KAAK,UAAU;AAC9B,SAAK;AAAA,MACH,OAAO,QAAQ,GAAG,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAAA,IAChE;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAwBA,OAAO,UAAU;AACjB,IAAO,iBAAQ;;;AClQf,IAAM,qBAAN,MAAM,4BAA2B,iBAAS;AAAA;AAAA;AAAA;AAAA,EAIxC,YAAY,YAAY;AACtB,UAAM;AAMN,SAAK,cAAc;AAMnB,SAAK,oBAAoB,CAAC;AAE1B,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,4BAA4B;AAC1B,SAAK,kBAAkB,QAAQ,aAAa;AAC5C,SAAK,kBAAkB,SAAS;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,WAAK,kBAAkB;AAAA,QACrB,OAAO,WAAW,CAAC,GAAG,kBAAU,QAAQ,KAAK,SAAS,IAAI;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,qBAAqB,IAAI;AAAA,MAC7B,gBAAgB,KAAK,WAAW;AAAA,IAClC;AACA,uBAAmB,gBAAgB,IAAI;AACvC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,2BAAqB,WAAW,CAAC,EAAE;AAAA,QACjC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,GAAG,GAAG;AACf,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,UAAI,WAAW,CAAC,EAAE,WAAW,GAAG,CAAC,GAAG;AAClC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,QAAQ;AACpB,wBAAoB,MAAM;AAC1B,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,aAAO,QAAQ,WAAW,CAAC,EAAE,UAAU,CAAC;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,gBAAgB,KAAK,WAAW;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAE5B,QAAI,kBAAkB,CAAC;AACvB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,UAAI,WAAW,CAAC,EAAE,QAAQ,MAAM,KAAK,QAAQ,GAAG;AAC9C,0BAAkB,gBAAgB;AAAA;AAAA,UAE9B,WAAW,CAAC,EACZ,4BAA4B;AAAA,QAChC;AAAA,MACF,OAAO;AACL,wBAAgB,KAAK,WAAW,CAAC,CAAC;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,kBAAkB;AACtC,QAAI,KAAK,+BAA+B,KAAK,YAAY,GAAG;AAC1D,WAAK,2CAA2C;AAChD,WAAK,6BAA6B,KAAK,YAAY;AAAA,IACrD;AACA,QACE,mBAAmB,KAClB,KAAK,6CAA6C,KACjD,mBAAmB,KAAK,0CAC1B;AACA,aAAO;AAAA,IACT;AAEA,UAAM,uBAAuB,CAAC;AAC9B,UAAM,aAAa,KAAK;AACxB,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,YAAM,WAAW,WAAW,CAAC;AAC7B,YAAM,qBACJ,SAAS,sBAAsB,gBAAgB;AACjD,2BAAqB,KAAK,kBAAkB;AAC5C,UAAI,uBAAuB,UAAU;AACnC,qBAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,YAAY;AACd,YAAM,+BAA+B,IAAI;AAAA,QACvC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,2CAA2C;AAChD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,UAAI,WAAW,CAAC,EAAE,iBAAiB,MAAM,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK,YAAY,WAAW;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAO,QAAQ;AACpB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,OAAO,OAAO,MAAM;AAAA,IACpC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,IAAI,IAAI,QAAQ;AACpB,QAAI,CAAC,QAAQ;AACX,eAAS,UAAU,KAAK,UAAU,CAAC;AAAA,IACrC;AACA,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,MAAM,IAAI,IAAI,MAAM;AAAA,IACpC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,YAAY;AACxB,SAAK,mBAAmB,gBAAgB,UAAU,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,YAAY;AAC7B,SAAK,0BAA0B;AAC/B,SAAK,cAAc;AACnB,SAAK,wBAAwB;AAC7B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,eAAe,aAAa;AAC1B,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,eAAe,WAAW;AAAA,IAC1C;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,QAAQ,QAAQ;AACxB,UAAM,aAAa,KAAK;AACxB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,iBAAW,CAAC,EAAE,UAAU,QAAQ,MAAM;AAAA,IACxC;AACA,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,SAAK,0BAA0B;AAC/B,UAAM,gBAAgB;AAAA,EACxB;AACF;AAMA,SAAS,gBAAgB,YAAY;AACnC,SAAO,WAAW,IAAI,CAAC,aAAa,SAAS,MAAM,CAAC;AACtD;AAEA,IAAO,6BAAQ;;;ACpVR,SAAS,WAAW,iBAAiBC,SAAQ,KAAK,QAAQ;AAC/D,MAAI,YAAY;AAChB,QAAM,KAAK,gBAAgB,MAAM,MAAM;AACvC,QAAM,KAAK,gBAAgB,MAAM,SAAS,CAAC;AAC3C,MAAI,MAAM;AACV,MAAI,MAAM;AACV,SAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,UAAM,MAAM,gBAAgBA,OAAM,IAAI;AACtC,UAAM,MAAM,gBAAgBA,UAAS,CAAC,IAAI;AAC1C,iBAAa,MAAM,MAAM,MAAM;AAC/B,UAAM;AACN,UAAM;AAAA,EACR;AACA,SAAO,YAAY;AACrB;AASO,SAAS,YAAY,iBAAiBA,SAAQ,MAAM,QAAQ;AACjE,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,YAAQ,WAAW,iBAAiBA,SAAQ,KAAK,MAAM;AACvD,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AASO,SAASC,cAAa,iBAAiBD,SAAQ,OAAO,QAAQ;AACnE,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,YAAQ,YAAY,iBAAiBA,SAAQ,MAAM,MAAM;AACzD,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;AC1CA,SAAS,cACP,iBACA,SACA,SACA,QACA,GACA,GACA,cACA;AACA,QAAM,KAAK,gBAAgB,OAAO;AAClC,QAAM,KAAK,gBAAgB,UAAU,CAAC;AACtC,QAAM,KAAK,gBAAgB,OAAO,IAAI;AACtC,QAAM,KAAK,gBAAgB,UAAU,CAAC,IAAI;AAC1C,MAAIE;AACJ,MAAI,OAAO,KAAK,OAAO,GAAG;AACxB,IAAAA,UAAS;AAAA,EACX,OAAO;AACL,UAAM,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK;AAC5D,QAAI,IAAI,GAAG;AACT,MAAAA,UAAS;AAAA,IACX,WAAW,IAAI,GAAG;AAChB,eAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,qBAAa,CAAC,IAAI;AAAA,UAChB,gBAAgB,UAAU,CAAC;AAAA,UAC3B,gBAAgB,UAAU,CAAC;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AACA,mBAAa,SAAS;AACtB;AAAA,IACF,OAAO;AACL,MAAAA,UAAS;AAAA,IACX;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,iBAAa,CAAC,IAAI,gBAAgBA,UAAS,CAAC;AAAA,EAC9C;AACA,eAAa,SAAS;AACxB;AAYO,SAAS,gBAAgB,iBAAiBA,SAAQ,KAAK,QAAQ,KAAK;AACzE,MAAI,KAAK,gBAAgBA,OAAM;AAC/B,MAAI,KAAK,gBAAgBA,UAAS,CAAC;AACnC,OAAKA,WAAU,QAAQA,UAAS,KAAKA,WAAU,QAAQ;AACrD,UAAM,KAAK,gBAAgBA,OAAM;AACjC,UAAM,KAAK,gBAAgBA,UAAS,CAAC;AACrC,UAAM,eAAe,gBAAU,IAAI,IAAI,IAAI,EAAE;AAC7C,QAAI,eAAe,KAAK;AACtB,YAAM;AAAA,IACR;AACA,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO;AACT;AAUO,SAAS,qBACd,iBACAA,SACA,MACA,QACA,KACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,gBAAgB,iBAAiBA,SAAQ,KAAK,QAAQ,GAAG;AAC/D,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAUO,SAAS,0BACd,iBACAA,SACA,OACA,QACA,KACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,qBAAqB,iBAAiBA,SAAQ,MAAM,QAAQ,GAAG;AACrE,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;AAgBO,SAAS,mBACd,iBACAA,SACA,KACA,QACA,UACA,QACA,GACA,GACA,cACA,oBACA,UACA;AACA,MAAIA,WAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,MAAI,GAAGC;AACP,MAAI,aAAa,GAAG;AAElB,IAAAA,mBAAkB;AAAA,MAChB;AAAA,MACA;AAAA,MACA,gBAAgBD,OAAM;AAAA,MACtB,gBAAgBA,UAAS,CAAC;AAAA,IAC5B;AACA,QAAIC,mBAAkB,oBAAoB;AACxC,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,qBAAa,CAAC,IAAI,gBAAgBD,UAAS,CAAC;AAAA,MAC9C;AACA,mBAAa,SAAS;AACtB,aAAOC;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,aAAW,WAAW,WAAW,CAAC,KAAK,GAAG;AAC1C,MAAI,QAAQD,UAAS;AACrB,SAAO,QAAQ,KAAK;AAClB;AAAA,MACE;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAC,mBAAkB,gBAAU,GAAG,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC1D,QAAIA,mBAAkB,oBAAoB;AACxC,2BAAqBA;AACrB,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,qBAAa,CAAC,IAAI,SAAS,CAAC;AAAA,MAC9B;AACA,mBAAa,SAAS;AACtB,eAAS;AAAA,IACX,OAAO;AAWL,eACE,SACA,KAAK;AAAA,SACD,KAAK,KAAKA,gBAAe,IAAI,KAAK,KAAK,kBAAkB,KACzD,WACA;AAAA,QACF;AAAA,MACF;AAAA,IACJ;AAAA,EACF;AACA,MAAI,QAAQ;AAEV;AAAA,MACE;AAAA,MACA,MAAM;AAAA,MACND;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAC,mBAAkB,gBAAU,GAAG,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC1D,QAAIA,mBAAkB,oBAAoB;AACxC,2BAAqBA;AACrB,WAAK,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC3B,qBAAa,CAAC,IAAI,SAAS,CAAC;AAAA,MAC9B;AACA,mBAAa,SAAS;AAAA,IACxB;AAAA,EACF;AACA,SAAO;AACT;AAgBO,SAAS,wBACd,iBACAD,SACA,MACA,QACA,UACA,QACA,GACA,GACA,cACA,oBACA,UACA;AACA,aAAW,WAAW,WAAW,CAAC,KAAK,GAAG;AAC1C,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,yBAAqB;AAAA,MACnB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAA,UAAS;AAAA,EACX;AACA,SAAO;AACT;AAgBO,SAAS,6BACd,iBACAA,SACA,OACA,QACA,UACA,QACA,GACA,GACA,cACA,oBACA,UACA;AACA,aAAW,WAAW,WAAW,CAAC,KAAK,GAAG;AAC1C,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,yBAAqB;AAAA,MACnB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;ACnUO,SAAS,mBACd,iBACAE,SACA,KACA,QACAC,cACA;AACA,EAAAA,eAAcA,iBAAgB,SAAYA,eAAc,CAAC;AACzD,MAAI,IAAI;AACR,WAAS,IAAID,SAAQ,IAAI,KAAK,KAAK,QAAQ;AACzC,IAAAC,aAAY,GAAG,IAAI,gBAAgB,MAAM,GAAG,IAAI,MAAM;AAAA,EACxD;AACA,EAAAA,aAAY,SAAS;AACrB,SAAOA;AACT;AAUO,SAAS,wBACd,iBACAD,SACA,MACA,QACA,cACA;AACA,iBAAe,iBAAiB,SAAY,eAAe,CAAC;AAC5D,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,UAAM,MAAM,KAAK,CAAC;AAClB,iBAAa,GAAG,IAAI;AAAA,MAClB;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,CAAC;AAAA,IAChB;AACA,IAAAA,UAAS;AAAA,EACX;AACA,eAAa,SAAS;AACtB,SAAO;AACT;AAWO,SAAS,6BACd,iBACAA,SACA,OACA,QACA,eACA;AACA,kBAAgB,kBAAkB,SAAY,gBAAgB,CAAC;AAC/D,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,kBAAc,GAAG,IACf,KAAK,WAAW,KAAK,KAAK,CAAC,MAAMA,UAC7B,CAAC,IACD;AAAA,MACE;AAAA,MACAA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,CAAC;AAAA,IACjB;AACN,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,gBAAc,SAAS;AACvB,SAAO;AACT;;;AC5EA,IAAM,aAAN,MAAM,oBAAmB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,YAAYE,cAAa,QAAQ;AAC/B,UAAM;AAMN,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAEzB,QAAI,WAAW,UAAa,CAAC,MAAM,QAAQA,aAAY,CAAC,CAAC,GAAG;AAC1D,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AAAA,IACF,OAAO;AACL,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,WAAO,IAAI,YAAW,KAAK,gBAAgB,MAAM,GAAG,KAAK,MAAM;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK,gBAAgB;AAAA,UACrB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,kBAAkB;AAE9C,UAAM,4BAA4B,CAAC;AACnC,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,YAAW,2BAA2B,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAeA,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,qBAAQ;;;AClLR,SAAS,QAAQ,iBAAiBC,SAAQ,KAAK,QAAQ,UAAU;AACtE,MAAI;AACJ,EAAAA,WAAU;AACV,SAAOA,UAAS,KAAKA,WAAU,QAAQ;AACrC,UAAM;AAAA,MACJ,gBAAgB,MAAMA,UAAS,QAAQA,OAAM;AAAA,MAC7C,gBAAgB,MAAMA,SAAQA,UAAS,MAAM;AAAA,IAC/C;AACA,QAAI,KAAK;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AASO,SAAS,qBAAqB,UAAU,UAAU;AACvD,QAAM,CAAC,GAAG,CAAC,IAAI;AACf,QAAM,CAAC,GAAG,CAAC,IAAI;AACf,QAAM,MACF,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAC1D,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAC9D,QAAM,MACF,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAC1D,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAG9D,MAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxC,WAAO,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE;AAAA,EAC5D;AACA,SAAO;AACT;;;ACjCO,SAAS,qBACd,iBACAC,SACA,KACA,QACA,QACA,mBACA;AACA,sBACE,qBACA,sBAAsB,YAAY,GAAG,iBAAiBA,SAAQ,KAAK,MAAM;AAC3E,MAAI,CAAC,WAAW,QAAQ,iBAAiB,GAAG;AAC1C,WAAO;AAAA,EACT;AACA,MACG,kBAAkB,CAAC,KAAK,OAAO,CAAC,KAAK,kBAAkB,CAAC,KAAK,OAAO,CAAC,KACrE,kBAAkB,CAAC,KAAK,OAAO,CAAC,KAAK,kBAAkB,CAAC,KAAK,OAAO,CAAC,GACtE;AACA,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,SAAU,QAAQ,QAAQ;AACxB,aAAO,kBAAkB,QAAQ,QAAQ,MAAM;AAAA,IACjD;AAAA,EACF;AACF;AAUO,SAAS,0BACd,iBACAA,SACA,MACA,QACA,QACA;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,QACE,qBAAqB,iBAAiBA,SAAQ,KAAK,CAAC,GAAG,QAAQ,MAAM,GACrE;AACA,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,KAAK,CAAC;AAAA,EACjB;AACA,SAAO;AACT;AAUO,SAAS,qBACd,iBACAA,SACA,KACA,QACA,QACA;AACA,MAAI,qBAAqB,iBAAiBA,SAAQ,KAAK,QAAQ,MAAM,GAAG;AACtE,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,MACE;AAAA,IACE;AAAA,IACAA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,CAAC;AAAA,IACR,OAAO,CAAC;AAAA,EACV,GACA;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAUO,SAAS,0BACd,iBACAA,SACA,MACA,QACA,QACA;AACA,MAAI,CAAC,qBAAqB,iBAAiBA,SAAQ,KAAK,CAAC,GAAG,QAAQ,MAAM,GAAG;AAC3E,WAAO;AAAA,EACT;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,QACE;AAAA,MACE;AAAA,MACA,KAAK,IAAI,CAAC;AAAA,MACV,KAAK,CAAC;AAAA,MACN;AAAA,MACA;AAAA,IACF,GACA;AACA,UACE,CAAC;AAAA,QACC;AAAA,QACA,KAAK,IAAI,CAAC;AAAA,QACV,KAAK,CAAC;AAAA,QACN;AAAA,QACA;AAAA,MACF,GACA;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAUO,SAAS,+BACd,iBACAA,SACA,OACA,QACA,QACA;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,UAAM,OAAO,MAAM,CAAC;AACpB,QACE,0BAA0B,iBAAiBA,SAAQ,MAAM,QAAQ,MAAM,GACvE;AACA,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,KAAK,KAAK,SAAS,CAAC;AAAA,EAC/B;AACA,SAAO;AACT;;;ACtNO,SAAS,iBAAiB,iBAAiBC,SAAQ,KAAK,QAAQ;AACrE,MAAI,KAAK,gBAAgBA,OAAM;AAC/B,MAAI,KAAK,gBAAgBA,UAAS,CAAC;AACnC,MAAI,SAAS;AACb,WAAS,IAAIA,UAAS,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAClD,UAAM,KAAK,gBAAgB,CAAC;AAC5B,UAAM,KAAK,gBAAgB,IAAI,CAAC;AAChC,cAAU,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,GAAG;AACjE,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAO;AACT;;;ACFA,IAAM,aAAN,MAAM,oBAAmB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,YAAYC,cAAa,QAAQ;AAC/B,UAAM;AAMN,SAAK,gBAAgB;AAMrB,SAAK,wBAAwB;AAM7B,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAEzB,QAAI,WAAW,UAAa,CAAC,MAAM,QAAQA,aAAY,CAAC,CAAC,GAAG;AAC1D,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AAAA,IACF,OAAO;AACL,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY;AAC3B,IAAAC,QAAO,KAAK,iBAAiB,UAAU;AACvC,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,aAAa,IAAI;AAAA,MACrB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,IACP;AACA,eAAW,gBAAgB,IAAI;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK,gBAAgB;AAAA,UACrB,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,eAAe,UAAU;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,iBAAiB,GAAG,aAAa;AAC/B,QAAI,KAAK,UAAU,SAAS,KAAK,UAAU,QAAQ;AACjD,aAAO;AAAA,IACT;AACA,kBAAc,gBAAgB,SAAY,cAAc;AACxD,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,gBAAgB,UAAU,MAAM;AAC9B,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,KAAK,yBAAyB,KAAK,YAAY,GAAG;AACpD,WAAK,gBAAgB,KAAK;AAAA,QACxB;AAAA,QACA,KAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,wBAAwB,KAAK,YAAY;AAAA,IAChD;AACA;AAAA;AAAA,MAAqC,KAAK;AAAA;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,kBAAkB;AAE9C,UAAM,4BAA4B,CAAC;AACnC,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,YAAW,2BAA2B,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,MACL;AAAA,MACA,KAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAeD,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,qBAAQ;;;ACtSf,IAAM,kBAAN,MAAM,yBAAwB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ3C,YAAYE,cAAa,QAAQ,MAAM;AACrC,UAAM;AAMN,SAAK,QAAQ,CAAC;AAMd,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAEzB,QAAI,MAAM,QAAQA,aAAY,CAAC,CAAC,GAAG;AACjC,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF,WAAW,WAAW,UAAa,MAAM;AACvC,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AACA,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,YAAM;AAAA;AAAA,QAAgDA;AAAA;AAEtD,YAAM,kBAAkB,CAAC;AACzB,YAAMC,QAAO,CAAC;AACd,eAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,cAAM,aAAa,YAAY,CAAC;AAChC,QAAAC,QAAO,iBAAiB,WAAW,mBAAmB,CAAC;AACvD,QAAAD,MAAK,KAAK,gBAAgB,MAAM;AAAA,MAClC;AACA,YAAME,UACJ,YAAY,WAAW,IACnB,KAAK,UAAU,IACf,YAAY,CAAC,EAAE,UAAU;AAC/B,WAAK,mBAAmBA,SAAQ,eAAe;AAC/C,WAAK,QAAQF;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY;AAC3B,IAAAC,QAAO,KAAK,iBAAiB,WAAW,mBAAmB,EAAE,MAAM,CAAC;AACpE,SAAK,MAAM,KAAK,KAAK,gBAAgB,MAAM;AAC3C,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,kBAAkB,IAAI;AAAA,MAC1B,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,MACL,KAAK,MAAM,MAAM;AAAA,IACnB;AACA,oBAAgB,gBAAgB,IAAI;AACpC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,iBAAiB,GAAG,aAAa,aAAa;AAC5C,QACG,KAAK,UAAU,SAAS,KAAK,UAAU,UACxC,KAAK,gBAAgB,WAAW,GAChC;AACA,aAAO;AAAA,IACT;AACA,kBAAc,gBAAgB,SAAY,cAAc;AACxD,kBAAc,gBAAgB,SAAY,cAAc;AACxD,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,OAAO;AACnB,QAAI,QAAQ,KAAK,KAAK,MAAM,UAAU,OAAO;AAC3C,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB;AAAA,QACnB,UAAU,IAAI,IAAI,KAAK,MAAM,QAAQ,CAAC;AAAA,QACtC,KAAK,MAAM,KAAK;AAAA,MAClB;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,UAAM,kBAAkB,KAAK;AAC7B,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AAEpB,UAAM,cAAc,CAAC;AACrB,QAAIE,UAAS;AACb,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,aAAa,IAAI;AAAA,QACrB,gBAAgB,MAAMA,SAAQ,GAAG;AAAA,QACjC;AAAA,MACF;AACA,kBAAY,KAAK,UAAU;AAC3B,MAAAA,UAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,UAAM,OAAO,KAAK;AAClB,QAAI,QAAQ;AACZ,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,gBAAU;AAAA,QACR,KAAK;AAAA,QACL;AAAA,QACA,KAAK,CAAC;AAAA,QACN,KAAK;AAAA,MACP;AACA,cAAQ,KAAK,CAAC;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAEjB,UAAM,YAAY,CAAC;AACnB,UAAM,kBAAkB,KAAK;AAC7B,QAAIA,UAAS;AACb,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,WAAW;AAAA,QACf;AAAA,QACAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,MAAAF,QAAO,WAAW,QAAQ;AAC1B,MAAAE,UAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,kBAAkB;AAE9C,UAAM,4BAA4B,CAAC;AAEnC,UAAM,iBAAiB,CAAC;AACxB,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,iBAAgB,2BAA2B,MAAM,cAAc;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAeJ,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,UAAM,OAAO;AAAA,MACX,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,SAAK,gBAAgB,SAAS,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC1E,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,0BAAQ;;;ACzWf,IAAM,aAAN,MAAM,oBAAmB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,YAAYK,cAAa,QAAQ;AAC/B,UAAM;AACN,QAAI,UAAU,CAAC,MAAM,QAAQA,aAAY,CAAC,CAAC,GAAG;AAC5C,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AAAA,IACF,OAAO;AACL,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,OAAO;AACjB,IAAAC,QAAO,KAAK,iBAAiB,MAAM,mBAAmB,CAAC;AACvD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,aAAa,IAAI;AAAA,MACrB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,IACP;AACA,eAAW,gBAAgB,IAAI;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK,QAAQ;AAChE,YAAMC,mBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,gBAAgB,CAAC;AAAA,QACjB,gBAAgB,IAAI,CAAC;AAAA,MACvB;AACA,UAAIA,mBAAkB,oBAAoB;AACxC,6BAAqBA;AACrB,iBAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,uBAAa,CAAC,IAAI,gBAAgB,IAAI,CAAC;AAAA,QACzC;AACA,qBAAa,SAAS;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB;AACf,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO;AACd,UAAM,IAAI,KAAK,gBAAgB,SAAS,KAAK;AAC7C,QAAI,QAAQ,KAAK,KAAK,OAAO;AAC3B,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB;AAAA,QACnB,QAAQ,KAAK;AAAA,SACZ,QAAQ,KAAK,KAAK;AAAA,MACrB;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK;AAEpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK,QAAQ;AAChE,YAAM,QAAQ,IAAI,cAAM,gBAAgB,MAAM,GAAG,IAAI,MAAM,GAAG,MAAM;AACpE,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,gBAAgB,QAAQ,IAAI,IAAI,KAAK,QAAQ;AAChE,YAAM,IAAI,gBAAgB,CAAC;AAC3B,YAAM,IAAI,gBAAgB,IAAI,CAAC;AAC/B,UAAI,WAAW,QAAQ,GAAG,CAAC,GAAG;AAC5B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAeF,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,SAAK,gBAAgB,SAAS;AAAA,MAC5B,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,IACP;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,qBAAQ;;;AChLf,IAAM,UAAN,MAAM,iBAAgB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYnC,YAAYG,cAAa,QAAQ,MAAM;AACrC,UAAM;AAMN,SAAK,QAAQ,CAAC;AAMd,SAAK,6BAA6B;AAMlC,SAAK,qBAAqB;AAM1B,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAMzB,SAAK,oBAAoB;AAMzB,SAAK,2BAA2B;AAEhC,QAAI,WAAW,UAAa,MAAM;AAChC,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AACA,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiBC,aAAY;AAC3B,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkBA,YAAW,mBAAmB,EAAE,MAAM;AAAA,IAC/D,OAAO;AACL,MAAAC,QAAO,KAAK,iBAAiBD,YAAW,mBAAmB,CAAC;AAAA,IAC9D;AACA,SAAK,MAAM,KAAK,KAAK,gBAAgB,MAAM;AAC3C,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,UAAU,IAAI;AAAA,MAClB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,MACL,KAAK,MAAM,MAAM;AAAA,IACnB;AACA,YAAQ,gBAAgB,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,GAAG,GAAG;AACf,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,eAAe,OAAO;AACpB,QAAI;AACJ,QAAI,UAAU,QAAW;AACvB,wBAAkB,KAAK,2BAA2B,EAAE,MAAM;AAC1D,wBAAkB,iBAAiB,GAAG,KAAK,OAAO,KAAK,QAAQ,KAAK;AAAA,IACtE,OAAO;AACL,wBAAkB,KAAK;AAAA,IACzB;AAEA,WAAO,wBAAwB,iBAAiB,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,KAAK,8BAA8B,KAAK,YAAY,GAAG;AACzD,YAAM,aAAa,UAAU,KAAK,UAAU,CAAC;AAC7C,WAAK,qBAAqB;AAAA,QACxB,KAAK,2BAA2B;AAAA,QAChC;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,WAAK,6BAA6B,KAAK,YAAY;AAAA,IACrD;AACA;AAAA;AAAA,MACE,KAAK;AAAA;AAAA,EAET;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB;AACjB,WAAO,IAAI,cAAM,KAAK,qBAAqB,GAAG,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB;AACnB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,cAAc,OAAO;AACnB,QAAI,QAAQ,KAAK,KAAK,MAAM,UAAU,OAAO;AAC3C,aAAO;AAAA,IACT;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB;AAAA,QACnB,UAAU,IAAI,IAAI,KAAK,MAAM,QAAQ,CAAC;AAAA,QACtC,KAAK,MAAM,KAAK;AAAA,MAClB;AAAA,MACA,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,OAAO,KAAK;AAClB,UAAME,eAAc,CAAC;AACrB,QAAIC,UAAS;AACb,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,YAAM,MAAM,KAAK,CAAC;AAClB,YAAMH,cAAa,IAAI;AAAA,QACrB,gBAAgB,MAAMG,SAAQ,GAAG;AAAA,QACjC;AAAA,MACF;AACA,MAAAD,aAAY,KAAKF,WAAU;AAC3B,MAAAG,UAAS;AAAA,IACX;AACA,WAAOD;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,YAAM,kBAAkB,KAAK;AAC7B,UAAI,uBAAuB,iBAAiB,GAAG,KAAK,OAAO,KAAK,MAAM,GAAG;AACvE,aAAK,2BAA2B;AAAA,MAClC,OAAO;AACL,aAAK,2BAA2B,gBAAgB,MAAM;AACtD,aAAK,yBAAyB,SAAS;AAAA,UACrC,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA;AAAA;AAAA,MAAqC,KAAK;AAAA;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,kBAAkB;AAE9C,UAAM,4BAA4B,CAAC;AAEnC,UAAM,iBAAiB,CAAC;AACxB,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,KAAK,gBAAgB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,SAAQ,2BAA2B,MAAM,cAAc;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAeH,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,UAAM,OAAO;AAAA,MACX,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,SAAK,gBAAgB,SAAS,KAAK,WAAW,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC1E,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,kBAAQ;AAkCR,SAAS,WAAW,QAAQ;AACjC,MAAI,QAAQ,MAAM,GAAG;AACnB,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AACA,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO,IAAI,QAAQ,iBAAiB,MAAM,CAAC,gBAAgB,MAAM,CAAC;AACpE;AAWO,SAAS,WAAW,QAAQ,OAAO,OAAO;AAC/C,UAAQ,QAAQ,QAAQ;AACxB,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,cAAc,UAAU,QAAQ;AACtC,QAAM,kBAAkB,IAAI,MAAM,WAAW;AAC7C,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ;AAC5C,oBAAgB,CAAC,IAAI;AACrB,oBAAgB,IAAI,CAAC,IAAI;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,sBAAgB,IAAI,CAAC,IAAI,OAAO,CAAC;AAAA,IACnC;AAAA,EACF;AACA,QAAM,OAAO,CAAC,gBAAgB,MAAM;AACpC,QAAM,UAAU,IAAI,QAAQ,iBAAiB,QAAQ,IAAI;AACzD,cAAY,SAAS,QAAQ,OAAO,UAAU,GAAG,KAAK;AACtD,SAAO;AACT;AAUO,SAAS,YAAY,SAAS,QAAQ,QAAQ,OAAO;AAC1D,QAAM,kBAAkB,QAAQ,mBAAmB;AACnD,QAAM,SAAS,QAAQ,UAAU;AACjC,QAAM,QAAQ,gBAAgB,SAAS,SAAS;AAChD,QAAM,aAAa,QAAQ,QAAQ;AACnC,WAAS,IAAI,GAAG,KAAK,OAAO,EAAE,GAAG;AAC/B,UAAMK,UAAS,IAAI;AACnB,UAAMC,SAAQ,aAAc,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAM;AAC9D,oBAAgBD,OAAM,IAAI,OAAO,CAAC,IAAI,SAAS,KAAK,IAAIC,MAAK;AAC7D,oBAAgBD,UAAS,CAAC,IAAI,OAAO,CAAC,IAAI,SAAS,KAAK,IAAIC,MAAK;AAAA,EACnE;AACA,UAAQ,QAAQ;AAClB;;;AC5eA,IAAM,eAAN,MAAM,sBAAqB,uBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxC,YAAYC,cAAa,QAAQ,OAAO;AACtC,UAAM;AAMN,SAAK,SAAS,CAAC;AAMf,SAAK,8BAA8B;AAMnC,SAAK,sBAAsB;AAM3B,SAAK,YAAY;AAMjB,SAAK,oBAAoB;AAMzB,SAAK,oBAAoB;AAMzB,SAAK,2BAA2B;AAEhC,QAAI,CAAC,SAAS,CAAC,MAAM,QAAQA,aAAY,CAAC,CAAC,GAAG;AAC5C,YAAM;AAAA;AAAA,QAA0CA;AAAA;AAEhD,YAAM,kBAAkB,CAAC;AACzB,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,cAAM,UAAU,SAAS,CAAC;AAC1B,cAAMC,UAAS,gBAAgB;AAC/B,cAAM,OAAO,QAAQ,QAAQ;AAC7B,iBAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,eAAK,CAAC,KAAKA;AAAA,QACb;AACA,QAAAC,QAAO,iBAAiB,QAAQ,mBAAmB,CAAC;AACpD,kBAAU,KAAK,IAAI;AAAA,MACrB;AACA,eACE,SAAS,WAAW,IAAI,KAAK,UAAU,IAAI,SAAS,CAAC,EAAE,UAAU;AACnE,MAAAF,eAAc;AACd,cAAQ;AAAA,IACV;AACA,QAAI,WAAW,UAAa,OAAO;AACjC,WAAK;AAAA,QACH;AAAA;AAAA,QAC8BA;AAAA,MAChC;AACA,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK;AAAA;AAAA,QAEDA;AAAA,QAEF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,SAAS;AAErB,QAAI;AACJ,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,QAAQ,mBAAmB,EAAE,MAAM;AAC1D,aAAO,QAAQ,QAAQ,EAAE,MAAM;AAC/B,WAAK,OAAO,KAAK;AAAA,IACnB,OAAO;AACL,YAAMC,UAAS,KAAK,gBAAgB;AACpC,MAAAC,QAAO,KAAK,iBAAiB,QAAQ,mBAAmB,CAAC;AACzD,aAAO,QAAQ,QAAQ,EAAE,MAAM;AAC/B,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,aAAK,CAAC,KAAKD;AAAA,MACb;AAAA,IACF;AACA,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,WAAW,IAAI,MAAM,GAAG;AAC9B,aAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,eAAS,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,MAAM;AAAA,IACrC;AAEA,UAAM,eAAe,IAAI;AAAA,MACvB,KAAK,gBAAgB,MAAM;AAAA,MAC3B,KAAK;AAAA,MACL;AAAA,IACF;AACA,iBAAa,gBAAgB,IAAI;AAEjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,GAAG,GAAG,cAAc,oBAAoB;AACrD,QAAI,qBAAqB,yBAAyB,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,WAAK,YAAY,KAAK;AAAA,QACpB;AAAA,UACE,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,GAAG,GAAG;AACf,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAOE;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,eAAe,OAAO;AACpB,QAAI;AACJ,QAAI,UAAU,QAAW;AACvB,wBAAkB,KAAK,2BAA2B,EAAE,MAAM;AAC1D;AAAA,QACE;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF,OAAO;AACL,wBAAkB,KAAK;AAAA,IACzB;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,KAAK,+BAA+B,KAAK,YAAY,GAAG;AAC1D,YAAM,cAAc;AAAA,QAClB,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,WAAK,sBAAsB;AAAA,QACzB,KAAK,2BAA2B;AAAA,QAChC;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,MACF;AACA,WAAK,8BAA8B,KAAK,YAAY;AAAA,IACtD;AACA;AAAA;AAAA,MAAqC,KAAK;AAAA;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB;AAClB,WAAO,IAAI,mBAAW,KAAK,sBAAsB,EAAE,MAAM,GAAG,KAAK;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,QAAI,KAAK,qBAAqB,KAAK,YAAY,GAAG;AAChD,YAAM,kBAAkB,KAAK;AAC7B,UACE,wBAAwB,iBAAiB,GAAG,KAAK,QAAQ,KAAK,MAAM,GACpE;AACA,aAAK,2BAA2B;AAAA,MAClC,OAAO;AACL,aAAK,2BAA2B,gBAAgB,MAAM;AACtD,aAAK,yBAAyB,SAAS;AAAA,UACrC,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,WAAK,oBAAoB,KAAK,YAAY;AAAA,IAC5C;AACA;AAAA;AAAA,MAAqC,KAAK;AAAA;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,8BAA8B,kBAAkB;AAE9C,UAAM,4BAA4B,CAAC;AAEnC,UAAM,kBAAkB,CAAC;AACzB,8BAA0B,SAAS;AAAA,MACjC,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,KAAK,gBAAgB;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,IAAI,cAAa,2BAA2B,MAAM,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,OAAO;AAChB,QAAI,QAAQ,KAAK,KAAK,OAAO,UAAU,OAAO;AAC5C,aAAO;AAAA,IACT;AACA,QAAIF;AACJ,QAAI,UAAU,GAAG;AACf,MAAAA,UAAS;AAAA,IACX,OAAO;AACL,YAAM,WAAW,KAAK,OAAO,QAAQ,CAAC;AACtC,MAAAA,UAAS,SAAS,SAAS,SAAS,CAAC;AAAA,IACvC;AACA,UAAM,OAAO,KAAK,OAAO,KAAK,EAAE,MAAM;AACtC,UAAM,MAAM,KAAK,KAAK,SAAS,CAAC;AAChC,QAAIA,YAAW,GAAG;AAChB,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,aAAK,CAAC,KAAKA;AAAA,MACb;AAAA,IACF;AACA,WAAO,IAAI;AAAA,MACT,KAAK,gBAAgB,MAAMA,SAAQ,GAAG;AAAA,MACtC,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc;AACZ,UAAM,SAAS,KAAK;AACpB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,QAAQ,KAAK;AACnB,UAAM,WAAW,CAAC;AAClB,QAAIA,UAAS;AACb,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,YAAM,OAAO,MAAM,CAAC,EAAE,MAAM;AAC5B,YAAM,MAAM,KAAK,KAAK,SAAS,CAAC;AAChC,UAAIA,YAAW,GAAG;AAChB,iBAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,eAAK,CAAC,KAAKA;AAAA,QACb;AAAA,MACF;AACA,YAAM,UAAU,IAAI;AAAA,QAClB,gBAAgB,MAAMA,SAAQ,GAAG;AAAA,QACjC;AAAA,QACA;AAAA,MACF;AACA,eAAS,KAAK,OAAO;AACrB,MAAAA,UAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,QAAQ;AACvB,WAAO;AAAA,MACL,KAAK,2BAA2B;AAAA,MAChC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAeD,cAAa,QAAQ;AAClC,SAAK,UAAU,QAAQA,cAAa,CAAC;AACrC,QAAI,CAAC,KAAK,iBAAiB;AACzB,WAAK,kBAAkB,CAAC;AAAA,IAC1B;AACA,UAAM,QAAQ;AAAA,MACZ,KAAK;AAAA,MACL;AAAA,MACAA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AACA,QAAI,MAAM,WAAW,GAAG;AACtB,WAAK,gBAAgB,SAAS;AAAA,IAChC,OAAO;AACL,YAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AACvC,WAAK,gBAAgB,SACnB,SAAS,WAAW,IAAI,IAAI,SAAS,SAAS,SAAS,CAAC;AAAA,IAC5D;AACA,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,uBAAQ;;;AC9af,IAAM,eAAe,OAAgB;AAOrC,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlB,YAAY,MAAM,iBAAiB,MAAM,QAAQ,YAAY,IAAI;AAI/D,SAAK;AAML,SAAK;AAML,SAAK,MAAM;AAMX,SAAK,QAAQ;AAMb,SAAK,mBAAmB;AAMxB,SAAK,sBAAsB;AAM3B,SAAK,iBAAiB;AAMtB,SAAK,QAAQ,QAAQ;AAMrB,SAAK,cAAc;AAMnB,SAAK;AAML,SAAK,UAAU;AAMf,SAAK;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,KAAK;AACP,WAAO,KAAK,YAAY,GAAG;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UACH,KAAK,UAAU,UACX,6BAA6B,KAAK,gBAAgB,IAClD;AAAA,QACE,KAAK;AAAA,QACL;AAAA,QACA,KAAK,iBAAiB;AAAA,QACtB;AAAA,MACF;AAAA,IACR;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAM,aAAa,UAAU,KAAK,UAAU,CAAC;AAC7C,WAAK,sBAAsB;AAAA,QACzB,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAM,OAAO,YAAY,KAAK,kBAAkB,KAAK,KAAK;AAC1D,YAAM,cAAc,aAAmB,KAAK,kBAAkB,GAAG,MAAM,CAAC;AACxE,WAAK,sBAAsB;AAAA,QACzB,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB;AAAA,QACpB,KAAK;AAAA,QACL;AAAA,QACA,KAAK,iBAAiB;AAAA,QACtB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB,CAAC;AACvB,YAAM,kBAAkB,KAAK;AAC7B,UAAII,UAAS;AACb,YAAM;AAAA;AAAA,QAAqC,KAAK;AAAA;AAChD,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC7C,cAAM,MAAM,KAAK,CAAC;AAClB,cAAM,WAAW,iBAAiB,iBAAiBA,SAAQ,KAAK,GAAG,GAAG;AACtE,QAAAC,QAAO,KAAK,gBAAgB,QAAQ;AACpC,QAAAD,UAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ;AACN,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B;AAC3B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,kBAAkB;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,kBAAkB,WAAW;AAC/C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,YAAY;AACpB,iBAAa,IAAc,UAAU;AACrC,UAAM,cAAc,WAAW,UAAU;AACzC,UAAM,kBAAkB,WAAW,eAAe;AAClD,QAAI,eAAe,iBAAiB;AAClC,YAAM,QAAQ,UAAU,eAAe,IAAI,UAAU,WAAW;AAChE;AAAA,QACE;AAAA,QACA,gBAAgB,CAAC;AAAA,QACjB,gBAAgB,CAAC;AAAA,QACjB;AAAA,QACA,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA;AAAA,QACE,KAAK;AAAA,QACL;AAAA,QACA,KAAK,iBAAiB;AAAA,QACtB;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,aAAa;AAC1B,gBAAY,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,OAAO;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAnXV;AAoXI,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK,iBAAiB,MAAM;AAAA,OAC5B,UAAK,UAAL,mBAAY;AAAA,MACZ,KAAK;AAAA,MACL,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW;AAAA,MAClC,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,4BAA4B;AAC1B,SAAK,sBAAsB,WAAW,CAAC,kBAAkB,cAAc;AACrE,UAAI,qBAAqB,KAAK,mBAAmB;AAC/C,eAAO,KAAK;AAAA,MACd;AACA,WAAK,sBAAsB,KAAK,MAAM;AACtC,UAAI,WAAW;AACb,aAAK,oBAAoB,eAAe,SAAS;AAAA,MACnD;AACA,YAAM,4BACJ,KAAK,oBAAoB,mBAAmB;AAC9C,UAAI;AACJ,cAAQ,KAAK,OAAO;AAAA,QAClB,KAAK;AACH,oCAA0B,SAAS;AAAA,YACjC;AAAA,YACA;AAAA,YACA,KAAK,oBAAoB,iBAAiB;AAAA,YAC1C,KAAK,oBAAoB;AAAA,YACzB;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,2BAAiB,CAAC,0BAA0B,MAAM;AAClD;AAAA,QACF,KAAK;AACH,2BAAiB,CAAC;AAClB,oCAA0B,SAAS;AAAA,YACjC;AAAA,YACA;AAAA,YACA,KAAK,oBAAoB;AAAA,YACzB,KAAK,oBAAoB;AAAA,YACzB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,2BAAiB,CAAC;AAClB,oCAA0B,SAAS;AAAA,YACjC;AAAA,YACA;AAAA,YACA,KAAK,oBAAoB;AAAA,YACzB,KAAK,oBAAoB;AAAA,YACzB,KAAK,KAAK,gBAAgB;AAAA,YAC1B;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA;AAAA,QACF;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,aAAK,sBAAsB,IAAI;AAAA,UAC7B,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,WAAK,oBAAoB;AACzB,aAAO,KAAK;AAAA,IACd,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAKA,cAAc,UAAU,qBACtB,cAAc,UAAU;AA8D1B,IAAOE,mBAAQ;;;ACrgBA,SAAR,YAA6B,KAAK,GAAG,OAAO,GAAG,QAAQ,IAAI,SAAS,GAAG,UAAU,gBAAgB;AAEpG,SAAO,QAAQ,MAAM;AACjB,QAAI,QAAQ,OAAO,KAAK;AACpB,YAAM,IAAI,QAAQ,OAAO;AACzB,YAAM,IAAI,IAAI,OAAO;AACrB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAClC,YAAM,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxE,YAAM,UAAU,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAC7D,YAAM,WAAW,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACrE,kBAAY,KAAK,GAAG,SAAS,UAAU,OAAO;AAAA,IAClD;AAEA,UAAM,IAAI,IAAI,CAAC;AACf,QAAI,IAAI;AAER,QAAI,IAAI;AAER,SAAK,KAAK,MAAM,CAAC;AACjB,QAAI,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,EAAG,MAAK,KAAK,MAAM,KAAK;AAErD,WAAO,IAAI,GAAG;AACV,WAAK,KAAK,GAAG,CAAC;AACd;AACA;AACA,aAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG;AAC/B,aAAO,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG;AAAA,IACnC;AAEA,QAAI,QAAQ,IAAI,IAAI,GAAG,CAAC,MAAM,EAAG,MAAK,KAAK,MAAM,CAAC;AAAA,SAC7C;AACD;AACA,WAAK,KAAK,GAAG,KAAK;AAAA,IACtB;AAEA,QAAI,KAAK,EAAG,QAAO,IAAI;AACvB,QAAI,KAAK,EAAG,SAAQ,IAAI;AAAA,EAC5B;AACJ;AAQA,SAAS,KAAK,KAAK,GAAG,GAAG;AACrB,QAAM,MAAM,IAAI,CAAC;AACjB,MAAI,CAAC,IAAI,IAAI,CAAC;AACd,MAAI,CAAC,IAAI;AACb;AAQA,SAAS,eAAe,GAAG,GAAG;AAC1B,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AACpC;;;ACvEA,IAAqB,QAArB,MAA2B;AAAA,EACvB,YAAY,aAAa,GAAG;AAExB,SAAK,cAAc,KAAK,IAAI,GAAG,UAAU;AACzC,SAAK,cAAc,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,cAAc,GAAG,CAAC;AAChE,SAAK,MAAM;AAAA,EACf;AAAA,EAEA,MAAM;AACF,WAAO,KAAK,KAAK,KAAK,MAAM,CAAC,CAAC;AAAA,EAClC;AAAA,EAEA,OAAO,MAAM;AACT,QAAI,OAAO,KAAK;AAChB,UAAM,SAAS,CAAC;AAEhB,QAAI,CAACC,YAAW,MAAM,IAAI,EAAG,QAAO;AAEpC,UAAM,SAAS,KAAK;AACpB,UAAM,gBAAgB,CAAC;AAEvB,WAAO,MAAM;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,cAAM,YAAY,KAAK,OAAO,OAAO,KAAK,IAAI;AAE9C,YAAIA,YAAW,MAAM,SAAS,GAAG;AAC7B,cAAI,KAAK,KAAM,QAAO,KAAK,KAAK;AAAA,mBACvB,SAAS,MAAM,SAAS,EAAG,MAAK,KAAK,OAAO,MAAM;AAAA,cACtD,eAAc,KAAK,KAAK;AAAA,QACjC;AAAA,MACJ;AACA,aAAO,cAAc,IAAI;AAAA,IAC7B;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,SAAS,MAAM;AACX,QAAI,OAAO,KAAK;AAEhB,QAAI,CAACA,YAAW,MAAM,IAAI,EAAG,QAAO;AAEpC,UAAM,gBAAgB,CAAC;AACvB,WAAO,MAAM;AACT,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,cAAM,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI;AAEnD,YAAIA,YAAW,MAAM,SAAS,GAAG;AAC7B,cAAI,KAAK,QAAQ,SAAS,MAAM,SAAS,EAAG,QAAO;AACnD,wBAAc,KAAK,KAAK;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO,cAAc,IAAI;AAAA,IAC7B;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,KAAK,MAAM;AACP,QAAI,EAAE,QAAQ,KAAK,QAAS,QAAO;AAEnC,QAAI,KAAK,SAAS,KAAK,aAAa;AAChC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,aAAK,OAAO,KAAK,CAAC,CAAC;AAAA,MACvB;AACA,aAAO;AAAA,IACX;AAGA,QAAI,OAAO,KAAK,OAAO,KAAK,MAAM,GAAG,GAAG,KAAK,SAAS,GAAG,CAAC;AAE1D,QAAI,CAAC,KAAK,KAAK,SAAS,QAAQ;AAE5B,WAAK,OAAO;AAAA,IAEhB,WAAW,KAAK,KAAK,WAAW,KAAK,QAAQ;AAEzC,WAAK,WAAW,KAAK,MAAM,IAAI;AAAA,IAEnC,OAAO;AACH,UAAI,KAAK,KAAK,SAAS,KAAK,QAAQ;AAEhC,cAAM,UAAU,KAAK;AACrB,aAAK,OAAO;AACZ,eAAO;AAAA,MACX;AAGA,WAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,KAAK,SAAS,GAAG,IAAI;AAAA,IAC/D;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,MAAM;AACT,QAAI,KAAM,MAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,CAAC;AACjD,WAAO;AAAA,EACX;AAAA,EAEA,QAAQ;AACJ,SAAK,OAAO,WAAW,CAAC,CAAC;AACzB,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,MAAM,UAAU;AACnB,QAAI,CAAC,KAAM,QAAO;AAElB,QAAI,OAAO,KAAK;AAChB,UAAM,OAAO,KAAK,OAAO,IAAI;AAC7B,UAAM,OAAO,CAAC;AACd,UAAM,UAAU,CAAC;AACjB,QAAI,GAAG,QAAQ;AAGf,WAAO,QAAQ,KAAK,QAAQ;AAExB,UAAI,CAAC,MAAM;AACP,eAAO,KAAK,IAAI;AAChB,iBAAS,KAAK,KAAK,SAAS,CAAC;AAC7B,YAAI,QAAQ,IAAI;AAChB,kBAAU;AAAA,MACd;AAEA,UAAI,KAAK,MAAM;AACX,cAAM,QAAQ,SAAS,MAAM,KAAK,UAAU,QAAQ;AAEpD,YAAI,UAAU,IAAI;AAEd,eAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,eAAK,KAAK,IAAI;AACd,eAAK,UAAU,IAAI;AACnB,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,UAAI,CAAC,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM,IAAI,GAAG;AAChD,aAAK,KAAK,IAAI;AACd,gBAAQ,KAAK,CAAC;AACd,YAAI;AACJ,iBAAS;AACT,eAAO,KAAK,SAAS,CAAC;AAAA,MAE1B,WAAW,QAAQ;AACf;AACA,eAAO,OAAO,SAAS,CAAC;AACxB,kBAAU;AAAA,MAEd,MAAO,QAAO;AAAA,IAClB;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,MAAM;AAAE,WAAO;AAAA,EAAM;AAAA,EAE5B,YAAY,GAAG,GAAG;AAAE,WAAO,EAAE,OAAO,EAAE;AAAA,EAAM;AAAA,EAC5C,YAAY,GAAG,GAAG;AAAE,WAAO,EAAE,OAAO,EAAE;AAAA,EAAM;AAAA,EAE5C,SAAS;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EAE7B,SAAS,MAAM;AACX,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EAEA,KAAK,MAAM,QAAQ;AACf,UAAM,gBAAgB,CAAC;AACvB,WAAO,MAAM;AACT,UAAI,KAAK,KAAM,QAAO,KAAK,GAAG,KAAK,QAAQ;AAAA,UACtC,eAAc,KAAK,GAAG,KAAK,QAAQ;AAExC,aAAO,cAAc,IAAI;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,OAAO,MAAM,OAAO,QAAQ;AAE/B,UAAM,IAAI,QAAQ,OAAO;AACzB,QAAI,IAAI,KAAK;AACb,QAAI;AAEJ,QAAI,KAAK,GAAG;AAER,aAAO,WAAW,MAAM,MAAM,MAAM,QAAQ,CAAC,CAAC;AAC9C,eAAS,MAAM,KAAK,MAAM;AAC1B,aAAO;AAAA,IACX;AAEA,QAAI,CAAC,QAAQ;AAET,eAAS,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAG5C,UAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC;AAAA,IAC7C;AAEA,WAAO,WAAW,CAAC,CAAC;AACpB,SAAK,OAAO;AACZ,SAAK,SAAS;AAId,UAAM,KAAK,KAAK,KAAK,IAAI,CAAC;AAC1B,UAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC;AAEtC,gBAAY,OAAO,MAAM,OAAO,IAAI,KAAK,WAAW;AAEpD,aAAS,IAAI,MAAM,KAAK,OAAO,KAAK,IAAI;AAEpC,YAAM,SAAS,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK;AAEzC,kBAAY,OAAO,GAAG,QAAQ,IAAI,KAAK,WAAW;AAElD,eAAS,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAI;AAElC,cAAM,SAAS,KAAK,IAAI,IAAI,KAAK,GAAG,MAAM;AAG1C,aAAK,SAAS,KAAK,KAAK,OAAO,OAAO,GAAG,QAAQ,SAAS,CAAC,CAAC;AAAA,MAChE;AAAA,IACJ;AAEA,aAAS,MAAM,KAAK,MAAM;AAE1B,WAAO;AAAA,EACX;AAAA,EAEA,eAAe,MAAM,MAAM,OAAO,MAAM;AACpC,WAAO,MAAM;AACT,WAAK,KAAK,IAAI;AAEd,UAAI,KAAK,QAAQ,KAAK,SAAS,MAAM,MAAO;AAE5C,UAAI,UAAU;AACd,UAAI,iBAAiB;AACrB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,cAAM,OAAO,SAAS,KAAK;AAC3B,cAAM,cAAc,aAAa,MAAM,KAAK,IAAI;AAGhD,YAAI,cAAc,gBAAgB;AAC9B,2BAAiB;AACjB,oBAAU,OAAO,UAAU,OAAO;AAClC,uBAAa;AAAA,QAEjB,WAAW,gBAAgB,gBAAgB;AAEvC,cAAI,OAAO,SAAS;AAChB,sBAAU;AACV,yBAAa;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO,cAAc,KAAK,SAAS,CAAC;AAAA,IACxC;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,QAAQ,MAAM,OAAO,QAAQ;AACzB,UAAM,OAAO,SAAS,OAAO,KAAK,OAAO,IAAI;AAC7C,UAAM,aAAa,CAAC;AAGpB,UAAM,OAAO,KAAK,eAAe,MAAM,KAAK,MAAM,OAAO,UAAU;AAGnE,SAAK,SAAS,KAAK,IAAI;AACvB,IAAAC,QAAO,MAAM,IAAI;AAGjB,WAAO,SAAS,GAAG;AACf,UAAI,WAAW,KAAK,EAAE,SAAS,SAAS,KAAK,aAAa;AACtD,aAAK,OAAO,YAAY,KAAK;AAC7B;AAAA,MACJ,MAAO;AAAA,IACX;AAGA,SAAK,oBAAoB,MAAM,YAAY,KAAK;AAAA,EACpD;AAAA;AAAA,EAGA,OAAO,YAAY,OAAO;AACtB,UAAM,OAAO,WAAW,KAAK;AAC7B,UAAM,IAAI,KAAK,SAAS;AACxB,UAAM,IAAI,KAAK;AAEf,SAAK,iBAAiB,MAAM,GAAG,CAAC;AAEhC,UAAM,aAAa,KAAK,kBAAkB,MAAM,GAAG,CAAC;AAEpD,UAAM,UAAU,WAAW,KAAK,SAAS,OAAO,YAAY,KAAK,SAAS,SAAS,UAAU,CAAC;AAC9F,YAAQ,SAAS,KAAK;AACtB,YAAQ,OAAO,KAAK;AAEpB,aAAS,MAAM,KAAK,MAAM;AAC1B,aAAS,SAAS,KAAK,MAAM;AAE7B,QAAI,MAAO,YAAW,QAAQ,CAAC,EAAE,SAAS,KAAK,OAAO;AAAA,QACjD,MAAK,WAAW,MAAM,OAAO;AAAA,EACtC;AAAA,EAEA,WAAW,MAAM,SAAS;AAEtB,SAAK,OAAO,WAAW,CAAC,MAAM,OAAO,CAAC;AACtC,SAAK,KAAK,SAAS,KAAK,SAAS;AACjC,SAAK,KAAK,OAAO;AACjB,aAAS,KAAK,MAAM,KAAK,MAAM;AAAA,EACnC;AAAA,EAEA,kBAAkB,MAAM,GAAG,GAAG;AAC1B,QAAI;AACJ,QAAI,aAAa;AACjB,QAAI,UAAU;AAEd,aAAS,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK;AAC7B,YAAM,QAAQ,SAAS,MAAM,GAAG,GAAG,KAAK,MAAM;AAC9C,YAAM,QAAQ,SAAS,MAAM,GAAG,GAAG,KAAK,MAAM;AAE9C,YAAM,UAAU,iBAAiB,OAAO,KAAK;AAC7C,YAAM,OAAO,SAAS,KAAK,IAAI,SAAS,KAAK;AAG7C,UAAI,UAAU,YAAY;AACtB,qBAAa;AACb,gBAAQ;AAER,kBAAU,OAAO,UAAU,OAAO;AAAA,MAEtC,WAAW,YAAY,YAAY;AAE/B,YAAI,OAAO,SAAS;AAChB,oBAAU;AACV,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,SAAS,IAAI;AAAA,EACxB;AAAA;AAAA,EAGA,iBAAiB,MAAM,GAAG,GAAG;AACzB,UAAM,cAAc,KAAK,OAAO,KAAK,cAAc;AACnD,UAAM,cAAc,KAAK,OAAO,KAAK,cAAc;AACnD,UAAM,UAAU,KAAK,eAAe,MAAM,GAAG,GAAG,WAAW;AAC3D,UAAM,UAAU,KAAK,eAAe,MAAM,GAAG,GAAG,WAAW;AAI3D,QAAI,UAAU,QAAS,MAAK,SAAS,KAAK,WAAW;AAAA,EACzD;AAAA;AAAA,EAGA,eAAe,MAAM,GAAG,GAAG,SAAS;AAChC,SAAK,SAAS,KAAK,OAAO;AAE1B,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,SAAS,MAAM,GAAG,GAAG,MAAM;AAC5C,UAAM,YAAY,SAAS,MAAM,IAAI,GAAG,GAAG,MAAM;AACjD,QAAI,SAAS,WAAW,QAAQ,IAAI,WAAW,SAAS;AAExD,aAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC5B,YAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,MAAAA,QAAO,UAAU,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK;AAClD,gBAAU,WAAW,QAAQ;AAAA,IACjC;AAEA,aAAS,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AACjC,YAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,MAAAA,QAAO,WAAW,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK;AACnD,gBAAU,WAAW,SAAS;AAAA,IAClC;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,oBAAoB,MAAM,MAAM,OAAO;AAEnC,aAAS,IAAI,OAAO,KAAK,GAAG,KAAK;AAC7B,MAAAA,QAAO,KAAK,CAAC,GAAG,IAAI;AAAA,IACxB;AAAA,EACJ;AAAA,EAEA,UAAU,MAAM;AAEZ,aAAS,IAAI,KAAK,SAAS,GAAG,UAAU,KAAK,GAAG,KAAK;AACjD,UAAI,KAAK,CAAC,EAAE,SAAS,WAAW,GAAG;AAC/B,YAAI,IAAI,GAAG;AACP,qBAAW,KAAK,IAAI,CAAC,EAAE;AACvB,mBAAS,OAAO,SAAS,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC;AAAA,QAEhD,MAAO,MAAK,MAAM;AAAA,MAEtB,MAAO,UAAS,KAAK,CAAC,GAAG,KAAK,MAAM;AAAA,IACxC;AAAA,EACJ;AACJ;AAEA,SAAS,SAAS,MAAM,OAAO,UAAU;AACrC,MAAI,CAAC,SAAU,QAAO,MAAM,QAAQ,IAAI;AAExC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,SAAS,MAAM,MAAM,CAAC,CAAC,EAAG,QAAO;AAAA,EACzC;AACA,SAAO;AACX;AAGA,SAAS,SAAS,MAAM,QAAQ;AAC5B,WAAS,MAAM,GAAG,KAAK,SAAS,QAAQ,QAAQ,IAAI;AACxD;AAGA,SAAS,SAAS,MAAM,GAAG,GAAG,QAAQ,UAAU;AAC5C,MAAI,CAAC,SAAU,YAAW,WAAW,IAAI;AACzC,WAAS,OAAO;AAChB,WAAS,OAAO;AAChB,WAAS,OAAO;AAChB,WAAS,OAAO;AAEhB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,IAAAA,QAAO,UAAU,KAAK,OAAO,OAAO,KAAK,IAAI,KAAK;AAAA,EACtD;AAEA,SAAO;AACX;AAEA,SAASA,QAAO,GAAG,GAAG;AAClB,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,IAAE,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAChC,SAAO;AACX;AAEA,SAAS,gBAAgB,GAAG,GAAG;AAAE,SAAO,EAAE,OAAO,EAAE;AAAM;AACzD,SAAS,gBAAgB,GAAG,GAAG;AAAE,SAAO,EAAE,OAAO,EAAE;AAAM;AAEzD,SAAS,SAAS,GAAK;AAAE,UAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;AAAO;AACvE,SAAS,WAAW,GAAG;AAAE,SAAQ,EAAE,OAAO,EAAE,QAAS,EAAE,OAAO,EAAE;AAAO;AAEvE,SAAS,aAAa,GAAG,GAAG;AACxB,UAAQ,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI,MAClD,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAC9D;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AACpC,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AACpC,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AACpC,QAAM,OAAO,KAAK,IAAI,EAAE,MAAM,EAAE,IAAI;AAEpC,SAAO,KAAK,IAAI,GAAG,OAAO,IAAI,IACvB,KAAK,IAAI,GAAG,OAAO,IAAI;AAClC;AAEA,SAAS,SAAS,GAAG,GAAG;AACpB,SAAO,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE;AACvB;AAEA,SAASD,YAAW,GAAG,GAAG;AACtB,SAAO,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE,QACZ,EAAE,QAAQ,EAAE;AACvB;AAEA,SAAS,WAAW,UAAU;AAC1B,SAAO;AAAA,IACH;AAAA,IACA,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACV;AACJ;AAKA,SAAS,YAAY,KAAK,MAAM,OAAO,GAAG,SAAS;AAC/C,QAAM,QAAQ,CAAC,MAAM,KAAK;AAE1B,SAAO,MAAM,QAAQ;AACjB,YAAQ,MAAM,IAAI;AAClB,WAAO,MAAM,IAAI;AAEjB,QAAI,QAAQ,QAAQ,EAAG;AAEvB,UAAM,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACvD,gBAAY,KAAK,KAAK,MAAM,OAAO,OAAO;AAE1C,UAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EACpC;AACJ;;;AC3eA,IAAME,SAAN,MAAY;AAAA;AAAA;AAAA;AAAA,EAIV,YAAY,YAAY;AAKtB,SAAK,SAAS,IAAI,MAAO,UAAU;AAQnC,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,OAAO;AAEpB,UAAM,OAAO;AAAA,MACX,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd;AAAA,IACF;AAEA,SAAK,OAAO,OAAO,IAAI;AACvB,SAAK,OAAO,OAAO,KAAK,CAAC,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,SAAS,QAAQ;AACpB,UAAM,QAAQ,IAAI,MAAM,OAAO,MAAM;AACrC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAM,SAAS,QAAQ,CAAC;AACxB,YAAM,QAAQ,OAAO,CAAC;AAGtB,YAAM,OAAO;AAAA,QACX,MAAM,OAAO,CAAC;AAAA,QACd,MAAM,OAAO,CAAC;AAAA,QACd,MAAM,OAAO,CAAC;AAAA,QACd,MAAM,OAAO,CAAC;AAAA,QACd;AAAA,MACF;AACA,YAAM,CAAC,IAAI;AACX,WAAK,OAAO,OAAO,KAAK,CAAC,IAAI;AAAA,IAC/B;AACA,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO;AACZ,UAAM,MAAM,OAAO,KAAK;AAIxB,UAAM,OAAO,KAAK,OAAO,GAAG;AAC5B,WAAO,KAAK,OAAO,GAAG;AACtB,WAAO,KAAK,OAAO,OAAO,IAAI,MAAM;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,OAAO;AACpB,UAAM,OAAO,KAAK,OAAO,OAAO,KAAK,CAAC;AACtC,UAAM,OAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AACxD,QAAI,CAAC,OAAO,MAAM,MAAM,GAAG;AACzB,WAAK,OAAO,KAAK;AACjB,WAAK,OAAO,QAAQ,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,UAAM,QAAQ,KAAK,OAAO,IAAI;AAC9B,WAAO,MAAM,IAAI,SAAU,MAAM;AAC/B,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,QAAQ;AAElB,UAAM,OAAO;AAAA,MACX,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,MACd,MAAM,OAAO,CAAC;AAAA,IAChB;AACA,UAAM,QAAQ,KAAK,OAAO,OAAO,IAAI;AACrC,WAAO,MAAM,IAAI,SAAU,MAAM;AAC/B,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,UAAU;AAChB,WAAO,KAAK,SAAS,KAAK,OAAO,GAAG,QAAQ;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,QAAQ,UAAU;AAChC,WAAO,KAAK,SAAS,KAAK,YAAY,MAAM,GAAG,QAAQ;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,QAAQ,UAAU;AACzB,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,eAAS,SAAS,OAAO,CAAC,CAAC;AAC3B,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAOC,SAAQ,KAAK,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,CAAC;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AAChB,UAAM,OAAO,KAAK,OAAO,OAAO;AAChC,WAAO,eAAe,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACZ,SAAK,OAAO,KAAK,MAAM,OAAO,IAAI,CAAC;AACnC,eAAW,KAAK,MAAM,QAAQ;AAC5B,WAAK,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC;AAAA,IACjC;AAAA,EACF;AACF;AAEA,IAAO,gBAAQD;;;ACzKf,IAAM,SAAN,cAAqB,eAAW;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,SAAS;AACnB,UAAM;AAMN,SAAK,aAAa,IAAc,QAAQ,UAAU;AAMlD,SAAK,gBAAgB,kBAAkB,QAAQ,YAAY;AAM3D,SAAK,2BAA2B,QAAQ,2BAA2B;AAOnE,SAAK,UAAU;AAMf,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,SAAS,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAM5D,SAAK,eAAe,CAAC,CAAC,QAAQ;AAM9B,SAAK,eAAe;AAMpB,SAAK,eAAe;AAEpB,UAAM,OAAO;AAKb,SAAK,eAAe,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACzD,WAAK,eAAe;AACpB,WAAK,eAAe;AAAA,IACtB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B;AAC3B,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,YAAY;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,cAAc;AAC5B,SAAK,gBAAgB,kBAAkB,YAAY;AACnD,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AAOA,SAAS,kBAAkB,iBAAiB;AAC1C,MAAI,CAAC,iBAAiB;AACpB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,oBAAoB,YAAY;AACzC,WAAO;AAAA,EACT;AACA,MAAI,CAAC,MAAM,QAAQ,eAAe,GAAG;AACnC,sBAAkB,CAAC,eAAe;AAAA,EACpC;AACA,SAAO,CAAC,eAAe;AACzB;AAEA,IAAO,iBAAQ;;;ACpOf,IAAO,0BAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOZ,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,mBAAmB;AACrB;;;ACnBO,IAAM,oBAAN,cAAgC,cAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3C,YAAY,MAAM,SAAS,UAAU;AACnC,UAAM,IAAI;AAOV,SAAK,UAAU;AAOf,SAAK,WAAW;AAAA,EAClB;AACF;AAwHA,IAAM,eAAN,cAA2B,eAAO;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,SAAS;AACnB,cAAU,WAAW,CAAC;AAEtB,UAAM;AAAA,MACJ,cAAc,QAAQ;AAAA,MACtB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO,QAAQ,UAAU,SAAY,QAAQ,QAAQ;AAAA,IACvD,CAAC;AAKD,SAAK;AAKL,SAAK;AAKL,SAAK;AAML,SAAK,UAAU;AAMf,SAAK,UAAU,QAAQ,UAAU;AAMjC,SAAK,YAAY,QAAQ,aAAa,SAAY,OAAO,QAAQ;AAMjE,SAAK,OAAO,QAAQ;AAEpB,QAAI,QAAQ,WAAW,QAAW;AAChC,WAAK,UAAU,QAAQ;AAAA,IACzB,WAAW,KAAK,SAAS,QAAW;AAClC,aAAO,KAAK,SAAS,wCAAwC;AAE7D,WAAK,UAAU,IAAI,KAAK,MAAM,KAAK,OAAO;AAAA,IAC5C;AAMA,SAAK,YACH,QAAQ,aAAa,SAAY,QAAQ,WAAW;AAEtD,UAAM,kBACJ,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAMpE,SAAK,iBAAiB,kBAAkB,IAAI,cAAM,IAAI;AAMtD,SAAK,sBAAsB,IAAI,cAAM;AAMrC,SAAK,uBAAuB;AAM5B,SAAK,wBAAwB,CAAC;AAO9B,SAAK,WAAW,CAAC;AAOjB,SAAK,YAAY,CAAC;AAMlB,SAAK,qBAAqB,CAAC;AAM3B,SAAK,sBAAsB;AAG3B,QAAI;AAEJ,QAAI;AACJ,QAAI,MAAM,QAAQ,QAAQ,QAAQ,GAAG;AACnC,iBAAW,QAAQ;AAAA,IACrB,WAAW,QAAQ,UAAU;AAC3B,mBAAa,QAAQ;AACrB,iBAAW,WAAW,SAAS;AAAA,IACjC;AACA,QAAI,CAAC,mBAAmB,eAAe,QAAW;AAChD,mBAAa,IAAI,mBAAW,QAAQ;AAAA,IACtC;AACA,QAAI,aAAa,QAAW;AAC1B,WAAK,oBAAoB,QAAQ;AAAA,IACnC;AACA,QAAI,eAAe,QAAW;AAC5B,WAAK,wBAAwB,UAAU;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,WAAW,SAAS;AAClB,SAAK,mBAAmB,OAAO;AAC/B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,SAAS;AAC1B,UAAM,aAAa,OAAO,OAAO;AAEjC,QAAI,CAAC,KAAK,YAAY,YAAY,OAAO,GAAG;AAC1C,UAAI,KAAK,qBAAqB;AAC5B,aAAK,oBAAoB,OAAO,OAAO;AAAA,MACzC;AACA;AAAA,IACF;AAEA,SAAK,mBAAmB,YAAY,OAAO;AAE3C,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,UAAU;AACZ,YAAM,SAAS,SAAS,UAAU;AAClC,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,OAAO,QAAQ,OAAO;AAAA,MAC5C;AAAA,IACF,OAAO;AACL,WAAK,sBAAsB,UAAU,IAAI;AAAA,IAC3C;AAEA,SAAK;AAAA,MACH,IAAI,kBAAkB,wBAAgB,YAAY,OAAO;AAAA,IAC3D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,YAAY,SAAS;AACtC,QAAI,mBAAmBE,kBAAe;AACpC;AAAA,IACF;AACA,SAAK,mBAAmB,UAAU,IAAI;AAAA,MACpC,OAAO,SAAS,kBAAU,QAAQ,KAAK,sBAAsB,IAAI;AAAA,MACjE;AAAA,QACE;AAAA,QACA,wBAAgB;AAAA,QAChB,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,YAAY,SAAS;AAC/B,QAAI,QAAQ;AACZ,QAAI,QAAQ,MAAM,MAAM,QAAW;AACjC,YAAM,KAAK,OAAO,QAAQ,MAAM,CAAC;AACjC,UAAI,EAAE,MAAM,KAAK,WAAW;AAC1B,aAAK,SAAS,EAAE,IAAI;AAAA,MACtB,WAAW,mBAAmBA,kBAAe;AAC3C,cAAM,iBAAiB,KAAK,SAAS,EAAE;AACvC,YAAI,EAAE,0BAA0BA,mBAAgB;AAC9C,kBAAQ;AAAA,QACV,WAAW,CAAC,MAAM,QAAQ,cAAc,GAAG;AACzC,eAAK,SAAS,EAAE,IAAI,CAAC,gBAAgB,OAAO;AAAA,QAC9C,OAAO;AACL,yBAAe,KAAK,OAAO;AAAA,QAC7B;AAAA,MACF,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF;AACA,QAAI,OAAO;AACT;AAAA,QACE,EAAE,cAAc,KAAK;AAAA,QACrB;AAAA,MACF;AACA,WAAK,UAAU,UAAU,IAAI;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,UAAU;AACpB,SAAK,oBAAoB,QAAQ;AACjC,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,UAAU;AAC5B,UAAM,UAAU,CAAC;AAEjB,UAAM,cAAc,CAAC;AAErB,UAAM,mBAAmB,CAAC;AAE1B,aAAS,IAAI,GAAG,SAAS,SAAS,QAAQ,IAAI,QAAQ,KAAK;AACzD,YAAM,UAAU,SAAS,CAAC;AAC1B,YAAM,aAAa,OAAO,OAAO;AACjC,UAAI,KAAK,YAAY,YAAY,OAAO,GAAG;AACzC,oBAAY,KAAK,OAAO;AAAA,MAC1B;AAAA,IACF;AAEA,aAAS,IAAI,GAAG,SAAS,YAAY,QAAQ,IAAI,QAAQ,KAAK;AAC5D,YAAM,UAAU,YAAY,CAAC;AAC7B,YAAM,aAAa,OAAO,OAAO;AACjC,WAAK,mBAAmB,YAAY,OAAO;AAE3C,YAAM,WAAW,QAAQ,YAAY;AACrC,UAAI,UAAU;AACZ,cAAM,SAAS,SAAS,UAAU;AAClC,gBAAQ,KAAK,MAAM;AACnB,yBAAiB,KAAK,OAAO;AAAA,MAC/B,OAAO;AACL,aAAK,sBAAsB,UAAU,IAAI;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,KAAK,SAAS,gBAAgB;AAAA,IACpD;AAEA,QAAI,KAAK,YAAY,wBAAgB,UAAU,GAAG;AAChD,eAAS,IAAI,GAAG,SAAS,YAAY,QAAQ,IAAI,QAAQ,KAAK;AAC5D,aAAK;AAAA,UACH,IAAI,kBAAkB,wBAAgB,YAAY,YAAY,CAAC,CAAC;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,YAAY;AAClC,QAAI,sBAAsB;AAC1B,SAAK;AAAA,MACH,wBAAgB;AAAA;AAAA;AAAA;AAAA,MAIhB,SAAU,KAAK;AACb,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,qBAAW,KAAK,IAAI,OAAO;AAC3B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,SAAK;AAAA,MACH,wBAAgB;AAAA;AAAA;AAAA;AAAA,MAIhB,SAAU,KAAK;AACb,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,qBAAW,OAAO,IAAI,OAAO;AAC7B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,eAAW;AAAA,MACT,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,QAAQ;AACP,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,eAAK,WAAW,IAAI,OAAO;AAC3B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,eAAW;AAAA,MACT,4BAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,CAAC,QAAQ;AACP,YAAI,CAAC,qBAAqB;AACxB,gCAAsB;AACtB,eAAK,cAAc,IAAI,OAAO;AAC9B,gCAAsB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,MAAM;AACV,QAAI,MAAM;AACR,iBAAW,aAAa,KAAK,oBAAoB;AAC/C,cAAM,OAAO,KAAK,mBAAmB,SAAS;AAC9C,aAAK,QAAQ,aAAa;AAAA,MAC5B;AACA,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,qBAAqB,CAAC;AAC3B,aAAK,WAAW,CAAC;AACjB,aAAK,YAAY,CAAC;AAAA,MACpB;AAAA,IACF,OAAO;AACL,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,QAAQ,CAAC,YAAY;AACvC,eAAK,sBAAsB,OAAO;AAAA,QACpC,CAAC;AACD,mBAAW,MAAM,KAAK,uBAAuB;AAC3C,eAAK,sBAAsB,KAAK,sBAAsB,EAAE,CAAC;AAAA,QAC3D;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,MAAM;AAAA,IACjC;AAEA,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,MAAM;AAAA,IAC5B;AACA,SAAK,wBAAwB,CAAC;AAE9B,UAAM,aAAa,IAAI,kBAAkB,wBAAgB,KAAK;AAC9D,SAAK,cAAc,UAAU;AAC7B,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,eAAe,UAAU;AACvB,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK,eAAe,QAAQ,QAAQ;AAAA,IAC7C;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,QAAQ,QAAQ;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,iCAAiC,YAAY,UAAU;AACrD,UAAM,SAAS,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAC1E,WAAO,KAAK,uBAAuB,QAAQ,SAAU,SAAS;AAC5D,YAAM,WAAW,QAAQ,YAAY;AACrC,UACE,oBAAoBA,oBACpB,SAAS,qBAAqB,UAAU,GACxC;AACA,eAAO,SAAS,OAAO;AAAA,MACzB;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,uBAAuB,QAAQ,UAAU;AACvC,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK,eAAe,gBAAgB,QAAQ,QAAQ;AAAA,IAC7D;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,QAAQ,QAAQ;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,iCAAiC,QAAQ,UAAU;AACjD,WAAO,KAAK;AAAA,MACV;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,SAAU,SAAS;AACjB,cAAM,WAAW,QAAQ,YAAY;AACrC,YACE,oBAAoBA,oBACpB,SAAS,iBAAiB,MAAM,GAChC;AACA,gBAAM,SAAS,SAAS,OAAO;AAC/B,cAAI,QAAQ;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc;AACZ,QAAI;AACJ,QAAI,KAAK,qBAAqB;AAC5B,iBAAW,KAAK,oBAAoB,SAAS,EAAE,MAAM,CAAC;AAAA,IACxD,WAAW,KAAK,gBAAgB;AAC9B,iBAAW,KAAK,eAAe,OAAO;AACtC,UAAI,CAACC,SAAQ,KAAK,qBAAqB,GAAG;AACxC,QAAAC,QAAO,UAAU,OAAO,OAAO,KAAK,qBAAqB,CAAC;AAAA,MAC5D;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB,YAAY;AAElC,UAAM,WAAW,CAAC;AAClB,SAAK,iCAAiC,YAAY,SAAU,SAAS;AACnE,eAAS,KAAK,OAAO;AAAA,IACvB,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,oBAAoB,QAAQ,YAAY;AACtC,QAAI,KAAK,gBAAgB;AACvB,YAAM,aAAa,cAAc,WAAW,SAAS,KAAK,KAAK,SAAS;AAExE,UAAI,CAAC,YAAY;AACf,eAAO,KAAK,eAAe,YAAY,MAAM;AAAA,MAC/C;AAEA,YAAM,UAAU,cAAc,QAAQ,UAAU;AAEhD,aAAO,CAAC,EAAE;AAAA,QACR,GAAG,QAAQ,IAAI,CAAC,aAAa,KAAK,eAAe,YAAY,QAAQ,CAAC;AAAA,MACxE;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB;AAC5B,aAAO,KAAK,oBAAoB,SAAS,EAAE,MAAM,CAAC;AAAA,IACpD;AACA,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,8BAA8B,YAAY,QAAQ;AAQhD,UAAM,IAAI,WAAW,CAAC;AACtB,UAAM,IAAI,WAAW,CAAC;AACtB,QAAI,iBAAiB;AACrB,UAAM,eAAe,CAAC,KAAK,GAAG;AAC9B,QAAI,qBAAqB;AACzB,UAAM,SAAS,CAAC,WAAW,WAAW,UAAU,QAAQ;AACxD,aAAS,SAAS,SAAS;AAC3B,SAAK,eAAe;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA,MAIA,SAAU,SAAS;AACjB,YAAI,OAAO,OAAO,GAAG;AACnB,gBAAM,WAAW,QAAQ,YAAY;AACrC,gBAAM,6BAA6B;AACnC,+BACE,oBAAoBF,mBAChB,IACA,SAAS,eAAe,GAAG,GAAG,cAAc,kBAAkB;AACpE,cAAI,qBAAqB,4BAA4B;AACnD,6BAAiB;AAKjB,kBAAM,cAAc,KAAK,KAAK,kBAAkB;AAChD,mBAAO,CAAC,IAAI,IAAI;AAChB,mBAAO,CAAC,IAAI,IAAI;AAChB,mBAAO,CAAC,IAAI,IAAI;AAChB,mBAAO,CAAC,IAAI,IAAI;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,UAAU,QAAQ;AAChB,WAAO,KAAK,eAAe,UAAU,MAAM;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,eAAe,IAAI;AACjB,UAAM,UAAU,KAAK,SAAS,GAAG,SAAS,CAAC;AAC3C,WAAO,YAAY;AAAA;AAAA,MAEb;AAAA,QAEF;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,KAAK;AACnB,UAAM,UAAU,KAAK,UAAU,GAAG;AAClC,WAAO,YAAY,SAAY,UAAU;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AAC1B,UAAM;AAAA;AAAA,MAAsC,MAAM;AAAA;AAClD,UAAM,aAAa,OAAO,OAAO;AACjC,UAAM,WAAW,QAAQ,YAAY;AACrC,QAAI,CAAC,UAAU;AACb,UAAI,EAAE,cAAc,KAAK,wBAAwB;AAC/C,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,OAAO,OAAO;AAAA,QACpC;AACA,aAAK,sBAAsB,UAAU,IAAI;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,YAAM,SAAS,SAAS,UAAU;AAClC,UAAI,cAAc,KAAK,uBAAuB;AAC5C,eAAO,KAAK,sBAAsB,UAAU;AAC5C,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,OAAO,QAAQ,OAAO;AAAA,QAC5C;AAAA,MACF,OAAO;AACL,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,OAAO,QAAQ,OAAO;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AACA,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,YAAM,MAAM,GAAG,SAAS;AACxB,UAAI,KAAK,SAAS,GAAG,MAAM,SAAS;AAClC,aAAK,mBAAmB,OAAO;AAC/B,aAAK,SAAS,GAAG,IAAI;AAAA,MACvB;AAAA,IACF,OAAO;AACL,WAAK,mBAAmB,OAAO;AAC/B,WAAK,UAAU,UAAU,IAAI;AAAA,IAC/B;AACA,SAAK,QAAQ;AACb,SAAK;AAAA,MACH,IAAI,kBAAkB,wBAAgB,eAAe,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAClB,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,aAAO,MAAM,KAAK;AAAA,IACpB;AACA,WAAO,OAAO,OAAO,KAAK,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,gBAAgB;AACvB,aACE,KAAK,eAAe,QAAQ,KAAKC,SAAQ,KAAK,qBAAqB;AAAA,IAEvE;AACA,QAAI,KAAK,qBAAqB;AAC5B,aAAO,KAAK,oBAAoB,UAAU,MAAM;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,QAAQ,YAAY,YAAY;AAC3C,UAAM,qBAAqB,KAAK;AAChC,UAAM,gBAAgB,KAAK,UAAU,QAAQ,YAAY,UAAU;AACnE,aAAS,IAAI,GAAG,KAAK,cAAc,QAAQ,IAAI,IAAI,EAAE,GAAG;AACtD,YAAM,eAAe,cAAc,CAAC;AACpC,YAAM,gBAAgB,mBAAmB;AAAA,QACvC;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,SAAU,QAAQ;AAChB,iBAAO,eAAe,OAAO,QAAQ,YAAY;AAAA,QACnD;AAAA,MACF;AACA,UAAI,CAAC,eAAe;AAClB,UAAE,KAAK;AACP,aAAK;AAAA,UACH,IAAI,kBAAkB,wBAAgB,iBAAiB;AAAA,QACzD;AACA,aAAK,QAAQ;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,UAIA,CAAC,aAAa;AACZ,cAAE,KAAK;AACP,iBAAK;AAAA,cACH,IAAI;AAAA,gBACF,wBAAgB;AAAA,gBAChB;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,MAAM;AACJ,cAAE,KAAK;AACP,iBAAK;AAAA,cACH,IAAI,kBAAkB,wBAAgB,iBAAiB;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AACA,2BAAmB,OAAO,cAAc,EAAC,QAAQ,aAAa,MAAM,EAAC,CAAC;AAAA,MACxE;AAAA,IACF;AACA,SAAK,UACH,KAAK,QAAQ,SAAS,IAAI,QAAQ,KAAK,uBAAuB;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,MAAM,IAAI;AACf,SAAK,oBAAoB,MAAM;AAC/B,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,QAAQ;AACzB,UAAM,qBAAqB,KAAK;AAChC,UAAM,MAAM,mBAAmB,gBAAgB,QAAQ,SAAU,QAAQ;AACvE,UAAI,OAAO,OAAO,QAAQ,MAAM,GAAG;AACjC,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,QAAI,KAAK;AACP,yBAAmB,OAAO,GAAG;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,UAAU;AACvB,QAAI,UAAU;AACd,aAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,gBAAU,KAAK,sBAAsB,SAAS,CAAC,CAAC,KAAK;AAAA,IACvD;AACA,QAAI,SAAS;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,SAAS;AACrB,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,UAAU,KAAK,sBAAsB,OAAO;AAClD,QAAI,SAAS;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,SAAS;AAC7B,UAAM,aAAa,OAAO,OAAO;AACjC,QAAI,EAAE,cAAc,KAAK,YAAY;AACnC,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,KAAK,uBAAuB;AAC5C,aAAO,KAAK,sBAAsB,UAAU;AAAA,IAC9C,OAAO;AACL,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,OAAO,OAAO;AAAA,MACpC;AAAA,IACF;AAEA,UAAM,oBAAoB,KAAK,mBAAmB,UAAU;AAC5D,2DAAmB,QAAQ;AAC3B,WAAO,KAAK,mBAAmB,UAAU;AAEzC,UAAM,KAAK,QAAQ,MAAM;AACzB,QAAI,OAAO,QAAW;AACpB,YAAM,WAAW,GAAG,SAAS;AAC7B,YAAM,iBAAiB,KAAK,SAAS,QAAQ;AAC7C,UAAI,mBAAmB,SAAS;AAC9B,eAAO,KAAK,SAAS,QAAQ;AAAA,MAC/B,WAAW,MAAM,QAAQ,cAAc,GAAG;AACxC,uBAAe,OAAO,eAAe,QAAQ,OAAO,GAAG,CAAC;AACxD,YAAI,eAAe,WAAW,GAAG;AAC/B,eAAK,SAAS,QAAQ,IAAI,eAAe,CAAC;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,UAAU,UAAU;AAChC,QAAI,KAAK,YAAY,wBAAgB,aAAa,GAAG;AACnD,WAAK;AAAA,QACH,IAAI,kBAAkB,wBAAgB,eAAe,OAAO;AAAA,MAC9D;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,SAAS;AAC1B,eAAW,MAAM,KAAK,UAAU;AAC9B,UAAI,KAAK,SAAS,EAAE,MAAM,SAAS;AACjC,eAAO,KAAK,SAAS,EAAE;AACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACV,WAAO,KAAK,SAAS,wCAAwC;AAC7D,SAAK,OAAO;AACZ,SAAK,UAAU,IAAI,KAAK,KAAK,OAAO,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,UAAU;AACpB,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,IAAO,iBAAQ;", "names": ["xhr", "offset", "offset", "offset", "offset", "offset", "offset", "offset", "squaredDistance", "offset", "squaredDistance", "offset", "coordinates", "offset", "linearRingss", "offset", "squaredDistance", "offset", "coordinates", "coordinates", "offset", "offset", "offset", "coordinates", "extend", "coordinates", "ends", "extend", "layout", "offset", "coordinates", "extend", "squaredDistance", "coordinates", "linearRing", "extend", "linearRings", "offset", "offset", "angle", "coordinates", "offset", "extend", "linearRingss", "offset", "extend", "Feature_default", "intersects", "extend", "<PERSON>ush", "isEmpty", "Feature_default", "isEmpty", "extend"]}