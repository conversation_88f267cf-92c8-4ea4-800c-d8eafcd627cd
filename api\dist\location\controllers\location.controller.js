"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const passport_1 = require("@nestjs/passport");
const enums_1 = require("../../shared/enums");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const services_1 = require("../services");
const dtos_2 = require("../dtos");
const location_filter_request_dto_1 = require("../dtos/request/location-filter-request.dto");
const location_capabilities_response_dto_1 = require("../dtos/response/location-capabilities.response.dto");
const dtos_3 = require("../../capability/dtos");
let LocationController = class LocationController {
    constructor(locationService) {
        this.locationService = locationService;
    }
    getAllLocations(searchTerm) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.getAllLocations(searchTerm);
        });
    }
    upsertLocation(request, createLocationDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.upsertLocation(createLocationDto, request.currentContext);
        });
    }
    getBasicLocationDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.getBasicLocationDetailById(id);
        });
    }
    getCompleteLocationDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.getCompleteLocationDetailById(id);
        });
    }
    getLocationsByFilter(page = 1, limit = 10, orderBy = 'updatedOn', orderDirection = 'DESC', filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.getLocationListByFilter(page, limit, orderBy, orderDirection, filterDto);
        });
    }
    exportLocations(res, request, filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { report, filename } = yield this.locationService.exportLocations(request, filterDto);
            res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
            res.header('Access-Control-Expose-Headers', 'Content-Disposition');
            res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            return res.send(report);
        });
    }
    deleteLocation(request, id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.deleteLocation(id, request.currentContext);
        });
    }
    updateLocationStatus(request, id, updateStatusRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.updateLocationStatus(id, updateStatusRequestDto, request.currentContext);
        });
    }
    getLocationWiseCapabilities(locationId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.locationService.getLocationWiseCapabilities(locationId);
        });
    }
    exportLocationWiseCapabilities(locationId, res, request, filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { report, filename } = yield this.locationService.exportLocationWiseCapabilities(locationId, request, filterDto);
            res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
            res.header('Access-Control-Expose-Headers', 'Content-Disposition');
            res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            return res.send(report);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get location list for dropdown by location name search.',
        type: [dtos_2.LocationDropdownResponseDto]
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchTerm',
        type: String,
        description: 'Provide search term to search location by name.',
        required: true
    }),
    (0, common_1.Get)('/dropdown'),
    __param(0, (0, common_1.Query)('searchTerm')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "getAllLocations", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add New or Update Existing location',
        type: dtos_1.MessageResponseDto
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.CreateLocationRequestDto]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "upsertLocation", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get Basic Detail of location by ID for Side Section of detail pages',
        type: dtos_2.LocationBasicDetailResponseDto
    }),
    (0, common_1.Get)(':id/basic-detail'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "getBasicLocationDetailById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get complete detail of location by ID for detail page.',
        type: dtos_2.LocationCompleteDetailResponseDto
    }),
    (0, common_1.Get)(':id/complete-detail'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "getCompleteLocationDetailById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get paginated locations by filter criteria',
        type: dtos_2.PaginatedLocationListResponseDto
    }),
    (0, common_1.Post)('/list'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('orderBy')),
    __param(3, (0, common_1.Query)('orderDirection')),
    __param(4, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, location_filter_request_dto_1.LocationFilterRequestDto]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "getLocationsByFilter", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Export locations to excel',
    }),
    (0, common_1.Post)('/download'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, location_filter_request_dto_1.LocationFilterRequestDto]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "exportLocations", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete location by ID',
        type: dtos_1.MessageResponseDto
    }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "deleteLocation", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update Existing Location Status.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Patch)('/:id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_2.UpdateLocationStatusRequestDto]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "updateLocationStatus", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get capabilities (entries) list of location by ID.',
        type: location_capabilities_response_dto_1.LocationCapabilitiesResponseDto
    }),
    (0, common_1.Get)(':locationId/capabilities'),
    __param(0, (0, common_1.Param)('locationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "getLocationWiseCapabilities", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Export available capabilities (entries) list of location.',
    }),
    (0, common_1.Post)(':locationId/capabilities/download'),
    __param(0, (0, common_1.Param)('locationId')),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object, dtos_3.ExportLocationWiseCapabilityFilterRequestDto]),
    __metadata("design:returntype", Promise)
], LocationController.prototype, "exportLocationWiseCapabilities", null);
LocationController = __decorate([
    (0, swagger_1.ApiTags)('Location Management APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('location'),
    __metadata("design:paramtypes", [services_1.LocationService])
], LocationController);
exports.LocationController = LocationController;
//# sourceMappingURL=location.controller.js.map