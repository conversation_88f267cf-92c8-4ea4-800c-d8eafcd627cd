import CustomModal from '@/components/custom-modal/custom-modal';
import { RHFAutocomplete, RHFRadioGroup, RHFTextField } from '@/components/hook-form';
import { useLoading } from '@/hooks/use-loading';
import { useTranslate } from '@/locales/use-locales';
import { yupResolver } from '@hookform/resolvers/yup';
import { Checkbox, Chip, FormControl, Typography } from '@mui/material';
import { Box, Grid, Stack } from '@mui/system';
import { useContext, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import FormProvider from '@/components/hook-form';

import { CloseIcon } from 'yet-another-react-lightbox';
import * as Yup from 'yup';
import { LoadingButton } from '@mui/lab';
import { useMutation, useQuery } from 'react-query';
import { getCapabilitySearchDrodown, queryClient, upsertReport } from '@/shared/services';
import CustomDropdownCapability from '@/components/custom-search/custom-dropdown-components/custom-dropdown-capabilities';
import { statusColors } from '@/modules/home/<USER>/search-by-capability/config';
import { getIcon } from '@/shared/utils/get-icon';
import { ReportResponse, UpsertReport } from '@/shared/models/report.model';
import { enqueueSnackbar } from 'notistack';
import { UserPermissionsContext } from '@/core/contexts';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { useMsal } from '@azure/msal-react';

interface ModifyReportProps {
  open: boolean;
  initialData?: ReportResponse | undefined;
  onClose: () => void;
}
export default function ModifyReport({ open, onClose, initialData }: ModifyReportProps) {
  const { t } = useTranslate();
  const userPermissons = useContext(UserPermissionsContext);
  const { setLoading, setMessage } = useLoading();
  const startLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(true);
      setMessage(t('submitting'));
    }
  };

  const stopLoadingState = () => {
    if (setLoading && setMessage) {
      setLoading(false);
      setMessage('');
    }
  };
  Yup.setLocale({
    mixed: {
      required: t('messages.field_is_required'),
    },
  });

  const EventSchema = Yup.object().shape({
    title: Yup.string().default('').required(),
    description: Yup.string().default('').required(),
    isPersonalReport: Yup.string().default('yes').required(),
    capabilityIds: Yup.array().required().default([]).min(1, t('messages.min_1_needs_selection')),
  });
  const methods = useForm({
    resolver: yupResolver(EventSchema),
  });
  const { data: capabilities } = useQuery(['capabilities'], () => getCapabilitySearchDrodown());
  const {
    reset,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: { isSubmitting },
  } = methods;
  const reportOptions = [{ label: t('label.custom'), value: 'yes' }];
  if (userPermissons.some((permission) => permission.permissionName == PERMISSIONS.APPLICATION_ADMIN)) {
    reportOptions.push({ label: t('label.global'), value: 'no' });
  }
  const iconSize = 35,
    chipLabelStyle = {
      borderRadius: 3,
      fontSize: { xs: 10, sm: 12 },
      fontWeight: 'bold',
      color: '#fff',
      height: { xs: 18, sm: 20 },
    },
    listItemStyle = { p: 1.5, boxShadow: 1 };
  const handleFormSubmit = async (payload: UpsertReport): Promise<ReportResponse> => {
    return upsertReport(payload); //TODO Update this
  };

  const { mutateAsync } = useMutation({
    mutationFn: handleFormSubmit,
    onSuccess: (_response: ReportResponse) => {
      queryClient.invalidateQueries(['reports']);
      onClose();
      reset();
      enqueueSnackbar(t('messages.report_updated_successfully'), {
        variant: 'success',
      });
      stopLoadingState();
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      stopLoadingState();
    },
  });
  const onSubmit = handleSubmit(
    async (data) => {
      startLoadingState();
      //   con;
      // Setting up request data
      const formattedData = {
        ...data,
        capabilityIds: data.capabilityIds.map((item) => item.value),
        ...(initialData?.id && { id: initialData.id }),
        isPersonalReport: data.isPersonalReport == 'yes',
      };
      await mutateAsync(formattedData);
      reset();

      stopLoadingState();
    },
    (errors) => {
      console.log(errors);
    },
  );
  useEffect(() => {
    if (initialData && capabilities) {
      setValue('title', initialData.title);
      setValue('isPersonalReport', initialData.isPersonalReport ? 'yes' : 'no');
      setValue('description', initialData.description);
      const capabilitiesResult = [];
      for (let index = 0; index < initialData.capabilityIds.length; index++) {
        const id = initialData.capabilityIds[index];
        const foundItem = capabilities.find((item: any) => item.id == id);
        if (foundItem) {
          capabilitiesResult.push({ label: foundItem.capability, value: foundItem.id, option: foundItem });
        }
      }
      setValue('capabilityIds', capabilitiesResult, { shouldValidate: true });
    } else {
      reset();
    }
  }, [initialData, capabilities]);
  useEffect(() => {
    return () => {
      reset();
    };
  }, []);
  return (
    <CustomModal
      isOpen={open}
      handleClose={(_) => {
        onClose();
        reset();
      }}
    >
      <Box display={'flex'} justifyContent={'space-between'} alignItems={'center'} mb={2}>
        <Typography variant="mainTitle" component="h2" style={{ verticalAlign: 'middle' }}>
          {Boolean(initialData) ? t('label.update') : t('label.create')} {t('label.report')}
        </Typography>
        <div
          onClick={(_event) => {
            onClose();
            reset();
          }}
          style={{ cursor: 'pointer' }}
        >
          <CloseIcon />
        </div>
      </Box>
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <Stack direction={'column'} spacing={2}>
          <Grid container size={12} spacing={2} gap={2}>
            <Grid size={6}>
              <FormControl fullWidth>
                <RHFTextField
                  name="title"
                  placeholder={`${t('placeholder.please_enter')} ${t('label.title')}`}
                  label={`${t('label.title')} *`}
                  onChange={(event) => {
                    setValue('title', event.target.value, { shouldValidate: true });
                  }}
                />
              </FormControl>{' '}
            </Grid>
            <Grid size={6}>
              <FormControl fullWidth>
                <RHFRadioGroup
                  row={true}
                  spacing={1}
                  // disabled={initialData && initialData.createdBy != accounts[0].username}
                  label="Type"
                  name="isPersonalReport"
                  defaultValue={'yes'}
                  // value={getValues('isPersonalReport')}
                  // onChange={(event) => {
                  //   setValue('isPersonalReport', event.target.value);
                  // }}
                  options={reportOptions}
                ></RHFRadioGroup>
              </FormControl>
            </Grid>
            <Grid size={12} maxHeight={'200px'} sx={{ overflowY: 'auto' }} mt={0}>
              {<Typography variant="label">{t('label.description')} *</Typography>}

              <FormControl fullWidth>
                <RHFTextField
                  name="description"
                  multiline
                  minRows={3}
                  placeholder={`${t('placeholder.please_enter')} ${t('label.description')}`}
                  // label={`${t('label.description')} *`}
                  onChange={(event) => {
                    setValue('description', event.target.value, { shouldValidate: true });
                  }}
                />
              </FormControl>
            </Grid>{' '}
            <Grid size={12} maxHeight={'150px'} sx={{ overflowY: 'auto' }}>
              {<Typography variant="label">{t('label.capabilities')} *</Typography>}
              <FormControl fullWidth>
                <RHFAutocomplete
                  name="capabilityIds"
                  multiple
                  disableCloseOnSelect
                  options={
                    capabilities
                      ? capabilities.map((item: any) => ({ label: item.capability, value: item.id, option: item }))
                      : []
                  }
                  onChange={(_event, value) => {
                    setValue('capabilityIds', value);
                  }}
                  value={watch('capabilityIds') ?? []}
                  isOptionEqualToValue={(option, value) => {
                    //@ts-ignore
                    return option.value == value.value;
                  }}
                  filterOptions={(options, { inputValue }) => {
                    return options.filter((option: any) =>
                      option.label.toLowerCase().includes(inputValue.toLowerCase()),
                    );
                  }}
                  renderOption={(props, option: any) => {
                    // Custom Dropdown Component can be Rendered Here
                    return (
                      <Stack direction={'row'} key={option.value}>
                        <Checkbox
                          size="small"
                          disableRipple
                          onChange={(event, checked) => {
                            const current = getValues('capabilityIds');

                            const checkedIndex = current?.findIndex((item) => item.value == option.value) ?? -1;

                            if (checkedIndex == -1) {
                              const newIds = [...(current ?? []), option];
                              setValue('capabilityIds', [...newIds]);
                            } else {
                              current.splice(checkedIndex, 1);
                              setValue('capabilityIds', [...current]);
                            }
                          }}
                          checked={
                            Boolean(getValues('capabilityIds')?.find((item) => item.value == option.value))
                              ? true
                              : false
                          }
                        />
                        <CustomDropdownCapability
                          props={props}
                          option={option.option}
                          key={option.option.id}
                          getIconPath={getIcon}
                          iconSize={iconSize}
                          listItemStyle={listItemStyle}
                          chipLabelStyle={chipLabelStyle}
                          statusColors={statusColors}
                          showChips={false}
                        />
                      </Stack>
                    );
                  }}
                  renderTags={(selected, getTagProps) => {
                    return selected.map((option, index) => (
                      //@ts-ignore
                      <Chip {...getTagProps({ index })} key={option.value} label={option.label} />
                    ));
                  }}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Stack>{' '}
        <Box display={'flex'} justifyContent={'flex-end'} marginTop={'15px'}>
          <LoadingButton onClick={onSubmit} variant="contained" disabled={isSubmitting}>
            {isSubmitting ? t('btn_name.processing') : t('btn_name.save')}
          </LoadingButton>
        </Box>
      </FormProvider>
    </CustomModal>
  );
}
