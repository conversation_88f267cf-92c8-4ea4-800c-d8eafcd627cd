import { LocationRepository, PartnerBranchRepository } from "../repositories";
import { ActionPartnerConnectionRequestDto, CreatePartnerBranchRequestDto, PartnerBranchResponseDto } from "../dtos";
import { CurrentContext } from "src/shared/types";
import { MessageResponseDto } from "src/shared/dtos";
import { DatabaseHelper } from "src/shared/helpers";
import { SharedPermissionService } from "src/shared/services";
import { HistoryApiClient } from "src/shared/clients";
export declare class PartnerBranchService {
    private readonly permissionService;
    private readonly partnerBranchRepository;
    private readonly locationRepository;
    private readonly historyService;
    private readonly databaseHelper;
    constructor(permissionService: SharedPermissionService, partnerBranchRepository: PartnerBranchRepository, locationRepository: LocationRepository, historyService: HistoryApiClient, databaseHelper: DatabaseHelper);
    newPartnerBranch(createPartnerBranchDto: CreatePartnerBranchRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getPartnerBranches(locationId: number, currentContext: CurrentContext): Promise<PartnerBranchResponseDto[]>;
    private mapLocationDetail;
    actionPartnerConnection(actionDto: ActionPartnerConnectionRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
}
