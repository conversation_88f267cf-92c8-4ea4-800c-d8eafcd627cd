import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/shared/repositories';
import { MasterCapability, MetaEvidence } from '../models';
import { CoreSolution } from 'src/metadata/models';
import { col, Op, where } from 'sequelize';
import { CapabilityCategory } from '../models/master-capability-category.model';
import { CurrentContext } from 'src/shared/types';
import { MasterCapabilityFilterRequestDto } from '../dtos';

@Injectable()
export class CapabilityRepository extends BaseRepository<MasterCapability> {
	constructor() {
		super(MasterCapability);
	}

	public async getMasterCapabilityById(id: number): Promise<MasterCapability> {
		return this.findOne({
			where: { id },
			include: [
				{
					model: MetaEvidence,
					as: 'evidence',
					attributes: ['id', 'name', 'code', 'type'],
					required: false,
				},
			],
		});
	}

	public async searchMasterCapabilityByCoreSolution(
		searchTerm: string,
		coreSolutionId?: number,
	): Promise<MasterCapability[]> {
		return this.findAll({
			where: {
				...(searchTerm && { capability: { [Op.iLike]: `%${searchTerm}%` } }),
				...(coreSolutionId && {
					[Op.or]: [{ coreSolutionId: coreSolutionId }, { coreSolutionId: null }],
				}),
			},
			include: [
				{
					model: CoreSolution,
					as: 'coreSolution',
					attributes: ['id', 'title', 'code', 'pillar'],
					required: false,
					where: {
						active: true,
						deleted: false,
					},
				},
				{
					model: CapabilityCategory,
					as: 'category',
					attributes: ['title'],
					required: true,
					where: {
						active: true,
						deleted: false,
					},
				},
			],
		});
	}

	public async getMasterCapabilityDetailById(
		capabilityId: number,
	): Promise<MasterCapability | null> {
		return this.findOne({
			where: { id: capabilityId },
			include: [
				{
					model: CoreSolution,
					as: 'coreSolution',
					attributes: ['id', 'title', 'code', 'pillar'],
					required: false,
					where: {
						active: true,
						deleted: false,
					},
				},
				{
					model: MetaEvidence,
					as: 'evidence',
					attributes: ['id', 'name', 'description', 'code', 'type'],
					required: false,
				},
				{
					model: CapabilityCategory,
					as: 'category',
					required: true,
					where: {
						active: true,
						deleted: false,
					},
				},
			],
		});
	}

	public async getMasterCapabilityDetailByCapabilityIds(
		capabilityIds: number[],
	): Promise<MasterCapability[]> {
		return this.findAll({
			where: { id: { [Op.in]: capabilityIds } },
			include: [
				{
					model: CapabilityCategory,
					as: 'category',
					required: true,
					where: {
						active: true,
						deleted: false,
					},
				},
			],
		});
	}

	public async setupNewCapability(
		payload: any,
		currentContext: CurrentContext,
	): Promise<MasterCapability> {
		const entryDetail = new MasterCapability(payload);
		return this.save(entryDetail, currentContext);
	}

	public async isCombinationDataExist(payload: any): Promise<boolean> {
		return this.isRecordExist({
			where: payload,
		});
	}

	public async getCapabilitiesByFilter(
		filterDto: MasterCapabilityFilterRequestDto,
		page: number,
		limit: number,
	): Promise<{ rows: MasterCapability[]; count: number }> {
		const whereClause = this.buildWhereClause(filterDto);

		return this.findAndCountAll({
			where: whereClause,
			include: [
				{
					model: CoreSolution,
					as: 'coreSolution',
					attributes: ['id', 'title', 'code', 'pillar'],
					required: false,
					where: {
						active: true,
						deleted: false,
					},
				},
				{
					model: CapabilityCategory,
					as: 'category',
					attributes: ['title'],
					required: true,
					where: {
						active: true,
						deleted: false,
					},
				},
				{
					model: MetaEvidence,
					as: 'evidence',
					attributes: ['id', 'name', 'description', 'code', 'type'],
					required: false,
				},
			],
			...(page && { offset: (page - 1) * limit }),
			...(limit && { limit }),
			order: [['updatedOn', 'DESC']],
			// subQuery: false,
			distinct: true,
		});
	}

	private buildWhereClause(filterDto: MasterCapabilityFilterRequestDto) {
		const { capabilityTypes, coreSolutionIds, verticalCodes, searchTerm, capabilityLevel } = filterDto;

		const whereClause: any = {};
		const andCondition = [];

		if (searchTerm && searchTerm.length) {
			andCondition.push({
				[Op.or]: [
					{ capability: { [Op.iLike]: `%${searchTerm}%` } },
					{ product: { [Op.iLike]: `%${searchTerm}%` } },
					{ subCategory: { [Op.iLike]: `%${searchTerm}%` } },
				],
			});
		}

		if (capabilityTypes && capabilityTypes.length) {
			andCondition.push({
				capabilityType: { [Op.in]: capabilityTypes },
			});
		}

		if (coreSolutionIds && coreSolutionIds.length) {
			andCondition.push({
				coreSolutionId: { [Op.in]: coreSolutionIds },
			});
		}

		if (verticalCodes && verticalCodes.length) {
			andCondition.push({
				[Op.and]: [
					{
						[Op.or]: verticalCodes.map(code =>
							where(col('verticals'), Op.contains, JSON.stringify([code])),
						),
					},
					{
						verticals: {
							[Op.not]: null,
						},
					},
				],
			});
		}

		if (capabilityLevel && capabilityLevel.length) {
			andCondition.push({
				level: { [Op.in]: capabilityLevel },
			});
		}

		if (andCondition.length > 0) {
			whereClause[Op.and] = andCondition;
		}

		return whereClause;
	}
}
