import { BaseModel } from 'src/shared/models';
import { CommonDropdown, CoreSolution, LocationType } from 'src/metadata/models';
import { LegalEntity } from './legal-entity.model';
import { LEASE_OWNERSHIP_STATUS_TYPE, LOCATION_STATUS_TYPE } from 'src/shared/enums';
import { Brands, CountryDetail, FFLocationIds, GoogleListing, LocationOtherDetails } from '../types';
import { UserPermission } from 'src/permission/models';
import { LocationIndustryVertical } from './location_industry_vertical.model';
import { ContactDetail } from 'src/contact-details/models';
import { PartnerBranch } from './partner-branch.model';
import { LocationWiseCapabilityDetail } from 'src/capability/models';
export declare class Location extends BaseModel<Location> {
    entityId: number;
    entityCode: string;
    entityTitle: string;
    entityType: string;
    coreSolutionId: number;
    coreSolution: CoreSolution;
    locationTypeId: number;
    locationType: LocationType;
    branchArchetypeCode: string;
    branchArchetype: CommonDropdown;
    locationName: string;
    legalEntityId: number;
    legalEntity: LegalEntity;
    leaseOwnershipStatus: LEASE_OWNERSHIP_STATUS_TYPE;
    status: LOCATION_STATUS_TYPE;
    statusComment: string;
    statusDate: Date;
    strategicClassificationCode: string;
    strategicClassification: CommonDropdown;
    ffLocationIds: FFLocationIds[];
    brands: Brands[];
    latitude: string;
    longitude: string;
    googleListing: GoogleListing;
    tags: string[];
    otherDetails: LocationOtherDetails;
    countryDetail: CountryDetail;
    partnerBranches: PartnerBranch[];
    userPermission: UserPermission[];
    locationIndustryVerticals: LocationIndustryVertical[];
    contacts: ContactDetail[];
    capabilities: LocationWiseCapabilityDetail[];
}
