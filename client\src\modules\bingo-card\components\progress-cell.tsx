import { Box } from '@mui/material';
import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';
import { CAPABILITY_STATUS_COLOR_ENUM } from '@/shared/enum/capability.enum';

interface ProgressCellProps {
  node: TreeViewDataType;
  capability: string;
  renderAs?: 'div' | 'td';
  customKey: string;
  columnWidth: any;
}

export const ProgressCell = ({ node, capability, renderAs = 'td', customKey, columnWidth }: ProgressCellProps) => {
  const getProgress = () => {

    console.info('<><><><><><><><><><><><><><><><><><><><><><>');
    console.log(node);
    console.log(capability);
    console.info('<><><><><><><><><><><><><><><><><><><><><><>');
    const capabilityCount = node?.capabilityList?.find((x) => x.capability === capability)?.count ?? 0;
    const locationCount = node?.locationCount ?? 0;

    const percentage = locationCount === 0 ? 0 : capabilityCount / locationCount;
    const displayPercentage = Math.round(percentage * 100) + '%';

    const colorMain = CAPABILITY_STATUS_COLOR_ENUM.EXISTING;
    const colorWhite = '#FFFFFF';

    const hex = (x: any) => {
      x = x.toString(16);
      return x.length === 1 ? '0' + x : x;
    };

    const interpolateColor = (from: string, to: string, t: number) => {
      const rf = parseInt(from.slice(1, 3), 16);
      const gf = parseInt(from.slice(3, 5), 16);
      const bf = parseInt(from.slice(5, 7), 16);

      const rt = parseInt(to.slice(1, 3), 16);
      const gt = parseInt(to.slice(3, 5), 16);
      const bt = parseInt(to.slice(5, 7), 16);

      const r = Math.round(rf * (1 - t) + rt * t);
      const g = Math.round(gf * (1 - t) + gt * t);
      const b = Math.round(bf * (1 - t) + bt * t);

      return `#${hex(r)}${hex(g)}${hex(b)}`;
    };

    const backgroundColor = interpolateColor(colorWhite, colorMain, percentage);

    let fontColor = '#000000';
    if (percentage <= 0) {
      fontColor = '#000000';
    } else if (percentage >= 0.35 && percentage <= 0.55) {
      fontColor = '#4c4b4b';
    } else {
      fontColor = interpolateColor(colorMain, colorWhite, percentage);
    }

    return {
      percentage: displayPercentage,
      color: backgroundColor,
      fontColor,
    };
  };

  const progress = getProgress();

  if (renderAs === 'td') {
    return (
      <td
        key={customKey}
        style={{
          border: '1px solid #e0e0e0',
          minWidth: columnWidth,
          width: columnWidth,
          backgroundColor: progress.color,
          color: progress.fontColor,
          padding: '2px',
          textAlign: 'center',
          fontSize: '0.80rem',
        }}
      >
        {progress.percentage}
      </td>
    );
  }

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: progress.color,
        color: progress.fontColor,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '0.80rem',
      }}
    >
      {progress.percentage}
    </div>
  );
};
