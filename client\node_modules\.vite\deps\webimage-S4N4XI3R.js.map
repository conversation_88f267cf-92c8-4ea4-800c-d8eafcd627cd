{"version": 3, "sources": ["../../geotiff/dist-module/compression/webimage.js"], "sourcesContent": ["import BaseDecoder from './basedecoder.js';\n\n/**\n * class WebImageDecoder\n *\n * This decoder uses the browsers image decoding facilities to read image\n * formats like WebP when supported.\n */\nexport default class WebImageDecoder extends BaseDecoder {\n  constructor() {\n    super();\n    if (typeof createImageBitmap === 'undefined') {\n      throw new Error('Cannot decode WebImage as `createImageBitmap` is not available');\n    } else if (typeof document === 'undefined' && typeof OffscreenCanvas === 'undefined') {\n      throw new Error('Cannot decode WebImage as neither `document` nor `OffscreenCanvas` is not available');\n    }\n  }\n\n  async decode(fileDirectory, buffer) {\n    const blob = new Blob([buffer]);\n    const imageBitmap = await createImageBitmap(blob);\n\n    let canvas;\n    if (typeof document !== 'undefined') {\n      canvas = document.createElement('canvas');\n      canvas.width = imageBitmap.width;\n      canvas.height = imageBitmap.height;\n    } else {\n      canvas = new OffscreenCanvas(imageBitmap.width, imageBitmap.height);\n    }\n\n    const ctx = canvas.getContext('2d');\n    ctx.drawImage(imageBitmap, 0, 0);\n\n    // TODO: check how many samples per pixel we have, and return RGB/RGBA accordingly\n    // it seems like GDAL always encodes via RGBA which does not require a translation\n\n    return ctx.getImageData(0, 0, imageBitmap.width, imageBitmap.height).data.buffer;\n  }\n}\n"], "mappings": ";;;;;;AAQA,IAAqB,kBAArB,cAA6C,YAAY;AAAA,EACvD,cAAc;AACZ,UAAM;AACN,QAAI,OAAO,sBAAsB,aAAa;AAC5C,YAAM,IAAI,MAAM,gEAAgE;AAAA,IAClF,WAAW,OAAO,aAAa,eAAe,OAAO,oBAAoB,aAAa;AACpF,YAAM,IAAI,MAAM,qFAAqF;AAAA,IACvG;AAAA,EACF;AAAA,EAEA,MAAM,OAAO,eAAe,QAAQ;AAClC,UAAM,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;AAC9B,UAAM,cAAc,MAAM,kBAAkB,IAAI;AAEhD,QAAI;AACJ,QAAI,OAAO,aAAa,aAAa;AACnC,eAAS,SAAS,cAAc,QAAQ;AACxC,aAAO,QAAQ,YAAY;AAC3B,aAAO,SAAS,YAAY;AAAA,IAC9B,OAAO;AACL,eAAS,IAAI,gBAAgB,YAAY,OAAO,YAAY,MAAM;AAAA,IACpE;AAEA,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,QAAI,UAAU,aAAa,GAAG,CAAC;AAK/B,WAAO,IAAI,aAAa,GAAG,GAAG,YAAY,OAAO,YAAY,MAAM,EAAE,KAAK;AAAA,EAC5E;AACF;", "names": []}