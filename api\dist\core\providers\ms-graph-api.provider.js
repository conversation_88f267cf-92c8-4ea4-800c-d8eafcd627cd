"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MSGraphProviders = void 0;
const msal = __importStar(require("@azure/msal-node"));
const config_service_1 = require("../../config/config.service");
const constants_1 = require("../../shared/constants");
const services_1 = require("../../shared/services");
exports.MSGraphProviders = [
    {
        provide: constants_1.MS_GRAPH_API.MS_GRAPH_API_PROVIDER,
        useFactory: (configService) => __awaiter(void 0, void 0, void 0, function* () {
            const { azureAD } = configService.getAppConfig();
            const { clientId, authority, tennantId, clientSecret } = azureAD;
            const msalConfig = {
                auth: {
                    clientId: clientId,
                    authority: `${authority}/${tennantId}`,
                    clientSecret: clientSecret,
                },
            };
            return new msal.ConfidentialClientApplication(msalConfig);
        }),
        inject: [config_service_1.ConfigService],
    },
    {
        provide: constants_1.MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER,
        useFactory: (configService) => {
            const { azureAD } = configService.getAppConfig();
            const { graphApiUrl, graphApiVersion } = azureAD;
            const url = `${graphApiUrl}/${graphApiVersion}`;
            return new services_1.HttpService().withBaseUrl(url);
        },
        inject: [config_service_1.ConfigService],
    },
];
//# sourceMappingURL=ms-graph-api.provider.js.map