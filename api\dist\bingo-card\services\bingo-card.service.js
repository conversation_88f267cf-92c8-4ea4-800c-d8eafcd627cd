"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BingoCardService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../repositories");
const dtos_1 = require("../dtos");
const helpers_1 = require("../../shared/helpers");
const repositories_2 = require("../../capability/repositories");
const repositories_3 = require("../../location/repositories");
const list_reports_response_dto_1 = require("../dtos/response/list-reports.response.dto");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
let BingoCardService = class BingoCardService {
    constructor(bingoCardConfigRepository, capabilityRepository, capabilityLegRepository, locationRepository, adminApiClient, historyService) {
        this.bingoCardConfigRepository = bingoCardConfigRepository;
        this.capabilityRepository = capabilityRepository;
        this.capabilityLegRepository = capabilityLegRepository;
        this.locationRepository = locationRepository;
        this.adminApiClient = adminApiClient;
        this.historyService = historyService;
    }
    getAllBingoCardConfigs() {
        return __awaiter(this, void 0, void 0, function* () {
            const configs = yield this.bingoCardConfigRepository.getAllBingoCardConfigs();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.BingoCardConfigResponseDto, configs);
        });
    }
    listReports(currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const reports = yield this.bingoCardConfigRepository.getAllBingoCardConfigs(currentContext.user.username);
            return (0, helpers_1.multiObjectToInstance)(list_reports_response_dto_1.ReportResponseDto, reports);
        });
    }
    createReport(createReportDTO, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = createReportDTO, restOfData = __rest(createReportDTO, ["id"]);
            const existingReport = yield this.bingoCardConfigRepository.getExistingReportByTitle(restOfData.title, id, currentContext.user.username);
            if (existingReport.length) {
                throw new common_1.HttpException('Report with same title exists for your account', common_1.HttpStatus.CONFLICT);
            }
            if (!restOfData.isPersonalReport) {
                const hasPermission = yield this.adminApiClient.hasPermissionToUser(currentContext.user.username, enums_1.PERMISSIONS.APPLICATION_ADMIN);
                if (!hasPermission) {
                    throw new common_1.HttpException(`Unauthorized to ${id != undefined ? 'edit' : 'add'} global report`, common_1.HttpStatus.UNAUTHORIZED);
                }
            }
            let upsertedReport = null;
            if (id) {
                upsertedReport = (0, helpers_1.singleObjectToInstance)(list_reports_response_dto_1.ReportResponseDto, yield this.bingoCardConfigRepository.updateReport(id, restOfData, currentContext));
            }
            else {
                upsertedReport = (0, helpers_1.singleObjectToInstance)(list_reports_response_dto_1.ReportResponseDto, yield this.bingoCardConfigRepository.createReport(restOfData, currentContext));
            }
            yield this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: upsertedReport.id,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.REPORT,
                action_performed: id ? enums_1.HISTORY_ACTION_TYPE.UPDATE : enums_1.HISTORY_ACTION_TYPE.CREATE,
                action_date: new Date(),
                comments: id ? `Report updated` : `Report created`,
                additional_info: createReportDTO,
            });
            return upsertedReport;
        });
    }
    deleteReport(currentContext, id) {
        return __awaiter(this, void 0, void 0, function* () {
            const config = yield this.bingoCardConfigRepository.getBingoCardConfigById(id);
            if (!config) {
                throw new common_1.HttpException('Config not found', common_1.HttpStatus.NOT_FOUND);
            }
            if (config.isPersonalReport &&
                config.createdBy.toLowerCase() != currentContext.user.username.toLowerCase()) {
                throw new common_1.HttpException('Unauthorized to delete report', common_1.HttpStatus.UNAUTHORIZED);
            }
            if (!config.isPersonalReport) {
                const hasPermission = yield this.adminApiClient.hasPermissionToUser(currentContext.user.username, enums_1.PERMISSIONS.APPLICATION_ADMIN);
                if (!hasPermission) {
                    throw new common_1.HttpException('Unauthorized to delete report', common_1.HttpStatus.UNAUTHORIZED);
                }
            }
            yield this.bingoCardConfigRepository.deleteById(config.id, currentContext);
            yield this.historyService.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: config.id,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.REPORT,
                action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                action_date: new Date(),
                comments: 'Report deleted',
            });
            return { message: 'Report deleted succesfully' };
        });
    }
    getBingoCardConfigById(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const config = yield this.bingoCardConfigRepository.getBingoCardConfigById(id);
            if (!config) {
                throw new common_1.HttpException('Config not found', common_1.HttpStatus.NOT_FOUND);
            }
            if (config.isPersonalReport &&
                config.createdBy.toLowerCase() != currentContext.user.username.toLowerCase()) {
                throw new common_1.HttpException('Configuration not available for your account', common_1.HttpStatus.UNAUTHORIZED);
            }
            const capabilities = yield this.capabilityRepository.getMasterCapabilityDetailByCapabilityIds(config.capabilityIds);
            if (!capabilities.length) {
                throw new common_1.HttpException('No entries found for this config', common_1.HttpStatus.NOT_FOUND);
            }
            const allLegs = yield this.capabilityLegRepository.getAllLegs();
            const capabilitiesWithLegs = capabilities.map(capability => {
                const legs = allLegs.filter(leg => capability.legs.includes(leg.id));
                return Object.assign(Object.assign({}, capability), { legs });
            });
            return (0, helpers_1.singleObjectToInstance)(dtos_1.BingoCardConfigWithCapabilitiesResponseDto, Object.assign(Object.assign({}, config), { capabilities: capabilitiesWithLegs }));
        });
    }
    getConfigLocationCapabilities(id, filters) {
        return __awaiter(this, void 0, void 0, function* () {
            const config = yield this.bingoCardConfigRepository.getBingoCardConfigById(id);
            if (!config) {
                throw new common_1.HttpException('Config not found', common_1.HttpStatus.NOT_FOUND);
            }
            const { capabilityIds } = config;
            if (!capabilityIds.length) {
                throw new common_1.HttpException('No entries found for this config', common_1.HttpStatus.NOT_FOUND);
            }
            const locations = yield this.locationRepository.getAllLocationsWithCapabilities(capabilityIds, filters);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.BingoLocationCapabilitiesResponse, locations, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
        });
    }
};
BingoCardService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.BingoCardConfigRepository,
        repositories_2.CapabilityRepository,
        repositories_2.CapabilityLegRepository,
        repositories_3.LocationRepository,
        clients_1.AdminApiClient,
        clients_1.HistoryApiClient])
], BingoCardService);
exports.BingoCardService = BingoCardService;
//# sourceMappingURL=bingo-card.service.js.map