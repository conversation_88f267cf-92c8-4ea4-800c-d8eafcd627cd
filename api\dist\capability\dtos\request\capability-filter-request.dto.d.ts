import { CAPABILITY_LEVEL_ENUM, CAPABILITY_STATUS_ENUM, CAPABILITY_TYPE_ENUM, PROVIDER_ENUM } from "src/shared/enums";
export declare class CapabilityFilterRequestDto {
    entityIds?: number[];
    entryIds?: number[];
    searchTerm?: string;
    capabilityTypes?: CAPABILITY_TYPE_ENUM[];
    capabilityLevel?: CAPABILITY_LEVEL_ENUM[];
    coreSolutionIds?: number[];
    statuses?: CAPABILITY_STATUS_ENUM[];
    providers?: PROVIDER_ENUM[];
    verticalCodes?: string[];
    ownerLoginId?: string;
    hierarchyEntityId?: number;
}
export declare class ExportLocationWiseCapabilityFilterRequestDto {
    searchTerm?: string;
    capabilityTypes?: CAPABILITY_TYPE_ENUM[];
    capabilityLevel?: CAPABILITY_LEVEL_ENUM[];
    statuses?: CAPABILITY_STATUS_ENUM[];
    providers?: PROVIDER_ENUM[];
    verticalCodes?: string[];
}
