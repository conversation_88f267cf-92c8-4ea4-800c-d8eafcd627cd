import { ConsoleLogger } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
export declare class LoggerService {
    private readonly logger;
    private readonly config;
    constructor(logger: ConsoleLogger, config: ConfigService);
    log(message: any, context?: string): void;
    error(message: any, stack?: string, context?: string): void;
    warn(message: any, context?: string): void;
    debug(message: any, context?: string): void;
    verbose(message: any, context?: string): void;
}
