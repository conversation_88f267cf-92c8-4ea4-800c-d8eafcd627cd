import { PartnerBranchService } from "../services";
import { RequestContext } from "src/shared/types";
import { MessageResponseDto } from "src/shared/dtos";
import { ActionPartnerConnectionRequestDto, CreatePartnerBranchRequestDto, PartnerBranchResponseDto } from "../dtos";
export declare class PartnerBranchController {
    private readonly partnerBranchService;
    constructor(partnerBranchService: PartnerBranchService);
    newPartnerBranch(request: RequestContext, createPartnerBranchDto: CreatePartnerBranchRequestDto): Promise<MessageResponseDto>;
    actionPartnerConnection(actionDto: ActionPartnerConnectionRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    getPartnerBranches(id: number, request: RequestContext): Promise<PartnerBranchResponseDto[]>;
}
