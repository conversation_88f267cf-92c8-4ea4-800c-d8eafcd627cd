import { BaseModel } from 'src/shared/models';
import { CommonDropdown } from './common-dropdown.model';
import { LocationType } from './location-type.model';
import { CORES_SOLUTION_ENUM, LEGAL_ENTITY_FORM_SECTION, PILLAR } from 'src/shared/enums';
import { Location } from 'src/location/models';
import { MasterCapability } from 'src/capability/models';
export declare class CoreSolution extends BaseModel<CoreSolution> {
    title: string;
    code: CORES_SOLUTION_ENUM;
    legalEntityFormSections: LEGAL_ENTITY_FORM_SECTION;
    pillar: PILLAR;
    commonDropdowns: CommonDropdown[];
    locationTypes: LocationType[];
    locations: Location[];
    masterCapabilities: MasterCapability[];
}
