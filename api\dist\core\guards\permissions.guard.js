"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const repositories_1 = require("../../permission/repositories");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const services_1 = require("../../shared/services");
let PermissionsGuard = class PermissionsGuard {
    constructor(reflector, adminApiClient, permissionService, userPermissionRepository) {
        this.reflector = reflector;
        this.adminApiClient = adminApiClient;
        this.permissionService = permissionService;
        this.userPermissionRepository = userPermissionRepository;
    }
    canActivate(context) {
        return __awaiter(this, void 0, void 0, function* () {
            const routePermission = this.reflector.get('permissions', context.getHandler());
            let request = context.switchToHttp().getRequest();
            const { body, user, query, params } = request;
            const { permission, checkEntity } = routePermission;
            let entityId = null;
            if (permission === enums_1.PERMISSIONS.ANY) {
                return true;
                const [globalPermissions, localPermissions] = yield Promise.all([
                    this.adminApiClient.getListOfUserPermissions(user.unique_name.toLowerCase()),
                    this.userPermissionRepository.getUserPermissionByLoginId(user.unique_name.toLowerCase()),
                ]);
                if ((globalPermissions === null || globalPermissions === void 0 ? void 0 : globalPermissions.length) || (localPermissions === null || localPermissions === void 0 ? void 0 : localPermissions.length)) {
                    return true;
                }
                return false;
            }
            if (checkEntity && ((body === null || body === void 0 ? void 0 : body.entityId) || (query === null || query === void 0 ? void 0 : query.entityId) || (params === null || params === void 0 ? void 0 : params.entityId))) {
                entityId = (body === null || body === void 0 ? void 0 : body.entityId) || (query === null || query === void 0 ? void 0 : query.entityId) || (params === null || params === void 0 ? void 0 : params.entityId);
            }
            if (typeof permission === 'string') {
                const locations = yield this.permissionService.getAllLocationIdForGivenPermission(user.unique_name, permission);
                request.user.locations = locations || [];
                return this.adminApiClient.hasPermissionToUser(user.unique_name, permission, entityId);
            }
            else if (Array.isArray(permission)) {
                const globalPermissions = permission.filter((p) => !p.startsWith('Local.'));
                const locationPermissions = permission.filter((p) => p.startsWith('Local.'));
                if (globalPermissions === null || globalPermissions === void 0 ? void 0 : globalPermissions.length) {
                    const isAllowed = yield this.permissionService.checkGlobalPermission(globalPermissions, user, entityId);
                    if (isAllowed) {
                        return true;
                    }
                }
                if (locationPermissions === null || locationPermissions === void 0 ? void 0 : locationPermissions.length) {
                    let locationId = null;
                    if ((body === null || body === void 0 ? void 0 : body.locationId) || (query === null || query === void 0 ? void 0 : query.locationId) || (params === null || params === void 0 ? void 0 : params.locationId)) {
                        locationId = (body === null || body === void 0 ? void 0 : body.locationId) || (query === null || query === void 0 ? void 0 : query.locationId) || (params === null || params === void 0 ? void 0 : params.locationId);
                    }
                    const isAllowed = yield this.permissionService.checkLocalPermission(locationPermissions, user, locationId);
                    if (isAllowed) {
                        return true;
                    }
                }
            }
            return false;
        });
    }
};
PermissionsGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector,
        clients_1.AdminApiClient,
        services_1.SharedPermissionService,
        repositories_1.UserPermissionRepository])
], PermissionsGuard);
exports.PermissionsGuard = PermissionsGuard;
//# sourceMappingURL=permissions.guard.js.map