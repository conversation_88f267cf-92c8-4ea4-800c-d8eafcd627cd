import { BaseModel } from 'src/shared/models';
import { CommonDropdown, CoreSolution } from 'src/metadata/models';
import { LOCATION_FORM_SECTION, LOCATION_TYPE_ENUM } from 'src/shared/enums';
import { Location } from 'src/location/models';
export declare class LocationType extends BaseModel<LocationType> {
    title: string;
    code: LOCATION_TYPE_ENUM;
    coreSolutionId: number;
    coreSolution: CoreSolution;
    canAcquireCapability: boolean;
    locationFormSections: LOCATION_FORM_SECTION[];
    commonDropdowns: CommonDropdown[];
    locations: Location[];
}
