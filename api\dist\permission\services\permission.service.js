"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionService = void 0;
const common_1 = require("@nestjs/common");
const pagination_1 = require("../../core/pagination");
const repositories_1 = require("../../location/repositories");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const dtos_1 = require("../dtos");
const repositories_2 = require("../repositories");
let PermissionService = class PermissionService {
    constructor(adminApiClient, accessControlConfigRepository, userPermissionRepository, locationRepository, mSGraphApiClient, sharedPermissionService, historyService, databaseHelper) {
        this.adminApiClient = adminApiClient;
        this.accessControlConfigRepository = accessControlConfigRepository;
        this.userPermissionRepository = userPermissionRepository;
        this.locationRepository = locationRepository;
        this.mSGraphApiClient = mSGraphApiClient;
        this.sharedPermissionService = sharedPermissionService;
        this.historyService = historyService;
        this.databaseHelper = databaseHelper;
    }
    getListOfUserPermissions(currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username } = currentContext.user;
            const [permissions, localPermissions] = yield Promise.all([
                this.adminApiClient.getListOfUserPermissions(username.toLowerCase()),
                this.userPermissionRepository.getUserPermissionByLoginId(username.toLowerCase()),
            ]);
            const manageLocation = new Set();
            const manageCapability = new Set();
            for (const { manageLocation: ml, manageCapability: mc, locationId } of localPermissions || []) {
                if (ml)
                    manageLocation.add(locationId);
                if (mc)
                    manageCapability.add(locationId);
            }
            if (manageLocation.size) {
                permissions.push({
                    permissionName: enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION,
                    applicationId: null,
                    locations: [...manageLocation],
                });
            }
            if (manageCapability.size) {
                permissions.push({
                    permissionName: enums_1.PERMISSIONS.LOCAL_MANAGE_CAPABILITY,
                    applicationId: null,
                    locations: [...manageCapability],
                });
            }
            return (0, helpers_1.multiObjectToInstance)(dtos_1.PermissionResponseDto, permissions);
        });
    }
    getAllAccessControlConfigs() {
        return __awaiter(this, void 0, void 0, function* () {
            const configs = yield this.accessControlConfigRepository.getAllAccessControlConfigs();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AllAccessControlConfigResponseDto, configs);
        });
    }
    getLocalLocationBasedPermissions(currentContext, page = 1, limit = 10, orderBy, orderDirection, loginid, configGroupId, locationId) {
        return __awaiter(this, void 0, void 0, function* () {
            const validPage = Number.isNaN(Number(page)) ? 1 : Number(page);
            const validLimit = Number.isNaN(Number(limit)) ? 10 : Number(limit);
            const validConfigGroupId = configGroupId !== undefined && configGroupId !== null
                ? Number.isNaN(Number(configGroupId))
                    ? null
                    : Number(configGroupId)
                : null;
            const validLocationId = locationId !== undefined && locationId !== null
                ? Number.isNaN(Number(locationId))
                    ? null
                    : Number(locationId)
                : null;
            if (locationId && !validLocationId) {
                throw new common_1.HttpException('Invalid Location Selected', common_1.HttpStatus.BAD_REQUEST);
            }
            if (configGroupId && !validConfigGroupId) {
                throw new common_1.HttpException('Invalid Config Group Selected', common_1.HttpStatus.BAD_REQUEST);
            }
            let accessibleLocationIds = [];
            const entityAccess = yield this.sharedPermissionService.getAllLocationIdForGivenPermission(currentContext.user.unique_name, enums_1.PERMISSIONS.GLOBAL_MANAGE);
            if (!locationId && !(entityAccess === null || entityAccess === void 0 ? void 0 : entityAccess.length)) {
                throw new common_1.HttpException("You don't have permission to view any location access.", common_1.HttpStatus.FORBIDDEN);
            }
            let locations = [];
            if (entityAccess.length) {
                locations = yield this.locationRepository.getLocationsIdsByEntityIds(entityAccess);
            }
            accessibleLocationIds.push(...locations.map(l => l.id));
            if (locationId) {
                if (accessibleLocationIds.includes(locationId)) {
                    accessibleLocationIds = [locationId];
                }
                else {
                    const hasPermission = yield this.sharedPermissionService.checkAnyPermission([enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, null, locationId);
                    if (!hasPermission) {
                        throw new common_1.HttpException("You don't have permission to view this location access.", common_1.HttpStatus.FORBIDDEN);
                    }
                    accessibleLocationIds = [locationId];
                }
            }
            const { rows, count } = yield this.userPermissionRepository.getUserPermissions(validPage, validLimit, orderBy, orderDirection, loginid === null || loginid === void 0 ? void 0 : loginid.toLowerCase(), validConfigGroupId, accessibleLocationIds);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.UserPermissionResponseDto, rows);
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    createUserPermission(data, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let { locationId, loginId, configGroupId } = data;
            loginId = loginId.toLowerCase();
            const configGroup = yield this.accessControlConfigRepository.getAccessControlConfigById(configGroupId);
            if (!configGroup) {
                throw new common_1.HttpException('Access control configuration not found', common_1.HttpStatus.BAD_REQUEST);
            }
            const userDetail = yield this.mSGraphApiClient.getUserDetailsInMsResponse(loginId);
            if (!((_a = userDetail === null || userDetail === void 0 ? void 0 : userDetail.value) === null || _a === void 0 ? void 0 : _a.length)) {
                throw new common_1.HttpException('Invalid User', common_1.HttpStatus.BAD_REQUEST);
            }
            if (locationId) {
                const locationDetail = yield this.locationRepository.getLocationDetailById(locationId);
                if (!locationDetail) {
                    throw new common_1.HttpException('Invalid Location Selected', common_1.HttpStatus.BAD_REQUEST);
                }
                const hasPermission = yield this.sharedPermissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, locationDetail.entityId, locationId);
                if (!hasPermission) {
                    throw new common_1.HttpException("You don't have permission to add user in this location.", common_1.HttpStatus.FORBIDDEN);
                }
            }
            const existingUser = yield this.userPermissionRepository.ifUserPermissionExist(configGroupId, loginId, locationId);
            if (existingUser) {
                throw new common_1.HttpException('User permission already exist', common_1.HttpStatus.CONFLICT);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const permission = yield this.userPermissionRepository.createUserPermission({
                    locationId,
                    loginId: loginId,
                    configGroupId: configGroupId,
                    manageLocation: configGroup.manageLocation,
                    manageCapability: configGroup.manageCapability,
                }, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: locationId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.ADDED_PERMISSION,
                    action_date: new Date(),
                    comments: `${loginId} added as ${configGroup.groupName}.`,
                    additional_info: {
                        permissionData: data,
                    },
                });
                return { message: 'User permission created successfully', data: permission };
            }));
        });
    }
    deleteUserPermission(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const permission = yield this.userPermissionRepository.getUserPermissionById(id);
            if (!permission) {
                throw new common_1.HttpException('User permission not found', common_1.HttpStatus.NOT_FOUND);
            }
            const locationDetail = yield this.locationRepository.getLocationDetailById(permission.locationId);
            if (locationDetail) {
                const hasPermission = yield this.sharedPermissionService.checkAnyPermission([enums_1.PERMISSIONS.GLOBAL_MANAGE, enums_1.PERMISSIONS.LOCAL_MANAGE_LOCATION], currentContext.user, locationDetail.entityId, locationDetail.id);
                if (!hasPermission) {
                    throw new common_1.HttpException("You don't have permission to delete user for this location.", common_1.HttpStatus.FORBIDDEN);
                }
            }
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.userPermissionRepository.deleteUserPermission(id, currentContext);
                yield this.historyService.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: permission.locationId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.LOCATION,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.REMOVED_PERMISSION,
                    action_date: new Date(),
                    comments: `${permission.loginId} removed from ${permission.accessControlConfig.groupName}.`,
                    additional_info: {
                        permissionData: permission,
                    },
                });
                return { message: 'User permission deleted successfully' };
            }));
        });
    }
    getPeopleSearch(loginId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const rawResult = (yield this.userPermissionRepository.getPeopleSearch(loginId));
                const data = rawResult.map(item => item.people_search_v2);
                return {
                    message: 'Fetch user list successfully',
                    data,
                };
            }));
        });
    }
};
PermissionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        repositories_2.AccessControlConfigRepository,
        repositories_2.UserPermissionRepository,
        repositories_1.LocationRepository,
        clients_1.MSGraphApiClient,
        services_1.SharedPermissionService,
        clients_1.HistoryApiClient,
        helpers_1.DatabaseHelper])
], PermissionService);
exports.PermissionService = PermissionService;
//# sourceMappingURL=permission.service.js.map