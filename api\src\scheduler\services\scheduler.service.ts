import { Injectable } from '@nestjs/common';
import { LocationWiseCapabilityRepository } from 'src/capability/repositories';
import { ConfigService } from 'src/config/config.service';
import { LoggerService } from 'src/core/services';
import { MSGraphApiClient } from 'src/shared/clients';
import {
	CAPABILITY_STATUS_ENUM,
	EMAIL_TEMPLATES,
	NOTIFICATION_ENTITY_TYPE,
	SCHEDULER_TYPE,
	UI_ROUTES,
} from 'src/shared/enums';
import { getTableCSS, jsonToHtmlTable, ReplaceUrlVariable } from 'src/shared/helpers';
import { SharedNotificationService } from 'src/shared/services';
import {
	CapabilitiesDetail,
	CAPABILITY_STATUS_TYPE_ENUM,
	LocationDetail,
	thresholdDayLimit,
} from '../types/schedular.types';

@Injectable()
export class SchedulerService {
	constructor(
		private readonly locationWiseCapabilityRepository: LocationWiseCapabilityRepository,
		private readonly configService: ConfigService,
		private readonly loggerService: LoggerService,
		private readonly mSGraphApiClient: MSGraphApiClient,
		private readonly sharedNotificationService: SharedNotificationService,
	) { }

	private readonly THRESHOLD_DAY_LIMIT = thresholdDayLimit;

	public async runScheduler(type: string) {
		this.loggerService.log(`Started the ${type} scheduler.`);

		switch (type) {
			case SCHEDULER_TYPE.ENTRIES_NOTIFICATION:
				await this.runEntriesNotification();
				break;
			default:
				throw Error(
					'Invalid scheduler type. Type should be (ENTRIES_NOTIFICATION).',
				);
		}

		this.loggerService.log(`Ended the ${type} scheduler.`);
	}

	public async runEntriesNotification() {
		const now = new Date();
		const nearExpiryDate = new Date(now.getTime() + this.THRESHOLD_DAY_LIMIT * 24 * 60 * 60 * 1000);

		const entries = await this.locationWiseCapabilityRepository.getEntriesForScheduler(
			now,
			nearExpiryDate,
		);

		const groupedMap = new Map<
			number,
			{
				recipients: Set<string>;
				location: LocationDetail;
				capabilityList: CapabilitiesDetail[];
			}
		>();

		for (const entry of entries) {
			const locId = entry.locationId;
			const location = entry.locationDetail;

			if (!groupedMap.has(locId)) {
				groupedMap.set(locId, {
					recipients: new Set(),
					location,
					capabilityList: [],
				});
			}

			const group = groupedMap.get(locId)!;

			for (const perm of entry.userPermissions ?? []) {
				group.recipients.add(perm.loginId);
			}

			const date = new Date(entry.statusDate);
			const daysLeft = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
			const daysAgo = Math.ceil((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

			let type: CAPABILITY_STATUS_TYPE_ENUM | null = null;

			if (
				entry.status === CAPABILITY_STATUS_ENUM.EXISTING &&
				date >= now &&
				date <= nearExpiryDate
			) {
				type = CAPABILITY_STATUS_TYPE_ENUM.NEAR_EXPIRY;
			} else if (
				entry.status === CAPABILITY_STATUS_ENUM.PLANNED &&
				date >= now &&
				date <= nearExpiryDate
			) {
				type = CAPABILITY_STATUS_TYPE_ENUM.NEAR_PLANNED;
			} else if (entry.status === CAPABILITY_STATUS_ENUM.EXPIRED) {
				type = CAPABILITY_STATUS_TYPE_ENUM.EXPIRED;
			} else if (entry.status === CAPABILITY_STATUS_ENUM.PLANNED && date < now) {
				type = CAPABILITY_STATUS_TYPE_ENUM.PLANNED_PASSED_AGO;
			}

			group.capabilityList.push({
				id: entry.id,
				locationId: entry.locationId,
				capabilityId: entry.capabilityId,
				status: entry.status,
				internalStatus: entry.internalStatus,
				statusDate: entry.statusDate,
				capability: entry.capabilityDetail?.capability,
				subCategory: entry.capabilityDetail?.subCategory,
				product: entry.capabilityDetail?.product,
				capabilityType: entry.capabilityDetail?.capabilityType,
				level: entry.capabilityDetail?.level,
				category: entry.capabilityDetail?.category?.title,
				type,
				...([
					CAPABILITY_STATUS_TYPE_ENUM.NEAR_EXPIRY,
					CAPABILITY_STATUS_TYPE_ENUM.NEAR_PLANNED,
				].includes(type) && { daysLeft: daysLeft > 0 ? daysLeft : 0 }),
				...([
					CAPABILITY_STATUS_TYPE_ENUM.EXPIRED,
					CAPABILITY_STATUS_TYPE_ENUM.PLANNED_PASSED_AGO,
				].includes(type) && { daysAgo: daysAgo > 0 ? daysAgo : 0 }),
			});
		}

		const finalList = Array.from(groupedMap.values()).map(group => {
			const loc = group.location;

			const expiredList = group.capabilityList.filter(
				cap => cap.type === CAPABILITY_STATUS_TYPE_ENUM.EXPIRED,
			);

			const plannedDatePassedList = group.capabilityList.filter(
				cap => cap.type === CAPABILITY_STATUS_TYPE_ENUM.PLANNED_PASSED_AGO,
			);

			return {
				id: loc.id,
				locationName: loc.locationName,
				entityId: loc.entityId,
				entityTitle: loc.entityTitle,
				entityCode: loc.entityCode,
				status: loc.status,
				statusDate: loc.statusDate,
				coreSolution: loc.coreSolution?.title ?? null,
				locationType: loc.locationType?.title ?? null,
				countryDetail: loc.countryDetail ?? null,
				recipients: Array.from(group.recipients),
				expiredList,
				plannedDatePassedList,
			};
		});

		function formatCategoryInfo(item: CapabilitiesDetail): string {
			const parts = [item.category, item.subCategory, item.product].filter(Boolean);
			if (!parts.length) return '';
			return `<div style="font-size: 12px; color: #555;">${parts.join(' - ')}</div>`;
		}

		console.dir(finalList, { depth: null });

		const config = this.configService.getAppConfig();

		for (const locationGroup of finalList) {
			const {
				locationName,
				id: locationId,
				recipients,
				expiredList,
				plannedDatePassedList,
			} = locationGroup;

			if (!expiredList.length && !plannedDatePassedList.length) continue;

			// Generate HTML tables
			const expiredListHtml = expiredList.length
				? `<h3>Expired Entries</h3>` +
				jsonToHtmlTable({
					data: expiredList.map(item => ({
						Capability: `
					<div>
						<a href="${config.uiClient.baseUrl}/capabilities/${item.capabilityId}">
							${item.capability}
						</a>
						${formatCategoryInfo(item)}
					</div>`,
						Level: item.level ?? '',
						Type: item.capabilityType ?? '',
						'Expiry Date': item.statusDate,
					})),
					css: getTableCSS(),
				})
				: '';

			const plannedDatePassedListHtml = plannedDatePassedList.length
				? `<h3>Planned Date Passed Entries</h3>` +
				jsonToHtmlTable({
					data: plannedDatePassedList.map(item => ({
						Capability: `
					<div>
						<a href="${ReplaceUrlVariable(config.uiClient.baseUrl + UI_ROUTES.CAPABILITY_DETAIL, {
							capabilityId: item.capabilityId,
						})}">
							${item.capability}
						</a>
						${formatCategoryInfo(item)}
					</div>`,
						Level: item.level ?? '',
						Type: item.capabilityType ?? '',
						'Planned Date': item.statusDate,
					})),
					css: getTableCSS(),
				})
				: '';

			// For each recipient in the location
			for (const recipientLoginId of recipients) {
				const usersDetails = await this.mSGraphApiClient.getUsersDetails([recipientLoginId]);

				const userDetail = usersDetails.find(
					user =>
						user.userPrincipalName?.toLowerCase() === recipientLoginId.toLowerCase() ||
						user.mail?.toLowerCase() === recipientLoginId.toLowerCase(),
				);

				const placeholderValues = {
					locationName: locationName,
					userName: (userDetail && userDetail?.displayName) || recipientLoginId,
					locationNameWithLink: `<a href="${ReplaceUrlVariable(
						config.uiClient.baseUrl + UI_ROUTES.AVAILABLE_CAPABILITY_LIST,
						{ locationId },
					)}">${locationName}</a>`,
					expiredList: expiredListHtml,
					plannedDatePassedList: plannedDatePassedListHtml,
				};

				try {
					if (userDetail && userDetail?.mail) {
						await this.sharedNotificationService.sendNotification(
							-1,
							NOTIFICATION_ENTITY_TYPE.SCHEDULER_ENTRIES_NOTIFICATION,
							{ to: [userDetail && userDetail?.mail] },
							EMAIL_TEMPLATES.SCHEDULER_ENTRIES_NOTIFICATION,
							placeholderValues,
						);
					}
				} catch (error) {
					this.loggerService.error(
						`Failed to send notification to ${userDetail.mail}: ${error?.message}`,
						error,
					);
				}
			}
		}
	}
}
