"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toUpperUnderscore = void 0;
function toUpperUnderscore(str) {
    const cleaned = str
        .replace(/[^a-zA-Z0-9 ()]/g, '')
        .replace(/[()]/g, '')
        .replace(/\s+/g, ' ');
    return cleaned.trim().toUpperCase().replace(/ /g, '_');
}
exports.toUpperUnderscore = toUpperUnderscore;
//# sourceMappingURL=to-upper-case.helper.js.map