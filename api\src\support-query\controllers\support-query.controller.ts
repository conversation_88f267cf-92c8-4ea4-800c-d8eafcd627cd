import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/core/decorators';
import { PermissionsGuard } from 'src/core/guards';
import { MessageResponseDto } from 'src/shared/dtos';
import { PERMISSIONS } from 'src/shared/enums';
import { RequestContext } from 'src/shared/types';
import { SupportQueryRequestDto, SupportQueryResponseDto } from '../dtos';
import { SupportQueryService } from '../services';

@ApiTags('Support APIs')
@ApiBearerAuth()
@Controller('support-query')
export class SupportQueryController {
	constructor(public readonly supportQueryService: SupportQueryService) {}

	@Permissions(PERMISSIONS.ANY)
	@UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
	@ApiResponse({
		status: 201,
		description: 'Create a new user support query',
		type: SupportQueryResponseDto,
	})
	@Post('/help')
	public createUserSupportQuery(
		@Req() request: RequestContext,
		@Body() data: SupportQueryRequestDto,
	): Promise<MessageResponseDto> {
		return this.supportQueryService.createUserSupportQuery(data, request.currentContext);
	}
}
