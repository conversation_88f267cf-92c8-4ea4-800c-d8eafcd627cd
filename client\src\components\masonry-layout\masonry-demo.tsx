import React, { useState } from 'react';
import { Box, Card, Typo<PERSON>, <PERSON><PERSON>, <PERSON>ack, Switch, FormControlLabel } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { MasonryLayout } from './masonry-layout';

// Demo component to showcase masonry layout
export const MasonryDemo: React.FC = () => {
  const theme = useTheme();
  const [useAdvancedLayout, setUseAdvancedLayout] = useState(true);

  // Sample data with varying content lengths
  const sampleCards = [
    {
      title: "Short Card",
      content: "This is a short card with minimal content.",
      color: theme.palette.primary.main,
    },
    {
      title: "Medium Length Card",
      content: "This card has a moderate amount of content. It demonstrates how cards with different heights will be positioned in the masonry layout. The layout should automatically adjust to fill available space efficiently.",
      color: theme.palette.secondary.main,
    },
    {
      title: "Very Long Card",
      content: "This is a very long card with extensive content to demonstrate the masonry layout's ability to handle varying heights. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
      color: theme.palette.success.main,
    },
    {
      title: "Another Short Card",
      content: "Another short card to show layout distribution.",
      color: theme.palette.warning.main,
    },
    {
      title: "Medium Card Again",
      content: "This is another medium-sized card that will help demonstrate how the masonry layout distributes cards across columns based on available space and content height.",
      color: theme.palette.error.main,
    },
    {
      title: "Final Card",
      content: "This is the final card in our demo. It shows how the layout handles the last items.",
      color: theme.palette.info.main,
    },
  ];

  // CSS-based masonry layout
  const CSSMasonryLayout = ({ children }: { children: React.ReactNode[] }) => {
    return (
      <Box
        sx={{
          columnCount: { xs: 1, md: 2 },
          columnGap: 2,
          columnFill: 'balance',
          '& > *': {
            breakInside: 'avoid',
            marginBottom: 2,
            display: 'inline-block',
            width: '100%',
          },
        }}
      >
        {children}
      </Box>
    );
  };

  const cardElements = sampleCards.map((card, index) => (
    <Card
      key={index}
      sx={{
        p: 3,
        borderRadius: 2,
        border: `2px solid ${card.color}20`,
        background: `linear-gradient(135deg, ${card.color}10, ${theme.palette.background.paper})`,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: `0 8px 25px ${card.color}30`,
          transform: 'translateY(-4px)',
          borderColor: `${card.color}40`,
        },
      }}
    >
      <Typography
        variant="h6"
        sx={{
          color: card.color,
          fontWeight: 600,
          mb: 2,
        }}
      >
        {card.title}
      </Typography>
      <Typography
        variant="body2"
        sx={{
          color: theme.palette.text.secondary,
          lineHeight: 1.6,
        }}
      >
        {card.content}
      </Typography>
    </Card>
  ));

  return (
    <Box sx={{ p: 3 }}>
      <Stack spacing={3}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
          Masonry Layout Demo
        </Typography>
        
        <Typography variant="body1" sx={{ color: theme.palette.text.secondary }}>
          This demo shows how cards with different heights automatically adjust their positions 
          to create an optimal layout. Toggle between the advanced JavaScript-based layout and 
          the CSS-based layout to see the differences.
        </Typography>

        <FormControlLabel
          control={
            <Switch
              checked={useAdvancedLayout}
              onChange={(e) => setUseAdvancedLayout(e.target.checked)}
              color="primary"
            />
          }
          label={`${useAdvancedLayout ? 'Advanced JavaScript' : 'CSS Column'} Layout`}
        />

        <Box sx={{ mt: 3 }}>
          {useAdvancedLayout ? (
            <MasonryLayout columns={2} gap={16} minColumnWidth={300}>
              {cardElements}
            </MasonryLayout>
          ) : (
            <CSSMasonryLayout>
              {cardElements}
            </CSSMasonryLayout>
          )}
        </Box>
      </Stack>
    </Box>
  );
};

export default MasonryDemo;
