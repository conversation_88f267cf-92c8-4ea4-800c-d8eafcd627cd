{"version": 3, "sources": ["../../ol/console.js", "../../ol/extent/Relationship.js", "../../ol/extent.js", "../../ol/math.js", "../../ol/string.js", "../../ol/coordinate.js", "../../ol/proj/Units.js", "../../ol/proj/Projection.js", "../../ol/proj/epsg3857.js", "../../ol/proj/epsg4326.js", "../../ol/proj/projections.js", "../../ol/proj/transforms.js", "../../ol/proj/utm.js", "../../ol/sphere.js", "../../ol/proj.js"], "sourcesContent": ["/**\n * @module ol/console\n */\n\n/**\n * @typedef {'info'|'warn'|'error'|'none'} Level\n */\n\n/**\n * @type {Object<Level, number>}\n */\nconst levels = {\n  info: 1,\n  warn: 2,\n  error: 3,\n  none: 4,\n};\n\n/**\n * @type {number}\n */\nlet level = levels.info;\n\n/**\n * Set the logging level.  By default, the level is set to 'info' and all\n * messages will be logged.  Set to 'warn' to only display warnings and errors.\n * Set to 'error' to only display errors.  Set to 'none' to silence all messages.\n *\n * @param {Level} l The new level.\n */\nexport function setLevel(l) {\n  level = levels[l];\n}\n\n/**\n * @param  {...any} args Arguments to log\n */\nexport function log(...args) {\n  if (level > levels.info) {\n    return;\n  }\n  console.log(...args); // eslint-disable-line no-console\n}\n\n/**\n * @param  {...any} args Arguments to log\n */\nexport function warn(...args) {\n  if (level > levels.warn) {\n    return;\n  }\n  console.warn(...args); // eslint-disable-line no-console\n}\n\n/**\n * @param  {...any} args Arguments to log\n */\nexport function error(...args) {\n  if (level > levels.error) {\n    return;\n  }\n  console.error(...args); // eslint-disable-line no-console\n}\n", "/**\n * @module ol/extent/Relationship\n */\n\n/**\n * Relationship to an extent.\n * @enum {number}\n */\nexport default {\n  UNKNOWN: 0,\n  INTERSECTING: 1,\n  ABOVE: 2,\n  RIGHT: 4,\n  BELOW: 8,\n  LEFT: 16,\n};\n", "/**\n * @module ol/extent\n */\nimport Relationship from './extent/Relationship.js';\n\n/**\n * An array of numbers representing an extent: `[minx, miny, maxx, maxy]`.\n * @typedef {Array<number>} Extent\n * @api\n */\n\n/**\n * Extent corner.\n * @typedef {'bottom-left' | 'bottom-right' | 'top-left' | 'top-right'} Corner\n */\n\n/**\n * Build an extent that includes all given coordinates.\n *\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates Coordinates.\n * @return {Extent} Bounding extent.\n * @api\n */\nexport function boundingExtent(coordinates) {\n  const extent = createEmpty();\n  for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n    extendCoordinate(extent, coordinates[i]);\n  }\n  return extent;\n}\n\n/**\n * @param {Array<number>} xs Xs.\n * @param {Array<number>} ys Ys.\n * @param {Extent} [dest] Destination extent.\n * @private\n * @return {Extent} Extent.\n */\nfunction _boundingExtentXYs(xs, ys, dest) {\n  const minX = Math.min.apply(null, xs);\n  const minY = Math.min.apply(null, ys);\n  const maxX = Math.max.apply(null, xs);\n  const maxY = Math.max.apply(null, ys);\n  return createOrUpdate(minX, minY, maxX, maxY, dest);\n}\n\n/**\n * Return extent increased by the provided value.\n * @param {Extent} extent Extent.\n * @param {number} value The amount by which the extent should be buffered.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n * @api\n */\nexport function buffer(extent, value, dest) {\n  if (dest) {\n    dest[0] = extent[0] - value;\n    dest[1] = extent[1] - value;\n    dest[2] = extent[2] + value;\n    dest[3] = extent[3] + value;\n    return dest;\n  }\n  return [\n    extent[0] - value,\n    extent[1] - value,\n    extent[2] + value,\n    extent[3] + value,\n  ];\n}\n\n/**\n * Creates a clone of an extent.\n *\n * @param {Extent} extent Extent to clone.\n * @param {Extent} [dest] Extent.\n * @return {Extent} The clone.\n */\nexport function clone(extent, dest) {\n  if (dest) {\n    dest[0] = extent[0];\n    dest[1] = extent[1];\n    dest[2] = extent[2];\n    dest[3] = extent[3];\n    return dest;\n  }\n  return extent.slice();\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {number} x X.\n * @param {number} y Y.\n * @return {number} Closest squared distance.\n */\nexport function closestSquaredDistanceXY(extent, x, y) {\n  let dx, dy;\n  if (x < extent[0]) {\n    dx = extent[0] - x;\n  } else if (extent[2] < x) {\n    dx = x - extent[2];\n  } else {\n    dx = 0;\n  }\n  if (y < extent[1]) {\n    dy = extent[1] - y;\n  } else if (extent[3] < y) {\n    dy = y - extent[3];\n  } else {\n    dy = 0;\n  }\n  return dx * dx + dy * dy;\n}\n\n/**\n * Check if the passed coordinate is contained or on the edge of the extent.\n *\n * @param {Extent} extent Extent.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n * @return {boolean} The coordinate is contained in the extent.\n * @api\n */\nexport function containsCoordinate(extent, coordinate) {\n  return containsXY(extent, coordinate[0], coordinate[1]);\n}\n\n/**\n * Check if one extent contains another.\n *\n * An extent is deemed contained if it lies completely within the other extent,\n * including if they share one or more edges.\n *\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {boolean} The second extent is contained by or on the edge of the\n *     first.\n * @api\n */\nexport function containsExtent(extent1, extent2) {\n  return (\n    extent1[0] <= extent2[0] &&\n    extent2[2] <= extent1[2] &&\n    extent1[1] <= extent2[1] &&\n    extent2[3] <= extent1[3]\n  );\n}\n\n/**\n * Check if the passed coordinate is contained or on the edge of the extent.\n *\n * @param {Extent} extent Extent.\n * @param {number} x X coordinate.\n * @param {number} y Y coordinate.\n * @return {boolean} The x, y values are contained in the extent.\n * @api\n */\nexport function containsXY(extent, x, y) {\n  return extent[0] <= x && x <= extent[2] && extent[1] <= y && y <= extent[3];\n}\n\n/**\n * Get the relationship between a coordinate and extent.\n * @param {Extent} extent The extent.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate The coordinate.\n * @return {import(\"./extent/Relationship.js\").default} The relationship (bitwise compare with\n *     import(\"./extent/Relationship.js\").Relationship).\n */\nexport function coordinateRelationship(extent, coordinate) {\n  const minX = extent[0];\n  const minY = extent[1];\n  const maxX = extent[2];\n  const maxY = extent[3];\n  const x = coordinate[0];\n  const y = coordinate[1];\n  let relationship = Relationship.UNKNOWN;\n  if (x < minX) {\n    relationship = relationship | Relationship.LEFT;\n  } else if (x > maxX) {\n    relationship = relationship | Relationship.RIGHT;\n  }\n  if (y < minY) {\n    relationship = relationship | Relationship.BELOW;\n  } else if (y > maxY) {\n    relationship = relationship | Relationship.ABOVE;\n  }\n  if (relationship === Relationship.UNKNOWN) {\n    relationship = Relationship.INTERSECTING;\n  }\n  return relationship;\n}\n\n/**\n * Create an empty extent.\n * @return {Extent} Empty extent.\n * @api\n */\nexport function createEmpty() {\n  return [Infinity, Infinity, -Infinity, -Infinity];\n}\n\n/**\n * Create a new extent or update the provided extent.\n * @param {number} minX Minimum X.\n * @param {number} minY Minimum Y.\n * @param {number} maxX Maximum X.\n * @param {number} maxY Maximum Y.\n * @param {Extent} [dest] Destination extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdate(minX, minY, maxX, maxY, dest) {\n  if (dest) {\n    dest[0] = minX;\n    dest[1] = minY;\n    dest[2] = maxX;\n    dest[3] = maxY;\n    return dest;\n  }\n  return [minX, minY, maxX, maxY];\n}\n\n/**\n * Create a new empty extent or make the provided one empty.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateEmpty(dest) {\n  return createOrUpdate(Infinity, Infinity, -Infinity, -Infinity, dest);\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromCoordinate(coordinate, dest) {\n  const x = coordinate[0];\n  const y = coordinate[1];\n  return createOrUpdate(x, y, x, y, dest);\n}\n\n/**\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates Coordinates.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromCoordinates(coordinates, dest) {\n  const extent = createOrUpdateEmpty(dest);\n  return extendCoordinates(extent, coordinates);\n}\n\n/**\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromFlatCoordinates(\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n  dest,\n) {\n  const extent = createOrUpdateEmpty(dest);\n  return extendFlatCoordinates(extent, flatCoordinates, offset, end, stride);\n}\n\n/**\n * @param {Array<Array<import(\"./coordinate.js\").Coordinate>>} rings Rings.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function createOrUpdateFromRings(rings, dest) {\n  const extent = createOrUpdateEmpty(dest);\n  return extendRings(extent, rings);\n}\n\n/**\n * Determine if two extents are equivalent.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {boolean} The two extents are equivalent.\n * @api\n */\nexport function equals(extent1, extent2) {\n  return (\n    extent1[0] == extent2[0] &&\n    extent1[2] == extent2[2] &&\n    extent1[1] == extent2[1] &&\n    extent1[3] == extent2[3]\n  );\n}\n\n/**\n * Determine if two extents are approximately equivalent.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @param {number} tolerance Tolerance in extent coordinate units.\n * @return {boolean} The two extents differ by less than the tolerance.\n */\nexport function approximatelyEquals(extent1, extent2, tolerance) {\n  return (\n    Math.abs(extent1[0] - extent2[0]) < tolerance &&\n    Math.abs(extent1[2] - extent2[2]) < tolerance &&\n    Math.abs(extent1[1] - extent2[1]) < tolerance &&\n    Math.abs(extent1[3] - extent2[3]) < tolerance\n  );\n}\n\n/**\n * Modify an extent to include another extent.\n * @param {Extent} extent1 The extent to be modified.\n * @param {Extent} extent2 The extent that will be included in the first.\n * @return {Extent} A reference to the first (extended) extent.\n * @api\n */\nexport function extend(extent1, extent2) {\n  if (extent2[0] < extent1[0]) {\n    extent1[0] = extent2[0];\n  }\n  if (extent2[2] > extent1[2]) {\n    extent1[2] = extent2[2];\n  }\n  if (extent2[1] < extent1[1]) {\n    extent1[1] = extent2[1];\n  }\n  if (extent2[3] > extent1[3]) {\n    extent1[3] = extent2[3];\n  }\n  return extent1;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n */\nexport function extendCoordinate(extent, coordinate) {\n  if (coordinate[0] < extent[0]) {\n    extent[0] = coordinate[0];\n  }\n  if (coordinate[0] > extent[2]) {\n    extent[2] = coordinate[0];\n  }\n  if (coordinate[1] < extent[1]) {\n    extent[1] = coordinate[1];\n  }\n  if (coordinate[1] > extent[3]) {\n    extent[3] = coordinate[1];\n  }\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates Coordinates.\n * @return {Extent} Extent.\n */\nexport function extendCoordinates(extent, coordinates) {\n  for (let i = 0, ii = coordinates.length; i < ii; ++i) {\n    extendCoordinate(extent, coordinates[i]);\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Array<number>} flatCoordinates Flat coordinates.\n * @param {number} offset Offset.\n * @param {number} end End.\n * @param {number} stride Stride.\n * @return {Extent} Extent.\n */\nexport function extendFlatCoordinates(\n  extent,\n  flatCoordinates,\n  offset,\n  end,\n  stride,\n) {\n  for (; offset < end; offset += stride) {\n    extendXY(extent, flatCoordinates[offset], flatCoordinates[offset + 1]);\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Array<Array<import(\"./coordinate.js\").Coordinate>>} rings Rings.\n * @return {Extent} Extent.\n */\nexport function extendRings(extent, rings) {\n  for (let i = 0, ii = rings.length; i < ii; ++i) {\n    extendCoordinates(extent, rings[i]);\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {number} x X.\n * @param {number} y Y.\n */\nexport function extendXY(extent, x, y) {\n  extent[0] = Math.min(extent[0], x);\n  extent[1] = Math.min(extent[1], y);\n  extent[2] = Math.max(extent[2], x);\n  extent[3] = Math.max(extent[3], y);\n}\n\n/**\n * This function calls `callback` for each corner of the extent. If the\n * callback returns a truthy value the function returns that value\n * immediately. Otherwise the function returns `false`.\n * @param {Extent} extent Extent.\n * @param {function(import(\"./coordinate.js\").Coordinate): S} callback Callback.\n * @return {S|boolean} Value.\n * @template S\n */\nexport function forEachCorner(extent, callback) {\n  let val;\n  val = callback(getBottomLeft(extent));\n  if (val) {\n    return val;\n  }\n  val = callback(getBottomRight(extent));\n  if (val) {\n    return val;\n  }\n  val = callback(getTopRight(extent));\n  if (val) {\n    return val;\n  }\n  val = callback(getTopLeft(extent));\n  if (val) {\n    return val;\n  }\n  return false;\n}\n\n/**\n * Get the size of an extent.\n * @param {Extent} extent Extent.\n * @return {number} Area.\n * @api\n */\nexport function getArea(extent) {\n  let area = 0;\n  if (!isEmpty(extent)) {\n    area = getWidth(extent) * getHeight(extent);\n  }\n  return area;\n}\n\n/**\n * Get the bottom left coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Bottom left coordinate.\n * @api\n */\nexport function getBottomLeft(extent) {\n  return [extent[0], extent[1]];\n}\n\n/**\n * Get the bottom right coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Bottom right coordinate.\n * @api\n */\nexport function getBottomRight(extent) {\n  return [extent[2], extent[1]];\n}\n\n/**\n * Get the center coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Center.\n * @api\n */\nexport function getCenter(extent) {\n  return [(extent[0] + extent[2]) / 2, (extent[1] + extent[3]) / 2];\n}\n\n/**\n * Get a corner coordinate of an extent.\n * @param {Extent} extent Extent.\n * @param {Corner} corner Corner.\n * @return {import(\"./coordinate.js\").Coordinate} Corner coordinate.\n */\nexport function getCorner(extent, corner) {\n  let coordinate;\n  if (corner === 'bottom-left') {\n    coordinate = getBottomLeft(extent);\n  } else if (corner === 'bottom-right') {\n    coordinate = getBottomRight(extent);\n  } else if (corner === 'top-left') {\n    coordinate = getTopLeft(extent);\n  } else if (corner === 'top-right') {\n    coordinate = getTopRight(extent);\n  } else {\n    throw new Error('Invalid corner');\n  }\n  return coordinate;\n}\n\n/**\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {number} Enlarged area.\n */\nexport function getEnlargedArea(extent1, extent2) {\n  const minX = Math.min(extent1[0], extent2[0]);\n  const minY = Math.min(extent1[1], extent2[1]);\n  const maxX = Math.max(extent1[2], extent2[2]);\n  const maxY = Math.max(extent1[3], extent2[3]);\n  return (maxX - minX) * (maxY - minY);\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} center Center.\n * @param {number} resolution Resolution.\n * @param {number} rotation Rotation.\n * @param {import(\"./size.js\").Size} size Size.\n * @param {Extent} [dest] Destination extent.\n * @return {Extent} Extent.\n */\nexport function getForViewAndSize(center, resolution, rotation, size, dest) {\n  const [x0, y0, x1, y1, x2, y2, x3, y3] = getRotatedViewport(\n    center,\n    resolution,\n    rotation,\n    size,\n  );\n  return createOrUpdate(\n    Math.min(x0, x1, x2, x3),\n    Math.min(y0, y1, y2, y3),\n    Math.max(x0, x1, x2, x3),\n    Math.max(y0, y1, y2, y3),\n    dest,\n  );\n}\n\n/**\n * @param {import(\"./coordinate.js\").Coordinate} center Center.\n * @param {number} resolution Resolution.\n * @param {number} rotation Rotation.\n * @param {import(\"./size.js\").Size} size Size.\n * @return {Array<number>} Linear ring representing the viewport.\n */\nexport function getRotatedViewport(center, resolution, rotation, size) {\n  const dx = (resolution * size[0]) / 2;\n  const dy = (resolution * size[1]) / 2;\n  const cosRotation = Math.cos(rotation);\n  const sinRotation = Math.sin(rotation);\n  const xCos = dx * cosRotation;\n  const xSin = dx * sinRotation;\n  const yCos = dy * cosRotation;\n  const ySin = dy * sinRotation;\n  const x = center[0];\n  const y = center[1];\n  return [\n    x - xCos + ySin,\n    y - xSin - yCos,\n    x - xCos - ySin,\n    y - xSin + yCos,\n    x + xCos - ySin,\n    y + xSin + yCos,\n    x + xCos + ySin,\n    y + xSin - yCos,\n    x - xCos + ySin,\n    y - xSin - yCos,\n  ];\n}\n\n/**\n * Get the height of an extent.\n * @param {Extent} extent Extent.\n * @return {number} Height.\n * @api\n */\nexport function getHeight(extent) {\n  return extent[3] - extent[1];\n}\n\n/**\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @return {number} Intersection area.\n */\nexport function getIntersectionArea(extent1, extent2) {\n  const intersection = getIntersection(extent1, extent2);\n  return getArea(intersection);\n}\n\n/**\n * Get the intersection of two extents.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent 2.\n * @param {Extent} [dest] Optional extent to populate with intersection.\n * @return {Extent} Intersecting extent.\n * @api\n */\nexport function getIntersection(extent1, extent2, dest) {\n  const intersection = dest ? dest : createEmpty();\n  if (intersects(extent1, extent2)) {\n    if (extent1[0] > extent2[0]) {\n      intersection[0] = extent1[0];\n    } else {\n      intersection[0] = extent2[0];\n    }\n    if (extent1[1] > extent2[1]) {\n      intersection[1] = extent1[1];\n    } else {\n      intersection[1] = extent2[1];\n    }\n    if (extent1[2] < extent2[2]) {\n      intersection[2] = extent1[2];\n    } else {\n      intersection[2] = extent2[2];\n    }\n    if (extent1[3] < extent2[3]) {\n      intersection[3] = extent1[3];\n    } else {\n      intersection[3] = extent2[3];\n    }\n  } else {\n    createOrUpdateEmpty(intersection);\n  }\n  return intersection;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @return {number} Margin.\n */\nexport function getMargin(extent) {\n  return getWidth(extent) + getHeight(extent);\n}\n\n/**\n * Get the size (width, height) of an extent.\n * @param {Extent} extent The extent.\n * @return {import(\"./size.js\").Size} The extent size.\n * @api\n */\nexport function getSize(extent) {\n  return [extent[2] - extent[0], extent[3] - extent[1]];\n}\n\n/**\n * Get the top left coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Top left coordinate.\n * @api\n */\nexport function getTopLeft(extent) {\n  return [extent[0], extent[3]];\n}\n\n/**\n * Get the top right coordinate of an extent.\n * @param {Extent} extent Extent.\n * @return {import(\"./coordinate.js\").Coordinate} Top right coordinate.\n * @api\n */\nexport function getTopRight(extent) {\n  return [extent[2], extent[3]];\n}\n\n/**\n * Get the width of an extent.\n * @param {Extent} extent Extent.\n * @return {number} Width.\n * @api\n */\nexport function getWidth(extent) {\n  return extent[2] - extent[0];\n}\n\n/**\n * Determine if one extent intersects another.\n * @param {Extent} extent1 Extent 1.\n * @param {Extent} extent2 Extent.\n * @return {boolean} The two extents intersect.\n * @api\n */\nexport function intersects(extent1, extent2) {\n  return (\n    extent1[0] <= extent2[2] &&\n    extent1[2] >= extent2[0] &&\n    extent1[1] <= extent2[3] &&\n    extent1[3] >= extent2[1]\n  );\n}\n\n/**\n * Determine if an extent is empty.\n * @param {Extent} extent Extent.\n * @return {boolean} Is empty.\n * @api\n */\nexport function isEmpty(extent) {\n  return extent[2] < extent[0] || extent[3] < extent[1];\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {Extent} [dest] Extent.\n * @return {Extent} Extent.\n */\nexport function returnOrUpdate(extent, dest) {\n  if (dest) {\n    dest[0] = extent[0];\n    dest[1] = extent[1];\n    dest[2] = extent[2];\n    dest[3] = extent[3];\n    return dest;\n  }\n  return extent;\n}\n\n/**\n * @param {Extent} extent Extent.\n * @param {number} value Value.\n */\nexport function scaleFromCenter(extent, value) {\n  const deltaX = ((extent[2] - extent[0]) / 2) * (value - 1);\n  const deltaY = ((extent[3] - extent[1]) / 2) * (value - 1);\n  extent[0] -= deltaX;\n  extent[2] += deltaX;\n  extent[1] -= deltaY;\n  extent[3] += deltaY;\n}\n\n/**\n * Determine if the segment between two coordinates intersects (crosses,\n * touches, or is contained by) the provided extent.\n * @param {Extent} extent The extent.\n * @param {import(\"./coordinate.js\").Coordinate} start Segment start coordinate.\n * @param {import(\"./coordinate.js\").Coordinate} end Segment end coordinate.\n * @return {boolean} The segment intersects the extent.\n */\nexport function intersectsSegment(extent, start, end) {\n  let intersects = false;\n  const startRel = coordinateRelationship(extent, start);\n  const endRel = coordinateRelationship(extent, end);\n  if (\n    startRel === Relationship.INTERSECTING ||\n    endRel === Relationship.INTERSECTING\n  ) {\n    intersects = true;\n  } else {\n    const minX = extent[0];\n    const minY = extent[1];\n    const maxX = extent[2];\n    const maxY = extent[3];\n    const startX = start[0];\n    const startY = start[1];\n    const endX = end[0];\n    const endY = end[1];\n    const slope = (endY - startY) / (endX - startX);\n    let x, y;\n    if (!!(endRel & Relationship.ABOVE) && !(startRel & Relationship.ABOVE)) {\n      // potentially intersects top\n      x = endX - (endY - maxY) / slope;\n      intersects = x >= minX && x <= maxX;\n    }\n    if (\n      !intersects &&\n      !!(endRel & Relationship.RIGHT) &&\n      !(startRel & Relationship.RIGHT)\n    ) {\n      // potentially intersects right\n      y = endY - (endX - maxX) * slope;\n      intersects = y >= minY && y <= maxY;\n    }\n    if (\n      !intersects &&\n      !!(endRel & Relationship.BELOW) &&\n      !(startRel & Relationship.BELOW)\n    ) {\n      // potentially intersects bottom\n      x = endX - (endY - minY) / slope;\n      intersects = x >= minX && x <= maxX;\n    }\n    if (\n      !intersects &&\n      !!(endRel & Relationship.LEFT) &&\n      !(startRel & Relationship.LEFT)\n    ) {\n      // potentially intersects left\n      y = endY - (endX - minX) * slope;\n      intersects = y >= minY && y <= maxY;\n    }\n  }\n  return intersects;\n}\n\n/**\n * Apply a transform function to the extent.\n * @param {Extent} extent Extent.\n * @param {import(\"./proj.js\").TransformFunction} transformFn Transform function.\n * Called with `[minX, minY, maxX, maxY]` extent coordinates.\n * @param {Extent} [dest] Destination extent.\n * @param {number} [stops] Number of stops per side used for the transform.\n * By default only the corners are used.\n * @return {Extent} Extent.\n * @api\n */\nexport function applyTransform(extent, transformFn, dest, stops) {\n  if (isEmpty(extent)) {\n    return createOrUpdateEmpty(dest);\n  }\n  let coordinates = [];\n  if (stops > 1) {\n    const width = extent[2] - extent[0];\n    const height = extent[3] - extent[1];\n    for (let i = 0; i < stops; ++i) {\n      coordinates.push(\n        extent[0] + (width * i) / stops,\n        extent[1],\n        extent[2],\n        extent[1] + (height * i) / stops,\n        extent[2] - (width * i) / stops,\n        extent[3],\n        extent[0],\n        extent[3] - (height * i) / stops,\n      );\n    }\n  } else {\n    coordinates = [\n      extent[0],\n      extent[1],\n      extent[2],\n      extent[1],\n      extent[2],\n      extent[3],\n      extent[0],\n      extent[3],\n    ];\n  }\n  transformFn(coordinates, coordinates, 2);\n  const xs = [];\n  const ys = [];\n  for (let i = 0, l = coordinates.length; i < l; i += 2) {\n    xs.push(coordinates[i]);\n    ys.push(coordinates[i + 1]);\n  }\n  return _boundingExtentXYs(xs, ys, dest);\n}\n\n/**\n * Modifies the provided extent in-place to be within the real world\n * extent.\n *\n * @param {Extent} extent Extent.\n * @param {import(\"./proj/Projection.js\").default} projection Projection\n * @return {Extent} The extent within the real world extent.\n */\nexport function wrapX(extent, projection) {\n  const projectionExtent = projection.getExtent();\n  const center = getCenter(extent);\n  if (\n    projection.canWrapX() &&\n    (center[0] < projectionExtent[0] || center[0] >= projectionExtent[2])\n  ) {\n    const worldWidth = getWidth(projectionExtent);\n    const worldsAway = Math.floor(\n      (center[0] - projectionExtent[0]) / worldWidth,\n    );\n    const offset = worldsAway * worldWidth;\n    extent[0] -= offset;\n    extent[2] -= offset;\n  }\n  return extent;\n}\n\n/**\n * Fits the extent to the real world\n *\n * If the extent does not cross the anti meridian, this will return the extent in an array\n * If the extent crosses the anti meridian, the extent will be sliced, so each part fits within the\n * real world\n *\n *\n * @param {Extent} extent Extent.\n * @param {import(\"./proj/Projection.js\").default} projection Projection\n * @param {boolean} [multiWorld] Return all worlds\n * @return {Array<Extent>} The extent within the real world extent.\n */\nexport function wrapAndSliceX(extent, projection, multiWorld) {\n  if (projection.canWrapX()) {\n    const projectionExtent = projection.getExtent();\n\n    if (!isFinite(extent[0]) || !isFinite(extent[2])) {\n      return [[projectionExtent[0], extent[1], projectionExtent[2], extent[3]]];\n    }\n\n    wrapX(extent, projection);\n    const worldWidth = getWidth(projectionExtent);\n\n    if (getWidth(extent) > worldWidth && !multiWorld) {\n      // the extent wraps around on itself\n      return [[projectionExtent[0], extent[1], projectionExtent[2], extent[3]]];\n    }\n    if (extent[0] < projectionExtent[0]) {\n      // the extent crosses the anti meridian, so it needs to be sliced\n      return [\n        [extent[0] + worldWidth, extent[1], projectionExtent[2], extent[3]],\n        [projectionExtent[0], extent[1], extent[2], extent[3]],\n      ];\n    }\n    if (extent[2] > projectionExtent[2]) {\n      // the extent crosses the anti meridian, so it needs to be sliced\n      return [\n        [extent[0], extent[1], projectionExtent[2], extent[3]],\n        [projectionExtent[0], extent[1], extent[2] - worldWidth, extent[3]],\n      ];\n    }\n  }\n\n  return [extent];\n}\n", "/**\n * @module ol/math\n */\n\n/**\n * Takes a number and clamps it to within the provided bounds.\n * @param {number} value The input number.\n * @param {number} min The minimum value to return.\n * @param {number} max The maximum value to return.\n * @return {number} The input number if it is within bounds, or the nearest\n *     number within the bounds.\n */\nexport function clamp(value, min, max) {\n  return Math.min(Math.max(value, min), max);\n}\n\n/**\n * Returns the square of the closest distance between the point (x, y) and the\n * line segment (x1, y1) to (x2, y2).\n * @param {number} x X.\n * @param {number} y Y.\n * @param {number} x1 X1.\n * @param {number} y1 Y1.\n * @param {number} x2 X2.\n * @param {number} y2 Y2.\n * @return {number} Squared distance.\n */\nexport function squaredSegmentDistance(x, y, x1, y1, x2, y2) {\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  if (dx !== 0 || dy !== 0) {\n    const t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n    if (t > 1) {\n      x1 = x2;\n      y1 = y2;\n    } else if (t > 0) {\n      x1 += dx * t;\n      y1 += dy * t;\n    }\n  }\n  return squaredDistance(x, y, x1, y1);\n}\n\n/**\n * Returns the square of the distance between the points (x1, y1) and (x2, y2).\n * @param {number} x1 X1.\n * @param {number} y1 Y1.\n * @param {number} x2 X2.\n * @param {number} y2 Y2.\n * @return {number} Squared distance.\n */\nexport function squaredDistance(x1, y1, x2, y2) {\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  return dx * dx + dy * dy;\n}\n\n/**\n * Solves system of linear equations using Gaussian elimination method.\n *\n * @param {Array<Array<number>>} mat Augmented matrix (n x n + 1 column)\n *                                     in row-major order.\n * @return {Array<number>|null} The resulting vector.\n */\nexport function solveLinearSystem(mat) {\n  const n = mat.length;\n\n  for (let i = 0; i < n; i++) {\n    // Find max in the i-th column (ignoring i - 1 first rows)\n    let maxRow = i;\n    let maxEl = Math.abs(mat[i][i]);\n    for (let r = i + 1; r < n; r++) {\n      const absValue = Math.abs(mat[r][i]);\n      if (absValue > maxEl) {\n        maxEl = absValue;\n        maxRow = r;\n      }\n    }\n\n    if (maxEl === 0) {\n      return null; // matrix is singular\n    }\n\n    // Swap max row with i-th (current) row\n    const tmp = mat[maxRow];\n    mat[maxRow] = mat[i];\n    mat[i] = tmp;\n\n    // Subtract the i-th row to make all the remaining rows 0 in the i-th column\n    for (let j = i + 1; j < n; j++) {\n      const coef = -mat[j][i] / mat[i][i];\n      for (let k = i; k < n + 1; k++) {\n        if (i == k) {\n          mat[j][k] = 0;\n        } else {\n          mat[j][k] += coef * mat[i][k];\n        }\n      }\n    }\n  }\n\n  // Solve Ax=b for upper triangular matrix A (mat)\n  const x = new Array(n);\n  for (let l = n - 1; l >= 0; l--) {\n    x[l] = mat[l][n] / mat[l][l];\n    for (let m = l - 1; m >= 0; m--) {\n      mat[m][n] -= mat[m][l] * x[l];\n    }\n  }\n  return x;\n}\n\n/**\n * Converts radians to to degrees.\n *\n * @param {number} angleInRadians Angle in radians.\n * @return {number} Angle in degrees.\n */\nexport function toDegrees(angleInRadians) {\n  return (angleInRadians * 180) / Math.PI;\n}\n\n/**\n * Converts degrees to radians.\n *\n * @param {number} angleInDegrees Angle in degrees.\n * @return {number} Angle in radians.\n */\nexport function toRadians(angleInDegrees) {\n  return (angleInDegrees * Math.PI) / 180;\n}\n\n/**\n * Returns the modulo of a / b, depending on the sign of b.\n *\n * @param {number} a Dividend.\n * @param {number} b Divisor.\n * @return {number} Modulo.\n */\nexport function modulo(a, b) {\n  const r = a % b;\n  return r * b < 0 ? r + b : r;\n}\n\n/**\n * Calculates the linearly interpolated value of x between a and b.\n *\n * @param {number} a Number\n * @param {number} b Number\n * @param {number} x Value to be interpolated.\n * @return {number} Interpolated value.\n */\nexport function lerp(a, b, x) {\n  return a + x * (b - a);\n}\n\n/**\n * Returns a number with a limited number of decimal digits.\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The input number with a limited number of decimal digits.\n */\nexport function toFixed(n, decimals) {\n  const factor = Math.pow(10, decimals);\n  return Math.round(n * factor) / factor;\n}\n\n/**\n * Rounds a number to the nearest integer value considering only the given number\n * of decimal digits (with rounding on the final digit).\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The nearest integer.\n */\nexport function round(n, decimals) {\n  return Math.round(toFixed(n, decimals));\n}\n\n/**\n * Rounds a number to the next smaller integer considering only the given number\n * of decimal digits (with rounding on the final digit).\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The next smaller integer.\n */\nexport function floor(n, decimals) {\n  return Math.floor(toFixed(n, decimals));\n}\n\n/**\n * Rounds a number to the next bigger integer considering only the given number\n * of decimal digits (with rounding on the final digit).\n * @param {number} n The input number.\n * @param {number} decimals The maximum number of decimal digits.\n * @return {number} The next bigger integer.\n */\nexport function ceil(n, decimals) {\n  return Math.ceil(toFixed(n, decimals));\n}\n\n/**\n * Wraps a number between some minimum and maximum values.\n * @param {number} n The number to wrap.\n * @param {number} min The minimum of the range (inclusive).\n * @param {number} max The maximum of the range (exclusive).\n * @return {number} The wrapped number.\n */\nexport function wrap(n, min, max) {\n  if (n >= min && n < max) {\n    return n;\n  }\n  const range = max - min;\n  return ((((n - min) % range) + range) % range) + min;\n}\n", "/**\n * @module ol/string\n */\n\n/**\n * @param {number} number Number to be formatted\n * @param {number} width The desired width\n * @param {number} [precision] Precision of the output string (i.e. number of decimal places)\n * @return {string} Formatted string\n */\nexport function padNumber(number, width, precision) {\n  const numberString =\n    precision !== undefined ? number.toFixed(precision) : '' + number;\n  let decimal = numberString.indexOf('.');\n  decimal = decimal === -1 ? numberString.length : decimal;\n  return decimal > width\n    ? numberString\n    : new Array(1 + width - decimal).join('0') + numberString;\n}\n\n/**\n * Adapted from https://github.com/omichelsen/compare-versions/blob/master/index.js\n * @param {string|number} v1 First version\n * @param {string|number} v2 Second version\n * @return {number} Value\n */\nexport function compareVersions(v1, v2) {\n  const s1 = ('' + v1).split('.');\n  const s2 = ('' + v2).split('.');\n\n  for (let i = 0; i < Math.max(s1.length, s2.length); i++) {\n    const n1 = parseInt(s1[i] || '0', 10);\n    const n2 = parseInt(s2[i] || '0', 10);\n\n    if (n1 > n2) {\n      return 1;\n    }\n    if (n2 > n1) {\n      return -1;\n    }\n  }\n\n  return 0;\n}\n", "/**\n * @module ol/coordinate\n */\nimport {getWidth} from './extent.js';\nimport {modulo, toFixed} from './math.js';\nimport {padNumber} from './string.js';\n\n/**\n * An array of numbers representing an `xy`, `xyz` or `xyzm` coordinate.\n * Example: `[16, 48]`.\n * @typedef {Array<number>} Coordinate\n * @api\n */\n\n/**\n * A function that takes a {@link module:ol/coordinate~Coordinate} and\n * transforms it into a `{string}`.\n *\n * @typedef {function((Coordinate|undefined)): string} CoordinateFormat\n * @api\n */\n\n/**\n * Add `delta` to `coordinate`. `coordinate` is modified in place and returned\n * by the function.\n *\n * Example:\n *\n *     import {add} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     add(coord, [-2, 4]);\n *     // coord is now [5.85, 51.983333]\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {Coordinate} delta Delta.\n * @return {Coordinate} The input coordinate adjusted by\n * the given delta.\n * @api\n */\nexport function add(coordinate, delta) {\n  coordinate[0] += +delta[0];\n  coordinate[1] += +delta[1];\n  return coordinate;\n}\n\n/**\n * Calculates the point closest to the passed coordinate on the passed circle.\n *\n * @param {Coordinate} coordinate The coordinate.\n * @param {import(\"./geom/Circle.js\").default} circle The circle.\n * @return {Coordinate} Closest point on the circumference.\n */\nexport function closestOnCircle(coordinate, circle) {\n  const r = circle.getRadius();\n  const center = circle.getCenter();\n  const x0 = center[0];\n  const y0 = center[1];\n  const x1 = coordinate[0];\n  const y1 = coordinate[1];\n\n  let dx = x1 - x0;\n  const dy = y1 - y0;\n  if (dx === 0 && dy === 0) {\n    dx = 1;\n  }\n  const d = Math.sqrt(dx * dx + dy * dy);\n\n  const x = x0 + (r * dx) / d;\n  const y = y0 + (r * dy) / d;\n\n  return [x, y];\n}\n\n/**\n * Calculates the point closest to the passed coordinate on the passed segment.\n * This is the foot of the perpendicular of the coordinate to the segment when\n * the foot is on the segment, or the closest segment coordinate when the foot\n * is outside the segment.\n *\n * @param {Coordinate} coordinate The coordinate.\n * @param {Array<Coordinate>} segment The two coordinates\n * of the segment.\n * @return {Coordinate} The foot of the perpendicular of\n * the coordinate to the segment.\n */\nexport function closestOnSegment(coordinate, segment) {\n  const x0 = coordinate[0];\n  const y0 = coordinate[1];\n  const start = segment[0];\n  const end = segment[1];\n  const x1 = start[0];\n  const y1 = start[1];\n  const x2 = end[0];\n  const y2 = end[1];\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const along =\n    dx === 0 && dy === 0\n      ? 0\n      : (dx * (x0 - x1) + dy * (y0 - y1)) / (dx * dx + dy * dy || 0);\n  let x, y;\n  if (along <= 0) {\n    x = x1;\n    y = y1;\n  } else if (along >= 1) {\n    x = x2;\n    y = y2;\n  } else {\n    x = x1 + along * dx;\n    y = y1 + along * dy;\n  }\n  return [x, y];\n}\n\n/**\n * Returns a {@link module:ol/coordinate~CoordinateFormat} function that can be\n * used to format\n * a {Coordinate} to a string.\n *\n * Example without specifying the fractional digits:\n *\n *     import {createStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const stringifyFunc = createStringXY();\n *     const out = stringifyFunc(coord);\n *     // out is now '8, 48'\n *\n * Example with explicitly specifying 2 fractional digits:\n *\n *     import {createStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const stringifyFunc = createStringXY(2);\n *     const out = stringifyFunc(coord);\n *     // out is now '7.85, 47.98'\n *\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {CoordinateFormat} Coordinate format.\n * @api\n */\nexport function createStringXY(fractionDigits) {\n  return (\n    /**\n     * @param {Coordinate} coordinate Coordinate.\n     * @return {string} String XY.\n     */\n    function (coordinate) {\n      return toStringXY(coordinate, fractionDigits);\n    }\n  );\n}\n\n/**\n * @param {string} hemispheres Hemispheres.\n * @param {number} degrees Degrees.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} String.\n */\nexport function degreesToStringHDMS(hemispheres, degrees, fractionDigits) {\n  const normalizedDegrees = modulo(degrees + 180, 360) - 180;\n  const x = Math.abs(3600 * normalizedDegrees);\n  const decimals = fractionDigits || 0;\n\n  let deg = Math.floor(x / 3600);\n  let min = Math.floor((x - deg * 3600) / 60);\n  let sec = toFixed(x - deg * 3600 - min * 60, decimals);\n\n  if (sec >= 60) {\n    sec = 0;\n    min += 1;\n  }\n\n  if (min >= 60) {\n    min = 0;\n    deg += 1;\n  }\n\n  let hdms = deg + '\\u00b0';\n  if (min !== 0 || sec !== 0) {\n    hdms += ' ' + padNumber(min, 2) + '\\u2032';\n  }\n  if (sec !== 0) {\n    hdms += ' ' + padNumber(sec, 2, decimals) + '\\u2033';\n  }\n  if (normalizedDegrees !== 0) {\n    hdms += ' ' + hemispheres.charAt(normalizedDegrees < 0 ? 1 : 0);\n  }\n\n  return hdms;\n}\n\n/**\n * Transforms the given {@link module:ol/coordinate~Coordinate} to a string\n * using the given string template. The strings `{x}` and `{y}` in the template\n * will be replaced with the first and second coordinate values respectively.\n *\n * Example without specifying the fractional digits:\n *\n *     import {format} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const template = 'Coordinate is ({x}|{y}).';\n *     const out = format(coord, template);\n *     // out is now 'Coordinate is (8|48).'\n *\n * Example explicitly specifying the fractional digits:\n *\n *     import {format} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const template = 'Coordinate is ({x}|{y}).';\n *     const out = format(coord, template, 2);\n *     // out is now 'Coordinate is (7.85|47.98).'\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {string} template A template string with `{x}` and `{y}` placeholders\n *     that will be replaced by first and second coordinate values.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} Formatted coordinate.\n * @api\n */\nexport function format(coordinate, template, fractionDigits) {\n  if (coordinate) {\n    return template\n      .replace('{x}', coordinate[0].toFixed(fractionDigits))\n      .replace('{y}', coordinate[1].toFixed(fractionDigits));\n  }\n  return '';\n}\n\n/**\n * @param {Coordinate} coordinate1 First coordinate.\n * @param {Coordinate} coordinate2 Second coordinate.\n * @return {boolean} The two coordinates are equal.\n */\nexport function equals(coordinate1, coordinate2) {\n  let equals = true;\n  for (let i = coordinate1.length - 1; i >= 0; --i) {\n    if (coordinate1[i] != coordinate2[i]) {\n      equals = false;\n      break;\n    }\n  }\n  return equals;\n}\n\n/**\n * Rotate `coordinate` by `angle`. `coordinate` is modified in place and\n * returned by the function.\n *\n * Example:\n *\n *     import {rotate} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const rotateRadians = Math.PI / 2; // 90 degrees\n *     rotate(coord, rotateRadians);\n *     // coord is now [-47.983333, 7.85]\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} angle Angle in radian.\n * @return {Coordinate} Coordinate.\n * @api\n */\nexport function rotate(coordinate, angle) {\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n  const x = coordinate[0] * cosAngle - coordinate[1] * sinAngle;\n  const y = coordinate[1] * cosAngle + coordinate[0] * sinAngle;\n  coordinate[0] = x;\n  coordinate[1] = y;\n  return coordinate;\n}\n\n/**\n * Scale `coordinate` by `scale`. `coordinate` is modified in place and returned\n * by the function.\n *\n * Example:\n *\n *     import {scale as scaleCoordinate} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const scale = 1.2;\n *     scaleCoordinate(coord, scale);\n *     // coord is now [9.42, 57.5799996]\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} scale Scale factor.\n * @return {Coordinate} Coordinate.\n */\nexport function scale(coordinate, scale) {\n  coordinate[0] *= scale;\n  coordinate[1] *= scale;\n  return coordinate;\n}\n\n/**\n * @param {Coordinate} coord1 First coordinate.\n * @param {Coordinate} coord2 Second coordinate.\n * @return {number} Squared distance between coord1 and coord2.\n */\nexport function squaredDistance(coord1, coord2) {\n  const dx = coord1[0] - coord2[0];\n  const dy = coord1[1] - coord2[1];\n  return dx * dx + dy * dy;\n}\n\n/**\n * @param {Coordinate} coord1 First coordinate.\n * @param {Coordinate} coord2 Second coordinate.\n * @return {number} Distance between coord1 and coord2.\n */\nexport function distance(coord1, coord2) {\n  return Math.sqrt(squaredDistance(coord1, coord2));\n}\n\n/**\n * Calculate the squared distance from a coordinate to a line segment.\n *\n * @param {Coordinate} coordinate Coordinate of the point.\n * @param {Array<Coordinate>} segment Line segment (2\n * coordinates).\n * @return {number} Squared distance from the point to the line segment.\n */\nexport function squaredDistanceToSegment(coordinate, segment) {\n  return squaredDistance(coordinate, closestOnSegment(coordinate, segment));\n}\n\n/**\n * Format a geographic coordinate with the hemisphere, degrees, minutes, and\n * seconds.\n *\n * Example without specifying fractional digits:\n *\n *     import {toStringHDMS} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringHDMS(coord);\n *     // out is now '47° 58′ 60″ N 7° 50′ 60″ E'\n *\n * Example explicitly specifying 1 fractional digit:\n *\n *     import {toStringHDMS} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringHDMS(coord, 1);\n *     // out is now '47° 58′ 60.0″ N 7° 50′ 60.0″ E'\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} Hemisphere, degrees, minutes and seconds.\n * @api\n */\nexport function toStringHDMS(coordinate, fractionDigits) {\n  if (coordinate) {\n    return (\n      degreesToStringHDMS('NS', coordinate[1], fractionDigits) +\n      ' ' +\n      degreesToStringHDMS('EW', coordinate[0], fractionDigits)\n    );\n  }\n  return '';\n}\n\n/**\n * Format a coordinate as a comma delimited string.\n *\n * Example without specifying fractional digits:\n *\n *     import {toStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringXY(coord);\n *     // out is now '8, 48'\n *\n * Example explicitly specifying 1 fractional digit:\n *\n *     import {toStringXY} from 'ol/coordinate.js';\n *\n *     const coord = [7.85, 47.983333];\n *     const out = toStringXY(coord, 1);\n *     // out is now '7.8, 48.0'\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {number} [fractionDigits] The number of digits to include\n *    after the decimal point. Default is `0`.\n * @return {string} XY.\n * @api\n */\nexport function toStringXY(coordinate, fractionDigits) {\n  return format(coordinate, '{x}, {y}', fractionDigits);\n}\n\n/**\n * Modifies the provided coordinate in-place to be within the real world\n * extent. The lower projection extent boundary is inclusive, the upper one\n * exclusive.\n *\n * @param {Coordinate} coordinate Coordinate.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @return {Coordinate} The coordinate within the real world extent.\n */\nexport function wrapX(coordinate, projection) {\n  if (projection.canWrapX()) {\n    const worldWidth = getWidth(projection.getExtent());\n    const worldsAway = getWorldsAway(coordinate, projection, worldWidth);\n    if (worldsAway) {\n      coordinate[0] -= worldsAway * worldWidth;\n    }\n  }\n  return coordinate;\n}\n/**\n * @param {Coordinate} coordinate Coordinate.\n * @param {import(\"./proj/Projection.js\").default} projection Projection.\n * @param {number} [sourceExtentWidth] Width of the source extent.\n * @return {number} Offset in world widths.\n */\nexport function getWorldsAway(coordinate, projection, sourceExtentWidth) {\n  const projectionExtent = projection.getExtent();\n  let worldsAway = 0;\n  if (\n    projection.canWrapX() &&\n    (coordinate[0] < projectionExtent[0] || coordinate[0] > projectionExtent[2])\n  ) {\n    sourceExtentWidth = sourceExtentWidth || getWidth(projectionExtent);\n    worldsAway = Math.floor(\n      (coordinate[0] - projectionExtent[0]) / sourceExtentWidth,\n    );\n  }\n  return worldsAway;\n}\n", "/**\n * @module ol/proj/Units\n */\n\n/**\n * @typedef {'radians' | 'degrees' | 'ft' | 'm' | 'pixels' | 'tile-pixels' | 'us-ft'} Units\n * Projection units.\n */\n\n/**\n * See http://duff.ess.washington.edu/data/raster/drg/docs/geotiff.txt\n * @type {Object<number, Units>}\n */\nconst unitByCode = {\n  '9001': 'm',\n  '9002': 'ft',\n  '9003': 'us-ft',\n  '9101': 'radians',\n  '9102': 'degrees',\n};\n\n/**\n * @param {number} code Unit code.\n * @return {Units} Units.\n */\nexport function fromCode(code) {\n  return unitByCode[code];\n}\n\n/**\n * @typedef {Object} MetersPerUnitLookup\n * @property {number} radians Radians\n * @property {number} degrees Degrees\n * @property {number} ft  Feet\n * @property {number} m Meters\n * @property {number} us-ft US feet\n */\n\n/**\n * Meters per unit lookup table.\n * @const\n * @type {MetersPerUnitLookup}\n * @api\n */\nexport const METERS_PER_UNIT = {\n  // use the radius of the Normal sphere\n  'radians': 6370997 / (2 * Math.PI),\n  'degrees': (2 * Math.PI * 6370997) / 360,\n  'ft': 0.3048,\n  'm': 1,\n  'us-ft': 1200 / 3937,\n};\n", "/**\n * @module ol/proj/Projection\n */\nimport {METERS_PER_UNIT} from './Units.js';\n\n/**\n * The function is called with a `number` view resolution and a\n * {@link module:ol/coordinate~Coordinate} as arguments, and returns the `number` resolution\n * in projection units at the passed coordinate.\n * @typedef {function(number, import(\"../coordinate.js\").Coordinate):number} GetPointResolution\n * @api\n */\n\n/**\n * @typedef {Object} Options\n * @property {string} code The SRS identifier code, e.g. `EPSG:4326`.\n * @property {import(\"./Units.js\").Units} [units] Units. Required unless a\n * proj4 projection is defined for `code`.\n * @property {import(\"../extent.js\").Extent} [extent] The validity extent for the SRS.\n * @property {string} [axisOrientation='enu'] The axis orientation as specified in Proj4.\n * @property {boolean} [global=false] Whether the projection is valid for the whole globe.\n * @property {number} [metersPerUnit] The meters per unit for the SRS.\n * If not provided, the `units` are used to get the meters per unit from the {@link METERS_PER_UNIT}\n * lookup table.\n * @property {import(\"../extent.js\").Extent} [worldExtent] The world extent for the SRS.\n * @property {GetPointResolution} [getPointResolution]\n * Function to determine resolution at a point. The function is called with a\n * `number` view resolution and a {@link module:ol/coordinate~Coordinate} as arguments, and returns\n * the `number` resolution in projection units at the passed coordinate. If this is `undefined`,\n * the default {@link module:ol/proj.getPointResolution} function will be used.\n */\n\n/**\n * @classdesc\n * In most cases, you should not need to create instances of this class.\n * Instead, where projection information is required, you can use a string\n * projection code or identifier (e.g. `EPSG:4326`) instead of a projection\n * instance.\n *\n * The library includes support for transforming coordinates between the following\n * projections:\n *\n *  WGS 84 / Geographic - Using codes `EPSG:4326`, `CRS:84`, `urn:ogc:def:crs:EPSG:6.6:4326`,\n *    `urn:ogc:def:crs:OGC:1.3:CRS84`, `urn:ogc:def:crs:OGC:2:84`, `http://www.opengis.net/gml/srs/epsg.xml#4326`,\n *    or `urn:x-ogc:def:crs:EPSG:4326`\n *  WGS 84 / Spherical Mercator - Using codes `EPSG:3857`, `EPSG:102100`, `EPSG:102113`, `EPSG:900913`,\n *    `urn:ogc:def:crs:EPSG:6.18:3:3857`, or `http://www.opengis.net/gml/srs/epsg.xml#3857`\n *  WGS 84 / UTM zones - Using codes `EPSG:32601` through `EPSG:32660` for northern zones\n *    and `EPSG:32701` through `EPSG:32760` for southern zones. Note that the built-in UTM transforms\n *    are lower accuracy (with errors on the order of 0.1 m) than those that you might get in a\n *    library like [proj4js](https://github.com/proj4js/proj4js).\n *\n * For additional projection support, or to use higher accuracy transforms than the built-in ones, you can use\n * the [proj4js](https://github.com/proj4js/proj4js) library. With `proj4js`, after adding any new projection\n * definitions, call the {@link module:ol/proj/proj4.register} function.\n *\n * You can use the {@link module:ol/proj.get} function to retrieve a projection instance\n * for one of the registered projections.\n *\n * @api\n */\nclass Projection {\n  /**\n   * @param {Options} options Projection options.\n   */\n  constructor(options) {\n    /**\n     * @private\n     * @type {string}\n     */\n    this.code_ = options.code;\n\n    /**\n     * Units of projected coordinates. When set to `TILE_PIXELS`, a\n     * `this.extent_` and `this.worldExtent_` must be configured properly for each\n     * tile.\n     * @private\n     * @type {import(\"./Units.js\").Units}\n     */\n    this.units_ = /** @type {import(\"./Units.js\").Units} */ (options.units);\n\n    /**\n     * Validity extent of the projection in projected coordinates. For projections\n     * with `TILE_PIXELS` units, this is the extent of the tile in\n     * tile pixel space.\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.extent_ = options.extent !== undefined ? options.extent : null;\n\n    /**\n     * Extent of the world in EPSG:4326. For projections with\n     * `TILE_PIXELS` units, this is the extent of the tile in\n     * projected coordinate space.\n     * @private\n     * @type {import(\"../extent.js\").Extent}\n     */\n    this.worldExtent_ =\n      options.worldExtent !== undefined ? options.worldExtent : null;\n\n    /**\n     * @private\n     * @type {string}\n     */\n    this.axisOrientation_ =\n      options.axisOrientation !== undefined ? options.axisOrientation : 'enu';\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.global_ = options.global !== undefined ? options.global : false;\n\n    /**\n     * @private\n     * @type {boolean}\n     */\n    this.canWrapX_ = !!(this.global_ && this.extent_);\n\n    /**\n     * @private\n     * @type {GetPointResolution|undefined}\n     */\n    this.getPointResolutionFunc_ = options.getPointResolution;\n\n    /**\n     * @private\n     * @type {import(\"../tilegrid/TileGrid.js\").default}\n     */\n    this.defaultTileGrid_ = null;\n\n    /**\n     * @private\n     * @type {number|undefined}\n     */\n    this.metersPerUnit_ = options.metersPerUnit;\n  }\n\n  /**\n   * @return {boolean} The projection is suitable for wrapping the x-axis\n   */\n  canWrapX() {\n    return this.canWrapX_;\n  }\n\n  /**\n   * Get the code for this projection, e.g. 'EPSG:4326'.\n   * @return {string} Code.\n   * @api\n   */\n  getCode() {\n    return this.code_;\n  }\n\n  /**\n   * Get the validity extent for this projection.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getExtent() {\n    return this.extent_;\n  }\n\n  /**\n   * Get the units of this projection.\n   * @return {import(\"./Units.js\").Units} Units.\n   * @api\n   */\n  getUnits() {\n    return this.units_;\n  }\n\n  /**\n   * Get the amount of meters per unit of this projection.  If the projection is\n   * not configured with `metersPerUnit` or a units identifier, the return is\n   * `undefined`.\n   * @return {number|undefined} Meters.\n   * @api\n   */\n  getMetersPerUnit() {\n    return this.metersPerUnit_ || METERS_PER_UNIT[this.units_];\n  }\n\n  /**\n   * Get the world extent for this projection.\n   * @return {import(\"../extent.js\").Extent} Extent.\n   * @api\n   */\n  getWorldExtent() {\n    return this.worldExtent_;\n  }\n\n  /**\n   * Get the axis orientation of this projection.\n   * Example values are:\n   * enu - the default easting, northing, elevation.\n   * neu - northing, easting, up - useful for \"lat/long\" geographic coordinates,\n   *     or south orientated transverse mercator.\n   * wnu - westing, northing, up - some planetary coordinate systems have\n   *     \"west positive\" coordinate systems\n   * @return {string} Axis orientation.\n   * @api\n   */\n  getAxisOrientation() {\n    return this.axisOrientation_;\n  }\n\n  /**\n   * Is this projection a global projection which spans the whole world?\n   * @return {boolean} Whether the projection is global.\n   * @api\n   */\n  isGlobal() {\n    return this.global_;\n  }\n\n  /**\n   * Set if the projection is a global projection which spans the whole world\n   * @param {boolean} global Whether the projection is global.\n   * @api\n   */\n  setGlobal(global) {\n    this.global_ = global;\n    this.canWrapX_ = !!(global && this.extent_);\n  }\n\n  /**\n   * @return {import(\"../tilegrid/TileGrid.js\").default} The default tile grid.\n   */\n  getDefaultTileGrid() {\n    return this.defaultTileGrid_;\n  }\n\n  /**\n   * @param {import(\"../tilegrid/TileGrid.js\").default} tileGrid The default tile grid.\n   */\n  setDefaultTileGrid(tileGrid) {\n    this.defaultTileGrid_ = tileGrid;\n  }\n\n  /**\n   * Set the validity extent for this projection.\n   * @param {import(\"../extent.js\").Extent} extent Extent.\n   * @api\n   */\n  setExtent(extent) {\n    this.extent_ = extent;\n    this.canWrapX_ = !!(this.global_ && extent);\n  }\n\n  /**\n   * Set the world extent for this projection.\n   * @param {import(\"../extent.js\").Extent} worldExtent World extent\n   *     [minlon, minlat, maxlon, maxlat].\n   * @api\n   */\n  setWorldExtent(worldExtent) {\n    this.worldExtent_ = worldExtent;\n  }\n\n  /**\n   * Set the getPointResolution function (see {@link module:ol/proj.getPointResolution}\n   * for this projection.\n   * @param {function(number, import(\"../coordinate.js\").Coordinate):number} func Function\n   * @api\n   */\n  setGetPointResolution(func) {\n    this.getPointResolutionFunc_ = func;\n  }\n\n  /**\n   * Get the custom point resolution function for this projection (if set).\n   * @return {GetPointResolution|undefined} The custom point\n   * resolution function (if set).\n   */\n  getPointResolutionFunc() {\n    return this.getPointResolutionFunc_;\n  }\n}\n\nexport default Projection;\n", "/**\n * @module ol/proj/epsg3857\n */\nimport Projection from './Projection.js';\n\n/**\n * Radius of WGS84 sphere\n *\n * @const\n * @type {number}\n */\nexport const RADIUS = 6378137;\n\n/**\n * @const\n * @type {number}\n */\nexport const HALF_SIZE = Math.PI * RADIUS;\n\n/**\n * @const\n * @type {import(\"../extent.js\").Extent}\n */\nexport const EXTENT = [-HALF_SIZE, -HALF_SIZE, HALF_SIZE, HALF_SIZE];\n\n/**\n * @const\n * @type {import(\"../extent.js\").Extent}\n */\nexport const WORLD_EXTENT = [-180, -85, 180, 85];\n\n/**\n * Maximum safe value in y direction\n * @const\n * @type {number}\n */\nexport const MAX_SAFE_Y = RADIUS * Math.log(Math.tan(Math.PI / 2));\n\n/**\n * @classdesc\n * Projection object for web/spherical Mercator (EPSG:3857).\n */\nclass EPSG3857Projection extends Projection {\n  /**\n   * @param {string} code Code.\n   */\n  constructor(code) {\n    super({\n      code: code,\n      units: 'm',\n      extent: EXTENT,\n      global: true,\n      worldExtent: WORLD_EXTENT,\n      getPointResolution: function (resolution, point) {\n        return resolution / Math.cosh(point[1] / RADIUS);\n      },\n    });\n  }\n}\n\n/**\n * Projections equal to EPSG:3857.\n *\n * @const\n * @type {Array<import(\"./Projection.js\").default>}\n */\nexport const PROJECTIONS = [\n  new EPSG3857Projection('EPSG:3857'),\n  new EPSG3857Projection('EPSG:102100'),\n  new EPSG3857Projection('EPSG:102113'),\n  new EPSG3857Projection('EPSG:900913'),\n  new EPSG3857Projection('http://www.opengis.net/def/crs/EPSG/0/3857'),\n  new EPSG3857Projection('http://www.opengis.net/gml/srs/epsg.xml#3857'),\n];\n\n/**\n * Transformation from EPSG:4326 to EPSG:3857.\n *\n * @param {Array<number>} input Input array of coordinate values.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @param {number} [dimension] Dimension (default is `2`).\n * @param {number} [stride] Stride (default is `dimension`).\n * @return {Array<number>} Output array of coordinate values.\n */\nexport function fromEPSG4326(input, output, dimension, stride) {\n  const length = input.length;\n  dimension = dimension > 1 ? dimension : 2;\n  stride = stride ?? dimension;\n  if (output === undefined) {\n    if (dimension > 2) {\n      // preserve values beyond second dimension\n      output = input.slice();\n    } else {\n      output = new Array(length);\n    }\n  }\n  for (let i = 0; i < length; i += stride) {\n    output[i] = (HALF_SIZE * input[i]) / 180;\n    let y = RADIUS * Math.log(Math.tan((Math.PI * (+input[i + 1] + 90)) / 360));\n    if (y > MAX_SAFE_Y) {\n      y = MAX_SAFE_Y;\n    } else if (y < -MAX_SAFE_Y) {\n      y = -MAX_SAFE_Y;\n    }\n    output[i + 1] = y;\n  }\n  return output;\n}\n\n/**\n * Transformation from EPSG:3857 to EPSG:4326.\n *\n * @param {Array<number>} input Input array of coordinate values.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @param {number} [dimension] Dimension (default is `2`).\n * @param {number} [stride] Stride (default is `dimension`).\n * @return {Array<number>} Output array of coordinate values.\n */\nexport function toEPSG4326(input, output, dimension, stride) {\n  const length = input.length;\n  dimension = dimension > 1 ? dimension : 2;\n  stride = stride ?? dimension;\n  if (output === undefined) {\n    if (dimension > 2) {\n      // preserve values beyond second dimension\n      output = input.slice();\n    } else {\n      output = new Array(length);\n    }\n  }\n  for (let i = 0; i < length; i += stride) {\n    output[i] = (180 * input[i]) / HALF_SIZE;\n    output[i + 1] =\n      (360 * Math.atan(Math.exp(input[i + 1] / RADIUS))) / Math.PI - 90;\n  }\n  return output;\n}\n", "/**\n * @module ol/proj/epsg4326\n */\nimport Projection from './Projection.js';\n\n/**\n * Semi-major radius of the WGS84 ellipsoid.\n *\n * @const\n * @type {number}\n */\nexport const RADIUS = 6378137;\n\n/**\n * Extent of the EPSG:4326 projection which is the whole world.\n *\n * @const\n * @type {import(\"../extent.js\").Extent}\n */\nexport const EXTENT = [-180, -90, 180, 90];\n\n/**\n * @const\n * @type {number}\n */\nexport const METERS_PER_UNIT = (Math.PI * RADIUS) / 180;\n\n/**\n * @classdesc\n * Projection object for WGS84 geographic coordinates (EPSG:4326).\n *\n * Note that OpenLayers does not strictly comply with the EPSG definition.\n * The EPSG registry defines 4326 as a CRS for Latitude,Longitude (y,x).\n * OpenLayers treats EPSG:4326 as a pseudo-projection, with x,y coordinates.\n */\nclass EPSG4326Projection extends Projection {\n  /**\n   * @param {string} code Code.\n   * @param {string} [axisOrientation] Axis orientation.\n   */\n  constructor(code, axisOrientation) {\n    super({\n      code: code,\n      units: 'degrees',\n      extent: EXTENT,\n      axisOrientation: axisOrientation,\n      global: true,\n      metersPerUnit: METERS_PER_UNIT,\n      worldExtent: EXTENT,\n    });\n  }\n}\n\n/**\n * Projections equal to EPSG:4326.\n *\n * @const\n * @type {Array<import(\"./Projection.js\").default>}\n */\nexport const PROJECTIONS = [\n  new EPSG4326Projection('CRS:84'),\n  new EPSG4326Projection('EPSG:4326', 'neu'),\n  new EPSG4326Projection('urn:ogc:def:crs:OGC:1.3:CRS84'),\n  new EPSG4326Projection('urn:ogc:def:crs:OGC:2:84'),\n  new EPSG4326Projection('http://www.opengis.net/def/crs/OGC/1.3/CRS84'),\n  new EPSG4326Projection('http://www.opengis.net/gml/srs/epsg.xml#4326', 'neu'),\n  new EPSG4326Projection('http://www.opengis.net/def/crs/EPSG/0/4326', 'neu'),\n];\n", "/**\n * @module ol/proj/projections\n */\n\n/**\n * @type {Object<string, import(\"./Projection.js\").default>}\n */\nlet cache = {};\n\n/**\n * Clear the projections cache.\n */\nexport function clear() {\n  cache = {};\n}\n\n/**\n * Get a cached projection by code.\n * @param {string} code The code for the projection.\n * @return {import(\"./Projection.js\").default|null} The projection (if cached).\n */\nexport function get(code) {\n  return (\n    cache[code] ||\n    cache[code.replace(/urn:(x-)?ogc:def:crs:EPSG:(.*:)?(\\w+)$/, 'EPSG:$3')] ||\n    null\n  );\n}\n\n/**\n * Add a projection to the cache.\n * @param {string} code The projection code.\n * @param {import(\"./Projection.js\").default} projection The projection to cache.\n */\nexport function add(code, projection) {\n  cache[code] = projection;\n}\n", "/**\n * @module ol/proj/transforms\n */\nimport {isEmpty} from '../obj.js';\n\n/**\n * @private\n * @type {!Object<string, Object<string, import(\"../proj.js\").TransformFunction>>}\n */\nlet transforms = {};\n\n/**\n * Clear the transform cache.\n */\nexport function clear() {\n  transforms = {};\n}\n\n/**\n * Registers a conversion function to convert coordinates from the source\n * projection to the destination projection.\n *\n * @param {import(\"./Projection.js\").default} source Source.\n * @param {import(\"./Projection.js\").default} destination Destination.\n * @param {import(\"../proj.js\").TransformFunction} transformFn Transform.\n */\nexport function add(source, destination, transformFn) {\n  const sourceCode = source.getCode();\n  const destinationCode = destination.getCode();\n  if (!(sourceCode in transforms)) {\n    transforms[sourceCode] = {};\n  }\n  transforms[sourceCode][destinationCode] = transformFn;\n}\n\n/**\n * Unregisters the conversion function to convert coordinates from the source\n * projection to the destination projection.  This method is used to clean up\n * cached transforms during testing.\n *\n * @param {import(\"./Projection.js\").default} source Source projection.\n * @param {import(\"./Projection.js\").default} destination Destination projection.\n * @return {import(\"../proj.js\").TransformFunction} transformFn The unregistered transform.\n */\nexport function remove(source, destination) {\n  const sourceCode = source.getCode();\n  const destinationCode = destination.getCode();\n  const transform = transforms[sourceCode][destinationCode];\n  delete transforms[sourceCode][destinationCode];\n  if (isEmpty(transforms[sourceCode])) {\n    delete transforms[sourceCode];\n  }\n  return transform;\n}\n\n/**\n * Get a transform given a source code and a destination code.\n * @param {string} sourceCode The code for the source projection.\n * @param {string} destinationCode The code for the destination projection.\n * @return {import(\"../proj.js\").TransformFunction|null} The transform function (if found).\n */\nexport function get(sourceCode, destinationCode) {\n  if (sourceCode in transforms && destinationCode in transforms[sourceCode]) {\n    return transforms[sourceCode][destinationCode];\n  }\n  return null;\n}\n", "/**\n * @module ol/proj/utm\n */\n\n/**\n * Adapted from https://github.com/Turbo87/utm\n * Copyright (c) 2012-2017 <PERSON>\n *\n * The functions here provide approximate transforms to and from UTM.\n * They are not appropriate for use beyond the validity extend of a UTM\n * zone, and the accuracy of the transform decreases toward the zone\n * edges.\n */\n\nimport {toDegrees, toRadians, wrap} from '../math.js';\nimport Projection from './Projection.js';\n\n/**\n * @typedef {Object} UTMZone\n * @property {number} number The zone number (1 - 60).\n * @property {boolean} north The northern hemisphere.\n */\n\nconst K0 = 0.9996;\n\nconst E = 0.00669438;\nconst E2 = E * E;\nconst E3 = E2 * E;\nconst E_P2 = E / (1 - E);\n\nconst SQRT_E = Math.sqrt(1 - E);\nconst _E = (1 - SQRT_E) / (1 + SQRT_E);\nconst _E2 = _E * _E;\nconst _E3 = _E2 * _E;\nconst _E4 = _E3 * _E;\nconst _E5 = _E4 * _E;\n\nconst M1 = 1 - E / 4 - (3 * E2) / 64 - (5 * E3) / 256;\nconst M2 = (3 * E) / 8 + (3 * E2) / 32 + (45 * E3) / 1024;\nconst M3 = (15 * E2) / 256 + (45 * E3) / 1024;\nconst M4 = (35 * E3) / 3072;\n\nconst P2 = (3 / 2) * _E - (27 / 32) * _E3 + (269 / 512) * _E5;\nconst P3 = (21 / 16) * _E2 - (55 / 32) * _E4;\nconst P4 = (151 / 96) * _E3 - (417 / 128) * _E5;\nconst P5 = (1097 / 512) * _E4;\n\nconst R = 6378137;\n\n/**\n * @param {number} easting Easting value of coordinate.\n * @param {number} northing Northing value of coordinate.\n * @param {UTMZone} zone The UTM zone.\n * @return {import(\"../coordinate.js\").Coordinate} The transformed coordinate.\n */\nfunction toLonLat(easting, northing, zone) {\n  const x = easting - 500000;\n  const y = zone.north ? northing : northing - 10000000;\n\n  const m = y / K0;\n  const mu = m / (R * M1);\n\n  const pRad =\n    mu +\n    P2 * Math.sin(2 * mu) +\n    P3 * Math.sin(4 * mu) +\n    P4 * Math.sin(6 * mu) +\n    P5 * Math.sin(8 * mu);\n\n  const pSin = Math.sin(pRad);\n  const pSin2 = pSin * pSin;\n\n  const pCos = Math.cos(pRad);\n\n  const pTan = pSin / pCos;\n  const pTan2 = pTan * pTan;\n  const pTan4 = pTan2 * pTan2;\n\n  const epSin = 1 - E * pSin2;\n  const epSinSqrt = Math.sqrt(1 - E * pSin2);\n\n  const n = R / epSinSqrt;\n  const r = (1 - E) / epSin;\n\n  const c = E_P2 * pCos ** 2;\n  const c2 = c * c;\n\n  const d = x / (n * K0);\n  const d2 = d * d;\n  const d3 = d2 * d;\n  const d4 = d3 * d;\n  const d5 = d4 * d;\n  const d6 = d5 * d;\n\n  const latitude =\n    pRad -\n    (pTan / r) *\n      (d2 / 2 - (d4 / 24) * (5 + 3 * pTan2 + 10 * c - 4 * c2 - 9 * E_P2)) +\n    (d6 / 720) * (61 + 90 * pTan2 + 298 * c + 45 * pTan4 - 252 * E_P2 - 3 * c2);\n\n  let longitude =\n    (d -\n      (d3 / 6) * (1 + 2 * pTan2 + c) +\n      (d5 / 120) * (5 - 2 * c + 28 * pTan2 - 3 * c2 + 8 * E_P2 + 24 * pTan4)) /\n    pCos;\n\n  longitude = wrap(\n    longitude + toRadians(zoneToCentralLongitude(zone.number)),\n    -Math.PI,\n    Math.PI,\n  );\n\n  return [toDegrees(longitude), toDegrees(latitude)];\n}\n\nconst MIN_LATITUDE = -80;\nconst MAX_LATITUDE = 84;\nconst MIN_LONGITUDE = -180;\nconst MAX_LONGITUDE = 180;\n\n/**\n * @param {number} longitude The longitude.\n * @param {number} latitude The latitude.\n * @param {UTMZone} zone The UTM zone.\n * @return {import('../coordinate.js').Coordinate} The UTM coordinate.\n */\nfunction fromLonLat(longitude, latitude, zone) {\n  longitude = wrap(longitude, MIN_LONGITUDE, MAX_LONGITUDE);\n\n  if (latitude < MIN_LATITUDE) {\n    latitude = MIN_LATITUDE;\n  } else if (latitude > MAX_LATITUDE) {\n    latitude = MAX_LATITUDE;\n  }\n\n  const latRad = toRadians(latitude);\n  const latSin = Math.sin(latRad);\n  const latCos = Math.cos(latRad);\n\n  const latTan = latSin / latCos;\n  const latTan2 = latTan * latTan;\n  const latTan4 = latTan2 * latTan2;\n\n  const lonRad = toRadians(longitude);\n  const centralLon = zoneToCentralLongitude(zone.number);\n  const centralLonRad = toRadians(centralLon);\n\n  const n = R / Math.sqrt(1 - E * latSin ** 2);\n  const c = E_P2 * latCos ** 2;\n\n  const a = latCos * wrap(lonRad - centralLonRad, -Math.PI, Math.PI);\n  const a2 = a * a;\n  const a3 = a2 * a;\n  const a4 = a3 * a;\n  const a5 = a4 * a;\n  const a6 = a5 * a;\n\n  const m =\n    R *\n    (M1 * latRad -\n      M2 * Math.sin(2 * latRad) +\n      M3 * Math.sin(4 * latRad) -\n      M4 * Math.sin(6 * latRad));\n\n  const easting =\n    K0 *\n      n *\n      (a +\n        (a3 / 6) * (1 - latTan2 + c) +\n        (a5 / 120) * (5 - 18 * latTan2 + latTan4 + 72 * c - 58 * E_P2)) +\n    500000;\n\n  let northing =\n    K0 *\n    (m +\n      n *\n        latTan *\n        (a2 / 2 +\n          (a4 / 24) * (5 - latTan2 + 9 * c + 4 * c ** 2) +\n          (a6 / 720) * (61 - 58 * latTan2 + latTan4 + 600 * c - 330 * E_P2)));\n\n  if (!zone.north) {\n    northing += 10000000;\n  }\n\n  return [easting, northing];\n}\n\n/**\n * @param {number} zone The zone number.\n * @return {number} The central longitude in degrees.\n */\nfunction zoneToCentralLongitude(zone) {\n  return (zone - 1) * 6 - 180 + 3;\n}\n\n/**\n * @type {Array<RegExp>}\n */\nconst epsgRegExes = [\n  /^EPSG:(\\d+)$/,\n  /^urn:ogc:def:crs:EPSG::(\\d+)$/,\n  /^http:\\/\\/www\\.opengis\\.net\\/def\\/crs\\/EPSG\\/0\\/(\\d+)$/,\n];\n\n/**\n * @param {string} code The projection code.\n * @return {UTMZone|null} The UTM zone info (or null if not UTM).\n */\nexport function zoneFromCode(code) {\n  let epsgId = 0;\n  for (const re of epsgRegExes) {\n    const match = code.match(re);\n    if (match) {\n      epsgId = parseInt(match[1]);\n      break;\n    }\n  }\n  if (!epsgId) {\n    return null;\n  }\n\n  let number = 0;\n  let north = false;\n  if (epsgId > 32700 && epsgId < 32761) {\n    number = epsgId - 32700;\n  } else if (epsgId > 32600 && epsgId < 32661) {\n    north = true;\n    number = epsgId - 32600;\n  }\n  if (!number) {\n    return null;\n  }\n\n  return {number, north};\n}\n\n/**\n * @param {function(number, number, UTMZone): import('../coordinate.js').Coordinate} transformer The transformer.\n * @param {UTMZone} zone The UTM zone.\n * @return {import('../proj.js').TransformFunction} The transform function.\n */\nfunction makeTransformFunction(transformer, zone) {\n  return function (input, output, dimension, stride) {\n    const length = input.length;\n    dimension = dimension > 1 ? dimension : 2;\n    stride = stride ?? dimension;\n    if (!output) {\n      if (dimension > 2) {\n        output = input.slice();\n      } else {\n        output = new Array(length);\n      }\n    }\n    for (let i = 0; i < length; i += stride) {\n      const x = input[i];\n      const y = input[i + 1];\n      const coord = transformer(x, y, zone);\n      output[i] = coord[0];\n      output[i + 1] = coord[1];\n    }\n    return output;\n  };\n}\n\n/**\n * @param {string} code The projection code.\n * @return {import('./Projection.js').default|null} A projection or null if unable to create one.\n */\nexport function makeProjection(code) {\n  const zone = zoneFromCode(code);\n  if (!zone) {\n    return null;\n  }\n  return new Projection({code, units: 'm'});\n}\n\n/**\n * @param {import('./Projection.js').default} projection The projection.\n * @return {import('../proj.js').Transforms|null} The transforms lookup or null if unable to handle projection.\n */\nexport function makeTransforms(projection) {\n  const zone = zoneFromCode(projection.getCode());\n  if (!zone) {\n    return null;\n  }\n\n  return {\n    forward: makeTransformFunction(fromLonLat, zone),\n    inverse: makeTransformFunction(toLonLat, zone),\n  };\n}\n", "/**\n * @module ol/sphere\n */\nimport {toDegrees, toRadians} from './math.js';\n\n/**\n * Object literal with options for the {@link getLength} or {@link getArea}\n * functions.\n * @typedef {Object} SphereMetricOptions\n * @property {import(\"./proj.js\").ProjectionLike} [projection='EPSG:3857']\n * Projection of the  geometry.  By default, the geometry is assumed to be in\n * Web Mercator.\n * @property {number} [radius=6371008.8] Sphere radius.  By default, the\n * [mean Earth radius](https://en.wikipedia.org/wiki/Earth_radius#Mean_radius)\n * for the WGS84 ellipsoid is used.\n */\n\n/**\n * The mean Earth radius (1/3 * (2a + b)) for the WGS84 ellipsoid.\n * https://en.wikipedia.org/wiki/Earth_radius#Mean_radius\n * @type {number}\n */\nexport const DEFAULT_RADIUS = 6371008.8;\n\n/**\n * Get the great circle distance (in meters) between two geographic coordinates.\n * @param {Array} c1 Starting coordinate.\n * @param {Array} c2 Ending coordinate.\n * @param {number} [radius] The sphere radius to use.  Defaults to the Earth's\n *     mean radius using the WGS84 ellipsoid.\n * @return {number} The great circle distance between the points (in meters).\n * @api\n */\nexport function getDistance(c1, c2, radius) {\n  radius = radius || DEFAULT_RADIUS;\n  const lat1 = toRadians(c1[1]);\n  const lat2 = toRadians(c2[1]);\n  const deltaLatBy2 = (lat2 - lat1) / 2;\n  const deltaLonBy2 = toRadians(c2[0] - c1[0]) / 2;\n  const a =\n    Math.sin(deltaLatBy2) * Math.sin(deltaLatBy2) +\n    Math.sin(deltaLonBy2) *\n      Math.sin(deltaLonBy2) *\n      Math.cos(lat1) *\n      Math.cos(lat2);\n  return 2 * radius * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n}\n\n/**\n * Get the cumulative great circle length of linestring coordinates (geographic).\n * @param {Array} coordinates Linestring coordinates.\n * @param {number} radius The sphere radius to use.\n * @return {number} The length (in meters).\n */\nfunction getLengthInternal(coordinates, radius) {\n  let length = 0;\n  for (let i = 0, ii = coordinates.length; i < ii - 1; ++i) {\n    length += getDistance(coordinates[i], coordinates[i + 1], radius);\n  }\n  return length;\n}\n\n/**\n * Get the spherical length of a geometry.  This length is the sum of the\n * great circle distances between coordinates.  For polygons, the length is\n * the sum of all rings.  For points, the length is zero.  For multi-part\n * geometries, the length is the sum of the length of each part.\n * @param {import(\"./geom/Geometry.js\").default} geometry A geometry.\n * @param {SphereMetricOptions} [options] Options for the\n * length calculation.  By default, geometries are assumed to be in 'EPSG:3857'.\n * You can change this by providing a `projection` option.\n * @return {number} The spherical length (in meters).\n * @api\n */\nexport function getLength(geometry, options) {\n  options = options || {};\n  const radius = options.radius || DEFAULT_RADIUS;\n  const projection = options.projection || 'EPSG:3857';\n  const type = geometry.getType();\n  if (type !== 'GeometryCollection') {\n    geometry = geometry.clone().transform(projection, 'EPSG:4326');\n  }\n  let length = 0;\n  let coordinates, coords, i, ii, j, jj;\n  switch (type) {\n    case 'Point':\n    case 'MultiPoint': {\n      break;\n    }\n    case 'LineString':\n    case 'LinearRing': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      length = getLengthInternal(coordinates, radius);\n      break;\n    }\n    case 'MultiLineString':\n    case 'Polygon': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      for (i = 0, ii = coordinates.length; i < ii; ++i) {\n        length += getLengthInternal(coordinates[i], radius);\n      }\n      break;\n    }\n    case 'MultiPolygon': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      for (i = 0, ii = coordinates.length; i < ii; ++i) {\n        coords = coordinates[i];\n        for (j = 0, jj = coords.length; j < jj; ++j) {\n          length += getLengthInternal(coords[j], radius);\n        }\n      }\n      break;\n    }\n    case 'GeometryCollection': {\n      const geometries =\n        /** @type {import(\"./geom/GeometryCollection.js\").default} */ (\n          geometry\n        ).getGeometries();\n      for (i = 0, ii = geometries.length; i < ii; ++i) {\n        length += getLength(geometries[i], options);\n      }\n      break;\n    }\n    default: {\n      throw new Error('Unsupported geometry type: ' + type);\n    }\n  }\n  return length;\n}\n\n/**\n * Returns the spherical area for a list of coordinates.\n *\n * [Reference](https://trs.jpl.nasa.gov/handle/2014/40409)\n * Robert. G. Chamberlain and William H. Duquette, \"Some Algorithms for\n * Polygons on a Sphere\", JPL Publication 07-03, Jet Propulsion\n * Laboratory, Pasadena, CA, June 2007\n *\n * @param {Array<import(\"./coordinate.js\").Coordinate>} coordinates List of coordinates of a linear\n * ring. If the ring is oriented clockwise, the area will be positive,\n * otherwise it will be negative.\n * @param {number} radius The sphere radius.\n * @return {number} Area (in square meters).\n */\nfunction getAreaInternal(coordinates, radius) {\n  let area = 0;\n  const len = coordinates.length;\n  let x1 = coordinates[len - 1][0];\n  let y1 = coordinates[len - 1][1];\n  for (let i = 0; i < len; i++) {\n    const x2 = coordinates[i][0];\n    const y2 = coordinates[i][1];\n    area +=\n      toRadians(x2 - x1) *\n      (2 + Math.sin(toRadians(y1)) + Math.sin(toRadians(y2)));\n    x1 = x2;\n    y1 = y2;\n  }\n  return (area * radius * radius) / 2.0;\n}\n\n/**\n * Get the spherical area of a geometry.  This is the area (in meters) assuming\n * that polygon edges are segments of great circles on a sphere.\n * @param {import(\"./geom/Geometry.js\").default} geometry A geometry.\n * @param {SphereMetricOptions} [options] Options for the area\n *     calculation.  By default, geometries are assumed to be in 'EPSG:3857'.\n *     You can change this by providing a `projection` option.\n * @return {number} The spherical area (in square meters).\n * @api\n */\nexport function getArea(geometry, options) {\n  options = options || {};\n  const radius = options.radius || DEFAULT_RADIUS;\n  const projection = options.projection || 'EPSG:3857';\n  const type = geometry.getType();\n  if (type !== 'GeometryCollection') {\n    geometry = geometry.clone().transform(projection, 'EPSG:4326');\n  }\n  let area = 0;\n  let coordinates, coords, i, ii, j, jj;\n  switch (type) {\n    case 'Point':\n    case 'MultiPoint':\n    case 'LineString':\n    case 'MultiLineString':\n    case 'LinearRing': {\n      break;\n    }\n    case 'Polygon': {\n      coordinates = /** @type {import(\"./geom/Polygon.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      area = Math.abs(getAreaInternal(coordinates[0], radius));\n      for (i = 1, ii = coordinates.length; i < ii; ++i) {\n        area -= Math.abs(getAreaInternal(coordinates[i], radius));\n      }\n      break;\n    }\n    case 'MultiPolygon': {\n      coordinates = /** @type {import(\"./geom/SimpleGeometry.js\").default} */ (\n        geometry\n      ).getCoordinates();\n      for (i = 0, ii = coordinates.length; i < ii; ++i) {\n        coords = coordinates[i];\n        area += Math.abs(getAreaInternal(coords[0], radius));\n        for (j = 1, jj = coords.length; j < jj; ++j) {\n          area -= Math.abs(getAreaInternal(coords[j], radius));\n        }\n      }\n      break;\n    }\n    case 'GeometryCollection': {\n      const geometries =\n        /** @type {import(\"./geom/GeometryCollection.js\").default} */ (\n          geometry\n        ).getGeometries();\n      for (i = 0, ii = geometries.length; i < ii; ++i) {\n        area += getArea(geometries[i], options);\n      }\n      break;\n    }\n    default: {\n      throw new Error('Unsupported geometry type: ' + type);\n    }\n  }\n  return area;\n}\n\n/**\n * Returns the coordinate at the given distance and bearing from `c1`.\n *\n * @param {import(\"./coordinate.js\").Coordinate} c1 The origin point (`[lon, lat]` in degrees).\n * @param {number} distance The great-circle distance between the origin\n *     point and the target point.\n * @param {number} bearing The bearing (in radians).\n * @param {number} [radius] The sphere radius to use.  Defaults to the Earth's\n *     mean radius using the WGS84 ellipsoid.\n * @return {import(\"./coordinate.js\").Coordinate} The target point.\n */\nexport function offset(c1, distance, bearing, radius) {\n  radius = radius || DEFAULT_RADIUS;\n  const lat1 = toRadians(c1[1]);\n  const lon1 = toRadians(c1[0]);\n  const dByR = distance / radius;\n  const lat = Math.asin(\n    Math.sin(lat1) * Math.cos(dByR) +\n      Math.cos(lat1) * Math.sin(dByR) * Math.cos(bearing),\n  );\n  const lon =\n    lon1 +\n    Math.atan2(\n      Math.sin(bearing) * Math.sin(dByR) * Math.cos(lat1),\n      Math.cos(dByR) - Math.sin(lat1) * Math.sin(lat),\n    );\n  return [toDegrees(lon), toDegrees(lat)];\n}\n", "/**\n * @module ol/proj\n */\n\n/**\n * The ol/proj module stores:\n * a list of {@link module:ol/proj/Projection~Projection}\n * objects, one for each projection supported by the application\n * a list of transform functions needed to convert coordinates in one projection\n * into another.\n *\n * The static functions are the methods used to maintain these.\n * Each transform function can handle not only simple coordinate pairs, but also\n * large arrays of coordinates such as vector geometries.\n *\n * When loaded, the library adds projection objects for EPSG:4326 (WGS84\n * geographic coordinates) and EPSG:3857 (Web or Spherical Mercator, as used\n * for example by Bing Maps or OpenStreetMap), together with the relevant\n * transform functions.\n *\n * Additional transforms may be added by using the http://proj4js.org/\n * library (version 2.2 or later). You can use the full build supplied by\n * Proj4js, or create a custom build to support those projections you need; see\n * the Proj4js website for how to do this. You also need the Proj4js definitions\n * for the required projections. These definitions can be obtained from\n * https://epsg.io/, and are a JS function, so can be loaded in a script\n * tag (as in the examples) or pasted into your application.\n *\n * After all required projection definitions are added to proj4's registry (by\n * using `proj4.defs()`), simply call `register(proj4)` from the `ol/proj/proj4`\n * package. Existing transforms are not changed by this function. See\n * examples/wms-image-custom-proj for an example of this.\n *\n * Additional projection definitions can be registered with `proj4.defs()` any\n * time. Just make sure to call `register(proj4)` again; for example, with user-supplied data where you don't\n * know in advance what projections are needed, you can initially load minimal\n * support and then load whichever are requested.\n *\n * Note that Proj4js does not support projection extents. If you want to add\n * one for creating default tile grids, you can add it after the Projection\n * object has been created with `setExtent`, for example,\n * `get('EPSG:1234').setExtent(extent)`.\n *\n * In addition to Proj4js support, any transform functions can be added with\n * {@link module:ol/proj.addCoordinateTransforms}. To use this, you must first create\n * a {@link module:ol/proj/Projection~Projection} object for the new projection and add it with\n * {@link module:ol/proj.addProjection}. You can then add the forward and inverse\n * functions with {@link module:ol/proj.addCoordinateTransforms}. See\n * examples/wms-custom-proj for an example of this.\n *\n * Note that if no transforms are needed and you only need to define the\n * projection, just add a {@link module:ol/proj/Projection~Projection} with\n * {@link module:ol/proj.addProjection}. See examples/wms-no-proj for an example of\n * this.\n */\nimport {warn} from './console.js';\nimport {equals, getWorldsAway} from './coordinate.js';\nimport {applyTransform, getWidth} from './extent.js';\nimport {clamp, modulo} from './math.js';\nimport Projection from './proj/Projection.js';\nimport {METERS_PER_UNIT} from './proj/Units.js';\nimport {\n  PROJECTIONS as EPSG3857_PROJECTIONS,\n  fromEPSG4326,\n  toEPSG4326,\n} from './proj/epsg3857.js';\nimport {PROJECTIONS as EPSG4326_PROJECTIONS} from './proj/epsg4326.js';\nimport {\n  add as addProj,\n  clear as clearProj,\n  get as getProj,\n} from './proj/projections.js';\nimport {\n  add as addTransformFunc,\n  clear as clearTransformFuncs,\n  get as getTransformFunc,\n} from './proj/transforms.js';\nimport {\n  makeProjection as makeUTMProjection,\n  makeTransforms as makeUTMTransforms,\n} from './proj/utm.js';\nimport {getDistance} from './sphere.js';\n\n/**\n * A projection as {@link module:ol/proj/Projection~Projection}, SRS identifier\n * string or undefined.\n * @typedef {Projection|string|undefined} ProjectionLike\n * @api\n */\n\n/**\n * @typedef {Object} Transforms\n * @property {TransformFunction} forward The forward transform (from geographic).\n * @property {TransformFunction} inverse The inverse transform (to geographic).\n */\n\n/**\n * @type {Array<function(Projection): Transforms|null>}\n */\nconst transformFactories = [makeUTMTransforms];\n\n/**\n * @type {Array<function(string): Projection|null>}\n */\nconst projectionFactories = [makeUTMProjection];\n\n/**\n * A transform function accepts an array of input coordinate values, an optional\n * output array, and an optional dimension (default should be 2).  The function\n * transforms the input coordinate values, populates the output array, and\n * returns the output array.\n *\n * @callback TransformFunction\n * @param {Array<number>} input\n * @param {Array<number>} [output]\n * @param {number} [dimension]\n * @param {number} [stride]\n * @return {Array<number>}\n *\n * @api\n */\n\nexport {METERS_PER_UNIT};\n\nexport {Projection};\n\nlet showCoordinateWarning = true;\n\n/**\n * @param {boolean} [disable] Disable console info about `useGeographic()`\n */\nexport function disableCoordinateWarning(disable) {\n  const hide = disable === undefined ? true : disable;\n  showCoordinateWarning = !hide;\n}\n\n/**\n * @param {Array<number>} input Input coordinate array.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @return {Array<number>} Output coordinate array (new array, same coordinate\n *     values).\n */\nexport function cloneTransform(input, output) {\n  if (output !== undefined) {\n    for (let i = 0, ii = input.length; i < ii; ++i) {\n      output[i] = input[i];\n    }\n    output = output;\n  } else {\n    output = input.slice();\n  }\n  return output;\n}\n\n/**\n * @param {Array<number>} input Input coordinate array.\n * @param {Array<number>} [output] Output array of coordinate values.\n * @return {Array<number>} Input coordinate array (same array as input).\n */\nexport function identityTransform(input, output) {\n  if (output !== undefined && input !== output) {\n    for (let i = 0, ii = input.length; i < ii; ++i) {\n      output[i] = input[i];\n    }\n    input = output;\n  }\n  return input;\n}\n\n/**\n * Add a Projection object to the list of supported projections that can be\n * looked up by their code.\n *\n * @param {Projection} projection Projection instance.\n * @api\n */\nexport function addProjection(projection) {\n  addProj(projection.getCode(), projection);\n  addTransformFunc(projection, projection, cloneTransform);\n}\n\n/**\n * @param {Array<Projection>} projections Projections.\n */\nexport function addProjections(projections) {\n  projections.forEach(addProjection);\n}\n\n/**\n * Fetches a Projection object for the code specified.\n *\n * @param {ProjectionLike} projectionLike Either a code string which is\n *     a combination of authority and identifier such as \"EPSG:4326\", or an\n *     existing projection object, or undefined.\n * @return {Projection|null} Projection object, or null if not in list.\n * @api\n */\nexport function get(projectionLike) {\n  if (!(typeof projectionLike === 'string')) {\n    return projectionLike;\n  }\n  const projection = getProj(projectionLike);\n  if (projection) {\n    return projection;\n  }\n  for (const makeProjection of projectionFactories) {\n    const projection = makeProjection(projectionLike);\n    if (projection) {\n      return projection;\n    }\n  }\n  return null;\n}\n\n/**\n * Get the resolution of the point in degrees or distance units.\n * For projections with degrees as the unit this will simply return the\n * provided resolution. For other projections the point resolution is\n * by default estimated by transforming the `point` pixel to EPSG:4326,\n * measuring its width and height on the normal sphere,\n * and taking the average of the width and height.\n * A custom function can be provided for a specific projection, either\n * by setting the `getPointResolution` option in the\n * {@link module:ol/proj/Projection~Projection} constructor or by using\n * {@link module:ol/proj/Projection~Projection#setGetPointResolution} to change an existing\n * projection object.\n * @param {ProjectionLike} projection The projection.\n * @param {number} resolution Nominal resolution in projection units.\n * @param {import(\"./coordinate.js\").Coordinate} point Point to find adjusted resolution at.\n * @param {import(\"./proj/Units.js\").Units} [units] Units to get the point resolution in.\n * Default is the projection's units.\n * @return {number} Point resolution.\n * @api\n */\nexport function getPointResolution(projection, resolution, point, units) {\n  projection = get(projection);\n  let pointResolution;\n  const getter = projection.getPointResolutionFunc();\n  if (getter) {\n    pointResolution = getter(resolution, point);\n    if (units && units !== projection.getUnits()) {\n      const metersPerUnit = projection.getMetersPerUnit();\n      if (metersPerUnit) {\n        pointResolution =\n          (pointResolution * metersPerUnit) / METERS_PER_UNIT[units];\n      }\n    }\n  } else {\n    const projUnits = projection.getUnits();\n    if ((projUnits == 'degrees' && !units) || units == 'degrees') {\n      pointResolution = resolution;\n    } else {\n      // Estimate point resolution by transforming the center pixel to EPSG:4326,\n      // measuring its width and height on the normal sphere, and taking the\n      // average of the width and height.\n      const toEPSG4326 = getTransformFromProjections(\n        projection,\n        get('EPSG:4326'),\n      );\n      if (!toEPSG4326 && projUnits !== 'degrees') {\n        // no transform is available\n        pointResolution = resolution * projection.getMetersPerUnit();\n      } else {\n        let vertices = [\n          point[0] - resolution / 2,\n          point[1],\n          point[0] + resolution / 2,\n          point[1],\n          point[0],\n          point[1] - resolution / 2,\n          point[0],\n          point[1] + resolution / 2,\n        ];\n        vertices = toEPSG4326(vertices, vertices, 2);\n        const width = getDistance(vertices.slice(0, 2), vertices.slice(2, 4));\n        const height = getDistance(vertices.slice(4, 6), vertices.slice(6, 8));\n        pointResolution = (width + height) / 2;\n      }\n      const metersPerUnit = units\n        ? METERS_PER_UNIT[units]\n        : projection.getMetersPerUnit();\n      if (metersPerUnit !== undefined) {\n        pointResolution /= metersPerUnit;\n      }\n    }\n  }\n  return pointResolution;\n}\n\n/**\n * Registers transformation functions that don't alter coordinates. Those allow\n * to transform between projections with equal meaning.\n *\n * @param {Array<Projection>} projections Projections.\n * @api\n */\nexport function addEquivalentProjections(projections) {\n  addProjections(projections);\n  projections.forEach(function (source) {\n    projections.forEach(function (destination) {\n      if (source !== destination) {\n        addTransformFunc(source, destination, cloneTransform);\n      }\n    });\n  });\n}\n\n/**\n * Registers transformation functions to convert coordinates in any projection\n * in projection1 to any projection in projection2.\n *\n * @param {Array<Projection>} projections1 Projections with equal\n *     meaning.\n * @param {Array<Projection>} projections2 Projections with equal\n *     meaning.\n * @param {TransformFunction} forwardTransform Transformation from any\n *   projection in projection1 to any projection in projection2.\n * @param {TransformFunction} inverseTransform Transform from any projection\n *   in projection2 to any projection in projection1..\n */\nexport function addEquivalentTransforms(\n  projections1,\n  projections2,\n  forwardTransform,\n  inverseTransform,\n) {\n  projections1.forEach(function (projection1) {\n    projections2.forEach(function (projection2) {\n      addTransformFunc(projection1, projection2, forwardTransform);\n      addTransformFunc(projection2, projection1, inverseTransform);\n    });\n  });\n}\n\n/**\n * Clear all cached projections and transforms.\n */\nexport function clearAllProjections() {\n  clearProj();\n  clearTransformFuncs();\n}\n\n/**\n * @param {Projection|string|undefined} projection Projection.\n * @param {string} defaultCode Default code.\n * @return {Projection} Projection.\n */\nexport function createProjection(projection, defaultCode) {\n  if (!projection) {\n    return get(defaultCode);\n  }\n  if (typeof projection === 'string') {\n    return get(projection);\n  }\n  return /** @type {Projection} */ (projection);\n}\n\n/**\n * Creates a {@link module:ol/proj~TransformFunction} from a simple 2D coordinate transform\n * function.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} coordTransform Coordinate\n *     transform.\n * @return {TransformFunction} Transform function.\n */\nexport function createTransformFromCoordinateTransform(coordTransform) {\n  return (\n    /**\n     * @param {Array<number>} input Input.\n     * @param {Array<number>} [output] Output.\n     * @param {number} [dimension] Dimensions that should be transformed.\n     * @param {number} [stride] Stride.\n     * @return {Array<number>} Output.\n     */\n    function (input, output, dimension, stride) {\n      const length = input.length;\n      dimension = dimension !== undefined ? dimension : 2;\n      stride = stride ?? dimension;\n      output = output !== undefined ? output : new Array(length);\n      for (let i = 0; i < length; i += stride) {\n        const point = coordTransform(input.slice(i, i + dimension));\n        const pointLength = point.length;\n        for (let j = 0, jj = stride; j < jj; ++j) {\n          output[i + j] = j >= pointLength ? input[i + j] : point[j];\n        }\n      }\n      return output;\n    }\n  );\n}\n\n/**\n * Registers coordinate transform functions to convert coordinates between the\n * source projection and the destination projection.\n * The forward and inverse functions convert coordinate pairs; this function\n * converts these into the functions used internally which also handle\n * extents and coordinate arrays.\n *\n * @param {ProjectionLike} source Source projection.\n * @param {ProjectionLike} destination Destination projection.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} forward The forward transform\n *     function (that is, from the source projection to the destination\n *     projection) that takes a {@link module:ol/coordinate~Coordinate} as argument and returns\n *     the transformed {@link module:ol/coordinate~Coordinate}.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} inverse The inverse transform\n *     function (that is, from the destination projection to the source\n *     projection) that takes a {@link module:ol/coordinate~Coordinate} as argument and returns\n *     the transformed {@link module:ol/coordinate~Coordinate}. If the transform function can only\n *     transform less dimensions than the input coordinate, it is supposeed to return a coordinate\n *     with only the length it can transform. The other dimensions will be taken unchanged from the\n *     source.\n * @api\n */\nexport function addCoordinateTransforms(source, destination, forward, inverse) {\n  const sourceProj = get(source);\n  const destProj = get(destination);\n  addTransformFunc(\n    sourceProj,\n    destProj,\n    createTransformFromCoordinateTransform(forward),\n  );\n  addTransformFunc(\n    destProj,\n    sourceProj,\n    createTransformFromCoordinateTransform(inverse),\n  );\n}\n\n/**\n * Transforms a coordinate from longitude/latitude to a different projection.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate as longitude and latitude, i.e.\n *     an array with longitude as 1st and latitude as 2nd element.\n * @param {ProjectionLike} [projection] Target projection. The\n *     default is Web Mercator, i.e. 'EPSG:3857'.\n * @return {import(\"./coordinate.js\").Coordinate} Coordinate projected to the target projection.\n * @api\n */\nexport function fromLonLat(coordinate, projection) {\n  disableCoordinateWarning();\n  return transform(\n    coordinate,\n    'EPSG:4326',\n    projection !== undefined ? projection : 'EPSG:3857',\n  );\n}\n\n/**\n * Transforms a coordinate to longitude/latitude.\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Projected coordinate.\n * @param {ProjectionLike} [projection] Projection of the coordinate.\n *     The default is Web Mercator, i.e. 'EPSG:3857'.\n * @return {import(\"./coordinate.js\").Coordinate} Coordinate as longitude and latitude, i.e. an array\n *     with longitude as 1st and latitude as 2nd element.\n * @api\n */\nexport function toLonLat(coordinate, projection) {\n  const lonLat = transform(\n    coordinate,\n    projection !== undefined ? projection : 'EPSG:3857',\n    'EPSG:4326',\n  );\n  const lon = lonLat[0];\n  if (lon < -180 || lon > 180) {\n    lonLat[0] = modulo(lon + 180, 360) - 180;\n  }\n  return lonLat;\n}\n\n/**\n * Checks if two projections are the same, that is every coordinate in one\n * projection does represent the same geographic point as the same coordinate in\n * the other projection.\n *\n * @param {Projection} projection1 Projection 1.\n * @param {Projection} projection2 Projection 2.\n * @return {boolean} Equivalent.\n * @api\n */\nexport function equivalent(projection1, projection2) {\n  if (projection1 === projection2) {\n    return true;\n  }\n  const equalUnits = projection1.getUnits() === projection2.getUnits();\n  if (projection1.getCode() === projection2.getCode()) {\n    return equalUnits;\n  }\n  const transformFunc = getTransformFromProjections(projection1, projection2);\n  return transformFunc === cloneTransform && equalUnits;\n}\n\n/**\n * Searches in the list of transform functions for the function for converting\n * coordinates from the source projection to the destination projection.\n *\n * @param {Projection} source Source Projection object.\n * @param {Projection} destination Destination Projection\n *     object.\n * @return {TransformFunction|null} Transform function.\n */\nexport function getTransformFromProjections(source, destination) {\n  const sourceCode = source.getCode();\n  const destinationCode = destination.getCode();\n  let transformFunc = getTransformFunc(sourceCode, destinationCode);\n  if (transformFunc) {\n    return transformFunc;\n  }\n\n  /**\n   * @type {Transforms|null}\n   */\n  let sourceTransforms = null;\n\n  /**\n   * @type {Transforms|null}\n   */\n  let destinationTransforms = null;\n\n  // lazily add projections if we have supported transforms\n  for (const makeTransforms of transformFactories) {\n    if (!sourceTransforms) {\n      sourceTransforms = makeTransforms(source);\n    }\n    if (!destinationTransforms) {\n      destinationTransforms = makeTransforms(destination);\n    }\n  }\n\n  if (!sourceTransforms && !destinationTransforms) {\n    return null;\n  }\n\n  const intermediateCode = 'EPSG:4326';\n  if (!destinationTransforms) {\n    const toDestination = getTransformFunc(intermediateCode, destinationCode);\n    if (toDestination) {\n      transformFunc = composeTransformFuncs(\n        sourceTransforms.inverse,\n        toDestination,\n      );\n    }\n  } else if (!sourceTransforms) {\n    const fromSource = getTransformFunc(sourceCode, intermediateCode);\n    if (fromSource) {\n      transformFunc = composeTransformFuncs(\n        fromSource,\n        destinationTransforms.forward,\n      );\n    }\n  } else {\n    transformFunc = composeTransformFuncs(\n      sourceTransforms.inverse,\n      destinationTransforms.forward,\n    );\n  }\n\n  if (transformFunc) {\n    addProjection(source);\n    addProjection(destination);\n    addTransformFunc(source, destination, transformFunc);\n  }\n\n  return transformFunc;\n}\n\n/**\n * @param {TransformFunction} t1 The first transform function.\n * @param {TransformFunction} t2 The second transform function.\n * @return {TransformFunction} The composed transform function.\n */\nfunction composeTransformFuncs(t1, t2) {\n  return function (input, output, dimensions, stride) {\n    output = t1(input, output, dimensions, stride);\n    return t2(output, output, dimensions, stride);\n  };\n}\n\n/**\n * Given the projection-like objects, searches for a transformation\n * function to convert a coordinates array from the source projection to the\n * destination projection.\n *\n * @param {ProjectionLike} source Source.\n * @param {ProjectionLike} destination Destination.\n * @return {TransformFunction} Transform function.\n * @api\n */\nexport function getTransform(source, destination) {\n  const sourceProjection = get(source);\n  const destinationProjection = get(destination);\n  return getTransformFromProjections(sourceProjection, destinationProjection);\n}\n\n/**\n * Transforms a coordinate from source projection to destination projection.\n * This returns a new coordinate (and does not modify the original). If there\n * is no available transform between the two projection, the function will throw\n * an error.\n *\n * See {@link module:ol/proj.transformExtent} for extent transformation.\n * See the transform method of {@link module:ol/geom/Geometry~Geometry} and its\n * subclasses for geometry transforms.\n *\n * @param {import(\"./coordinate.js\").Coordinate} coordinate Coordinate.\n * @param {ProjectionLike} source Source projection-like.\n * @param {ProjectionLike} destination Destination projection-like.\n * @return {import(\"./coordinate.js\").Coordinate} Coordinate.\n * @api\n */\nexport function transform(coordinate, source, destination) {\n  const transformFunc = getTransform(source, destination);\n  if (!transformFunc) {\n    const sourceCode = get(source).getCode();\n    const destinationCode = get(destination).getCode();\n    throw new Error(\n      `No transform available between ${sourceCode} and ${destinationCode}`,\n    );\n  }\n  return transformFunc(coordinate, undefined, coordinate.length);\n}\n\n/**\n * Transforms an extent from source projection to destination projection.  This\n * returns a new extent (and does not modify the original).\n *\n * @param {import(\"./extent.js\").Extent} extent The extent to transform.\n * @param {ProjectionLike} source Source projection-like.\n * @param {ProjectionLike} destination Destination projection-like.\n * @param {number} [stops] Number of stops per side used for the transform.\n * By default only the corners are used.\n * @return {import(\"./extent.js\").Extent} The transformed extent.\n * @api\n */\nexport function transformExtent(extent, source, destination, stops) {\n  const transformFunc = getTransform(source, destination);\n  return applyTransform(extent, transformFunc, undefined, stops);\n}\n\n/**\n * Transforms the given point to the destination projection.\n *\n * @param {import(\"./coordinate.js\").Coordinate} point Point.\n * @param {Projection} sourceProjection Source projection.\n * @param {Projection} destinationProjection Destination projection.\n * @return {import(\"./coordinate.js\").Coordinate} Point.\n */\nexport function transformWithProjections(\n  point,\n  sourceProjection,\n  destinationProjection,\n) {\n  const transformFunc = getTransformFromProjections(\n    sourceProjection,\n    destinationProjection,\n  );\n  return transformFunc(point);\n}\n\n/**\n * @type {Projection|null}\n */\nlet userProjection = null;\n\n/**\n * Set the projection for coordinates supplied from and returned by API methods.\n * This includes all API methods except for those interacting with tile grids,\n * plus {@link import(\"./Map.js\").FrameState} and {@link import(\"./View.js\").State}.\n * @param {ProjectionLike} projection The user projection.\n * @api\n */\nexport function setUserProjection(projection) {\n  userProjection = get(projection);\n}\n\n/**\n * Clear the user projection if set.\n * @api\n */\nexport function clearUserProjection() {\n  userProjection = null;\n}\n\n/**\n * Get the projection for coordinates supplied from and returned by API methods.\n * @return {Projection|null} The user projection (or null if not set).\n * @api\n */\nexport function getUserProjection() {\n  return userProjection;\n}\n\n/**\n * Use geographic coordinates (WGS-84 datum) in API methods.\n * This includes all API methods except for those interacting with tile grids,\n * plus {@link import(\"./Map.js\").FrameState} and {@link import(\"./View.js\").State}.\n * @api\n */\nexport function useGeographic() {\n  setUserProjection('EPSG:4326');\n}\n\n/**\n * Return a coordinate transformed into the user projection.  If no user projection\n * is set, the original coordinate is returned.\n * @param {Array<number>} coordinate Input coordinate.\n * @param {ProjectionLike} sourceProjection The input coordinate projection.\n * @return {Array<number>} The input coordinate in the user projection.\n */\nexport function toUserCoordinate(coordinate, sourceProjection) {\n  if (!userProjection) {\n    return coordinate;\n  }\n  return transform(coordinate, sourceProjection, userProjection);\n}\n\n/**\n * Return a coordinate transformed from the user projection.  If no user projection\n * is set, the original coordinate is returned.\n * @param {Array<number>} coordinate Input coordinate.\n * @param {ProjectionLike} destProjection The destination projection.\n * @return {Array<number>} The input coordinate transformed.\n */\nexport function fromUserCoordinate(coordinate, destProjection) {\n  if (!userProjection) {\n    if (\n      showCoordinateWarning &&\n      !equals(coordinate, [0, 0]) &&\n      coordinate[0] >= -180 &&\n      coordinate[0] <= 180 &&\n      coordinate[1] >= -90 &&\n      coordinate[1] <= 90\n    ) {\n      showCoordinateWarning = false;\n      warn(\n        'Call useGeographic() from ol/proj once to work with [longitude, latitude] coordinates.',\n      );\n    }\n    return coordinate;\n  }\n  return transform(coordinate, userProjection, destProjection);\n}\n\n/**\n * Return an extent transformed into the user projection.  If no user projection\n * is set, the original extent is returned.\n * @param {import(\"./extent.js\").Extent} extent Input extent.\n * @param {ProjectionLike} sourceProjection The input extent projection.\n * @return {import(\"./extent.js\").Extent} The input extent in the user projection.\n */\nexport function toUserExtent(extent, sourceProjection) {\n  if (!userProjection) {\n    return extent;\n  }\n  return transformExtent(extent, sourceProjection, userProjection);\n}\n\n/**\n * Return an extent transformed from the user projection.  If no user projection\n * is set, the original extent is returned.\n * @param {import(\"./extent.js\").Extent} extent Input extent.\n * @param {ProjectionLike} destProjection The destination projection.\n * @return {import(\"./extent.js\").Extent} The input extent transformed.\n */\nexport function fromUserExtent(extent, destProjection) {\n  if (!userProjection) {\n    return extent;\n  }\n  return transformExtent(extent, userProjection, destProjection);\n}\n\n/**\n * Return the resolution in user projection units per pixel. If no user projection\n * is set, or source or user projection are missing units, the original resolution\n * is returned.\n * @param {number} resolution Resolution in input projection units per pixel.\n * @param {ProjectionLike} sourceProjection The input projection.\n * @return {number} Resolution in user projection units per pixel.\n */\nexport function toUserResolution(resolution, sourceProjection) {\n  if (!userProjection) {\n    return resolution;\n  }\n  const sourceMetersPerUnit = get(sourceProjection).getMetersPerUnit();\n  const userMetersPerUnit = userProjection.getMetersPerUnit();\n  return sourceMetersPerUnit && userMetersPerUnit\n    ? (resolution * sourceMetersPerUnit) / userMetersPerUnit\n    : resolution;\n}\n\n/**\n * Return the resolution in user projection units per pixel. If no user projection\n * is set, or source or user projection are missing units, the original resolution\n * is returned.\n * @param {number} resolution Resolution in user projection units per pixel.\n * @param {ProjectionLike} destProjection The destination projection.\n * @return {number} Resolution in destination projection units per pixel.\n */\nexport function fromUserResolution(resolution, destProjection) {\n  if (!userProjection) {\n    return resolution;\n  }\n  const destMetersPerUnit = get(destProjection).getMetersPerUnit();\n  const userMetersPerUnit = userProjection.getMetersPerUnit();\n  return destMetersPerUnit && userMetersPerUnit\n    ? (resolution * userMetersPerUnit) / destMetersPerUnit\n    : resolution;\n}\n\n/**\n * Creates a safe coordinate transform function from a coordinate transform function.\n * \"Safe\" means that it can handle wrapping of x-coordinates for global projections,\n * and that coordinates exceeding the source projection validity extent's range will be\n * clamped to the validity range.\n * @param {Projection} sourceProj Source projection.\n * @param {Projection} destProj Destination projection.\n * @param {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} transform Transform function (source to destination).\n * @return {function(import(\"./coordinate.js\").Coordinate): import(\"./coordinate.js\").Coordinate} Safe transform function (source to destination).\n */\nexport function createSafeCoordinateTransform(sourceProj, destProj, transform) {\n  return function (coord) {\n    let transformed, worldsAway;\n    if (sourceProj.canWrapX()) {\n      const sourceExtent = sourceProj.getExtent();\n      const sourceExtentWidth = getWidth(sourceExtent);\n      coord = coord.slice(0);\n      worldsAway = getWorldsAway(coord, sourceProj, sourceExtentWidth);\n      if (worldsAway) {\n        // Move x to the real world\n        coord[0] = coord[0] - worldsAway * sourceExtentWidth;\n      }\n      coord[0] = clamp(coord[0], sourceExtent[0], sourceExtent[2]);\n      coord[1] = clamp(coord[1], sourceExtent[1], sourceExtent[3]);\n      transformed = transform(coord);\n    } else {\n      transformed = transform(coord);\n    }\n    if (worldsAway && destProj.canWrapX()) {\n      // Move transformed coordinate back to the offset world\n      transformed[0] += worldsAway * getWidth(destProj.getExtent());\n    }\n    return transformed;\n  };\n}\n\n/**\n * Add transforms to and from EPSG:4326 and EPSG:3857.  This function is called\n * by when this module is executed and should only need to be called again after\n * `clearAllProjections()` is called (e.g. in tests).\n */\nexport function addCommon() {\n  // Add transformations that don't alter coordinates to convert within set of\n  // projections with equal meaning.\n  addEquivalentProjections(EPSG3857_PROJECTIONS);\n  addEquivalentProjections(EPSG4326_PROJECTIONS);\n  // Add transformations to convert EPSG:4326 like coordinates to EPSG:3857 like\n  // coordinates and back.\n  addEquivalentTransforms(\n    EPSG4326_PROJECTIONS,\n    EPSG3857_PROJECTIONS,\n    fromEPSG4326,\n    toEPSG4326,\n  );\n}\n\naddCommon();\n"], "mappings": ";AAWA,IAAM,SAAS;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AACR;AAKA,IAAI,QAAQ,OAAO;AA0BZ,SAAS,QAAQ,MAAM;AAC5B,MAAI,QAAQ,OAAO,MAAM;AACvB;AAAA,EACF;AACA,UAAQ,KAAK,GAAG,IAAI;AACtB;AAKO,SAAS,SAAS,MAAM;AAC7B,MAAI,QAAQ,OAAO,OAAO;AACxB;AAAA,EACF;AACA,UAAQ,MAAM,GAAG,IAAI;AACvB;;;ACtDA,IAAO,uBAAQ;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AACR;;;ACQO,SAAS,eAAe,aAAa;AAC1C,QAAM,SAAS,YAAY;AAC3B,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,qBAAiB,QAAQ,YAAY,CAAC,CAAC;AAAA,EACzC;AACA,SAAO;AACT;AASA,SAAS,mBAAmB,IAAI,IAAI,MAAM;AACxC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE;AACpC,SAAO,eAAe,MAAM,MAAM,MAAM,MAAM,IAAI;AACpD;AAUO,SAAS,OAAO,QAAQ,OAAO,MAAM;AAC1C,MAAI,MAAM;AACR,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,SAAK,CAAC,IAAI,OAAO,CAAC,IAAI;AACtB,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,OAAO,CAAC,IAAI;AAAA,IACZ,OAAO,CAAC,IAAI;AAAA,IACZ,OAAO,CAAC,IAAI;AAAA,IACZ,OAAO,CAAC,IAAI;AAAA,EACd;AACF;AASO,SAAS,MAAM,QAAQ,MAAM;AAClC,MAAI,MAAM;AACR,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,WAAO;AAAA,EACT;AACA,SAAO,OAAO,MAAM;AACtB;AAQO,SAAS,yBAAyB,QAAQ,GAAG,GAAG;AACrD,MAAI,IAAI;AACR,MAAI,IAAI,OAAO,CAAC,GAAG;AACjB,SAAK,OAAO,CAAC,IAAI;AAAA,EACnB,WAAW,OAAO,CAAC,IAAI,GAAG;AACxB,SAAK,IAAI,OAAO,CAAC;AAAA,EACnB,OAAO;AACL,SAAK;AAAA,EACP;AACA,MAAI,IAAI,OAAO,CAAC,GAAG;AACjB,SAAK,OAAO,CAAC,IAAI;AAAA,EACnB,WAAW,OAAO,CAAC,IAAI,GAAG;AACxB,SAAK,IAAI,OAAO,CAAC;AAAA,EACnB,OAAO;AACL,SAAK;AAAA,EACP;AACA,SAAO,KAAK,KAAK,KAAK;AACxB;AAUO,SAAS,mBAAmB,QAAQ,YAAY;AACrD,SAAO,WAAW,QAAQ,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AACxD;AAcO,SAAS,eAAe,SAAS,SAAS;AAC/C,SACE,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAE3B;AAWO,SAAS,WAAW,QAAQ,GAAG,GAAG;AACvC,SAAO,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAC5E;AASO,SAAS,uBAAuB,QAAQ,YAAY;AACzD,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,MAAI,eAAe,qBAAa;AAChC,MAAI,IAAI,MAAM;AACZ,mBAAe,eAAe,qBAAa;AAAA,EAC7C,WAAW,IAAI,MAAM;AACnB,mBAAe,eAAe,qBAAa;AAAA,EAC7C;AACA,MAAI,IAAI,MAAM;AACZ,mBAAe,eAAe,qBAAa;AAAA,EAC7C,WAAW,IAAI,MAAM;AACnB,mBAAe,eAAe,qBAAa;AAAA,EAC7C;AACA,MAAI,iBAAiB,qBAAa,SAAS;AACzC,mBAAe,qBAAa;AAAA,EAC9B;AACA,SAAO;AACT;AAOO,SAAS,cAAc;AAC5B,SAAO,CAAC,UAAU,UAAU,WAAW,SAAS;AAClD;AAWO,SAAS,eAAe,MAAM,MAAM,MAAM,MAAM,MAAM;AAC3D,MAAI,MAAM;AACR,SAAK,CAAC,IAAI;AACV,SAAK,CAAC,IAAI;AACV,SAAK,CAAC,IAAI;AACV,SAAK,CAAC,IAAI;AACV,WAAO;AAAA,EACT;AACA,SAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAChC;AAOO,SAAS,oBAAoB,MAAM;AACxC,SAAO,eAAe,UAAU,UAAU,WAAW,WAAW,IAAI;AACtE;AAOO,SAAS,6BAA6B,YAAY,MAAM;AAC7D,QAAM,IAAI,WAAW,CAAC;AACtB,QAAM,IAAI,WAAW,CAAC;AACtB,SAAO,eAAe,GAAG,GAAG,GAAG,GAAG,IAAI;AACxC;AAoBO,SAAS,kCACd,iBACA,QACA,KACA,QACA,MACA;AACA,QAAM,SAAS,oBAAoB,IAAI;AACvC,SAAO,sBAAsB,QAAQ,iBAAiB,QAAQ,KAAK,MAAM;AAC3E;AAmBO,SAAS,OAAO,SAAS,SAAS;AACvC,SACE,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAE3B;AASO,SAAS,oBAAoB,SAAS,SAAS,WAAW;AAC/D,SACE,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,aACpC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,aACpC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,aACpC,KAAK,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI;AAExC;AASO,SAAS,OAAO,SAAS,SAAS;AACvC,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,MAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,YAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AAMO,SAAS,iBAAiB,QAAQ,YAAY;AACnD,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACA,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACA,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACA,MAAI,WAAW,CAAC,IAAI,OAAO,CAAC,GAAG;AAC7B,WAAO,CAAC,IAAI,WAAW,CAAC;AAAA,EAC1B;AACF;AAsBO,SAAS,sBACd,QACA,iBACA,QACA,KACA,QACA;AACA,SAAO,SAAS,KAAK,UAAU,QAAQ;AACrC,aAAS,QAAQ,gBAAgB,MAAM,GAAG,gBAAgB,SAAS,CAAC,CAAC;AAAA,EACvE;AACA,SAAO;AACT;AAmBO,SAAS,SAAS,QAAQ,GAAG,GAAG;AACrC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACjC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACjC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACjC,SAAO,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;AACnC;AAWO,SAAS,cAAc,QAAQ,UAAU;AAC9C,MAAI;AACJ,QAAM,SAAS,cAAc,MAAM,CAAC;AACpC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,QAAM,SAAS,eAAe,MAAM,CAAC;AACrC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,QAAM,SAAS,YAAY,MAAM,CAAC;AAClC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,QAAM,SAAS,WAAW,MAAM,CAAC;AACjC,MAAI,KAAK;AACP,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQO,SAAS,QAAQ,QAAQ;AAC9B,MAAI,OAAO;AACX,MAAI,CAAC,QAAQ,MAAM,GAAG;AACpB,WAAO,SAAS,MAAM,IAAI,UAAU,MAAM;AAAA,EAC5C;AACA,SAAO;AACT;AAQO,SAAS,cAAc,QAAQ;AACpC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,eAAe,QAAQ;AACrC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,UAAU,QAAQ;AAChC,SAAO,EAAE,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;AAClE;AAQO,SAAS,UAAU,QAAQ,QAAQ;AACxC,MAAI;AACJ,MAAI,WAAW,eAAe;AAC5B,iBAAa,cAAc,MAAM;AAAA,EACnC,WAAW,WAAW,gBAAgB;AACpC,iBAAa,eAAe,MAAM;AAAA,EACpC,WAAW,WAAW,YAAY;AAChC,iBAAa,WAAW,MAAM;AAAA,EAChC,WAAW,WAAW,aAAa;AACjC,iBAAa,YAAY,MAAM;AAAA,EACjC,OAAO;AACL,UAAM,IAAI,MAAM,gBAAgB;AAAA,EAClC;AACA,SAAO;AACT;AAuBO,SAAS,kBAAkB,QAAQ,YAAY,UAAU,MAAM,MAAM;AAC1E,QAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,SAAO;AAAA,IACL,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACvB;AAAA,EACF;AACF;AASO,SAAS,mBAAmB,QAAQ,YAAY,UAAU,MAAM;AACrE,QAAM,KAAM,aAAa,KAAK,CAAC,IAAK;AACpC,QAAM,KAAM,aAAa,KAAK,CAAC,IAAK;AACpC,QAAM,cAAc,KAAK,IAAI,QAAQ;AACrC,QAAM,cAAc,KAAK,IAAI,QAAQ;AACrC,QAAM,OAAO,KAAK;AAClB,QAAM,OAAO,KAAK;AAClB,QAAM,OAAO,KAAK;AAClB,QAAM,OAAO,KAAK;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,QAAM,IAAI,OAAO,CAAC;AAClB,SAAO;AAAA,IACL,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,IACX,IAAI,OAAO;AAAA,EACb;AACF;AAQO,SAAS,UAAU,QAAQ;AAChC,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC7B;AAoBO,SAAS,gBAAgB,SAAS,SAAS,MAAM;AACtD,QAAM,eAAe,OAAO,OAAO,YAAY;AAC/C,MAAI,WAAW,SAAS,OAAO,GAAG;AAChC,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AACA,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AACA,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AACA,QAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AAC3B,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B,OAAO;AACL,mBAAa,CAAC,IAAI,QAAQ,CAAC;AAAA,IAC7B;AAAA,EACF,OAAO;AACL,wBAAoB,YAAY;AAAA,EAClC;AACA,SAAO;AACT;AA0BO,SAAS,WAAW,QAAQ;AACjC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,YAAY,QAAQ;AAClC,SAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC9B;AAQO,SAAS,SAAS,QAAQ;AAC/B,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC;AAC7B;AASO,SAAS,WAAW,SAAS,SAAS;AAC3C,SACE,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC,KACvB,QAAQ,CAAC,KAAK,QAAQ,CAAC;AAE3B;AAQO,SAAS,QAAQ,QAAQ;AAC9B,SAAO,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC;AACtD;AAOO,SAAS,eAAe,QAAQ,MAAM;AAC3C,MAAI,MAAM;AACR,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,SAAK,CAAC,IAAI,OAAO,CAAC;AAClB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMO,SAAS,gBAAgB,QAAQ,OAAO;AAC7C,QAAM,UAAW,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,KAAM,QAAQ;AACxD,QAAM,UAAW,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,KAAM,QAAQ;AACxD,SAAO,CAAC,KAAK;AACb,SAAO,CAAC,KAAK;AACb,SAAO,CAAC,KAAK;AACb,SAAO,CAAC,KAAK;AACf;AAUO,SAAS,kBAAkB,QAAQ,OAAO,KAAK;AACpD,MAAIA,cAAa;AACjB,QAAM,WAAW,uBAAuB,QAAQ,KAAK;AACrD,QAAM,SAAS,uBAAuB,QAAQ,GAAG;AACjD,MACE,aAAa,qBAAa,gBAC1B,WAAW,qBAAa,cACxB;AACA,IAAAA,cAAa;AAAA,EACf,OAAO;AACL,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,SAAS,MAAM,CAAC;AACtB,UAAM,SAAS,MAAM,CAAC;AACtB,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,SAAS,OAAO,WAAW,OAAO;AACxC,QAAI,GAAG;AACP,QAAI,CAAC,EAAE,SAAS,qBAAa,UAAU,EAAE,WAAW,qBAAa,QAAQ;AAEvE,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,QACE,CAACA,eACD,CAAC,EAAE,SAAS,qBAAa,UACzB,EAAE,WAAW,qBAAa,QAC1B;AAEA,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,QACE,CAACA,eACD,CAAC,EAAE,SAAS,qBAAa,UACzB,EAAE,WAAW,qBAAa,QAC1B;AAEA,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,QACE,CAACA,eACD,CAAC,EAAE,SAAS,qBAAa,SACzB,EAAE,WAAW,qBAAa,OAC1B;AAEA,UAAI,QAAQ,OAAO,QAAQ;AAC3B,MAAAA,cAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AAAA,EACF;AACA,SAAOA;AACT;AAaO,SAAS,eAAe,QAAQ,aAAa,MAAM,OAAO;AAC/D,MAAI,QAAQ,MAAM,GAAG;AACnB,WAAO,oBAAoB,IAAI;AAAA,EACjC;AACA,MAAI,cAAc,CAAC;AACnB,MAAI,QAAQ,GAAG;AACb,UAAM,QAAQ,OAAO,CAAC,IAAI,OAAO,CAAC;AAClC,UAAM,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC;AACnC,aAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,kBAAY;AAAA,QACV,OAAO,CAAC,IAAK,QAAQ,IAAK;AAAA,QAC1B,OAAO,CAAC;AAAA,QACR,OAAO,CAAC;AAAA,QACR,OAAO,CAAC,IAAK,SAAS,IAAK;AAAA,QAC3B,OAAO,CAAC,IAAK,QAAQ,IAAK;AAAA,QAC1B,OAAO,CAAC;AAAA,QACR,OAAO,CAAC;AAAA,QACR,OAAO,CAAC,IAAK,SAAS,IAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,OAAO;AACL,kBAAc;AAAA,MACZ,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,IACV;AAAA,EACF;AACA,cAAY,aAAa,aAAa,CAAC;AACvC,QAAM,KAAK,CAAC;AACZ,QAAM,KAAK,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK,GAAG;AACrD,OAAG,KAAK,YAAY,CAAC,CAAC;AACtB,OAAG,KAAK,YAAY,IAAI,CAAC,CAAC;AAAA,EAC5B;AACA,SAAO,mBAAmB,IAAI,IAAI,IAAI;AACxC;AAUO,SAAS,MAAM,QAAQ,YAAY;AACxC,QAAM,mBAAmB,WAAW,UAAU;AAC9C,QAAM,SAAS,UAAU,MAAM;AAC/B,MACE,WAAW,SAAS,MACnB,OAAO,CAAC,IAAI,iBAAiB,CAAC,KAAK,OAAO,CAAC,KAAK,iBAAiB,CAAC,IACnE;AACA,UAAM,aAAa,SAAS,gBAAgB;AAC5C,UAAM,aAAa,KAAK;AAAA,OACrB,OAAO,CAAC,IAAI,iBAAiB,CAAC,KAAK;AAAA,IACtC;AACA,UAAM,SAAS,aAAa;AAC5B,WAAO,CAAC,KAAK;AACb,WAAO,CAAC,KAAK;AAAA,EACf;AACA,SAAO;AACT;AAeO,SAAS,cAAc,QAAQ,YAAY,YAAY;AAC5D,MAAI,WAAW,SAAS,GAAG;AACzB,UAAM,mBAAmB,WAAW,UAAU;AAE9C,QAAI,CAAC,SAAS,OAAO,CAAC,CAAC,KAAK,CAAC,SAAS,OAAO,CAAC,CAAC,GAAG;AAChD,aAAO,CAAC,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AAAA,IAC1E;AAEA,UAAM,QAAQ,UAAU;AACxB,UAAM,aAAa,SAAS,gBAAgB;AAE5C,QAAI,SAAS,MAAM,IAAI,cAAc,CAAC,YAAY;AAEhD,aAAO,CAAC,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;AAAA,IAC1E;AACA,QAAI,OAAO,CAAC,IAAI,iBAAiB,CAAC,GAAG;AAEnC,aAAO;AAAA,QACL,CAAC,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QAClE,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MACvD;AAAA,IACF;AACA,QAAI,OAAO,CAAC,IAAI,iBAAiB,CAAC,GAAG;AAEnC,aAAO;AAAA,QACL,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QACrD,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,CAAC;AAAA,MACpE;AAAA,IACF;AAAA,EACF;AAEA,SAAO,CAAC,MAAM;AAChB;;;AC94BO,SAAS,MAAM,OAAO,KAAK,KAAK;AACrC,SAAO,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,GAAG,GAAG;AAC3C;AAaO,SAAS,uBAAuB,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AAC3D,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,MAAI,OAAO,KAAK,OAAO,GAAG;AACxB,UAAM,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK;AAC5D,QAAI,IAAI,GAAG;AACT,WAAK;AACL,WAAK;AAAA,IACP,WAAW,IAAI,GAAG;AAChB,YAAM,KAAK;AACX,YAAM,KAAK;AAAA,IACb;AAAA,EACF;AACA,SAAO,gBAAgB,GAAG,GAAG,IAAI,EAAE;AACrC;AAUO,SAAS,gBAAgB,IAAI,IAAI,IAAI,IAAI;AAC9C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,SAAO,KAAK,KAAK,KAAK;AACxB;AASO,SAAS,kBAAkB,KAAK;AACrC,QAAM,IAAI,IAAI;AAEd,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE1B,QAAI,SAAS;AACb,QAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B,aAAS,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC9B,YAAM,WAAW,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACnC,UAAI,WAAW,OAAO;AACpB,gBAAQ;AACR,iBAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,UAAU,GAAG;AACf,aAAO;AAAA,IACT;AAGA,UAAM,MAAM,IAAI,MAAM;AACtB,QAAI,MAAM,IAAI,IAAI,CAAC;AACnB,QAAI,CAAC,IAAI;AAGT,aAAS,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAC9B,YAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;AAClC,eAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC9B,YAAI,KAAK,GAAG;AACV,cAAI,CAAC,EAAE,CAAC,IAAI;AAAA,QACd,OAAO;AACL,cAAI,CAAC,EAAE,CAAC,KAAK,OAAO,IAAI,CAAC,EAAE,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,QAAM,IAAI,IAAI,MAAM,CAAC;AACrB,WAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC/B,MAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;AAC3B,aAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC/B,UAAI,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC9B;AAAA,EACF;AACA,SAAO;AACT;AAQO,SAAS,UAAU,gBAAgB;AACxC,SAAQ,iBAAiB,MAAO,KAAK;AACvC;AAQO,SAAS,UAAU,gBAAgB;AACxC,SAAQ,iBAAiB,KAAK,KAAM;AACtC;AASO,SAAS,OAAO,GAAG,GAAG;AAC3B,QAAM,IAAI,IAAI;AACd,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B;AAUO,SAAS,KAAK,GAAG,GAAG,GAAG;AAC5B,SAAO,IAAI,KAAK,IAAI;AACtB;AAQO,SAAS,QAAQ,GAAG,UAAU;AACnC,QAAM,SAAS,KAAK,IAAI,IAAI,QAAQ;AACpC,SAAO,KAAK,MAAM,IAAI,MAAM,IAAI;AAClC;AASO,SAAS,MAAM,GAAG,UAAU;AACjC,SAAO,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC;AACxC;AASO,SAAS,MAAM,GAAG,UAAU;AACjC,SAAO,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC;AACxC;AASO,SAAS,KAAK,GAAG,UAAU;AAChC,SAAO,KAAK,KAAK,QAAQ,GAAG,QAAQ,CAAC;AACvC;AASO,SAAS,KAAK,GAAG,KAAK,KAAK;AAChC,MAAI,KAAK,OAAO,IAAI,KAAK;AACvB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM;AACpB,WAAW,IAAI,OAAO,QAAS,SAAS,QAAS;AACnD;;;AC3MO,SAAS,UAAU,QAAQ,OAAO,WAAW;AAClD,QAAM,eACJ,cAAc,SAAY,OAAO,QAAQ,SAAS,IAAI,KAAK;AAC7D,MAAI,UAAU,aAAa,QAAQ,GAAG;AACtC,YAAU,YAAY,KAAK,aAAa,SAAS;AACjD,SAAO,UAAU,QACb,eACA,IAAI,MAAM,IAAI,QAAQ,OAAO,EAAE,KAAK,GAAG,IAAI;AACjD;AAQO,SAAS,gBAAgB,IAAI,IAAI;AACtC,QAAM,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,QAAM,MAAM,KAAK,IAAI,MAAM,GAAG;AAE9B,WAAS,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK;AACvD,UAAM,KAAK,SAAS,GAAG,CAAC,KAAK,KAAK,EAAE;AACpC,UAAM,KAAK,SAAS,GAAG,CAAC,KAAK,KAAK,EAAE;AAEpC,QAAI,KAAK,IAAI;AACX,aAAO;AAAA,IACT;AACA,QAAI,KAAK,IAAI;AACX,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACHO,SAAS,IAAI,YAAY,OAAO;AACrC,aAAW,CAAC,KAAK,CAAC,MAAM,CAAC;AACzB,aAAW,CAAC,KAAK,CAAC,MAAM,CAAC;AACzB,SAAO;AACT;AASO,SAAS,gBAAgB,YAAY,QAAQ;AAClD,QAAM,IAAI,OAAO,UAAU;AAC3B,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,KAAK,OAAO,CAAC;AACnB,QAAM,KAAK,OAAO,CAAC;AACnB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AAEvB,MAAI,KAAK,KAAK;AACd,QAAM,KAAK,KAAK;AAChB,MAAI,OAAO,KAAK,OAAO,GAAG;AACxB,SAAK;AAAA,EACP;AACA,QAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAErC,QAAM,IAAI,KAAM,IAAI,KAAM;AAC1B,QAAM,IAAI,KAAM,IAAI,KAAM;AAE1B,SAAO,CAAC,GAAG,CAAC;AACd;AAcO,SAAS,iBAAiB,YAAY,SAAS;AACpD,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,KAAK,WAAW,CAAC;AACvB,QAAM,QAAQ,QAAQ,CAAC;AACvB,QAAM,MAAM,QAAQ,CAAC;AACrB,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,KAAK,MAAM,CAAC;AAClB,QAAM,KAAK,IAAI,CAAC;AAChB,QAAM,KAAK,IAAI,CAAC;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,QACJ,OAAO,KAAK,OAAO,IACf,KACC,MAAM,KAAK,MAAM,MAAM,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM;AAChE,MAAI,GAAG;AACP,MAAI,SAAS,GAAG;AACd,QAAI;AACJ,QAAI;AAAA,EACN,WAAW,SAAS,GAAG;AACrB,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAI,KAAK,QAAQ;AACjB,QAAI,KAAK,QAAQ;AAAA,EACnB;AACA,SAAO,CAAC,GAAG,CAAC;AACd;AAiDO,SAAS,oBAAoB,aAAa,SAAS,gBAAgB;AACxE,QAAM,oBAAoB,OAAO,UAAU,KAAK,GAAG,IAAI;AACvD,QAAM,IAAI,KAAK,IAAI,OAAO,iBAAiB;AAC3C,QAAM,WAAW,kBAAkB;AAEnC,MAAI,MAAM,KAAK,MAAM,IAAI,IAAI;AAC7B,MAAI,MAAM,KAAK,OAAO,IAAI,MAAM,QAAQ,EAAE;AAC1C,MAAI,MAAM,QAAQ,IAAI,MAAM,OAAO,MAAM,IAAI,QAAQ;AAErD,MAAI,OAAO,IAAI;AACb,UAAM;AACN,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,IAAI;AACb,UAAM;AACN,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM;AACjB,MAAI,QAAQ,KAAK,QAAQ,GAAG;AAC1B,YAAQ,MAAM,UAAU,KAAK,CAAC,IAAI;AAAA,EACpC;AACA,MAAI,QAAQ,GAAG;AACb,YAAQ,MAAM,UAAU,KAAK,GAAG,QAAQ,IAAI;AAAA,EAC9C;AACA,MAAI,sBAAsB,GAAG;AAC3B,YAAQ,MAAM,YAAY,OAAO,oBAAoB,IAAI,IAAI,CAAC;AAAA,EAChE;AAEA,SAAO;AACT;AA+CO,SAASC,QAAO,aAAa,aAAa;AAC/C,MAAIA,UAAS;AACb,WAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAChD,QAAI,YAAY,CAAC,KAAK,YAAY,CAAC,GAAG;AACpC,MAAAA,UAAS;AACT;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;AAoBO,SAAS,OAAO,YAAY,OAAO;AACxC,QAAM,WAAW,KAAK,IAAI,KAAK;AAC/B,QAAM,WAAW,KAAK,IAAI,KAAK;AAC/B,QAAM,IAAI,WAAW,CAAC,IAAI,WAAW,WAAW,CAAC,IAAI;AACrD,QAAM,IAAI,WAAW,CAAC,IAAI,WAAW,WAAW,CAAC,IAAI;AACrD,aAAW,CAAC,IAAI;AAChB,aAAW,CAAC,IAAI;AAChB,SAAO;AACT;AAmBO,SAAS,MAAM,YAAYC,QAAO;AACvC,aAAW,CAAC,KAAKA;AACjB,aAAW,CAAC,KAAKA;AACjB,SAAO;AACT;AAOO,SAASC,iBAAgB,QAAQ,QAAQ;AAC9C,QAAM,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,QAAM,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC;AAC/B,SAAO,KAAK,KAAK,KAAK;AACxB;AAOO,SAAS,SAAS,QAAQ,QAAQ;AACvC,SAAO,KAAK,KAAKA,iBAAgB,QAAQ,MAAM,CAAC;AAClD;AAUO,SAAS,yBAAyB,YAAY,SAAS;AAC5D,SAAOA,iBAAgB,YAAY,iBAAiB,YAAY,OAAO,CAAC;AAC1E;AA6EO,SAASC,OAAM,YAAY,YAAY;AAC5C,MAAI,WAAW,SAAS,GAAG;AACzB,UAAM,aAAa,SAAS,WAAW,UAAU,CAAC;AAClD,UAAM,aAAa,cAAc,YAAY,YAAY,UAAU;AACnE,QAAI,YAAY;AACd,iBAAW,CAAC,KAAK,aAAa;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AACT;AAOO,SAAS,cAAc,YAAY,YAAY,mBAAmB;AACvE,QAAM,mBAAmB,WAAW,UAAU;AAC9C,MAAI,aAAa;AACjB,MACE,WAAW,SAAS,MACnB,WAAW,CAAC,IAAI,iBAAiB,CAAC,KAAK,WAAW,CAAC,IAAI,iBAAiB,CAAC,IAC1E;AACA,wBAAoB,qBAAqB,SAAS,gBAAgB;AAClE,iBAAa,KAAK;AAAA,OACf,WAAW,CAAC,IAAI,iBAAiB,CAAC,KAAK;AAAA,IAC1C;AAAA,EACF;AACA,SAAO;AACT;;;ACzaA,IAAM,aAAa;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AAMO,SAAS,SAAS,MAAM;AAC7B,SAAO,WAAW,IAAI;AACxB;AAiBO,IAAM,kBAAkB;AAAA;AAAA,EAE7B,WAAW,WAAW,IAAI,KAAK;AAAA,EAC/B,WAAY,IAAI,KAAK,KAAK,UAAW;AAAA,EACrC,MAAM;AAAA,EACN,KAAK;AAAA,EACL,SAAS,OAAO;AAClB;;;ACUA,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA,EAIf,YAAY,SAAS;AAKnB,SAAK,QAAQ,QAAQ;AASrB,SAAK;AAAA,IAAoD,QAAQ;AASjE,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAS/D,SAAK,eACH,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAM5D,SAAK,mBACH,QAAQ,oBAAoB,SAAY,QAAQ,kBAAkB;AAMpE,SAAK,UAAU,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAM/D,SAAK,YAAY,CAAC,EAAE,KAAK,WAAW,KAAK;AAMzC,SAAK,0BAA0B,QAAQ;AAMvC,SAAK,mBAAmB;AAMxB,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB;AACjB,WAAO,KAAK,kBAAkB,gBAAgB,KAAK,MAAM;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,YAAY,CAAC,EAAE,UAAU,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,UAAU;AAC3B,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,YAAY,CAAC,EAAE,KAAK,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,aAAa;AAC1B,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,MAAM;AAC1B,SAAK,0BAA0B;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAO,qBAAQ;;;AC7QR,IAAM,SAAS;AAMf,IAAM,YAAY,KAAK,KAAK;AAM5B,IAAM,SAAS,CAAC,CAAC,WAAW,CAAC,WAAW,WAAW,SAAS;AAM5D,IAAM,eAAe,CAAC,MAAM,KAAK,KAAK,EAAE;AAOxC,IAAM,aAAa,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAMjE,IAAM,qBAAN,cAAiC,mBAAW;AAAA;AAAA;AAAA;AAAA,EAI1C,YAAY,MAAM;AAChB,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,oBAAoB,SAAU,YAAY,OAAO;AAC/C,eAAO,aAAa,KAAK,KAAK,MAAM,CAAC,IAAI,MAAM;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAQO,IAAM,cAAc;AAAA,EACzB,IAAI,mBAAmB,WAAW;AAAA,EAClC,IAAI,mBAAmB,aAAa;AAAA,EACpC,IAAI,mBAAmB,aAAa;AAAA,EACpC,IAAI,mBAAmB,aAAa;AAAA,EACpC,IAAI,mBAAmB,4CAA4C;AAAA,EACnE,IAAI,mBAAmB,8CAA8C;AACvE;AAWO,SAAS,aAAa,OAAO,QAAQ,WAAW,QAAQ;AAC7D,QAAM,SAAS,MAAM;AACrB,cAAY,YAAY,IAAI,YAAY;AACxC,WAAS,UAAU;AACnB,MAAI,WAAW,QAAW;AACxB,QAAI,YAAY,GAAG;AAEjB,eAAS,MAAM,MAAM;AAAA,IACvB,OAAO;AACL,eAAS,IAAI,MAAM,MAAM;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ;AACvC,WAAO,CAAC,IAAK,YAAY,MAAM,CAAC,IAAK;AACrC,QAAI,IAAI,SAAS,KAAK,IAAI,KAAK,IAAK,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,MAAO,GAAG,CAAC;AAC1E,QAAI,IAAI,YAAY;AAClB,UAAI;AAAA,IACN,WAAW,IAAI,CAAC,YAAY;AAC1B,UAAI,CAAC;AAAA,IACP;AACA,WAAO,IAAI,CAAC,IAAI;AAAA,EAClB;AACA,SAAO;AACT;AAWO,SAAS,WAAW,OAAO,QAAQ,WAAW,QAAQ;AAC3D,QAAM,SAAS,MAAM;AACrB,cAAY,YAAY,IAAI,YAAY;AACxC,WAAS,UAAU;AACnB,MAAI,WAAW,QAAW;AACxB,QAAI,YAAY,GAAG;AAEjB,eAAS,MAAM,MAAM;AAAA,IACvB,OAAO;AACL,eAAS,IAAI,MAAM,MAAM;AAAA,IAC3B;AAAA,EACF;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ;AACvC,WAAO,CAAC,IAAK,MAAM,MAAM,CAAC,IAAK;AAC/B,WAAO,IAAI,CAAC,IACT,MAAM,KAAK,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,IAAK,KAAK,KAAK;AAAA,EACnE;AACA,SAAO;AACT;;;AC7HO,IAAMC,UAAS;AAQf,IAAMC,UAAS,CAAC,MAAM,KAAK,KAAK,EAAE;AAMlC,IAAMC,mBAAmB,KAAK,KAAKF,UAAU;AAUpD,IAAM,qBAAN,cAAiC,mBAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,YAAY,MAAM,iBAAiB;AACjC,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,QAAQC;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,MACR,eAAeC;AAAA,MACf,aAAaD;AAAA,IACf,CAAC;AAAA,EACH;AACF;AAQO,IAAME,eAAc;AAAA,EACzB,IAAI,mBAAmB,QAAQ;AAAA,EAC/B,IAAI,mBAAmB,aAAa,KAAK;AAAA,EACzC,IAAI,mBAAmB,+BAA+B;AAAA,EACtD,IAAI,mBAAmB,0BAA0B;AAAA,EACjD,IAAI,mBAAmB,8CAA8C;AAAA,EACrE,IAAI,mBAAmB,gDAAgD,KAAK;AAAA,EAC5E,IAAI,mBAAmB,8CAA8C,KAAK;AAC5E;;;AC5DA,IAAI,QAAQ,CAAC;AAKN,SAAS,QAAQ;AACtB,UAAQ,CAAC;AACX;AAOO,SAAS,IAAI,MAAM;AACxB,SACE,MAAM,IAAI,KACV,MAAM,KAAK,QAAQ,0CAA0C,SAAS,CAAC,KACvE;AAEJ;AAOO,SAASC,KAAI,MAAM,YAAY;AACpC,QAAM,IAAI,IAAI;AAChB;;;AC3BA,IAAI,aAAa,CAAC;AAKX,SAASC,SAAQ;AACtB,eAAa,CAAC;AAChB;AAUO,SAASC,KAAI,QAAQ,aAAa,aAAa;AACpD,QAAM,aAAa,OAAO,QAAQ;AAClC,QAAM,kBAAkB,YAAY,QAAQ;AAC5C,MAAI,EAAE,cAAc,aAAa;AAC/B,eAAW,UAAU,IAAI,CAAC;AAAA,EAC5B;AACA,aAAW,UAAU,EAAE,eAAe,IAAI;AAC5C;AA4BO,SAASC,KAAI,YAAY,iBAAiB;AAC/C,MAAI,cAAc,cAAc,mBAAmB,WAAW,UAAU,GAAG;AACzE,WAAO,WAAW,UAAU,EAAE,eAAe;AAAA,EAC/C;AACA,SAAO;AACT;;;AC3CA,IAAM,KAAK;AAEX,IAAM,IAAI;AACV,IAAM,KAAK,IAAI;AACf,IAAM,KAAK,KAAK;AAChB,IAAM,OAAO,KAAK,IAAI;AAEtB,IAAM,SAAS,KAAK,KAAK,IAAI,CAAC;AAC9B,IAAM,MAAM,IAAI,WAAW,IAAI;AAC/B,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,MAAM;AAClB,IAAM,MAAM,MAAM;AAClB,IAAM,MAAM,MAAM;AAElB,IAAM,KAAK,IAAI,IAAI,IAAK,IAAI,KAAM,KAAM,IAAI,KAAM;AAClD,IAAM,KAAM,IAAI,IAAK,IAAK,IAAI,KAAM,KAAM,KAAK,KAAM;AACrD,IAAM,KAAM,KAAK,KAAM,MAAO,KAAK,KAAM;AACzC,IAAM,KAAM,KAAK,KAAM;AAEvB,IAAM,KAAM,IAAI,IAAK,KAAM,KAAK,KAAM,MAAO,MAAM,MAAO;AAC1D,IAAM,KAAM,KAAK,KAAM,MAAO,KAAK,KAAM;AACzC,IAAM,KAAM,MAAM,KAAM,MAAO,MAAM,MAAO;AAC5C,IAAM,KAAM,OAAO,MAAO;AAE1B,IAAM,IAAI;AAQV,SAAS,SAAS,SAAS,UAAU,MAAM;AACzC,QAAM,IAAI,UAAU;AACpB,QAAM,IAAI,KAAK,QAAQ,WAAW,WAAW;AAE7C,QAAM,IAAI,IAAI;AACd,QAAM,KAAK,KAAK,IAAI;AAEpB,QAAM,OACJ,KACA,KAAK,KAAK,IAAI,IAAI,EAAE,IACpB,KAAK,KAAK,IAAI,IAAI,EAAE,IACpB,KAAK,KAAK,IAAI,IAAI,EAAE,IACpB,KAAK,KAAK,IAAI,IAAI,EAAE;AAEtB,QAAM,OAAO,KAAK,IAAI,IAAI;AAC1B,QAAM,QAAQ,OAAO;AAErB,QAAM,OAAO,KAAK,IAAI,IAAI;AAE1B,QAAM,OAAO,OAAO;AACpB,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,QAAQ;AAEtB,QAAM,QAAQ,IAAI,IAAI;AACtB,QAAM,YAAY,KAAK,KAAK,IAAI,IAAI,KAAK;AAEzC,QAAM,IAAI,IAAI;AACd,QAAM,KAAK,IAAI,KAAK;AAEpB,QAAM,IAAI,OAAO,QAAQ;AACzB,QAAM,KAAK,IAAI;AAEf,QAAM,IAAI,KAAK,IAAI;AACnB,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,WACJ,OACC,OAAO,KACL,KAAK,IAAK,KAAK,MAAO,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,SAC9D,KAAK,OAAQ,KAAK,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,OAAO,IAAI;AAE1E,MAAI,aACD,IACE,KAAK,KAAM,IAAI,IAAI,QAAQ,KAC3B,KAAK,OAAQ,IAAI,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,OAAO,KAAK,UAClE;AAEF,cAAY;AAAA,IACV,YAAY,UAAU,uBAAuB,KAAK,MAAM,CAAC;AAAA,IACzD,CAAC,KAAK;AAAA,IACN,KAAK;AAAA,EACP;AAEA,SAAO,CAAC,UAAU,SAAS,GAAG,UAAU,QAAQ,CAAC;AACnD;AAEA,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AAQtB,SAAS,WAAW,WAAW,UAAU,MAAM;AAC7C,cAAY,KAAK,WAAW,eAAe,aAAa;AAExD,MAAI,WAAW,cAAc;AAC3B,eAAW;AAAA,EACb,WAAW,WAAW,cAAc;AAClC,eAAW;AAAA,EACb;AAEA,QAAM,SAAS,UAAU,QAAQ;AACjC,QAAM,SAAS,KAAK,IAAI,MAAM;AAC9B,QAAM,SAAS,KAAK,IAAI,MAAM;AAE9B,QAAM,SAAS,SAAS;AACxB,QAAM,UAAU,SAAS;AACzB,QAAM,UAAU,UAAU;AAE1B,QAAM,SAAS,UAAU,SAAS;AAClC,QAAM,aAAa,uBAAuB,KAAK,MAAM;AACrD,QAAM,gBAAgB,UAAU,UAAU;AAE1C,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,UAAU,CAAC;AAC3C,QAAM,IAAI,OAAO,UAAU;AAE3B,QAAM,IAAI,SAAS,KAAK,SAAS,eAAe,CAAC,KAAK,IAAI,KAAK,EAAE;AACjE,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,IACJ,KACC,KAAK,SACJ,KAAK,KAAK,IAAI,IAAI,MAAM,IACxB,KAAK,KAAK,IAAI,IAAI,MAAM,IACxB,KAAK,KAAK,IAAI,IAAI,MAAM;AAE5B,QAAM,UACJ,KACE,KACC,IACE,KAAK,KAAM,IAAI,UAAU,KACzB,KAAK,OAAQ,IAAI,KAAK,UAAU,UAAU,KAAK,IAAI,KAAK,SAC7D;AAEF,MAAI,WACF,MACC,IACC,IACE,UACC,KAAK,IACH,KAAK,MAAO,IAAI,UAAU,IAAI,IAAI,IAAI,KAAK,KAC3C,KAAK,OAAQ,KAAK,KAAK,UAAU,UAAU,MAAM,IAAI,MAAM;AAEpE,MAAI,CAAC,KAAK,OAAO;AACf,gBAAY;AAAA,EACd;AAEA,SAAO,CAAC,SAAS,QAAQ;AAC3B;AAMA,SAAS,uBAAuB,MAAM;AACpC,UAAQ,OAAO,KAAK,IAAI,MAAM;AAChC;AAKA,IAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF;AAMO,SAAS,aAAa,MAAM;AACjC,MAAI,SAAS;AACb,aAAW,MAAM,aAAa;AAC5B,UAAM,QAAQ,KAAK,MAAM,EAAE;AAC3B,QAAI,OAAO;AACT,eAAS,SAAS,MAAM,CAAC,CAAC;AAC1B;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,SAAS,SAAS,SAAS,OAAO;AACpC,aAAS,SAAS;AAAA,EACpB,WAAW,SAAS,SAAS,SAAS,OAAO;AAC3C,YAAQ;AACR,aAAS,SAAS;AAAA,EACpB;AACA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,SAAO,EAAC,QAAQ,MAAK;AACvB;AAOA,SAAS,sBAAsB,aAAa,MAAM;AAChD,SAAO,SAAU,OAAO,QAAQ,WAAW,QAAQ;AACjD,UAAM,SAAS,MAAM;AACrB,gBAAY,YAAY,IAAI,YAAY;AACxC,aAAS,UAAU;AACnB,QAAI,CAAC,QAAQ;AACX,UAAI,YAAY,GAAG;AACjB,iBAAS,MAAM,MAAM;AAAA,MACvB,OAAO;AACL,iBAAS,IAAI,MAAM,MAAM;AAAA,MAC3B;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ;AACvC,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,IAAI,MAAM,IAAI,CAAC;AACrB,YAAM,QAAQ,YAAY,GAAG,GAAG,IAAI;AACpC,aAAO,CAAC,IAAI,MAAM,CAAC;AACnB,aAAO,IAAI,CAAC,IAAI,MAAM,CAAC;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AACF;AAMO,SAAS,eAAe,MAAM;AACnC,QAAM,OAAO,aAAa,IAAI;AAC9B,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,IAAI,mBAAW,EAAC,MAAM,OAAO,IAAG,CAAC;AAC1C;AAMO,SAAS,eAAe,YAAY;AACzC,QAAM,OAAO,aAAa,WAAW,QAAQ,CAAC;AAC9C,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,SAAS,sBAAsB,YAAY,IAAI;AAAA,IAC/C,SAAS,sBAAsB,UAAU,IAAI;AAAA,EAC/C;AACF;;;AC7QO,IAAM,iBAAiB;AAWvB,SAAS,YAAY,IAAI,IAAI,QAAQ;AAC1C,WAAS,UAAU;AACnB,QAAM,OAAO,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAM,OAAO,UAAU,GAAG,CAAC,CAAC;AAC5B,QAAM,eAAe,OAAO,QAAQ;AACpC,QAAM,cAAc,UAAU,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;AAC/C,QAAM,IACJ,KAAK,IAAI,WAAW,IAAI,KAAK,IAAI,WAAW,IAC5C,KAAK,IAAI,WAAW,IAClB,KAAK,IAAI,WAAW,IACpB,KAAK,IAAI,IAAI,IACb,KAAK,IAAI,IAAI;AACjB,SAAO,IAAI,SAAS,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AAC/D;AAQA,SAAS,kBAAkB,aAAa,QAAQ;AAC9C,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,KAAK,GAAG,EAAE,GAAG;AACxD,cAAU,YAAY,YAAY,CAAC,GAAG,YAAY,IAAI,CAAC,GAAG,MAAM;AAAA,EAClE;AACA,SAAO;AACT;AAcO,SAAS,UAAU,UAAU,SAAS;AAC3C,YAAU,WAAW,CAAC;AACtB,QAAM,SAAS,QAAQ,UAAU;AACjC,QAAM,aAAa,QAAQ,cAAc;AACzC,QAAM,OAAO,SAAS,QAAQ;AAC9B,MAAI,SAAS,sBAAsB;AACjC,eAAW,SAAS,MAAM,EAAE,UAAU,YAAY,WAAW;AAAA,EAC/D;AACA,MAAI,SAAS;AACb,MAAI,aAAa,QAAQ,GAAG,IAAI,GAAG;AACnC,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK,cAAc;AACjB;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,cAAc;AACjB;AAAA,MACE,SACA,eAAe;AACjB,eAAS,kBAAkB,aAAa,MAAM;AAC9C;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,KAAK,WAAW;AACd;AAAA,MACE,SACA,eAAe;AACjB,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,kBAAU,kBAAkB,YAAY,CAAC,GAAG,MAAM;AAAA,MACpD;AACA;AAAA,IACF;AAAA,IACA,KAAK,gBAAgB;AACnB;AAAA,MACE,SACA,eAAe;AACjB,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,iBAAS,YAAY,CAAC;AACtB,aAAK,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3C,oBAAU,kBAAkB,OAAO,CAAC,GAAG,MAAM;AAAA,QAC/C;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,sBAAsB;AACzB,YAAM;AAAA;AAAA,QAEF,SACA,cAAc;AAAA;AAClB,WAAK,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,kBAAU,UAAU,WAAW,CAAC,GAAG,OAAO;AAAA,MAC5C;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,IAAI,MAAM,gCAAgC,IAAI;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;AAgBA,SAAS,gBAAgB,aAAa,QAAQ;AAC5C,MAAI,OAAO;AACX,QAAM,MAAM,YAAY;AACxB,MAAI,KAAK,YAAY,MAAM,CAAC,EAAE,CAAC;AAC/B,MAAI,KAAK,YAAY,MAAM,CAAC,EAAE,CAAC;AAC/B,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,KAAK,YAAY,CAAC,EAAE,CAAC;AAC3B,UAAM,KAAK,YAAY,CAAC,EAAE,CAAC;AAC3B,YACE,UAAU,KAAK,EAAE,KAChB,IAAI,KAAK,IAAI,UAAU,EAAE,CAAC,IAAI,KAAK,IAAI,UAAU,EAAE,CAAC;AACvD,SAAK;AACL,SAAK;AAAA,EACP;AACA,SAAQ,OAAO,SAAS,SAAU;AACpC;AAYO,SAASC,SAAQ,UAAU,SAAS;AACzC,YAAU,WAAW,CAAC;AACtB,QAAM,SAAS,QAAQ,UAAU;AACjC,QAAM,aAAa,QAAQ,cAAc;AACzC,QAAM,OAAO,SAAS,QAAQ;AAC9B,MAAI,SAAS,sBAAsB;AACjC,eAAW,SAAS,MAAM,EAAE,UAAU,YAAY,WAAW;AAAA,EAC/D;AACA,MAAI,OAAO;AACX,MAAI,aAAa,QAAQ,GAAG,IAAI,GAAG;AACnC,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,cAAc;AACjB;AAAA,IACF;AAAA,IACA,KAAK,WAAW;AACd;AAAA,MACE,SACA,eAAe;AACjB,aAAO,KAAK,IAAI,gBAAgB,YAAY,CAAC,GAAG,MAAM,CAAC;AACvD,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,gBAAQ,KAAK,IAAI,gBAAgB,YAAY,CAAC,GAAG,MAAM,CAAC;AAAA,MAC1D;AACA;AAAA,IACF;AAAA,IACA,KAAK,gBAAgB;AACnB;AAAA,MACE,SACA,eAAe;AACjB,WAAK,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,iBAAS,YAAY,CAAC;AACtB,gBAAQ,KAAK,IAAI,gBAAgB,OAAO,CAAC,GAAG,MAAM,CAAC;AACnD,aAAK,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC3C,kBAAQ,KAAK,IAAI,gBAAgB,OAAO,CAAC,GAAG,MAAM,CAAC;AAAA,QACrD;AAAA,MACF;AACA;AAAA,IACF;AAAA,IACA,KAAK,sBAAsB;AACzB,YAAM;AAAA;AAAA,QAEF,SACA,cAAc;AAAA;AAClB,WAAK,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC/C,gBAAQA,SAAQ,WAAW,CAAC,GAAG,OAAO;AAAA,MACxC;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,IAAI,MAAM,gCAAgC,IAAI;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AACT;;;ACtIA,IAAM,qBAAqB,CAAC,cAAiB;AAK7C,IAAM,sBAAsB,CAAC,cAAiB;AAsB9C,IAAI,wBAAwB;AAKrB,SAAS,yBAAyB,SAAS;AAChD,QAAM,OAAO,YAAY,SAAY,OAAO;AAC5C,0BAAwB,CAAC;AAC3B;AAQO,SAAS,eAAe,OAAO,QAAQ;AAC5C,MAAI,WAAW,QAAW;AACxB,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AACA,aAAS;AAAA,EACX,OAAO;AACL,aAAS,MAAM,MAAM;AAAA,EACvB;AACA,SAAO;AACT;AAOO,SAAS,kBAAkB,OAAO,QAAQ;AAC/C,MAAI,WAAW,UAAa,UAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AACA,YAAQ;AAAA,EACV;AACA,SAAO;AACT;AASO,SAAS,cAAc,YAAY;AACxC,EAAAC,KAAQ,WAAW,QAAQ,GAAG,UAAU;AACxC,EAAAA,KAAiB,YAAY,YAAY,cAAc;AACzD;AAKO,SAAS,eAAe,aAAa;AAC1C,cAAY,QAAQ,aAAa;AACnC;AAWO,SAASC,KAAI,gBAAgB;AAClC,MAAI,EAAE,OAAO,mBAAmB,WAAW;AACzC,WAAO;AAAA,EACT;AACA,QAAM,aAAa,IAAQ,cAAc;AACzC,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AACA,aAAWC,mBAAkB,qBAAqB;AAChD,UAAMC,cAAaD,gBAAe,cAAc;AAChD,QAAIC,aAAY;AACd,aAAOA;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAsBO,SAAS,mBAAmB,YAAY,YAAY,OAAO,OAAO;AACvE,eAAaF,KAAI,UAAU;AAC3B,MAAI;AACJ,QAAM,SAAS,WAAW,uBAAuB;AACjD,MAAI,QAAQ;AACV,sBAAkB,OAAO,YAAY,KAAK;AAC1C,QAAI,SAAS,UAAU,WAAW,SAAS,GAAG;AAC5C,YAAM,gBAAgB,WAAW,iBAAiB;AAClD,UAAI,eAAe;AACjB,0BACG,kBAAkB,gBAAiB,gBAAgB,KAAK;AAAA,MAC7D;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,YAAY,WAAW,SAAS;AACtC,QAAK,aAAa,aAAa,CAAC,SAAU,SAAS,WAAW;AAC5D,wBAAkB;AAAA,IACpB,OAAO;AAIL,YAAMG,cAAa;AAAA,QACjB;AAAA,QACAH,KAAI,WAAW;AAAA,MACjB;AACA,UAAI,CAACG,eAAc,cAAc,WAAW;AAE1C,0BAAkB,aAAa,WAAW,iBAAiB;AAAA,MAC7D,OAAO;AACL,YAAI,WAAW;AAAA,UACb,MAAM,CAAC,IAAI,aAAa;AAAA,UACxB,MAAM,CAAC;AAAA,UACP,MAAM,CAAC,IAAI,aAAa;AAAA,UACxB,MAAM,CAAC;AAAA,UACP,MAAM,CAAC;AAAA,UACP,MAAM,CAAC,IAAI,aAAa;AAAA,UACxB,MAAM,CAAC;AAAA,UACP,MAAM,CAAC,IAAI,aAAa;AAAA,QAC1B;AACA,mBAAWA,YAAW,UAAU,UAAU,CAAC;AAC3C,cAAM,QAAQ,YAAY,SAAS,MAAM,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,CAAC,CAAC;AACpE,cAAM,SAAS,YAAY,SAAS,MAAM,GAAG,CAAC,GAAG,SAAS,MAAM,GAAG,CAAC,CAAC;AACrE,2BAAmB,QAAQ,UAAU;AAAA,MACvC;AACA,YAAM,gBAAgB,QAClB,gBAAgB,KAAK,IACrB,WAAW,iBAAiB;AAChC,UAAI,kBAAkB,QAAW;AAC/B,2BAAmB;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AASO,SAAS,yBAAyB,aAAa;AACpD,iBAAe,WAAW;AAC1B,cAAY,QAAQ,SAAU,QAAQ;AACpC,gBAAY,QAAQ,SAAU,aAAa;AACzC,UAAI,WAAW,aAAa;AAC1B,QAAAJ,KAAiB,QAAQ,aAAa,cAAc;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAeO,SAAS,wBACd,cACA,cACA,kBACA,kBACA;AACA,eAAa,QAAQ,SAAU,aAAa;AAC1C,iBAAa,QAAQ,SAAU,aAAa;AAC1C,MAAAA,KAAiB,aAAa,aAAa,gBAAgB;AAC3D,MAAAA,KAAiB,aAAa,aAAa,gBAAgB;AAAA,IAC7D,CAAC;AAAA,EACH,CAAC;AACH;AAKO,SAAS,sBAAsB;AACpC,QAAU;AACV,EAAAK,OAAoB;AACtB;AAOO,SAAS,iBAAiB,YAAY,aAAa;AACxD,MAAI,CAAC,YAAY;AACf,WAAOJ,KAAI,WAAW;AAAA,EACxB;AACA,MAAI,OAAO,eAAe,UAAU;AAClC,WAAOA,KAAI,UAAU;AAAA,EACvB;AACA;AAAA;AAAA,IAAkC;AAAA;AACpC;AASO,SAAS,uCAAuC,gBAAgB;AACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQE,SAAU,OAAO,QAAQ,WAAW,QAAQ;AAC1C,YAAM,SAAS,MAAM;AACrB,kBAAY,cAAc,SAAY,YAAY;AAClD,eAAS,UAAU;AACnB,eAAS,WAAW,SAAY,SAAS,IAAI,MAAM,MAAM;AACzD,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,QAAQ;AACvC,cAAM,QAAQ,eAAe,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC;AAC1D,cAAM,cAAc,MAAM;AAC1B,iBAAS,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AACxC,iBAAO,IAAI,CAAC,IAAI,KAAK,cAAc,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC;AAAA,QAC3D;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAEJ;AAwBO,SAAS,wBAAwB,QAAQ,aAAa,SAAS,SAAS;AAC7E,QAAM,aAAaA,KAAI,MAAM;AAC7B,QAAM,WAAWA,KAAI,WAAW;AAChC,EAAAD;AAAA,IACE;AAAA,IACA;AAAA,IACA,uCAAuC,OAAO;AAAA,EAChD;AACA,EAAAA;AAAA,IACE;AAAA,IACA;AAAA,IACA,uCAAuC,OAAO;AAAA,EAChD;AACF;AAWO,SAASM,YAAW,YAAY,YAAY;AACjD,2BAAyB;AACzB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,eAAe,SAAY,aAAa;AAAA,EAC1C;AACF;AAWO,SAASC,UAAS,YAAY,YAAY;AAC/C,QAAM,SAAS;AAAA,IACb;AAAA,IACA,eAAe,SAAY,aAAa;AAAA,IACxC;AAAA,EACF;AACA,QAAM,MAAM,OAAO,CAAC;AACpB,MAAI,MAAM,QAAQ,MAAM,KAAK;AAC3B,WAAO,CAAC,IAAI,OAAO,MAAM,KAAK,GAAG,IAAI;AAAA,EACvC;AACA,SAAO;AACT;AAYO,SAAS,WAAW,aAAa,aAAa;AACnD,MAAI,gBAAgB,aAAa;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,aAAa,YAAY,SAAS,MAAM,YAAY,SAAS;AACnE,MAAI,YAAY,QAAQ,MAAM,YAAY,QAAQ,GAAG;AACnD,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,4BAA4B,aAAa,WAAW;AAC1E,SAAO,kBAAkB,kBAAkB;AAC7C;AAWO,SAAS,4BAA4B,QAAQ,aAAa;AAC/D,QAAM,aAAa,OAAO,QAAQ;AAClC,QAAM,kBAAkB,YAAY,QAAQ;AAC5C,MAAI,gBAAgBN,KAAiB,YAAY,eAAe;AAChE,MAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAKA,MAAI,mBAAmB;AAKvB,MAAI,wBAAwB;AAG5B,aAAWO,mBAAkB,oBAAoB;AAC/C,QAAI,CAAC,kBAAkB;AACrB,yBAAmBA,gBAAe,MAAM;AAAA,IAC1C;AACA,QAAI,CAAC,uBAAuB;AAC1B,8BAAwBA,gBAAe,WAAW;AAAA,IACpD;AAAA,EACF;AAEA,MAAI,CAAC,oBAAoB,CAAC,uBAAuB;AAC/C,WAAO;AAAA,EACT;AAEA,QAAM,mBAAmB;AACzB,MAAI,CAAC,uBAAuB;AAC1B,UAAM,gBAAgBP,KAAiB,kBAAkB,eAAe;AACxE,QAAI,eAAe;AACjB,sBAAgB;AAAA,QACd,iBAAiB;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,CAAC,kBAAkB;AAC5B,UAAM,aAAaA,KAAiB,YAAY,gBAAgB;AAChE,QAAI,YAAY;AACd,sBAAgB;AAAA,QACd;AAAA,QACA,sBAAsB;AAAA,MACxB;AAAA,IACF;AAAA,EACF,OAAO;AACL,oBAAgB;AAAA,MACd,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,eAAe;AACjB,kBAAc,MAAM;AACpB,kBAAc,WAAW;AACzB,IAAAD,KAAiB,QAAQ,aAAa,aAAa;AAAA,EACrD;AAEA,SAAO;AACT;AAOA,SAAS,sBAAsB,IAAI,IAAI;AACrC,SAAO,SAAU,OAAO,QAAQ,YAAY,QAAQ;AAClD,aAAS,GAAG,OAAO,QAAQ,YAAY,MAAM;AAC7C,WAAO,GAAG,QAAQ,QAAQ,YAAY,MAAM;AAAA,EAC9C;AACF;AAYO,SAAS,aAAa,QAAQ,aAAa;AAChD,QAAM,mBAAmBC,KAAI,MAAM;AACnC,QAAM,wBAAwBA,KAAI,WAAW;AAC7C,SAAO,4BAA4B,kBAAkB,qBAAqB;AAC5E;AAkBO,SAAS,UAAU,YAAY,QAAQ,aAAa;AACzD,QAAM,gBAAgB,aAAa,QAAQ,WAAW;AACtD,MAAI,CAAC,eAAe;AAClB,UAAM,aAAaA,KAAI,MAAM,EAAE,QAAQ;AACvC,UAAM,kBAAkBA,KAAI,WAAW,EAAE,QAAQ;AACjD,UAAM,IAAI;AAAA,MACR,kCAAkC,UAAU,QAAQ,eAAe;AAAA,IACrE;AAAA,EACF;AACA,SAAO,cAAc,YAAY,QAAW,WAAW,MAAM;AAC/D;AAcO,SAAS,gBAAgB,QAAQ,QAAQ,aAAa,OAAO;AAClE,QAAM,gBAAgB,aAAa,QAAQ,WAAW;AACtD,SAAO,eAAe,QAAQ,eAAe,QAAW,KAAK;AAC/D;AAUO,SAAS,yBACd,OACA,kBACA,uBACA;AACA,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACA,SAAO,cAAc,KAAK;AAC5B;AAKA,IAAI,iBAAiB;AASd,SAAS,kBAAkB,YAAY;AAC5C,mBAAiBA,KAAI,UAAU;AACjC;AAMO,SAAS,sBAAsB;AACpC,mBAAiB;AACnB;AAOO,SAAS,oBAAoB;AAClC,SAAO;AACT;AAQO,SAAS,gBAAgB;AAC9B,oBAAkB,WAAW;AAC/B;AASO,SAAS,iBAAiB,YAAY,kBAAkB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SAAO,UAAU,YAAY,kBAAkB,cAAc;AAC/D;AASO,SAAS,mBAAmB,YAAY,gBAAgB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,QACE,yBACA,CAACQ,QAAO,YAAY,CAAC,GAAG,CAAC,CAAC,KAC1B,WAAW,CAAC,KAAK,QACjB,WAAW,CAAC,KAAK,OACjB,WAAW,CAAC,KAAK,OACjB,WAAW,CAAC,KAAK,IACjB;AACA,8BAAwB;AACxB;AAAA,QACE;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,UAAU,YAAY,gBAAgB,cAAc;AAC7D;AASO,SAAS,aAAa,QAAQ,kBAAkB;AACrD,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,QAAQ,kBAAkB,cAAc;AACjE;AASO,SAAS,eAAe,QAAQ,gBAAgB;AACrD,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,QAAQ,gBAAgB,cAAc;AAC/D;AAUO,SAAS,iBAAiB,YAAY,kBAAkB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,QAAM,sBAAsBR,KAAI,gBAAgB,EAAE,iBAAiB;AACnE,QAAM,oBAAoB,eAAe,iBAAiB;AAC1D,SAAO,uBAAuB,oBACzB,aAAa,sBAAuB,oBACrC;AACN;AAUO,SAAS,mBAAmB,YAAY,gBAAgB;AAC7D,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,QAAM,oBAAoBA,KAAI,cAAc,EAAE,iBAAiB;AAC/D,QAAM,oBAAoB,eAAe,iBAAiB;AAC1D,SAAO,qBAAqB,oBACvB,aAAa,oBAAqB,oBACnC;AACN;AAYO,SAAS,8BAA8B,YAAY,UAAUS,YAAW;AAC7E,SAAO,SAAU,OAAO;AACtB,QAAI,aAAa;AACjB,QAAI,WAAW,SAAS,GAAG;AACzB,YAAM,eAAe,WAAW,UAAU;AAC1C,YAAM,oBAAoB,SAAS,YAAY;AAC/C,cAAQ,MAAM,MAAM,CAAC;AACrB,mBAAa,cAAc,OAAO,YAAY,iBAAiB;AAC/D,UAAI,YAAY;AAEd,cAAM,CAAC,IAAI,MAAM,CAAC,IAAI,aAAa;AAAA,MACrC;AACA,YAAM,CAAC,IAAI,MAAM,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;AAC3D,YAAM,CAAC,IAAI,MAAM,MAAM,CAAC,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;AAC3D,oBAAcA,WAAU,KAAK;AAAA,IAC/B,OAAO;AACL,oBAAcA,WAAU,KAAK;AAAA,IAC/B;AACA,QAAI,cAAc,SAAS,SAAS,GAAG;AAErC,kBAAY,CAAC,KAAK,aAAa,SAAS,SAAS,UAAU,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AACF;AAOO,SAAS,YAAY;AAG1B,2BAAyB,WAAoB;AAC7C,2BAAyBC,YAAoB;AAG7C;AAAA,IACEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,UAAU;", "names": ["intersects", "equals", "scale", "squaredDistance", "wrapX", "RADIUS", "EXTENT", "METERS_PER_UNIT", "PROJECTIONS", "add", "clear", "add", "get", "getArea", "add", "get", "makeProjection", "projection", "toEPSG4326", "clear", "fromLonLat", "toLonLat", "makeTransforms", "equals", "transform", "PROJECTIONS"]}