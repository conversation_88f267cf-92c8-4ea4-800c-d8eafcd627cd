{"version": 3, "file": "support-query.service.js", "sourceRoot": "", "sources": ["../../../src/support-query/services/support-query.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,sDAAiD;AACjD,kDAAsE;AAEtE,8CAA+F;AAC/F,kDAAoD;AACpD,oDAAgE;AAGhE,kDAAyD;AAGlD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC/B,YACkB,cAA8B,EAC9B,sBAA8C,EAC9C,cAA8B,EAC9B,yBAAoD,EACpD,cAA8B,EAC9B,gBAAkC;QALlC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,mBAAc,GAAd,cAAc,CAAgB;QAC9B,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,mBAAc,GAAd,cAAc,CAAgB;QAC9B,qBAAgB,GAAhB,gBAAgB,CAAkB;IACjD,CAAC;IAQS,sBAAsB,CAClC,IAA4B,EAC5B,cAA8B;;YAE9B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAE5B,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAS,EAAE;gBAC5D,IAAI;oBACH,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,cAAc,CAAC,CAAC;oBACrF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,yBAAyB,CACrE,wBAAgB,CAAC,2BAA2B,CAC5C,CAAC;oBAEF,MAAM,UAAU,GAAa,EAAE,CAAC;oBAEhC,MAAM,OAAO,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;oBACtE,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM;wBACnC,CAAC,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC;wBACtD,CAAC,CAAC,EAAE,CAAC;oBACN,UAAU,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;oBAEvE,MAAM,iBAAiB,GAAG,EAAE,KAAK,EAAE,CAAC;oBAEpC,IAAI,UAAU,CAAC,MAAM,EAAE;wBACtB,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CACpD,CAAC,CAAC,EACF,gCAAwB,CAAC,0BAA0B,EACnD,EAAE,EAAE,EAAE,UAAU,EAAE,EAClB,uBAAe,CAAC,0BAA0B,EAC1C,iBAAiB,CACjB,CAAC;qBACF;iBACD;gBAAC,OAAO,KAAK,EAAE;oBACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;iBACnE;gBAED,OAAO,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;YACtD,CAAC,CAAA,CAAC,CAAC;QACJ,CAAC;KAAA;IAEY,oBAAoB,CAChC,IAAY,EACZ,KAAa,EACb,YAAiB,EAAE;;YAEnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,CAC7E,SAAS,EACT,IAAI,EACJ,KAAK,CACL,CAAC;YAEF,OAAO,IAAI,uBAAU,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;KAAA;CACD,CAAA;AArEY,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAGsB,wBAAc;QACN,qCAAsB;QAC9B,wBAAc;QACH,oCAAyB;QACpC,wBAAc;QACZ,0BAAgB;GAPxC,mBAAmB,CAqE/B;AArEY,kDAAmB"}