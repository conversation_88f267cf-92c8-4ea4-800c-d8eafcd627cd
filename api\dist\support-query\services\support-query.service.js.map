{"version": 3, "file": "support-query.service.js", "sourceRoot": "", "sources": ["../../../src/support-query/services/support-query.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAE5C,kDAAoD;AAGpD,kDAAyD;AAGlD,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAC/B,YACkB,sBAA8C,EAC9C,cAA8B;QAD9B,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,mBAAc,GAAd,cAAc,CAAgB;IAC7C,CAAC;IAQS,sBAAsB,CAClC,IAA4B,EAC5B,cAA8B;;YAE9B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAE5B,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,GAAS,EAAE;gBAC5D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CACxE;oBACC,GAAG;oBACH,KAAK;iBACL,EACD,cAAc,CACd,CAAC;gBAEF,OAAO,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;YACtD,CAAC,CAAA,CAAC,CAAC;QACJ,CAAC;KAAA;CACD,CAAA;AA9BY,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAG8B,qCAAsB;QAC9B,wBAAc;GAHpC,mBAAmB,CA8B/B;AA9BY,kDAAmB"}