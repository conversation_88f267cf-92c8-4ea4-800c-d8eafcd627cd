import Iconify from '@/components/iconify';
import { useTranslate } from '@/locales/use-locales';
import { paths, ReplaceUrlVariable } from '@/routes/paths';
import { LOCATION_ENTITY_TYPE } from '@/shared/enum';
import { USER_TYPE } from '@/shared/enum/permission.enum';
import { ListItemButton, ListItemText, Link as MuiLink, Stack, Typography } from '@mui/material';
import React from 'react';

type MessageDetails = Record<string, any>;

interface UserActionMessageProps {
  type: string;
  details: MessageDetails;
  name?: string;
}

const getLocationPathByEntityType = (entityType?: string) => {
  if (!entityType) return '';

  switch (entityType.toLowerCase()) {
    case LOCATION_ENTITY_TYPE.GROUP:
      return paths.locationSetup.groupDetails;
    case LOCATION_ENTITY_TYPE.REGION:
      return paths.locationSetup.regionDetails;
    case LOCATION_ENTITY_TYPE.CLUSTER:
      return paths.locationSetup.clusterDetails;
    case LOCATION_ENTITY_TYPE.COUNTRY:
      return paths.locationSetup.countryDetails;
    case LOCATION_ENTITY_TYPE.AREA:
      return paths.locationSetup.areaDetails;
    default:
      return '';
  }
};

const UserActionMessage: React.FC<UserActionMessageProps> = ({ type, details, name = '' }) => {
  let message = '';
  let link = '';
  const { t } = useTranslate();

  switch (type) {
    case USER_TYPE.GEO_CONTACT:
      message = `${t('messages.contact_person')} ${t('messages.for')} ${details.coreSolution} ${t('messages.in')} ${details.entityTitle} ${details.entityType}`;

      const entityPath = getLocationPathByEntityType(details.entityType);
      if (entityPath) {
        link = ReplaceUrlVariable(paths.locationSetup.root + entityPath, {
          id: details.entityId,
        });
      } else {
        link = '#';
      }
      break;

    case USER_TYPE.INDUSTRY_EXPERT:
      message = `${details.verticalName} ${t('messages.industry_expert')} ${t('messages.for')} ${t('messages.location')} ${details.locationName} `;
      link = ReplaceUrlVariable(paths.locationSetup.root + paths.locationSetup.locationDetails, {
        id: details.locationId,
      });
      break;

    case USER_TYPE.SUSTAINABILITY_EXPERT:
      message = `${t('messages.sustainability_expert')} ${t('messages.for')} ${t('messages.location')} ${details.locationName} `;
      link = ReplaceUrlVariable(paths.locationSetup.root + paths.locationSetup.locationDetails, {
        id: details.locationId,
      });
      break;

    case USER_TYPE.LOCATION_CONTACT:
      message = `${t('messages.contact_person')} ${t('messages.in')} ${details.contactType} ${t('messages.for')} ${t('messages.location')} ${details.locationName}`;
      link = ReplaceUrlVariable(paths.locationSetup.root + paths.locationSetup.locationDetails, {
        id: details.locationId,
      });
      break;

    case USER_TYPE.USER_PERMISSION:
      message = `${details.groupName} ${t('messages.of')} ${t('messages.location')} ${details.locationName} `;
      link = ReplaceUrlVariable(paths.locationSetup.root + paths.locationSetup.locationDetails, {
        id: details.locationId,
      });
      break;

    case USER_TYPE.CAPABILITY_OWNER:
      message = `${t('messages.owner')} ${t('messages.of')} ${[
        details?.capabilityName,
        details?.product,
        details?.category,
        details?.subCategory,
      ]
        .filter(Boolean)
        .join(' - ')} ${t('messages.entry')} ${t('messages.for')} ${t('messages.location')} ${details.locationName} `;
      link = ReplaceUrlVariable(paths.capabilities.root + paths.capabilities.capabilityDetail, {
        capabilityId: details.id,
      });
      break;

    case USER_TYPE.CAPABILITY_COOWNER:
      message = `${t('messages.co_owner')} ${t('messages.of')} ${[
        details?.capabilityName,
        details?.product,
        details?.category,
        details?.subCategory,
      ]
        .filter(Boolean)
        .join(' - ')} ${t('messages.entry')} ${t('messages.for')} ${t('messages.location')} ${details.locationName}`;
      link = ReplaceUrlVariable(paths.capabilities.root + paths.capabilities.capabilityDetail, {
        capabilityId: details.id,
      });
      break;

    default:
      message = 'Unknown type';
      link = '#';
  }

  return (
    <ListItemButton
      disableRipple
      sx={{
        p: 2.5,
        alignItems: 'center',
        borderBottom: (theme) => `dashed 1px ${theme.palette.divider}`,
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between" width="100%" spacing={1}>
        <ListItemText
          disableTypography
          primary={
            <Typography
              variant="body2"
              sx={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                flexGrow: 1,
                textWrap: 'wrap',
              }}
            >
              {message}
            </Typography>
          }
        />
        <MuiLink
          href={link}
          target="_blank"
          rel="noopener noreferrer"
          sx={{
            flexShrink: 0,
            color: 'text.secondary',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Iconify icon="mingcute:external-link-line" width={25} />
        </MuiLink>
      </Stack>
    </ListItemButton>
  );
};

export default UserActionMessage;
