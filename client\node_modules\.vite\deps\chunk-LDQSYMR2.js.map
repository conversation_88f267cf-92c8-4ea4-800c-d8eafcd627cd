{"version": 3, "sources": ["../../@mui/material/DialogContent/DialogContent.js", "../../@mui/material/DialogContent/dialogContentClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDialogContentUtilityClass } from \"./dialogContentClasses.js\";\nimport dialogTitleClasses from \"../DialogTitle/dialogTitleClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    dividers\n  } = ownerState;\n  const slots = {\n    root: ['root', dividers && 'dividers']\n  };\n  return composeClasses(slots, getDialogContentUtilityClass, classes);\n};\nconst DialogContentRoot = styled('div', {\n  name: '<PERSON>iDialogContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.dividers && styles.dividers];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  flex: '1 1 auto',\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  overflowY: 'auto',\n  padding: '20px 24px',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.dividers,\n    style: {\n      padding: '16px 24px',\n      borderTop: `1px solid ${(theme.vars || theme).palette.divider}`,\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.dividers,\n    style: {\n      [`.${dialogTitleClasses.root} + &`]: {\n        paddingTop: 0\n      }\n    }\n  }]\n})));\nconst DialogContent = /*#__PURE__*/React.forwardRef(function DialogContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogContent'\n  });\n  const {\n    className,\n    dividers = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    dividers\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogContentRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Display the top and bottom dividers.\n   * @default false\n   */\n  dividers: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContent;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDialogContentUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogContent', slot);\n}\nconst dialogContentClasses = generateUtilityClasses('MuiDialogContent', ['root', 'dividers']);\nexport default dialogContentClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,oBAAoB,IAAI;AACtD;AACA,IAAM,uBAAuB,uBAAuB,oBAAoB,CAAC,QAAQ,UAAU,CAAC;AAC5F,IAAO,+BAAQ;;;ADKf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,UAAU;AAAA,EACvC;AACA,SAAO,eAAe,OAAO,8BAA8B,OAAO;AACpE;AACA,IAAM,oBAAoB,eAAO,OAAO;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,YAAY,OAAO,QAAQ;AAAA,EAC7D;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,MAAM;AAAA;AAAA,EAEN,yBAAyB;AAAA,EACzB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,SAAS;AAAA,MACT,WAAW,cAAc,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,MAC7D,cAAc,cAAc,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAClE;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,CAAC,WAAW;AAAA,IAClB,OAAO;AAAA,MACL,CAAC,IAAI,2BAAmB,IAAI,MAAM,GAAG;AAAA,QACnC,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,gBAAmC,iBAAW,SAASA,eAAc,SAAS,KAAK;AACvF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,mBAAmB;AAAA,IAC1C,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH,CAAC;AACD,OAAwC,cAAc,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,wBAAQ;", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsx", "PropTypes"]}