"use client";
import {
  Icon,
  InlineIcon,
  _api,
  addAPIProvider,
  addCollection,
  addIcon,
  calculateSize,
  disableCache,
  enableCache,
  getIcon,
  iconLoaded,
  iconToSVG,
  listIcons,
  loadIcon,
  loadIcons,
  replaceIDs,
  setCustomIconLoader,
  setCustomIconsLoader
} from "./chunk-WVWYCXK4.js";
import "./chunk-OU5AQDZK.js";
import "./chunk-EWTE5DHJ.js";
export {
  Icon,
  InlineIcon,
  _api,
  addAPIProvider,
  addCollection,
  addIcon,
  iconToSVG as buildIcon,
  calculateSize,
  disableCache,
  enableCache,
  getIcon,
  iconLoaded as iconExists,
  iconLoaded,
  listIcons,
  loadIcon,
  loadIcons,
  replaceIDs,
  setCustomIconLoader,
  setCustomIconsLoader
};
