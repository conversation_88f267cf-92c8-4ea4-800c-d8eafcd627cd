import { BingoCardConfigRepository } from '../repositories';
import { BingoCardConfigResponseDto, BingoCardConfigWithCapabilitiesResponseDto, BingoLocationCapabilitiesResponse, FilterRequestDto } from '../dtos';
import { CapabilityLegRepository, CapabilityRepository } from 'src/capability/repositories';
import { LocationRepository } from 'src/location/repositories';
import { CurrentContext } from 'src/shared/types';
import { ReportResponseDto } from '../dtos/response/list-reports.response.dto';
import { MessageResponseDto } from 'src/shared/dtos';
import { UpsertReportDTO } from '../dtos/request/upsert-report-request.dto';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
export declare class BingoCardService {
    private readonly bingoCardConfigRepository;
    private readonly capabilityRepository;
    private readonly capabilityLegRepository;
    private readonly locationRepository;
    private readonly adminApiClient;
    private readonly historyService;
    constructor(bingoCardConfigRepository: BingoCardConfigRepository, capabilityRepository: CapabilityRepository, capabilityLegRepository: CapabilityLegRepository, locationRepository: LocationRepository, adminApiClient: AdminApiClient, historyService: HistoryApiClient);
    getAllBingoCardConfigs(): Promise<BingoCardConfigResponseDto[]>;
    listReports(currentContext: CurrentContext): Promise<ReportResponseDto[]>;
    createReport(createReportDTO: UpsertReportDTO, currentContext: CurrentContext): Promise<ReportResponseDto>;
    deleteReport(currentContext: CurrentContext, id: number): Promise<MessageResponseDto>;
    getBingoCardConfigById(id: number, currentContext: CurrentContext): Promise<BingoCardConfigWithCapabilitiesResponseDto>;
    getConfigLocationCapabilities(id: number, filters?: FilterRequestDto): Promise<BingoLocationCapabilitiesResponse[]>;
}
