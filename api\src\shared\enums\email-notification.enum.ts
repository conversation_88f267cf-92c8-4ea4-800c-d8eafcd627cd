export enum EMAIL_TEMPLATES {
	SUPPORT_EMAIL_NOTIFICATION = 'SUPPORT.EMAIL',
	SCHEDULER_ENTRIES_NOTIFICATION = 'SCHEDULER.ENTRIES.NOTIFICATION',
}

export enum USER_EMAIL_GROUP {
	PRODUCT_SUPPORT_EMAIL_GROUP = 'ProductSupportEmailGroup',
}

export enum NOTIFICATION_ENTITY_TYPE {
	SUPPORT_EMAIL_NOTIFICATION = 'SUPPORT.EMAIL.NOTIFICATION',
	SCHEDULER_ENTRIES_NOTIFICATION = 'SCHEDULER.ENTRIES.NOTIFICATION',
}

export enum UI_ROUTES {
	A<PERSON><PERSON>ABLE_CAPABILITY_LIST = '/:locationId/capabilities',
	CAPABILITY_DETAIL = '/:capabilityId',
}
