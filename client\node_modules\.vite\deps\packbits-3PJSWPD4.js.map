{"version": 3, "sources": ["../../geotiff/dist-module/compression/packbits.js"], "sourcesContent": ["import BaseDecoder from './basedecoder.js';\n\nexport default class PackbitsDecoder extends BaseDecoder {\n  decodeBlock(buffer) {\n    const dataView = new DataView(buffer);\n    const out = [];\n\n    for (let i = 0; i < buffer.byteLength; ++i) {\n      let header = dataView.getInt8(i);\n      if (header < 0) {\n        const next = dataView.getUint8(i + 1);\n        header = -header;\n        for (let j = 0; j <= header; ++j) {\n          out.push(next);\n        }\n        i += 1;\n      } else {\n        for (let j = 0; j <= header; ++j) {\n          out.push(dataView.getUint8(i + j + 1));\n        }\n        i += header + 1;\n      }\n    }\n    return new Uint8Array(out).buffer;\n  }\n}\n"], "mappings": ";;;;;;AAEA,IAAqB,kBAArB,cAA6C,YAAY;AAAA,EACvD,YAAY,QAAQ;AAClB,UAAM,WAAW,IAAI,SAAS,MAAM;AACpC,UAAM,MAAM,CAAC;AAEb,aAAS,IAAI,GAAG,IAAI,OAAO,YAAY,EAAE,GAAG;AAC1C,UAAI,SAAS,SAAS,QAAQ,CAAC;AAC/B,UAAI,SAAS,GAAG;AACd,cAAM,OAAO,SAAS,SAAS,IAAI,CAAC;AACpC,iBAAS,CAAC;AACV,iBAAS,IAAI,GAAG,KAAK,QAAQ,EAAE,GAAG;AAChC,cAAI,KAAK,IAAI;AAAA,QACf;AACA,aAAK;AAAA,MACP,OAAO;AACL,iBAAS,IAAI,GAAG,KAAK,QAAQ,EAAE,GAAG;AAChC,cAAI,KAAK,SAAS,SAAS,IAAI,IAAI,CAAC,CAAC;AAAA,QACvC;AACA,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AACA,WAAO,IAAI,WAAW,GAAG,EAAE;AAAA,EAC7B;AACF;", "names": []}