"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CAPABILITY_STATUS_TYPE_ENUM = exports.thresholdDayLimit = void 0;
exports.thresholdDayLimit = 7;
var CAPABILITY_STATUS_TYPE_ENUM;
(function (CAPABILITY_STATUS_TYPE_ENUM) {
    CAPABILITY_STATUS_TYPE_ENUM["NEAR_EXPIRY"] = "NEAR_EXPIRY";
    CAPABILITY_STATUS_TYPE_ENUM["NEAR_PLANNED"] = "NEAR_PLANNED";
    CAPABILITY_STATUS_TYPE_ENUM["EXPIRED"] = "EXPIRED";
    CAPABILITY_STATUS_TYPE_ENUM["PLANNED_PASSED_AGO"] = "PLANNED_PASSED_AGO";
})(CAPABILITY_STATUS_TYPE_ENUM = exports.CAPABILITY_STATUS_TYPE_ENUM || (exports.CAPABILITY_STATUS_TYPE_ENUM = {}));
//# sourceMappingURL=schedular.types.js.map