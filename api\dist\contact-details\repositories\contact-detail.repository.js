"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactDetailRepository = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../shared/repositories");
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
const models_2 = require("../../metadata/models");
let ContactDetailRepository = class ContactDetailRepository extends repositories_1.BaseRepository {
    constructor() {
        super(models_1.ContactDetail);
    }
    getContactDetailsByCondition(condition) {
        return this.findAll({ where: condition });
    }
    getDetailedContactsByCondition(condition) {
        return this.findAll({
            where: condition,
            include: [
                {
                    model: models_2.CommonDropdown,
                    as: 'contactType',
                    attributes: ['id', 'title', 'code', 'type'],
                    required: false,
                }
            ],
        });
    }
    getContactDetailByCondition(condition) {
        return this.findOne({ where: condition });
    }
    addHierarchyContact(payload, currentContext) {
        const entity = new models_1.ContactDetail(payload);
        return this.save(entity, currentContext);
    }
    updateUserDetailsByCondition(condition, userDetails, currentContext) {
        return this.update({ userDetails }, currentContext, {
            where: condition,
        });
    }
    getPaginatedContactListByCondition(paginationParms, condition) {
        return this.findAndCountAll({
            where: condition,
            offset: (paginationParms.page - 1) * paginationParms.limit,
            limit: paginationParms.limit,
            order: [['createdOn', 'desc']],
        });
    }
    getPaginatedContactAsIndividualUserByCondition(paginationParms, condition) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.findAndCountAll({
                where: condition,
                order: [['createdOn', 'desc']],
            });
            const transformedRows = result.rows.flatMap((row) => {
                return row.userDetails.map((userDetail) => (Object.assign(Object.assign({}, row), { userDetail })));
            });
            const offset = (paginationParms.page - 1) * paginationParms.limit;
            const paginatedRows = transformedRows.slice(offset, offset + paginationParms.limit);
            return {
                count: transformedRows.length,
                rows: paginatedRows,
            };
        });
    }
    getContactAsIndividualUserByCondition(condition, transform = false) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.findAll({
                where: condition,
                order: [['createdOn', 'desc']],
            });
            if (transform) {
                const transformedRows = result.flatMap((row) => {
                    return row.userDetails.map((userDetail) => (Object.assign(Object.assign({}, row), { userDetail })));
                });
                return transformedRows;
            }
            return result;
        });
    }
    addBulkContacts(records, currentContext) {
        return this.insertMany(records, currentContext);
    }
    deleteByIds(ids, currentContext) {
        return this.deleteByCondition({
            id: { [sequelize_1.Op.in]: ids },
        }, currentContext);
    }
    deleteByLocationId(locationId, currentContext) {
        return this.deleteByCondition({ locationId }, currentContext);
    }
};
ContactDetailRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ContactDetailRepository);
exports.ContactDetailRepository = ContactDetailRepository;
//# sourceMappingURL=contact-detail.repository.js.map