import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { SupportQuery } from '../models';

@Injectable()
export class SupportQueryRepository extends BaseRepository<SupportQuery> {
	constructor() {
		super(SupportQuery);
	}

	public async createSupportQuery(
		payload: Partial<SupportQuery>,
		currentContext: CurrentContext,
	): Promise<SupportQuery> {
		const supportQuery = new SupportQuery(payload);
		return this.save(supportQuery, currentContext);
	}
}
