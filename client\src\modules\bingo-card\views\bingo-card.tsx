import { FC, useEffect, useMemo, useState } from 'react';
import { Container, Typography, Box, Button, Stack, Card } from '@mui/material';
import { useSettingsContext } from '@/components/settings';
import { useTranslate } from '@/locales/use-locales';
import BingoCardComponent from '../components/bingo-card';
import { KeyValue } from '@/shared/models';
import SvgColor from '@/components/svg-color';
import CustomBreadcrumbs from '@/components/custom-breadcrumbs';
import { paths } from '@/routes/paths';
import { useNavigate, useParams } from 'react-router';
import { useResponsive } from '@/hooks/use-responsive';
import { getIcon } from '@/shared/utils/get-icon';
import { getCapabilitiesById, getLocationCapabilities } from '@/shared/services/bingo-card.service';
import { CapabilityList, LocationCapability, LocationCapabilityList } from '@/shared/models/bingo-card.model';
import _ from 'lodash';
import { getBusinessUnitHierarchyByPermission } from '@/shared/services';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { TreeViewDataType } from '@/components/business-entity-treeview/treeview-types';
import { CAPABILITY_STATUS_ENUM, VIEW_TYPE } from '@/shared/enum/capability.enum';
import { mapBingoCardFiltersToApiPayload } from '@/shared/utils/capability-filter.util';
import { useQuery } from 'react-query';
import { enqueueSnackbar } from 'notistack';
import EmptyContent from '@/components/empty-content';

const BingoCardView = () => {
  const { t } = useTranslate();
  const { id } = useParams();
  const settings = useSettingsContext();
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [filters, setFilters] = useState<Record<string, KeyValue[]>>({});
  const navigate = useNavigate();
  const isSmallScreen = useResponsive('down', 'sm');
  const [capabilityList, setCapabilityList] = useState<CapabilityList | null>(null);
  const [locationCapabilityList, setLocationCapabilityList] = useState<LocationCapabilityList[]>([]);
  const [legCapability, setLegCapability] = useState<any | null>(null);
  const [businessEntityHierarchy, setBusinessEntityHierarchy] = useState<TreeViewDataType | null>(null);
  const [showAvailable, setShowAvailable] = useState(false);
  const [mergedBusinessEntityHierarchy, setMergedBusinessEntityHierarchy] = useState<TreeViewDataType | null>(null);
  const [filterLocationCapabilityList, setFilterLocationCapabilityList] = useState<LocationCapabilityList[]>([]);
  const [selectedBusinessEntities, setSelectedBusinessEntities] = useState<any>(null);
  const [viewType, setViewType] = useState<string>(VIEW_TYPE.FLAT);
  const [reset, setReset] = useState(false);
  const [isLoading, setIsloading] = useState(true);

  const shouldFetchCapabilities = useMemo(() => {
    const skipFilter = ['region', 'cluster', 'country', 'area'];
    const skipFetch = Object.keys(filters).length > 0 && Object.keys(filters).every((key) => skipFilter.includes(key));

    return reset || !skipFetch;
  }, [businessEntityHierarchy, capabilityList, filters, reset]);

  // Fetch business entity hierarchy
  const { data: businessEntityHierarchyData, isFetching: isBusinessEntityFetching } = useQuery({
    queryKey: ['businessunit-bingo-card', PERMISSIONS.APPLICATION_ADMIN],
    queryFn: () => getBusinessUnitHierarchyByPermission(PERMISSIONS.APPLICATION_ADMIN),
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong_business_units'), { variant: 'error' });
    },
    keepPreviousData: false,
  });

  // Fetch capability list
  const { data: capabilityListData, isFetching: isCapabilityListFetching } = useQuery({
    queryKey: ['capabilities', id],
    queryFn: () => getCapabilitiesById(id),
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string'
          ? error?.message
          : t('error_messages.something_went_wrong_capabilities'),
        { variant: 'error' },
      );
    },
    enabled: !!id,
    keepPreviousData: false,
  });

  // Fetch location capabilities
  const { data: locationCapabilitiesData, isFetching: isLocationCapabilitiesFetching } = useQuery({
    queryKey: ['location-capabilities', id, filters, reset],
    queryFn: () => {
      const apiFilters = mapBingoCardFiltersToApiPayload(filters);
      return getLocationCapabilities(id, apiFilters);
    },
    onError: () => {
      enqueueSnackbar(t('error_messages.something_went_wrong_location_capabilities'), { variant: 'error' });
    },
    enabled: !!id && shouldFetchCapabilities,
    keepPreviousData: false,
  });

  useEffect(() => {
    const loading = isCapabilityListFetching || isLocationCapabilitiesFetching || isBusinessEntityFetching;
    setIsloading(loading);
  }, [isCapabilityListFetching, isLocationCapabilitiesFetching, isBusinessEntityFetching]);

  // Set business entity hierarchy when data is fetched
  useEffect(() => {
    if (businessEntityHierarchyData) {
      setBusinessEntityHierarchy(businessEntityHierarchyData);
    }
  }, [businessEntityHierarchyData]);

  // Set capability list and structure by leg when data is fetched
  useEffect(() => {
    if (capabilityListData) {
      setCapabilityList(capabilityListData);
      const legCapabilityData = structureCapabilitiesByLeg(capabilityListData);
      setLegCapability(legCapabilityData);
    }
  }, [capabilityListData]);

  // Apply filtering logic - centralized function
  const applyFiltering = (
    locationData: LocationCapabilityList[],
    currentViewType: string,
    currentShowAvailable: boolean,
    currentSelectedEntities: any,
  ) => {
    if (currentViewType === VIEW_TYPE.HIERARCHICAL) {
      if (businessEntityHierarchy) {
        const filteredHierarchy = filterHierarchyTree(
          businessEntityHierarchy,
          locationData,
          capabilityList?.capabilities?.map((x) => x.capability) ?? [],
          currentShowAvailable,
          currentSelectedEntities?.map((entity: any) => Number(entity.id)),
        );
        setMergedBusinessEntityHierarchy(filteredHierarchy);
      }
    } else if (currentViewType === VIEW_TYPE.FLAT) {
      if (currentSelectedEntities && currentSelectedEntities.length > 0) {
        var allChildEntities = getChildrenByLevels(
          businessEntityHierarchy,
          currentSelectedEntities?.map((entity: any) => Number(entity.id)),
        );
        const filteredLocationCapabilities = locationData.filter(
          (x) => allChildEntities.includes(x.entityId) && (x.capabilities.length > 0 || !currentShowAvailable),
        );
        setFilterLocationCapabilityList(filteredLocationCapabilities);
      } else {
        const filteredLocationCapabilities = locationData.filter(
          (x) => x.capabilities.length > 0 || !currentShowAvailable,
        );
        setFilterLocationCapabilityList(filteredLocationCapabilities);
      }
    }
  };

  // Handle location capabilities data when fetched
  useEffect(() => {
    if (locationCapabilitiesData) {
      setLocationCapabilityList(locationCapabilitiesData);
      setFilterLocationCapabilityList(locationCapabilitiesData);

      // Apply filtering with current state
      applyFiltering(locationCapabilitiesData, viewType, showAvailable, selectedBusinessEntities);
    }
  }, [locationCapabilitiesData]);

  // Handle filter changes
  useEffect(() => {
    if (!businessEntityHierarchy || !capabilityList) return;

    const selectedEntities = Object.entries(filters)
      .filter(([key]) => ['region', 'cluster', 'country', 'area'].includes(key))
      .flatMap(([_, values]) => values);

    setSelectedBusinessEntities(selectedEntities);

    const skipFilter = ['region', 'cluster', 'country', 'area'];
    const skipFetch = Object.keys(filters).every((key) => skipFilter.includes(key));

    // If we skip fetch, apply local filtering
    if (!reset && skipFetch && locationCapabilityList.length > 0) {
      applyFiltering(locationCapabilityList, viewType, showAvailable, selectedEntities);
    }
  }, [filters, businessEntityHierarchy, capabilityList, locationCapabilityList, showAvailable, reset]);

  // Handle showAvailable changes
  useEffect(() => {
    if (businessEntityHierarchy && capabilityList && locationCapabilityList.length > 0) {
      // Use fresh data if available, otherwise use state data
      const dataToUse = locationCapabilitiesData || locationCapabilityList;
      applyFiltering(dataToUse, viewType, showAvailable, selectedBusinessEntities);
    }
  }, [showAvailable, viewType]);

  function getChildrenByLevels(root: TreeViewDataType | any, entityIds?: number[]): number[] {
    const result = new Set<number>();

    const collectAllChildIds = (node: TreeViewDataType) => {
      result.add(Number(node.id));
      for (const child of node.children || []) {
        collectAllChildIds(child);
      }
    };

    const traverse = (node: TreeViewDataType) => {
      if (entityIds?.includes(Number(node.id))) {
        collectAllChildIds(node); // collect this node and all its children
      } else {
        for (const child of node.children || []) {
          traverse(child);
        }
      }
    };

    traverse(root);
    return Array.from(result);
  }

  // Handle show available toggle - SIMPLIFIED VERSION
  const handleShowAvailableChange = (newShowAvailable: any) => {
    setShowAvailable(newShowAvailable);
    // The useEffect foor shwAvailable will handle the filtering
  };

  const handleViewChange = (viewType: any) => {
    setViewType(viewType);
    // The useEffect foor shwAvailable will handle the filtering
  };

  function structureCapabilitiesByLeg(responseData: CapabilityList) {
    const result: any = {
      id: responseData.id,
      title: responseData.title,
      description: responseData.description,
      legs: [],
    };

    const legMap = new Map();

    responseData.capabilities.forEach((capability) => {
      capability.legs.forEach((leg) => {
        const simplifiedCapability = {
          id: capability.id,
          product: capability.product.trim(),
          category: capability.category.title,
          subCategory: capability.subCategory || null,
          capability: capability.capability,
          capabilityType: capability.capabilityType,
          level: capability.level,
          verticals: capability.verticals,
        };

        if (!legMap.has(leg.code)) {
          legMap.set(leg.code, {
            shortName: leg.shortName,
            fullName: leg.fullName,
            code: leg.code,
            capabilities: [],
          });
        }

        legMap.get(leg.code).capabilities.push(simplifiedCapability);
      });
    });

    const legColor = ['#1e1450', '#3e3c90', '#5154b6', '#b8b6eb', '#ececff'];
    result.legs = Array.from(legMap.values());
    result.legs = _.sortBy(result.legs, (x) => x.code, 'asc');

    result.legs?.forEach((x: any, index: any) => {
      x['color'] = legColor[index];
    });

    return result;
  }

  function filterHierarchyTree(
    root: TreeViewDataType,
    locationCapabilityList: LocationCapabilityList[],
    targetCapabilityNames: string[],
    showOnlyAvailable?: boolean,
    visibleHierarchyIds?: number[],
  ): TreeViewDataType | null {
    const locationMap = new Map<number, LocationCapabilityList[]>();
    for (const loc of locationCapabilityList) {
      if (!locationMap.has(loc.entityId)) {
        locationMap.set(loc.entityId, []);
      }
      locationMap.get(loc.entityId)!.push(loc);
    }

    const parentMap = new Map<number, TreeViewDataType>();
    const buildParentMap = (node: TreeViewDataType) => {
      for (const child of node.children || []) {
        parentMap.set(Number(child.id), node);
        buildParentMap(child);
      }
    };
    buildParentMap(root);

    const findNodeById = (node: TreeViewDataType, id: number): TreeViewDataType | null => {
      if (Number(node.id) === id) return node;
      for (const child of node.children || []) {
        const found = findNodeById(child, id);
        if (found) return found;
      }
      return null;
    };

    const getAllDescendantIds = (node: TreeViewDataType): Set<number> => {
      const ids = new Set<number>([Number(node.id)]);
      for (const child of node.children || []) {
        for (const id of getAllDescendantIds(child)) ids.add(id);
      }
      return ids;
    };

    const getAllAncestorIds = (node: TreeViewDataType): Set<number> => {
      const ids = new Set<number>();
      let current = node;
      while (current.parentId !== undefined && parentMap.has(Number(current.parentId))) {
        const parent = parentMap.get(Number(current.parentId))!;
        ids.add(Number(parent.id));
        current = parent;
      }
      return ids;
    };

    const validHierarchyIds = new Set<number>();
    if (visibleHierarchyIds?.length) {
      for (const id of visibleHierarchyIds) {
        const node = findNodeById(root, id);
        if (node) {
          getAllDescendantIds(node).forEach((id) => validHierarchyIds.add(id));
          getAllAncestorIds(node).forEach((id) => validHierarchyIds.add(id));
        }
      }
    }

    const filterNode = (node: TreeViewDataType): TreeViewDataType | null => {
      const nodeId = Number(node.id);
      const rawLocations = locationMap.get(nodeId) || [];

      const finalLocations = showOnlyAvailable
        ? rawLocations.filter((loc) =>
            loc.capabilities.some((cap) => targetCapabilityNames.includes(cap.capabilityDetail?.capability ?? '')),
          )
        : rawLocations;

      // Only count capabilities with status === EXISTING for capabilityList
      const capabilityCountMap: Map<string, number> = new Map();
      for (const loc of finalLocations) {
        for (const cap of loc.capabilities) {
          const capName = cap.capabilityDetail?.capability;
          if (cap.status === CAPABILITY_STATUS_ENUM.EXISTING && capName && targetCapabilityNames.includes(capName)) {
            capabilityCountMap.set(capName, (capabilityCountMap.get(capName) || 0) + 1);
          }
        }
      }

      const filteredChildren: TreeViewDataType[] = [];
      let totalLocationCount = finalLocations.length;
      const childCapabilityMap: Map<string, number> = new Map(capabilityCountMap);

      for (const child of node.children || []) {
        const filteredChild = filterNode(child);
        if (filteredChild) {
          filteredChildren.push(filteredChild);
          totalLocationCount += filteredChild.locationCount;

          // Merge child capabilities
          for (const cap of filteredChild.capabilityList || []) {
            childCapabilityMap.set(cap.capability, (childCapabilityMap.get(cap.capability) || 0) + cap.count);
          }
        }
      }

      const isNodeInVisibleList = !visibleHierarchyIds?.length || validHierarchyIds.has(nodeId);
      const shouldInclude = isNodeInVisibleList || filteredChildren.length > 0;

      if (!shouldInclude || (showOnlyAvailable && childCapabilityMap.size === 0)) return null;

      const capabilityList = Array.from(childCapabilityMap.entries()).map(([capability, count]) => ({
        capability,
        count,
      }));

      return {
        ...node,
        children: filteredChildren,
        locations: finalLocations,
        capabilityCount: capabilityList.reduce((sum, item) => sum + item.count, 0),
        locationCount: totalLocationCount,
        capabilityList,
      };
    };

    return filterNode(root);
  }

  if (!isLoading && !capabilityList) {
    return (
      <EmptyContent
        filled={false}
        title={t('messages.entity_not_found')}
        sx={{
          py: 7,
          flexShrink: 0,
          width: { xs: 1, md: 1 },
          minHeight: 300,
          position: 'relative',
          overflow: 'unset',
        }}
      />
    );
  }

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <Box sx={{ mb: 2 }}>
        <Card sx={{ borderRadius: '7px', border: 'solid 1px #E8D6D6', pr: 1 }}>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent={'space-between'} alignItems={'center'}>
            <CustomBreadcrumbs
              heading={capabilityList?.title}
              links={[
                { name: t('label.home'), href: paths.root },
                { name: capabilityList?.title, href: paths.bingoCard.root },
              ]}
              sx={{}}
            />
            <Stack direction={'row'}>
              <Box
                component="span"
                display="flex"
                alignItems="center"
                sx={{ cursor: 'pointer', gap: 1, mr: 1 }}
                alignSelf={'center'}
                onClick={() => navigate(-1)}
              >
                <Box component="img" src={getIcon('backBtn')} sx={{ width: 24, height: 16 }} />
                <Typography variant="value">{t('btn_name.back')}</Typography>
              </Box>
              <Button
                onClick={() => setShowFilterDrawer(true)}
                variant="outlined"
                size={isSmallScreen ? 'small' : 'medium'}
                sx={{
                  bgcolor: 'white',
                  borderRadius: '24px',
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                  minWidth: 'auto',
                  transition: 'all 0.2s ease',
                }}
                startIcon={<SvgColor sx={{ height: 16 }} src={getIcon('Filter-B')} />}
              >
                {t('label.filters')}
              </Button>
            </Stack>
          </Stack>
          <Typography component="div" sx={{ pl: 3, pb: 1 }}>
            {capabilityList?.description}
          </Typography>
        </Card>
      </Box>
      {
        <BingoCardComponent
          showFilterDrawer={showFilterDrawer}
          filters={filters}
          presentCapabilties={capabilityListData?.capabilities}
          setFilters={setFilters}
          setShowFilterDrawer={(show) => setShowFilterDrawer(show)}
          legCapabilityGroup={legCapability}
          locationCapabilityList={filterLocationCapabilityList}
          businessEntityHierarchy={mergedBusinessEntityHierarchy}
          onShowAvailableChange={(show) => handleShowAvailableChange(show)}
          rootBusinessEntityId={businessEntityHierarchy?.id}
          setReset={setReset}
          isLoading={isLoading}
          onViewChange={(viewType) => handleViewChange(viewType)}
          viewType={viewType}
        />
      }
    </Container>
  );
};

export default BingoCardView;
