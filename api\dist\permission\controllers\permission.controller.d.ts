import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { AllAccessControlConfigResponseDto, CreateUserPermissionRequestDto, PaginatedUserPermissionResponseDto, PermissionResponseDto } from '../dtos';
import { PermissionService } from '../services';
export declare class PermissionController {
    readonly permissionService: PermissionService;
    constructor(permissionService: PermissionService);
    getListOfUserPermissions(request: RequestContext): Promise<PermissionResponseDto[]>;
    getAllAccessControlConfigs(): Promise<AllAccessControlConfigResponseDto[]>;
    getLocalLocationBasedPermissions(request: RequestContext, page?: number, limit?: number, orderBy?: string, orderDirection?: string, loginid?: string, configGroupId?: number, locationId?: number): Promise<PaginatedUserPermissionResponseDto>;
    createUserPermission(request: RequestContext, data: CreateUserPermissionRequestDto): Promise<MessageResponseDto>;
    deleteUserPermission(request: RequestContext, id: number): Promise<MessageResponseDto>;
    getPeopleSearch(loginId: string): Promise<MessageResponseDto>;
}
