"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SetupNewLocationRequest = void 0;
const repositories_1 = require("../../metadata/repositories");
const common_1 = require("@nestjs/common");
const clients_1 = require("../../shared/clients");
const exceptions_1 = require("../../shared/exceptions");
const enums_1 = require("../../shared/enums");
let SetupNewLocationRequest = class SetupNewLocationRequest {
    constructor(adminApiClient, coreSolutionRepository) {
        this.adminApiClient = adminApiClient;
        this.coreSolutionRepository = coreSolutionRepository;
    }
    validate(requestPayload, hasLegalPermission) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId, coreSolutionId, locationTypeId } = requestPayload;
            const entityDetail = yield this.getEntityDetailIfValid(entityId);
            const coreSolutionDetail = yield this.coreSolutionRepository.getCoreSolutionWithLoTypeById(coreSolutionId);
            if (!coreSolutionDetail) {
                throw new exceptions_1.HttpException('Capability not found.', enums_1.HttpStatus.NOT_FOUND);
            }
            const locationTypeDetail = coreSolutionDetail.locationTypes.find(locationType => {
                return locationType.id === locationTypeId;
            });
            if (!locationTypeDetail) {
                throw new exceptions_1.HttpException('Invalid location type for selected capability.', enums_1.HttpStatus.NOT_FOUND);
            }
            const validSections = (_a = locationTypeDetail === null || locationTypeDetail === void 0 ? void 0 : locationTypeDetail.locationFormSections) !== null && _a !== void 0 ? _a : [];
            this.validateSection(locationTypeDetail, validSections, requestPayload, hasLegalPermission);
            this.conditionalInputValidation(requestPayload, coreSolutionDetail, locationTypeDetail);
            return {
                status: true,
                message: 'Request is valid',
                data: {
                    entityDetail,
                },
            };
        });
    }
    getEntityDetailIfValid(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const entityDetail = yield this.adminApiClient.getBusinessEntityDetailsById(entityId);
            if (!entityDetail) {
                throw new exceptions_1.HttpException('Entity not found.', enums_1.HttpStatus.NOT_FOUND);
            }
            if (![enums_1.HIERARCHY_ENTITY_TYPE.COUNTRY, enums_1.HIERARCHY_ENTITY_TYPE.AREA].includes(entityDetail.entity_type)) {
                throw new exceptions_1.HttpException('Location can be setup only for country or area.', enums_1.HttpStatus.BAD_REQUEST);
            }
            return entityDetail;
        });
    }
    validateSection(locationTypeDetail, validSections, requestPayload, hasLegalPermission) {
        const { competencyDetails, agentDetail, contractDetail, locationLifecycleManagement, additionalDetails, } = requestPayload;
        if (competencyDetails && !locationTypeDetail.canAcquireCapability) {
            throw new exceptions_1.HttpException(`Competency detail is not allowed for ${locationTypeDetail.title} as it doesn't have entry acquisition enabled.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (competencyDetails && !validSections.includes(enums_1.LOCATION_FORM_SECTION.COMPETENCE_DETAILS)) {
            throw new exceptions_1.HttpException(`Competency detail is not allowed for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (!validSections.includes(enums_1.LOCATION_FORM_SECTION.AGENT_DETAILS) && agentDetail) {
            throw new exceptions_1.HttpException(`Cannot add agent detail for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (hasLegalPermission &&
            validSections.includes(enums_1.LOCATION_FORM_SECTION.AGENT_DETAILS) &&
            !agentDetail) {
            throw new exceptions_1.HttpException(`Agent detail is required for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (!validSections.includes(enums_1.LOCATION_FORM_SECTION.CONTRACT_DETAILS) && contractDetail) {
            throw new exceptions_1.HttpException(`Cannot add contract detail for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (!validSections.includes(enums_1.LOCATION_FORM_SECTION.LOCATION_LIFE_CYCLE_MANAGEMENT) &&
            locationLifecycleManagement) {
            throw new exceptions_1.HttpException(`Cannot add location life cycle management detail for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (validSections.includes(enums_1.LOCATION_FORM_SECTION.LOCATION_LIFE_CYCLE_MANAGEMENT) &&
            !locationLifecycleManagement) {
            throw new exceptions_1.HttpException(`Location life cycle management detail is required for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (!validSections.includes(enums_1.LOCATION_FORM_SECTION.ADDITIONAL_DETAILS) && additionalDetails) {
            throw new exceptions_1.HttpException(`Cannot add additional detail for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
    }
    conditionalInputValidation(requestPayload, coreSolutionDetail, locationTypeDetail) {
        var _a;
        const { branchArchetypeCode, basicInformation, locationInformation, additionalDetails } = requestPayload;
        if (coreSolutionDetail.code == enums_1.CORES_SOLUTION_ENUM.FF &&
            locationTypeDetail.code == enums_1.LOCATION_TYPE_ENUM.BRANCH_OFFICE &&
            !branchArchetypeCode) {
            throw new exceptions_1.HttpException('Branch archetype is required for freight forwarding branch office.', enums_1.HttpStatus.BAD_REQUEST);
        }
        if (coreSolutionDetail.code !== enums_1.CORES_SOLUTION_ENUM.PNT && locationTypeDetail.code !== enums_1.LOCATION_TYPE_ENUM.AGENT && !(basicInformation === null || basicInformation === void 0 ? void 0 : basicInformation.legalEntityid)) {
            throw new exceptions_1.HttpException(`Legal entity is required for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (locationTypeDetail.code === enums_1.LOCATION_TYPE_ENUM.AGENT && (basicInformation === null || basicInformation === void 0 ? void 0 : basicInformation.legalEntityid)) {
            throw new exceptions_1.HttpException(`Legal entity can't be added for ${locationTypeDetail.title}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (((_a = locationInformation === null || locationInformation === void 0 ? void 0 : locationInformation.locationIds) === null || _a === void 0 ? void 0 : _a.length) > 0 &&
            !(coreSolutionDetail.code === enums_1.CORES_SOLUTION_ENUM.FF &&
                locationTypeDetail.code == enums_1.LOCATION_TYPE_ENUM.BRANCH_OFFICE)) {
            throw new exceptions_1.HttpException(`Location IDs are only applicable for Freight Forwarding Branch Office.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (coreSolutionDetail.code === enums_1.CORES_SOLUTION_ENUM.FF &&
            (!(locationInformation === null || locationInformation === void 0 ? void 0 : locationInformation.brands) || locationInformation.brands.length === 0)) {
            throw new exceptions_1.HttpException('Brands are required for Freight Forwarding.', enums_1.HttpStatus.BAD_REQUEST);
        }
        if (basicInformation.locationStatus !== enums_1.LOCATION_STATUS_TYPE.OTHER &&
            basicInformation.locationStatus !== enums_1.LOCATION_STATUS_TYPE.IN_ACTIVE &&
            !basicInformation.statusDate) {
            throw new exceptions_1.HttpException('Status date is required.', enums_1.HttpStatus.BAD_REQUEST);
        }
        if (basicInformation.locationStatus === enums_1.LOCATION_STATUS_TYPE.OTHER &&
            !basicInformation.locationStatusComment) {
            throw new exceptions_1.HttpException('Status Comment is required.', enums_1.HttpStatus.BAD_REQUEST);
        }
        if (coreSolutionDetail.code == enums_1.CORES_SOLUTION_ENUM.FF &&
            locationTypeDetail.code == enums_1.LOCATION_TYPE_ENUM.BRANCH_OFFICE &&
            !branchArchetypeCode) {
            throw new exceptions_1.HttpException('Branch archetype is required for freight forwarding branch office.', enums_1.HttpStatus.BAD_REQUEST);
        }
        if ((coreSolutionDetail.code == enums_1.CORES_SOLUTION_ENUM.CL ||
            coreSolutionDetail.code == enums_1.CORES_SOLUTION_ENUM.MA) &&
            (!(additionalDetails === null || additionalDetails === void 0 ? void 0 : additionalDetails.customer) || additionalDetails.customer.trim() === '')) {
            throw new exceptions_1.HttpException('Customer details are required for Contract Logistics and Market Access solutions.', enums_1.HttpStatus.BAD_REQUEST);
        }
    }
};
SetupNewLocationRequest = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        repositories_1.CoreSolutionRepository])
], SetupNewLocationRequest);
exports.SetupNewLocationRequest = SetupNewLocationRequest;
//# sourceMappingURL=setup-new-location-request-validation.js.map