// ----------------------------------------------------------------------

import usePermission from '@/core/hooks/userPermission';
import { useTranslate } from '@/locales/use-locales';
import { paths } from '@/routes/paths';
import { PERMISSIONS } from '@/shared/enum/permission.enum';
import { useMemo } from 'react';

export const HEADER = {
  H_MOBILE: 64,
  H_DESKTOP: 80,
  H_DESKTOP_OFFSET: 80 - 16,
};

export const NAV = {
  W_VERTICAL: 280,
  W_MINI: 88,
};
export function useNavData() {
  const { t } = useTranslate();
  const { isPermissionGranted } = usePermission();
  const isGlobalAdmin = isPermissionGranted(PERMISSIONS.GLOBAL_MANAGE);
  const isAppAdmin = isPermissionGranted(PERMISSIONS.APPLICATION_ADMIN);

  const navItems = [
    { label: 'label.home', path: paths.home.root },
    { label: 'label.master_setup', path: paths.locationSetup.root },
    { label: 'label.capabilities', path: paths.capabilities.root },
    { label: 'label.locations', path: paths.locations.root },
    { label: 'label.reports', path: paths.reports.root },
    { label: 'headings.people_profile', path: paths.peopleProfile.root },
  ];
  if (isAppAdmin) navItems.push({ label: 'headings.master_capabilities', path: paths.masterCapabilities.root });
  if (isGlobalAdmin) navItems.push({ label: 'label.user_access', path: paths.userAccess.root });

  const data = useMemo(
    () => [
      {
        items: navItems.map((item) => ({
          title: t(item.label),
          path: item.path,
          // icon: ICONS.external, // Placeholder icon, replace as needed
        })),
      },
    ],
    [t],
  );

  return data;
}
