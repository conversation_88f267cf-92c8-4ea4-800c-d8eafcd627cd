import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/shared/repositories';
import { LegalEntity, Location, LocationIndustryVertical } from '../models';
import { cast, col, fn, json, literal, Op, where, WhereOptions } from 'sequelize';
import { CommonDropdown, CoreSolution, LocationType } from 'src/metadata/models';
import { CurrentContext } from 'src/shared/types';
import { LocationFilterRequestDto } from '../dtos/request/location-filter-request.dto';
import { ContactDetail } from 'src/contact-details/models';
import { CapabilityCategory, LocationWiseCapabilityDetail, MasterCapability } from 'src/capability/models';
import { FilterRequestDto } from 'src/bingo-card/dtos';

@Injectable()
export class LocationRepository extends BaseRepository<Location> {
    constructor() {
        super(Location);
    }

    public async getAllLocations(searchTerm: string): Promise<Location[]> {
        const trimmed = searchTerm.trim().toLowerCase().replace(/'/g, "''");

        const whereClause = trimmed
            ? {
                [Op.or]: [
                    { locationName: { [Op.iLike]: `%${trimmed}%` } },
                    literal(`LOWER(country_detail->>'entityTitle') LIKE '%${trimmed}%'`),
                    literal(`LOWER(country_detail->>'entityCode') LIKE '%${trimmed}%'`)
                ],
            }
            : {};

        return this.findAll({
            where: whereClause,
            attributes: ['id', 'locationName', 'entityTitle', 'status', 'statusDate', 'leaseOwnershipStatus', 'countryDetail'],
            include: [
                {
                    model: LocationType,
                    as: 'locationType',
                    attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CoreSolution,
                    as: 'coreSolution',
                    attributes: ['id', 'title', 'code', 'pillar'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
            ],
            order: [['locationName', 'DESC']]
        });
    }

    public async getLocationNameById(id: number): Promise<Location | null> {
        return this.findOne({
            where: { id },
            attributes: ['id', 'locationName']
        });
    }

    public async getLocationById(id: number): Promise<Location | null> {
        return this.findOne({
            where: { id },
            include: [
                {
                    model: LocationType,
                    as: 'locationType',
                    attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CoreSolution,
                    as: 'coreSolution',
                    attributes: ['id', 'title', 'code', 'pillar'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
            ]
        });
    }

    public async getLocationDetailForUpdate(locationId: number): Promise<Location | null> {
        return this.findOne({
            where: { id: locationId },
            include: [
                {
                    model: LocationIndustryVertical,
                    as: 'locationIndustryVerticals',
                    attributes: ['id', 'industryVerticalCode', 'expertUsers'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    }
                },
                {
                    model: ContactDetail,
                    as: 'contacts',
                    attributes: ['id', 'objectId', 'objectType', 'userDetails'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    }
                },
            ]
        });
    }

    public async getCompleteLocationDetailById(id: number): Promise<Location | null> {
        return this.findOne({
            where: { id },
            include: [
                {
                    model: LocationType,
                    as: 'locationType',
                    attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CoreSolution,
                    as: 'coreSolution',
                    attributes: ['id', 'title', 'code', 'pillar'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: LegalEntity,
                    as: 'legalEntity',
                    attributes: ['id', 'name'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CommonDropdown,
                    as: 'branchArchetype',
                    attributes: ['id', 'title', 'code'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CommonDropdown,
                    as: 'strategicClassification',
                    attributes: ['id', 'title', 'code'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
            ]
        });
    }

    public async isLocationExit(id: number): Promise<boolean> {
        return this.isRecordExist({ where: { id } });
    }

    public async getLocationDetailById(id: number): Promise<Location | null> {
        return this.findOne({
            where: { id },
            attributes: ['id', 'locationName', 'entityId', 'entityTitle', 'coreSolutionId', 'locationTypeId', 'status'],
        });
    }

    public async isLocationNameExit(entityId: number, locationName: string, id?: number): Promise<boolean> {
        return this.isRecordExist({
            where: {
                entityId,
                locationName,
                ...(id && { id: { [Op.ne]: id } })
            }
        });
    }

    public async createLocation(payload: any, currentContext: CurrentContext): Promise<Location> {
        const location = new Location(payload);
        return this.save(location, currentContext);
    }

    public updateLocationDetailById(
        locationId: any,
        data,
        currentContext: CurrentContext,
    ): Promise<number | null> {
        return this.update(data, currentContext, {
            where: {
                id: locationId
            },
        });
    }

    public async getLocationsByFilter(
        filter: LocationFilterRequestDto,
        page: number,
        limit: number,
        orderBy: string,
        orderDirection: string,
    ): Promise<{ rows: Location[], count: number }> {
        const whereClause = this.buildWhereClause(filter);

        console.log(this.getOrderByColumn(orderBy));
        console.log(this.getOrderByDirection(orderDirection));

        return this.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: LocationType,
                    as: 'locationType',
                    attributes: ['id', 'title', 'code', 'canAcquireCapability'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CoreSolution,
                    as: 'coreSolution',
                    attributes: ['id', 'title', 'code', 'pillar'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CommonDropdown,
                    as: 'branchArchetype',
                    attributes: ['id', 'title', 'code'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CommonDropdown,
                    as: 'strategicClassification',
                    attributes: ['id', 'title', 'code'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                // ...(filter?.verticalCodes?.length ? [{
                //     model: LocationIndustryVertical,
                //     as: 'locationIndustryVerticals',
                //     attributes: [],
                //     required: true,
                //     where: {
                //         active: true,
                //         deleted: false,
                //         industryVerticalCode: {
                //             [Op.in]: filter.verticalCodes,
                //         },
                //     },
                // }] : []),
                {
                    model: LocationIndustryVertical,
                    as: 'locationIndustryVerticals',
                    attributes: ['id', 'expertUsers'],
                    required: !!(filter.verticalCodes?.length),
                    where: filter?.verticalCodes?.length ? {
                        active: true,
                        deleted: false,
                        industryVerticalCode: {
                            [Op.in]: filter.verticalCodes,
                        },
                    } : {
                        active: true,
                        deleted: false,
                    },
                    include: [
                        {
                            model: CommonDropdown,
                            as: 'industryVertical',
                            attributes: ['id', 'title', 'code'],
                            required: false,
                            where: {
                                active: true,
                                deleted: false,
                            },
                        },
                    ]
                },
                // {
                //     model: LocationIndustryVertical,
                //     as: 'locationIndustryVerticals',
                //     attributes: ['id', 'expertUsers'],
                //     required: false,
                //     where: {
                //         active: true,
                //         deleted: false,
                //     },
                //     include: [
                //         {
                //             model: CommonDropdown,
                //             as: 'industryVertical',
                //             attributes: ['id', 'title', 'code'],
                //             required: false,
                //             where: { active: true, deleted: false },
                //         },
                //     ],
                // },
            ],
            ...(page && { offset: (page - 1) * limit }),
            ...(limit && { limit }),
            order: [[
                this.getOrderByColumn(orderBy),
                this.getOrderByDirection(orderDirection)
            ]],
            // subQuery: false,
            logging: console.log,
            distinct: true,
        });
    }

    private getOrderByColumn(orderBy: string) {
        switch (orderBy) {
            case 'locationName':
                return 'locationName';
            case 'locationStatus':
                return literal(`"Location"."status"::text`);
            case 'locationType':
                return literal(`"locationType.title"`);
            case 'coreSolution':
                return literal(`"coreSolution.title"`);
            // case 'leaseOwnershipStatus':
            //     return literal(`"Location"."lease_ownership_status"::text`);
            // case 'strategicClassification':
            //     return literal(`"strategicClassification.title"`);
            default:
                return 'updatedOn';
        }
    }

    private getOrderByDirection(orderByDirection: string) {
        switch (orderByDirection) {
            case 'asc':
            case 'ASC':
                return 'ASC';
            case 'desc':
            case 'DESC':
                return 'DESC';
            default:
                return 'DESC';
        }
    }


    private buildWhereClause(filter: LocationFilterRequestDto): WhereOptions {

        const { entityIds, branchArchetypeCodes, brandIds, coreSolutionIds, leaseOwnershipStatuses, locationTypeIds, statuses, strategicClassifications } = filter;

        const whereClause: WhereOptions = {};
        const andConditions: WhereOptions[] = [];

        if (entityIds && entityIds.length) {
            whereClause.entityId = { [Op.in]: entityIds };
        }

        if (coreSolutionIds && coreSolutionIds.length) {
            whereClause.coreSolutionId = { [Op.in]: coreSolutionIds };
        }

        if (locationTypeIds && locationTypeIds.length) {
            whereClause.locationTypeId = { [Op.in]: locationTypeIds };
        }

        if (statuses && statuses.length) {
            whereClause.status = { [Op.in]: statuses };
        }

        if (leaseOwnershipStatuses && leaseOwnershipStatuses.length) {
            whereClause.leaseOwnershipStatus = { [Op.in]: leaseOwnershipStatuses };
        }

        if (branchArchetypeCodes && branchArchetypeCodes.length) {
            whereClause.branchArchetypeCode = { [Op.in]: branchArchetypeCodes };
        }

        if (brandIds?.length) {
            const brandConditions = brandIds.map(brandId => ({
                brands: { [Op.contains]: [{ id: brandId }] }
            }));
            andConditions.push({ [Op.or]: brandConditions });
        }

        if (strategicClassifications && strategicClassifications.length) {
            whereClause.strategicClassificationCode = { [Op.in]: strategicClassifications };
        }

        if (andConditions.length > 0) {
            if (Object.keys(whereClause).length > 0) {
                andConditions.push(whereClause);
                return { [Op.and]: andConditions };
            } else {
                return { [Op.and]: andConditions };
            }
        }

        return whereClause;
    }

    public async deleteLocation(id: number, currentContext: CurrentContext) {
        return this.deleteByCondition({ id }, currentContext);
    }

    public async isLegalEntityAddedInLocation(legalEntityId: number): Promise<boolean> {
        return this.isRecordExist({ where: { legalEntityId } });
    }

    public async getLocationsToExport(
        filter: LocationFilterRequestDto
    ): Promise<Location[]> {
        const whereClause = this.buildWhereClause(filter);

        return this.findAll({
            where: whereClause,
            include: [
                {
                    model: LocationType,
                    as: 'locationType',
                    attributes: ['title'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CoreSolution,
                    as: 'coreSolution',
                    attributes: ['title'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: LegalEntity,
                    as: 'legalEntity',
                    attributes: ['name'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CommonDropdown,
                    as: 'branchArchetype',
                    attributes: ['title'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: CommonDropdown,
                    as: 'strategicClassification',
                    attributes: ['title'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
                {
                    model: LocationIndustryVertical,
                    as: 'locationIndustryVerticals',
                    attributes: ['id'],
                    required: !!(filter.verticalCodes?.length),
                    where: filter?.verticalCodes?.length ? {
                        active: true,
                        deleted: false,
                        industryVerticalCode: {
                            [Op.in]: filter.verticalCodes,
                        },
                    } : {
                        active: true,
                        deleted: false,
                    },
                    include: [
                        {
                            model: CommonDropdown,
                            as: 'industryVertical',
                            attributes: ['title'],
                            required: false,
                            where: {
                                active: true,
                                deleted: false,
                            },
                        },
                    ]
                },
            ],
            order: [['updatedOn', 'DESC']],
            // subQuery: false,
            distinct: true,
        });
    }

    public async getLocationsByCondition(condition: any): Promise<Location[]> {
        return this.findAll({
            attributes: ['id', 'locationName', 'entityId', 'status'],
            where: condition,
            include: [
                {
                    model: LocationType,
                    as: 'locationType',
                    attributes: ['title'],
                    required: true,
                    where: {
                        active: true,
                        deleted: false,
                    },
                },
            ],
        });
    }

    public async getLocationsIdsByEntityIds(entityIds: number[]): Promise<Location[]> {
        return this.findAll({
            attributes: ['id', 'locationName', 'entityId'],
            where: {
                entityId: { [Op.in]: entityIds },
            }
        });
    }



    public async getAllLocationsWithCapabilities(capabilityIds: number[], filter: FilterRequestDto) {

        return this.findAll({
            attributes: ['id', 'locationName', 'entityId', 'status'],
            include: [
                {
                    model: LocationWiseCapabilityDetail,
                    as: 'capabilities',
                    attributes: ['id', 'capabilityId', 'status', 'legs'],
                    required: false,
                    where: {
                        active: true,
                        deleted: false,
                        ...(filter?.statuses?.length ? { status: { [Op.in]: filter.statuses } } : {})
                    },
                    include: [
                        {
                            model: MasterCapability,
                            as: 'capabilityDetail',
                            attributes: ['id', 'capability', 'capabilityType', 'legs'],
                            where: {
                                id: { [Op.in]: capabilityIds },
                                active: true,
                                deleted: false,
                                ...(filter?.capabilityTypes?.length && { capabilityType: { [Op.in]: filter.capabilityTypes } }),
                                ...(filter?.capabilityLevel?.length && { level: { [Op.in]: filter.capabilityLevel } }),
                                ...(filter?.coreSolutionIds?.length && { coreSolutionId: { [Op.in]: filter.coreSolutionIds } }),
                                ...(filter?.verticalCodes?.length && {
                                    [Op.and]: [
                                        {
                                            [Op.or]: filter?.verticalCodes.map(code =>
                                                where(
                                                    col('verticals'),
                                                    Op.contains,
                                                    JSON.stringify([code])
                                                )
                                            ),
                                        },
                                        {
                                            verticals: {
                                                [Op.not]: null,
                                            },
                                        },
                                    ],
                                }),
                            },
                            required: true,
                            include: [
                                {
                                    model: CapabilityCategory,
                                    as: 'category',
                                    attributes: ['title'],
                                    required: true,
                                    where: {
                                        active: true,
                                        deleted: false,
                                    }
                                },
                            ],
                        },
                    ],
                },
            ],
            order: [['locationName', 'ASC']],
        });
    }

}
