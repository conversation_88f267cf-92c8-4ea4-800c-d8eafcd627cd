import { Column, DataType, Table } from 'sequelize-typescript';
import { SUPPORT_QUERY_STATUS_ENUM } from 'src/shared/enums';
import { enumToArray } from 'src/shared/helpers';
import { BaseModel } from 'src/shared/models';

@Table({
	tableName: 'data_support_queries',
})
export class SupportQuery extends BaseModel<SupportQuery> {
	@Column({ field: 'url', type: DataType.STRING, allowNull: false })
	public url: string;

	@Column({ field: 'query', type: DataType.STRING, allowNull: false })
	public query: string;
	@Column({
		field: 'status',
		type: DataType.ENUM(...enumToArray(SUPPORT_QUERY_STATUS_ENUM)),
		allowNull: false,
		defaultValue: SUPPORT_QUERY_STATUS_ENUM.NEW,
	})
	public status: SUPPORT_QUERY_STATUS_ENUM;
}
