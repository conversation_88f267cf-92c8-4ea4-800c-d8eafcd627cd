"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonDropdown = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const core_solution_model_1 = require("./core-solution.model");
const location_type_model_1 = require("./location-type.model");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
let CommonDropdown = class CommonDropdown extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], CommonDropdown.prototype, "title", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'code', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], CommonDropdown.prototype, "code", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => core_solution_model_1.CoreSolution),
    (0, sequelize_typescript_1.Column)({ field: 'core_solution_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], CommonDropdown.prototype, "coreSolutionId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => core_solution_model_1.CoreSolution),
    __metadata("design:type", core_solution_model_1.CoreSolution)
], CommonDropdown.prototype, "coreSolution", void 0);
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => location_type_model_1.LocationType),
    (0, sequelize_typescript_1.Column)({ field: 'location_type_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], CommonDropdown.prototype, "locationTypeId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => location_type_model_1.LocationType),
    __metadata("design:type", location_type_model_1.LocationType)
], CommonDropdown.prototype, "locationType", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'other_details', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], CommonDropdown.prototype, "otherDetails", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'type',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.COMMON_DROPDOWN_TYPE)),
        allowNull: false
    }),
    __metadata("design:type", String)
], CommonDropdown.prototype, "type", void 0);
CommonDropdown = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'meta_common_dropdowns',
        indexes: [
            {
                name: 'unique_entry',
                unique: true,
                fields: ['title', 'code', 'core_solution_id', 'location_type_id'],
            },
        ],
    })
], CommonDropdown);
exports.CommonDropdown = CommonDropdown;
//# sourceMappingURL=common-dropdown.model.js.map