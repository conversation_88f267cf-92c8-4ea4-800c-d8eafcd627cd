"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../capability/repositories");
const config_service_1 = require("../../config/config.service");
const services_1 = require("../../core/services");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const services_2 = require("../../shared/services");
const schedular_types_1 = require("../types/schedular.types");
let SchedulerService = class SchedulerService {
    constructor(locationWiseCapabilityRepository, configService, loggerService, mSGraphApiClient, sharedNotificationService) {
        this.locationWiseCapabilityRepository = locationWiseCapabilityRepository;
        this.configService = configService;
        this.loggerService = loggerService;
        this.mSGraphApiClient = mSGraphApiClient;
        this.sharedNotificationService = sharedNotificationService;
        this.THRESHOLD_DAY_LIMIT = schedular_types_1.thresholdDayLimit;
    }
    runScheduler(type) {
        return __awaiter(this, void 0, void 0, function* () {
            this.loggerService.log(`Started the ${type} scheduler.`);
            switch (type) {
                case enums_1.SCHEDULER_TYPE.ENTRIES_NOTIFICATION:
                    yield this.runEntriesNotification();
                    break;
                default:
                    throw Error('Invalid scheduler type. Type should be (ENTRIES_NOTIFICATION).');
            }
            this.loggerService.log(`Ended the ${type} scheduler.`);
        });
    }
    runEntriesNotification() {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        return __awaiter(this, void 0, void 0, function* () {
            const now = new Date();
            const nearExpiryDate = new Date(now.getTime() + this.THRESHOLD_DAY_LIMIT * 24 * 60 * 60 * 1000);
            const entries = yield this.locationWiseCapabilityRepository.getEntriesForScheduler(now, nearExpiryDate);
            const groupedMap = new Map();
            for (const entry of entries) {
                const locId = entry.locationId;
                const location = entry.locationDetail;
                if (!groupedMap.has(locId)) {
                    groupedMap.set(locId, {
                        recipients: new Set(),
                        location,
                        capabilityList: [],
                    });
                }
                const group = groupedMap.get(locId);
                for (const perm of (_a = entry.userPermissions) !== null && _a !== void 0 ? _a : []) {
                    group.recipients.add(perm.loginId);
                }
                const date = new Date(entry.statusDate);
                const daysLeft = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
                const daysAgo = Math.ceil((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
                let type = null;
                if (entry.status === enums_1.CAPABILITY_STATUS_ENUM.EXISTING &&
                    date >= now &&
                    date <= nearExpiryDate) {
                    type = schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.NEAR_EXPIRY;
                }
                else if (entry.status === enums_1.CAPABILITY_STATUS_ENUM.PLANNED &&
                    date >= now &&
                    date <= nearExpiryDate) {
                    type = schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.NEAR_PLANNED;
                }
                else if (entry.status === enums_1.CAPABILITY_STATUS_ENUM.EXPIRED) {
                    type = schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.EXPIRED;
                }
                else if (entry.status === enums_1.CAPABILITY_STATUS_ENUM.PLANNED && date < now) {
                    type = schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.PLANNED_PASSED_AGO;
                }
                group.capabilityList.push(Object.assign(Object.assign({ id: entry.id, locationId: entry.locationId, capabilityId: entry.capabilityId, status: entry.status, internalStatus: entry.internalStatus, statusDate: entry.statusDate, capability: (_b = entry.capabilityDetail) === null || _b === void 0 ? void 0 : _b.capability, subCategory: (_c = entry.capabilityDetail) === null || _c === void 0 ? void 0 : _c.subCategory, product: (_d = entry.capabilityDetail) === null || _d === void 0 ? void 0 : _d.product, capabilityType: (_e = entry.capabilityDetail) === null || _e === void 0 ? void 0 : _e.capabilityType, level: (_f = entry.capabilityDetail) === null || _f === void 0 ? void 0 : _f.level, category: (_h = (_g = entry.capabilityDetail) === null || _g === void 0 ? void 0 : _g.category) === null || _h === void 0 ? void 0 : _h.title, type }, ([
                    schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.NEAR_EXPIRY,
                    schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.NEAR_PLANNED,
                ].includes(type) && { daysLeft: daysLeft > 0 ? daysLeft : 0 })), ([
                    schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.EXPIRED,
                    schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.PLANNED_PASSED_AGO,
                ].includes(type) && { daysAgo: daysAgo > 0 ? daysAgo : 0 })));
            }
            const finalList = Array.from(groupedMap.values()).map(group => {
                var _a, _b, _c, _d, _e;
                const loc = group.location;
                const expiredList = group.capabilityList.filter(cap => cap.type === schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.EXPIRED);
                const plannedDatePassedList = group.capabilityList.filter(cap => cap.type === schedular_types_1.CAPABILITY_STATUS_TYPE_ENUM.PLANNED_PASSED_AGO);
                return {
                    id: loc.id,
                    locationName: loc.locationName,
                    entityId: loc.entityId,
                    entityTitle: loc.entityTitle,
                    entityCode: loc.entityCode,
                    status: loc.status,
                    statusDate: loc.statusDate,
                    coreSolution: (_b = (_a = loc.coreSolution) === null || _a === void 0 ? void 0 : _a.title) !== null && _b !== void 0 ? _b : null,
                    locationType: (_d = (_c = loc.locationType) === null || _c === void 0 ? void 0 : _c.title) !== null && _d !== void 0 ? _d : null,
                    countryDetail: (_e = loc.countryDetail) !== null && _e !== void 0 ? _e : null,
                    recipients: Array.from(group.recipients),
                    expiredList,
                    plannedDatePassedList,
                };
            });
            function formatCategoryInfo(item) {
                const parts = [item.category, item.subCategory, item.product].filter(Boolean);
                if (!parts.length)
                    return '';
                return `<div style="font-size: 12px; color: #555;">${parts.join(' - ')}</div>`;
            }
            console.dir(finalList, { depth: null });
            const config = this.configService.getAppConfig();
            for (const locationGroup of finalList) {
                const { locationName, id: locationId, recipients, expiredList, plannedDatePassedList, } = locationGroup;
                if (!expiredList.length && !plannedDatePassedList.length)
                    continue;
                const expiredListHtml = expiredList.length
                    ? `<h3>Expired Entries</h3>` +
                        (0, helpers_1.jsonToHtmlTable)({
                            data: expiredList.map(item => {
                                var _a, _b;
                                return ({
                                    Capability: `
					<div>
						<a href="${config.uiClient.baseUrl}/capabilities/${item.capabilityId}">
							${item.capability}
						</a>
						${formatCategoryInfo(item)}
					</div>`,
                                    Level: (_a = item.level) !== null && _a !== void 0 ? _a : '',
                                    Type: (_b = item.capabilityType) !== null && _b !== void 0 ? _b : '',
                                    'Expiry Date': item.statusDate,
                                });
                            }),
                            css: (0, helpers_1.getTableCSS)(),
                        })
                    : '';
                const plannedDatePassedListHtml = plannedDatePassedList.length
                    ? `<h3>Planned Date Passed Entries</h3>` +
                        (0, helpers_1.jsonToHtmlTable)({
                            data: plannedDatePassedList.map(item => {
                                var _a, _b;
                                return ({
                                    Capability: `
					<div>
						<a href="${(0, helpers_1.ReplaceUrlVariable)(config.uiClient.baseUrl + enums_1.UI_ROUTES.CAPABILITY_DETAIL, {
                                        capabilityId: item.capabilityId,
                                    })}">
							${item.capability}
						</a>
						${formatCategoryInfo(item)}
					</div>`,
                                    Level: (_a = item.level) !== null && _a !== void 0 ? _a : '',
                                    Type: (_b = item.capabilityType) !== null && _b !== void 0 ? _b : '',
                                    'Planned Date': item.statusDate,
                                });
                            }),
                            css: (0, helpers_1.getTableCSS)(),
                        })
                    : '';
                for (const recipientLoginId of recipients) {
                    const usersDetails = yield this.mSGraphApiClient.getUsersDetails([recipientLoginId]);
                    const userDetail = usersDetails.find(user => {
                        var _a, _b;
                        return ((_a = user.userPrincipalName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === recipientLoginId.toLowerCase() ||
                            ((_b = user.mail) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === recipientLoginId.toLowerCase();
                    });
                    const placeholderValues = {
                        locationName: locationName,
                        userName: (userDetail && (userDetail === null || userDetail === void 0 ? void 0 : userDetail.displayName)) || recipientLoginId,
                        locationNameWithLink: `<a href="${(0, helpers_1.ReplaceUrlVariable)(config.uiClient.baseUrl + enums_1.UI_ROUTES.AVAILABLE_CAPABILITY_LIST, { locationId })}">${locationName}</a>`,
                        expiredList: expiredListHtml,
                        plannedDatePassedList: plannedDatePassedListHtml,
                    };
                    try {
                        if (userDetail && (userDetail === null || userDetail === void 0 ? void 0 : userDetail.mail)) {
                            yield this.sharedNotificationService.sendNotification(-1, enums_1.NOTIFICATION_ENTITY_TYPE.SCHEDULER_ENTRIES_NOTIFICATION, { to: [userDetail && (userDetail === null || userDetail === void 0 ? void 0 : userDetail.mail)] }, enums_1.EMAIL_TEMPLATES.SCHEDULER_ENTRIES_NOTIFICATION, placeholderValues);
                        }
                    }
                    catch (error) {
                        this.loggerService.error(`Failed to send notification to ${userDetail.mail}: ${error === null || error === void 0 ? void 0 : error.message}`, error);
                    }
                }
            }
        });
    }
};
SchedulerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.LocationWiseCapabilityRepository,
        config_service_1.ConfigService,
        services_1.LoggerService,
        clients_1.MSGraphApiClient,
        services_2.SharedNotificationService])
], SchedulerService);
exports.SchedulerService = SchedulerService;
//# sourceMappingURL=scheduler.service.js.map