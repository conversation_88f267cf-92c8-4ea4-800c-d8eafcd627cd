"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartnerBranchController = void 0;
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
const passport_1 = require("@nestjs/passport");
const common_1 = require("@nestjs/common");
const dtos_1 = require("../../shared/dtos");
const dtos_2 = require("../dtos");
const enums_1 = require("../../shared/enums");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
let PartnerBranchController = class PartnerBranchController {
    constructor(partnerBranchService) {
        this.partnerBranchService = partnerBranchService;
    }
    newPartnerBranch(request, createPartnerBranchDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.partnerBranchService.newPartnerBranch(createPartnerBranchDto, request.currentContext);
        });
    }
    actionPartnerConnection(actionDto, request) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.partnerBranchService.actionPartnerConnection(actionDto, request.currentContext);
        });
    }
    getPartnerBranches(id, request) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.partnerBranchService.getPartnerBranches(id, request.currentContext);
        });
    }
};
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Send new partner connection request',
        type: dtos_1.MessageResponseDto
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.CreatePartnerBranchRequestDto]),
    __metadata("design:returntype", Promise)
], PartnerBranchController.prototype, "newPartnerBranch", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Action on partner connection',
        type: dtos_1.MessageResponseDto
    }),
    (0, common_1.Post)('/action'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_2.ActionPartnerConnectionRequestDto, Object]),
    __metadata("design:returntype", Promise)
], PartnerBranchController.prototype, "actionPartnerConnection", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.ANY),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all partner branches by location id',
        type: [dtos_2.PartnerBranchResponseDto]
    }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], PartnerBranchController.prototype, "getPartnerBranches", null);
PartnerBranchController = __decorate([
    (0, swagger_1.ApiTags)('Partner Branche APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('partner-branch'),
    __metadata("design:paramtypes", [services_1.PartnerBranchService])
], PartnerBranchController);
exports.PartnerBranchController = PartnerBranchController;
//# sourceMappingURL=partner-branch.controller.js.map