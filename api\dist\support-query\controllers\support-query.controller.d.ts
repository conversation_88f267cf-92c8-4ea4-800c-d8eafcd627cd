import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { SupportQueryRequestDto } from '../dtos';
import { SupportQueryService } from '../services';
export declare class SupportQueryController {
    readonly supportQueryService: SupportQueryService;
    constructor(supportQueryService: SupportQueryService);
    createUserSupportQuery(request: RequestContext, data: SupportQueryRequestDto): Promise<MessageResponseDto>;
    getSupportQueryList(page: number, limit: number, requestPayload: any): Promise<import("../dtos").PaginatedSupportQueryListResponseDto>;
}
