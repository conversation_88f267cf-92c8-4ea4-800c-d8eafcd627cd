import { CommonDropdownRepository, CoreSolutionRepository, LocationTypeRepository, LocationLifecycleManagementsTypeRepository } from '../repositories';
import { CoreSolutionCompleteResponseDto, GetMetadataByTypeRequestDto, LocationTypeResponseDto, MetadataByTypeResponseDto, LocationLifecycleTypeResponseDto } from '../dtos';
import { LOCATION_LIFECYCLE_MANAGEMENTS_TYPE } from 'src/shared/enums';
import { LocationRepository } from 'src/location/repositories';
import { LocationWiseCapabilityRepository } from 'src/capability/repositories';
export declare class MetadataService {
    private readonly coreSolutionRepo;
    private readonly locationTypeRepo;
    private readonly commonDropdownRepo;
    private readonly locationLifecycleManagementsRepo;
    private readonly locationRepository;
    private readonly locationWiseCapabilityRepo;
    constructor(coreSolutionRepo: CoreSolutionRepository, locationTypeRepo: LocationTypeRepository, commonDropdownRepo: CommonDropdownRepository, locationLifecycleManagementsRepo: LocationLifecycleManagementsTypeRepository, locationRepository: LocationRepository, locationWiseCapabilityRepo: LocationWiseCapabilityRepository);
    getAllCoreSolutions(): Promise<CoreSolutionCompleteResponseDto[]>;
    getLocationTypesByCoreSolutionId(coreSolutionId: number): Promise<LocationTypeResponseDto[]>;
    getAllLocationTypes(): Promise<LocationTypeResponseDto[]>;
    getAllLocationLifeCycleTypes(): Promise<Record<LOCATION_LIFECYCLE_MANAGEMENTS_TYPE, LocationLifecycleTypeResponseDto[]>>;
    getMetadataByType(requestParms: GetMetadataByTypeRequestDto): Promise<Record<string, MetadataByTypeResponseDto[]>>;
    private validateMetadataByTypeRequestParams;
    private groupResultsByType;
    private transformGroupedResults;
    private transformGroupedResultsOfLocationLifecycleManagements;
    getUniqueTags(type: 'CAPABILITY' | 'LOCATION', searchTerm?: string): Promise<string[]>;
}
