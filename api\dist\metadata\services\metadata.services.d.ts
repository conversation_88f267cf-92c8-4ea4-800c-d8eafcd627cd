import { CommonDropdownRepository, CoreSolutionRepository, LocationTypeRepository, LocationLifecycleManagementsTypeRepository } from '../repositories';
import { CoreSolutionCompleteResponseDto, GetMetadataByTypeRequestDto, LocationTypeResponseDto, MetadataByTypeResponseDto, LocationLifecycleTypeResponseDto } from '../dtos';
import { LOCATION_LIFECYCLE_MANAGEMENTS_TYPE } from 'src/shared/enums';
export declare class MetadataService {
    private readonly coreSolutionRepo;
    private readonly locationTypeRepo;
    private readonly commonDropdownRepo;
    private readonly locationLifecycleManagementsRepo;
    constructor(coreSolutionRepo: CoreSolutionRepository, locationTypeRepo: LocationTypeRepository, commonDropdownRepo: CommonDropdownRepository, locationLifecycleManagementsRepo: LocationLifecycleManagementsTypeRepository);
    getAllCoreSolutions(): Promise<CoreSolutionCompleteResponseDto[]>;
    getLocationTypesByCoreSolutionId(coreSolutionId: number): Promise<LocationTypeResponseDto[]>;
    getAllLocationTypes(): Promise<LocationTypeResponseDto[]>;
    getAllLocationLifeCycleTypes(): Promise<Record<LOCATION_LIFECYCLE_MANAGEMENTS_TYPE, LocationLifecycleTypeResponseDto[]>>;
    getMetadataByType(requestParms: GetMetadataByTypeRequestDto): Promise<Record<string, MetadataByTypeResponseDto[]>>;
    private validateMetadataByTypeRequestParams;
    private groupResultsByType;
    private transformGroupedResults;
    private transformGroupedResultsOfLocationLifecycleManagements;
}
