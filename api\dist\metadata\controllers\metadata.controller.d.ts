import { MetadataService } from '../services';
import { CoreSolutionCompleteResponseDto, GetMetadataByTypeRequestDto, LocationTypeResponseDto, MetadataByTypeResponseDto, LocationLifecycleTypeResponseDto } from '../dtos';
import { LOCATION_LIFECYCLE_MANAGEMENTS_TYPE } from 'src/shared/enums';
export declare class MetadataController {
    private readonly metadataService;
    constructor(metadataService: MetadataService);
    getByCoreSolutionId(): Promise<CoreSolutionCompleteResponseDto[]>;
    getLocationTypesByCoreSolutionId(coreSolutionId: number): Promise<LocationTypeResponseDto[]>;
    getAllLocationTypes(): Promise<LocationTypeResponseDto[]>;
    getAllLocationLifeCycleManagementTypes(): Promise<Record<LOCATION_LIFECYCLE_MANAGEMENTS_TYPE, LocationLifecycleTypeResponseDto[]>>;
    getMetadataByType(requestParms: GetMetadataByTypeRequestDto): Promise<Record<string, MetadataByTypeResponseDto[]>>;
    getUniqueTags(type: 'CAPABILITY' | 'LOCATION', searchTerm?: string): Promise<string[]>;
}
