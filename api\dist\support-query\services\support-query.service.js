"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupportQueryService = void 0;
const common_1 = require("@nestjs/common");
const pagination_1 = require("../../core/pagination");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const repositories_1 = require("../repositories");
let SupportQueryService = class SupportQueryService {
    constructor(adminApiClient, supportQueryRepository, databaseHelper, sharedNotificationService, adminApiServie, mSGraphApiClient) {
        this.adminApiClient = adminApiClient;
        this.supportQueryRepository = supportQueryRepository;
        this.databaseHelper = databaseHelper;
        this.sharedNotificationService = sharedNotificationService;
        this.adminApiServie = adminApiServie;
        this.mSGraphApiClient = mSGraphApiClient;
    }
    createUserSupportQuery(data, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { query, url } = data;
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                try {
                    yield this.supportQueryRepository.createSupportQuery({ url, query }, currentContext);
                    const userDetail = yield this.adminApiServie.getRoleUserListByRoleName(enums_1.USER_EMAIL_GROUP.PRODUCT_SUPPORT_EMAIL_GROUP);
                    const userEmails = [];
                    const userIds = userDetail === null || userDetail === void 0 ? void 0 : userDetail.map(user => user.user_name.toLowerCase());
                    const userAdDetails = userIds.length
                        ? yield this.mSGraphApiClient.getUsersDetails(userIds)
                        : [];
                    userEmails.push(...userAdDetails.map(user => user.mail.toLowerCase()));
                    const placeholderValues = { query };
                    if (userEmails.length) {
                        yield this.sharedNotificationService.sendNotification(-1, enums_1.NOTIFICATION_ENTITY_TYPE.SUPPORT_EMAIL_NOTIFICATION, { to: userEmails }, enums_1.EMAIL_TEMPLATES.SUPPORT_EMAIL_NOTIFICATION, placeholderValues);
                    }
                }
                catch (error) {
                    console.error('Failed to send support notification email:', error);
                }
                return { message: 'Your query raised successfully' };
            }));
        });
    }
    getQueryListByFilter(page, limit, filterDto = {}) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.supportQueryRepository.getQueryListByFilter(filterDto, page, limit);
            return new pagination_1.Pagination({ records: rows, total: count });
        });
    }
};
SupportQueryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        repositories_1.SupportQueryRepository,
        helpers_1.DatabaseHelper,
        services_1.SharedNotificationService,
        clients_1.AdminApiClient,
        clients_1.MSGraphApiClient])
], SupportQueryService);
exports.SupportQueryService = SupportQueryService;
//# sourceMappingURL=support-query.service.js.map