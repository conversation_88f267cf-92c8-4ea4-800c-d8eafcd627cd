"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupportQueryService = void 0;
const common_1 = require("@nestjs/common");
const helpers_1 = require("../../shared/helpers");
const repositories_1 = require("../repositories");
let SupportQueryService = class SupportQueryService {
    constructor(supportQueryRepository, databaseHelper) {
        this.supportQueryRepository = supportQueryRepository;
        this.databaseHelper = databaseHelper;
    }
    createUserSupportQuery(data, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { query, url } = data;
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const supportQuery = yield this.supportQueryRepository.createSupportQuery({
                    url,
                    query,
                }, currentContext);
                return { message: 'Your query raised successfully' };
            }));
        });
    }
};
SupportQueryService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.SupportQueryRepository,
        helpers_1.DatabaseHelper])
], SupportQueryService);
exports.SupportQueryService = SupportQueryService;
//# sourceMappingURL=support-query.service.js.map