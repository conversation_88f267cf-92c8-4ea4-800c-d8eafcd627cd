import {
  ToggleButtonGroupButtonContext_default,
  ToggleButtonGroupContext_default,
  toggleButtonClasses_default
} from "./chunk-VFEZICVW.js";
import {
  Tooltip_default
} from "./chunk-2LW5BDSK.js";
import {
  PaginationItem_default
} from "./chunk-6MBEAKNC.js";
import {
  Fab_default
} from "./chunk-TZC7QVJO.js";
import {
  Typography_default
} from "./chunk-5FONVV63.js";
import {
  getTransitionProps,
  reflow
} from "./chunk-ZOKU5DI5.js";
import {
  useSlot
} from "./chunk-GEWEBP33.js";
import {
  Transition_default
} from "./chunk-KO3MEAAR.js";
import {
  mergeSlotProps
} from "./chunk-ZKV3TKMI.js";
import {
  useControlled_default
} from "./chunk-UGHI33QN.js";
import {
  isMuiElement_default
} from "./chunk-SLNW3ETP.js";
import {
  createSvgIcon
} from "./chunk-BTIOHRX6.js";
import {
  capitalize_default
} from "./chunk-YXVLDLGC.js";
import {
  memoTheme_default
} from "./chunk-5DQNP7OD.js";
import {
  useForkRef_default
} from "./chunk-U27VJAZE.js";
import {
  useDefaultProps
} from "./chunk-WEUG6UQ7.js";
import {
  useTheme
} from "./chunk-E5UMWPXU.js";
import {
  styled_default
} from "./chunk-HGZMMVE4.js";
import {
  clamp_default,
  composeClasses,
  elementAcceptingRef_default,
  emphasize,
  getReactElementRef,
  getValidReactChildren,
  integerPropType_default,
  useControlled,
  useTimeout
} from "./chunk-OLICLHZN.js";
import {
  clsx_default,
  generateUtilityClass,
  generateUtilityClasses
} from "./chunk-UE7CETWW.js";
import {
  require_react_is
} from "./chunk-NUO2DALJ.js";
import {
  require_prop_types
} from "./chunk-J4LPPHPF.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/AlertTitle/alertTitleClasses.js
function getAlertTitleUtilityClass(slot) {
  return generateUtilityClass("MuiAlertTitle", slot);
}
var alertTitleClasses = generateUtilityClasses("MuiAlertTitle", ["root"]);
var alertTitleClasses_default = alertTitleClasses;

// node_modules/@mui/material/AlertTitle/AlertTitle.js
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var useUtilityClasses = (ownerState) => {
  const {
    classes
  } = ownerState;
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getAlertTitleUtilityClass, classes);
};
var AlertTitleRoot = styled_default(Typography_default, {
  name: "MuiAlertTitle",
  slot: "Root",
  overridesResolver: (props, styles2) => styles2.root
})(memoTheme_default(({
  theme
}) => {
  return {
    fontWeight: theme.typography.fontWeightMedium,
    marginTop: -2
  };
}));
var AlertTitle = React.forwardRef(function AlertTitle2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiAlertTitle"
  });
  const {
    className,
    ...other
  } = props;
  const ownerState = props;
  const classes = useUtilityClasses(ownerState);
  return (0, import_jsx_runtime.jsx)(AlertTitleRoot, {
    gutterBottom: true,
    component: "div",
    ownerState,
    ref,
    className: clsx_default(classes.root, className),
    ...other
  });
});
true ? AlertTitle.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object])
} : void 0;
var AlertTitle_default = AlertTitle;

// node_modules/@mui/material/Pagination/paginationClasses.js
function getPaginationUtilityClass(slot) {
  return generateUtilityClass("MuiPagination", slot);
}
var paginationClasses = generateUtilityClasses("MuiPagination", ["root", "ul", "outlined", "text"]);
var paginationClasses_default = paginationClasses;

// node_modules/@mui/material/usePagination/usePagination.js
function usePagination(props = {}) {
  const {
    boundaryCount = 1,
    componentName = "usePagination",
    count = 1,
    defaultPage = 1,
    disabled = false,
    hideNextButton = false,
    hidePrevButton = false,
    onChange: handleChange,
    page: pageProp,
    showFirstButton = false,
    showLastButton = false,
    siblingCount = 1,
    ...other
  } = props;
  const [page, setPageState] = useControlled({
    controlled: pageProp,
    default: defaultPage,
    name: componentName,
    state: "page"
  });
  const handleClick = (event, value) => {
    if (!pageProp) {
      setPageState(value);
    }
    if (handleChange) {
      handleChange(event, value);
    }
  };
  const range = (start, end) => {
    const length = end - start + 1;
    return Array.from({
      length
    }, (_, i) => start + i);
  };
  const startPages = range(1, Math.min(boundaryCount, count));
  const endPages = range(Math.max(count - boundaryCount + 1, boundaryCount + 1), count);
  const siblingsStart = Math.max(
    Math.min(
      // Natural start
      page - siblingCount,
      // Lower boundary when page is high
      count - boundaryCount - siblingCount * 2 - 1
    ),
    // Greater than startPages
    boundaryCount + 2
  );
  const siblingsEnd = Math.min(
    Math.max(
      // Natural end
      page + siblingCount,
      // Upper boundary when page is low
      boundaryCount + siblingCount * 2 + 2
    ),
    // Less than endPages
    count - boundaryCount - 1
  );
  const itemList = [
    ...showFirstButton ? ["first"] : [],
    ...hidePrevButton ? [] : ["previous"],
    ...startPages,
    // Start ellipsis
    // eslint-disable-next-line no-nested-ternary
    ...siblingsStart > boundaryCount + 2 ? ["start-ellipsis"] : boundaryCount + 1 < count - boundaryCount ? [boundaryCount + 1] : [],
    // Sibling pages
    ...range(siblingsStart, siblingsEnd),
    // End ellipsis
    // eslint-disable-next-line no-nested-ternary
    ...siblingsEnd < count - boundaryCount - 1 ? ["end-ellipsis"] : count - boundaryCount > boundaryCount ? [count - boundaryCount] : [],
    ...endPages,
    ...hideNextButton ? [] : ["next"],
    ...showLastButton ? ["last"] : []
  ];
  const buttonPage = (type) => {
    switch (type) {
      case "first":
        return 1;
      case "previous":
        return page - 1;
      case "next":
        return page + 1;
      case "last":
        return count;
      default:
        return null;
    }
  };
  const items = itemList.map((item) => {
    return typeof item === "number" ? {
      onClick: (event) => {
        handleClick(event, item);
      },
      type: "page",
      page: item,
      selected: item === page,
      disabled,
      "aria-current": item === page ? "page" : void 0
    } : {
      onClick: (event) => {
        handleClick(event, buttonPage(item));
      },
      type: item,
      page: buttonPage(item),
      selected: false,
      disabled: disabled || !item.includes("ellipsis") && (item === "next" || item === "last" ? page >= count : page <= 1)
    };
  });
  return {
    items,
    ...other
  };
}

// node_modules/@mui/material/Pagination/Pagination.js
var React2 = __toESM(require_react());
var import_prop_types2 = __toESM(require_prop_types());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var useUtilityClasses2 = (ownerState) => {
  const {
    classes,
    variant
  } = ownerState;
  const slots = {
    root: ["root", variant],
    ul: ["ul"]
  };
  return composeClasses(slots, getPaginationUtilityClass, classes);
};
var PaginationRoot = styled_default("nav", {
  name: "MuiPagination",
  slot: "Root",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [styles2.root, styles2[ownerState.variant]];
  }
})({});
var PaginationUl = styled_default("ul", {
  name: "MuiPagination",
  slot: "Ul",
  overridesResolver: (props, styles2) => styles2.ul
})({
  display: "flex",
  flexWrap: "wrap",
  alignItems: "center",
  padding: 0,
  margin: 0,
  listStyle: "none"
});
function defaultGetAriaLabel(type, page, selected) {
  if (type === "page") {
    return `${selected ? "" : "Go to "}page ${page}`;
  }
  return `Go to ${type} page`;
}
var Pagination = React2.forwardRef(function Pagination2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiPagination"
  });
  const {
    boundaryCount = 1,
    className,
    color = "standard",
    count = 1,
    defaultPage = 1,
    disabled = false,
    getItemAriaLabel = defaultGetAriaLabel,
    hideNextButton = false,
    hidePrevButton = false,
    onChange,
    page,
    renderItem = (item) => (0, import_jsx_runtime2.jsx)(PaginationItem_default, {
      ...item
    }),
    shape = "circular",
    showFirstButton = false,
    showLastButton = false,
    siblingCount = 1,
    size = "medium",
    variant = "text",
    ...other
  } = props;
  const {
    items
  } = usePagination({
    ...props,
    componentName: "Pagination"
  });
  const ownerState = {
    ...props,
    boundaryCount,
    color,
    count,
    defaultPage,
    disabled,
    getItemAriaLabel,
    hideNextButton,
    hidePrevButton,
    renderItem,
    shape,
    showFirstButton,
    showLastButton,
    siblingCount,
    size,
    variant
  };
  const classes = useUtilityClasses2(ownerState);
  return (0, import_jsx_runtime2.jsx)(PaginationRoot, {
    "aria-label": "pagination navigation",
    className: clsx_default(classes.root, className),
    ownerState,
    ref,
    ...other,
    children: (0, import_jsx_runtime2.jsx)(PaginationUl, {
      className: classes.ul,
      ownerState,
      children: items.map((item, index) => (0, import_jsx_runtime2.jsx)("li", {
        children: renderItem({
          ...item,
          color,
          "aria-label": getItemAriaLabel(item.type, item.page, item.selected),
          shape,
          size,
          variant
        })
      }, index))
    })
  });
});
true ? Pagination.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Number of always visible pages at the beginning and end.
   * @default 1
   */
  boundaryCount: integerPropType_default,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types2.default.object,
  /**
   * @ignore
   */
  className: import_prop_types2.default.string,
  /**
   * The active color.
   * It supports both default and custom theme colors, which can be added as shown in the
   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).
   * @default 'standard'
   */
  color: import_prop_types2.default.oneOfType([import_prop_types2.default.oneOf(["primary", "secondary", "standard"]), import_prop_types2.default.string]),
  /**
   * The total number of pages.
   * @default 1
   */
  count: integerPropType_default,
  /**
   * The page selected by default when the component is uncontrolled.
   * @default 1
   */
  defaultPage: integerPropType_default,
  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled: import_prop_types2.default.bool,
  /**
   * Accepts a function which returns a string value that provides a user-friendly name for the current page.
   * This is important for screen reader users.
   *
   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).
   * @param {string} type The link or button type to format ('page' | 'first' | 'last' | 'next' | 'previous' | 'start-ellipsis' | 'end-ellipsis'). Defaults to 'page'.
   * @param {number | null} page The page number to format.
   * @param {boolean} selected If true, the current page is selected.
   * @returns {string}
   */
  getItemAriaLabel: import_prop_types2.default.func,
  /**
   * If `true`, hide the next-page button.
   * @default false
   */
  hideNextButton: import_prop_types2.default.bool,
  /**
   * If `true`, hide the previous-page button.
   * @default false
   */
  hidePrevButton: import_prop_types2.default.bool,
  /**
   * Callback fired when the page is changed.
   *
   * @param {React.ChangeEvent<unknown>} event The event source of the callback.
   * @param {number} page The page selected.
   */
  onChange: import_prop_types2.default.func,
  /**
   * The current page. Unlike `TablePagination`, which starts numbering from `0`, this pagination starts from `1`.
   */
  page: integerPropType_default,
  /**
   * Render the item.
   * @param {PaginationRenderItemParams} params The props to spread on a PaginationItem.
   * @returns {ReactNode}
   * @default (item) => <PaginationItem {...item} />
   */
  renderItem: import_prop_types2.default.func,
  /**
   * The shape of the pagination items.
   * @default 'circular'
   */
  shape: import_prop_types2.default.oneOf(["circular", "rounded"]),
  /**
   * If `true`, show the first-page button.
   * @default false
   */
  showFirstButton: import_prop_types2.default.bool,
  /**
   * If `true`, show the last-page button.
   * @default false
   */
  showLastButton: import_prop_types2.default.bool,
  /**
   * Number of always visible pages before and after the current page.
   * @default 1
   */
  siblingCount: integerPropType_default,
  /**
   * The size of the component.
   * @default 'medium'
   */
  size: import_prop_types2.default.oneOfType([import_prop_types2.default.oneOf(["small", "medium", "large"]), import_prop_types2.default.string]),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types2.default.oneOfType([import_prop_types2.default.arrayOf(import_prop_types2.default.oneOfType([import_prop_types2.default.func, import_prop_types2.default.object, import_prop_types2.default.bool])), import_prop_types2.default.func, import_prop_types2.default.object]),
  /**
   * The variant to use.
   * @default 'text'
   */
  variant: import_prop_types2.default.oneOfType([import_prop_types2.default.oneOf(["outlined", "text"]), import_prop_types2.default.string])
} : void 0;
var Pagination_default = Pagination;

// node_modules/@mui/material/Zoom/Zoom.js
var React3 = __toESM(require_react());
var import_prop_types3 = __toESM(require_prop_types());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var styles = {
  entering: {
    transform: "none"
  },
  entered: {
    transform: "none"
  }
};
var Zoom = React3.forwardRef(function Zoom2(props, ref) {
  const theme = useTheme();
  const defaultTimeout = {
    enter: theme.transitions.duration.enteringScreen,
    exit: theme.transitions.duration.leavingScreen
  };
  const {
    addEndListener,
    appear = true,
    children,
    easing,
    in: inProp,
    onEnter,
    onEntered,
    onEntering,
    onExit,
    onExited,
    onExiting,
    style,
    timeout = defaultTimeout,
    // eslint-disable-next-line react/prop-types
    TransitionComponent = Transition_default,
    ...other
  } = props;
  const nodeRef = React3.useRef(null);
  const handleRef = useForkRef_default(nodeRef, getReactElementRef(children), ref);
  const normalizedTransitionCallback = (callback) => (maybeIsAppearing) => {
    if (callback) {
      const node = nodeRef.current;
      if (maybeIsAppearing === void 0) {
        callback(node);
      } else {
        callback(node, maybeIsAppearing);
      }
    }
  };
  const handleEntering = normalizedTransitionCallback(onEntering);
  const handleEnter = normalizedTransitionCallback((node, isAppearing) => {
    reflow(node);
    const transitionProps = getTransitionProps({
      style,
      timeout,
      easing
    }, {
      mode: "enter"
    });
    node.style.webkitTransition = theme.transitions.create("transform", transitionProps);
    node.style.transition = theme.transitions.create("transform", transitionProps);
    if (onEnter) {
      onEnter(node, isAppearing);
    }
  });
  const handleEntered = normalizedTransitionCallback(onEntered);
  const handleExiting = normalizedTransitionCallback(onExiting);
  const handleExit = normalizedTransitionCallback((node) => {
    const transitionProps = getTransitionProps({
      style,
      timeout,
      easing
    }, {
      mode: "exit"
    });
    node.style.webkitTransition = theme.transitions.create("transform", transitionProps);
    node.style.transition = theme.transitions.create("transform", transitionProps);
    if (onExit) {
      onExit(node);
    }
  });
  const handleExited = normalizedTransitionCallback(onExited);
  const handleAddEndListener = (next) => {
    if (addEndListener) {
      addEndListener(nodeRef.current, next);
    }
  };
  return (0, import_jsx_runtime3.jsx)(TransitionComponent, {
    appear,
    in: inProp,
    nodeRef,
    onEnter: handleEnter,
    onEntered: handleEntered,
    onEntering: handleEntering,
    onExit: handleExit,
    onExited: handleExited,
    onExiting: handleExiting,
    addEndListener: handleAddEndListener,
    timeout,
    ...other,
    children: (state, {
      ownerState,
      ...restChildProps
    }) => {
      return React3.cloneElement(children, {
        style: {
          transform: "scale(0)",
          visibility: state === "exited" && !inProp ? "hidden" : void 0,
          ...styles[state],
          ...style,
          ...children.props.style
        },
        ref: handleRef,
        ...restChildProps
      });
    }
  });
});
true ? Zoom.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Add a custom transition end trigger. Called with the transitioning DOM
   * node and a done callback. Allows for more fine grained transition end
   * logic. Note: Timeouts are still used as a fallback if provided.
   */
  addEndListener: import_prop_types3.default.func,
  /**
   * Perform the enter transition when it first mounts if `in` is also `true`.
   * Set this to `false` to disable this behavior.
   * @default true
   */
  appear: import_prop_types3.default.bool,
  /**
   * A single child content element.
   */
  children: elementAcceptingRef_default.isRequired,
  /**
   * The transition timing function.
   * You may specify a single easing or a object containing enter and exit values.
   */
  easing: import_prop_types3.default.oneOfType([import_prop_types3.default.shape({
    enter: import_prop_types3.default.string,
    exit: import_prop_types3.default.string
  }), import_prop_types3.default.string]),
  /**
   * If `true`, the component will transition in.
   */
  in: import_prop_types3.default.bool,
  /**
   * @ignore
   */
  onEnter: import_prop_types3.default.func,
  /**
   * @ignore
   */
  onEntered: import_prop_types3.default.func,
  /**
   * @ignore
   */
  onEntering: import_prop_types3.default.func,
  /**
   * @ignore
   */
  onExit: import_prop_types3.default.func,
  /**
   * @ignore
   */
  onExited: import_prop_types3.default.func,
  /**
   * @ignore
   */
  onExiting: import_prop_types3.default.func,
  /**
   * @ignore
   */
  style: import_prop_types3.default.object,
  /**
   * The duration for the transition, in milliseconds.
   * You may specify a single timeout for all transitions, or individually with an object.
   * @default {
   *   enter: theme.transitions.duration.enteringScreen,
   *   exit: theme.transitions.duration.leavingScreen,
   * }
   */
  timeout: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.shape({
    appear: import_prop_types3.default.number,
    enter: import_prop_types3.default.number,
    exit: import_prop_types3.default.number
  })])
} : void 0;
var Zoom_default = Zoom;

// node_modules/@mui/material/SpeedDial/speedDialClasses.js
function getSpeedDialUtilityClass(slot) {
  return generateUtilityClass("MuiSpeedDial", slot);
}
var speedDialClasses = generateUtilityClasses("MuiSpeedDial", ["root", "fab", "directionUp", "directionDown", "directionLeft", "directionRight", "actions", "actionsClosed"]);
var speedDialClasses_default = speedDialClasses;

// node_modules/@mui/material/SpeedDial/SpeedDial.js
var React4 = __toESM(require_react());
var import_react_is = __toESM(require_react_is());
var import_prop_types4 = __toESM(require_prop_types());
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var useUtilityClasses3 = (ownerState) => {
  const {
    classes,
    open,
    direction
  } = ownerState;
  const slots = {
    root: ["root", `direction${capitalize_default(direction)}`],
    fab: ["fab"],
    actions: ["actions", !open && "actionsClosed"]
  };
  return composeClasses(slots, getSpeedDialUtilityClass, classes);
};
function getOrientation(direction) {
  if (direction === "up" || direction === "down") {
    return "vertical";
  }
  if (direction === "right" || direction === "left") {
    return "horizontal";
  }
  return void 0;
}
var dialRadius = 32;
var spacingActions = 16;
var SpeedDialRoot = styled_default("div", {
  name: "MuiSpeedDial",
  slot: "Root",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [styles2.root, styles2[`direction${capitalize_default(ownerState.direction)}`]];
  }
})(memoTheme_default(({
  theme
}) => ({
  zIndex: (theme.vars || theme).zIndex.speedDial,
  display: "flex",
  alignItems: "center",
  pointerEvents: "none",
  variants: [{
    props: {
      direction: "up"
    },
    style: {
      flexDirection: "column-reverse",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "column-reverse",
        marginBottom: -dialRadius,
        paddingBottom: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: "down"
    },
    style: {
      flexDirection: "column",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "column",
        marginTop: -dialRadius,
        paddingTop: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: "left"
    },
    style: {
      flexDirection: "row-reverse",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "row-reverse",
        marginRight: -dialRadius,
        paddingRight: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: "right"
    },
    style: {
      flexDirection: "row",
      [`& .${speedDialClasses_default.actions}`]: {
        flexDirection: "row",
        marginLeft: -dialRadius,
        paddingLeft: spacingActions + dialRadius
      }
    }
  }]
})));
var SpeedDialFab = styled_default(Fab_default, {
  name: "MuiSpeedDial",
  slot: "Fab",
  overridesResolver: (props, styles2) => styles2.fab
})({
  pointerEvents: "auto"
});
var SpeedDialActions = styled_default("div", {
  name: "MuiSpeedDial",
  slot: "Actions",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [styles2.actions, !ownerState.open && styles2.actionsClosed];
  }
})({
  display: "flex",
  pointerEvents: "auto",
  variants: [{
    props: ({
      ownerState
    }) => !ownerState.open,
    style: {
      transition: "top 0s linear 0.2s",
      pointerEvents: "none"
    }
  }]
});
var SpeedDial = React4.forwardRef(function SpeedDial2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiSpeedDial"
  });
  const theme = useTheme();
  const defaultTransitionDuration = {
    enter: theme.transitions.duration.enteringScreen,
    exit: theme.transitions.duration.leavingScreen
  };
  const {
    ariaLabel,
    FabProps: {
      ref: origDialButtonRef,
      ...FabProps
    } = {},
    children: childrenProp,
    className,
    direction = "up",
    hidden = false,
    icon,
    onBlur,
    onClose,
    onFocus,
    onKeyDown,
    onMouseEnter,
    onMouseLeave,
    onOpen,
    open: openProp,
    openIcon,
    slots = {},
    slotProps = {},
    TransitionComponent: TransitionComponentProp,
    TransitionProps: TransitionPropsProp,
    transitionDuration = defaultTransitionDuration,
    ...other
  } = props;
  const [open, setOpenState] = useControlled_default({
    controlled: openProp,
    default: false,
    name: "SpeedDial",
    state: "open"
  });
  const ownerState = {
    ...props,
    open,
    direction
  };
  const classes = useUtilityClasses3(ownerState);
  const eventTimer = useTimeout();
  const focusedAction = React4.useRef(0);
  const nextItemArrowKey = React4.useRef();
  const actions = React4.useRef([]);
  actions.current = [actions.current[0]];
  const handleOwnFabRef = React4.useCallback((fabFef) => {
    actions.current[0] = fabFef;
  }, []);
  const handleFabRef = useForkRef_default(origDialButtonRef, handleOwnFabRef);
  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {
    return (buttonRef) => {
      actions.current[dialActionIndex + 1] = buttonRef;
      if (origButtonRef) {
        origButtonRef(buttonRef);
      }
    };
  };
  const handleKeyDown = (event) => {
    if (onKeyDown) {
      onKeyDown(event);
    }
    const key = event.key.replace("Arrow", "").toLowerCase();
    const {
      current: nextItemArrowKeyCurrent = key
    } = nextItemArrowKey;
    if (event.key === "Escape") {
      setOpenState(false);
      actions.current[0].focus();
      if (onClose) {
        onClose(event, "escapeKeyDown");
      }
      return;
    }
    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== void 0) {
      event.preventDefault();
      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;
      const nextAction = clamp_default(focusedAction.current + actionStep, 0, actions.current.length - 1);
      actions.current[nextAction].focus();
      focusedAction.current = nextAction;
      nextItemArrowKey.current = nextItemArrowKeyCurrent;
    }
  };
  React4.useEffect(() => {
    if (!open) {
      focusedAction.current = 0;
      nextItemArrowKey.current = void 0;
    }
  }, [open]);
  const handleClose = (event) => {
    if (event.type === "mouseleave" && onMouseLeave) {
      onMouseLeave(event);
    }
    if (event.type === "blur" && onBlur) {
      onBlur(event);
    }
    eventTimer.clear();
    if (event.type === "blur") {
      eventTimer.start(0, () => {
        setOpenState(false);
        if (onClose) {
          onClose(event, "blur");
        }
      });
    } else {
      setOpenState(false);
      if (onClose) {
        onClose(event, "mouseLeave");
      }
    }
  };
  const handleClick = (event) => {
    if (FabProps.onClick) {
      FabProps.onClick(event);
    }
    eventTimer.clear();
    if (open) {
      setOpenState(false);
      if (onClose) {
        onClose(event, "toggle");
      }
    } else {
      setOpenState(true);
      if (onOpen) {
        onOpen(event, "toggle");
      }
    }
  };
  const handleOpen = (event) => {
    if (event.type === "mouseenter" && onMouseEnter) {
      onMouseEnter(event);
    }
    if (event.type === "focus" && onFocus) {
      onFocus(event);
    }
    eventTimer.clear();
    if (!open) {
      eventTimer.start(0, () => {
        setOpenState(true);
        if (onOpen) {
          const eventMap = {
            focus: "focus",
            mouseenter: "mouseEnter"
          };
          onOpen(event, eventMap[event.type]);
        }
      });
    }
  };
  const id = ariaLabel.replace(/^[^a-z]+|[^\w:.-]+/gi, "");
  const allItems = React4.Children.toArray(childrenProp).filter((child) => {
    if (true) {
      if ((0, import_react_is.isFragment)(child)) {
        console.error(["MUI: The SpeedDial component doesn't accept a Fragment as a child.", "Consider providing an array instead."].join("\n"));
      }
    }
    return React4.isValidElement(child);
  });
  const children = allItems.map((child, index) => {
    const {
      FabProps: {
        ref: origButtonRef,
        ...ChildFabProps
      } = {},
      tooltipPlacement: tooltipPlacementProp
    } = child.props;
    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === "vertical" ? "left" : "top");
    return React4.cloneElement(child, {
      FabProps: {
        ...ChildFabProps,
        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)
      },
      delay: 30 * (open ? index : allItems.length - index),
      open,
      tooltipPlacement,
      id: `${id}-action-${index}`
    });
  });
  const backwardCompatibleSlots = {
    transition: TransitionComponentProp,
    ...slots
  };
  const backwardCompatibleSlotProps = {
    transition: TransitionPropsProp,
    ...slotProps
  };
  const externalForwardedProps = {
    slots: backwardCompatibleSlots,
    slotProps: backwardCompatibleSlotProps
  };
  const [RootSlot, rootSlotProps] = useSlot("root", {
    elementType: SpeedDialRoot,
    externalForwardedProps: {
      ...externalForwardedProps,
      ...other
    },
    ownerState,
    ref,
    className: clsx_default(classes.root, className),
    additionalProps: {
      role: "presentation"
    },
    getSlotProps: (handlers) => ({
      ...handlers,
      onKeyDown: (event) => {
        var _a;
        (_a = handlers.onKeyDown) == null ? void 0 : _a.call(handlers, event);
        handleKeyDown(event);
      },
      onBlur: (event) => {
        var _a;
        (_a = handlers.onBlur) == null ? void 0 : _a.call(handlers, event);
        handleClose(event);
      },
      onFocus: (event) => {
        var _a;
        (_a = handlers.onFocus) == null ? void 0 : _a.call(handlers, event);
        handleOpen(event);
      },
      onMouseEnter: (event) => {
        var _a;
        (_a = handlers.onMouseEnter) == null ? void 0 : _a.call(handlers, event);
        handleOpen(event);
      },
      onMouseLeave: (event) => {
        var _a;
        (_a = handlers.onMouseLeave) == null ? void 0 : _a.call(handlers, event);
        handleClose(event);
      }
    })
  });
  const [TransitionSlot, transitionProps] = useSlot("transition", {
    elementType: Zoom_default,
    externalForwardedProps,
    ownerState
  });
  return (0, import_jsx_runtime4.jsxs)(RootSlot, {
    ...rootSlotProps,
    children: [(0, import_jsx_runtime4.jsx)(TransitionSlot, {
      in: !hidden,
      timeout: transitionDuration,
      unmountOnExit: true,
      ...transitionProps,
      children: (0, import_jsx_runtime4.jsx)(SpeedDialFab, {
        color: "primary",
        "aria-label": ariaLabel,
        "aria-haspopup": "true",
        "aria-expanded": open,
        "aria-controls": `${id}-actions`,
        ...FabProps,
        onClick: handleClick,
        className: clsx_default(classes.fab, FabProps.className),
        ref: handleFabRef,
        ownerState,
        children: React4.isValidElement(icon) && isMuiElement_default(icon, ["SpeedDialIcon"]) ? React4.cloneElement(icon, {
          open
        }) : icon
      })
    }), (0, import_jsx_runtime4.jsx)(SpeedDialActions, {
      id: `${id}-actions`,
      role: "menu",
      "aria-orientation": getOrientation(direction),
      className: clsx_default(classes.actions, !open && classes.actionsClosed),
      ownerState,
      children
    })]
  });
});
true ? SpeedDial.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The aria-label of the button element.
   * Also used to provide the `id` for the `SpeedDial` element and its children.
   */
  ariaLabel: import_prop_types4.default.string.isRequired,
  /**
   * SpeedDialActions to display when the SpeedDial is `open`.
   */
  children: import_prop_types4.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types4.default.object,
  /**
   * @ignore
   */
  className: import_prop_types4.default.string,
  /**
   * The direction the actions open relative to the floating action button.
   * @default 'up'
   */
  direction: import_prop_types4.default.oneOf(["down", "left", "right", "up"]),
  /**
   * Props applied to the [`Fab`](https://mui.com/material-ui/api/fab/) element.
   * @default {}
   */
  FabProps: import_prop_types4.default.object,
  /**
   * If `true`, the SpeedDial is hidden.
   * @default false
   */
  hidden: import_prop_types4.default.bool,
  /**
   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component
   * provides a default Icon with animation.
   */
  icon: import_prop_types4.default.node,
  /**
   * @ignore
   */
  onBlur: import_prop_types4.default.func,
  /**
   * Callback fired when the component requests to be closed.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"blur"`, `"mouseLeave"`, `"escapeKeyDown"`.
   */
  onClose: import_prop_types4.default.func,
  /**
   * @ignore
   */
  onFocus: import_prop_types4.default.func,
  /**
   * @ignore
   */
  onKeyDown: import_prop_types4.default.func,
  /**
   * @ignore
   */
  onMouseEnter: import_prop_types4.default.func,
  /**
   * @ignore
   */
  onMouseLeave: import_prop_types4.default.func,
  /**
   * Callback fired when the component requests to be open.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"focus"`, `"mouseEnter"`.
   */
  onOpen: import_prop_types4.default.func,
  /**
   * If `true`, the component is shown.
   */
  open: import_prop_types4.default.bool,
  /**
   * The icon to display in the SpeedDial Fab when the SpeedDial is open.
   */
  openIcon: import_prop_types4.default.node,
  /**
   * The props used for each slot inside.
   * @default {}
   */
  slotProps: import_prop_types4.default.shape({
    root: import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.object]),
    transition: import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.object])
  }),
  /**
   * The components used for each slot inside.
   * @default {}
   */
  slots: import_prop_types4.default.shape({
    root: import_prop_types4.default.elementType,
    transition: import_prop_types4.default.elementType
  }),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types4.default.oneOfType([import_prop_types4.default.arrayOf(import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.object, import_prop_types4.default.bool])), import_prop_types4.default.func, import_prop_types4.default.object]),
  /**
   * The component used for the transition.
   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.
   * @default Zoom
   * * @deprecated Use `slots.transition` instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
   */
  TransitionComponent: import_prop_types4.default.elementType,
  /**
   * The duration for the transition, in milliseconds.
   * You may specify a single timeout for all transitions, or individually with an object.
   * @default {
   *   enter: theme.transitions.duration.enteringScreen,
   *   exit: theme.transitions.duration.leavingScreen,
   * }
   */
  transitionDuration: import_prop_types4.default.oneOfType([import_prop_types4.default.number, import_prop_types4.default.shape({
    appear: import_prop_types4.default.number,
    enter: import_prop_types4.default.number,
    exit: import_prop_types4.default.number
  })]),
  /**
   * Props applied to the transition element.
   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.
   * @deprecated Use `slotProps.transition` instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
   */
  TransitionProps: import_prop_types4.default.object
} : void 0;
var SpeedDial_default = SpeedDial;

// node_modules/@mui/material/SpeedDialAction/speedDialActionClasses.js
function getSpeedDialActionUtilityClass(slot) {
  return generateUtilityClass("MuiSpeedDialAction", slot);
}
var speedDialActionClasses = generateUtilityClasses("MuiSpeedDialAction", ["fab", "fabClosed", "staticTooltip", "staticTooltipClosed", "staticTooltipLabel", "tooltipPlacementLeft", "tooltipPlacementRight"]);
var speedDialActionClasses_default = speedDialActionClasses;

// node_modules/@mui/material/SpeedDialAction/SpeedDialAction.js
var React5 = __toESM(require_react());
var import_prop_types5 = __toESM(require_prop_types());
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var useUtilityClasses4 = (ownerState) => {
  const {
    open,
    tooltipPlacement,
    classes
  } = ownerState;
  const slots = {
    fab: ["fab", !open && "fabClosed"],
    staticTooltip: ["staticTooltip", `tooltipPlacement${capitalize_default(tooltipPlacement)}`, !open && "staticTooltipClosed"],
    staticTooltipLabel: ["staticTooltipLabel"]
  };
  return composeClasses(slots, getSpeedDialActionUtilityClass, classes);
};
var SpeedDialActionFab = styled_default(Fab_default, {
  name: "MuiSpeedDialAction",
  slot: "Fab",
  skipVariantsResolver: false,
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [styles2.fab, !ownerState.open && styles2.fabClosed];
  }
})(memoTheme_default(({
  theme
}) => ({
  margin: 8,
  color: (theme.vars || theme).palette.text.secondary,
  backgroundColor: (theme.vars || theme).palette.background.paper,
  "&:hover": {
    backgroundColor: theme.vars ? theme.vars.palette.SpeedDialAction.fabHoverBg : emphasize(theme.palette.background.paper, 0.15)
  },
  transition: `${theme.transitions.create("transform", {
    duration: theme.transitions.duration.shorter
  })}, opacity 0.8s`,
  opacity: 1,
  variants: [{
    props: ({
      ownerState
    }) => !ownerState.open,
    style: {
      opacity: 0,
      transform: "scale(0)"
    }
  }]
})));
var SpeedDialActionStaticTooltip = styled_default("span", {
  name: "MuiSpeedDialAction",
  slot: "StaticTooltip",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [styles2.staticTooltip, !ownerState.open && styles2.staticTooltipClosed, styles2[`tooltipPlacement${capitalize_default(ownerState.tooltipPlacement)}`]];
  }
})(memoTheme_default(({
  theme
}) => ({
  position: "relative",
  display: "flex",
  alignItems: "center",
  [`& .${speedDialActionClasses_default.staticTooltipLabel}`]: {
    transition: theme.transitions.create(["transform", "opacity"], {
      duration: theme.transitions.duration.shorter
    }),
    opacity: 1
  },
  variants: [{
    props: ({
      ownerState
    }) => !ownerState.open,
    style: {
      [`& .${speedDialActionClasses_default.staticTooltipLabel}`]: {
        opacity: 0,
        transform: "scale(0.5)"
      }
    }
  }, {
    props: {
      tooltipPlacement: "left"
    },
    style: {
      [`& .${speedDialActionClasses_default.staticTooltipLabel}`]: {
        transformOrigin: "100% 50%",
        right: "100%",
        marginRight: 8
      }
    }
  }, {
    props: {
      tooltipPlacement: "right"
    },
    style: {
      [`& .${speedDialActionClasses_default.staticTooltipLabel}`]: {
        transformOrigin: "0% 50%",
        left: "100%",
        marginLeft: 8
      }
    }
  }]
})));
var SpeedDialActionStaticTooltipLabel = styled_default("span", {
  name: "MuiSpeedDialAction",
  slot: "StaticTooltipLabel",
  overridesResolver: (props, styles2) => styles2.staticTooltipLabel
})(memoTheme_default(({
  theme
}) => ({
  position: "absolute",
  ...theme.typography.body1,
  backgroundColor: (theme.vars || theme).palette.background.paper,
  borderRadius: (theme.vars || theme).shape.borderRadius,
  boxShadow: (theme.vars || theme).shadows[1],
  color: (theme.vars || theme).palette.text.secondary,
  padding: "4px 16px",
  wordBreak: "keep-all"
})));
var SpeedDialAction = React5.forwardRef(function SpeedDialAction2(inProps, ref) {
  var _a;
  const props = useDefaultProps({
    props: inProps,
    name: "MuiSpeedDialAction"
  });
  const {
    className,
    delay = 0,
    FabProps = {},
    icon,
    id,
    open,
    TooltipClasses,
    tooltipOpen: tooltipOpenProp = false,
    tooltipPlacement = "left",
    tooltipTitle,
    slots = {},
    slotProps = {},
    ...other
  } = props;
  const ownerState = {
    ...props,
    tooltipPlacement
  };
  const classes = useUtilityClasses4(ownerState);
  const externalForwardedProps = {
    slots,
    slotProps: {
      fab: FabProps,
      ...slotProps,
      tooltip: mergeSlotProps(typeof slotProps.tooltip === "function" ? slotProps.tooltip(ownerState) : slotProps.tooltip, {
        title: tooltipTitle,
        open: tooltipOpenProp,
        placement: tooltipPlacement,
        classes: TooltipClasses
      })
    }
  };
  const [tooltipOpen, setTooltipOpen] = React5.useState((_a = externalForwardedProps.slotProps.tooltip) == null ? void 0 : _a.open);
  const handleTooltipClose = () => {
    setTooltipOpen(false);
  };
  const handleTooltipOpen = () => {
    setTooltipOpen(true);
  };
  const transitionStyle = {
    transitionDelay: `${delay}ms`
  };
  const [FabSlot, fabSlotProps] = useSlot("fab", {
    elementType: SpeedDialActionFab,
    externalForwardedProps,
    ownerState,
    shouldForwardComponentProp: true,
    className: clsx_default(classes.fab, className),
    additionalProps: {
      style: transitionStyle,
      tabIndex: -1,
      role: "menuitem",
      size: "small"
    }
  });
  const [TooltipSlot, tooltipSlotProps] = useSlot("tooltip", {
    elementType: Tooltip_default,
    externalForwardedProps,
    shouldForwardComponentProp: true,
    ref,
    additionalProps: {
      id
    },
    ownerState,
    getSlotProps: (handlers) => ({
      ...handlers,
      onClose: (event) => {
        var _a2;
        (_a2 = handlers.onClose) == null ? void 0 : _a2.call(handlers, event);
        handleTooltipClose();
      },
      onOpen: (event) => {
        var _a2;
        (_a2 = handlers.onOpen) == null ? void 0 : _a2.call(handlers, event);
        handleTooltipOpen();
      }
    })
  });
  const [StaticTooltipSlot, staticTooltipSlotProps] = useSlot("staticTooltip", {
    elementType: SpeedDialActionStaticTooltip,
    externalForwardedProps,
    ownerState,
    ref,
    className: classes.staticTooltip,
    additionalProps: {
      id
    }
  });
  const [StaticTooltipLabelSlot, staticTooltipLabelSlotProps] = useSlot("staticTooltipLabel", {
    elementType: SpeedDialActionStaticTooltipLabel,
    externalForwardedProps,
    ownerState,
    className: classes.staticTooltipLabel,
    additionalProps: {
      style: transitionStyle,
      id: `${id}-label`
    }
  });
  const fab = (0, import_jsx_runtime5.jsx)(FabSlot, {
    ...fabSlotProps,
    children: icon
  });
  if (tooltipSlotProps.open) {
    return (0, import_jsx_runtime5.jsxs)(StaticTooltipSlot, {
      ...staticTooltipSlotProps,
      ...other,
      children: [(0, import_jsx_runtime5.jsx)(StaticTooltipLabelSlot, {
        ...staticTooltipLabelSlotProps,
        children: tooltipSlotProps.title
      }), React5.cloneElement(fab, {
        "aria-labelledby": `${id}-label`
      })]
    });
  }
  if (!open && tooltipOpen) {
    setTooltipOpen(false);
  }
  return (0, import_jsx_runtime5.jsx)(TooltipSlot, {
    ...tooltipSlotProps,
    title: tooltipSlotProps.title,
    open: open && tooltipOpen,
    placement: tooltipSlotProps.placement,
    classes: tooltipSlotProps.classes,
    ...other,
    children: fab
  });
});
true ? SpeedDialAction.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types5.default.object,
  /**
   * @ignore
   */
  className: import_prop_types5.default.string,
  /**
   * Adds a transition delay, to allow a series of SpeedDialActions to be animated.
   * @default 0
   */
  delay: import_prop_types5.default.number,
  /**
   * Props applied to the [`Fab`](https://mui.com/material-ui/api/fab/) component.
   * @default {}
   * @deprecated Use `slotProps.fab` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.
   */
  FabProps: import_prop_types5.default.object,
  /**
   * The icon to display in the SpeedDial Fab.
   */
  icon: import_prop_types5.default.node,
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types5.default.string,
  /**
   * If `true`, the component is shown.
   */
  open: import_prop_types5.default.bool,
  /**
   * The props used for each slot inside.
   * @default {}
   */
  slotProps: import_prop_types5.default.shape({
    fab: import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.object]),
    staticTooltip: import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.object]),
    staticTooltipLabel: import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.object]),
    tooltip: import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.object])
  }),
  /**
   * The components used for each slot inside.
   * @default {}
   */
  slots: import_prop_types5.default.shape({
    fab: import_prop_types5.default.elementType,
    staticTooltip: import_prop_types5.default.elementType,
    staticTooltipLabel: import_prop_types5.default.elementType,
    tooltip: import_prop_types5.default.elementType
  }),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types5.default.oneOfType([import_prop_types5.default.arrayOf(import_prop_types5.default.oneOfType([import_prop_types5.default.func, import_prop_types5.default.object, import_prop_types5.default.bool])), import_prop_types5.default.func, import_prop_types5.default.object]),
  /**
   * `classes` prop applied to the [`Tooltip`](https://mui.com/material-ui/api/tooltip/) element.
   * @deprecated Use `slotProps.tooltip.classes` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.
   */
  TooltipClasses: import_prop_types5.default.object,
  /**
   * Make the tooltip always visible when the SpeedDial is open.
   * @default false
   * @deprecated Use `slotProps.tooltip.open` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.
   */
  tooltipOpen: import_prop_types5.default.bool,
  /**
   * Placement of the tooltip.
   * @default 'left'
   * @deprecated Use `slotProps.tooltip.placement` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.
   */
  tooltipPlacement: import_prop_types5.default.oneOf(["auto-end", "auto-start", "auto", "bottom-end", "bottom-start", "bottom", "left-end", "left-start", "left", "right-end", "right-start", "right", "top-end", "top-start", "top"]),
  /**
   * Label to display in the tooltip.
   * @deprecated Use `slotProps.tooltip.title` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.
   */
  tooltipTitle: import_prop_types5.default.node
} : void 0;
var SpeedDialAction_default = SpeedDialAction;

// node_modules/@mui/material/SpeedDialIcon/speedDialIconClasses.js
function getSpeedDialIconUtilityClass(slot) {
  return generateUtilityClass("MuiSpeedDialIcon", slot);
}
var speedDialIconClasses = generateUtilityClasses("MuiSpeedDialIcon", ["root", "icon", "iconOpen", "iconWithOpenIconOpen", "openIcon", "openIconOpen"]);
var speedDialIconClasses_default = speedDialIconClasses;

// node_modules/@mui/material/SpeedDialIcon/SpeedDialIcon.js
var React7 = __toESM(require_react());
var import_prop_types6 = __toESM(require_prop_types());

// node_modules/@mui/material/internal/svg-icons/Add.js
var React6 = __toESM(require_react());
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var Add_default = createSvgIcon((0, import_jsx_runtime6.jsx)("path", {
  d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"
}), "Add");

// node_modules/@mui/material/SpeedDialIcon/SpeedDialIcon.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var useUtilityClasses5 = (ownerState) => {
  const {
    classes,
    open,
    openIcon
  } = ownerState;
  const slots = {
    root: ["root"],
    icon: ["icon", open && "iconOpen", openIcon && open && "iconWithOpenIconOpen"],
    openIcon: ["openIcon", open && "openIconOpen"]
  };
  return composeClasses(slots, getSpeedDialIconUtilityClass, classes);
};
var SpeedDialIconRoot = styled_default("span", {
  name: "MuiSpeedDialIcon",
  slot: "Root",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [{
      [`& .${speedDialIconClasses_default.icon}`]: styles2.icon
    }, {
      [`& .${speedDialIconClasses_default.icon}`]: ownerState.open && styles2.iconOpen
    }, {
      [`& .${speedDialIconClasses_default.icon}`]: ownerState.open && ownerState.openIcon && styles2.iconWithOpenIconOpen
    }, {
      [`& .${speedDialIconClasses_default.openIcon}`]: styles2.openIcon
    }, {
      [`& .${speedDialIconClasses_default.openIcon}`]: ownerState.open && styles2.openIconOpen
    }, styles2.root];
  }
})(memoTheme_default(({
  theme
}) => ({
  height: 24,
  [`& .${speedDialIconClasses_default.icon}`]: {
    transition: theme.transitions.create(["transform", "opacity"], {
      duration: theme.transitions.duration.short
    })
  },
  [`& .${speedDialIconClasses_default.openIcon}`]: {
    position: "absolute",
    transition: theme.transitions.create(["transform", "opacity"], {
      duration: theme.transitions.duration.short
    }),
    opacity: 0,
    transform: "rotate(-45deg)"
  },
  variants: [{
    props: ({
      ownerState
    }) => ownerState.open,
    style: {
      [`& .${speedDialIconClasses_default.icon}`]: {
        transform: "rotate(45deg)"
      }
    }
  }, {
    props: ({
      ownerState
    }) => ownerState.open && ownerState.openIcon,
    style: {
      [`& .${speedDialIconClasses_default.icon}`]: {
        opacity: 0
      }
    }
  }, {
    props: ({
      ownerState
    }) => ownerState.open,
    style: {
      [`& .${speedDialIconClasses_default.openIcon}`]: {
        transform: "rotate(0deg)",
        opacity: 1
      }
    }
  }]
})));
var SpeedDialIcon = React7.forwardRef(function SpeedDialIcon2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiSpeedDialIcon"
  });
  const {
    className,
    icon: iconProp,
    open,
    openIcon: openIconProp,
    ...other
  } = props;
  const ownerState = props;
  const classes = useUtilityClasses5(ownerState);
  function formatIcon(icon, newClassName) {
    if (React7.isValidElement(icon)) {
      return React7.cloneElement(icon, {
        className: newClassName
      });
    }
    return icon;
  }
  return (0, import_jsx_runtime7.jsxs)(SpeedDialIconRoot, {
    className: clsx_default(classes.root, className),
    ref,
    ownerState,
    ...other,
    children: [openIconProp ? formatIcon(openIconProp, classes.openIcon) : null, iconProp ? formatIcon(iconProp, classes.icon) : (0, import_jsx_runtime7.jsx)(Add_default, {
      className: classes.icon
    })]
  });
});
true ? SpeedDialIcon.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types6.default.object,
  /**
   * @ignore
   */
  className: import_prop_types6.default.string,
  /**
   * The icon to display.
   */
  icon: import_prop_types6.default.node,
  /**
   * @ignore
   * If `true`, the component is shown.
   */
  open: import_prop_types6.default.bool,
  /**
   * The icon to display in the SpeedDial Floating Action Button when the SpeedDial is open.
   */
  openIcon: import_prop_types6.default.node,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types6.default.oneOfType([import_prop_types6.default.arrayOf(import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.object, import_prop_types6.default.bool])), import_prop_types6.default.func, import_prop_types6.default.object])
} : void 0;
SpeedDialIcon.muiName = "SpeedDialIcon";
var SpeedDialIcon_default = SpeedDialIcon;

// node_modules/@mui/material/ToggleButtonGroup/toggleButtonGroupClasses.js
function getToggleButtonGroupUtilityClass(slot) {
  return generateUtilityClass("MuiToggleButtonGroup", slot);
}
var toggleButtonGroupClasses = generateUtilityClasses("MuiToggleButtonGroup", ["root", "selected", "horizontal", "vertical", "disabled", "grouped", "groupedHorizontal", "groupedVertical", "fullWidth", "firstButton", "lastButton", "middleButton"]);
var toggleButtonGroupClasses_default = toggleButtonGroupClasses;

// node_modules/@mui/material/ToggleButtonGroup/ToggleButtonGroup.js
var React8 = __toESM(require_react());
var import_react_is2 = __toESM(require_react_is());
var import_prop_types7 = __toESM(require_prop_types());
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var useUtilityClasses6 = (ownerState) => {
  const {
    classes,
    orientation,
    fullWidth,
    disabled
  } = ownerState;
  const slots = {
    root: ["root", orientation, fullWidth && "fullWidth"],
    grouped: ["grouped", `grouped${capitalize_default(orientation)}`, disabled && "disabled"],
    firstButton: ["firstButton"],
    lastButton: ["lastButton"],
    middleButton: ["middleButton"]
  };
  return composeClasses(slots, getToggleButtonGroupUtilityClass, classes);
};
var ToggleButtonGroupRoot = styled_default("div", {
  name: "MuiToggleButtonGroup",
  slot: "Root",
  overridesResolver: (props, styles2) => {
    const {
      ownerState
    } = props;
    return [{
      [`& .${toggleButtonGroupClasses_default.grouped}`]: styles2.grouped
    }, {
      [`& .${toggleButtonGroupClasses_default.grouped}`]: styles2[`grouped${capitalize_default(ownerState.orientation)}`]
    }, {
      [`& .${toggleButtonGroupClasses_default.firstButton}`]: styles2.firstButton
    }, {
      [`& .${toggleButtonGroupClasses_default.lastButton}`]: styles2.lastButton
    }, {
      [`& .${toggleButtonGroupClasses_default.middleButton}`]: styles2.middleButton
    }, styles2.root, ownerState.orientation === "vertical" && styles2.vertical, ownerState.fullWidth && styles2.fullWidth];
  }
})(memoTheme_default(({
  theme
}) => ({
  display: "inline-flex",
  borderRadius: (theme.vars || theme).shape.borderRadius,
  variants: [{
    props: {
      orientation: "vertical"
    },
    style: {
      flexDirection: "column",
      [`& .${toggleButtonGroupClasses_default.grouped}`]: {
        [`&.${toggleButtonGroupClasses_default.selected} + .${toggleButtonGroupClasses_default.grouped}.${toggleButtonGroupClasses_default.selected}`]: {
          borderTop: 0,
          marginTop: 0
        }
      },
      [`& .${toggleButtonGroupClasses_default.firstButton},& .${toggleButtonGroupClasses_default.middleButton}`]: {
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0
      },
      [`& .${toggleButtonGroupClasses_default.lastButton},& .${toggleButtonGroupClasses_default.middleButton}`]: {
        marginTop: -1,
        borderTop: "1px solid transparent",
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0
      },
      [`& .${toggleButtonGroupClasses_default.lastButton}.${toggleButtonClasses_default.disabled},& .${toggleButtonGroupClasses_default.middleButton}.${toggleButtonClasses_default.disabled}`]: {
        borderTop: "1px solid transparent"
      }
    }
  }, {
    props: {
      fullWidth: true
    },
    style: {
      width: "100%"
    }
  }, {
    props: {
      orientation: "horizontal"
    },
    style: {
      [`& .${toggleButtonGroupClasses_default.grouped}`]: {
        [`&.${toggleButtonGroupClasses_default.selected} + .${toggleButtonGroupClasses_default.grouped}.${toggleButtonGroupClasses_default.selected}`]: {
          borderLeft: 0,
          marginLeft: 0
        }
      },
      [`& .${toggleButtonGroupClasses_default.firstButton},& .${toggleButtonGroupClasses_default.middleButton}`]: {
        borderTopRightRadius: 0,
        borderBottomRightRadius: 0
      },
      [`& .${toggleButtonGroupClasses_default.lastButton},& .${toggleButtonGroupClasses_default.middleButton}`]: {
        marginLeft: -1,
        borderLeft: "1px solid transparent",
        borderTopLeftRadius: 0,
        borderBottomLeftRadius: 0
      },
      [`& .${toggleButtonGroupClasses_default.lastButton}.${toggleButtonClasses_default.disabled},& .${toggleButtonGroupClasses_default.middleButton}.${toggleButtonClasses_default.disabled}`]: {
        borderLeft: "1px solid transparent"
      }
    }
  }]
})));
var ToggleButtonGroup = React8.forwardRef(function ToggleButtonGroup2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiToggleButtonGroup"
  });
  const {
    children,
    className,
    color = "standard",
    disabled = false,
    exclusive = false,
    fullWidth = false,
    onChange,
    orientation = "horizontal",
    size = "medium",
    value,
    ...other
  } = props;
  const ownerState = {
    ...props,
    disabled,
    fullWidth,
    orientation,
    size
  };
  const classes = useUtilityClasses6(ownerState);
  const handleChange = React8.useCallback((event, buttonValue) => {
    if (!onChange) {
      return;
    }
    const index = value && value.indexOf(buttonValue);
    let newValue;
    if (value && index >= 0) {
      newValue = value.slice();
      newValue.splice(index, 1);
    } else {
      newValue = value ? value.concat(buttonValue) : [buttonValue];
    }
    onChange(event, newValue);
  }, [onChange, value]);
  const handleExclusiveChange = React8.useCallback((event, buttonValue) => {
    if (!onChange) {
      return;
    }
    onChange(event, value === buttonValue ? null : buttonValue);
  }, [onChange, value]);
  const context = React8.useMemo(() => ({
    className: classes.grouped,
    onChange: exclusive ? handleExclusiveChange : handleChange,
    value,
    size,
    fullWidth,
    color,
    disabled
  }), [classes.grouped, exclusive, handleExclusiveChange, handleChange, value, size, fullWidth, color, disabled]);
  const validChildren = getValidReactChildren(children);
  const childrenCount = validChildren.length;
  const getButtonPositionClassName = (index) => {
    const isFirstButton = index === 0;
    const isLastButton = index === childrenCount - 1;
    if (isFirstButton && isLastButton) {
      return "";
    }
    if (isFirstButton) {
      return classes.firstButton;
    }
    if (isLastButton) {
      return classes.lastButton;
    }
    return classes.middleButton;
  };
  return (0, import_jsx_runtime8.jsx)(ToggleButtonGroupRoot, {
    role: "group",
    className: clsx_default(classes.root, className),
    ref,
    ownerState,
    ...other,
    children: (0, import_jsx_runtime8.jsx)(ToggleButtonGroupContext_default.Provider, {
      value: context,
      children: validChildren.map((child, index) => {
        if (true) {
          if ((0, import_react_is2.isFragment)(child)) {
            console.error(["MUI: The ToggleButtonGroup component doesn't accept a Fragment as a child.", "Consider providing an array instead."].join("\n"));
          }
        }
        return (0, import_jsx_runtime8.jsx)(ToggleButtonGroupButtonContext_default.Provider, {
          value: getButtonPositionClassName(index),
          children: child
        }, index);
      })
    })
  });
});
true ? ToggleButtonGroup.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types7.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types7.default.object,
  /**
   * @ignore
   */
  className: import_prop_types7.default.string,
  /**
   * The color of the button when it is selected.
   * It supports both default and custom theme colors, which can be added as shown in the
   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).
   * @default 'standard'
   */
  color: import_prop_types7.default.oneOfType([import_prop_types7.default.oneOf(["standard", "primary", "secondary", "error", "info", "success", "warning"]), import_prop_types7.default.string]),
  /**
   * If `true`, the component is disabled. This implies that all ToggleButton children will be disabled.
   * @default false
   */
  disabled: import_prop_types7.default.bool,
  /**
   * If `true`, only allow one of the child ToggleButton values to be selected.
   * @default false
   */
  exclusive: import_prop_types7.default.bool,
  /**
   * If `true`, the button group will take up the full width of its container.
   * @default false
   */
  fullWidth: import_prop_types7.default.bool,
  /**
   * Callback fired when the value changes.
   *
   * @param {React.MouseEvent<HTMLElement>} event The event source of the callback.
   * @param {any} value of the selected buttons. When `exclusive` is true
   * this is a single value; when false an array of selected values. If no value
   * is selected and `exclusive` is true the value is null; when false an empty array.
   */
  onChange: import_prop_types7.default.func,
  /**
   * The component orientation (layout flow direction).
   * @default 'horizontal'
   */
  orientation: import_prop_types7.default.oneOf(["horizontal", "vertical"]),
  /**
   * The size of the component.
   * @default 'medium'
   */
  size: import_prop_types7.default.oneOfType([import_prop_types7.default.oneOf(["small", "medium", "large"]), import_prop_types7.default.string]),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types7.default.oneOfType([import_prop_types7.default.arrayOf(import_prop_types7.default.oneOfType([import_prop_types7.default.func, import_prop_types7.default.object, import_prop_types7.default.bool])), import_prop_types7.default.func, import_prop_types7.default.object]),
  /**
   * The currently selected value within the group or an array of selected
   * values when `exclusive` is false.
   *
   * The value must have reference equality with the option in order to be selected.
   */
  value: import_prop_types7.default.any
} : void 0;
var ToggleButtonGroup_default = ToggleButtonGroup;

export {
  getAlertTitleUtilityClass,
  alertTitleClasses_default,
  AlertTitle_default,
  getPaginationUtilityClass,
  paginationClasses_default,
  usePagination,
  Pagination_default,
  Zoom_default,
  getSpeedDialUtilityClass,
  speedDialClasses_default,
  SpeedDial_default,
  getSpeedDialActionUtilityClass,
  speedDialActionClasses_default,
  SpeedDialAction_default,
  getSpeedDialIconUtilityClass,
  speedDialIconClasses_default,
  SpeedDialIcon_default,
  getToggleButtonGroupUtilityClass,
  toggleButtonGroupClasses_default,
  ToggleButtonGroup_default
};
//# sourceMappingURL=chunk-3LNCLNBV.js.map
