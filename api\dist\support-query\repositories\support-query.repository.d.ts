import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { SupportQuery } from '../models';
export declare class SupportQueryRepository extends BaseRepository<SupportQuery> {
    constructor();
    createSupportQuery(payload: Partial<SupportQuery>, currentContext: CurrentContext): Promise<SupportQuery>;
    getQueryListByFilter(filter: any, page: number, limit: number): Promise<{
        rows: SupportQuery[];
        count: number;
    }>;
}
