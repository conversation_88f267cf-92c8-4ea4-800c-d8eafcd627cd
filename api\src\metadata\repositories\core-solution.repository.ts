import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/shared/repositories';
import { CoreSolution, LocationType } from '../models';
import { PILLAR } from 'src/shared/enums';

@Injectable()
export class CoreSolutionRepository extends BaseRepository<CoreSolution> {
	constructor() {
		super(CoreSolution);
	}

	public getAllCoreSolutions(): Promise<CoreSolution[] | null> {
		return this.findAll({
			include: [
				{
					model: LocationType,
					attributes: ['id', 'title', 'code', 'canAcquireCapability', 'locationFormSections'],
					where: {
						active: true,
						deleted: false,
					},
					required: false,
				},
			],
			order: [
				['id', 'ASC'],
				[{ model: LocationType, as: 'locationTypes' }, 'title', 'ASC'], // LocationType.title via alias
			],
		});
	}

	public getCoreSolutionById(id: number): Promise<CoreSolution | null> {
		return this.findById(id);
	}

	public getCoreSolutionWithLoTypeById(id: number): Promise<CoreSolution | null> {
		return this.findOne({
			where: { id },
			include: [
				{
					model: LocationType,
					attributes: ['id', 'title', 'code', 'canAcquireCapability', 'locationFormSections'],
					where: {
						active: true,
						deleted: false,
					},
					required: false,
				},
			],
		});
	}

	public isCoreSolutionExistById(id: number): Promise<boolean> {
		return this.isRecordExist({ where: { id } });
	}

	public getCoreSolutionByPillar(pillar: PILLAR): Promise<CoreSolution[]> {
		return this.findAll({ where: { pillar } });
	}
}



