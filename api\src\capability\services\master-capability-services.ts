import { Injectable } from '@nestjs/common';
import {
	CapabilityLegRepository,
	CapabilityRepository,
	MasterCapabilityDropdownRepository,
	MasterEvidenceRepository,
} from '../repositories';
import { DatabaseHelper, multiObjectToInstance, toUpperUnderscore } from 'src/shared/helpers';
import { HistoryApiClient } from 'src/shared/clients';
import {
	CAPABILITY_DROPDOWN_LEVEL_ENUM,
	COMMON_DROPDOWN_TYPE,
	HISTORY_ACTION_TYPE,
	HISTORY_ENTITY_TYPE,
	HttpStatus,
	MASTER_CAPABILITY_DROPDOWN_TYPES,
} from 'src/shared/enums';
import { HttpException } from 'src/shared/exceptions';
import { CapabilityCategoryRepository } from '../repositories/capability-category.repository';
import {
	MasterCapabilityDropdownRequestDto,
	MasterCapabilityFilterRequestDto,
	MasterCapabilityListResponseDto,
	PaginatedMasterCapabilityListResponseDto,
	SetupNewMasterCapabilityRequestDto,
	SetupNewMasterEvidenceRequestDto,
} from '../dtos';
import { CurrentContext } from 'src/shared/types';
import { capitalize, startCase, toLower, trim, upperCase } from 'lodash';
import { Pagination } from 'src/core/pagination';
import { SetupNewMetadataRequestDto } from '../dtos/request/setup-metadata-request.dto';
import { METADATA_TYPE_ENUM } from 'src/shared/enums/metadata.enum';
import { col, fn, literal, Op } from 'sequelize';
import { CommonDropdownRepository } from 'src/metadata/repositories';

@Injectable()
export class MasterCapabilityService {
	constructor(
		private readonly capabilityRepository: CapabilityRepository,
		private readonly databaseHelper: DatabaseHelper,
		private readonly historyService: HistoryApiClient,
		private readonly capabilityLegRepository: CapabilityLegRepository,
		private readonly capabilityCategoryRepository: CapabilityCategoryRepository,
		private readonly masterCapabilityDropdownsRepository: MasterCapabilityDropdownRepository,
		private readonly masterEvidenceRepository: MasterEvidenceRepository,
		private readonly commonDropdownRepository: CommonDropdownRepository,
	) {}

	public async getMasterCapabilityDropdown(filterDto: MasterCapabilityDropdownRequestDto) {
		const { types } = filterDto;

		if (!types?.length) {
			throw new HttpException('Types are required.', HttpStatus.BAD_REQUEST);
		}

		let dropdowns = {};

		if (types.includes(MASTER_CAPABILITY_DROPDOWN_TYPES.LEGS)) {
			const allLegs = await this.capabilityLegRepository.getAllLegs();
			dropdowns[MASTER_CAPABILITY_DROPDOWN_TYPES.LEGS] = allLegs;
		}

		if (types.includes(MASTER_CAPABILITY_DROPDOWN_TYPES.CATEGORY)) {
			const allCategories = await this.capabilityCategoryRepository.getAllCategories();
			dropdowns[MASTER_CAPABILITY_DROPDOWN_TYPES.CATEGORY] = allCategories;
		}

		if (
			types.includes(MASTER_CAPABILITY_DROPDOWN_TYPES.PRODUCT) ||
			types.includes(MASTER_CAPABILITY_DROPDOWN_TYPES['SUB-CATEGORY'])
		) {
			const allProducts = await this.masterCapabilityDropdownsRepository.getAllDropdowns(
				types.map(type => CAPABILITY_DROPDOWN_LEVEL_ENUM[type]),
			);

			if (types.includes(MASTER_CAPABILITY_DROPDOWN_TYPES['SUB-CATEGORY'])) {
				dropdowns[MASTER_CAPABILITY_DROPDOWN_TYPES['SUB-CATEGORY']] = allProducts.filter(
					subCategory => {
						return subCategory.level === CAPABILITY_DROPDOWN_LEVEL_ENUM['SUB-CATEGORY'];
					},
				);
			}

			if (types.includes(MASTER_CAPABILITY_DROPDOWN_TYPES.PRODUCT)) {
				dropdowns[MASTER_CAPABILITY_DROPDOWN_TYPES.PRODUCT] = allProducts.filter(product => {
					return product.level === CAPABILITY_DROPDOWN_LEVEL_ENUM.PRODUCT;
				});
			}
		}

		return dropdowns;
	}

	public async searchMasterEvidences(searchTerm: string = '') {
		const results = await this.masterEvidenceRepository.getAllEvidences(searchTerm);
		return results;
	}

	public async setupNewMasterCapability(
		requestPayload: SetupNewMasterCapabilityRequestDto,
		currentContext: CurrentContext,
	) {
		return await this.databaseHelper.startTransaction(async () => {
			const {
				categoryId,
				entryName,
				coreSolutionId,
				productId,
				subCategoryId,
				type,
				level,
				evidenceCode,
				isEvidenceMandatory,
				legIds,
				verticalCodes,
			} = requestPayload;

			let productTitle = null;
			let subCategoryTitle = null;

			if (productId || subCategoryId) {
				let ids = [];

				if (productId) {
					ids.push(productId);
				}

				if (subCategoryId) {
					ids.push(subCategoryId);
				}

				const data = await this.masterCapabilityDropdownsRepository.getDataByIds(ids);

				if (data?.length < ids.length) {
					throw new HttpException('Invalid product or sub category.', HttpStatus.BAD_REQUEST);
				}

				data.forEach(item => {
					if (item.level === CAPABILITY_DROPDOWN_LEVEL_ENUM.PRODUCT && item.id === productId) {
						productTitle = item.title;
					} else if (
						item.level === CAPABILITY_DROPDOWN_LEVEL_ENUM['SUB-CATEGORY'] &&
						item.id === subCategoryId
					) {
						subCategoryTitle = item.title;
					}
				});
			}

			const newEntry = {
				coreSolutionId: coreSolutionId ? coreSolutionId : null,
				capability: trim(entryName),
				evidenceCode: evidenceCode ? evidenceCode : null,
				isEvidenceMandatory: isEvidenceMandatory ? isEvidenceMandatory : false,
				capabilityType: type,
				level,
				verticals: verticalCodes?.length ? verticalCodes : null,
				categoryId,
				legs: legIds,
				product: productTitle,
				subCategory: subCategoryTitle,
			};

			const isDataExist = await this.capabilityRepository.isCombinationDataExist({
				coreSolutionId: coreSolutionId ? coreSolutionId : null,
				categoryId,
				product: productTitle,
				subCategory: subCategoryTitle,
				capability: trim(entryName),
				capabilityType: type,
				level,
			});

			if (isDataExist) {
				throw new HttpException('Capability already exist.', HttpStatus.CONFLICT);
			}

			const newCapability = await this.capabilityRepository.setupNewCapability(
				newEntry,
				currentContext,
			);

			await this.historyService.addRequestHistory({
				created_by: currentContext.user.username,
				entity_id: newCapability.id,
				entity_type: HISTORY_ENTITY_TYPE.MASTER_CAPABILITY,
				action_performed: HISTORY_ACTION_TYPE.CREATE,
				action_date: new Date(),
				comments: 'New master capability added.',
				additional_info: {
					capabilityData: newEntry,
				},
			});

			return { message: 'New master capability setup successfully.', data: newCapability };
		});
	}

	public async setupNewMasterEvidence(
		requestPayload: SetupNewMasterEvidenceRequestDto,
		currentContext: CurrentContext,
	) {
		return await this.databaseHelper.startTransaction(async () => {
			const { name, description, type } = requestPayload;

			const isNameExist = await this.masterEvidenceRepository.isNameExist(trim(name));

			if (isNameExist) {
				throw new HttpException('Evidence name already exist.', HttpStatus.CONFLICT);
			}

			const newEntry = {
				name: trim(name),
				description: trim(description),
				type,
				code: `E${name}`,
			};

			const newEvidence = await this.masterEvidenceRepository.setupNewEvidence(
				newEntry,
				currentContext,
			);

			await this.masterEvidenceRepository.updateEvidenceById(
				newEvidence.id,
				{ code: `E${newEvidence.id}` },
				currentContext,
			);

			await this.historyService.addRequestHistory({
				created_by: currentContext.user.username,
				entity_id: newEvidence.id,
				entity_type: HISTORY_ENTITY_TYPE.MASTER_EVIDENCE,
				action_performed: HISTORY_ACTION_TYPE.CREATE,
				action_date: new Date(),
				comments: 'New master evidence added.',
				additional_info: {
					evidenceData: newEntry,
				},
			});

			return { message: 'New master evidence setup successfully.', data: newEvidence };
		});
	}

	public async getMasterCapabilitiesByFilter(
		page: number,
		limit: number,
		filterDto?: MasterCapabilityFilterRequestDto,
	): Promise<PaginatedMasterCapabilityListResponseDto> {
		const { rows, count } = await this.capabilityRepository.getCapabilitiesByFilter(
			filterDto,
			page,
			limit,
		);

		const records = multiObjectToInstance(MasterCapabilityListResponseDto, rows, {
			excludeExtraneousValues: true,
			enableImplicitConversion: true,
		});

		const legs = await this.capabilityLegRepository.getALlLegsBasic();
		const legMap = new Map(legs.map(leg => [leg.id, leg]));

		records.forEach(record => {
			record.legsDetail = record.legs.map(legId => legMap.get(legId)).filter(Boolean);
		});

		return new Pagination({ records, total: count });
	}

	getDropdownLevel(
		type: METADATA_TYPE_ENUM | COMMON_DROPDOWN_TYPE,
	): CAPABILITY_DROPDOWN_LEVEL_ENUM {
		if (type === METADATA_TYPE_ENUM.PRODUCT) {
			return CAPABILITY_DROPDOWN_LEVEL_ENUM['SUB-CATEGORY'];
		}

		if (type === METADATA_TYPE_ENUM.PRODUCT_FAMILY) {
			return CAPABILITY_DROPDOWN_LEVEL_ENUM['PRODUCT'];
		}
	}

	public async setupNewMetadata(
		requestPayload: SetupNewMetadataRequestDto,
		currentContext: CurrentContext,
	) {
		const { name, type, legIds } = requestPayload;
		let trimedName = trim(name);

		const isCommonDropdownType = Object.values(COMMON_DROPDOWN_TYPE).includes(
			type as COMMON_DROPDOWN_TYPE,
		);

		if (isCommonDropdownType) {
			return await this.setupNewMetadataOfNewTypes(requestPayload, currentContext);
		}

		if (type === METADATA_TYPE_ENUM.CATEGORY) {
			const isNameExist = await this.capabilityCategoryRepository.isNameExist(trimedName);

			if (isNameExist) {
				throw new HttpException('Metadata already exist.', HttpStatus.CONFLICT);
			}

			return this.databaseHelper.startTransaction(async () => {
				const newEntry = {
					title: trim(name),
					code: toUpperUnderscore(name),
				};
				const newCategory = await this.capabilityCategoryRepository.setupNewCategory(
					newEntry,
					currentContext,
				);

				const updatedCategories = await this.capabilityLegRepository.updateLegsCategories(
					{
						categories: fn(
							'jsonb_concat',
							col('categories'),
							literal(`'[${newCategory.id}]'::jsonb`),
						),
					},
					currentContext,
					{
						where: {
							id: {
								[Op.in]: legIds,
							},
						},
					},
				);

				await this.historyService.addRequestHistory({
					created_by: currentContext.user.username,
					entity_id: newCategory.id,
					entity_type: HISTORY_ENTITY_TYPE.METADATA,
					action_performed: HISTORY_ACTION_TYPE.CREATE,
					action_date: new Date(),
					comments: `New ${toLower(type)} added.`,
					additional_info: {
						metadata: newEntry,
					},
				});

				return { message: 'New master metadata setup successfully.', data: updatedCategories };
			});
		}

		const isNameExist = await this.masterCapabilityDropdownsRepository.isNameExist(
			trimedName,
			this.getDropdownLevel(type),
		);

		if (isNameExist) {
			throw new HttpException('Metadata already exist.', HttpStatus.CONFLICT);
		}

		const newEntry = {
			title: trimedName,
			level: this.getDropdownLevel(type),
			otherDetails: '',
		};

		return this.databaseHelper.startTransaction(async () => {
			const newMetadata = await this.masterCapabilityDropdownsRepository.setupNewMetadata(
				newEntry,
				currentContext,
			);

			await this.historyService.addRequestHistory({
				created_by: currentContext.user.username,
				entity_id: newMetadata.id,
				entity_type: HISTORY_ENTITY_TYPE.METADATA,
				action_performed: HISTORY_ACTION_TYPE.CREATE,
				action_date: new Date(),
				comments: `New ${toLower(type)} added.`,
				additional_info: {
					metadata: newEntry,
				},
			});

			return { message: 'New master metadata setup successfully.', data: newMetadata };
		});
	}

	public async setupNewMetadataOfNewTypes(
		requestPayload: SetupNewMetadataRequestDto,
		currentContext: CurrentContext,
	) {
		let { name, type, code, coreSolutionId, locationTypeId, offset } = requestPayload;

		const title = startCase(toLower(trim(name)));
		code = code ? upperCase(code) : toUpperUnderscore(title);

		let condition: Record<string, any> = {};

		const typesWithCode = [
			COMMON_DROPDOWN_TYPE.CURRENCY,
			COMMON_DROPDOWN_TYPE.LANGUAGE,
			COMMON_DROPDOWN_TYPE.TIMEZONE,
		];

		const typesWithTitleOnly = [
			COMMON_DROPDOWN_TYPE.FF_LOCATION_ID_TYPES,
			COMMON_DROPDOWN_TYPE.BRANDS,
		];

		if (typesWithCode.includes(type as COMMON_DROPDOWN_TYPE)) {
			condition = { title, code };
		}

		if (typesWithTitleOnly.includes(type as COMMON_DROPDOWN_TYPE)) {
			condition = { title };
		}

		if (type !== COMMON_DROPDOWN_TYPE.STRATEGIC_CLASSIFICATION) {
			const isRecordExist = await this.commonDropdownRepository.isDropdownRecordExists(condition);

			if (isRecordExist) {
				throw new HttpException('Metadata already exist.', HttpStatus.CONFLICT);
			}
		}

		const payload = {
			title,
			code,
			type,
			...(offset && { offset }),
			...(coreSolutionId && { coreSolutionId }),
			...(locationTypeId && { locationTypeId }),
		};

		return this.databaseHelper.startTransaction(async () => {
			const newMetadata = await this.commonDropdownRepository.setupNewMetadataOfCommonTypes(
				payload,
				currentContext,
			);

			await this.historyService.addRequestHistory({
				created_by: currentContext.user.username,
				entity_id: newMetadata.id,
				entity_type: HISTORY_ENTITY_TYPE.METADATA,
				action_performed: HISTORY_ACTION_TYPE.CREATE,
				action_date: new Date(),
				comments: `New ${toLower(type)} added.`,
				additional_info: {
					metadata: payload,
				},
			});

			return {
				message: 'New master metadata setup successfully.',
				data: newMetadata,
			};
		});
	}
}
