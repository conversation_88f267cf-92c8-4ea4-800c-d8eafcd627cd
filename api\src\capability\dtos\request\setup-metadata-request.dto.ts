import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsN<PERSON>ber, IsString, IsOptional } from 'class-validator';
import { COMMON_DROPDOWN_TYPE, EVIDENCE_TYPE } from 'src/shared/enums';
import { METADATA_TYPE_ENUM } from 'src/shared/enums/metadata.enum';

const combinedEnum = {
	...METADATA_TYPE_ENUM,
	...COMMON_DROPDOWN_TYPE,
} as const;

export class SetupNewMetadataRequestDto {
	@ApiProperty()
	@IsNotEmpty()
	@IsString()
	public name: string;

	@ApiProperty({ type: [Number], isArray: true })
	@IsOptional()
	@IsArray()
	@IsNumber({}, { each: true })
	public legIds?: number[];

	@ApiProperty({ enum: combinedEnum })
	@IsNotEmpty()
	@IsEnum(combinedEnum)
	public type: typeof combinedEnum[keyof typeof combinedEnum];

	@ApiProperty()
	@IsOptional()
	@IsString()
	public code?: string;

	@ApiProperty()
	@IsOptional()
	@IsNumber()
	public coreSolutionId?: number;

	@ApiProperty()
	@IsOptional()
	@IsNumber()
	public locationTypeId?: number;

	@ApiProperty()
	@IsOptional()
	@IsString()
	public offset?: string;
}
