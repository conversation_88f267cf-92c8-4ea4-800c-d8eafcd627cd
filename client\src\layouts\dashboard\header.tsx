import {
  AppBar,
  Avatar,
  Box,
  IconButton,
  ListItemIcon,
  Menu,
  MenuItem,
  Stack,
  Toolbar,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';

import Logo from '@/components/logo';
import AccountPopover from '../common/account-popover';

import SvgColor from '@/components/svg-color';
import { useActiveLink } from '@/routes/hooks/use-active-link';
import { bgBlur } from '@/theme/css';
import NotificationsPopover from '../common/notifications-popover';
import { useNavData } from '../config-layout';
import { AccountCircle } from '@mui/icons-material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

type Props = {
  onOpenNav?: VoidFunction;
};

const Header = ({ onOpenNav }: Props) => {
  const theme = useTheme();
  const { data, moreNavData } = useNavData();
  const { t } = useTranslation();

  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavItemClick = (path: string) => {
    navigate(path);
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        width: '100%',
        // backgroundColor: theme.palette.background.paper,
        zIndex: theme.zIndex.appBar + 1,
        transition: theme.transitions.create(['height'], {
          duration: theme.transitions.duration.shorter,
        }),
        background: '#FFFFFF 0% 0% no-repeat padding-box', // ✅ Applies background styling
        boxShadow: '0px 3px 12px #00000029', // ✅ Adds shadow effect
        opacity: 1,
        ...bgBlur({ color: theme.palette.background.paper }),
      }}
    >
      <Toolbar sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {/* Mobile: Hamburger menu on the LEFT of Logo */}
        {isMobile && (
          <IconButton onClick={onOpenNav}>
            <SvgColor src="assets/icons/navbar/ic_menu_item.svg" />
          </IconButton>
        )}
        {/* Logo Section */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Logo />
        </Box>
        {/* Desktop & Tablet Navigation */}
        {(isDesktop || isTablet) && (
          <Stack flexGrow={1} justifyContent="flex-end" alignItems="center" marginRight={5} direction="row" spacing={1}>
            {data.length > 0 &&
              data[0] &&
              data[0].items.map((item) => {
                const isActive = useActiveLink(item.path);
                return (
                  <Typography
                    key={item.path}
                    variant="body1"
                    sx={{
                      color: isActive ? theme.palette.text.primary : theme.palette.text.secondary,
                      cursor: 'pointer',
                      fontWeight: isActive ? 600 : 500,
                      padding: '0.75rem 1.25rem',
                      borderRadius: '12px',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      whiteSpace: 'nowrap',
                      position: 'relative',
                      fontSize: '0.9rem',
                      letterSpacing: '0.02em',
                      '&:hover': {
                        color: theme.palette.text.primary,
                        backgroundColor: theme.palette.action.hover,
                        transform: 'translateY(-1px)',
                      },
                      backgroundColor: isActive ? theme.palette.action.selected : 'transparent',
                      border: '1px solid transparent',
                    }}
                    onClick={() => handleNavItemClick(item.path)}
                  >
                    {item.title}
                  </Typography>
                );
              })}
            <Box sx={{ position: 'relative' }}>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.text.secondary,
                  cursor: 'pointer',
                  fontWeight: 500,
                  padding: '0.75rem 1.25rem',
                  borderRadius: '12px',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  whiteSpace: 'nowrap',
                  fontSize: '0.9rem',
                  letterSpacing: '0.02em',
                  border: '1px solid transparent',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  '&:hover': {
                    color: theme.palette.primary.main,
                    backgroundColor: theme.palette.action.hover,
                    transform: 'translateY(-1px)',
                  },
                  '&::after': {
                    content: '"▼"',
                    fontSize: '0.7rem',
                    marginLeft: '4px',
                    transition: 'transform 0.3s ease',
                    transform: open ? 'rotate(180deg)' : 'rotate(0deg)',
                  },
                }}
                onMouseEnter={handleMenu}
              >
                {t('label.more')}
              </Typography>
              <Menu
                anchorEl={anchorEl}
                id="account-menu"
                open={open}
                onClose={handleClose}
                onClick={handleClose}
                slotProps={{
                  paper: {
                    elevation: 0,
                    sx: {
                      overflow: 'visible',
                      filter: 'drop-shadow(0px 8px 32px rgba(0,0,0,0.12))',
                      mt: 1.5,
                      borderRadius: '12px',
                      border: `1px solid ${theme.palette.divider}`,
                      background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
                      minWidth: 200,
                      '& .MuiAvatar-root': {
                        width: 32,
                        height: 32,
                        ml: -0.5,
                        mr: 1,
                      },
                      '&::before': {
                        content: '""',
                        display: 'block',
                        position: 'absolute',
                        top: 0,
                        right: 20,
                        width: 12,
                        height: 12,
                        bgcolor: 'background.paper',
                        transform: 'translateY(-50%) rotate(45deg)',
                        zIndex: 0,
                        border: `1px solid ${theme.palette.divider}`,
                        borderBottom: 'none',
                        borderRight: 'none',
                      },
                    },
                  },
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
              >
                {moreNavData.length > 0 &&
                  moreNavData[0] &&
                  moreNavData[0].items?.map((item, index) => {
                    const isActive = useActiveLink(item.path);
                    return (
                      <MenuItem
                        key={index}
                        onClick={() => {
                          handleNavItemClick(item.path);
                          handleClose();
                        }}
                        sx={{
                          color: isActive ? theme.palette.primary.main : theme.palette.text.secondary,
                          cursor: 'pointer',
                          fontWeight: isActive ? 600 : 500,
                          padding: '0.75rem 1.25rem',
                          borderRadius: '8px',
                          margin: '4px 8px',
                          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                          whiteSpace: 'nowrap',
                          fontSize: '0.9rem',
                          letterSpacing: '0.02em',
                          border: '1px solid transparent',
                          '&:hover': {
                            color: theme.palette.primary.main,
                            backgroundColor: `${theme.palette.primary.main}08`,
                            borderColor: `${theme.palette.primary.main}20`,
                            transform: 'translateX(4px)',
                          },
                          backgroundColor: isActive ? `${theme.palette.primary.main}12` : 'transparent',
                          borderColor: isActive ? `${theme.palette.primary.main}20` : 'transparent',
                          '&:first-of-type': {
                            marginTop: '8px',
                          },
                          '&:last-of-type': {
                            marginBottom: '8px',
                          },
                        }}
                      >
                        {item.title}
                      </MenuItem>
                    );
                  })}
              </Menu>
            </Box>
          </Stack>
        )}
        <Stack direction="row" spacing={2}>
          <NotificationsPopover />
          <AccountPopover />
        </Stack>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
