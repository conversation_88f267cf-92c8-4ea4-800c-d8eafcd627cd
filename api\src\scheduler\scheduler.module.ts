import { Module } from '@nestjs/common';
import { LocationWiseCapabilityRepository } from 'src/capability/repositories';
import { ConfigService } from 'src/config/config.service';
import { LoggerService } from 'src/core/services';
import { UserPermissionRepository } from 'src/permission/repositories';
import {
	AdminApiClient,
	HistoryApiClient,
	MSGraphApiClient,
	NotificationApiClient,
} from 'src/shared/clients';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedNotificationService, SharedPermissionService } from 'src/shared/services';
import { SchedulerService } from './services';

const repositories = [UserPermissionRepository, LocationWiseCapabilityRepository];

@Module({
	controllers: [],
	providers: [
		...repositories,
		SchedulerService,
		AdminApiClient,
		MSGraphApiClient,
		SharedPermissionService,
		SharedNotificationService,
		HistoryApiClient,
		NotificationApiClient,
		DatabaseHelper,
		ConfigService,
	],
})
export class SchedulerModule {}
