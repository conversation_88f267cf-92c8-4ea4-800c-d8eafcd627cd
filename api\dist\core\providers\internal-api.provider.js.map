{"version": 3, "file": "internal-api.provider.js", "sourceRoot": "", "sources": ["../../../src/core/providers/internal-api.provider.ts"], "names": [], "mappings": ";;;AAAA,gEAA0D;AAC1D,sDAAoD;AACpD,qEAA+D;AAElD,QAAA,oBAAoB,GAAG;IACnC;QACC,OAAO,EAAE,wBAAY,CAAC,kBAAkB;QACxC,UAAU,EAAE,CAAC,aAA4B,EAAE,EAAE;YAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;YACvD,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAC;YACnC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC;YAC3C,OAAO,IAAI,0BAAW,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC;gBACrD,SAAS,EAAE,SAAS;gBACpB,aAAa,EAAE,KAAK;aACpB,CAAC,CAAC;QACJ,CAAC;QACD,MAAM,EAAE,CAAC,8BAAa,CAAC;KACvB;IACD;QACC,OAAO,EAAE,wBAAY,CAAC,oBAAoB;QAC1C,UAAU,EAAE,CAAC,aAA4B,EAAE,EAAE;YAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;YACvD,MAAM,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;YACrC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC;YAC7C,OAAO,IAAI,0BAAW,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC;gBACrD,SAAS,EAAE,SAAS;gBACpB,aAAa,EAAE,KAAK;aACpB,CAAC,CAAC;QACJ,CAAC;QACD,MAAM,EAAE,CAAC,8BAAa,CAAC;KACvB;IACD;QACC,OAAO,EAAE,wBAAY,CAAC,yBAAyB;QAC/C,UAAU,EAAE,CAAC,aAA4B,EAAE,EAAE;YAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;YACvD,MAAM,EAAE,eAAe,EAAE,GAAG,aAAa,CAAC;YAC1C,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,eAAe,CAAC;YAClD,OAAO,IAAI,0BAAW,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC;gBACrD,SAAS,EAAE,SAAS;gBACpB,aAAa,EAAE,KAAK;aACpB,CAAC,CAAC;QACJ,CAAC;QACD,MAAM,EAAE,CAAC,8BAAa,CAAC;KACvB;CACD,CAAC"}